<?php

namespace Database\CCP;

use Database\Archive,
    Database\CCP\om\BaseInvoiceQuery,
    Database\CCP\Payment,
    Database\CCP\MovementQuery,
    Database\CCP\PaymentMethodQuery,
    Database\CCP\MovementViewQuery,
    Database\Setting\InstituteQuery,
    Database\Core\ParameterQuery,
    Database\Core\BankAccountQuery,
    Database\Archive\ArchiveMailAccountQuery,
    Database\CCP\om\BaseAdditionalPeer,
    Database\CCP\om\BaseMovementAdditionalPeer,
    CCP\Model\McStudent,
    Archive\Model\SignInfocert,
    MT\Logger\LoggerZend,
    Core\Model\Log,
    Zend\Mail\Transport\Smtp as SmtpTransport,
    Zend\Mail\Transport\SmtpOptions,
    BasePeer,
    Criteria;


/**
 * Skeleton subclass for performing query and update operations on the 'ccp_invoice' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.CCP
 */
class InvoiceQuery extends BaseInvoiceQuery
{

    /**
     * Generate zip FPA file <PIVA>_00001.zip
     *
     * @return string
     */
    public function generateZipXmlFPA($signedPath) {
        // Generate zip file
        $piva = InstituteQuery::create()->filterByActive(true)->findOne()->getPartitaIva();

        $zipPath = '/tmp/IT' .  $piva . "_00001.zip";
        $zip = new \ZipArchive();
        $zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        foreach (glob($signedPath . "/*.xml*") as $filepath) {
            $zip->addFile($filepath, basename($filepath));
        }
        $zip->close();

        $uuid = explode('/', $signedPath)[2];
        $pathToClean = '/tmp/'.$uuid;
        if(file_exists($pathToClean)) {
            exec('rm -r ' . $pathToClean);
        }

        return $zipPath;
    }

    /**
     * Generate xml file for FPA
     *
     * @return string
     */
    public function generateXmlFPA($invoices) {
        $institute = InstituteQuery::create()->filterByActive(true)->findOne();

        if(!$institute) {
            throw new \Exception("Istituto non selezionato", 400);
        }
        if(!$institute->getPartitaIva()) {
            throw new \Exception("Partita IVA non inserita", 400);
        }

        $uuid = uniqid();
        $sourcePath = '/tmp/'. $uuid .'/source';
        mkdir( $sourcePath, 0777, true);

        $parameter = ParameterQuery::create()->findOneByName('INVOICE_TRANSMISSION_ID');
        $idTransmission = (int) $parameter->getValue();
        foreach ($invoices as $invoice) {
            $idTransmission++;

            $idTransmissionPad =  str_pad(base_convert($idTransmission, 10, 36), 5,"0",STR_PAD_LEFT);
            $xmlName = 'IT' . $institute->getPartitaIva() . "_".$idTransmissionPad.".xml";
            file_put_contents($sourcePath.'/'.$xmlName, $invoice->getXml());

            $invoice->setXmlName($xmlName);
            $invoice->save();
        }
        $parameter->setValue($idTransmission);
        $parameter->save();

        return $sourcePath;
    }

    /**
     * Sign xml invoice and return path of signed file
     *
     * @return string
     */
    public function signXmlFPA($sourcePath, $userId, $alias, $pin) {
        $ri = new SignInfocert($userId);
        $ri->setType('CADES');

        if (!$alias or !$pin) {
            if(!$ri->checkCredentials()){
                throw new \Exception("Cache scaduta", 307);
            } else {
                Log::info(Log::SECTION_INVOICE, "Credenziali ancora in cache" , $userId);
            }
        } else {
            $ri->setCredentials($alias, $pin);
            Log::info(Log::SECTION_INVOICE, "Settate le credenziali alias " . $alias. " e pin " . $pin , $userId);
        }

        $signedPath = str_replace('source', 'dest', $sourcePath);
        mkdir( $signedPath, 0777, true);

        $sign_result = $ri->sign($sourcePath, $signedPath, '', true);
        if ($sign_result){
            Log::info(Log::SECTION_INVOICE, "Firma avvenuta con successo da $sourcePath a $signedPath", $userId);

            if (count(glob($signedPath.'/*.p7m')) != count(glob($sourcePath.'/*.xml')) ) {
                throw new Exception("File firmato mancante", 500);
                Log::error(Log::SECTION_INVOICE, "File firmato mancante: " . json_encode($ri->getLastError()) , $userId);
            }
        } else {
            Log::error(Log::SECTION_INVOICE, "Errore durante la firma: " . json_encode($ri->getLastError()) , $userId);
            throw new \Exception("Errore durante la firma", 500);
        }

        return $signedPath;
    }

    public function sendMailFPA($invoices, $userId, $alias, $pin) {

        $sourcePath = $this->generateXmlFPA($invoices);
        $signedPath = $this->signXmlFPA($sourcePath, $userId, $alias, $pin);
        $zipPath    = $this->generateZipXmlFPA($signedPath);

        $mailAccount = ArchiveMailAccountQuery::create()->filterByFatturaPa(true)->findOne();
        $toSend = [ParameterQuery::create()->findOneByName('FATTURAPA_EMAIL')->getValue()];
        $outname = Archive\ArchiveMailAccountQuery::create()->getSender($mailAccount);

        $fileContent = fopen($zipPath , 'r');
        $attachment = new \Zend\Mime\Part($fileContent);
        $attachment->encoding = \Zend\Mime\Mime::ENCODING_BASE64;
        $attachment->type = 'application/zip';
        $attachment->filename = basename($zipPath);
        $attachment->disposition = \Zend\Mime\Mime::DISPOSITION_ATTACHMENT;

        $mimeMessage = new \Zend\Mime\Message();
        $mimeMessage->setParts([$attachment]);

        $message = new \Zend\Mail\Message();
        $message->setBody($mimeMessage);
        $message->setFrom($outname);
        $message->addTo($toSend);
        # $message->setSubject($data['subject']);

        $transport = new SmtpTransport();
        $options = new SmtpOptions(array(
            'host'              => $mailAccount->getHost(),
            'connection_class'  => 'plain',
            'port'              => $mailAccount->getPort(),
            'connection_config' => array(
                'username' => $mailAccount->getUsername(),
                'password' => base64_decode($mailAccount->getPassword()),
                'ssl'      => Archive\ArchiveMailSecurityQuery::create()->findOneById($mailAccount->getSecurity())->getName()
            ),
        ));

        $transport->setOptions($options);
        try {
            $transport->send($message);
        } catch (Exception $e) {
            throw new Exception("Errore durante l'invio della mail", 500);
        }

        return True;

    }

    public function getList($filter, $onlyCount = false, $returnObj=false) {
        $invoice = InvoiceQuery::create()->filterByNumber(0, \Criteria::GREATER_THAN);

        $filter['paid'] = isset($filter['paid']) ? (bool) $filter['paid'] : true;
        $filter['not_paid'] = isset($filter['not_paid']) ? (bool) $filter['not_paid'] : true;
        $filter['published'] = isset($filter['published']) ? (bool) $filter['published'] : true;
        $filter['unpublished'] = isset($filter['unpublished']) ? (bool) $filter['unpublished'] : true;

        if($filter['query']) {
            $query = pg_escape_string($filter['query']);
            $invoice->where("(rows ILIKE '%$query%' OR accountholder ILIKE '%$query%')");
        }
        if($filter['date_start']) {
            $date_start = date('c', strtotime($filter['date_start']));
            $invoice->filterByDate($date_start, Criteria::GREATER_EQUAL);
        }
        if($filter['date_end']) {
            $date_end = date('c', strtotime($filter['date_end'].' +1 days -2 hours'));

            $invoice->filterByDate($date_end, Criteria::LESS_THAN);
        }

        if(!empty($filter['suffix'])) {
            $invoice->filterBySuffix($filter['suffix']);
        }

        if($filter['expiration_date_start']) {
            $expiration_date_start = date('c', strtotime($filter['expiration_date_start']));

            # Filtering from deposit slip creation mean you filter expiration date into movement rows
            if((bool) $filter['deposit_slip'] === true) {
                $q = " id in (
                    SELECT id
                    FROM (
                        SELECT id,jsonb_array_elements(rows::jsonb) AS obj
                        FROM ccp_invoice
                    ) AS subquery
                    WHERE (obj->>'expiration_date')::int >= ".strtotime($expiration_date_start)."
                    )
                ";
                $invoice->where($q);
            }
            else {
                $invoice->filterByExpirationDate($expiration_date_start, Criteria::GREATER_EQUAL);
            }
        }
        if($filter['expiration_date_end']) {
            $expiration_date_end = date('c', strtotime($filter['expiration_date_end'].' +1 days'));

            # Filtering from deposit slip creation mean you filter expiration date into movement rows
            if((bool) $filter['deposit_slip'] === true) {
                    $q = " id in (
                        SELECT id
                        FROM (
                            SELECT id,jsonb_array_elements(rows::jsonb) AS obj
                            FROM ccp_invoice
                        ) AS subquery
                        WHERE (obj->>'expiration_date')::int < ".strtotime($expiration_date_end)."
                        )
                    ";
                    $invoice->where($q);
            }
            else {
                $invoice->filterByExpirationDate($expiration_date_end, Criteria::LESS_THAN);
            }
        }
        if($filter['payment_method']) {
           $invoice->filterByPaymentMethod($filter['payment_method']);
        }

        if($filter['number']) {
            $invoice->filterByNumber($filter['number']);
        }

        if($filter['number_from']) {
            $invoice->filterByNumber($filter['number_from'], \Criteria::GREATER_EQUAL);
        }

        if($filter['number_to']) {
            $invoice->filterByNumber($filter['number_to'], \Criteria::LESS_EQUAL);
        }

        if(isset($filter['credit_note'])){
           $invoice->filterByCreditNote($filter['credit_note']);
        }

        if((bool) $filter['deposit_slip'] === true) {
            // $invoice->where("(SELECT count(id) from ccp_invoice_deposit_slip cids where cids.ccp_invoice=ccp_invoice.id and cids.unpaid_date IS NULL) = 0");
            //$invoice->where("id NOT IN (SELECT ccp_invoice FROM ccp_invoice_deposit_slip WHERE unpaid_date IS NULL)");
            //$invoice->where("(SELECT count(*) FROM ccp_invoice_deposit_slip where ccp_invoice=ccp_invoice.id)=0");
            //$invoice->where("((SELECT count(*) FROM ccp_invoice_deposit_slip where ccp_invoice=ccp_invoice.id)=0 or id in (
            //    SELECT ccp_invoice FROM ccp_invoice_deposit_slip where ccp_invoice=ccp_invoice.id and last_expiration_date<to_timestamp((select MAX((rw::json->>'expiration_date')::integer) AS max_expiration_date from (select json_array_elements(rows::json) as rw from ccp_invoice where ccp_invoice=ccp_invoice.id) t))))");

        }

        if($filter['paid'] === false) {
            $invoice->where("id in (select invoice_id  from (select invoice_id, total, total_payments from (select invoice_id, sum(total) as total, sum(total_payments) as total_payments from ccp_view_movement where ccp_invoice.id=ccp_view_movement.invoice_id group by invoice_id) t where t.total!=t.total_payments ) t2)");
        }
        if($filter['not_paid'] === false) {
            $invoice->where("id in (select invoice_id  from (select invoice_id, total, total_payments from (select invoice_id, sum(total) as total, sum(total_payments) as total_payments from ccp_view_movement where ccp_invoice.id=ccp_view_movement.invoice_id group by invoice_id) t where t.total=t.total_payments ) t2)");
        }
        if($filter['published']===false) {
            $invoice->filterByPublicationPath(NULL, \Criteria::ISNULL);
        }
        if($filter['unpublished']===false) {
            $invoice->filterByPublicationPath(NULL, \Criteria::ISNOTNULL);
        }

        if($filter['generic_query']) {
            $query = pg_escape_string($filter['generic_query']);
            $invoice->where(
                <<<EOF
            (
                number::text = '$query'
                OR accountholder ILIKE '%$query%'
                OR rows ILIKE '%$query%'
            )
EOF
            );
        }

        $orderings = [];
        if ($filter['sort']) {
            $orderings = json_decode($filter['sort'], true);
        } else {
            $invoice->orderByNumber(\Criteria::DESC)->orderByDate(\Criteria::DESC);
        }

        foreach ($orderings as $ordering) {

            if($ordering['property'] == 'number') {
                $invoice->withColumn('to_char("date", \'YYYY\')', 'year');
                $invoice->orderBy('year', $ordering['direction']);
            }
            $invoice->orderBy($ordering['property'], $ordering['direction']);
        }

        if($onlyCount === true) {
            return $invoice->count();
        }

        if (isset($filter['limit']) && isset($filter['start'])) {
            $invoice->setLimit($filter['limit']);
            $invoice->setOffset($filter['start']);
        }

        if ($returnObj===true) {
            return $invoice;
        } else {
            return $invoice
                ->find()
                ->toArray(null, false, BasePeer::TYPE_FIELDNAME);
        }

    }

    /**
     * Get invoice total by movement
     */
    public function getTotal($rows, $creditNote=false, $fromDate=null, $toDate=null) {
        $total = 0;
        foreach ($rows as $row) {
            $total += $this->getMovementTotal($row, $fromDate, $toDate);
        }
        return $total;
    }



    /*
        OLD -> Get total considering additionals in json invoice row
    */
    public function _getTotal($rows, $creditNote=false, $fromDate=null, $toDate=null) {
        $discountCalcModel = ParameterQuery::create()->findOneByName('DISCOUNT_CALC_MODEL')->getValue();
        //$movement = MovementQuery::create()->findPk($rows[0]['id']);


        $total = 0;
        $absAdditionals = 0;
        foreach ($rows as $row) {

            // Check specific calculation row for deposit slip base on movement expiration date
            if($row['type']!='Bollo') {
                if($fromDate && $row['expiration_date']<$fromDate) continue;
                if($toDate && $row['expiration_date']>=$toDate) continue;
            }
            //if(($fromDate or $toDate) and $row['type']=='Bollo') continue;

            $gross = $row['amount'];

            //if($fromDate or $toDate) $gross -= $movement->getTotalPayments();


            // Se nota di credito allora la riga del bollo (type=bollo) va sottratta
            //if($creditNote==true and $row['type']=='Bollo') $gross*=-1;

            $total += $gross;
            $additionals = isset($row['additionals']) ? $row['additionals'] : [];

            foreach ($additionals as $k => $a) {

                if(isset($a['abs_amount'])) {
                    $total += $a['abs_amount'];
                } else {
                    $additionalsAmount = $a['amount'];

                    $positive = $a['positive'] ? 1 : -1;


                    if ($a['percentual']) {
                        $additionalsAmount = ($gross / 100) * $a['amount'];
                        $additionalsAmount *= $positive;

                        if ($discountCalcModel == 'PROGRESSIVE') {
                            $gross += $additionalsAmount;
                        }
                        $total += $additionalsAmount;
                    } else {

                        $absAdditionals += ($additionalsAmount * $positive);
                        // $absAdditionals *= $positive;
                    }
                }


            }
        }
        $total += $absAdditionals;
        return $total;

    }

    // TODO: utilizzare gli oggetti originali invece della riga fattura
    public function getMaxBolloMovementIndex($rows){
        $max_amount = 0;
        $idx = 0;
        foreach ($rows as $i => $row) {
            if ($row['bollo']) {
                $tot = InvoiceQuery::create()->getMovementTotal($row);
                if($tot>$max_amount) {
                    $idx = $i;
                    $max_amount = $tot;
                }
            }
        }
        return $idx;
    }

    // TODO: utilizzare gli oggetti originali invece della riga fattura
    public function calcBollo($rows, $invoice)
    {
        $hasBollo = false;
        $totalBollo = 0;

        // Consideriamo le righe già presenti in fattura
        if ($invoice->getRows()) {
            $invoiceRows = json_decode($invoice->getRows(), true);
            $hasBollo = array_filter(
                $invoiceRows,
                function ($a) {
                    return $a['type'] == 'Bollo';
                }
            );
            // Restituisce null se il bollo è già stato calcolato
            if ($hasBollo) {
                return null;
            }

            foreach ($invoiceRows as $invoiceRow) {
                // Consideriamo solo le righe che partecipano al calcolo del bollo
                if ($invoiceRow['bollo']) {
                    $totalBollo += InvoiceQuery::create()->getMovementTotal($invoiceRow);
                }
            }
        }

        // Poi le righe in aggiunta a quelle eventualmente già presenti
        foreach ($rows as $row) {
            // Consideriamo solo le righe che partecipano al calcolo del bollo
            if ($row['bollo']) {
                $totalBollo += InvoiceQuery::create()->getMovementTotal($row);
            }
        }

        if ($totalBollo >= 77.47) {
            return [
                'type' => 'Bollo',
                'amount' => 2
            ];
        }

        return null;


    }
    /*
        Get total considering additionals
    */
    public function getMovementTotal($row, $fromDate=null, $toDate=null) {
        if ($row['type'] == 'Bollo') {
            return $row['amount'];
        }


        if($fromDate && $row['expiration_date']<$fromDate) return 0;
        if($toDate && $row['expiration_date']>=$toDate) return 0;



        if(!isset($row['id'])) return 0;
        $movement = MovementQuery::create()->findPk($row['id']);
        if(!$movement) return 0;

        return $movement->getTotal();

        /*
        // Da salvare in fattura e passare alla funzione
        $discountCalcModel = ParameterQuery::create()->findOneByName('DISCOUNT_CALC_MODEL')->getValue();

        $total = $row['amount'];
        $additionals = isset($row['additionals']) ? $row['additionals'] : [];
        $absAdditionals = 0;
        foreach ($additionals as $k => $a) {

            if(isset($a['abs_amount'])) {
                $total += $a['abs_amount'];
            } else {
                $additionalsAmount = $a['amount'];

                $positive = $a['positive'] ? 1 : -1;

                if ($a['percentual']) {
                    $additionalsAmount = ($gross / 100) * $a['amount'];
                    $additionalsAmount *= $positive;

                    if ($discountCalcModel == 'PROGRESSIVE') {
                        $gross += $additionalsAmount;
                    }
                    $total += $additionalsAmount;
                } else {
                    $absAdditionals += ($additionalsAmount * $positive);
                }
            }
        }
        $total += $absAdditionals;
        return $total;
        */

    }

    public function generateInvoice($data)
    {
        $data_invoice = null;
        if($data['invoice_id']) {
            $data_invoice = InvoiceQuery::create()->findOneById($data['invoice_id']);
            $data_invoice->setTotal(0);
        }

        $studentState = (int)ParameterQuery::create()->findOneByName('ID_STATO_STUDENTE_SVIZZERO')->getValue();

        $d = $data['date'] ? $data['date'] : date('c', time());
        $number = $data['number'] ? (int) $data['number'] : $this->getMaxNumber($d);
        $results = [];
        $isCompany = false;
        $accountHolders = [];

        if (!$data_invoice) {
            if(!$data['bank_id']) {
                $banks = BankAccountQuery::create()->filterByInvoiceDefault(true)->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);
            } else {
                $banks = BankAccountQuery::create()->filterById($data['bank_id'])->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);
            }
            if(count($banks) == 0) {
                throw new \Exception('Non ci sono banche da inserire nella fattura per il versamento', 400);
            }
        }

        list($accountHolders, $payers) = $this->getInvoiceRows(
            $data['linked_movements'],
            $data['multiple_expiration_dates'],
            $data
        );

        if(isset($data['accountholder'])) {
            $isCompany = true;
        }
        $tableText = isset($data['table_text']) ? trim($data['table_text']) : null;
        $expirationText = isset($data['expiration_text']) ? trim($data['expiration_text']) : null;

        // Una fattura per ogni intestatario e data di scadenza (COMMENTO OBSOLETO?)

        // Flag per indicare se stiamo generando note di credito o fatture
        $creditNote = isset($data['credit_note']) ?  $data['credit_note'] : false;
        $invoices = [];

        // Ciclo per ogni intestatario -
        foreach ($accountHolders as $ahExpDate) {

            //$invoice = $data_invoice ? $data_invoice : null;
            if ($data_invoice) {
                $invoice = $data_invoice;
                $invoice->setRows(null);
            } else {
                $invoice = null;
            }
            $invoiceRows = [];
            $payer = reset($ahExpDate)['payers'][0];
            /**
             * Trovo paymentId
             */

            $paymentMethodId = (
                $payer['tipo_addebito'] ? PaymentMethodQuery::create()
                ->filterByEasyCode($payer['tipo_addebito'])
                ->findOne()
                ->getId() : null
            );


            /**
             * Usa il metodo di pagamento impostato
             * altrimenti quello default del pagante
             */
            $paymentMethod = (
                isset($data['payment_method']) ? $data['payment_method'] : $paymentMethodId
            );

            foreach ($ahExpDate as $ah) {
                $includeBollo = $ah['include_bollo'];
                /**
                 * In fase di creazione, se non sono previste fatture con
                 * scadenze multiple, ad ogni variazione di scadenza viene
                 * generata una nuova fattura.
                */
                if (!$data_invoice && $data['multiple_expiration_dates'] == false) {
                    $invoice = null;
                    $invoiceRows = [];
                    $hasBollo = false;
                }
                //
                $ed = (
                    $data['expiration_date'] ?
                    $data['expiration_date'] :
                    $ah['expiration_date']
                );


                /**
                 * Se sto generando una nuova fattura, inserisco i dati di
                 * testata del documento
                 */
                if (!$invoice) {
                    // Check state_student parameter to fix sdi_code from 0000000 -> XXXXXXX in xml for sdi
                    if( $studentState > 0 ) {
                        $mc = new McStudent();

                        $student = $mc->getStudenteById($ah['movements'][0]['subject_id'], $ah['movements'][0]['subject_school_year']);
                        if((int)$student['stato_studente_personalizzato'] == (int)$studentState) {
                            $ah['payers'][0]['sdi_code'] = 'XXXXXXX';
                        }
                    }


                    $invoice = new Invoice;
                    $invoice->setDate($d);
                    $invoice->setNumber($number);
                    $invoice->setAccountHolder(json_encode($ah['payers']));
                    $invoice->setBank(json_encode($banks));
                    $invoice->setExpirationText($expirationText);
                    $invoice->setTableText($tableText);
                    $invoice->setPaymentMethod($paymentMethod);
                    $invoice->setCreditNote($creditNote);
                    if(!empty($data['suffix'])) {
                        $invoice->setSuffix($data['suffix']);
                    }
                    if($number > 0) $number++;
                }

                /**
                 * Se il documento richiede l'applicazione del bollo, aggiungiamo un
                 * movimento di tipo bollo al totale.
                 */

                // $riga_bollo = null;
                // if ($data_invoice) {
                //     if ($invoice->getRows()) {
                //         $righe_bollo = json_decode($invoice->getRows(), true);
                //         $riga_bollo = array_filter(
                //             $righe_bollo,
                //             function ($a) {
                //                 return $a['type'] == 'Bollo';
                //             }
                //         );
                //         if ($riga_bollo) {
                //             $ah['movements'][] = $riga_bollo[0];
                //         }
                //     }
                // }

                $bollo = InvoiceQuery::create()->calcBollo($ah['movements'], $invoice);

                if ($bollo) {
                    // Non basta sapere se ci va il bollo.
                    // In caso di bollo incluso, bisogna verificare che venga tolto dalla riga giusta,
                    // ovvero al movimento con importo maggiore fra i movimenti che hanno il bollo
                    $maxBolloIdx = InvoiceQuery::create()
                    ->getMaxBolloMovementIndex($ah['movements']);

                    $ah['movements'][] = $bollo;
                    if($includeBollo) {
                        $ah['movements'][$maxBolloIdx]['amount'] -= 2;
                        $ah['movements'][$maxBolloIdx]['gross'] -= 2;
                        if($creditNote) {
                            $ah['movements'][$maxBolloIdx]['gross'] -= 2;
                            $ah['movements'][$maxBolloIdx]['amount'] -= 2;
                        }
                    } else if($creditNote) {
                        $ah['movements'][$maxBolloIdx]['gross'] -= 2;
                        $ah['movements'][$maxBolloIdx]['amount'] -= 2;
                    }
                }



                /**
                 * Totale del documento =
                 * totale dei documenti selezionati per scadenza +
                 * totale precedentemente salvato sulla fattura.
                 */
                $total = InvoiceQuery::create()->getTotal($ah['movements'], $creditNote) + $invoice->getTotal();
                /*if($bollo && $includeBollo) {
                    $total -= 2;
                }*/

                // Il calcolo del bollo è già stato fatto in precedenza sul movimento ma siccome $total viene calcolato sul movimento originale,
                // le varie sottrazioni non hanno effetto e devono essere rifatte.
                // TODO: DA RIVEDERE!
                if ($bollo) {

                    if($includeBollo) {
                        $total -= 2;
                        if($creditNote) {
                            $total -= 2;
                        }
                    } else if($creditNote) {
                        $total -= 2;
                    }
                }

                $mIds = [];


                // -------------------------- DA VERIFICARE LO SCORPORO O MENO ---------------------------- //
                /*if ($isCompany) {
                   $total += $total*0.22;
                }*/
                // -------------------------- DA VERIFICARE LO SCORPORO O MENO ---------------------------- //

                $invoiceRows = array_merge($invoiceRows, $ah['movements']);

                if ($data['multiple_expiration_dates']) {
                    $ed = 0;
                }
                foreach ($invoiceRows as $m) {
                    if (! $m['expiration_date']) {
                        continue;
                    }
                    $mIds[] = $m['id'];
                    if ($data['multiple_expiration_dates']) {
                        $ed = (
                            $ed == 0 ?
                            $m['expiration_date'] :
                            (
                                $m['expiration_date'] < $ed ?
                                $m['expiration_date']:
                                $ed
                            )
                        );
                    }
                }


                $invoice->setExpirationDate($ed);
                $invoice->setRows(json_encode($invoiceRows));

                $student = [
                    'intestatario_fatture' => 'NO'
                ];
                if(!$isCompany) {
                    $mc = new McStudent();
                    $student = $mc->getStudenteById($invoiceRows[0]['subject_id'], $invoiceRows[0]['subject_school_year']);
                }

                if($student['intestatario_fatture'] == 'SI') {
                    $header = [
                        "name"              =>$student["nome"],
                        "surname"           =>$student["cognome"],
                        "fiscal_code"       =>$student["codice_fiscale"],
                        "address"           =>$student["indirizzo"],
                        "type"              =>'S',
                        "city"              =>$student["descrizione_residenza"],
                        "province"          =>$student["provincia_residenza_da_comune"],
                        "zip_code"          =>$student["cap_residenza"],
                        "gender"            =>$student["sesso"]
                    ];
                    $invoice->setHeader(json_encode($header));
                } else {
                    $invoice->setHeader(json_encode(json_decode($invoice->getAccountHolder(), true)[0]));
                }

                $invoice->setTotal($total);

                try{
                    $invoice->save();

                    // $invoice->addToMagisterSync('inserimento');

                    if( $expirationText !== null) {
                        $par = ParameterQuery::create()
                            ->filterByName('CCP_EXPIRATION_TEXT')
                            ->findOne();
                        $par->setValue($expirationText);
                        $par->save();
                    }
                    if( $tableText !== null) {
                        $par = ParameterQuery::create()
                            ->filterByName('CCP_TABLE_TEXT')
                            ->findOne();
                        $par->setValue($tableText);
                        $par->save();
                    }

                    foreach ($mIds as $movementId) {
                        $m = MovementQuery::create()
                            ->findOneById($movementId);
                        if ($m !== null) {
                            $m->setInvoiceId(
                                $invoice->getId()
                            );
                            $tot = $m->save();
                        }
                    }

                    //-------------------------------------------------------------------------------
                    // Se è una nota di credito ed il bollo non è incluso (parametro)
                    // allora aggiungiamo già un pagamento di 2 euro al movimento in uscita maggiore
                    // in modo che NON venga ridato al genitore.
                    // In questo modo è come se lo pagasse il parente stesso
                    if(!$includeBollo && $creditNote && $bollo) {
                        $movApplyBollo = MovementQuery::create()
                            ->filterById($mIds, \Criteria::IN)
                            ->orderByAmount(\Criteria::DESC)->findOne();

                        $mc = new McStudent;

                        if($movApplyBollo->getSubjectType() == 'S') {
                            $payer = $mc->getAccountHolders($movApplyBollo->getSubjectId(), $movApplyBollo->getSubjectSchoolYear())[0];
                        } else {
                            $payer = json_decode($invoice->getAccountHolder(), true)[0];
                        }
                        $now = time();
                        $payment = new Payment;
                        $payment->setAccountId(BankAccountQuery::create()->findOneByInvoiceDefault(true)->getId());
                        $payment->setAccountableDate($now);
                        $payment->setAmount(2);
                        $payment->setMovementId($movApplyBollo->getId());
                        $payment->setOperationDate($now);
                        $payment->setPayerAddress($payer['address']);
                        $payment->setPayerCity($payer['city']);
                        $payment->setPayerFiscalCode($payer['fiscal_code']);
                        $payment->setPayerId($payer['db_id']);
                        $payment->setPayerName($payer['name']);
                        $payment->setPayerProvince($payer['province']);
                        $payment->setPayerSurname($payer['surname']);
                        $payment->setPayerType($payer['type']);
                        $payment->setPayerZipCode($payer['zip_code']);
                        $payment->setPaymentMethodId(21);
                        $payment->save();
                    }
                    //------------------------------------------------------------------------
                    $invoices[] = $invoice->getId();
                    //$results[] = $invoice->toArray(BasePeer::TYPE_FIELDNAME);


                } catch(\Exception $e) {
                    throw new \Exception(
                        "Errore durante il salvataggio della fattura numero " .
                        $number . "--> ".$e->getMessage(),
                        500
                    );
                }

            }



        }

        return $invoices;
    }


    /**
     * We check formal correctness of only italian IBAN.
     * About the other countries, we cosider the IBAN as valid.
     */
    public function checkIBAN($iban) {
        $iban = strtolower(str_replace(' ','',$iban));
        if (!$iban) return false;

        if (strpos($iban, 'it') !== 0) return true;

        $Countries = ['al'=>28,'ad'=>24,'at'=>20,'az'=>28,'bh'=>22,'be'=>16,'ba'=>20,'br'=>29,'bg'=>22,'cr'=>21,'hr'=>21,'cy'=>28,'cz'=>24,'dk'=>18,'do'=>28,'ee'=>20,'fo'=>18,'fi'=>18,'fr'=>27,'ge'=>22,'de'=>22,'gi'=>23,'gr'=>27,'gl'=>18,'gt'=>28,'hu'=>28,'is'=>26,'ie'=>22,'il'=>23,'it'=>27,'jo'=>30,'kz'=>20,'kw'=>30,'lv'=>21,'lb'=>28,'li'=>21,'lt'=>20,'lu'=>20,'mk'=>19,'mt'=>31,'mr'=>27,'mu'=>30,'mc'=>27,'md'=>24,'me'=>22,'nl'=>18,'no'=>15,'pk'=>24,'ps'=>29,'pl'=>28,'pt'=>25,'qa'=>29,'ro'=>24,'sm'=>27,'sa'=>24,'rs'=>22,'sk'=>24,'si'=>19,'es'=>24,'se'=>24,'ch'=>21,'tn'=>24,'tr'=>26,'ae'=>23,'gb'=>22,'vg'=>24];
        $Chars = array('a'=>10,'b'=>11,'c'=>12,'d'=>13,'e'=>14,'f'=>15,'g'=>16,'h'=>17,'i'=>18,'j'=>19,'k'=>20,'l'=>21,'m'=>22,'n'=>23,'o'=>24,'p'=>25,'q'=>26,'r'=>27,'s'=>28,'t'=>29,'u'=>30,'v'=>31,'w'=>32,'x'=>33,'y'=>34,'z'=>35);

        if(strlen($iban) == $Countries[substr($iban,0,2)] and substr($iban,0,2)=='it'){

            $MovedChar = substr($iban, 4).substr($iban,0,4);
            $MovedCharArray = str_split($MovedChar);
            $NewString = "";


            foreach($MovedCharArray AS $key => $value){

                if(!is_numeric($MovedCharArray[$key])){
                    $MovedCharArray[$key] = $Chars[$MovedCharArray[$key]];

                    #$NewString .= $Chars[$value];
                }
                $NewString .= $MovedCharArray[$key];
            }

            $x = $NewString; $y = "97";
            $take = 5; $mod = "";

            do {
                $a = (int)$mod . substr($x, 0, $take);
                $x = substr($x, $take);
                $mod = $a % $y;
            }
            while (strlen($x));

            return (int)$mod == 1;
        }
        return false;
    }

    public function getInvoiceRows(
        $movementIds,
        $multiple_expiration_dates = false,
        $data = []
    ) {
        $tipoAddebito  = null;
        $accountHolders = [];

        $movements = MovementQuery::create()->filterById($movementIds, \Criteria::IN)->orderByAmount('desc')->find();

        #foreach ($movementIds as $movementId) {
        foreach ($movements as $movement) {
            #$movement = MovementQuery::create()
            #    ->findOneById($movementId);

            $isCompany = false;
            if (isset($data['accountholder'])) {
                $payers = [$data['accountholder']];
                $isCompany = true;
            } else {
                $student = new McStudent();
                $payers = $student->getAccountHolders($movement->getSubjectId(), $movement->getSubjectSchoolYear());

                $tipoAddebito = (
                    $data['payment_method'] ? PaymentMethodQuery::create()
                    ->filterById($data['payment_method'] )
                    ->findOne()
                    ->getEasyCode() : null
                );
            }

            $idPayers = [];
            foreach ($payers as $payer) {
                $errors = [];
                if(!$payer['address']) $errors[] = "Indirizzo non valorizzato";
                if(!$payer['zip_code'] or strlen(trim($payer['zip_code']))>5 ) $errors[] = "CAP non valorizzato o errato";
                if(!$payer['city']) $errors[] = "Città di residenza non valorizzata";
                if(!$payer['province']) $errors[] = "Provincia di residenza non valorizzata";
                if(!$payer['fiscal_code']) $errors[] = "Codice fiscale non valorizzato";

                if($tipoAddebito) {
                    $payer['tipo_addebito'] = $tipoAddebito;
                }

                if($payer['tipo_addebito'] == 1) {
                    if(!$this->checkIBAN($payer['iban'])) $errors[] = "IBAN non valorizzato o non valido";
                    if(!$payer['codice_rid']) $errors[] = "Codice RID non valorizzato";
                    if(!$payer['data_mandato_rid']) $errors[] = "Data mandato non valorizzata";
                }

                if( count($errors)>0 ) {
                    $message = "Nell'intestatario ".$payer['surname']." ".$payer['name']." mancano dei dati necessari alla fattura: ". join(", ", $errors );

                    if( $isCompany === true ) {
                        $message = "Nell'intestatario mancano dei dati necessari alla fattura: ". join(', ', $errors );
                    }
                    throw new \Exception($message, 400);
                }
                $idPayers[] = $payer['db_id'];
            }
            $idStr = join('_', $idPayers);

            $join_brothers = ParameterQuery::create()->findOneByName('CCP_INVOICE_JOIN_BROTHERS')->getValue() === 't';
            $idStr .= $join_brothers === true ? '' : '_'.$payers[0]['id_studente'];
            if (!isset($accountHolders[$idStr])) {
                $accountHolders[$idStr] = [];
            }

            // Forziamo il fatto che per ogni movimento ci sia una fattura
            if(isset($data['force_split']) && $data['force_split'] === 1) {
                $idStr = uniqid();
            }

            // Se forziamo la fattura singola allora inibiamo il controllo sulla scadenza
            // Di fatto nella fattura finiranno i movimenti di scadenze diverse, ovviamente se l'iban del paganete è lo stesso
            // Aggiungiamo a queste 2 casistiche anche il fatto che la scuola possa forzare la separazione delle fatture x fratelli
            // In questo caso anche se la scadenza e l'iban coincidono, in caso di fratelli vengono generate più fatture, una per fratello
            $expDate = $multiple_expiration_dates === true ? '' : date('Ymd', $movement->getExpirationDate()).'_';

            $expDate .= $payers[0]['iban'];

            if (!isset($accountHolders[$idStr][$expDate])) {
                $accountHolders[$idStr][$expDate] = [];
            }

            $accountHolders[$idStr][$expDate]['payers'] = $payers;
            $accountHolders[$idStr][$expDate]['expiration_date'] = date(
                'c',
                $movement->getExpirationDate()
            );

            if(!$accountHolders[$idStr][$expDate]['include_bollo']) {
                $accountHolders[$idStr][$expDate]['include_bollo'] = false;
            }
            if(!$movement->getType()->getIncludeVat() and $movement->getType()->getBollo()) {
                $accountHolders[$idStr][$expDate]['include_bollo'] = $movement->getType()->getIncludeBollo();
            }

            $mov = $movement->toArray(BasePeer::TYPE_FIELDNAME);
            // $mov['type'] = $movement->getType()->getName();
            $mov['type'] = $movement->getDescrizioneMovimento();
            $mov['bollo'] = $movement->getCalcoloBollo();
            $mov['include_bollo'] = $movement->getType()->getIncludeBollo();

            $mov['vat'] = $movement->getType()->getVat();
            $mov['gross'] = $movement->getVatDivision('gross');
            $mov['vat_abs'] = $movement->getVatDivision('vat_abs');

            $mov['vat_code'] = $movement->getVatCode();
            $mov['additionals'] = MovementAdditionalQuery::create()
                            ->withColumn(BaseMovementAdditionalPeer::SUBJECT_TYPE, 'subject_type')
                            ->withColumn(BaseMovementAdditionalPeer::SUBJECT_ID, 'subject_id')
                            ->joinAdditional()
                            ->where(
                                BaseMovementAdditionalPeer::MOVEMENT_ID .
                                " = " . $movement->getId()
                            )

                            ->withColumn(BaseAdditionalPeer::NAME, 'name')
                            ->withColumn(BaseAdditionalPeer::POSITIVE, 'positive')
                            ->withColumn(
                                BaseAdditionalPeer::PERCENTUAL,
                                'percentual'
                            )
                            ->withColumn(BaseAdditionalPeer::CODE, 'code')
                            ->select(['id','amount','abs_amount', 'name', 'positive', 'percentual', 'code',])
                            ->orderBy('id')
                            ->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);

            if (!$isCompany) {

                $student = new McStudent();
                $s = $student->getStudenteById($movement->getSubjectId(), $movement->getSubjectSchoolYear());

                if($s) {
                    $mov['subject_class'] = $s['classe'];
                }
            }

            $accountHolders[$idStr][$expDate]['movements'][] = $mov;
        }

        return [$accountHolders, $payers];
    }

}
