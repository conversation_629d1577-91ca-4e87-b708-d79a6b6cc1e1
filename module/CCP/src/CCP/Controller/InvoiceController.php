<?php

namespace CCP\Controller;

use CCP\Model\DataServiceStatus;
use Database\CCP\InvoiceDepositSlip;
use MT\Logger\LoggerZend,
    MT\Sencha\Response,
    Database\Core\ParameterQuery,
    Database\Core\InstituteQuery,
    Database\Core\BankAccountQuery,
    Database\CCP\Invoice,
    Database\CCP\MovementQuery,
    Database\CCP\InvoiceQuery,
    Database\CCP\InvoiceTransmissionQuery,
    Database\CCP\InvoiceViewQuery,
    Database\CCP\InvoiceViewPeer,
    Database\CCP\MovementAdditionalQuery,
    Database\CCP\InvoiceDepositSlipQuery,
    Database\CCP\DepositSlipQuery,
    Database\CCP\om\BaseInvoicePeer,
    Database\CCP\om\BaseMovementAdditionalPeer,
    Database\CCP\om\BaseAdditionalPeer,
    Core\Model\Session\Session,
    CCP\Model\McStudent,
    BasePeer,
    Criteria,
    Zend\Http\Request,
    Zend\Mvc\Controller\AbstractRestfulController,
    Zend\View\Model\JsonModel;

class InvoiceController extends AbstractRestfulController {

    protected $logger;
    protected $session_auth;

    public function __construct() {
        $this->logger = new LoggerZend;
        $this->session_auth = Session::readSessionAuth();
    }

    public function get($id) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $response->setSuccess(true);
            $response->results = InvoiceQuery::create()->findPk($id)->toArray(BasePeer::TYPE_FIELDNAME);
        }
        return new JsonModel((array) $response);

    }

    public function update($id, $data) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $response->setSuccess(true);
            $invoice = InvoiceQuery::create()->findPk($id);

            $dataAh = $data['account_holder'][0];
            $dataAh['tipo_addebito'] = $dataAh['payment_method'];
            unset($dataAh['payment_method']);

            $invoiceAh = json_decode($invoice->getAccountHolder(),true)[0];
            $ah = array_replace($invoiceAh, $dataAh);
            $ah['data_mandato_rid'] = strtotime($ah['data_mandato_rid']);
            $invoice->setAccountHolder(json_encode([$ah]));

            if($invoice->getPaymentMethod() != $data['payment_method'] and $data['payment_method']>0) {
                $invoice->setPaymentMethod($data['payment_method']);
            }

            $dataHeader = $data['header'];
            $invoiceHeader = json_decode($invoice->getHeader(),true);
            $header = array_replace($invoiceHeader, $dataHeader);
            $invoice->setHeader(json_encode($header));

            $invoice->save();
            $response->results = $invoice;
        }

        return new JsonModel((array) $response);

    }

    public function getDistinctSuffixAction() {
        $response = new Response;
        if ($this->session_auth['uid'] > 0) {
            $response->setSuccess(true);
            $response->results = InvoiceQuery::create()->select(['suffix'])->distinct()->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);
        }
        return new JsonModel((array) $response);
    }

    public function getList() {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $mc = new McStudent();
            $response->setSuccess(true);

            $filter = $this->params()->fromQuery();
            $invoices = InvoiceQuery::create()->getList($filter);
            foreach ($invoices as $key => $invoice) {
                $rows = json_decode($invoice['rows'], true);
                $accountholder = json_decode($invoice['accountholder'], true);
                $bank = json_decode($invoice['bank'], true);
                $invoices[$key]['accountholder'] = $accountholder;
                $invoices[$key]['rows'] = $rows;
                $invoices[$key]['bank'] = $bank;

                $fpaStatus = $invoice['xml_name'] ? 'SN' : null;
                if ($fpaStatus) {
                    $tr = InvoiceTransmissionQuery::create()->filterByCcpInvoice($invoice['id'])->orderByDate(\Criteria::DESC)->findOne();
                    $fpaStatus = $tr ? $tr->getStatus() : $fpaStatus;

                    if ($fpaStatus == 'NE') {
                        $fpaStatus = strpos($tr->getDescription(), 'EC01') !== FALSE ? 'NEA' : 'NER';
                    }
                }
                $invoices[$key]['fpa_status'] = $fpaStatus;
                $dsStatus = $invoice['ds_id'] ? 'wait' : null;
                $tr = InvoiceTransmissionQuery::create()->filterByCcpInvoice($invoice['id'])->orderByDate(\Criteria::DESC)->findOne();
                if($tr) {
                    $dsS = new DataServiceStatus($tr);
                    $dsStatus = $dsS->getStatusType();
                }
                $invoices[$key]['ds_status'] = $dsStatus;

                // In filter for deposit slip, we give the expiration date of movements into rows
                if((bool) $filter['deposit_slip'] === true) {
                    $expirationStart = $filter['expiration_date_start'] ? strtotime($filter['expiration_date_start']) : null;
                    $expirationEnd = $filter['expiration_date_end'] ? strtotime($filter['expiration_date_end'] . ' +1 days') : null;
                    $invoiceObj = InvoiceQuery::create()->findPk($invoice['id']);
                    $invoices[$key]['expirations'] = $invoiceObj->getExpirationDates($expirationStart,$expirationEnd);

                    $invoices[$key]['accountholder'] = $mc->getAccountHolders((int) $rows[0]['subject_id'], $rows[0]['subject_school_year'])[0];

                    if(count($invoices[$key]['expirations'])==0) {
                        unset($invoices[$key]);
                        unset($invoices[$key]);
                        continue;
                    }

                    $invoices[$key]['total_expirations'] = $invoiceObj->getExpirationDates(null, null, true);
                    $invoices[$key]['error_expirations'] = $invoiceObj->getMissingExpirationDates($expirationStart);
                    $dsData = $invoiceObj->getTotalDepositSlip($expirationStart,$expirationEnd);
                    $total = $dsData[0];
                    $bollo = $dsData[1];
                    $invoices[$key]['remain_to_pay'] = $total+$bollo;
                }
            }

            $response->total = InvoiceQuery::create()->getList($filter, true);
            $response->results = array_values($invoices)  ;
        }

        return new JsonModel((array) $response);
    }

    public function create($data) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            try {
                $response->results = InvoiceQuery::create()->generateInvoice($data);
            } catch (\Exception $e) {
                $response->message = $e->getMessage();
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }
            $this->logger->log(array(
                'Type'    => 'INFO',
                'Scope'   => __FILE__,
                'Event'   => basename(__FILE__, ".php"),
                'Message' => "Creazione Fattura/e",
                'Context' => print_r($data, true)
            ));
            $response->setSuccess(true);
        }

        return new JsonModel((array) $response);
    }


    public function delete($id) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $invoice = InvoiceQuery::create()->findPk($id);

            if($invoice->getInvoiceDepositSlips()->count()>0) {
                $response->message = 'La fattura non può essere cancellata in quanto legata ad una distinta';
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }

            if ($invoice !== null) {
                // Logging
                $this->logger->log(array(
                    'Type'    => 'INFO',
                    'Scope'   => __FILE__,
                    'Event'   => basename(__FILE__, ".php"),
                    'Message' => "Eliminazione Fattura: {$invoice->getId()};",
                    'Context' => print_r($id, true)
                ));

                $data = $invoice->toArray(BasePeer::TYPE_FIELDNAME);

                // $invoice->addToMagisterSync('eliminazione');

                if($invoice->getPublicationPath()) {
                    $invoice->unpublish();
                }
                $invoice->delete();

                $response->results = $data;
                $response->setSuccess(true);
            } else {
                $response->message = 'Invoice not found';
            }
        }

        return new JsonModel((array) $response);
    }

    public function paramsAction() {
        $response = new Response;
        $response->setSuccess(true);

        if ($this->session_auth['uid'] > 0) {
            $response->results = ParameterQuery::create()
                ->filterByName(['CCP_EXPIRATION_TEXT', 'CCP_TABLE_TEXT'], \Criteria::IN)
                ->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);
        }

        return new JsonModel((array) $response);
    }

    public function getNextNumberAction() {
        $response = new Response;
        $response->setSuccess(true);

        if ($this->session_auth['uid'] > 0) {
            $response->results = $this->getMaxNumber(date('Y-m-d', time())) + 1;
        }

        return new JsonModel((array) $response);
    }

    private function getMaxNumber($date) {

        $date = strtotime($date);
        $min = date('c', strtotime('01-01-' . date('Y', $date)));
        $max = date('c', strtotime('01-01-' . (date('Y', $date) + 1)));

        $invoice =  InvoiceQuery::create()
                        ->withColumn('max(' . BaseInvoicePeer::NUMBER . ')', 'maxnum')
                        ->condition('min', BaseInvoicePeer::DATE . ' >= ?', $min)
                        ->condition('max', BaseInvoicePeer::DATE . ' < ?', $max)
                        ->where(array('min', 'max'), Criteria::LOGICAL_AND)
                        ->select('maxnum')
                        ->findOne();

        return $invoice ? $invoice : 0;
    }

    public function sendToFPAAction() {

        if ($this->session_auth['uid'] > 0) {
            $response = new Response;
            $step = $this->params()->fromQuery('step');

            if ($step == 'xml') { // Only zip file download
                $ids = json_decode($this->params()->fromQuery('ids'), true);
                $filters = json_decode($this->params()->fromQuery('filters'), true);

                if(empty($ids)) {
                    $invoices = InvoiceQuery::create()->getList($filters);
                    foreach($invoices as $invoice) {
                        $ids[] = $invoice['id'];
                    }
                }

                $invoices = InvoiceQuery::create()->filterById($ids, \Criteria::IN)->find();

                if ($invoices->count() < 1 ) {
                    exit('Nessuna fattura selezionata');
                }

                try {
                    $sourcePath = InvoiceQuery::create()->generateXmlFPA($invoices);
                    $zipPath    = InvoiceQuery::create()->generateZipXmlFPA($sourcePath);
                } catch (\Exception $e) {
                    exit($e->getMessage());
                }

                if (file_exists($zipPath)) {
                    $name = basename($zipPath);

                    $response = $this->getResponse();
                    $headers = $response->getHeaders();
                    $response->setContent(file_get_contents($zipPath));
                    $headers->addHeaderLine("Cache-Control", "no-cache");
                    $headers->addHeaderLine("Content-type", mime_content_type($zipPath));
                    $headers->addHeaderLine("Content-Length", filesize($zipPath));
                    $headers->addHeaderLine("Content-Disposition", "inline; filename=\"{$name}\"");

                    unlink($zipPath);
                    return $response;
                }

            } else if ($step == 'ds') { // Data service one zip for invoice
                $ids = json_decode($this->params()->fromPost('ids'), true);
                $filters = json_decode($this->params()->fromPost('filters'), true);
                if(empty($ids)) {
                    $invoices = InvoiceQuery::create()->getList($filters);
                    foreach($invoices as $invoice) {
                        $ids[] = $invoice['id'];
                    }
                }


                exec('php /var/www/mc2-api/index.php send-invoice-ds ' . join($ids, ','). ' > /dev/null 2>&1 &');
                $response->setSuccess(true);
                return new JsonModel((array) $response);
            }


            $alias = $this->params()->fromPost('alias');
            $pin = $this->params()->fromPost('pin');
            $invoices = InvoiceQuery::create()->filterById($ids, \Criteria::IN)->find();

            if(!$alias || !$pin) {
                $response->setSuccess(false);
                $response->message = 'Alias e pin sono obbligatori';
                return new JsonModel((array) $response);
            }

            if ($invoices->count() < 1 ) {
                $response->setSuccess(false);
                $response->message = 'Nessuna fattura selezionata';
                return new JsonModel((array) $response);
            }

            try {
                InvoiceQuery::create()->sendMailFPA($invoices, $this->session_auth['uid'], $alias, $pin);
            } catch (\Exception $e) {
                $response->setSuccess(false);
                $response->setStatus($e->getCode());
                $response->message = $e->getMessage();
                return new JsonModel((array) $response);
            }

            $response->setSuccess(true);
        }
        return new JsonModel((array) $response);

    }

    public function creditNoteAction() {
        $response = new Response;
        if ($this->session_auth['uid'] > 0 ) {
            $invoiceId = $this->params()->fromPost('id');

            $d = date('c', time());
            $number = $this->getMaxNumber($d) + 1;

            $invoice = InvoiceQuery::create()->findOneById($invoiceId);
            $creditNote = $invoice->copy();
            $creditNote->setNumber($number);
            $creditNote->setDate($d);
            // $creditNote->setExpirationDate(null); PAOLO - Senza data scadenza non vengono elencate. Mettiamo scadenza fattura originaria
            $creditNote->setExpirationDate($invoice->getExpirationDate());
            $creditNote->setCreditNote(true);
            $creditNote->setXmlName(null);
            $creditNote->setDsName(null);
            $creditNote->setDsId(null);
            //PAOLO - azzeriamo publication path altrimenti la nota risulta pubblicata quando non esiste nessun documento
            $creditNote->setPublicationPath(null);
            $creditNote->save();

            MovementQuery::create()->filterByInvoice($invoice)->update(['InvoiceId' =>  null]);

            $response->setSuccess(true);
        }

        return new JsonModel((array) $response);
    }


    /**
     * Function publish invoice for site/app
     *
     * @return void
     */
    public function publishAction() {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {

            $massive = (bool) $this->params()->fromPost('massive');
            $idsStr = $this->params()->fromPost('ids', null);
            $ids = [];
            if($idsStr) {
                $ids = json_decode($idsStr);
            }

            if($massive === true) {
                if(empty($ids)) {
                    $filter = $this->params()->fromPost();

                    $invoices = InvoiceQuery::create()->getList($filter);
                    foreach ($invoices as $i) {
                        $ids[] = $i['id'];
                    }
                }

                $args = join(',', $ids);
                exec("php /var/www/mc2-api/index.php invoice-massive publish $args > /dev/null 2>&1 &");

                $response->setSuccess(true);

            } else {

                $invoiceId =  $ids[0];
                $invoice = InvoiceQuery::create()->findOneById($invoiceId);
                // Non esiste la fattura
                if ($invoice === null) {
                    $response->message = 'Fattura non trovata';
                    $response->setSuccess(false);
                    return new JsonModel((array) $response);
                }

                try {
                    $config = $this->getServiceLocator()->get('Config');
                    $invoice->publish($config['PATHS']['INVOICES']['DOCUMENTS']);
                    $response->setSuccess(true);
                } catch (\Exception $e) {
                    $response->message = $e->getMessage();
                    $response->setSuccess(false);
                    return new JsonModel((array) $response);
                }
            }

        }
        return new JsonModel((array) $response);
    }

    /**
     * Function unpublish invoice from site/app
     *
     * @return void
     */
    public function unpublishAction() {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {

            $massive = (bool) $this->params()->fromPost('massive');
            $idsStr = $this->params()->fromPost('ids', null);
            $ids = [];
            if($idsStr) {
                $ids = json_decode($idsStr);
            }

            if($massive === true) {
                if(empty($ids)) {
                    $filter = $this->params()->fromPost();

                    $invoices = InvoiceQuery::create()->getList($filter);
                    foreach ($invoices as $i) {
                        $ids[] = $i['id'];
                    }
                }

                $args = join(',', $ids);
                exec("php /var/www/mc2-api/index.php invoice-massive unpublish $args > /dev/null 2>&1 &");

                $response->setSuccess(true);

            } else {

                $invoiceId =  $ids[0];
                $invoice = InvoiceQuery::create()->findOneById($invoiceId);
                if ($invoice === null) {
                    $response->message = 'Fattura non trovata';
                    $response->setSuccess(false);
                    return new JsonModel((array) $response);
                }

                try {
                    $invoice->unpublish();
                    $response->setSuccess(true);
                } catch (\Exception $e) {
                    $response->message = $e->getMessage();
                    $response->setSuccess(false);
                    return new JsonModel((array) $response);
                }

            }

        }
        return new JsonModel((array) $response);
    }

    /**
     * Function addMovementsAction
     * Permette di aggiungere movimenti a fatture esistenti
     * Restituisce eccezioni se:
     * - Fattura già inviata SDI;
     * - Errore salvataggio.
     * - Fattura già pubblicata
     *
     * @return void
     */
    public function addMovementsAction() {
        $response = new Response;
        $request = $this->getRequest();

        if ($this->session_auth['uid'] > 0 && $request->isPost()) {
            $data = [];
            $payload = json_decode($request->getContent(), true);

            $invoice_id = $payload['invoice_id'];
            $movements = $payload['movements'];


            $invoice = InvoiceQuery::create()->findOneById($invoice_id);

            if($invoice->getPublicationPath()) {
                $response->message = 'Fattura già pubblicata. Prima di procedere rimuovere la pubblicazione.';
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }

            // Non esiste la fattura
            if ($invoice === null) {
                $response->message = 'Fattura non trovata';
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }

            // No modifica Se già inviata a SDI
            if ($invoice->getXmlName() != '' && $invoice->getXmlName() != null) {
                $response->message = 'Fattura già inviata SDI';
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }


            // No modifica Se è già stata inviata a SEPA no modifica
            $deposit_slip = InvoiceDepositSlipQuery::create()
                ->filterByCcpInvoiceId($invoice_id)
                ->findOne();

            if (!$deposit_slip == null) {
                $deposit_slip = DepositSlipQuery::create()
                    ->filterById($deposit_slip->getCcpDepositSlipId())
                    ->findOne();

                $deposit_slip_number = $deposit_slip->getNumber();
                $deposit_slip_date = date('d-m-Y', strtotime($deposit_slip->getDate()));
                $response->message = 'Fattura già inclusa nella distinta di incasso ' . $deposit_slip_number . ' del ' . $deposit_slip_date . '.';
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }



            $data['invoice_id'] = $invoice_id;
            $data['number'] = $invoice->getNumber();

            $invoice_movements = MovementQuery::create()
                ->select(['id'])
                ->findByInvoiceId($invoice_id);

            foreach ($invoice_movements as $m) {
                if (!in_array($m, $movements)) {
                    $movements[] = $m;
                }
            }

            $data['linked_movements'] = $movements;

            try {
                InvoiceQuery::create()->generateInvoice($data);
                $log = $data;
                $log['nuovi_movimenti'] = $payload['movements'];
                $this->logger->log(
                    [
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Aggiunti movimenti a fattura",
                        'Context' => print_r($data, true)
                    ]
                );
                $response->setSuccess(true);
            } catch (\Exception $e) {
                $response->message = $e->getMessage();
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }
        }
        return new JsonModel((array) $response);
    }
}
