<?php

// Index file for MC2
header('Content-type: text/html; charset=utf-8');
session_start();


// Imports INIT scripts
require_once dirname(__FILE__) . '/configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';
require_once PATH_ROOT . 'configurations/init-api.php';

if (!isset($_SESSION['uid']) || (int) $_SESSION['uid'] < 1 ) { // TODO: non far caricare i tree store prima di checkUserSession
    if($_SERVER['REMOTE_ADDR'] == '127.0.0.1'){
        $_SESSION['uid'] = 1;
    } else {
        session_destroy();
        exit(Response::Create([
                    'success' => false,
                    'status'  => 402
        ]));
    }
}

// Sets error reporting for all errors
error_reporting(E_ALL ^ E_NOTICE);

// Checks if request is regular for permission and, in case, parsing it
require_once PATH_ROOT . 'applications/core/checkRequest.php';

// Imports finction for check if user is logged
require_once PATH_ROOT . 'applications/core/utils.php';

// Imports smarty and tcpdf file
require_once PATH_ROOT . 'configurations/init-smarty.php';
require_once PATH_ROOT . 'configurations/init-tcpdf.php';


// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");


// Logger path
require_once "/var/www/logger/logger.php";
