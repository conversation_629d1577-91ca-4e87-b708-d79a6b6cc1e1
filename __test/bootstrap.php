<?php

session_start();

define('DB_HOST', 'localhost');
define('DB_NAME', 'mastercom2_test');
define('DB_USER', 'postgres');
define('DB_PASSWD', 'postgres');

define('DB_PORT', 5432);
define('LOGGER_DB_PORT', 5432);

// LOGGER Database data
define('LOGGER_DB_HOST', 'localhost');
define('LOGGER_DB_NAME', 'logger');
define('LOGGER_DB_USER', 'postgres');
define('LOGGER_DB_PASSWORD', 'postgres');

error_reporting(E_ALL ^ E_NOTICE);
define('DB_TEST_NAME', 'mastercom2_test');
define('PATH_ROOT', __DIR__ . '/../');
define('PATH_TMP_URL', '/mc2/');
define('PATH_TMP_ROOT', '/tmp/');
define('DB_ERR_CONNECTION', 2001);
define('DB_ERR_QUERY', 2002);
define('DB_OBJ_NOT_FOUND', 2012);
define('PATH_PROPEL', '/var/www/mastertek-api/php-propel/latest/');
define('PATH_MC_DB', '/etc/mastercom/database_test.conf');


//require_once PATH_ROOT . '../libs/tcpdf/tcpdf.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'applications/core/utils.php';

// PRESENCES
define('PRESENCE_ENTRANCE', 1);
define('PRESENCE_EXIT', 2);
define('PRESENCE_NORMAL', 1);
define('PRESENCE_LUNCH', 2);
define('PRESENCE_SERVICE', 3);

// EMPLOYEE's PARAMETERS
define('EMPLOYEE_PARAM_RETRIBUTION_UNIT_PERC', '%');
define('EMPLOYEE_PARAM_RETRIBUTION_UNIT_MINS', 'min');


// ABSENCE STACK
define('ABS_STACK_UNIT_DAILY', 'd');
define('ABS_STACK_UNIT_HOURLY', 'h');

require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(__DIR__ . "/mc2api-conf.php");

function load_fixtures($fixtures = array()) {
    if (!is_array($fixtures)) {
        $fixtures = array($fixtures);
    }
    foreach ($fixtures as $fixture) {
        if (file_exists(PATH_ROOT . "__test/fixtures/" . $fixture . ".sql")) {
            exec("psql -U postgres " . DB_TEST_NAME . " < " . PATH_ROOT . "__test/fixtures/" . $fixture . ".sql");
        } else {
            print 'Installing file for fixtures ' . $fixture . ' does not exists';
        }
    }
}

function remove_fixtures($fixtures = array()) {
    if (!is_array($fixtures)) {
        $fixtures = array($fixtures);
    }
    foreach ($fixtures as $fixture) {
        if (file_exists(PATH_ROOT . "__test/fixtures/" . $fixture . "_del.sql")) {
            exec("psql -U postgres " . DB_TEST_NAME . " < " . PATH_ROOT . "__test/fixtures/" . $fixture . "_del.sql");
        } else {
            print 'Removing file for fixtures ' . $fixture . ' does not exists';
        }
    }
}

$_SESSION['uid'] = 1;

