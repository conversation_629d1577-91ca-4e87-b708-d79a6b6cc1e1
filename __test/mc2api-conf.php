<?php


$conf = array(
	'datasources'		 =>
	array(
		'mc2api'				 =>
		array(
			'adapter'	 => 'pgsql',
			'connection' =>
			array(
				'dsn'		 => 'pgsql:host=localhost;dbname=mastercom2_test;port=5432',
				'user'		 => 'postgres',
				'password'	 => 'postgres',
			),
		),
                'mastercom_test'				 =>
		array(
			'adapter'	 => 'pgsql',
			'connection' =>
			array(
				'dsn'		 => 'pgsql:host=localhost;dbname=mastercom_test;port=5432',
				'user'		 => 'postgres',
				'password'	 => 'postgres',
			),
		),
		'default'				 => 'mc2api',
	),
	'generator_version'	 => '1.7.0-dev',
);
return $conf;