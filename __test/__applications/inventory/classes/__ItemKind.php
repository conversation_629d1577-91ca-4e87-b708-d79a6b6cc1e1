<?php

/**
 * Test class for ItemKind.
 * Generated by PHPUnit on 2012-11-26 at 17:32:51.
 */
class ItemKindTest extends PHPUnit_Framework_TestCase
{

    /**
     * @var ItemKind
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp()
    {

    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown()
    {

    }

    /**
     * @todo Implement testGetMuDisplay().
     */
    public function testGetMuDisplay()
    {
	$this->object = new ItemKind(90);
	$this->object->read();
	$this->assertEquals(
		'KG', $this->object->getMuDisplay()
	);
    }

    /**
     * @todo Implement testIsInventariable().
     */
    public function testIsInventariable()
    {
	$this->object1 = new ItemKind(91);
	$this->assertEquals(
		true, $this->object1->isInventariable()
	);
    }

    public function testIsInventariable1()
    {
	$this->object1 = new ItemKind(91);
	$this->assertEquals(
		true, $this->object1->isInventariable()
	);
    }

    /**
     * @todo Implement testGetClass().
     */
    public function testGetClassNumber()
    {
	$this->object = new ItemKind(90);
	$this->object->read();
	$this->assertEquals(
		1, $this->object->getClassNumber()
	);
    }

    public function testGetClassNumber1()
    {
	$this->object = new ItemKind(91);
	$this->object->read();
	$this->assertEquals(
		3, $this->object->getClassNumber()
	);
    }

}
