<?php

/**
 * Test class for Item.
 * Generated by PHPUnit on 2012-11-26 at 17:45:31.
 */
class ItemTest extends PHPUnit_Framework_TestCase
{

    /**
     * @var Item
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp()
    {
	$this->db = Db::getInstance();
	$this->db->query("DELETE FROM wh_item");
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown()
    {

    }

    /**
     * @todo Implement testGetOrderRow().
     */
    public function testGetOrderRow()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @todo Implement testGetOrder().
     */
    public function testGetOrder()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @todo Implement testGetResponsableName().
     */
    public function testGetResponsableName()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @todo Implement testGetLocationName().
     */
    public function testGetLocationName()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    public function testInPrevYearCharged1()
    {
	$item = new Item();
	$data = array(
	    "respons" => 35,
	    "kind" => 90,
	    "value" => 236.34,
	    "quantity" => 1,
	    "inv_number" => 1,
	    "charge_date" => strtotime("1 January 2012")
	);
	$item->load($data);
	$item->save();

	$this->assertFalse(
		$item->inPrevYearCharged(2012)
	);
    }

    public function testTotalValue()
    {
	$item = new Item();
	$data = array(
	    "respons" => 35,
	    "kind" => 90,
	    "value" => 110,
	    "quantity" => 1,
	    "inv_number" => 1,
	    "charge_date" => strtotime("1 January 2012")
	);
	$item->load($data);
	$item->save();
	$this->assertEquals(
		110, $item->totalValue()
	);
    }

    public function testTotalValue1()
    {
	$item = new Item();
	$data = array(
	    "respons" => 35,
	    "kind" => 90,
	    "value" => 102,
	    "quantity" => 2,
	    "inv_number" => 1,
	    "charge_date" => strtotime("1 January 2012")
	);
	$item->load($data);
	$item->save();
	$this->assertEquals(
		204, $item->totalValue()
	);
    }

}
