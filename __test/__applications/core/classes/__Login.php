<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-04-23 at 12:49:25.
 */
class LoginTest extends PHPUnit_Framework_TestCase
{

    /**
     * @var Login
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp()
    {
	$user = new User;
	$user->user_name = 'prova';
	$user->user_password = md5('');
	$user->enabled = 1;
	$user->save();
	$user->setPassword('prova');
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown()
    {
	$db = Db::getInstance();
	$db->query("DELETE FROM users");
    }

    /**
     * @covers Login::authentication
     * @todo   Implement testAuthentication().
     */
    public function testAuthentication2()
    {
	$login = new Login('prova', 'prova');
	$user = $login->authentication();
	$this->assertEquals($user->user_name, 'prova');
    }

    /**
     * @covers Login::authentication
     * @todo   Implement testAuthentication().
     */
    public function testAuthentication()
    {
	$login = new Login('prova', 'prova1');
	$user = $login->authentication();
	$this->assertFalse($user);
    }

    /**
     * @covers Login::authentication
     * @todo   Implement testAuthentication().
     */
    public function testAuthentication3()
    {
	$login = new Login('prova1', 'prova');
	$user = $login->authentication();
	$this->assertFalse($user);
    }

}
