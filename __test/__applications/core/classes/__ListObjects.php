<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-07 at 08:39:54.
 */
class ListObjectsTest extends PHPUnit_Framework_TestCase {

    /**
     * @var ListObjects
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp() {
        $this->employees = new Employees;
        $db = Db::getInstance();
        $db->query("DELETE FROM employee");
        $db->query("INSERT INTO employee (surname, name) VALUES ('s1','n1')");
        $db->query("INSERT INTO employee (surname, name) VALUES ('s2','n2')");
        $db->query("INSERT INTO employee (surname, name) VALUES ('s3','n3')");
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown() {

    }

    /**
     * @covers ListObjects::filter
     * @todo   Implement testFilter().
     */
    public function testFilter() {
        $this->employees->filter(array(
            "surname ILIKE '%2%'"
            ));
        $arr = $this->employees->get();
        $this->assertEquals(1, count($arr));
        $this->assertContains('s2', $arr[0]);
        $this->assertContains('n2', $arr[0]);
        $this->assertNotContains('n3', $arr[0]);
        $this->assertNotContains('d1', $arr[0]);
    }

    /**
     * @covers ListObjects::clearFiter
     * @todo   Implement testClearFiter().
     */
    public function testClearFiter() {
        $this->employees->filter(array(
            "surname ILIKE '%2%'"
            ));
        $arr = $this->employees->get();
        $this->assertEquals(1, count($arr));

        $this->employees->clearFiter();
        $arr2 = $this->employees->get();
        $this->assertEquals(3, count($arr2));
    }

    /**
     * @covers ListObjects::orderBy
     * @todo   Implement testOrderBy().
     */
    public function testOrderBy() {
        $this->employees->orderBy(array(
            "name DESC"
            ));

        $arr = $this->employees->get();
        $this->assertContains('n3', $arr[0]);
        $this->employees->orderBy(array(
            "name ASC"
            ));
        $arr1 = $this->employees->get();
        $this->assertContains('n1', $arr1[0]);
    }

    /**
     * @covers ListObjects::limit
     * @todo   Implement testLimit().
     */
    public function testLimit() {
        $this->employees->limit(1);
        $this->assertEquals(1, count($this->employees->get()));
        $this->employees->limit(2);
        $this->assertEquals(2, count($this->employees->get()));
    }

    /**
     * @covers ListObjects::offset
     * @todo   Implement testOffset().
     */
    public function testOffset() {
        $this->employees->limit(1);
        $this->employees->offset(1);
        $arr = $this->employees->get();
        $this->assertContains('n2', $arr[0]);
    }

}
