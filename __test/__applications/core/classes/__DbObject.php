<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-03 at 09:55:56.
 */
class DbObjectTest extends PHPUnit_Framework_TestCase
{

    /**
     * @var DbObject
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp() {
        //$this->object = new DbObject;
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown()
	{

	}

    public function test_ByAbsenceStack() {
        $absStack = new AbsenceStack;
        $arr = array(
            'denomination' => 'TEST'
        );
        $absStack->load($arr);
        $absStack->save();
        $this->assertEquals('h', $absStack->unit);
        $absStack->unit = 'd';
        $absStack->save();
        $this->assertEquals('d', $absStack->unit);
        $this->assertEquals('TEST', $absStack->denomination);

        $newAbsStack = new AbsenceStack($absStack->getId());
        $newAbsStack->unit = 'h';
        $newAbsStack->save();
        $this->assertEquals('TEST', $newAbsStack->denomination);
        $this->assertEquals('h', $newAbsStack->unit);
    }

	public function test_ByAbsenceKind() {
        $abs = new AbsenceKind('FERIE');
        $this->assertEquals('Ferie', $abs->description);
        $abs->absence_stack = 1;
        $abs->save();
        $this->assertEquals(1, $abs->absence_stack);
    }

    public function test_ByEmployee() {
        $employee = new Employee;
        $employee->surname = 'Surname';
        $employee->name = 'Name';
        $employee->liquid_group = '0002';
        $employee->save();
        $this->assertEquals(0, $employee->liquid_office_id);
    }

    /**
     * @covers DbObject::__set
     * @todo   Implement test__set().
     */
    public function test__set()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::__get
     * @todo   Implement test__get().
     */
    public function test__get()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::isNew
     * @todo   Implement testIsNew().
     */
    public function testIsNew()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::read
     * @todo   Implement testRead().
     */
    public function testRead()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::load
     * @todo   Implement testLoad().
     */
    public function testLoad()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::save
     * @todo   Implement testSave().
     */
    public function testSave()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::delete
     * @todo   Implement testDelete().
     */
    public function testDelete()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers DbObject::toArray
     * @todo   Implement testToArray().
     */
    public function testToArray()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

}
