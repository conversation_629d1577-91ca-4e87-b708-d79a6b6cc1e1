<?php

/**
 * Test class for Db.
 * Generated by PHPUnit on 2012-11-26 at 13:03:16.
 */
class DbTest extends PHPUnit_Framework_TestCase
{

    /**
     * @var Db
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp()
    {
	$this->object = Db::getInstance();
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown()
    {

    }

    /**
     * Generated from @assert ("SELECT 1 as one") == true.
     */
    public function testQuery()
    {
	$this->assertTrue(
		$this->object->query("SELECT 1 as one")
	);
    }

    /**
     * Generated from @assert () == array(0 => array('one' => 1)).
     */
    public function testFetchAll()
    {
	$this->object->query("SELECT 1 as one");
	$this->assertEquals(
		array(0 => array('one' => 1)), $this->object->fetchAll()
	);
    }

}

?>
