<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-04-24 at 14:07:38.
 */
class TimeTableTest extends PHPUnit_Framework_TestCase {

	protected $_tt1;
	protected $_tt2;
	protected $_tt3;
	protected $_tt4;
	protected $_tt5;
	protected $_tt6;
	protected $_emp;

	/**
	 * Sets up the fixture, for example, opens a network connection.
	 * This method is called before a test is executed.
	 */
	protected function setUp() {
		// Employee
		$emp = new Employee;
		$emp->load(array(
			'surname'		 => '<PERSON>',
			'name'			 => 'Mario',
			'liquid_group'	 => '0002'
		));
		$emp->save();
		$this->_emp = $emp;

		// Normal
		$tt1 = new TimeTable;
		$tt1->load(array(
			'employee_id'	 => $this->_emp->employee_id,
			'date_start'	 => strtotime('02-05-2013 08:00:00'),
			'date_end'		 => strtotime('02-05-2013 14:00:00')
		));
		$tt1->save();
		$this->_tt1 = $tt1;

		// Normal + Pause
		$tt2 = new TimeTable;
		$tt2->load(array(
			'employee_id'		 => $this->_emp->employee_id,
			'date_start'		 => strtotime('03-05-2013 08:00:00'),
			'date_start_pause'	 => strtotime('03-05-2013 10:00:00'),
			'date_end_pause'	 => strtotime('03-05-2013 11:00:00'),
			'date_end'			 => strtotime('03-05-2013 14:00:00')
		));
		$tt2->save();
		$this->_tt2 = $tt2;

		// Night
		$tt3 = new TimeTable;
		$tt3->load(array(
			'employee_id'	 => $this->_emp->employee_id,
			'date_start'	 => strtotime('06-05-2013 21:00:00'),
			'date_end'		 => strtotime('07-05-2013 07:00:00')
		));
		$tt3->save();
		$this->_tt3 = $tt3;

		// Night + Pause 1
		$tt4 = new TimeTable;
		$tt4->load(array(
			'employee_id'		 => $this->_emp->employee_id,
			'date_start'		 => strtotime('08-05-2013 21:00:00'),
			'date_start_pause'	 => strtotime('08-05-2013 22:00:00'),
			'date_end_pause'	 => strtotime('08-05-2013 23:00:00'),
			'date_end'			 => strtotime('09-05-2013 07:00:00')
		));
		$tt4->save();
		$this->_tt4 = $tt4;

		// Night + Pause 2
		$tt5 = new TimeTable;
		$tt5->load(array(
			'employee_id'		 => $this->_emp->employee_id,
			'date_start'		 => strtotime('13-05-2013 21:00:00'),
			'date_start_pause'	 => strtotime('13-05-2013 23:30:00'),
			'date_end_pause'	 => strtotime('14-05-2013 00:30:00'),
			'date_end'			 => strtotime('14-05-2013 07:00:00')
		));
		$tt5->save();
		$this->_tt5 = $tt5;

		// Night + Pause 3
		$tt6 = new TimeTable;
		$tt6->load(array(
			'employee_id'		 => $this->_emp->employee_id,
			'date_start'		 => strtotime('15-05-2013 21:00:00'),
			'date_start_pause'	 => strtotime('16-05-2013 04:00:00'),
			'date_end_pause'	 => strtotime('16-05-2013 05:00:00'),
			'date_end'			 => strtotime('16-05-2013 07:00:00')
		));
		$tt6->save();
		$this->_tt6 = $tt6;
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {
		$db = Db::getInstance();
		$db->query("DELETE FROM employee");
		$db->query("DELETE FROM personnel_timetable");
	}

	public function providerTestIsWorkDay() {
		return array(
			array(strtotime('02-05-2013'), true), // pieno
			array(strtotime('04-05-2013'), false), // vuoto
			array(strtotime('06-05-2013'), true), // notte inizio
			array(strtotime('07-05-2013'), true)   // notte fine
		);
	}

	public function providerTestDurations() {
		return array(
			array("_tt1", 21600, 0, 21600, array(21600, 0)), // normale
			array("_tt2", 21600, 3600, 18000, array(7200, 10800)), // normale + pausa
			array("_tt3", 36000, 0, 36000, array(36000, 0)), // notte
			array("_tt4", 36000, 3600, 32400, array(3600, 28800)), // notte + pausa 1
			array("_tt5", 36000, 3600, 32400, array(9000, 23400)), // notte + pausa 2
			array("_tt6", 36000, 3600, 32400, array(25200, 7200)) // notte + pausa 3
		);
	}

	/**
	 * @covers TimeTable::isWorkDay
	 * @dataProvider providerTestIsWorkDay
	 */
	public function testIsWorkDay($date, $result) {
		$tt = new TimeTable();
		$this->assertEquals($result, $tt->isWorkDay($date, $this->_emp->employee_id));
	}

	/**
	 * @covers TimeTable::timetableDuration
	 * @dataProvider providerTestDurations
	 */
	public function testTimetableDuration($idx, $ttd, $ttp, $ttw, $tts) {
		$tt = $this->{$idx};
		$this->assertEquals($ttd, $tt->timetableDuration());
	}

	/**
	 * @covers TimeTable::pauseDuration
	 * @dataProvider providerTestDurations
	 */
	public function testPauseDuration($idx, $ttd, $ttp, $ttw, $tts) {
		$tt = $this->{$idx};
		$this->assertEquals($ttp, $tt->pauseDuration());
	}

	/**
	 * @covers TimeTable::workDuration
	 * @dataProvider providerTestDurations
	 */
	public function testWorkDuration($idx, $ttd, $ttp, $ttw, $tts) {
		$tt = $this->{$idx};
		$this->assertEquals($ttw, $tt->workDuration());
	}

	/**
	 * @covers TimeTable::sectionsDuration
	 * @dataProvider providerTestDurations
	 */
	public function testSectionsDuration($idx, $ttd, $ttp, $ttw, $tts) {
		$tt = $this->{$idx};
		$this->assertEquals($tts, $tt->sectionsDuration());
	}

}