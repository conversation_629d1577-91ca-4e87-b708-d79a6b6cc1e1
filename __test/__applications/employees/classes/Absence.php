<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-16 at 14:10:51.
 */
class AbsenceTest extends PHPUnit_Framework_TestCase {

	protected $_emp;
	protected $_sh;
	protected $_sd;
	protected $_kh;
	protected $_kd;
	protected $_kn;
	protected $_a10h;
	protected $_a10d;
	protected $_a10n;
	protected $_a12h;
	protected $_a12d;
	protected $_a12n;
	protected $_a11h;
	protected $_a11d;
	protected $_a11n;
	protected $_a30h;
	protected $_a30d;
	protected $_a30n;
	protected $_a32h;
	protected $_a32d;
	protected $_a32n;
	protected $_a31h;
	protected $_a31d;
	protected $_a31n;

	private function setEmployee() {
		$emp = new Employee;
		$emp->load(array(
			'surname'		 => 'Rossi',
			'name'			 => 'Mario',
			'liquid_group'	 => '0002'
		));
		$emp->save();
		$this->_emp = $emp;
	}

	private function setAbsStacks() {
		$sh = new AbsenceStack;
		$sh->load(array(
			'unit'			 => ABS_STACK_UNIT_HOURLY,
			'denomination'	 => 'PERMESSI'
		));
		$sh->save();
		$this->_sh = $sh;

		$sd = new AbsenceStack;
		$sd->load(array(
			'unit'			 => ABS_STACK_UNIT_DAILY,
			'denomination'	 => 'FERIE'
		));
		$sd->save();
		$this->_sd = $sd;
	}

	private function setAbsKinds() {
		$db = Db::getInstance();
		$db->query_params("INSERT INTO absence_kind (code, description, absence_stack) VALUES ($1,$2,$3)", array("TEST1", "Test description 1", $this->_sh->getId()));
		$this->_kh = new AbsenceKind("TEST1");
		$db->query_params("INSERT INTO absence_kind (code, description, absence_stack) VALUES ($1,$2,$3)", array("TEST2", "Test description 2", $this->_sd->getId()));
		$this->_kd = new AbsenceKind("TEST2");
		$db->query_params("INSERT INTO absence_kind (code, description) VALUES ($1,$2)", array("TEST3", "Test description 3"));
		$this->_kn = new AbsenceKind("TEST3");
	}

	private function setAbsences() {
		$a10h = new Absence;
		$a10h->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kh->getId(),
			'start_date'	 => strtotime('01-05-2013 08:00'),
			'end_date'		 => strtotime('01-05-2013 08:00'),
		));
		$a10h->save();
		$this->_a10h = $a10h;

		$a10d = new Absence;
		$a10d->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kd->getId(),
			'start_date'	 => strtotime('02-05-2013 00:00'),
			'end_date'		 => strtotime('02-05-2013 00:00'),
		));
		$a10d->save();
		$this->_a10d = $a10d;

		$a10n = new Absence;
		$a10n->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kn->getId(),
			'start_date'	 => strtotime('03-05-2013 08:00'),
			'end_date'		 => strtotime('03-05-2013 08:00'),
		));
		$a10n->save();
		$this->_a10n = $a10n;

		$a12h = new Absence;
		$a12h->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kh->getId(),
			'start_date'	 => strtotime('04-05-2013 08:30'),
			'end_date'		 => strtotime('04-05-2013 10:30'),
		));
		$a12h->save();
		$this->_a12h = $a12h;

		$a12d = new Absence;
		$a12d->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kd->getId(),
			'start_date'	 => strtotime('05-05-2013 08:30'),
			'end_date'		 => strtotime('05-05-2013 10:30'),
		));
		$a12d->save();
		$this->_a12d = $a12d;

		$a12n = new Absence;
		$a12n->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kn->getId(),
			'start_date'	 => strtotime('06-05-2013 08:30'),
			'end_date'		 => strtotime('06-05-2013 10:30'),
		));
		$a12n->save();
		$this->_a12n = $a12n;

		$a11h = new Absence;
		$a11h->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kh->getId(),
			'start_date'	 => strtotime('07-05-2013 23:23'),
			'end_date'		 => strtotime('08-05-2013 00:23'),
		));
		$a11h->save();
		$this->_a11h = $a11h;

		$a11d = new Absence;
		$a11d->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kd->getId(),
			'start_date'	 => strtotime('08-05-2013 23:30'),
			'end_date'		 => strtotime('09-05-2013 00:30'),
		));
		$a11d->save();
		$this->_a11d = $a11d;

		$a11n = new Absence;
		$a11n->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kn->getId(),
			'start_date'	 => strtotime('09-05-2013 23:30'),
			'end_date'		 => strtotime('10-05-2013 00:30'),
		));
		$a11n->save();
		$this->_a11n = $a11n;

		$a30h = new Absence;
		$a30h->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kh->getId(),
			'start_date'	 => strtotime('10-05-2013 00:00'),
			'end_date'		 => strtotime('13-05-2013 00:00'),
		));
		$a30h->save();
		$this->_a30h = $a30h;

		$a30d = new Absence;
		$a30d->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kd->getId(),
			'start_date'	 => strtotime('13-05-2013 08:00'),
			'end_date'		 => strtotime('16-05-2013 08:00'),
		));
		$a30d->save();
		$this->_a30d = $a30d;

		$a30n = new Absence;
		$a30n->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kn->getId(),
			'start_date'	 => strtotime('16-05-2013 09:30'),
			'end_date'		 => strtotime('19-05-2013 09:30'),
		));
		$a30n->save();
		$this->_a30n = $a30n;

		$a32h = new Absence;
		$a32h->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kh->getId(),
			'start_date'	 => strtotime('19-05-2013 10:00'),
			'end_date'		 => strtotime('22-05-2013 12:00'),
		));
		$a32h->save();
		$this->_a32h = $a32h;

		$a32d = new Absence;
		$a32d->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kd->getId(),
			'start_date'	 => strtotime('22-05-2013 13:20'),
			'end_date'		 => strtotime('25-05-2013 15:20'),
		));
		$a32d->save();
		$this->_a32d = $a32d;

		$a32n = new Absence;
		$a32n->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kn->getId(),
			'start_date'	 => strtotime('25-05-2013 16:00'),
			'end_date'		 => strtotime('28-05-2013 18:00'),
		));
		$a32n->save();
		$this->_a32n = $a32n;

		$a31h = new Absence;
		$a31h->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kh->getId(),
			'start_date'	 => strtotime('30-05-2013 23:45'),
			'end_date'		 => strtotime('02-06-2013 00:45'),
		));
		$a31h->save();
		$this->_a31h = $a31h;

		$a31d = new Absence;
		$a31d->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kd->getId(),
			'start_date'	 => strtotime('30-05-2013 23:10'),
			'end_date'		 => strtotime('02-06-2013 00:10'),
		));
		$a31d->save();
		$this->_a31d = $a31d;

		$a31n = new Absence;
		$a31n->load(array(
			'employee_id'	 => $this->_emp->getId(),
			'ab_kind'		 => $this->_kn->getId(),
			'start_date'	 => strtotime('30-05-2013 23:00'),
			'end_date'		 => strtotime('02-06-2013 00:00'),
		));
		$a31n->save();
		$this->_a31n = $a31n;
	}

	/**
	 * Sets up the fixture, for example, opens a network connection.
	 * This method is called before a test is executed.
	 */
	protected function setUp() {
		$this->setEmployee();
		$this->setAbsStacks();
		$this->setAbsKinds();
		$this->setAbsences();
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {
		$db = Db::getInstance();
		$db->query("DELETE FROM employee");
		$db->query("DELETE FROM absences");
		$db->query("DELETE FROM absence_kind WHERE code='TEST1' OR code='TEST2' OR code='TEST3'");
		$db->query("DELETE FROM absence_stack");
	}

	public function providerTests() {
		return array(
			array("_a10h", "_kh", array(
					"time"	 => 0,
					"days"	 => 1)),
			array("_a10d", "_kd", array(
					"time"	 => 0,
					"days"	 => 1)),
			array("_a10n", "_kn", array(
					"time"	 => 0,
					"days"	 => 0)),
			array("_a12h", "_kh", array(
					"time"	 => 7200,
					"days"	 => 1)),
			array("_a12d", "_kd", array(
					"time"	 => 0,
					"days"	 => 1)),
			array("_a12n", "_kn", array(
					"time"	 => 7200,
					"days"	 => 0)),
			array("_a11h", "_kh", array(
					"time"	 => 3600,
					"days"	 => 1)),
			array("_a11d", "_kd", array(
					"time"	 => 0,
					"days"	 => 2)),
			array("_a11n", "_kn", array(
					"time"	 => 3600,
					"days"	 => 0)),
			array("_a30h", "_kh", array(
					"time"	 => 0,
					"days"	 => 4)),
			array("_a30d", "_kd", array(
					"time"	 => 0,
					"days"	 => 4)),
			array("_a30n", "_kn", array(
					"time"	 => 259200,
					"days"	 => 0)),
			array("_a32h", "_kh", array(
					"time"	 => 7200,
					"days"	 => 4)),
			array("_a32d", "_kd", array(
					"time"	 => 0,
					"days"	 => 4)),
			array("_a32n", "_kn", array(
					"time"	 => 266400,
					"days"	 => 0)),
			array("_a31h", "_kh", array(
					"time"	 => 3600,
					"days"	 => 3)),
			array("_a31d", "_kd", array(
					"time"	 => 0,
					"days"	 => 4)),
			array("_a31n", "_kn", array(
					"time"	 => 176400,
					"days"	 => 0))
		);
	}

	/**
	 * @covers Absence::getKind
	 * @dataProvider providerTests
	 */
	public function testGetKind($idxA, $idxK) {
		$a = $this->{$idxA};
		$this->assertEquals($this->{$idxK}->getId(), $a->getKind()->getId());
	}

	/**
	 * @covers Absence::getDuration
	 * @dataProvider providerTests
	 */
	public function testGetDuration($idxA, $idxK, $expected) {
		$a = $this->{$idxA};
		$this->assertEquals($expected, $a->getDuration());
	}

}