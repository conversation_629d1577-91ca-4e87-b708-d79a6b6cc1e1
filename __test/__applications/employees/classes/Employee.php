<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-04-18 at 16:20:37.
 */
class EmployeeTest extends PHPUnit_Framework_TestCase
{

    /**
     * @var Employee
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp()
    {
	//$this->object = new Employee;
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown()
    {

    }

    /**
     * @covers Employee::getFiscalKind
     * @todo   Implement testGetFiscalKind().
     */
    public function testGetFiscalKind()
    {
	// Remove the following lines when you implement this test.
	$this->assertEquals(
		1, 1
	);
    }

    /**
     * @covers Employee::getFiscalRole
     * @todo   Implement testGetFiscalRole().
     */
    public function testGetFiscalRole()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers Employee::getQualificationDisplay
     * @todo   Implement testGetQualificationDisplay().
     */
    public function testGetQualificationDisplay()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers Employee::getLinkedProjects
     * @todo   Implement testGetLinkedProjects().
     */
    public function testGetLinkedProjects()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

    /**
     * @covers Employee::setLinkedProjects
     * @todo   Implement testSetLinkedProjects().
     */
    public function testSetLinkedProjects()
    {
	// Remove the following lines when you implement this test.
	$this->markTestIncomplete(
		'This test has not been implemented yet.'
	);
    }

}
