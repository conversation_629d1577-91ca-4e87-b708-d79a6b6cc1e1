<?php

//use Employee\AbsencesQuery;
//use Employee\PresenceQuery;

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-21 at 11:25:34.
 *
 * Data:
 *  timetable 5 June 2013, 7:30 - 13:30
 *  timetable 6 June 2013, 7:30 - 13:30 with break between 10:00-11:00
 *
 *  presences 5 June 2013, 7:30 - 13:30
 */
class ExtRecDayTest extends PHPUnit_Framework_TestCase {

//    const PRESENCE_TYPE_IN = 1;
//    const PRESENCE_TYPE_OUT = 0;
//    const PRESENCE_KIND_NORMAL = 0;
//    const PRESENCE_KIND_LUNCH = 1;
//    const PRESENCE_KIND_SERVICE = 2;

    const FIRST_SEPT = 1441058400;
    const SIXTH_SEPT = 1441490400;

    protected function setUp() {
		remove_fixtures(array('users', 'employees', 'timetables'));
		load_fixtures(array('users', 'employees', 'timetables'));
		$this->emp = \Employee\EmployeeQuery::create()->findPk(1);
		$this->db = \Propel::getConnection('mc2api');
        $this->presenceObj = new \Employee\PresenceQuery();
        //\Employee\PresencePeer::clearInstancePool(true);
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {
        //remove_fixtures(array('users', 'employees', 'timetables'));
	}

    /*
	public function test_calcDay_1() {
        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 0);
        $this->assertEquals($data['extraordinary'], 0);
    }

    public function test_calcDay_2() {
		$ext = new ExtRecDay($this->emp, strtotime('2013-06-06'));
		$data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -300);
		$this->assertEquals($data['extraordinary'], -300);
	}

    public function test_calcDay_3() {
        $presences = $this->presenceObj->find();
        foreach($presences as $p){
            $p->setDateEdit($p->getDateEdit() + 86400);
            $p->save();
        }

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-06'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 60);
        $this->assertEquals($data['extraordinary'], 60);
    }

    public function test_calcDay_4() {
        $p = $this->presenceObj->findPk(1);
        $p->setDateEdit($p->getDateEdit() + 1800);
        $p->save();
        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -30);
        $this->assertEquals($data['extraordinary'], -30);
    }

    public function test_calcDay_5() {
        $p = $this->presenceObj->findPk(2);
        $p->setDateEdit($p->getDateEdit() - 1800);
        $p->save();
        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -30);
        $this->assertEquals($data['extraordinary'], -30);
    }

    public function test_calcDay_normal_presences_1() {
        $presence = array(
            'date_edit' => '2013-06-05',
            'date_hour' => '10:00',
            'original_inout_edit' => 2,
            'employee_id' => 1
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '11:30';
        $presence['original_inout_edit'] = 1;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -90);
        $this->assertEquals($data['extraordinary'], -90);
    }

    public function test_calcDay_service_presences_1() {
        $presence = array(
            'date_edit' => '2013-06-05',
            'date_hour' => '10:00',
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1,
            'type_edit' => PRESENCE_SERVICE
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '11:30';
        $presence['original_inout_edit'] = PRESENCE_ENTRANCE;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 0);
        $this->assertEquals($data['extraordinary'], 0);
    }

    public function test_calcDay_lunch_presences_1() {
        $presence = array(
            'date_edit' => '2013-06-05',
            'date_hour' => '10:00',
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1,
            'type_edit' => PRESENCE_LUNCH
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '11:30';
        $presence['original_inout_edit'] = PRESENCE_ENTRANCE;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -90);
        $this->assertEquals($data['extraordinary'], -90);
    }

    public function test_calcDay_b_normal_presences_1() {
        $presences = $this->presenceObj->find();
        foreach($presences as $p){
            $p->setDateEdit($p->getDateEdit() + 86400);
            $p->save();
        }

        $presence = array(
            'date_edit' => '2013-06-06',
            'date_hour' => '10:00',
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '11:30';
        $presence['original_inout_edit'] = PRESENCE_ENTRANCE;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-06'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -30);
        $this->assertEquals($data['extraordinary'], -30);
    }

    public function test_calcDay_b_lunch_presences_1() {
        $presences = $this->presenceObj->find();
        foreach($presences as $p){
            $p->setDateEdit($p->getDateEdit() + 86400);
            $p->save();
        }

        $presence = array(
            'date_edit' => '2013-06-06',
            'date_hour' => '10:00',
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1,
            'type_edit' => PRESENCE_LUNCH
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '11:30';
        $presence['original_inout_edit'] = PRESENCE_ENTRANCE;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-06'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -30);
        $this->assertEquals($data['extraordinary'], -30);
    }

    public function test_calcDay_b_service_presences_1() {
        $presences = $this->presenceObj->find();
        foreach($presences as $p){
            $p->setDateEdit($p->getDateEdit() + 86400);
            $p->save();
        }

        $presence = array(
            'date_edit' => '2013-06-06',
            'date_hour' => '10:00',
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1,
            'type_edit' => PRESENCE_SERVICE
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '11:30';
        $presence['original_inout_edit'] = PRESENCE_ENTRANCE;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-06'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 60);
        $this->assertEquals($data['extraordinary'], 60);
    }

    public function test_calcDay_absence_1() {
        $data = array(
            'start_date' => '2013-06-05',
            'end_date' => '2013-06-05',
            'start_time' => '10:00',
            'end_time' => '10:30',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'PERNOR',
            'employee_id' => 1,
            );

        $absenceQ = new AbsencesQuery();
        $absenceQ->write($data);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 30);
        $this->assertEquals($data['extraordinary'], 30);
    }

    public function test_calcDay_absence_2() {
        // With relative correspondent presences
        $data = array(
            'start_date' => '2013-06-05',
            'end_date' => '2013-06-05',
            'start_time' => '10:00',
            'end_time' => '10:30',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'PERNOR',
            'employee_id' => 1,
            );
        $absenceQ = new AbsencesQuery();
        $absenceQ->write($data);

        $presence = array(
            'date_edit' => '2013-06-05',
            'date_hour' => '10:00',
            'original_inout_edit' => 2,
            'employee_id' => 1
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '10:30';
        $presence['original_inout_edit'] = 1;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 0);
        $this->assertEquals($data['extraordinary'], 0);
    }

    public function test_calcDay_absence_3() {
        // With relative correspondent presences
        $data = array(
            'start_date' => '2013-06-05',
            'end_date' => '2013-06-05',
            'start_time' => '10:00',
            'end_time' => '10:30',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'FERIE',
            'employee_id' => 1,
            );
        $absenceQ = new AbsencesQuery();
        $absenceQ->write($data);

        PresenceQuery::create()->find()->delete();

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 0);
        $this->assertEquals($data['extraordinary'], 0);
    }

    public function test_calcDay_absence_4() {
        // With relative correspondent presences
        $data = array(
            'start_date' => '2013-06-05',
            'end_date' => '2013-06-05',
            'start_time' => '10:00',
            'end_time' => '10:30',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'FERIE',
            'employee_id' => 1,
            );
        $absenceQ = new AbsencesQuery();
        $absenceQ->write($data);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 360);
        $this->assertEquals($data['extraordinary'], 360);
    }

    public function test_calcDay_absence_5() {
        // With relative correspondent presences
        $data = array(
            'start_date' => '2013-06-05',
            'end_date' => '2013-06-05',
            'start_time' => '10:00',
            'end_time' => '10:30',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'RIPCLS',
            'employee_id' => 1,
            );
        $absenceQ = new AbsencesQuery();
        $absenceQ->write($data);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 30);
        $this->assertEquals($data['extraordinary'], 30);
    }

    public function test_calcDay_presences_unalign_1() {
        $p = $this->presenceObj->findPk(1);
        $p->setDateEdit($p->getDateEdit() + 600);
        $p->save();


        $presence = array(
            'date_edit' => '2013-06-05',
            'date_hour' => '05:00',
            'original_inout_edit' => PRESENCE_ENTRANCE,
            'employee_id' => 1
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '06:00';
        $presence['original_inout_edit'] = PRESENCE_EXIT;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 50);
        $this->assertEquals($data['extraordinary'], 50);
    }


    public function test_calcDay_presences_unalign_2() {
        $p = $this->presenceObj->findPk(1);
        $p->setDateEdit($p->getDateEdit() + 7200);
        $p->save();


        $presence = array(
            'date_edit' => '2013-06-05',
            'date_hour' => '06:00',
            'original_inout_edit' => PRESENCE_ENTRANCE,
            'employee_id' => 1
        );

        $p1 = new PresenceQuery();
        $p1->write($presence);

        $presence['date_hour'] = '07:00';
        $presence['original_inout_edit'] = PRESENCE_EXIT;
        $p2 = new PresenceQuery();
        $p2->write($presence);

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], -60);
        $this->assertEquals($data['extraordinary'], -60);
    }

    public function test_calcDay_auto_break_1() {
        $p = $this->presenceObj->findPk(2);
        $p->setDateEdit($p->getDateEdit() + 60);
        $p->save();

        $emp = \Employee\EmployeeQuery::create()->findPk(1);
        $emp->setBreakAfterMaxWork(30);
        $emp->setMaxContWork(360);
        $emp->save();

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 0);
        $this->assertEquals($data['extraordinary'], 0);
    }

    public function test_calcDay_auto_break_2() {
        $p = $this->presenceObj->findPk(2);
        $p->setDateEdit($p->getDateEdit() + 3600);
        $p->save();

        $emp = \Employee\EmployeeQuery::create()->findPk(1);
        $emp->setBreakAfterMaxWork(30);
        $emp->setMaxContWork(360);
        $emp->save();

        $ext = new ExtRecDay($this->emp, strtotime('2013-06-05'));
        $data = $ext->calcDay();
        $this->assertEquals($data['authorized'], 30);
        $this->assertEquals($data['extraordinary'], 30);
    }*/

    /**
     * Employee = 1, Timetable = 1
     * @param type $param
     */
    public function test_1_1_normal() {
        $dataIn = [
            'date_edit' => '2015-09-01',
            'date_hour' => '08:00',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_ENTRANCE,
            'employee_id' => 1
        ];

        $dataOut = [
            'date_edit' => '2015-09-01',
            'date_hour' => '14:00',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1
        ];

        $this->presenceObj->write($dataIn);
        $this->presenceObj->write($dataOut);

        $ext = new ExtRecDay($this->emp, ExtRecDayTest::FIRST_SEPT);

        $calc = $ext->calcDay();
        $this->assertEquals($calc['authorized'], 0);
        $this->assertEquals($calc['extraordinary'], 0);
    }

    /**
     * Employee = 1, Timetable = 1
     * @param type $param
     */
    public function test_1_1_normal_2() {
        $dataIn = [
            'date_edit' => '2015-09-01',
            'date_hour' => '08:00',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_ENTRANCE,
            'employee_id' => 1
        ];

        $dataOut = [
            'date_edit' => '2015-09-01',
            'date_hour' => '14:10',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1
        ];

        $this->presenceObj->write($dataIn);
        $this->presenceObj->write($dataOut);

        $ext = new ExtRecDay($this->emp, ExtRecDayTest::FIRST_SEPT);

        $calc = $ext->calcDay();
        $this->assertEquals($calc['authorized'], 10);
        $this->assertEquals($calc['extraordinary'], 10);
    }

    /**
     * Employee = 1, Timetable = 1
     * @param type $param
     */
    public function test_1_1_normal_3() {
        $dataIn = [
            'date_edit' => '2015-09-01',
            'date_hour' => '08:10',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_ENTRANCE,
            'employee_id' => 1
        ];

        $dataOut = [
            'date_edit' => '2015-09-01',
            'date_hour' => '14:00',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1
        ];

        $this->presenceObj->write($dataIn);
        $this->presenceObj->write($dataOut);

        $ext = new ExtRecDay($this->emp, ExtRecDayTest::FIRST_SEPT);

        $calc = $ext->calcDay();
        $this->assertEquals($calc['authorized'], -10);
        $this->assertEquals($calc['extraordinary'], -10);
    }

    /**
     * Employee = 1, Timetable = 1
     * @param type $param
     */
    public function test_1_1_normal_4() {
        $dataIn = [
            'date_edit' => '2015-09-01',
            'date_hour' => '07:50',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_ENTRANCE,
            'employee_id' => 1
        ];

        $dataOut = [
            'date_edit' => '2015-09-01',
            'date_hour' => '13:50',
            'type_edit' => PRESENCE_NORMAL,
            'original_inout_edit' => PRESENCE_EXIT,
            'employee_id' => 1
        ];

        $this->presenceObj->write($dataIn);
        $this->presenceObj->write($dataOut);

        $ext = new ExtRecDay($this->emp, ExtRecDayTest::FIRST_SEPT);

        $calc = $ext->calcDay();
        $this->assertEquals($calc['authorized'], 0);
        $this->assertEquals($calc['extraordinary'], 0);
    }

}

