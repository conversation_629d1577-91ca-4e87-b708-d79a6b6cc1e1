<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-09-11 at 14:10:05.
 *
 * Working on June 2013
 */
class ExtRecMonthTest extends PHPUnit_Framework_TestCase {

	/**
	 * @var ExtRecMonth
	 */
	protected $object;
    public $data;

	/**
	 * Sets up the fixture, for example, opens a network connection.
	 * This method is called before a test is executed.
	 */
	protected function setUp() {
        $monthStr = '{"ext_start":0,"ext_end":50,"ext_start_o":"0","ext_end_o":"-70","note":""}';
        $month = json_decode($monthStr,  true);

        $stackStr = '[
        {"id":1,"denomination":"PERMESSI","totalStart":0,"totalStartO":0,"totalEnd":0,"totalEndO":0,"unit":"h","recover":false},
        {"id":2,"denomination":"FERIE","totalStart":0,"totalStartO":0,"totalEnd":0,"totalEndO":0,"unit":"d","recover":false},
        {"id":3,"denomination":"A RECUPERO","totalStart":0, "totalStartO":0,"totalEnd":0,"totalEndO":0,"unit":"h","recover":true}
        ]';
        $stack = json_decode($stackStr,  true);

        $daysStr = '[
            {"date":"2013-06-01T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-02T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-03T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-04T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-05T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-06T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-07T00:00:00","note":"","totalO":50,"total":50},
            {"date":"2013-06-08T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-09T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-10T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-11T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-12T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-13T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-14T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-15T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-16T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-17T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-18T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-19T00:00:00","note":"","totalO":-120,"total":0},
            {"date":"2013-06-20T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-21T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-22T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-23T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-24T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-25T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-26T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-27T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-28T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-29T00:00:00","note":"","totalO":0,"total":0},
            {"date":"2013-06-30T00:00:00","note":"","totalO":0,"total":0}
        ]';
        $days = json_decode($daysStr,  true);


        $this->data = array(
            'employee_id'   => 1,
            'year'          => 2013,
            'month'         => 1,
            'month_data'    => $month,
            'stacks'        => $stack,
            'days'          => $days,
            );

        remove_fixtures(array('users', 'employees', 'timetables', 'presences','absence_kind','storage_personnel_presences'));
        load_fixtures(array('users', 'employees', 'timetables', 'presences','absence_kind'));
        $this->emp = \Employee\EmployeeQuery::create()->findPk(1);
        \Employee\PresencePeer::clearInstancePool(true);
        $this->erm = new ExtRecMonth($this->emp, strtotime('2013-06-01'));
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {

	}

	/**
	 * @covers ExtRecMonth::saveMonth
	 * @todo   Implement testSaveMonth().
	 */
	public function test_calc_month_first_1() {
		$res = $this->erm->calcMonth();
        $this->assertEquals($res['general']['dateStart'], strtotime('2013-06-01'));
        $this->assertEquals($res['general']['dateEnd'], strtotime('2013-06-30'));

        $day = strtotime("2013-06-06");
        $this->assertEquals($res['days'][$day]['totalO'], -300);

        $this->assertEquals($res['general']['totalStartO'], 0);
        $this->assertEquals($res['general']['totalEndO'], -300);
        $this->assertEquals(count($res['stacks']), 3);

        $this->assertEquals(($res['stacks'][1]['totalStart']), 0);
        $this->assertEquals(($res['stacks'][2]['totalEnd']), 0);
        $this->assertEquals(($res['stacks'][3]['totalMonth']), 0);

	}

    public function test_calc_month_first_2() {
        $p = \Employee\PresenceQuery::create()->findPk(2);
        $p->setDateEdit($p->getDateEdit() + 1800); // 14:00
        $p->save();

        $res = $this->erm->calcMonth();
        $this->assertEquals($res['general']['dateStart'], strtotime('2013-06-01'));
        $this->assertEquals($res['general']['dateEnd'], strtotime('2013-06-30'));

        $day = strtotime("2013-06-05");
        $this->assertEquals($res['days'][$day]['totalO'], 30);

        $this->assertEquals($res['general']['totalStartO'], 0);
        $this->assertEquals($res['general']['totalEndO'], -270);
        $this->assertEquals(count($res['stacks']), 3);

        $this->assertEquals(($res['stacks'][1]['totalStart']), 0);
        $this->assertEquals(($res['stacks'][2]['totalEnd']), 0);
        $this->assertEquals(($res['stacks'][3]['totalMonth']), 0);

    }

    public function test_calc_month_first_3() {
        $p = \Employee\PresenceQuery::create()->findPk(2);
        $p->setDateEdit($p->getDateEdit() + 1800); // 14:00
        $p->save();

        $pcopy = $p->copy();
        $pcopy->setDateEdit($p->getDateEdit() + 82800); // 6 june at 13:00
        $pcopy->save();

        $p = \Employee\PresenceQuery::create()->findPk(1);
        $pcopy = $p->copy();
        $pcopy->setDateEdit($p->getDateEdit() + 86400); // 6 june at 7:30
        $pcopy->save();

        // 30 minutes absences as classic permission
        $data = array(
            'start_date' => '2013-06-06',
            'end_date' => '2013-06-06',
            'start_time' => '13:00',
            'end_time' => '13:30',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'PERNOR',
            'employee_id' => 1,
            );
        $absenceQ = new \Employee\AbsencesQuery();
        $absenceQ->write($data);

        $res = $this->erm->calcMonth();

        $day = strtotime("2013-06-06");
        $this->assertEquals($res['days'][$day]['totalO'], 60);

        $this->assertEquals($res['general']['totalStartO'], 0);
        $this->assertEquals($res['general']['totalEndO'], 90);
        $this->assertEquals(count($res['stacks']), 3);

        $this->assertEquals(($res['stacks'][1]['totalEnd']), -30);
        $this->assertEquals(($res['stacks'][2]['totalEnd']), 0);
        $this->assertEquals(($res['stacks'][3]['totalMonth']), 0);

    }

    public function test_calc_month_first_4() {
        $data = array(
            'start_date' => '2013-06-06',
            'end_date' => '2013-06-06',
            'date_of_req' => '2013-06-01',
            'ab_kind' => 'FERIE',
            'employee_id' => 1,
            );
        $absenceQ = new \Employee\AbsencesQuery();
        $absenceQ->write($data);

        $res = $this->erm->calcMonth();
        $day = strtotime("2013-06-06");
        $freeDay = strtotime("2013-06-08");
        $this->assertEquals($res['days'][$day]['totalO'], 0);

        $this->assertEquals($res['general']['totalStartO'], 0);
        $this->assertEquals($res['general']['totalEndO'], 0);
        $this->assertEquals(count($res['stacks']), 3);

        $this->assertEquals(count($res['days'][$day]['info']), 1);
        $this->assertEquals(count($res['days'][$day]['warning']), 0);

        $this->assertEquals(count($res['days'][$freeDay]['info']), 1);

        $this->assertEquals($res['stacks'][2]['totalEnd'], -1);
        $this->assertEquals($res['stacks'][2]['unit'], 'd');

    }

    public function test_save_month_first_1() {
        $res = $this->erm->saveMonth(
            $this->data['month_data'],
            $this->data['days'],
            $this->data['stacks']
        );

        $this->assertTrue(is_numeric($res));

        $storedRow = \Employee\StoredMonthQuery::create()->find()->toArray();
        $this->assertEquals(count($storedRow), 1);
        $this->assertEquals($storedRow[0]['ExtEndOriginal'], -70);
        $this->assertEquals($storedRow[0]['ExtEnd'], 50);
        $stackRow = \Employee\StoredStackQuery::create()->find()->toArray();
        $this->assertEquals(count($stackRow), 3);
        $this->assertEquals($stackRow[1]['valueEnd'], 0);
        $this->assertEquals($stackRow[1]['valueEndO'], 0);

        $checkRow = new ExtRecMonth($this->emp, strtotime('2013-07-01'));
        $res = $checkRow->calcMonth();

        $this->assertEquals($res['general']['totalStartO'], 50);
        $this->assertEquals($res['general']['totalStart'], 50);
        $this->assertEquals($res['stacks'][2]['totalEndO'],$this->data['stacks'][2]['totalEndO']);

    }

    public function test_save_month_first_2() {
        $this->data['stacks'][1]['totalEnd'] = 1;
        $res = $this->erm->saveMonth(
            $this->data['month_data'],
            $this->data['days'],
            $this->data['stacks']
        );

        $this->assertTrue(is_numeric($res));

        $storedRow = \Employee\StoredMonthQuery::create()->find()->toArray();
        $this->assertEquals(count($storedRow), 1);
        $this->assertEquals($storedRow[0]['ExtEndOriginal'], -70);
        $this->assertEquals($storedRow[0]['ExtEnd'], 50);
        $stackRow = \Employee\StoredStackQuery::create()->find()->toArray();
        $this->assertEquals(count($stackRow), 3);
        $this->assertEquals($stackRow[1]['valueEnd'], 0);
        $this->assertEquals($stackRow[1]['valueEndO'], 0);

        $checkRow = new ExtRecMonth($this->emp, strtotime('2013-07-01'));
        $res = $checkRow->calcMonth();

        $this->assertEquals($res['general']['totalStartO'], 50);
        $this->assertEquals($res['general']['totalStart'], 50);
        $this->assertEquals($res['stacks'][2]['totalStart'],$this->data['stacks'][1]['totalEnd']);

    }

    public function test_save_month_first_3() {
        $this->data['stacks'][0]['totalEnd'] = 100;
        $res = $this->erm->saveMonth(
            $this->data['month_data'],
            $this->data['days'],
            $this->data['stacks']
        );

        $this->assertTrue(is_numeric($res));

        $storedRow = \Employee\StoredMonthQuery::create()->find()->toArray();
        $this->assertEquals(count($storedRow), 1);
        $this->assertEquals($storedRow[0]['ExtEndOriginal'], -70);
        $this->assertEquals($storedRow[0]['ExtEnd'], 50);
        $stackRow = \Employee\StoredStackQuery::create()->find()->toArray();
        $this->assertEquals(count($stackRow), 3);
        $this->assertEquals($stackRow[1]['valueEnd'], 0);
        $this->assertEquals($stackRow[1]['valueEndO'], 0);

        $checkRow = new ExtRecMonth($this->emp, strtotime('2013-07-01'));
        $res = $checkRow->calcMonth();

        $this->assertEquals($res['general']['totalStartO'], 50);
        $this->assertEquals($res['general']['totalStart'], 50);

        $this->assertEquals($res['stacks'][1]['totalStart'],$this->data['stacks'][0]['totalEnd']);


    }

}
