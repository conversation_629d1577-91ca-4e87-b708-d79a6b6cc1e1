<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-16 at 13:03:50.
 */
class AbsenceStackTest extends PHPUnit_Framework_TestCase {

	/**
	 * @var AbsenceStack
	 */
	protected $_sd;
	protected $_sh;

	/**
	 * Sets up the fixture, for example, opens a network connection.
	 * This method is called before a test is executed.
	 */
	protected function setUp() {
		// ABSENCE STACKS
		$absStack1 = new AbsenceStack;
		$absStack1->load(array(
			'unit'			 => ABS_STACK_UNIT_HOURLY,
			'denomination'	 => 'PERMESSI'
		));
		$absStack1->save();
		$this->_sh = $absStack1;

		$absStack2 = new AbsenceStack;
		$absStack2->load(array(
			'unit'			 => ABS_STACK_UNIT_DAILY,
			'denomination'	 => 'FERIE'
		));
		$absStack2->save();
		$this->_sd = $absStack2;
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {
		$db = Db::getInstance();
		$db->query("DELETE FROM absence_stack");
	}

	public function providerTestIsDaily() {
		return array(
			array("_sd", true),
			array("_sh", false));
	}

	/**
	 * @covers AbsenceStack::isDaily
	 * @dataProvider providerTestIsDaily
	 */
	public function testIsDaily($idx, $daily) {
		$s = $this->{$idx};
		$this->assertEquals($daily, $s->isDaily());
	}

}
