<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-16 at 13:14:16.
 */
class AbsenceKindTest extends PHPUnit_Framework_TestCase {

	protected $_sh;
	protected $_k;
	protected $_kr;

	/**
	 * Sets up the fixture, for example, opens a network connection.
	 * This method is called before a test is executed.
	 */
	protected function setUp() {
		// ABSENCE STACKS
		$sh = new AbsenceStack;
		$sh->load(array(
			'unit'			 => ABS_STACK_UNIT_HOURLY,
			'denomination'	 => 'PERMESSI'
		));
		$sh->save();
		$this->_sh = $sh;

		// ABSENCE KINDS
		$k1 = new AbsenceKind("PERLUT");
		$k1->absence_stack = $this->_sh->getId();
		$k1->save();
		$this->_k1 = $k1;

		$kr = new AbsenceKind("RIPCLS");
		$kr->absence_stack = $this->_sh->getId();
		$kr->save();
		$this->_kr = $kr;

		$db = Db::getInstance();
		$db->query_params("INSERT INTO absence_kind (code, description) VALUES ($1,$2)", array("TEST", "Test description"));
		$this->_k2 = new AbsenceKind("TEST");
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {
		$db = Db::getInstance();
		$db->query("DELETE FROM absence_kind WHERE code = 'TEST'");
		$db->query("DELETE FROM absence_stack");
	}

	public function providerTests() {
		return array(
			array("_k1", false), // normal kind
			array("_k2", false), // normal kind no stack
			array("_kr", true)  // recover kind
		);
	}

	/**
	 * @covers AbsenceKind::isRecover
	 * @dataProvider providerTests
	 */
	public function testIsRecover($idx, $recover) {
		$k = $this->{$idx};
		$this->assertEquals($recover, $k->isRecover());
	}

	/**
	 * @covers AbsenceKind::getStack
	 * @dataProvider providerTests
	 */
	public function testGetStack($idx) {
		$k = $this->{$idx};
		$result = 0;
		$expected = 0;
		if ($k->absence_stack) {
			$result = $k->getStack()->getId();
			$expected = $this->_sh->getId();
		}
		$this->assertEquals($expected, $result);
	}

}