<?php

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-05-16 at 11:45:49.
 */
class PresenceTest extends PHPUnit_Framework_TestCase {

	protected $_emp;
	protected $_pp11;
	protected $_pp12;
	protected $_pp13;
	protected $_pp21;
	protected $_pp22;
	protected $_pp31;
	protected $_pp32;

	/**
	 * Sets up the fixture, for example, opens a network connection.
	 * This method is called before a test is executed.
	 */
	protected function setUp() {
		// Employee
		$emp = new Employee;
		$emp->load(array(
			'surname'		 => '<PERSON>',
			'name'			 => '<PERSON>',
			'liquid_group'	 => '0002'
		));
		$emp->save();
		$this->_emp = $emp;

		// Presences
		$pp11 = new Presence;
		$pp11->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:00:00'),
			'date_edit'				 => strtotime('01-05-2013 08:00:00'),
			'type'					 => PRESENCE_NORMAL,
			'type_edit'				 => PRESENCE_NORMAL,
			'original_inout'		 => PRESENCE_ENTRANCE,
			'original_inout_edit'	 => PRESENCE_ENTRANCE
		));
		$pp11->save();
		$this->_pp11 = $pp11;

		$pp12 = new Presence;
		$pp12->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:05:00'),
			'date_edit'				 => strtotime('01-05-2013 08:05:00'),
			'type'					 => PRESENCE_LUNCH,
			'type_edit'				 => PRESENCE_LUNCH,
			'original_inout'		 => PRESENCE_ENTRANCE,
			'original_inout_edit'	 => PRESENCE_ENTRANCE
		));
		$pp12->save();
		$this->_pp12 = $pp12;

		$pp13 = new Presence;
		$pp13->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:10:00'),
			'date_edit'				 => strtotime('01-05-2013 08:10:00'),
			'type'					 => PRESENCE_SERVICE,
			'type_edit'				 => PRESENCE_SERVICE,
			'original_inout'		 => PRESENCE_ENTRANCE,
			'original_inout_edit'	 => PRESENCE_ENTRANCE
		));
		$pp13->save();
		$this->_pp13 = $pp13;

		$pp21 = new Presence;
		$pp21->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:20:00'),
			'date_edit'				 => strtotime('01-05-2013 08:20:00'),
			'type'					 => PRESENCE_NORMAL,
			'type_edit'				 => PRESENCE_NORMAL,
			'original_inout'		 => PRESENCE_ENTRANCE,
			'original_inout_edit'	 => PRESENCE_ENTRANCE
		));
		$pp21->save();
		$this->_pp21 = $pp21;

		$pp22 = new Presence;
		$pp22->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:25:00'),
			'date_edit'				 => strtotime('01-05-2013 08:25:00'),
			'type'					 => PRESENCE_NORMAL,
			'type_edit'				 => PRESENCE_NORMAL,
			'original_inout'		 => PRESENCE_EXIT,
			'original_inout_edit'	 => PRESENCE_EXIT
		));
		$pp22->save();
		$this->_pp22 = $pp22;

		$pp31 = new Presence;
		$pp31->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:30:10'),
			'date_edit'				 => strtotime('01-05-2013 08:30:10'),
			'type'					 => PRESENCE_NORMAL,
			'type_edit'				 => PRESENCE_NORMAL,
			'original_inout'		 => PRESENCE_ENTRANCE,
			'original_inout_edit'	 => PRESENCE_ENTRANCE
		));
		$pp31->save();
		$this->_pp31 = $pp31;

		$pp32 = new Presence;
		$pp32->load(array(
			'employee_id'			 => $this->_emp->employee_id,
			'date'					 => strtotime('01-05-2013 08:30:40'),
			'date_edit'				 => strtotime('01-05-2013 08:30:40'),
			'type'					 => PRESENCE_NORMAL,
			'type_edit'				 => PRESENCE_NORMAL,
			'original_inout'		 => PRESENCE_ENTRANCE,
			'original_inout_edit'	 => PRESENCE_ENTRANCE
		));
		$pp32->save();
		$this->_pp32 = $pp32;
	}

	/**
	 * Tears down the fixture, for example, closes a network connection.
	 * This method is called after a test is executed.
	 */
	protected function tearDown() {
		$db = Db::getInstance();
		$db->query("DELETE FROM employee");
		$db->query("DELETE FROM personnel_presences");
	}

	public function providerTests() {
		return array(
			array("_pp11", true, false, false, true, false, strtotime('01-05-2013 08:00:00'), true), // N i
			array("_pp12", false, true, false, true, false, strtotime('01-05-2013 08:05:00'), true), // L i
			array("_pp13", false, false, true, true, false, strtotime('01-05-2013 08:10:00'), true), // S i
			array("_pp21", true, false, false, true, false, strtotime('01-05-2013 08:20:00'), true), // n I
			array("_pp22", true, false, false, false, true, strtotime('01-05-2013 08:25:00'), true), // n O
			array("_pp31", true, false, false, true, false, strtotime('01-05-2013 08:30:00'), true), // n i UNDER
			array("_pp32", true, false, false, true, false, strtotime('01-05-2013 08:30:00'), true), // n i OVER
			array("_pp31", true, false, false, true, false, strtotime('01-05-2013 08:30:00'), false), // n i UNDER
			array("_pp32", true, false, false, true, false, strtotime('01-05-2013 08:31:00'), false) // n i OVER
		);
	}

	/**
	 * @covers Presence::isNormal
	 * @dataProvider providerTests
	 */
	public function testIsNormal($idx, $normal, $lunch, $service, $in, $out, $timestamp, $cut) {
		$pp = $this->{$idx};
		$this->assertEquals($normal, $pp->isNormal());
	}

	/**
	 * @covers Presence::isLunch
	 * @dataProvider providerTests
	 */
	public function testIsLunch($idx, $normal, $lunch, $service, $in, $out, $timestamp, $cut) {
		$pp = $this->{$idx};
		$this->assertEquals($lunch, $pp->isLunch());
	}

	/**
	 * @covers Presence::isService
	 * @dataProvider providerTests
	 */
	public function testIsService($idx, $normal, $lunch, $service, $in, $out, $timestamp, $cut) {
		$pp = $this->{$idx};
		$this->assertEquals($service, $pp->isService());
	}

	/**
	 * @covers Presence::isEntrance
	 * @dataProvider providerTests
	 */
	public function testIsEntrance($idx, $normal, $lunch, $service, $in, $out, $timestamp, $cut) {
		$pp = $this->{$idx};
		$this->assertEquals($in, $pp->isEntrance());
	}

	/**
	 * @covers Presence::isExit
	 * @dataProvider providerTests
	 */
	public function testIsExit($idx, $normal, $lunch, $service, $in, $out, $timestamp, $cut) {
		$pp = $this->{$idx};
		$this->assertEquals($out, $pp->isExit());
	}

	/**
	 * @covers Presence::roundSeconds
	 * @dataProvider providerTests
	 */
	public function testRoundSeconds($idx, $normal, $lunch, $service, $in, $out, $timestamp, $cut) {
		$pp = $this->{$idx};
		$this->assertEquals($timestamp, $pp->roundSeconds($cut));
	}

}