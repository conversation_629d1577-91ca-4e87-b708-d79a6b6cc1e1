<?xml version="1.0" encoding="utf-8"?>
<!--Autogenerated by PropelSchemaReverseTask class.-->
<database package="Auth" name="mc2api" defaultIdMethod="native" namespace="Auth">
    <table name="users" phpName="Users" idMethod="native">
        <column name="uid" phpName="Uid" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="user_name" phpName="UserName" type="VARCHAR" size="255" required="false" defaultValue="USER_NOT_DEFINED"/>
        <column name="user_password" phpName="UserPassword" type="VARCHAR" size="255" required="false" defaultValue="PASSWORD_NOT_DEFINED"/>
        <column name="name" phpName="Name" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="surname" phpName="Surname" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="version" phpName="Version" type="VARCHAR" size="255" required="false"/>
        <column name="lastcheck" phpName="Lastcheck" type="BIGINT" required="false"/>
        <column name="enabled" phpName="Enabled" type="INTEGER" required="false" defaultValue="0"/>
        <column name="user_type" phpName="UserType" type="INTEGER" required="false" defaultValue="NULL"/>
        <column name="privelege" phpName="Privelege" type="INTEGER" required="false" defaultValue="0"/>
        <column name="email" phpName="Email" type="VARCHAR" size="40" required="false"/>
        <column name="employee_id" phpName="EmployeeId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="modify_protocol" phpName="ModifyProtocol" type="INTEGER" required="false" defaultValue="0"/>
        <column name="super_user" phpName="SuperUser" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="expiration" phpName="Expiration" type="BIGINT" required="false"/>
        <id-method-parameter value="uid_seq"/>
    </table>
    <table name="groups" phpName="Groups" idMethod="native">
        <column name="gid" phpName="Gid" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="group_name" phpName="GroupName" type="VARCHAR" required="false"/>
        <column name="enabled" phpName="Enabled" type="SMALLINT" required="false" defaultValue="1"/>
    </table>
    <table name="auth_section" phpName="AuthSection" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="title" phpName="Title" type="VARCHAR" size="255" required="true"/>
    </table>
    <table name="auth_permission" phpName="AuthPermission" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="title" phpName="Title" type="VARCHAR" size="255" required="true"/>
        <column name="super_user" phpName="SuperUser" type="BOOLEAN" required="false" defaultValue="false"/>
        <column name="auth_section" phpName="AuthSection" type="INTEGER" required="false"/>
        <foreign-key foreignTable="auth_section" name="auth_permission_auth_section_fkey" phpName="AuthPermissionToSection" onDelete="CASCADE">
            <reference local="auth_section" foreign="id"/>
        </foreign-key>
    </table>
    <table name="auth_element" phpName="AuthElement" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="50" required="true"/>
        <column name="control_interface" phpName="ControlInterface" type="VARCHAR" size="50" required="true"/>
        <column name="auth_permission" phpName="AuthPermission" type="INTEGER" required="false"/>
        <column name="state" phpName="State" type="VARCHAR" size="10" required="true" defaultValue="hide"/>
        <foreign-key foreignTable="auth_permission" name="auth_element_auth_permission_fkey" phpName="AuthElementAuthPermission" onDelete="CASCADE">
            <reference local="auth_permission" foreign="id"/>
        </foreign-key>
    </table>
    <table name="auth_path" phpName="AuthPath" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="path" phpName="Path" type="VARCHAR" size="50" required="true"/>
        <column name="auth_permission" phpName="AuthPermission" type="INTEGER" required="false"/>
        <foreign-key foreignTable="auth_permission" name="auth_path_auth_permission_fkey" phpName="AuthPathAuthPermission" onDelete="CASCADE">
            <reference local="auth_permission" foreign="id"/>
        </foreign-key>
    </table>
    <table name="auth_permission_group" phpName="AuthPermissionGroup" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="groups" phpName="Groups" type="INTEGER" required="false"/>
        <column name="auth_permission" phpName="AuthPermission" type="INTEGER" required="false"/>
        <foreign-key foreignTable="auth_permission" name="auth_permission_group_auth_permission_fkey" phpName="AuthPermissionGroupAuthPermission" onDelete="CASCADE">
            <reference local="auth_permission" foreign="id"/>
        </foreign-key>
        <foreign-key foreignTable="groups" name="auth_permission_group_groups_fkey" phpName="AuthPermissionGroupGroups" onDelete="CASCADE">
            <reference local="groups" foreign="gid"/>
        </foreign-key>
    </table>
</database>
