<?xml version="1.0" encoding="utf-8"?>
<!--Autogenerated by PropelSchemaReverseTask class.-->
<database package="Budget" name="mc2api" defaultIdMethod="native" namespace="Budget">
	<table name="bdg_activities" phpName="Activity" idMethod="native">
		<column name="activ_id" phpName="ActivityId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
		<column name="aggreg_code" phpName="AggregateCode" type="CHAR" size="1" required="true"/>
		<column name="aggreg_nr" phpName="AggregateNumber" type="SMALLINT" required="true"/>
		<column name="description" phpName="Description" type="VARCHAR" size="200" required="true"/>
		<column name="ext_desc" phpName="DescriptionExtended" type="LONGVARCHAR" required="false"/>
		<column name="start_date" phpName="DateStart" type="BIGINT" required="false"/>
		<column name="end_date" phpName="DateEnd" type="BIGINT" required="false"/>
		<column name="suspend" phpName="Suspend" type="BOOLEAN" required="false" defaultValue="false"/>
		<column name="avm" phpName="AVM" type="BOOLEAN" required="false" defaultValue="false"/>
		<column name="notsdate" phpName="NotDateStart" type="BOOLEAN" required="false" defaultValue="false"/>
		<column name="notedate" phpName="NotDateEnd" type="BOOLEAN" required="false" defaultValue="false"/>
		<column name="responsibles" phpName="Responsibles" type="LONGVARCHAR" required="false"/>
		<column name="objectives" phpName="Objectives" type="LONGVARCHAR" required="false"/>
		<column name="human_resources" phpName="HumanResources" type="LONGVARCHAR" required="false"/>
		<column name="goods_services" phpName="GoodsServices" type="LONGVARCHAR" required="false"/>
		<column name="durata" phpName="Duration" type="LONGVARCHAR" required="false"/>
		<column name="budget_year" phpName="BudgetYear" type="INTEGER" required="true" defaultValue="2008"/>
		<column name="residui" phpName="Residuals" type="DOUBLE" required="true" defaultValue="0"/>
		<column name="hours_insertions_end_date" phpName="HoursInsertionsEndDate" type="BIGINT" required="false"/>
		<id-method-parameter value="bdg_activities_activ_id_seq"/>
	</table>
</database>