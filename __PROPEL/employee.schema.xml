<?xml version="1.0" encoding="utf-8"?>
<!--Autogenerated by PropelSchemaReverseTask class.-->
<database package="Employee" name="mc2api" defaultIdMethod="native" namespace="Employee">
    <table name="employee" phpName="Employee" idMethod="native">
        <column name="employee_id" phpName="EmployeeId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="100" required="true"/>
        <column name="surname" phpName="Surname" type="VARCHAR" size="50" required="true"/>
        <column name="gender" phpName="Gender" type="VARCHAR" size="10" required="false"/>
        <column name="birthdate" phpName="Birthdate" type="BIGINT" required="false" defaultValue="NULL"/>
        <column name="fiscal_code" phpName="FiscalCode" type="VARCHAR" size="30" required="false"/>
        <column name="residence_id" phpName="ResidenceId" type="INTEGER" required="false"/>
        <column name="address_id" phpName="AddressId" type="INTEGER" required="false" />
        <column name="part_spesa" phpName="PartSpesa" type="VARCHAR" size="16" required="false"/>
        <column name="bank" phpName="Bank" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="liq_office" phpName="LiqOffice" type="VARCHAR" size="4" required="false"/>
        <column name="inps" phpName="Inps" type="VARCHAR" size="17" required="false"/>
        <column name="insur_qual" phpName="InsurQual" type="CHAR" size="1" required="false"/>
        <column name="fore" phpName="Fore" type="BOOLEAN" required="false"/>
        <column name="asl" phpName="Asl" type="VARCHAR" size="10" required="false"/>
        <column name="adm_code" phpName="AdmCode" type="CHAR" size="16" required="false"/>
        <column name="way_pay" phpName="WayPay" type="CHAR" size="2" required="false"/>
        <column name="liquid_group" phpName="LiquidGroup" type="VARCHAR" size="4" required="false"/>
        <column name="contr_code" phpName="ContrCode" type="VARCHAR" size="2" required="false"/>
        <column name="contr_type" phpName="ContrType" type="VARCHAR" size="5" required="false"/>
        <column name="contr_cat" phpName="ContrCat" type="SMALLINT" required="false"/>
        <column name="ssp_frm_pmnt" phpName="SspFrmPmnt" type="SMALLINT" required="false"/>
        <column name="personal_data" phpName="PersonalData" type="SMALLINT" required="false" defaultValue="(-1)"/>
        <column name="susp" phpName="Susp" type="BOOLEAN" required="false"/>
        <column name="payment_group" phpName="PaymentGroup" type="INTEGER" required="false"/>
        <column name="priv_ret_type" phpName="PrivRetType" type="VARCHAR" size="5" required="false"/>
        <column name="social_position" phpName="SocialPosition" type="SMALLINT" required="false"/>
        <column name="active" phpName="Active" type="BOOLEAN" required="false" defaultValue="true"/>
        <column name="statal_code" phpName="StatalCode" type="VARCHAR" size="16" required="false"/>
        <column name="fiscal_city_code" phpName="FiscalCityCode" type="VARCHAR" size="16" required="false"/>
        <column name="birthplace" phpName="Birthplace" type="LONGVARCHAR" required="false"/>
        <column name="income" phpName="Income" type="BIGINT" required="false" defaultValue="0"/>
        <column name="state_birth" phpName="StateBirth" type="VARCHAR" size="5" required="false"/>
        <column name="citizenship" phpName="Citizenship" type="VARCHAR" size="5" required="false"/>
        <column name="id_sissi" phpName="IdSissi" type="VARCHAR" size="5" required="false"/>
        <column name="dom_first_prev_year" phpName="DomFirstPrevYear" type="INTEGER" required="false"/>
        <column name="dom_last_prev_year" phpName="DomLastPrevYear" type="INTEGER" required="false"/>
        <column name="dom_first_curr_year" phpName="DomFirstCurrYear" type="INTEGER" autoIncrement="true" required="true"/>
        <column name="qualification" phpName="Qualification" type="LONGVARCHAR" required="false"/>
        <column name="liquid_office_id" phpName="LiquidOfficeId" type="INTEGER" required="true" defaultValue="0"/>
        <column name="badge_number" phpName="BadgeNumber" type="BIGINT" required="false"/>
        <column name="tolerance_in" phpName="ToleranceIn" type="INTEGER" required="true" defaultValue="0"/>
        <column name="tolerance_out" phpName="ToleranceOut" type="INTEGER" required="true" defaultValue="0"/>
        <column name="flexibility" phpName="Flexibility" type="INTEGER" required="true" defaultValue="0"/>
        <column name="generic_tolerance" phpName="GenericTolerance" type="INTEGER" required="true" defaultValue="0"/>
        <column name="negative_round" phpName="NegativeRound" type="INTEGER" required="true" defaultValue="0"/>
        <column name="recover_hours" phpName="RecoverHours" type="INTEGER" required="true" defaultValue="100"/>
        <column name="max_extraordinary_in" phpName="MaxExtraordinaryIn" type="INTEGER" required="true" defaultValue="999"/>
        <column name="max_extraordinary_out" phpName="MaxExtraordinaryOut" type="INTEGER" required="true" defaultValue="999"/>
        <column name="min_extraordinary_in" phpName="MinExtraordinaryIn" type="INTEGER" required="true" defaultValue="0"/>
        <column name="min_extraordinary_out" phpName="MinExtraordinaryOut" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_out" phpName="StepOut" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_in" phpName="StepIn" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_break" phpName="MaxBreak" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_cont_work" phpName="MaxContWork" type="INTEGER" required="true" defaultValue="720"/>
        <column name="simplified_ata_settings" phpName="SimplifiedAtaSettings" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="tolerance_in_und" phpName="ToleranceInUnd" type="INTEGER" required="true" defaultValue="0"/>
        <column name="tolerance_out_und" phpName="ToleranceOutUnd" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_undefined_in" phpName="MaxUndefinedIn" type="INTEGER" required="true" defaultValue="999"/>
        <column name="max_undefined_out" phpName="MaxUndefinedOut" type="INTEGER" required="true" defaultValue="999"/>
        <column name="min_undefined_in" phpName="MinUndefinedIn" type="INTEGER" required="true" defaultValue="0"/>
        <column name="min_undefined_out" phpName="MinUndefinedOut" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_out_und" phpName="StepOutUnd" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_in_und" phpName="StepInUnd" type="INTEGER" required="true" defaultValue="0"/>
        <column name="undefined_parameter_active" phpName="UndefinedParameterActive" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="min_extraordinary_total" phpName="MinExtraordinaryTotal" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_extraordinary_total" phpName="MaxExtraordinaryTotal" type="INTEGER" required="true" defaultValue="999"/>
        <column name="min_undefined_total" phpName="MinUndefinedTotal" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_undefined_total" phpName="MaxUndefinedTotal" type="INTEGER" required="true" defaultValue="999"/>
        <column name="step_total_undefined" phpName="StepTotalUndefined" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_total_extraordinary" phpName="StepTotalExtraordinary" type="INTEGER" required="true" defaultValue="0"/>
        <column name="lunch_duration" phpName="LunchDuration" type="INTEGER" required="true" defaultValue="0"/>
        <column name="lunch_deductible" phpName="LunchDeductible" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="service_deductible" phpName="ServiceDeductible" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="min_undefined_lunch" phpName="MinUndefinedLunch" type="INTEGER" required="true" defaultValue="0"/>
        <column name="min_extraordinary_lunch" phpName="MinExtraordinaryLunch" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_undefined_lunch" phpName="MaxUndefinedLunch" type="INTEGER" required="true" defaultValue="0"/>
        <column name="max_extraordinary_lunch" phpName="MaxExtraordinaryLunch" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_lunch_undefined" phpName="StepLunchUndefined" type="INTEGER" required="true" defaultValue="0"/>
        <column name="step_lunch_extraordinary" phpName="StepLunchExtraordinary" type="INTEGER" required="true" defaultValue="0"/>
        <column name="break_after_max_work" phpName="BreakAfterMaxWork" type="INTEGER" required="true" defaultValue="0"/>
        <column name="unit_recover_hours" phpName="UnitRecoverHours" type="VARCHAR" size="3" required="true" defaultValue="%"/>
        <column name="max_work" phpName="MaxWork" type="INTEGER" required="true" defaultValue="999"/>
        <id-method-parameter value="employee_id_seq"/>
        <foreign-key foreignTable="contact" name="residence_key" phpName="ResidenceKey" onDelete="CASCADE">
            <reference local="residence_id" foreign="contact_id"/>
        </foreign-key>
        <foreign-key foreignTable="contact" name="address_key" phpName="AddressKey" onDelete="CASCADE">
            <reference local="address_id" foreign="contact_id"/>
        </foreign-key>
    </table>
    <table name="absences" phpName="Absences" idMethod="native">
        <column name="absence_id" phpName="AbsenceId" type="BIGINT" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="start_date" phpName="StartDate" type="BIGINT" required="false"/>
        <column name="end_date" phpName="EndDate" type="BIGINT" required="false"/>
        <column name="ab_kind" phpName="AbKind" type="VARCHAR" size="10" required="false"/>
        <column name="total_days" phpName="TotalDays" type="INTEGER" required="false"/>
        <column name="employee_id" phpName="EmployeeId" type="INTEGER" required="false"/>
        <column name="date_of_req" phpName="DateOfReq" type="BIGINT" required="false"/>
        <column name="protocol_id" phpName="ProtocolId" type="BIGINT" required="false"/>
        <column name="type_of_abs" phpName="TypeOfAbs" type="SMALLINT" required="false"/>
        <column name="decreto" phpName="Decreto" type="INTEGER" required="false"/>
        <column name="note" phpName="Note" type="LONGVARCHAR" required="false"/>
        <id-method-parameter value="absence_id_seq"/>
        <foreign-key foreignTable="absence_kind" name="foreign_absence_kind_absences" phpName="AbsencesAbsenceKind" onDelete="SET NULL">
            <reference local="ab_kind" foreign="code"/>
        </foreign-key>
        <foreign-key foreignTable="employee" name="foreign_employee" phpName="AbsenceEmployee" onDelete="CASCADE">
            <reference local="employee_id" foreign="employee_id"/>
        </foreign-key>
    </table>
    <table name="absence_kind" phpName="AbsenceKind" idMethod="native">
        <column name="code" phpName="Code" type="VARCHAR" size="10" required="true" primaryKey="true"/>
        <column name="description" phpName="Description" type="VARCHAR" size="50" required="true"/>
        <column name="absence_stack" phpName="AbsenceStack" type="BIGINT" required="false"/>
        <column name="date_start" phpName="DateStart" type="TIMESTAMP" required="false"/>
        <column name="date_end" phpName="DateEnd" type="TIMESTAMP" required="false"/>
        <column name="calc_festivities" phpName="CalcFestivities" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="calc_ferials" phpName="CalcFerials" type="BOOLEAN" required="true" defaultValue="false"/>
        <foreign-key foreignTable="absence_stack" name="foreign_absence_stack" phpName="AbsenceKindAbsenceStack" onDelete="SET NULL">
            <reference local="absence_stack" foreign="id"/>
        </foreign-key>
    </table>
    <table name="absence_stack" phpName="AbsenceStack" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="unit" phpName="Unit" type="VARCHAR" size="1" required="true" defaultValue="h"/>
        <column name="denomination" phpName="Denomination" type="VARCHAR" size="255" required="true"/>
        <column name="recover" phpName="Recover" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="reset_type" phpName="ResetType" type="SMALLINT" required="true" defaultValue="0"/>
        <column name="reset_to_stack_id" phpName="ResetToStackId" type="BIGINT" required="false" />
        <column name="reset_date" phpName="ResetDate" type="TIMESTAMP" required="false"/>
        <column name="reset_default_quota" phpName="ResetDefaultQuota" type="FLOAT" required="true" defaultValue="0.00"/>
    </table>
    <table name="decreti" phpName="Decreti" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" autoIncrement="true" required="true" primaryKey="true"/>
        <column name="absence_kind" phpName="AbsenceKind" type="VARCHAR" size="10" required="true"/>
        <column name="employee_kind" phpName="EmployeeKind" type="VARCHAR" size="10" required="true"/>
        <column name="employee_role" phpName="EmployeeRole" type="VARCHAR" size="10" required="true"/>
        <column name="html" phpName="Html" type="LONGVARCHAR" required="true"/>
        <column name="time_back" phpName="TimeBack" type="VARCHAR" size="50" required="true"/>
        <column name="prev_abs_kind" phpName="PrevAbsKind" type="VARCHAR" size="150" required="false"/>
    </table>
    <table name="personnel_timetable" phpName="Timetable" idMethod="native">
        <column name="personnel_timetable_id" phpName="PersonnelTimetableId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="employee_id" phpName="EmployeeId" type="BIGINT" required="true"/>
        <column name="date_start" phpName="DateStart" type="BIGINT" required="true"/>
        <column name="date_end" phpName="DateEnd" type="BIGINT" required="true"/>
        <column name="date_start_pause" phpName="DateStartPause" type="BIGINT" required="false"/>
        <column name="date_end_pause" phpName="DateEndPause" type="BIGINT" required="false"/>
        <id-method-parameter value="personnel_timetable_personnel_timetable_id_seq"/>
        <foreign-key foreignTable="employee" name="foreign_employee" phpName="TimetableEmployee" onDelete="CASCADE">
            <reference local="employee_id" foreign="employee_id"/>
        </foreign-key>
    </table>
    <table name="personnel_presences" phpName="Presence" idMethod="native">
        <column name="personnel_presence_id" phpName="PersonnelPresenceId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="employee_id" phpName="EmployeeId" type="BIGINT" required="true" defaultValue="-1"/>
        <column name="project_id" phpName="ProjectId" type="BIGINT" required="false"/>
        <column name="project_edit_id" phpName="ProjectIdEdit" type="BIGINT" required="false"/>
        <column name="date" phpName="Date" type="BIGINT" required="false"/>
        <column name="date_edit" phpName="DateEdit" type="BIGINT" required="false"/>
        <column name="type" phpName="Type" type="INTEGER" required="true" defaultValue="1"/>
        <column name="type_edit" phpName="TypeEdit" type="INTEGER" required="true" defaultValue="1"/>
        <column name="original_inout" phpName="OriginalInOut" type="INTEGER" required="true" defaultValue="-1"/>
        <column name="original_inout_edit" phpName="OriginalInOutEdit" type="INTEGER" required="true" defaultValue="1"/>
        <column name="description" phpName="Description" type="VARCHAR" required="true" defaultValue=""/>
        <column name="hour_type_id" phpName="HourTypeId" type="BIGINT" required="true" defaultValue="-1"/>
        <column name="hour_type_edit_id" phpName="HourTypeIdEdit" type="BIGINT" required="true" defaultValue="-1"/>
        <column name="insertion_mode" phpName="InsertionMode" type="VARCHAR" size="1" required="true" defaultValue="T"/>
        <id-method-parameter value="personnel_presences_personnel_presence_id_seq"/>
        <foreign-key foreignTable="employee" name="foreign_employee_presences" phpName="PresenceEmployee" onDelete="CASCADE">
            <reference local="employee_id" foreign="employee_id"/>
        </foreign-key>
        <foreign-key foreignTable="bdg_activities" name="foreign_bdg_activities_personnel_presences" phpName="PresenceProject" onDelete="SET NULL">
            <reference local="project_edit_id" foreign="activ_id"/>
        </foreign-key>
    </table>
    <table name="personnel_stacks" phpName="PersonnelStacks" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="employee_id" phpName="EmployeeId" type="BIGINT" required="true" defaultValue="-1"/>
        <column name="stack_id" phpName="StackId" type="BIGINT" required="true" defaultValue="-1"/>
        <column name="reset_quota" phpName="ResetQuota" type="FLOAT" required="true" defaultValue="0.00"/>
        <id-method-parameter value="personnel_stacks_id_seq"/>
        <foreign-key foreignTable="employee" name="personnel_stacks_employee_fkey" phpName="PersonnelStacksEmployee" onDelete="CASCADE">
            <reference local="employee_id" foreign="employee_id"/>
        </foreign-key>
        <foreign-key foreignTable="absence_stack" name="personnel_stacks_absence_stack_fkey" phpName="PersonnelStacksAbsenceStack" onDelete="CASCADE">
            <reference local="stack_id" foreign="id"/>
        </foreign-key>
    </table>
    <table name="storage_personnel_presences" phpName="StoredMonth" idMethod="native">
        <column name="storage_personnel_presences_id" phpName="StoredMonthId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="employee_id" phpName="EmployeeId" type="INTEGER" required="true" defaultValue="0"/>
        <column name="date_start" phpName="DateStart" type="BIGINT" required="true" defaultValue="0"/>
        <column name="date_end" phpName="DateEnd" type="BIGINT" required="true" defaultValue="0"/>
        <column name="ext_start_o" phpName="ExtStartOriginal" type="INTEGER" required="true" defaultValue="0"/>
        <column name="ext_end_o" phpName="ExtEndOriginal" type="INTEGER" required="true" defaultValue="0"/>
        <column name="ext_start" phpName="ExtStart" type="INTEGER" required="true" defaultValue="0"/>
        <column name="ext_end" phpName="ExtEnd" type="INTEGER" required="true" defaultValue="0"/>
        <column name="note" phpName="Note" type="LONGVARCHAR" required="false"/>
        <id-method-parameter value="storage_personnel_presences_storage_personnel_presences_id_seq"/>
        <foreign-key foreignTable="employee" name="foreign_employee_storage_personnel_presences" phpName="StoredMonthEmployee" onDelete="CASCADE">
            <reference local="employee_id" foreign="employee_id"/>
        </foreign-key>
    </table>
    <table name="extraordinary_stored" phpName="StoredDay" idMethod="native">
        <column name="extraordinary_stored_id" phpName="StoredDayId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="employee_id" phpName="EmployeeId" type="BIGINT" required="true"/>
        <column name="date" phpName="Date" type="BIGINT" required="true"/>
        <column name="extraordinary" phpName="Extraordinary" type="INTEGER" required="true" defaultValue="0"/>
        <column name="authorized" phpName="Authorized" type="INTEGER" required="true" defaultValue="0"/>
        <column name="note" phpName="Note" type="LONGVARCHAR" required="true" defaultValue=""/>
        <id-method-parameter value="extraordinary_stored_extraordinary_stored_id_seq"/>
        <foreign-key foreignTable="employee" name="foreign_employee_extraordinary_stored" phpName="StoredDayEmployee" onDelete="CASCADE">
            <reference local="employee_id" foreign="employee_id"/>
        </foreign-key>
    </table>
    <table name="storage_personnel_stack" phpName="StoredStack" idMethod="native">
        <column name="id" phpName="StoredStackId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="storage_personnel_presences" phpName="StoredMonth" type="BIGINT" required="false"/>
        <column name="absence_stack" phpName="AbsenceStack" type="BIGINT" required="false"/>
        <column name="stack_denomination" phpName="StackDenomination" type="VARCHAR" size="255" required="true"/>
        <column name="value_start_o" phpName="ValueStartOriginal" type="DOUBLE" required="true" defaultValue="0"/>
        <column name="value_end_o" phpName="ValueEndOriginal" type="DOUBLE" required="true" defaultValue="0"/>
        <column name="value_start" phpName="ValueStart" type="DOUBLE" required="true" defaultValue="0"/>
        <column name="value_end" phpName="ValueEnd" type="DOUBLE" required="true" defaultValue="0"/>
        <column name="unit" phpName="Unit" type="VARCHAR" size="1" required="true" defaultValue="h"/>
        <column name="recover" phpName="Recover" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="reset_type_applied" phpName="ResetTypeApplied" type="SMALLINT" required="true" defaultValue="0"/>
        <id-method-parameter value="storage_personnel_stack_id_seq"/>
        <foreign-key foreignTable="absence_stack" name="foreign_absence_stack" phpName="StoredStackAbsenceStack" onDelete="SET NULL">
            <reference local="absence_stack" foreign="id"/>
        </foreign-key>
        <foreign-key foreignTable="storage_personnel_presences" name="foreign_storage_personnel_presences" phpName="StoredStackStoredMonth" onDelete="CASCADE">
            <reference local="storage_personnel_presences" foreign="storage_personnel_presences_id"/>
        </foreign-key>
    </table>
</database>