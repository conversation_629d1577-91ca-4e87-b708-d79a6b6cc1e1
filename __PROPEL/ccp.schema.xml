<?xml version="1.0" encoding="utf-8"?>
<!--Autogenerated by PropelSchemaReverseTask class.-->
<database package="Ccp" name="mc2api" defaultIdMethod="native" namespace="Ccp">
    <table name="tax_residuals" phpName="TaxResiduals" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="tasse" phpName="Tasse" type="DOUBLE" required="false" defaultValue="0.00"/>
        <column name="contributi" phpName="Contributi" type="DOUBLE" required="false" defaultValue="0.00"/>
        <column name="quote" phpName="Quote" type="DOUBLE" required="false" defaultValue="0.00"/>
        <column name="diversi" phpName="Diversi" type="DOUBLE" required="false" defaultValue="0.00"/>
        <column name="debito" phpName="Debito" type="DOUBLE" required="false" defaultValue="0.00"/>
    </table>
    <table name="payment_ways" phpName="PaymentWays" idMethod="native">
        <column name="id" phpName="Id" type="VARCHAR" size="100" primaryKey="true" required="true"/>
        <column name="text" phpName="Text" type="VARCHAR" size="100" required="true"/>
    </table>
    <table name="tasse" phpName="Tasse" idMethod="native">
        <column name="id_tasse" phpName="IdTasse" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="id_tipo_tassa" phpName="IdTipoTassa" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="id_studente" phpName="IdStudente" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="sede_ufficio_postale" phpName="SedeUfficioPostale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="numero_versamento" phpName="NumeroVersamento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_versamento" phpName="DataVersamento" type="BIGINT" required="false" defaultValue="0"/>
        <column name="note" phpName="Note" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="anno_scolastico" phpName="AnnoScolastico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="riferimento_estratto_conto" phpName="RiferimentoEstrattoConto" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="data_estratto_conto" phpName="DataEstrattoConto" type="BIGINT" required="false" defaultValue="0"/>
        <column name="dati_debitore" phpName="DatiDebitore" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="numero_bollettino" phpName="NumeroBollettino" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="is_incoming" phpName="IsIncoming" type="BOOLEAN" required="false" defaultValue="true"/>
        <column name="employee_id" phpName="EmployeeId" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="tipologia_uscita" phpName="TipologiaUscita" type="VARCHAR" required="false" defaultValue="DIVERSI"/>
        <column name="destinazione_pagamento" phpName="DestinazionePagamento" type="VARCHAR" size="10" required="false" defaultValue="ccp_0"/>
        <column name="modalita_pagamento" phpName="ModalitaPagamento" type="VARCHAR" size="10" required="false"/>
        <column name="sede_studente" phpName="SedeStudente" type="INTEGER" required="false"/>
        <column name="classe" phpName="Classe" type="VARCHAR" size="64" required="false"/>
        <column name="importo_versato" phpName="ImportoVersato" type="FLOAT" required="false" defaultValue="0.00"/>
        <id-method-parameter value="tasse_id_seq"/>
        <foreign-key foreignTable="tipi_tasse" name="foreign_tasse_tipi_tasse">
            <reference local="id_tipo_tassa" foreign="id_tipo_tassa"/>
        </foreign-key>
        <foreign-key foreignTable="studenti_completi" name="foreign_tasse_studenti_completi">
            <reference local="id_studente" foreign="id_studente"/>
        </foreign-key>
        <foreign-key foreignTable="studenti" name="foreign_tasse_studenti">
            <reference local="id_studente" foreign="id_studente"/>
        </foreign-key>
        <index name="tasse_id_key">
            <index-column name="id_tasse"/>
        </index>
        <validator column="numero_versamento" translate="_">
            <rule name="required" message="Transaction number is mandatory" />
        </validator>
        <validator column="importo_versato" translate="_">
            <rule name="minValue" value="0" message="Amount must be positive" />
        </validator>
    </table>
    <table name="tipi_tasse" phpName="TipiTasse" idMethod="native">
        <column name="id_tipo_tassa" phpName="IdTipoTassa" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="descrizione" phpName="Descrizione" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipologia" phpName="Tipologia" type="VARCHAR" required="false" defaultValue="DIVERSI"/>
        <column name="tassa_governativa" phpName="TassaGovernativa" type="BOOLEAN" required="false" defaultValue="false"/>
        <column name="data_scadenza" phpName="DataScadenza" type="BIGINT" required="false" defaultValue="0"/>
        <column name="cumulativa" phpName="Cumulativa" type="INTEGER" required="false" defaultValue="0"/>
        <column name="anno_scolastico_riferimento" phpName="AnnoScolasticoRiferimento" type="VARCHAR" required="false" defaultValue="TUTTI"/>
        <column name="importo_base" phpName="ImportoBase" type="FLOAT" required="false" defaultValue="0.00"/>
        <id-method-parameter value="tipi_tasse_id_seq"/>
        <index name="tipo_tassa_id_key">
            <index-column name="id_tipo_tassa"/>
        </index>
        <validator column="descrizione" translate="_">
            <rule name="required" message="Description is mandatory." />
        </validator>
        <validator column="importo_base" translate="_">
            <rule name="minValue" value="0" message="Amount must be positive" />
        </validator>
    </table>
    <table name="studenti_completi" phpName="StudentiCompleti" idMethod="native" readOnly="true">
        <column name="id_studente" phpName="IdStudente" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="nome" phpName="Nome" type="VARCHAR" size="255" required="true" defaultValue="STUDENTE IGNOTO"/>
        <column name="cognome" phpName="Cognome" type="VARCHAR" size="255" required="true" defaultValue="STUDENTE IGNOTO"/>
        <column name="indirizzo" phpName="Indirizzo" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="citta" phpName="Citta" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="cap" phpName="Cap" type="VARCHAR" size="5" required="false" defaultValue=""/>
        <column name="provincia" phpName="Provincia" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="sesso" phpName="Sesso" type="VARCHAR" size="1" required="false" defaultValue=""/>
        <column name="telefono" phpName="Telefono" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="cellulare1" phpName="Cellulare1" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="cellulare2" phpName="Cellulare2" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="email1" phpName="Email1" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="email2" phpName="Email2" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="invio_email" phpName="InvioEmail" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="invio_email_cumulativo" phpName="InvioEmailCumulativo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="invio_email_parametrico" phpName="InvioEmailParametrico" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="invio_email_temporale" phpName="InvioEmailTemporale" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms" phpName="TipoSms" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms_cumulativo" phpName="TipoSmsCumulativo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms_parametrico" phpName="TipoSmsParametrico" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms_temporale" phpName="TipoSmsTemporale" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="aut_entrata_ritardo" phpName="AutEntrataRitardo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="aut_uscita_anticipo" phpName="AutUscitaAnticipo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="aut_pomeriggio" phpName="AutPomeriggio" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="acconsente" phpName="Acconsente" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="ritirato" phpName="Ritirato" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="data_nascita" phpName="DataNascita" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="codice_studente" phpName="CodiceStudente" type="VARCHAR" required="false" defaultValue=""/>
        <column name="password_studente" phpName="PasswordStudente" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_giustificazioni_studente" phpName="CodiceGiustificazioniStudente" type="BIGINT" required="false" defaultValue="0"/>
        <column name="esonero_religione" phpName="EsoneroReligione" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="materia_sostitutiva_religione" phpName="MateriaSostitutivaReligione" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="esonero_ed_fisica" phpName="EsoneroEdFisica" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="materia_sostitutiva_edfisica" phpName="MateriaSostitutivaEdfisica" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="crediti_terza" phpName="CreditiTerza" type="INTEGER" required="false" defaultValue="0"/>
        <column name="media_voti_terza" phpName="MediaVotiTerza" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="debiti_terza" phpName="DebitiTerza" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="crediti_sospesi_terza" phpName="CreditiSospesiTerza" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_reintegrati_terza" phpName="CreditiReintegratiTerza" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_quarta" phpName="CreditiQuarta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="media_voti_quarta" phpName="MediaVotiQuarta" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="debiti_quarta" phpName="DebitiQuarta" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="crediti_sospesi_quarta" phpName="CreditiSospesiQuarta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_reintegrati_quarta" phpName="CreditiReintegratiQuarta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_quinta" phpName="CreditiQuinta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="media_voti_quinta" phpName="MediaVotiQuinta" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="crediti_finali_agg" phpName="CreditiFinaliAgg" type="INTEGER" required="false" defaultValue="0"/>
        <column name="matricola" phpName="Matricola" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="luogo_nascita" phpName="LuogoNascita" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="provincia_nascita" phpName="ProvinciaNascita" type="VARCHAR" size="50" required="false" defaultValue=""/>
        <column name="motivi_crediti_terza" phpName="MotiviCreditiTerza" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="motivi_crediti_quarta" phpName="MotiviCreditiQuarta" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="motivi_crediti_quinta" phpName="MotiviCreditiQuinta" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="motivi_crediti_agg" phpName="MotiviCreditiAgg" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="codice_comune_nascita" phpName="CodiceComuneNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="stato_nascita" phpName="StatoNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cittadinanza" phpName="Cittadinanza" type="VARCHAR" required="false" defaultValue="-1"/>
        <column name="seconda_cittadinanza" phpName="SecondaCittadinanza" type="VARCHAR" required="false" defaultValue="-1"/>
        <column name="codice_comune_residenza" phpName="CodiceComuneResidenza" type="VARCHAR" required="false" defaultValue=""/>
        <column name="distretto" phpName="Distretto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_fiscale" phpName="CodiceFiscale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="medico" phpName="Medico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="telefono_medico" phpName="TelefonoMedico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="intolleranze_alim" phpName="IntolleranzeAlim" type="VARCHAR" required="false" defaultValue=""/>
        <column name="gruppo_sanguigno" phpName="GruppoSanguigno" type="VARCHAR" required="false" defaultValue=""/>
        <column name="gruppo_rh" phpName="GruppoRh" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_asl" phpName="CodiceAsl" type="VARCHAR" required="false" defaultValue=""/>
        <column name="annotazioni" phpName="Annotazioni" type="VARCHAR" required="false" defaultValue=""/>
        <column name="stato_civile" phpName="StatoCivile" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_primo_scritto" phpName="VotoPrimoScritto" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_secondo_scritto" phpName="VotoSecondoScritto" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_terzo_scritto" phpName="VotoTerzoScritto" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_orale" phpName="VotoOrale" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_bonus" phpName="VotoBonus" type="INTEGER" required="false" defaultValue="0"/>
        <column name="materia_secondo_scr" phpName="MateriaSecondoScr" type="VARCHAR" required="false" defaultValue=""/>
        <column name="ulteriori_specif_diploma" phpName="UlterioriSpecifDiploma" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="numero_diploma" phpName="NumeroDiploma" type="INTEGER" required="false" defaultValue="0"/>
        <column name="chi_inserisce" phpName="ChiInserisce" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="data_inserimento" phpName="DataInserimento" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_inserimento" phpName="TipoInserimento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="chi_modifica" phpName="ChiModifica" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="data_modifica" phpName="DataModifica" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_modifica" phpName="TipoModifica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="flag_canc" phpName="FlagCanc" type="BIGINT" required="false" defaultValue="0"/>
        <column name="stato_avanzamento" phpName="StatoAvanzamento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_stato_avanzamento" phpName="DataStatoAvanzamento" type="BIGINT" required="false" defaultValue="0"/>
        <column name="cap_provincia_nascita" phpName="CapProvinciaNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="badge" phpName="Badge" type="BIGINT" required="false" defaultValue="0"/>
        <column name="cap_residenza" phpName="CapResidenza" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_comune_domicilio" phpName="CodiceComuneDomicilio" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cap_domicilio" phpName="CapDomicilio" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cap_nascita" phpName="CapNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="indirizzo_domicilio" phpName="IndirizzoDomicilio" type="VARCHAR" required="false" defaultValue=""/>
        <column name="citta_nascita_straniera" phpName="CittaNascitaStraniera" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cellulare_allievo" phpName="CellulareAllievo" type="VARCHAR" required="false" defaultValue=""/>
        <column name="handicap" phpName="Handicap" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="stato_convittore" phpName="StatoConvittore" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="data_ritiro" phpName="DataRitiro" type="BIGINT" required="false" defaultValue="0"/>
        <column name="voto_ammissione" phpName="VotoAmmissione" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="differenza_punteggio" phpName="DifferenzaPunteggio" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_qualifica" phpName="VotoQualifica" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_esame_sc1_qual" phpName="VotoEsameSc1Qual" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_esame_sc2_qual" phpName="VotoEsameSc2Qual" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_esame_or_qual" phpName="VotoEsameOrQual" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="stato_privatista" phpName="StatoPrivatista" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="foto" phpName="Foto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="rappresentante" phpName="Rappresentante" type="VARCHAR" required="false" defaultValue="NO#NO#NO"/>
        <column name="obbligo_formativo" phpName="ObbligoFormativo" type="VARCHAR" required="false" defaultValue="01"/>
        <column name="id_lingua_1" phpName="IdLingua1" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_2" phpName="IdLingua2" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_3" phpName="IdLingua3" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_4" phpName="IdLingua4" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_5" phpName="IdLingua5" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_provenienza_scolastica" phpName="IdProvenienzaScolastica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="id_scuola_media" phpName="IdScuolaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="lingua_scuola_media" phpName="LinguaScuolaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="lingua_scuola_media_2" phpName="LinguaScuolaMedia2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_scuola_media" phpName="GiudizioScuolaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="trasporto" phpName="Trasporto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_iscrizione" phpName="DataIscrizione" type="BIGINT" required="false" defaultValue="0"/>
        <column name="pei" phpName="Pei" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="ammesso_esame_qualifica" phpName="AmmessoEsameQualifica" type="VARCHAR" required="false" defaultValue="--"/>
        <column name="ammesso_esame_quinta" phpName="AmmessoEsameQuinta" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="giudizio_ammissione_quinta" phpName="GiudizioAmmissioneQuinta" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="grado_handicap" phpName="GradoHandicap" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="tipo_handicap" phpName="TipoHandicap" type="VARCHAR" required="false" defaultValue=""/>
        <column name="stato_licenza_maestro" phpName="StatoLicenzaMaestro" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="id_studente_sissi" phpName="IdStudenteSissi" type="VARCHAR" required="false" defaultValue=""/>
        <column name="badge_rfid" phpName="BadgeRfid" type="VARCHAR" required="false" defaultValue=""/>
        <column name="lode" phpName="Lode" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="distretto_scolastico" phpName="DistrettoScolastico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_ammissione_terza" phpName="GiudizioAmmissioneTerza" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="esito_prima_media" phpName="EsitoPrimaMedia" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="esito_seconda_media" phpName="EsitoSecondaMedia" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="esito_terza_media" phpName="EsitoTerzaMedia" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="giudizio_esame_sc1_qual" phpName="GiudizioEsameSc1Qual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_esame_sc2_qual" phpName="GiudizioEsameSc2Qual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_esame_or_qual" phpName="GiudizioEsameOrQual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_complessivo_esame_qual" phpName="GiudizioComplessivoEsameQual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="acconsente_aziende" phpName="AcconsenteAziende" type="INTEGER" required="false" defaultValue="1"/>
        <column name="curriculum_prima" phpName="CurriculumPrima" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="curriculum_seconda" phpName="CurriculumSeconda" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="stage_professionali" phpName="StageProfessionali" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="data_orale" phpName="DataOrale" type="BIGINT" required="false" defaultValue="0"/>
        <column name="ordine_esame_orale" phpName="OrdineEsameOrale" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_primo_scritto" phpName="TipoPrimoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_secondo_scritto" phpName="TipoSecondoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_terzo_scritto" phpName="TipoTerzoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_primo_scritto" phpName="UnanimitaPrimoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_secondo_scritto" phpName="UnanimitaSecondoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_terzo_scritto" phpName="UnanimitaTerzoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="argomento_scelto_orale" phpName="ArgomentoSceltoOrale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="area_disc_1_orale" phpName="AreaDisc1Orale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="area_disc_2_orale" phpName="AreaDisc2Orale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="disc_elaborati_orale" phpName="DiscElaboratiOrale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_voto_finale" phpName="UnanimitaVotoFinale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="presente_esame_quinta" phpName="PresenteEsameQuinta" type="VARCHAR" required="false" defaultValue="SI"/>
        <column name="stampa_badge" phpName="StampaBadge" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="id_classe_destinazione" phpName="IdClasseDestinazione" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="sconto_rette" phpName="ScontoRette" type="INTEGER" required="false" defaultValue="0"/>
        <column name="carta_studente_numero" phpName="CartaStudenteNumero" type="BIGINT" required="false" defaultValue="0"/>
        <column name="carta_studente_scadenza" phpName="CartaStudenteScadenza" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="esito_corrente_calcolato" phpName="EsitoCorrenteCalcolato" type="VARCHAR" required="true" defaultValue=""/>
        <column name="id_flusso" phpName="IdFlusso" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="data_aggiornamento_sogei" phpName="DataAggiornamentoSogei" type="VARCHAR" required="true" defaultValue=""/>
        <column name="codice_alunno_ministeriale" phpName="CodiceAlunnoMinisteriale" type="VARCHAR" required="true" defaultValue=""/>
        <column name="flag_cf_fittizio" phpName="FlagCfFittizio" type="INTEGER" required="true" defaultValue="0"/>
        <column name="flag_s2f" phpName="FlagS2f" type="VARCHAR" required="true" defaultValue=""/>
        <column name="codice_stato_sogei" phpName="CodiceStatoSogei" type="VARCHAR" required="true" defaultValue=""/>
        <column name="codice_gruppo_nomade" phpName="CodiceGruppoNomade" type="VARCHAR" required="true" defaultValue=""/>
        <column name="flag_minore_straniero" phpName="FlagMinoreStraniero" type="INTEGER" required="true" defaultValue="0"/>
        <column name="chiave" phpName="Chiave" type="VARCHAR" required="false"/>
        <column name="voto_esame_medie_italiano" phpName="VotoEsameMedieItaliano" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_inglese" phpName="VotoEsameMedieInglese" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_matematica" phpName="VotoEsameMedieMatematica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_seconda_lingua" phpName="VotoEsameMedieSecondaLingua" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_invalsi_ita" phpName="VotoEsameMedieInvalsiIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_invalsi_mat" phpName="VotoEsameMedieInvalsiMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_orale" phpName="VotoEsameMedieOrale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_ammissione_medie" phpName="VotoAmmissioneMedie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_prima_elementare" phpName="EsitoPrimaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_seconda_elementare" phpName="EsitoSecondaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_terza_elementare" phpName="EsitoTerzaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_quarta_elementare" phpName="EsitoQuartaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_quinta_elementare" phpName="EsitoQuintaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_voto_esame_medie_italiano" phpName="TipoVotoEsameMedieItaliano" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_voto_esame_medie_inglese" phpName="TipoVotoEsameMedieInglese" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_1_medie" phpName="Giudizio1Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_2_medie" phpName="Giudizio2Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_3_medie" phpName="Giudizio3Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="argomenti_orali_medie" phpName="ArgomentiOraliMedie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_finale_1_medie" phpName="GiudizioFinale1Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_finale_2_medie" phpName="GiudizioFinale2Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_finale_3_medie" phpName="GiudizioFinale3Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="consiglio_terza_media" phpName="ConsiglioTerzaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_sintetico_esame_terza_media" phpName="GiudizioSinteticoEsameTerzaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_arrivo_in_italia" phpName="DataArrivoInItalia" type="INTEGER" required="false" defaultValue="0"/>
        <column name="frequenza_asilo_nido" phpName="FrequenzaAsiloNido" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="frequenza_scuola_materna" phpName="FrequenzaScuolaMaterna" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="data_aggiornamento_sidi" phpName="DataAggiornamentoSidi" type="INTEGER" required="false" defaultValue="0"/>
        <column name="cmp_sup_val_ita" phpName="CmpSupValIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_ita" phpName="CmpSupTxtIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_ing" phpName="CmpSupValIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_ing" phpName="CmpSupTxtIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_altri" phpName="CmpSupValAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_altri" phpName="CmpSupTxtAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_mat" phpName="CmpSupValMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_mat" phpName="CmpSupTxtMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_sci_tec" phpName="CmpSupValSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_sci_tec" phpName="CmpSupTxtSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_sto_soc" phpName="CmpSupValStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_sto_soc" phpName="CmpSupTxtStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_ita" phpName="CmpMedValIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_ita" phpName="CmpMedTxtIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_ing" phpName="CmpMedValIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_ing" phpName="CmpMedTxtIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_altri" phpName="CmpMedValAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_altri" phpName="CmpMedTxtAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_mat" phpName="CmpMedValMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_mat" phpName="CmpMedTxtMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_sci_tec" phpName="CmpMedValSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_sci_tec" phpName="CmpMedTxtSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_sto_soc" phpName="CmpMedValStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_sto_soc" phpName="CmpMedTxtStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_l2" phpName="CmpMedValL2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_l2" phpName="CmpMedTxtL2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_l3" phpName="CmpMedValL3" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_l3" phpName="CmpMedTxtL3" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_arte" phpName="CmpMedValArte" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_arte" phpName="CmpMedTxtArte" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_mus" phpName="CmpMedValMus" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_mus" phpName="CmpMedTxtMus" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_mot" phpName="CmpMedValMot" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_mot" phpName="CmpMedTxtMot" type="VARCHAR" required="false" defaultValue=""/>
        <index name="id_studente">
            <index-column name="id_studente"/>
        </index>
    </table>
    <table name="classi" phpName="Classi" idMethod="native">
        <column name="id_classe" phpName="IdClasse" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="classe" phpName="Classe" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="sezione" phpName="Sezione" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="id_indirizzo" phpName="IdIndirizzo" type="INTEGER" required="true"/>
        <column name="codice_registro" phpName="CodiceRegistro" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="ordinamento" phpName="Ordinamento" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="chi_inserisce" phpName="ChiInserisce" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="data_inserimento" phpName="DataInserimento" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_inserimento" phpName="TipoInserimento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="chi_modifica" phpName="ChiModifica" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="data_modifica" phpName="DataModifica" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_modifica" phpName="TipoModifica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="flag_canc" phpName="FlagCanc" type="BIGINT" required="false" defaultValue="0"/>
        <column name="codice_registro_2" phpName="CodiceRegistro2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_registro_3" phpName="CodiceRegistro3" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_registro_4" phpName="CodiceRegistro4" type="VARCHAR" required="false" defaultValue=""/>
        <column name="blocco_scrutini" phpName="BloccoScrutini" type="VARCHAR" required="false" defaultValue=""/>
        <column name="consiglio_classe_attivo" phpName="ConsiglioClasseAttivo" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="tempo_funzionamento" phpName="TempoFunzionamento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="pubb_primo_scritto" phpName="PubbPrimoScritto" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="pubb_secondo_scritto" phpName="PubbSecondoScritto" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="pubb_terzo_scritto" phpName="PubbTerzoScritto" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="pubb_orale" phpName="PubbOrale" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="id_flusso" phpName="IdFlusso" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="autenticazione_alternativa" phpName="AutenticazioneAlternativa" type="VARCHAR" required="false"/>
        <column name="effettua_controllo_gate" phpName="EffettuaControlloGate" type="VARCHAR" required="false" defaultValue="SI"/>
        <column name="data_aggiornamento_sidi" phpName="DataAggiornamentoSidi" type="INTEGER" required="false" defaultValue="0"/>
    </table>
    <table name="studenti" phpName="Studenti" idMethod="native">
        <column name="id_studente" phpName="IdStudente" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="nome" phpName="Nome" type="VARCHAR" size="255" required="true" defaultValue="STUDENTE IGNOTO"/>
        <column name="cognome" phpName="Cognome" type="VARCHAR" size="255" required="true" defaultValue="STUDENTE IGNOTO"/>
        <column name="indirizzo" phpName="Indirizzo" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="citta" phpName="Citta" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="cap" phpName="Cap" type="VARCHAR" size="5" required="false" defaultValue=""/>
        <column name="provincia" phpName="Provincia" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="sesso" phpName="Sesso" type="VARCHAR" size="1" required="false" defaultValue=""/>
        <column name="telefono" phpName="Telefono" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="cellulare1" phpName="Cellulare1" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="cellulare2" phpName="Cellulare2" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="email1" phpName="Email1" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="email2" phpName="Email2" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="invio_email" phpName="InvioEmail" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="invio_email_cumulativo" phpName="InvioEmailCumulativo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="invio_email_parametrico" phpName="InvioEmailParametrico" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="invio_email_temporale" phpName="InvioEmailTemporale" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms" phpName="TipoSms" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms_cumulativo" phpName="TipoSmsCumulativo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms_parametrico" phpName="TipoSmsParametrico" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="tipo_sms_temporale" phpName="TipoSmsTemporale" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="aut_entrata_ritardo" phpName="AutEntrataRitardo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="aut_uscita_anticipo" phpName="AutUscitaAnticipo" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="aut_pomeriggio" phpName="AutPomeriggio" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="acconsente" phpName="Acconsente" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="ritirato" phpName="Ritirato" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="data_nascita" phpName="DataNascita" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="codice_studente" phpName="CodiceStudente" type="VARCHAR" required="false" defaultValue=""/>
        <column name="password_studente" phpName="PasswordStudente" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_giustificazioni_studente" phpName="CodiceGiustificazioniStudente" type="BIGINT" required="false" defaultValue="0"/>
        <column name="esonero_religione" phpName="EsoneroReligione" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="materia_sostitutiva_religione" phpName="MateriaSostitutivaReligione" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="esonero_ed_fisica" phpName="EsoneroEdFisica" type="VARCHAR" size="1" required="false" defaultValue="0"/>
        <column name="materia_sostitutiva_edfisica" phpName="MateriaSostitutivaEdfisica" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="crediti_terza" phpName="CreditiTerza" type="INTEGER" required="false" defaultValue="0"/>
        <column name="media_voti_terza" phpName="MediaVotiTerza" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="debiti_terza" phpName="DebitiTerza" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="crediti_sospesi_terza" phpName="CreditiSospesiTerza" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_reintegrati_terza" phpName="CreditiReintegratiTerza" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_quarta" phpName="CreditiQuarta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="media_voti_quarta" phpName="MediaVotiQuarta" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="debiti_quarta" phpName="DebitiQuarta" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="crediti_sospesi_quarta" phpName="CreditiSospesiQuarta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_reintegrati_quarta" phpName="CreditiReintegratiQuarta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="crediti_quinta" phpName="CreditiQuinta" type="INTEGER" required="false" defaultValue="0"/>
        <column name="media_voti_quinta" phpName="MediaVotiQuinta" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="crediti_finali_agg" phpName="CreditiFinaliAgg" type="INTEGER" required="false" defaultValue="0"/>
        <column name="matricola" phpName="Matricola" type="VARCHAR" size="255" required="false" defaultValue="0"/>
        <column name="luogo_nascita" phpName="LuogoNascita" type="VARCHAR" size="255" required="false" defaultValue=""/>
        <column name="provincia_nascita" phpName="ProvinciaNascita" type="VARCHAR" size="50" required="false" defaultValue=""/>
        <column name="motivi_crediti_terza" phpName="MotiviCreditiTerza" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="motivi_crediti_quarta" phpName="MotiviCreditiQuarta" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="motivi_crediti_quinta" phpName="MotiviCreditiQuinta" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="motivi_crediti_agg" phpName="MotiviCreditiAgg" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="codice_comune_nascita" phpName="CodiceComuneNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="stato_nascita" phpName="StatoNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cittadinanza" phpName="Cittadinanza" type="VARCHAR" required="false" defaultValue="-1"/>
        <column name="seconda_cittadinanza" phpName="SecondaCittadinanza" type="VARCHAR" required="false" defaultValue="-1"/>
        <column name="codice_comune_residenza" phpName="CodiceComuneResidenza" type="VARCHAR" required="false" defaultValue=""/>
        <column name="distretto" phpName="Distretto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_fiscale" phpName="CodiceFiscale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="medico" phpName="Medico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="telefono_medico" phpName="TelefonoMedico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="intolleranze_alim" phpName="IntolleranzeAlim" type="VARCHAR" required="false" defaultValue=""/>
        <column name="gruppo_sanguigno" phpName="GruppoSanguigno" type="VARCHAR" required="false" defaultValue=""/>
        <column name="gruppo_rh" phpName="GruppoRh" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_asl" phpName="CodiceAsl" type="VARCHAR" required="false" defaultValue=""/>
        <column name="annotazioni" phpName="Annotazioni" type="VARCHAR" required="false" defaultValue=""/>
        <column name="stato_civile" phpName="StatoCivile" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_primo_scritto" phpName="VotoPrimoScritto" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_secondo_scritto" phpName="VotoSecondoScritto" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_terzo_scritto" phpName="VotoTerzoScritto" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_orale" phpName="VotoOrale" type="INTEGER" required="false" defaultValue="0"/>
        <column name="voto_bonus" phpName="VotoBonus" type="INTEGER" required="false" defaultValue="0"/>
        <column name="materia_secondo_scr" phpName="MateriaSecondoScr" type="VARCHAR" required="false" defaultValue=""/>
        <column name="ulteriori_specif_diploma" phpName="UlterioriSpecifDiploma" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="numero_diploma" phpName="NumeroDiploma" type="INTEGER" required="false" defaultValue="0"/>
        <column name="chi_inserisce" phpName="ChiInserisce" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="data_inserimento" phpName="DataInserimento" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_inserimento" phpName="TipoInserimento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="chi_modifica" phpName="ChiModifica" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="data_modifica" phpName="DataModifica" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_modifica" phpName="TipoModifica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="flag_canc" phpName="FlagCanc" type="BIGINT" required="false" defaultValue="0"/>
        <column name="stato_avanzamento" phpName="StatoAvanzamento" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_stato_avanzamento" phpName="DataStatoAvanzamento" type="BIGINT" required="false" defaultValue="0"/>
        <column name="cap_provincia_nascita" phpName="CapProvinciaNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="badge" phpName="Badge" type="BIGINT" required="false" defaultValue="0"/>
        <column name="cap_residenza" phpName="CapResidenza" type="VARCHAR" required="false" defaultValue=""/>
        <column name="codice_comune_domicilio" phpName="CodiceComuneDomicilio" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cap_domicilio" phpName="CapDomicilio" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cap_nascita" phpName="CapNascita" type="VARCHAR" required="false" defaultValue=""/>
        <column name="indirizzo_domicilio" phpName="IndirizzoDomicilio" type="VARCHAR" required="false" defaultValue=""/>
        <column name="citta_nascita_straniera" phpName="CittaNascitaStraniera" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cellulare_allievo" phpName="CellulareAllievo" type="VARCHAR" required="false" defaultValue=""/>
        <column name="handicap" phpName="Handicap" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="stato_convittore" phpName="StatoConvittore" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="data_ritiro" phpName="DataRitiro" type="BIGINT" required="false" defaultValue="0"/>
        <column name="voto_ammissione" phpName="VotoAmmissione" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="differenza_punteggio" phpName="DifferenzaPunteggio" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_qualifica" phpName="VotoQualifica" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_esame_sc1_qual" phpName="VotoEsameSc1Qual" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_esame_sc2_qual" phpName="VotoEsameSc2Qual" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="voto_esame_or_qual" phpName="VotoEsameOrQual" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="stato_privatista" phpName="StatoPrivatista" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="foto" phpName="Foto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="rappresentante" phpName="Rappresentante" type="VARCHAR" required="false" defaultValue="NO#NO#NO"/>
        <column name="obbligo_formativo" phpName="ObbligoFormativo" type="VARCHAR" required="false" defaultValue="01"/>
        <column name="id_lingua_1" phpName="IdLingua1" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_2" phpName="IdLingua2" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_3" phpName="IdLingua3" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_4" phpName="IdLingua4" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_lingua_5" phpName="IdLingua5" type="BIGINT" required="false" defaultValue="(-1)"/>
        <column name="id_provenienza_scolastica" phpName="IdProvenienzaScolastica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="id_scuola_media" phpName="IdScuolaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="lingua_scuola_media" phpName="LinguaScuolaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="lingua_scuola_media_2" phpName="LinguaScuolaMedia2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_scuola_media" phpName="GiudizioScuolaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="trasporto" phpName="Trasporto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_iscrizione" phpName="DataIscrizione" type="BIGINT" required="false" defaultValue="0"/>
        <column name="pei" phpName="Pei" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="ammesso_esame_qualifica" phpName="AmmessoEsameQualifica" type="VARCHAR" required="false" defaultValue="--"/>
        <column name="ammesso_esame_quinta" phpName="AmmessoEsameQuinta" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="giudizio_ammissione_quinta" phpName="GiudizioAmmissioneQuinta" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="grado_handicap" phpName="GradoHandicap" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="tipo_handicap" phpName="TipoHandicap" type="VARCHAR" required="false" defaultValue=""/>
        <column name="stato_licenza_maestro" phpName="StatoLicenzaMaestro" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="id_studente_sissi" phpName="IdStudenteSissi" type="VARCHAR" required="false" defaultValue=""/>
        <column name="badge_rfid" phpName="BadgeRfid" type="VARCHAR" required="false" defaultValue=""/>
        <column name="lode" phpName="Lode" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="distretto_scolastico" phpName="DistrettoScolastico" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_ammissione_terza" phpName="GiudizioAmmissioneTerza" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="esito_prima_media" phpName="EsitoPrimaMedia" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="esito_seconda_media" phpName="EsitoSecondaMedia" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="esito_terza_media" phpName="EsitoTerzaMedia" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="giudizio_esame_sc1_qual" phpName="GiudizioEsameSc1Qual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_esame_sc2_qual" phpName="GiudizioEsameSc2Qual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_esame_or_qual" phpName="GiudizioEsameOrQual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_complessivo_esame_qual" phpName="GiudizioComplessivoEsameQual" type="LONGVARCHAR" required="false" defaultValue=""/>
        <column name="acconsente_aziende" phpName="AcconsenteAziende" type="INTEGER" required="false" defaultValue="1"/>
        <column name="curriculum_prima" phpName="CurriculumPrima" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="curriculum_seconda" phpName="CurriculumSeconda" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="stage_professionali" phpName="StageProfessionali" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="data_orale" phpName="DataOrale" type="BIGINT" required="false" defaultValue="0"/>
        <column name="ordine_esame_orale" phpName="OrdineEsameOrale" type="BIGINT" required="false" defaultValue="0"/>
        <column name="tipo_primo_scritto" phpName="TipoPrimoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_secondo_scritto" phpName="TipoSecondoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_terzo_scritto" phpName="TipoTerzoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_primo_scritto" phpName="UnanimitaPrimoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_secondo_scritto" phpName="UnanimitaSecondoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_terzo_scritto" phpName="UnanimitaTerzoScritto" type="VARCHAR" required="false" defaultValue=""/>
        <column name="argomento_scelto_orale" phpName="ArgomentoSceltoOrale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="area_disc_1_orale" phpName="AreaDisc1Orale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="area_disc_2_orale" phpName="AreaDisc2Orale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="disc_elaborati_orale" phpName="DiscElaboratiOrale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="unanimita_voto_finale" phpName="UnanimitaVotoFinale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="presente_esame_quinta" phpName="PresenteEsameQuinta" type="VARCHAR" required="false" defaultValue="SI"/>
        <column name="stampa_badge" phpName="StampaBadge" type="VARCHAR" required="false" defaultValue="NO"/>
        <column name="id_classe_destinazione" phpName="IdClasseDestinazione" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="sconto_rette" phpName="ScontoRette" type="INTEGER" required="false" defaultValue="0"/>
        <column name="carta_studente_numero" phpName="CartaStudenteNumero" type="BIGINT" required="false" defaultValue="0"/>
        <column name="carta_studente_scadenza" phpName="CartaStudenteScadenza" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="esito_corrente_calcolato" phpName="EsitoCorrenteCalcolato" type="VARCHAR" required="true" defaultValue=""/>
        <column name="id_flusso" phpName="IdFlusso" type="VARCHAR" required="false" defaultValue="0"/>
        <column name="data_aggiornamento_sogei" phpName="DataAggiornamentoSogei" type="VARCHAR" required="true" defaultValue=""/>
        <column name="codice_alunno_ministeriale" phpName="CodiceAlunnoMinisteriale" type="VARCHAR" required="true" defaultValue=""/>
        <column name="flag_cf_fittizio" phpName="FlagCfFittizio" type="INTEGER" required="true" defaultValue="0"/>
        <column name="flag_s2f" phpName="FlagS2f" type="VARCHAR" required="true" defaultValue=""/>
        <column name="codice_stato_sogei" phpName="CodiceStatoSogei" type="VARCHAR" required="true" defaultValue=""/>
        <column name="codice_gruppo_nomade" phpName="CodiceGruppoNomade" type="VARCHAR" required="true" defaultValue=""/>
        <column name="flag_minore_straniero" phpName="FlagMinoreStraniero" type="INTEGER" required="true" defaultValue="0"/>
        <column name="chiave" phpName="Chiave" type="VARCHAR" required="false"/>
        <column name="voto_esame_medie_italiano" phpName="VotoEsameMedieItaliano" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_inglese" phpName="VotoEsameMedieInglese" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_matematica" phpName="VotoEsameMedieMatematica" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_seconda_lingua" phpName="VotoEsameMedieSecondaLingua" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_invalsi_ita" phpName="VotoEsameMedieInvalsiIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_invalsi_mat" phpName="VotoEsameMedieInvalsiMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_esame_medie_orale" phpName="VotoEsameMedieOrale" type="VARCHAR" required="false" defaultValue=""/>
        <column name="voto_ammissione_medie" phpName="VotoAmmissioneMedie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_prima_elementare" phpName="EsitoPrimaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_seconda_elementare" phpName="EsitoSecondaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_terza_elementare" phpName="EsitoTerzaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_quarta_elementare" phpName="EsitoQuartaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="esito_quinta_elementare" phpName="EsitoQuintaElementare" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_voto_esame_medie_italiano" phpName="TipoVotoEsameMedieItaliano" type="VARCHAR" required="false" defaultValue=""/>
        <column name="tipo_voto_esame_medie_inglese" phpName="TipoVotoEsameMedieInglese" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_1_medie" phpName="Giudizio1Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_2_medie" phpName="Giudizio2Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_3_medie" phpName="Giudizio3Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="argomenti_orali_medie" phpName="ArgomentiOraliMedie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_finale_1_medie" phpName="GiudizioFinale1Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_finale_2_medie" phpName="GiudizioFinale2Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_finale_3_medie" phpName="GiudizioFinale3Medie" type="VARCHAR" required="false" defaultValue=""/>
        <column name="consiglio_terza_media" phpName="ConsiglioTerzaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="giudizio_sintetico_esame_terza_media" phpName="GiudizioSinteticoEsameTerzaMedia" type="VARCHAR" required="false" defaultValue=""/>
        <column name="data_arrivo_in_italia" phpName="DataArrivoInItalia" type="INTEGER" required="false" defaultValue="0"/>
        <column name="frequenza_asilo_nido" phpName="FrequenzaAsiloNido" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="frequenza_scuola_materna" phpName="FrequenzaScuolaMaterna" type="INTEGER" required="false" defaultValue="(-1)"/>
        <column name="data_aggiornamento_sidi" phpName="DataAggiornamentoSidi" type="INTEGER" required="false" defaultValue="0"/>
        <column name="cmp_sup_val_ita" phpName="CmpSupValIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_ita" phpName="CmpSupTxtIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_ing" phpName="CmpSupValIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_ing" phpName="CmpSupTxtIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_altri" phpName="CmpSupValAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_altri" phpName="CmpSupTxtAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_mat" phpName="CmpSupValMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_mat" phpName="CmpSupTxtMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_sci_tec" phpName="CmpSupValSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_sci_tec" phpName="CmpSupTxtSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_val_sto_soc" phpName="CmpSupValStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_sup_txt_sto_soc" phpName="CmpSupTxtStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_ita" phpName="CmpMedValIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_ita" phpName="CmpMedTxtIta" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_ing" phpName="CmpMedValIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_ing" phpName="CmpMedTxtIng" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_altri" phpName="CmpMedValAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_altri" phpName="CmpMedTxtAltri" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_mat" phpName="CmpMedValMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_mat" phpName="CmpMedTxtMat" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_sci_tec" phpName="CmpMedValSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_sci_tec" phpName="CmpMedTxtSciTec" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_sto_soc" phpName="CmpMedValStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_sto_soc" phpName="CmpMedTxtStoSoc" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_l2" phpName="CmpMedValL2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_l2" phpName="CmpMedTxtL2" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_l3" phpName="CmpMedValL3" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_l3" phpName="CmpMedTxtL3" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_arte" phpName="CmpMedValArte" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_arte" phpName="CmpMedTxtArte" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_mus" phpName="CmpMedValMus" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_mus" phpName="CmpMedTxtMus" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_val_mot" phpName="CmpMedValMot" type="VARCHAR" required="false" defaultValue=""/>
        <column name="cmp_med_txt_mot" phpName="CmpMedTxtMot" type="VARCHAR" required="false" defaultValue=""/>
        <index name="id_studente">
            <index-column name="id_studente"/>
        </index>
    </table>
</database>
