<?xml version="1.0" encoding="UTF-8"?>
<config>

    <log>
        <type>file</type>
        <name>/var/log/propel.log</name>
        <ident>propel</ident>
        <level>7</level>
    </log>

    <propel>
        <datasources default="mc2api">
            <datasource id="mc2api">
                <adapter>pgsql</adapter>
                <connection>
                    <dsn>pgsql:host=localhost;dbname=mastercom2</dsn>
                    <user>postgres</user>
                    <password>postgres</password>
                </connection>
            </datasource>
        </datasources>
    </propel>
</config>