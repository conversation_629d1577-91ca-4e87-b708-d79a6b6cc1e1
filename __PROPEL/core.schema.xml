<?xml version="1.0" encoding="utf-8"?>
<!--Autogenerated by PropelSchemaReverseTask class.-->
<database package="Core" name="mc2api" defaultIdMethod="native" namespace="Core">
    <table name="event_error" phpName="EventError" idMethod="native" >
        <column name="id" phpName="Id" type="INTEGER" autoIncrement="true" required="true" primaryKey="true"/>
        <column name="level_nr" phpName="LevelNr" type="INTEGER" required="true"/>
        <column name="level_descr" phpName="LevelDescr" type="LONGVARCHAR" required="true"/>
        <column name="description" phpName="Description" type="LONGVARCHAR" required="true"/>
        <column name="path" phpName="Path" type="LONGVARCHAR" required="true"/>
        <column name="line" phpName="Line" type="LONGVARCHAR" required="true"/>
        <column name="error_time" phpName="ErrorTime" type="TIMESTAMP" required="false"/>
    </table>
    <table name="institute" phpName="Institute" idMethod="native">
        <column name="institute_id" phpName="InstituteId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="200" required="true"/>
        <column name="mechan_code" phpName="MechanCode" type="VARCHAR" size="16" required="true"/>
        <column name="contact_id" phpName="ContactId" type="INTEGER" required="false" defaultValue="NULL" primaryKey="true"/>
        <column name="fiscal_code" phpName="FiscalCode" type="VARCHAR" size="16" required="false"/>
        <column name="school_type" phpName="SchoolType" type="VARCHAR" size="3" required="false"/>
        <column name="parent" phpName="Parent" type="INTEGER" required="false"/>
        <column name="def" phpName="Def" type="BOOLEAN" required="false" defaultValue="false"/>
        <column name="dir_name" phpName="DirName" type="VARCHAR" size="100" required="false"/>
        <column name="dir_surname" phpName="DirSurname" type="VARCHAR" size="100" required="false"/>
        <column name="adir_name" phpName="AdirName" type="VARCHAR" size="100" required="false"/>
        <column name="adir_surname" phpName="AdirSurname" type="VARCHAR" size="100" required="false"/>
        <column name="pres_ge_name" phpName="PresGeName" type="VARCHAR" size="100" required="false"/>
        <column name="pres_ge_surname" phpName="PresGeSurname" type="VARCHAR" size="100" required="false"/>
        <column name="seg_cons_name" phpName="SegConsName" type="VARCHAR" size="100" required="false"/>
        <column name="seg_cons_surname" phpName="SegConsSurname" type="VARCHAR" size="100" required="false"/>
        <column name="pres_con_name" phpName="PresConName" type="VARCHAR" size="100" required="false"/>
        <column name="pres_con_surname" phpName="PresConSurname" type="VARCHAR" size="100" required="false"/>
        <column name="dir_fiscal_code" phpName="DirFiscalCode" type="LONGVARCHAR" required="false"/>
        <column name="school_fiscal_code" phpName="SchoolFiscalCode" type="LONGVARCHAR" required="false"/>
        <column name="inpdap_code" phpName="InpdapCode" type="LONGVARCHAR" required="false"/>
        <column name="assicurazioni_sanitarie" phpName="AssicurazioniSanitarie" type="LONGVARCHAR" required="false"/>
        <column name="dir_sesso" phpName="DirSesso" type="VARCHAR" size="1" required="true" defaultValue=""/>
        <column name="dir_birth" phpName="DirBirth" type="BIGINT" required="true" defaultValue="0"/>
        <column name="dir_city" phpName="DirCity" type="LONGVARCHAR" required="true" defaultValue=""/>
        <column name="postal_account" phpName="PostalAccount" type="BIGINT" required="false"/>
        <column name="ateco_code" phpName="AtecoCode" type="LONGVARCHAR" required="false"/>
        <column name="activity_code" phpName="ActivityCode" type="LONGVARCHAR" required="false"/>
        <column name="dir_curr_addr" phpName="DirCurrAddr" type="LONGVARCHAR" required="true" defaultValue=""/>
        <column name="dir_curr_city" phpName="DirCurrCity" type="LONGVARCHAR" required="true" defaultValue=""/>
        <column name="dir_curr_phone" phpName="DirCurrPhone" type="LONGVARCHAR" required="true" defaultValue=""/>
        <column name="dir_emp_id" phpName="DirEmpId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="adir_emp_id" phpName="AdirEmpId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="presge_emp_id" phpName="PresgeEmpId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="segcons_emp_id" phpName="SegconsEmpId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="prescon_emp_id" phpName="PresconEmpId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="respacq_emp_id" phpName="RespacqEmpId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_director_id" phpName="JobDirectorId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_vice_director_id" phpName="JobViceDirectorId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_dsga_id" phpName="JobDSGAId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_personnel_id" phpName="JobPersonnelId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_accounting_id" phpName="JobAccountingId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_warehouse_id" phpName="JobWarehouseId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <column name="job_registry_id" phpName="JobRegistryId" type="INTEGER" required="true" defaultValue="(-1)"/>
        <id-method-parameter value="institute_id_seq"/>
        <foreign-key foreignTable="contact" name="foreign_institute_contact" onDelete="SET NULL">
            <reference local="contact_id" foreign="contact_id"/>
        </foreign-key>
        <foreign-key foreignTable="employee" name="foreign_institute_employee_dir" onDelete="SET NULL">
            <reference local="dir_emp_id" foreign="employee_id"/>
        </foreign-key>
        <index name="fki_foreign_institute_contact">
            <index-column name="contact_id"/>
        </index>
        <index name="fki_foreign_institute_employee_d">
            <index-column name="dir_emp_id"/>
        </index>
        <unique name="institute_contact_pkey">
            <unique-column name="contact_id"/>
        </unique>
    </table>
    <table name="contact" phpName="Contact" idMethod="native">
        <column name="contact_id" phpName="ContactId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="address" phpName="Address" type="LONGVARCHAR" required="false"/>
        <column name="phone_num" phpName="PhoneNum" type="LONGVARCHAR" required="false"/>
        <column name="fax" phpName="Fax" type="LONGVARCHAR" required="false"/>
        <column name="city_id" phpName="CityId" type="INTEGER" required="false" defaultValue="NULL"/>
        <column name="email" phpName="Email" type="LONGVARCHAR" required="false"/>
        <column name="mobile" phpName="Mobile" type="LONGVARCHAR" required="false"/>
        <column name="web" phpName="Web" type="LONGVARCHAR" required="false"/>
        <column name="cap" phpName="Cap" type="VARCHAR" size="5" required="false" defaultValue=""/>
        <id-method-parameter value="contact_id_seq"/>
        <foreign-key foreignTable="cities" name="foreign_cities_contact" phpName="CityKey" onDelete="SET NULL">
            <reference local="city_id" foreign="city_id"/>
        </foreign-key>
        <index name="fki_foreign_cities_contact">
            <index-column name="city_id"/>
        </index>
    </table>
    <table name="cities" phpName="Cities" idMethod="native">
        <column name="city_id" phpName="CityId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="description" phpName="Description" type="VARCHAR" size="50" required="true"/>
        <column name="city_code" phpName="CityCode" type="VARCHAR" size="10" required="false"/>
        <column name="province" phpName="Province" type="VARCHAR" size="5" required="false"/>
        <column name="region" phpName="Region" type="VARCHAR" size="5" required="false"/>
        <column name="is_city" phpName="IsCity" type="SMALLINT" required="false" defaultValue="0"/>
        <foreign-key foreignTable="regions" name="foreign_cities_regions" phpName="RegionKey">
            <reference local="region" foreign="code"/>
        </foreign-key>
    </table>
    <table name="regions" phpName="Regions" idMethod="native">
        <column name="code" phpName="Code" type="VARCHAR" size="2" primaryKey="true" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="50" required="true"/>
    </table>
    <table name="bank_profile" phpName="BankProfile" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="country_code" phpName="CountryCode" type="VARCHAR" size="2" required="false"/>
        <column name="check_code" phpName="CheckCode" type="VARCHAR" size="2" required="false"/>
        <column name="bban" phpName="Bban" type="VARCHAR" size="100" required="false"/>
        <column name="denomination" phpName="Denomination" type="VARCHAR" size="100" required="false"/>
        <unique name="bank_profile_institute_bank_profile">
            <unique-column name="institute_bank_profile"/>
        </unique>
    </table>
    <table name="institute_bank_profile" phpName="InstituteBankProfile" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="institute" phpName="Institute" type="INTEGER" required="true"/>
        <column name="bank_profile" phpName="BankProfile" type="INTEGER" required="true"/>
        <foreign-key foreignTable="bank_profile" name="foreign_institute_bank_profile_bank_profile" phpName="InstituteBankProfileBankProfileKey" onDelete="CASCADE">
            <reference local="bank_profile" foreign="id"/>
        </foreign-key>
        <foreign-key foreignTable="institute" name="foreign_institute_bank_profile_institute"  phpName="InstituteBankProfileKey" onDelete="CASCADE">
            <reference local="institute" foreign="institute_id"/>
        </foreign-key>
        <unique name="institute_bank_profile_bank_profile">
            <unique-column name="bank_profile"/>
        </unique>
    </table>
    <!--    <table name="print_ready" phpName="PrintReady" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="100" required="true"/>
        <column name="path" phpName="Path" type="VARCHAR" size="100" required="true"/>
        <column name="user" phpName="User" type="INTEGER" required="true"/>
        <column name="status" phpName="Status" type="BOOLEAN" required="true" default="false"/>
        <column name="params" phpName="Params" type="LONGVARCHAR" required="true" default=""/>
        <column name="notified" phpName="Notified" type="BOOLEAN" required="true" default="false"/>
    </table>-->
    <table name="core_print_spool" phpName="PrintSpool" idMethod="native">
        <column name="id" phpName="Id" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="user_id" phpName="UserId" type="INTEGER" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="100" required="true"/>
        <column name="path" phpName="Path" type="VARCHAR" size="100" required="true"/>
        <column name="completed" phpName="Completed" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="params" phpName="Parameters" type="LONGVARCHAR" required="true" defaultValue=""/>
        <column name="notified" phpName="Notified" type="BOOLEAN" required="true" defaultValue="false"/>
        <column name="mime" phpName="Mime" type="VARCHAR" size="50" required="true"/>
    </table>
    <table name="parameter" phpName="Parameter" idMethod="native">
        <column name="parameter_id" phpName="ParameterId" type="INTEGER" primaryKey="true" autoIncrement="true" required="true"/>
        <column name="name" phpName="Name" type="VARCHAR" size="50" required="true" defaultValue=""/>
        <column name="value" phpName="Value" type="LONGVARCHAR" required="false" defaultValue=""/>
        <id-method-parameter value="parameter_parameter_id_seq"/>
    </table>
    <!-- Core Contacts -->
    <table package="Core"
           namespace="Database\Core"
           name="core_contacts"
           phpName="CoreContacts"
           idMethod="native">
           <column name="id"
                   phpName="Id"
                   type="INTEGER"
                   primaryKey="true"
                   autoIncrement="true"
                   required="true"/>
           <column name="name"
                   phpName="Name"
                   type="VARCHAR"
                   size="255"
                   required="true"/>
           <column name="email"
                   phpName="Email"
                   type="VARCHAR"
                   size="255"
                   required="true"/>
           <column name="contact_type_id"
                   phpName="ContactTypeId"
                   type="INTEGER"
                   required="true"/>
           <column name="user_id"
                   phpName="UserId"
                   type="INTEGER"
                   required="false"/>
           <foreign-key foreignTable="core_contact_type" phpName="CoreContactType" refPhpName="CoreContacts">
                <reference local="contact_type_id" foreign="id" />
           </foreign-key>
    </table>
    <!-- Core Contact Type -->
    <table package="Core"
           namespace="Database\Core"
           name="core_contact_type"
           phpName="CoreContactType"
           idMethod="native">
           <column name="id"
                   phpName="Id"
                   type="INTEGER"
                   primaryKey="true"
                   autoIncrement="true"
                   required="true"/>
           <column name="name"
                   phpName="Name"
                   type="VARCHAR"
                   size="255"
                   required="true"/>
           <column name="code"
                   phpName="Code"
                   type="VARCHAR"
                   size="20"
                   required="true"/>        
    </table>
    <!-- Core Contact Groups -->
    <table package="Core"
           namespace="Database\Core"
           name="core_contactgroups"
           phpName="CoreContactgroups"
           idMethod="native">
           <column name="id"
                   phpName="Id"
                   type="INTEGER"
                   primaryKey="true"
                   autoIncrement="true"
                   required="false"/>
           <column name="name"
                   phpName="Name"
                   type="VARCHAR"
                   size="255"
                   required="true"/>
           <column name="user_id"
                   phpName="UserId"
                   type="INTEGER"
                   required="false"/>
    </table>
    <!-- Core Contact - Contact Groups -->
    <table package="Core"
           namespace="Database\Core"
           name="core_contacts_contactgroups"
           phpName="CoreContactsContactgroups"
           idMethod="native">
           <column name="id"
                   phpName="Id"
                   type="INTEGER"
                   primaryKey="true"
                   autoIncrement="true"
                   required="true"/>
           <column name="contact_id"
                   phpName="ContactId"
                   type="INTEGER"
                   required="true"/>
           <column name="group_id"
                   phpName="GroupId"
                   type="INTEGER"
                   required="true"/>
           <foreign-key foreignTable="core_contacts" phpName="CoreContacts" refPhpName="CoreContactsContactgroups">
                <reference local="contact_id" foreign="id" />
           </foreign-key>
           <foreign-key foreignTable="core_contactgroups" phpName="CoreContactgroups" refPhpName="CoreContactsContactgroups">
                <reference local="group_id" foreign="id" />
           </foreign-key>
    </table>
</database>
