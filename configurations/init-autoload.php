<?php

function mc2_autoload($class) {
    $find = false;
    $paths = array(
        PATH_ROOT . 'applications/core/classes/',
        PATH_ROOT . 'applications/core/views/',
        PATH_ROOT . 'applications/core/classes/exceptions/',
        PATH_ROOT . 'applications/budget/classes/',
        PATH_ROOT . 'applications/employees/classes/',
        PATH_ROOT . 'applications/employees/classes/prints/',
        PATH_ROOT . 'applications/employees/views/',
        PATH_ROOT . 'applications/employees/absences/',
        PATH_ROOT . 'applications/inventory/classes/',
        PATH_ROOT . 'applications/inventory/prints/',
        PATH_ROOT . 'applications/warehouse/classes/',
        PATH_ROOT . 'applications/warehouse/prints/',
        PATH_ROOT . 'applications/protocols/classes/',
        PATH_ROOT . 'applications/protocols/views/',
        
    );
    foreach ($paths as $path) {
        $conf = $path . 'config/' . $class . '.php';
        $path .= $class . '.php';
        if (file_exists($path)) {
            if (file_exists($conf)) {
                include_once $conf;
            }
            include_once $path;
            $find = true;
            break;
        }
    }

    if (!$find) {
        // If arrives here it try to search with namespace
        $path = PATH_ROOT . 'classes/' . str_replace('\\', '/', $class) . '.php';
        if (file_exists($path)) {
            include_once $path;
            $find = true;
        }
    }

    if (! $find ){
        $path = PATH_MC2_API . 'data/database/build/classes/';       
        $cp = str_replace( 'Database\\', "", $class );       
        $class_path = str_replace( '\\', '/', $cp) . '.php';
        $path .= $class_path;        
        if (file_exists($path ) ) {
            include_once( $path );
        }
    }
}

spl_autoload_register('mc2_autoload');


function mt_autoload($class) {
// If arrives here it try to search with namespace
    $path = '/var/www-source/mt/1.2/classes/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($path)) {
        include_once $path;
    }
}

spl_autoload_register('mt_autoload');
