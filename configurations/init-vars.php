<?php

// PATH
define('PATH_ROOT', '/var/www/mc2/');
define('PATH_SMARTY', '/var/www/mastertek-api/php-smarty/3.1.12/');
define('PATH_TCPDF', '/var/www/mastertek-api/tcpdf/5.9.179/');
define('PATH_PROPEL', '/var/www/mastertek-api/php-propel/1.7.0-dev/');
define('PATH_MC_DB', '/etc/mastercom/database_main.conf');
define('URL_ROOT', '/mc2/');
define('PATH_TMP_ROOT', PATH_ROOT . 'tmp/');
define('PATH_TMP_URL', URL_ROOT . 'tmp/');

define( 'PATH_MC2_API', '/var/www-source/mc2-api/');

// VARS
define('DEV_MODE', true);
define('SESSION_EXPIRES', 30);
define('MONEY_FORMAT_PRINT', '%10#8.2n');
define('MONEY_NO_SIGN_FORMAT_PRINT', '%!.2n');

/*
 * NOTIFICATION CODES
 */

// SESSION
define('SESSION_EXPIRED', 1001);

// DATABASE (DB)
define('DB_ERR_CONNECTION', 2001);
define('DB_ERR_QUERY', 2002);
define('DB_OBJ_NOT_FOUND', 2012);

// VIEW (GENERAL)
define('VIEW_NOT_FOUND', 4004);
define('PERMISSION_VIEW', 4001);

// LOGIN
define('AUTH_FAILED', 5004);

/*
 * CONSTANTS
 */

// AREAS
define('AREA_PERSONNEL', 'AREA_PERSONNEL');
define('AREA_CCP', 'AREA_CCP');
define('AREA_WAREHOUSE', 'AREA_WAREHOUSE');
define('AREA_INVENTORY', 'AREA_INVENTORY');
define('AREA_PROTOCOL', 'AREA_PROTOCOL');
define('AREA_ARCHIVE', 'AREA_ARCHIVE');
define('AREA_ALBO', 'AREA_ALBO');
define('AREA_TRASPARENZA', 'AREA_TRASPARENZA');
define('AREA_SETTINGS', 'AREA_SETTINGS');
define('AREA_MAIL_ACCOUNT', 'AREA_MAIL_ACCOUNT');

// PRESENCES
define('PRESENCE_ENTRANCE', 1);
define('PRESENCE_EXIT', 2);
define('PRESENCE_NORMAL', 1);
define('PRESENCE_LUNCH', 2);
define('PRESENCE_SERVICE', 3);

// EMPLOYEE's PARAMETERS
define('EMPLOYEE_PARAM_RETRIBUTION_UNIT_PERC', '%');
define('EMPLOYEE_PARAM_RETRIBUTION_UNIT_MINS', 'min');

// ABSENCE STACK
define('ABS_STACK_UNIT_DAILY', 'd');
define('ABS_STACK_UNIT_HOURLY', 'h');

// EMPLOYEE LIQUID GROUP
define('ATA_GROUP', '0002');
define('TEACHER_GROUP', '0001');

// STACK RESET MODES
define('STACK_RESET_MANUAL', 0);
define('STACK_RESET_RESET_YEARLY', 1);
define('STACK_RESET_RESET_MONTHLY', 2);
define('STACK_RESET_ADD_YEARLY', 3);
define('STACK_RESET_ADD_MONTHLY', 4);
define('STACK_RESET_SUB_YEARLY', 5);
define('STACK_RESET_SUB_MONTHLY', 6);

// USER LOGS SECTIONS
define('LOG_LOGIN', 'login');
define('LOG_LOGOUT', 'logout');

define('LOG_EMPLOYEE', 'employee');
define('LOG_EMPLOYEE_DATA', 'employee_data');
define('LOG_EMPLOYEE_ABSENCES', 'employee_absences');
define('LOG_EMPLOYEE_TIMETABLE', 'employee_timetable');
define('LOG_EMPLOYEE_PRESENCES', 'employee_presences');
define('LOG_EMPLOYEE_PROJECTS', 'employee_projects');
define('LOG_EMPLOYEE_LOCK_MONTH', 'employee_month');
define('LOG_EMPLOYEE_SETTINGS', 'employee_settings');
define('LOG_EMPLOYEE_HOLIDAY_CALENDAR', 'employee_holiday_calendar');
define('LOG_EMPLOYEE_HOUR_STACK', 'employee_hour_stack');
define('LOG_EMPLOYEE_PRINT_EXT_MONTH_DETAIL', 'employee_print_ext_month_detail');

define('LOG_SETTINGS', 'settings');

// USER LOG OPERATIONS
define('VIEW', 'view');
define('CREATE', 'create');
define('UPDATE', 'update');
define('DESTROY', 'destroy');

// PHP internal error handler
define('LOGGER_PHP_ERROR_HANDLER', false);
define('LOGGER_DISPLAY_ERRORS', true);

// Logger error handler
define('LOGGER_ERROR_HANDLER', true);

// Application
define('LOGGER_APP_NAME', 'mc2-server');
//define('LOGGER_APP_VERSION', '{{ version }}');

// Sets Current User for Logger
if (isset($_SESSION['uid'])) {
    if ($_SESSION['uid'] > 0) {
        define('LOGGER_USER_ID', $_SESSION['uid']);
        define('LOGGER_USER_NAME', $_SESSION['user_name']);
    }
}
