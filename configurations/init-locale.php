<?php

/*
 * FIND A STRING TO TRANSLATE WITH
 *      ./makemessages from locale folder
 *
 * COMPILE TRANSLATED MESSAGES WITH
 *      ./compilemessages from locale folder
 */

$locale = "it_IT";
putenv("LC_ALL=" . $locale);
setlocale(LC_ALL, $locale . ".UTF-8");
bindtextdomain("mc2", PATH_ROOT . "locale");
textdomain("mc2");
setlocale(LC_MONETARY, 'it_IT');
setlocale(LC_COLLATE, 'it_IT');
setlocale(LC_CTYPE, 'it_IT');
setlocale(LC_NUMERIC, 'it_IT');
setlocale(LC_TIME, 'it_IT');
setlocale(LC_MESSAGES, 'it_IT');

