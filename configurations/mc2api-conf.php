<?php

// This file generated by Propel 1.7.0-dev convert-conf target
// from XML runtime conf file /home/<USER>/workspace/mercurial/mc2api/propel/runtime-conf.xml
// Include mc2 db configaration for port
require_once 'init-db.php';

$conf = array(
    'datasources'       =>
    array(
        'mc2api'              =>
        array(
            'adapter'    => 'pgsql',
            'connection' =>
            array(
                'dsn'      => 'pgsql:host='.getenv('DB_HOST').';dbname='. DB_NAME .';port=' . DB_PORT,
                'user'     => 'postgres',
                'password' => 'postgres',
            ),
        ),        
        'mc2importer'         =>
        array(
            'adapter'    => 'pgsql',
            'connection' =>
            array(
                'dsn'      => 'pgsql:host='.getenv('DB_HOST').';dbname=mc2importer;port=' . DB_PORT,
                'user'     => 'postgres',
                'password' => 'postgres',
            ),
        ),
        'mastercom_2011_2012' =>
        array(
            'adapter'    => 'pgsql',
            'connection' =>
            array(
                'dsn'      => 'pgsql:host='.getenv('DB_HOST').';dbname=mastercom_2011_2012;port=' . DB_PORT,
                'user'     => 'postgres',
                'password' => 'postgres',
            ),
        ),
        'mastercom_2012_2013' =>
        array(
            'adapter'    => 'pgsql',
            'connection' =>
            array(
                'dsn'      => 'pgsql:host='.getenv('DB_HOST').';dbname=mastercom_2012_2013;port=' . DB_PORT,
                'user'     => 'postgres',
                'password' => 'postgres',
            ),
        ),
        'mastercom_2013_2014' =>
        array(
            'adapter'    => 'pgsql',
            'connection' =>
            array(
                'dsn'      => 'pgsql:host='.getenv('DB_HOST').';dbname=mastercom_2013_2014;port=' . DB_PORT,
                'user'     => 'postgres',
                'password' => 'postgres',
            ),
        ),
        'mastercom_2014_2015' =>
        array(
            'adapter'    => 'pgsql',
            'connection' =>
            array(
                'dsn'      => 'pgsql:host='.getenv('DB_HOST').';dbname=mastercom_2014_2015;port=' . DB_PORT,
                'user'     => 'postgres',
                'password' => 'postgres',
            ),
        ),
        'default'             => 'mc2api',
    ),
    'generator_version' => '1.7.0-dev',
);
//$conf['classmap'] = include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'classmap-mc2api-conf.php');
$conf[ 'datasources' ][ 'mc2'] = $conf[ 'datasources'][ 'mc2api'];
return $conf;
