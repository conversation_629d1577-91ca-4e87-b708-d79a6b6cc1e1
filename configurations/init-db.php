<?php

if ( getenv('DB_HOST') == '' ){
    putenv("DB_HOST=localhost");
}

define('DB_HOST', getenv( 'DB_HOST'));
define('DB_NAME', getenv( 'DB_NAME') ? getenv( 'DB_NAME') : 'mastercom2');
define('DB_USER', 'postgres');
define('DB_PASSWD', 'postgres');

if (file_exists('/etc/mastercom2/local_conf.php')) {
    define('DB_PORT', 5432);
    define('LOGGER_DB_PORT', 5432);
} else {
    define('LOGGER_DB_PORT', '{{ port }}');
    define('DB_PORT', getenv( 'DB_PORT') ? getenv( 'DB_PORT') : '{{ port }}');
}

// LOGGER Database data
define('LOGGER_DB_HOST',  getenv( 'DB_HOST'));
define('LOGGER_DB_NAME', getenv( 'DB_NAME') ? getenv( 'DB_NAME') : 'logger');
define('LOGGER_DB_USER', 'postgres');
define('LOGGER_DB_PASSWORD', 'postgres');
