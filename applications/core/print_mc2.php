<?php

/*
 * Manage and dispatch TCPDF print
 */

/**
 * File manage all prints in mastercom2.
 * You call this file with name of class you have to instance. After that
 * you can create it to customize each print.
 */
header('Content-type: text/html; charset=utf-8');
session_start();

/*
 * Chek user session and if user is logged. If not, it redirects to login page after session distroy
 */
if (isset($_SESSION['uid']) && $_SESSION['uid'] < 1) {
    unset($_SESSION['uid']);
    session_destroy();
    header("Location: /mc2ui/");
}

/*
 * include 'init-vars.php'; 'init-autoload.php';'init-errors.php';'init-db.php';'init-locale.php';
 */
require_once '/var/www/mc2/configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';

/*
 * Set error reporting for all errors
 */
error_reporting(E_ALL ^ E_NOTICE);

/*
 *  Import smarty and tcpdf file
 */
require_once PATH_ROOT . 'configurations/init-smarty.php';
require_once PATH_ROOT . 'configurations/init-tcpdf.php';

$print = new $_GET['printClass']($_GET);
$print->as_view();
