<?php

/*
 * Refresh session and check logged user.
 * Return, if exist, uid of logged user.
 */

//require_once '../../index.php';

header('Content-type: text/html; charset=utf-8');
session_start();

// Imports INIT scripts
require_once dirname(__FILE__) . '/../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';

// Sets error reporting for all errors
error_reporting(E_ALL ^ E_NOTICE);

// Checks if request is regular for permission and, in case, parsing it
require_once PATH_ROOT . 'applications/core/checkRequest.php';

// Imports finction for check if user is logged
require_once PATH_ROOT . 'applications/core/utils.php';

// Imports smarty and tcpdf file
require_once PATH_ROOT . 'configurations/init-smarty.php';
require_once PATH_ROOT . 'configurations/init-tcpdf.php';


// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");


// Logger path
require_once "/var/www/logger/logger.php";

// Checks session
$data = array();
if ($request->user->uid) {
    $conn = \Propel::getConnection();
    $officeQuery = 'SELECT office from archive_office_user as aou, archive_office as ao where ao.id = aou.office and "user" = ' . $request->user->uid;
    $st = $conn->prepare($officeQuery);
    $st->execute();
    $offices = $st->fetchAll(\PDO::FETCH_ASSOC);

    $sql = "SELECT name, value FROM parameter WHERE name IN (
        'SIGN_TYPE', 'PAYMENT_METHOD_DEFAULT', 'PRINT_CLASS_RECEIPT', 'PRINT_CLASS_DECALARATION',
        'PRINT_CLASS_DECALARATION_INVOICE', 'CCP_DECLARATION_MOVEMENT', 'INVOICE_ENABLED',
        'CREDIT_NOTES_PUBLISH', 'ENABLE_SYNC_MATTHAEUS', 'CCP_FAMILY_MANAGEMENT',
        'CCP_FILTER_MOVEMENT_BY_SCHOOL_YEAR', 'EASY_MC2_API', 'SEPA_UPDATE_INFO',
        'SEPA_CHECK_BROTHER_GROUP', 'PRINT_CLASS_ATTESTATION', 'CCP_SHOW_OLD_CREDIT_NOTE_METHOD',
        'EASY_CONTI_RICAVI_RISCONTI', 'AREA_PERSONNEL', 'AREA_WAREHOUSE','AREA_ARCHIVE','AREA_ALBO',
        'AREA_TRASPARENZA','AREA_SETTINGS','AREA_PROTOCOL','AREA_CCP','AREA_MAIL_ACCOUNT'
    )";
    $st = $conn->prepare($sql);
    $st->execute();
    $results = $st->fetchAll(\PDO::FETCH_KEY_PAIR);

    $signType = $results['SIGN_TYPE'];
    $defPaymenthMethod = $results['PAYMENT_METHOD_DEFAULT'];
    $receiptPrint = $results['PRINT_CLASS_RECEIPT'];
    $declarationPrint = $results['PRINT_CLASS_DECALARATION'];
    $declarationInvoicePrint = $results['PRINT_CLASS_DECALARATION_INVOICE'];
    $declarationMovementPrint = $results['CCP_DECLARATION_MOVEMENT'];
    $invoiceEnabled = $results['INVOICE_ENABLED'] == 't';
    $creditNotesPublish = $results['CREDIT_NOTES_PUBLISH'] == 't';
    $enableSyncMatthaeus = $results['ENABLE_SYNC_MATTHAEUS'] == 't';
    $enableFamilyManagement = $results['CCP_FAMILY_MANAGEMENT'] == 't';
    $ccpFilterMovementBySchoolYear = $results['CCP_FILTER_MOVEMENT_BY_SCHOOL_YEAR'] == 't';
    $easyMc2Api = $results['EASY_MC2_API'];
    $sepaUpdateInfo = $results['SEPA_UPDATE_INFO'] == 't';
    $sepaCheckBrother = $results['SEPA_CHECK_BROTHER_GROUP'] == 't';
    $attestationClass = $results['PRINT_CLASS_ATTESTATION'];
    $oldCreditNoteMethod = $results['CCP_SHOW_OLD_CREDIT_NOTE_METHOD'] === 't';
    $easy_conti_ricavi_risconti = $results['EASY_CONTI_RICAVI_RISCONTI'] === 't';
    $area_archive = $results['AREA_ARCHIVE'] === 't';
    $area_personnel = $results['AREA_PERSONNEL'] === 't';
    $area_ccp = $results['AREA_CCP'] === 't';
    $area_warehouse = $results['AREA_WAREHOUSE'] === 't';
    $area_protocol = $results['AREA_PROTOCOL'] === 't';
    $area_albo = $results['AREA_ALBO'] === 't';
    $area_trasparenza = $results['AREA_TRASPARENZA'] === 't';
    $area_settings = $results['AREA_SETTINGS'] === 't';
    $area_mail_account = $results['AREA_MAIL_ACCOUNT'] === 't';

    $magisterEnabled = getenv('MAGISTER_URL') !== false && getenv('MAGISTER_URL') !== "" && $enableSyncMatthaeus;

    $oids = [];
    foreach ($offices as $o) {
        $oids[] = $o['office'];
    }

    /**
     * Check special permissions
     * eg. Protocollo Riservato
     */
    $permissions = [
        '470' => 'PROTOCOLLO_RISERVATO'
    ];
    $userPermissions = [];
    $user = Auth\UsersQuery::create()->findPk($request->user->uid);
    $group = Auth\GroupsQuery::create()->findPk( $user->getUserType());
    /*
    if ( $group->hasPermission(470) || $user->getSuperUser()){
        $permissions['PROTOCOLLO_RISERVATO'] = true;
    }*/



    $authPermissions = Auth\AuthPermissionQuery::create()->getPermissionsList($user->getSuperUser(),$group);

    if ( is_array( $authPermissions) ) {
        foreach( $permissions as $key => $val ) {
            if ( is_array( $authPermissions[$key]) ){
                $userPermissions[$val] = $user->hasPermission($key);
            }
        }
    }

    $discountEnabledSql = "SELECT value from parameter where name = 'DISCOUNT_ENABLED'";
    $st = $conn->prepare($discountEnabledSql);
    $st->execute();
    $userPermissions['DISCOUNT_ENABLED'] = $st->fetchAll(\PDO::FETCH_ASSOC)[0]['value'] == 't';

    //if ( is_array( $authPermissions))
    /*
    echo '<pre>';
    var_dump( $authPermissions );
    echo '</pre>';
    */
    $budget = new Budget();
    $data = array(
        'success'       => true,
        'uid'           => $_SESSION['uid'],
        'username'      => $user->getUserName(),
        'couch_tk'      => isset($_SESSION['couch_tk']) ? $_SESSION['couch_tk'] : null,
        'year'          => $budget->getYear(),
        'modify'        => $user->getModifyProtocol(),
        'permissions'   => $userPermissions,
        'offices'       => $oids,
        'sign_type'     => $signType,
        'credit_notes_publish'     => $creditNotesPublish,
        'ccp_filter_movement_by_school_year'     => $ccpFilterMovementBySchoolYear,
        'easy_mc2_api'  =>  $easyMc2Api,
        'sepa_update_info'  => $sepaUpdateInfo,
        'sepa_check_brother'  => $sepaCheckBrother,
        'invoiceEnabled'     => $invoiceEnabled,
        'familyManagementEnabled'     => $enableFamilyManagement,
        'magisterEnabled'    => $magisterEnabled,
        'oldCreditNoteMethod' => $oldCreditNoteMethod,
        'easy_conti_ricavi_risconti' => $easy_conti_ricavi_risconti,
        'area_archive' => $area_archive,
        'area_personnel' => $area_personnel,
        'area_ccp' => $area_ccp,
        'area_warehouse' => $area_warehouse,
        'area_protocol' => $area_protocol,
        'area_albo' => $area_albo,
        'area_trasparenza' => $area_trasparenza,
        'area_settings' => $area_settings,
        'area_mail_account' => $area_mail_account,
        'def'       => [
            'paymenth_method' => $defPaymenthMethod,
        ],
        'prints'        => [
            'receipt'   => $receiptPrint,
            'attestation'   => $attestationClass,
            'declaration' => $declarationPrint,
            'declaration_invoice' => $declarationInvoicePrint,
            'declaration_movement' => $declarationMovementPrint
        ]
    );
} else {
    $data = array(
        'success' => false
    );
}

echo Response::Create($data);
