<?php

header('Content-type: text/html; charset=utf-8');
set_time_limit(3600);

define('LOGGER_USER_ID', 0);
define('LOGGER_USER_NAME', 'root');

// Includes needed library
require_once __DIR__ . '/../../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';

// Sets error reporting for all errors
error_reporting(E_ALL ^ E_NOTICE);

// Imports smarty and tcpdf libraries
require_once PATH_ROOT . 'configurations/init-smarty.php';
require_once PATH_ROOT . 'configurations/init-tcpdf.php';

// Includes Propel library and initializes Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");

// Logger path
require "/var/www/logger/logger.php";

$p = \Core\PrintSpoolQuery::create()->read(array('id' => $argv[1]));
if (count($p) > 0) {
    $parameters = unserialize($p[0]['params']);
    $parameters['id'] = (int) $p[0]['id'];
    $parameters['name'] = $p[0]['name'];
    $parameters['path'] = $p[0]['path'];
    $parameters['user'] = (int) $p[0]['user_id'];
    $parameters['mime'] = $p[0]['mime'];
    $printClass = trim($parameters['printClass']);
    $print = new $printClass($parameters);
    $print->as_view();
}
