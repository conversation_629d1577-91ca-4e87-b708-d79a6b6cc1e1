<?php

/*
 * Manage all permission data.
 * It links all data between UI structure and call backend structure.
 */

/**
 * Set of function to mantain consistence between auth_permissions_group,
 * and all other auth tables permission
 *
 * <AUTHOR>
 */
class Permissions {

	public $id;
	public $data = array();

	/**
	 * Get permission row from table and bild default array
	 * @param id from auth_permission
	 */
	public function __construct($id = -1) {
		if (!$id)
			return false;
		$this->id = (int) $id;
	}

	public function getOrCreateSection($section) {
		if (!$section)
			return false;
		$sqls = array();
		$db = Db::getInstance();
		$db->query_params("SELECT * FROM auth_section WHERE title=$1", array($section));
		$sectionArr = $db->fetchAll();
		if ($sectionArr === false) {
			$db->query_params("INSERT INTO auth_section (title) VALUES ($1)", array($section));
			$sqls[] = "INSERT INTO auth_section (title) VALUES ('{$section}');";
			$db->query_params("SELECT * FROM auth_section WHERE title=$1", array($section));
			$sectionArr = $db->fetchAll();
		}
		$this->data['auth_section'] = $sectionArr[0];
		return array(
			'result'	 => $sectionArr[0]['id'],
			'queries'	 => $sqls
		);
	}

	public function save($title, $is_superuser, $section_id) {
		$sqls = array();
		$db = Db::getInstance();
		$db->query_params("INSERT INTO auth_permissions (title, super_user, auth_section) VALUES  ($1, $2, $3)", array($title, $is_superuser, $section_id));
		$sqls[] = "INSERT INTO auth_permissions (title, super_user, auth_section) VALUES  ('{$title}', '{$is_superuser}', {$section_id});";
		$db->query("SELECT max(id) FROM auth_permissions");
		$permission = $db->fetchAll();
		$this->id = (int) $permission[0]['max'];
		return array(
			'result'	 => $permission[0]['max'],
			'queries'	 => $sqls
		);
	}

	public function setPaths($paths) {
		$sqls = array();
		$db = Db::getInstance();
		$db->query_params("DELETE FROM auth_path WHERE auth_permission = $1", array($this->id));
		$sqls[] = "DELETE FROM auth_path WHERE auth_permission = {$this->id};";
		foreach ($paths as $path) {
			$db->query_params("INSERT INTO auth_path (path, auth_permission) VALUES ($1, $2)", array($path, $this->id));
			$sqls[] = "INSERT INTO auth_path (path, auth_permission) VALUES ('{$path}', {$this->id});";
		}
		return array(
			'result'	 => true,
			'queries'	 => $sqls
		);
	}

	public function setElements($elements, $control_interface) {
		$sqls = array();
		$db = Db::getInstance();
		$db->query_params("DELETE FROM auth_element WHERE auth_permission = $1", array($this->id));
		$sqls[] = "DELETE FROM auth_element WHERE auth_permission = {$this->id};";
		foreach ($elements as $element) {
			$db->query_params("INSERT INTO auth_element (name, control_interface, auth_permission) VALUES ($1, $2, $3)", array($element, $control_interface, $this->id));
			$sqls[] = "INSERT INTO auth_element (name, control_interface, auth_permission) VALUES ('{$element}', '{$control_interface}', {$this->id});";
		}
		return array(
			'result'	 => true,
			'queries'	 => $sqls
		);
	}

	public function setProhibition($group) {
		$db = Db::getInstance();
		$db->query_params("INSERT INTO auth_permission_group (groups, auth_permission) VALUES ($1, $2)", array($group, $this->id));
		return true;
	}

	public function deleteProhibition($group) {
		$db = Db::getInstance();
		$db->query_params("DELETE FROM auth_permission_group WHERE groups=$1 AND auth_permission = $2", array($group, $this->id));
	}

	public function getMainInterface() {
		$db = Db::getInstance();
		$db->query_params("SELECT distinct(control_interface) FROM auth_element WHERE auth_permission = $1", array($this->id));
		$arr = $db->fetchAll();
		$mainView = array();
		foreach ($arr as $value) {
			$mainView[] = $value['control_interface'];
		}
		return $mainView;
	}

}

?>
