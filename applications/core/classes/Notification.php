<?php

/*
 * Classes handle all error from code
 *
 */

/**
 * Notification class.
 * This manage all notification must be sent to the client.
 *
 * <AUTHOR>
 */
class Notification
{

    public $error_code;
    public $error_message;
    public $success = false;

	public function __construct($code, $message)
	{
		$this->error_code = (int) $code;
		$this->error_message = $message;
	}

    public function notify()
	{
		echo Response::Create(array(
			"success" => false,
			"error_code" => $this->error_code,
			"error_message" => $this->error_message
		));
		exit(0);
    }

}