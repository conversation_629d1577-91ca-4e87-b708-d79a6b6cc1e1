<?php

/*
 * Created on 19/11/2012 by Cellarosi Marco
 */

class Region
{

    public $table_name = 'states';
    private $_id_name = 'initials';

    function __construct($id)
    {
	$this->db = Db::getInstance();
	$this->id = trim($id);
	$this->qString = "SELECT * FROM " . $this->table_name . " WHERE " . $this->_id_name . "='" . $this->id . "'";
	$this->db->query($this->qString);
	$regionInfo = $this->db->fetchAll();
	foreach ($regionInfo[0] as $key => $value)
	{
	    $this->$key = $value;
	}
    }

}

?>
