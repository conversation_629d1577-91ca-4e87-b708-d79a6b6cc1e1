<?php

class Db {

    private static $instance;
    public $result = '';
    public $error = '';
    public $connectionID = '';
    private $qResult = '';
    private $host = '';
    private $port = '';
    private $name = '';
    private $user = '';
    private $pwd = '';

    public static function getInstance($host = '', $port = '', $name = '', $user = '', $pwd = '') {
        if (self::$instance == NULL) {
            self::$instance = new Db($host, $port, $name, $user, $pwd);
        }
        return self::$instance;
    }

    /**
     *
     * @param string $host
     * @param integer $port
     * @param string $name
     * @param string $user
     * @param string $pwd
     *
     * @assert () == true
     */
    public function __construct($host, $port, $name, $user, $pwd) {
        $this->host = $host ? $host : DB_HOST;
        $this->port = $port ? $port : DB_PORT;
        $this->name = $name ? $name : DB_NAME;
        $this->user = $user ? $user : DB_USER;
        $this->pwd = $pwd ? $pwd : DB_PASSWD;

        $this->connect();
    }

    private function connect() {
        $connectionString = "host='" . $this->host . "' port=" . $this->port . " dbname='" . $this->name . "' user='" . $this->user . "' password='" . $this->pwd . "'";

        $this->connectionID = pg_connect($connectionString);
        if (!$this->connectionID) {
            return false;
        }
        return true;
    }

    /**
     *
     * @param type $qString
     * @throws DbQueryException
     * @return bool true if success
     *
     * @assert ("SELECT 1 as one") == true
     */
    function query($qString) {
        $this->qResult = pg_query($this->connectionID, $qString) or false;
        if (!$this->qResult) {
            return false;
        }
        return true;
    }

    /**
     *
     * @param type $qString
     * @throws DbQueryException
     * @return bool true if success
     *
     * @assert ("SELECT $1 as one",array(1)) == true
     */
    function query_params($qString, $vars = array()) {
        $this->qResult = pg_query_params($this->connectionID, $qString, $vars) or false;
        if (!$this->qResult) {
            return false;
        }
        return true;
    }

    /**
     * Return array of element, based on previeus query select
     *
     * @return array
     *
     * @assert () == array(0 => array('one' => 1))
     */
    function fetchAll() {
        return pg_fetch_all($this->qResult);
    }

    /**
     * Return array of element, based on previeus query select
     *
     * @return array
     *
     * @assert () == array(0 => array('one' => 1))
     */
    function fetchRow() {
        return pg_fetch_row($this->qResult);
    }

    /**
     * @param string $table name
     * @return number of rows in table
     */
    public function countRowsInTable($table) {
        $this->query("SELECT count(*) FROM " . $table . " ");
        $total = $this->fetchAll();
        return $total[0]['count'];
    }

    public function parsePgArray($arrayString = '') {
        if (!$arrayString OR $arrayString == '{}') {
            return array();
        }
        return explode(',', str_replace('}', '', str_replace('{', '', trim($arrayString))));
    }

}

?>