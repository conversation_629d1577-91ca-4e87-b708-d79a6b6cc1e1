<?php

/**
 * This is an abstract class to manage every kind of lists. The other specific
 * list regarding tables will extend this class, that give a possibility to filter
 * elements and make filtering without use direct query.
 *
 * <AUTHOR>
 */
abstract class ListObjects {

	protected $_table;  // Reference table where manage the list
	protected $_filter = array();
	protected $_fields = array();
	protected $_order = array();
	protected $_limit;
	protected $_offset;

	/**
	 * Set the where clausole filter into array
	 * @param array $filters
	 */
	public function filter($filters) {
		$this->_filter = $filters;
	}

	/**
	 * Add a single filter to the where clausole
	 * @param string single $filter
	 */
	public function addFilter($filter) {
		if (!is_array($filter)) {
			$filter = array($filter);
		}
		$this->_filter = array_merge($this->_filter, $filter);
	}

	/**
	 * Clear all where clausole in filter
	 */
	public function clearFiter() {
		$this->_filter = array();
	}

	/**
	 * Set one or more orders clausole
	 * @param array $orders
	 */
	public function orderBy($orders) {
		$this->_order = $orders;
	}

	/**
	 * Set limit options
	 * @param string $llimit
	 */
	public function limit($llimit) {
		$this->_limit = $llimit;
	}

	/**
	 * Set offset options
	 * @param string $offset
	 */
	public function offset($offset) {
		$this->_offset = $offset;
	}

	/**
	 * Set fields you want to return for current table
	 * @param array $fileds
	 */
	public function fields($fileds) {
		$this->_fields = $fileds;
	}

	/**
	 * Build a query based on previous information and exec it.
	 * @return array of data you filtered
	 */
	public function get() {
		$f = count($this->_fields) > 0 ? join(',', $this->_fields) : '*';
		$w = count($this->_filter) > 0 ? ' WHERE ' . join(' AND ', $this->_filter) : '';
		$o = count($this->_order) > 0 ? 'ORDER BY ' . join(',', $this->_order) : '';
		$l = $this->_limit ? 'LIMIT ' . $this->_limit : '';
		$off = $this->_offset ? 'OFFSET ' . $this->_offset : '';
		$query = 'SELECT ' . $f . ' FROM ' . $this->_table . ' ' . $w . ' ' . $o . ' ' . $l . ' ' . $off;
		$db = Db::getInstance();
		$db->query($query);
		$list = $db->fetchAll();
		return $list === false ? array() : $list;
	}

}

?>
