<?php

/*
 * Created on 15/11/2012 by Cellarosi Marco
 */

class Institute extends DbObject {

	protected $_tableN = 'institute';
	protected $_idF = 'institute_id';

	public function __construct($id = -1) {
		if ((int) $id < 0) {
			$db = Db::getInstance();
			$qString = "SELECT institute_id from institute where def='t' limit 1";
			$db->query($qString);
			$instInfo = $db->fetchAll();
			$id = (int) $instInfo[0]['institute_id'];
		}

		parent::__construct($id);
		//$this->contact = new Contact($this->contact_id);
	}

	public function save() {
		parent::save();
		if ($this->def == true) {
			$db = Db::getInstance();
			$db->query_params(
					"UPDATE institute SET def = 'f' WHERE institute_id != $1", array((int) $this->_idV));
		}
	}

	public function getContact() {
		return new Contact((int) $this->contact_id);
	}

}

?>
