<?php

/*
 * Classes handle user Sessions
 *
 */

/**
 * Session class.
 *
 * <AUTHOR>
 */
class Session
{

    private static $instance;
    protected $_db;
    public $savePath;
    public $sessionName;

    public static function getInstance()
    {
	if (self::$instance == NULL)
	{
	    self::$instance = new Session;
	}
	return self::$instance;
    }

    private function __construct()
    {
	// Register this object as the session handler
	session_set_save_handler(
		array(&$this, "open"), array(&$this, "close"), array(&$this, "read"), array(&$this, "write"), array(&$this, "destroy"), array(&$this, "gc")
	);

	$this->_db = Db::getInstance();
	$this->startSession();
    }

    /**
     * Do nothing
     *
     * @param string $savePath
     *
     * @return boolean
     */
    public function open($savePath, $sessionName)
    {
	$this->savePath = $savePath;
	$this->sessionName = $sessionName;

	return true;
    }

    /**
     * Close the session.
     *
     * The close() function is responsible for calling the gc() function to
     * perform garbage collection. Notice that normally it obtains its value
     * from the session.gc_maxlifetime parameter in the php.ini file.
     *
     * @return boolean
     */
    public function close()
    {
	$this->gc();

	return true;
    }

    /**
     * The read() function is responsible for retrieving the data for the
     * specified session Id's.
     *
     * @param string $sessionId
     *
     * @return array
     */
    public function read($sessionId)
    {
	$this->_db->query_params(
		"SELECT session_data FROM sessions
             WHERE session_id = $1
             AND session_expires >= now()", array($sessionId)
	);
	$res = $this->_db->fetchAll();
	return $res[0]['session_data'];
    }

    /**
     *
     * @param string $sessionId
     * @param string $sessionData
     *
     * @return boolean
     */
    public function write($sessionId, $sessionData)
    {
	$this->_db->query_params(
		"SELECT * FROM sessions WHERE session_id = $1", array($sessionId)
	);
	$session = $this->_db->fetchAll();

	$sql = "";

	$vars = array($sessionId, $sessionData);
	if ($session !== false)
	{
	    $sql = "UPDATE sessions set session_data = $2, session_expires = now() + interval '" . SESSION_EXPIRES . " minutes', modified = now()
                WHERE session_id = $1";
	}
	else
	{
	    $sql = "INSERT INTO sessions (session_id, session_expires, session_data, modified)
                VALUES ($1, now() + interval '" . (int) SESSION_EXPIRES . " minutes', $2, now())";
	}

	$this->_db->query_params($sql, $vars);

	return true;
    }

    /**
     *
     * @param string $sessionId
     *
     * @return type
     */
    public function destroy($sessionId)
    {
	$res = $this->_db->query_params(
		"DELETE FROM sessions WHERE session_id = $1", array($sessionId)
	);

	return $res;
    }

    /**
     * Garbage Collection
     *
     * Delete all records who have passed the expiration time
     *
     * @return boolean
     */
    public function gc()
    {
	$this->_db->query(
		"DELETE FROM sessions WHERE session_expires < now()"
	);

	return true;
    }

    /**
     * Start session
     *
     * @access public
     *
     * @return boolean Returns TRUE if a session was successfully started,
     *                 otherwise FALSE.
     */
    public function startSession()
    {
	return session_start();
    }

    /**
     * Check if session ended or not
     *
     * @return boolean
     *
     */
    private function _isEnded()
    {
	if (!isset($_SESSION['uid']))
	{
	    return true;
	}
	return false;
    }

    public function checkLive()
    {
	if ($this->_isEnded())
	{
	    throw new SessionException(SESSION_EXPIRED, _('Session Expired'));
	}
	return true;
    }

}
