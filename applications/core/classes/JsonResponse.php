<?php

/*
 * Classes handle all json response from client
 *
 */

/**
 * JsonResponseHandler
 *
 * <AUTHOR>
 */
class JsonResponse {

	private $_jsonData;
	public $data;

	public function __construct($data) {
		header('Content-Type: application/json; charset=utf-8');

		$this->data = $data;
		$this->_jsonData = json_encode($this->data);
	}

	public function __toString() {
		return $this->_jsonData;
	}

}

