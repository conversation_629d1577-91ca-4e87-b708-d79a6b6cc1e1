<?php

/*
 * Classes handle all json request from client
 *
 */

/**
 * RequestManager
 *
 * <AUTHOR>
 */
class JsonRequest {

	private $request = array();
	public $method = 'GET';
	public $is_ajax = true;
	public $format = 'json';
	public $extra_params = array();
	public $post;
	public $get;
	public $raw;

	public function __construct($request = array()) {
		$this->request = $request;
		$this->uid = $_SESSION['uid'];
		$this->method = $_SERVER['REQUEST_METHOD'];
		$this->url = $_SERVER['REQUEST_URI'];
		$this->is_ajax = (strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ? true : false);
		$this->json = json_decode(file_get_contents('php://input'), true);

		if (isset($this->json)) {
			foreach ($this->json as $k => $v) {
				$this->$k = $v;
				$this->raw[$k] = $v;
			}
		}
		if ($this->method == 'POST') {
			foreach ($this->request as $k => $v) {
				$this->post[$k] = $v;
			}
		} elseif ($this->method == 'GET') {
			foreach ($this->request as $k => $v) {
				$this->get[$k] = $v;
			}
		}

		$this->user = new User((int) $this->uid);
	}

	public function parse() {
		foreach ($this->request as $key => $value) {
			$this->$key = $value;
		}
	}

	public function checkPermitted() {
		$this->user->hasPermissionPath($this->url);
	}

}
