<?php

/*
 * Created on 19/11/2012 by Cellarosi Marco
 */

class City
{

    public $table_name = 'cities';
    private $_id_name = 'city_id';

    function __construct($id)
    {
	$this->db = Db::getInstance();
	$this->id = (int) $id;
	$this->qString = "SELECT * FROM " . $this->table_name . " WHERE " . $this->_id_name . "=" . $this->id;
	$this->db->query($this->qString);
	$cityInfo = $this->db->fetchAll();
	foreach ($cityInfo[0] as $key => $value)
	{
	    $this->$key = $value;
	}

	$this->region = new Region($this->region);
    }

}

?>
