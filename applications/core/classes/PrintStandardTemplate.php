<?php

class PrintStandardTemplate extends TCPDF {

    public $success = false;
    protected $page_orientation = 'P';

    /**
     * Call parent builder for base TCPDF initialization.
     * Then prepare a base data, structure and styling about print
     */
    public function __construct($file_name) {
        $this->file_name = $file_name;
        parent::__construct($this->page_orientation, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $this->institute = new Institute();
        $this->prepareData();
    }

    /**
     * Write a title of the print passed from extended class
     */
    protected function __writePrintTitle($title) {
        $this->SetFont('', '', 9);
        $this->Cell(0, 0, $title, 0, 1, 'C', false);
        $this->Ln();
        $this->SetFont('', '', 7);
    }

    /**
     * Define base data and set them, for example institute data, title
     * of document and print style
     *
     * @param type $filename
     */
    public function prepareData() {
        $this->SetCreator(PDF_CREATOR);
        $this->SetTitle($this->file_name);
        $this->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE . ' 048', PDF_HEADER_STRING);
        $this->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $this->SetHeaderMargin(PDF_MARGIN_HEADER);
        $this->SetFooterMargin(PDF_MARGIN_FOOTER);
        $this->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
        $this->SetFont('', '', 7);
        $this->AddPage();
    }

    /**
     * Design Header page section,
     * based also on data prepared in prepareData method
     */
    public function Header() {
        // Logo
        $image_file = PATH_ROOT . 'applications/core/resources/images/logo_repubblica.jpg';
        $this->Image($image_file, 5, 5, 15, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
        // Set font
        $this->SetFont('', 'B', 10);

        $this->SetX(30);

        $this->Cell(0, 0, 'MINISTERO DELL\'ISTRUZIONE E DEL MERITO', 0, 1, 'L', false);
        $this->SetX(30);
        $this->SetFont('', '', 8);
        $this->Cell(0, 0, 'UFFICIO SCOLASTICO REGIONALE PER IL/LA ' . strtoupper($this->institute->contact->city->region->description), 0, 1, 'L', false);
        $this->SetX(30);
        $this->SetFont('', 'B', 10);
        $this->Cell(0, 0, $this->institute->name, 0, 1, 'L', false);
        $this->SetX(30);
        $this->SetFont('', '', 8);
        $this->Cell(0, 0, $this->institute->contact->city->description . ' (' . $this->institute->contact->city->province . ') ' . $this->institute->contact->address . ' C.F. ' . $this->institute->fiscal_code, 0, 1, 'L', false);
    }

    /**
     * Create PDF from HTML passed by parameter and save it in PATH_TMP_ROOT
     *
     * @param type $html
     */
    public function html2pdf($html) {
        $file_path = PATH_TMP_ROOT . $this->file_name . '.pdf';
        $file_url = PATH_TMP_URL . $this->file_name . '.pdf';
        $this->writeHTML($html, true, false, true, false, '');
        $this->Output($file_path, 'F');
        if (file_exists($file_path)) {
            return $file_url;
        }
        return false;
    }

    /**
     * Create PDF from native TCPDF method
     *
     */
    public function native2pdf() {
        $file_path = PATH_TMP_ROOT . $this->file_name . '.pdf';
        $file_url = PATH_TMP_URL . $this->file_name . '.pdf';
        $this->Output($file_path, 'F');
        if (file_exists($file_path)) {
            return $file_url;
        }
        return false;
    }

    /**
     * Draw Footer in all base prints
     */
    public function Footer() {
        // Position at 15 mm from bottom
        $this->SetY(-15);
        // Set font
        $this->SetFont('', 'I', 5);
        // Page number
        $this->Cell(0, 10, 'Page ' . $this->getAliasNumPage() . '/' . $this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }

}
