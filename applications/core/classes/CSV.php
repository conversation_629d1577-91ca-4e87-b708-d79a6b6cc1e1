<?php

/**
 * The class manage data for csv creation by table fields or
 * associave array.
 *
 * <AUTHOR>
 */
class CSV
{

    public $file_name;
    public $table;
    public $fields;
    public $filter;
    public $query;
    public $array;
    public $delimiter;
    public $url_location;

    /**
     * Initialize the variables
     */
    public function __construct()
    {
	$this->file_name = 'csv_mc2.csv';
	$this->table = '';
	$this->fields = array();
	$this->filter = array();
	$this->query = '';
	$this->array = array();
	$this->delimiter = '|';
	$this->url_location = '';
    }

    /**
     *
     * Save associative array to print in csv
     *
     * @param associative array $array_data
     * @return boolean if the saving has been completed or not
     *
     * @assert (array('key'=>'value')) == true
     * @assert (array()) == true
     * @assert ('test') == false
     * @assert (1) == false
     */
    public function setArray($array_data = array())
    {
	if (!is_array($array_data))
	{
	    return false;
	}
	$this->array = $array_data;
	return true;
    }

    /**
     * Fill $array with data found by $table, $fields and $filter variables.
     *
     * @assert ('employee', array('surname','name')) == true
     * @assert ('employee', array('surname','name'), array('employee_id=-1')) == false
     */
    public function prepareData($table, $fields = array(), $filter = array())
    {
	$db = Db::getInstance();
	$this->table = $table;
	$this->fields = $fields;
	$this->filter = $filter;
	$fieldsS = count($fields) > 0 ? join(',', $fields) : '*';
	$where = count($filter) > 0 ? 'WHERE ' . join(' AND ', $filter) : '';

	$db->query(
		"SELECT " . $fieldsS . " FROM " . $table . " " . $where
	);
	$this->array = $db->fetchAll();
	return $this->array === false ? false : true;
    }

    /**
     * Set file name to save
     *
     * @param string $name about file name
     * @return boolean if operation success or not
     *
     * @assert ('test_name') == true
     * @assert ('') == false
     */
    public function setFileName($name = '')
    {
	if (!$name)
	{
	    return false;
	}
	else
	{
	    $this->file_name = $name . '.csv';
	    return true;
	}
    }

    /**
     * Generate csv file and fill it with $array data.
     *
     * @return boolean If the file has been created. False otherwise.
     *
     */
    public function as_view()
    {
	if ($this->array === false)
	    return false;
	$fp = fopen(PATH_TMP_ROOT . $this->file_name, 'w');
	foreach ($this->array as $fields)
	{
	    fputcsv($fp, $fields, $this->delimiter);
	}
	fclose($fp);
	$this->url_location = PATH_TMP_URL . $this->file_name;
	if (file_exists(PATH_TMP_ROOT . $this->file_name))
	{
	    return true;
	}
	else
	{
	    return false;
	}
    }

}

?>
