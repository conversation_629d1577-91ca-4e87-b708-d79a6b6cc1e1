<?php

/*
 * Created on 19/11/2012 by <PERSON><PERSON><PERSON> Marco
 */

class Contact extends DbObject {

	protected $_tableN = 'contact';
	protected $_idF = 'contact_id';
	public $addictional_data;

	public function getAddictionalData() {
		if (empty($this->addictional_data)) {
			$db = Db::getInstance();
			$db->query_params(
					"SELECT * FROM view_city_reg WHERE city_id = $1", array($this->city_id));
			$this->addictional_data = $db->fetchAll();
			$this->addictional_data = $this->addictional_data[0];
		}
	}

	public function getRegionDescription() {
		$this->getAddictionalData();
		return $this->addictional_data['reg_desc'];
	}

	public function getCityDescription() {
		$this->getAddictionalData();
		return $this->addictional_data['city_desc'];
	}

	public function getProvinceCode() {
		$this->getAddictionalData();
		return $this->addictional_data['prov_code'];
	}

}

?>