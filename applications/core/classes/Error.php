<?php

/*
 * Classes handle all error from code
 *
 */

/**
 * Error class.
 * This manage all kind of errors. From PHP error handler or whatever bug in code
 *
 * <AUTHOR>
 */
class Error {

    private $_tableName = 'event_error';
    private $_level;
    private $_description;
    private $_path;
    private $_line;
    private $_error_time;
    private $_toLog = false;
    private $_errorType = array(
        E_ERROR        => "Error",
        E_WARNING      => "Warning",
        E_USER_ERROR   => "User Error",
        E_USER_WARNING => "User Warning",
            //E_NOTICE       => "Notice"
    );

    public function __construct($level, $message, $path, $line) {
        if (array_key_exists($level, $this->_errorType)) {
            $this->_toLog = true;
            $this->_error_time = date("Y-m-d H:i:s (T)");
            $this->_level = $level;
            $this->_level_descr = $this->_errorType[$this->_level];
            $this->_description = pg_escape_string($message);
            $this->_path = pg_escape_string($path);
            $this->_line = (int) $line;
            $this->db = Db::getInstance();
        }
    }

    public function writeLog() {
        if ($this->_toLog === true) {
            $sql = "INSERT INTO " . $this->_tableName . "
                (level_nr, level_descr, description, path, line, error_time)
                VALUES (" . $this->_level . ", '" . $this->_level_descr . "', '" . $this->_description . "', '" . $this->_path . "', " . $this->_line . ", '" . $this->_error_time . "' )";
            $this->db->query($sql);
            return true;
        }
        return false;
    }

}
