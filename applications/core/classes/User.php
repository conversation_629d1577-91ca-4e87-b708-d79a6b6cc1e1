<?php

/**
 * Class manage users data.
 *
 * <AUTHOR>
 */
class User extends DbObject {

    protected $_tableN = 'users';
    protected $_idF = 'uid';

    public function __construct($id = -1) {
        parent::__construct($id);
    }

    public function getUiPermissions() {
        if ($this->isSuperUser() == 't') {
            return array();
        }

        $db = Db::getInstance();
        $db->query(
                "SELECT
                        *
                 FROM
                        auth_element
                 WHERE
                        auth_permission in (
                                SELECT auth_permission FROM auth_permission_group WHERE groups=" . (int) $this->user_type . "
                                        " . ($this->isAdmin() === false ? " UNION SELECT id from auth_permissions WHERE super_user = 't'" : "") . "
                                ) "
        );
        $permissionArr = $db->fetchAll();
        if ($permissionArr === false) {
            return array();
        }
        $permissions = array();

        foreach ($permissionArr as $p) {
            $permissions[$p['control_interface']][$p['name']] = $p['state'];
        }
        return $permissions;
    }

    /**
     *
     * Method checks if current group has permmission to execute passed $element,
     * string or array of elements.
     *
     * @param string/array $element
     * @return boolean true if user has permission, false otherwise
     */
    public function hasPermissionPath($path) {
        // Convert to array if not, to be able to check array
        // or string in the same way
        if ($this->isSuperUser() or $this->isAdmin()) {
            return true;
        }

        if (!is_array($path)) {
            $paths = array($path);
        } else {
            $paths = $path;
        }

        foreach ($paths as $path) {
            $this->db->query_params(
                    "SELECT
                        *
                     FROM
                        auth_permission_group
                     WHERE
                        groups = $1 AND auth_permission in
                        (SELECT auth_permission from auth_path WHERE path=$2)", array($this->gid, $path)
            );
            if ($this->db->fetchAll() !== false) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if user is admin. If that is true, you don't have to
     * consider group permission.
     *
     * @return true if user is admin, false otherwise
     */
    public function isAdmin() {
        if ($this->privelege == 1)
            return true;
        else
            return false;
    }

    public function isSuperUser() {
        if ($this->super_user == 't')
            return true;
        else
            return false;
    }

    /**
     *
     * @return year (integer) selected by user
     */
    public function getActiveYear() {
        if ((int) $this->active_year > 0)
            return $this->active_year;
        else
            return date('Y');
    }

    public function setPassword($passwd) {
        // $this->user_password = md5($passwd);
        $this->user_password = MT\Utils\Pbkdf2::encode($passwd);
        $this->save();
    }

    public function setExpiration($timestamp = null) {
        $this->expiration = $timestamp ? $timestamp : -1;
        $this->save();
    }

    public function getGroup() {
        if ((int) $this->user_type < 1)
            return null;
        else
            return \Auth\GroupsQuery::create()->findPk($this->user_type);
    }

}

?>
