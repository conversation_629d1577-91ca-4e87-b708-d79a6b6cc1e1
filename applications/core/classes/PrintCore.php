<?php

class PrintCore extends TCPDF {

    private $_urlPath;
    private $_institute;

    /**
     * Call parent builder for base TCPDF initialization.
     * Then prepare a base data, structure and styling about print
     */
    public function __construct() {
        $this->_institute = new Institute();
        parent::__construct('P', PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    }

    /**
     *
     * @param integer $delta
     * @param char $unit
     * @param boolean $showZero false to not show the value if it is zero
     *
     * @return integer or string of delta, based on format day or hh:mm
     */
    protected function formatTime($delta, $showZero = true, $unit = 'h') {
        if (!$showZero && $delta == 0) {
            return "";
        }

        if ($unit == 'd') {
            return $delta;
        }

        if ($unit == 'h') {
            $negative = $delta < 0 ? true : false;
            $deltaAbs = abs($delta);
            $delta = sprintf("%02d:%02d", floor($deltaAbs / 60), $deltaAbs % 60);

            if ($negative) {
                $delta = '-' . $delta;
            }
            return $delta;
        }
    }

    /**
     * Create PDF file. Check if exist. If not return false, file url otherwise
     *
     */
    public function createPdf() {
        $file_path = PATH_TMP_ROOT . $this->title . '.pdf';
        $file_url = PATH_TMP_URL . $this->title . '.pdf';
        $this->Output($file_path, 'F');
        if (file_exists($file_path)) {
            $this->_urlPath = $file_url;
            return true;
        }
        return false;
    }

    public function getUrlPath() {
        return $this->_urlPath ? $this->_urlPath : '';
    }

    /**
     * Design Header page section, tipically with institute information
     */
    public function Header() {
        // Logo
        $image_file = PATH_ROOT . 'applications/core/resources/images/logo_repubblica.jpg';
        $this->Image($image_file, 5, 5, 15, '', 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);
        // Set font
        $this->SetFont('', 'B', 10);

        $this->SetX(30);

        $this->Cell(0, 0, 'MINISTERO DELL\'ISTRUZIONE E DEL MERITO', 0, 1, 'L', false);
        $this->SetX(30);
        $this->SetFont('', '', 8);
        $this->Cell(0, 0, 'UFFICIO SCOLASTICO REGIONALE PER IL/LA ' . strtoupper($this->_institute->getContact()->getRegionDescription()), 0, 1, 'L', false);
        $this->SetX(30);
        $this->SetFont('', 'B', 10);
        $this->Cell(0, 0, $this->_institute->name, 0, 1, 'L', false);
        $this->SetX(30);
        $this->SetFont('', '', 8);
        $this->Cell(0, 0, $this->_institute->getContact()->getCityDescription() . ' (' . $this->_institute->getContact()->getProvinceCode() . ') ' . $this->_institute->getContact()->address . ' C.F. ' . $this->_institute->fiscal_code, 0, 1, 'L', false);
    }

    /**
     * Draw Footer in all base prints
     */
    public function Footer() {
        $this->SetY(-15);
        $this->SetFont('', 'I', 5);
        //$this->Cell(0, 10, _('Page ') . $this->getAliasNumPage() . '/' . $this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
        $this->Cell(0, 10, _('Page') . " " . $this->getAliasNumPage() . " " . _('of') . " " . $this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }

}
