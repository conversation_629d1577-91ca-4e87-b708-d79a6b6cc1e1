<?php

/**
 * Base database objects that write read and update element
 * who extends it
 *
 * <AUTHOR>
 */
abstract class DbObject {

	private $_dbColumns = array();
	protected $_tableN;
	protected $_idF;
	protected $_idV;
	protected $_fields = array();
	public $db;
	public $values = array();

	/**
	 *
	 * Instance the objects based on extended one.
	 * Prepare all data with corresponded field type
	 * and, if id is provided, load all value from the table.
	 *
	 * @return boolean
	 */
	public function __construct($id = -1) {

		if (!$this->_tableN OR !$this->_idF) {
			return false;
//throw new InvalidArgumentException();
		}
		if ($id) {
			$this->_idV = $id;
		}
		$this->db = Db::getInstance();
		$sql = "SELECT column_name, data_type, column_default
		FROM information_schema.columns WHERE table_name = '" . $this->_tableN . "'
		AND table_schema = 'public'";
		$this->db->query($sql);
		$data = $this->db->fetchAll();
		if ($data === false) {
			return false;
		}
		foreach ($data as $kind) {
			$this->_dbColumns[$kind['column_name']] = array(
				'type'		 => $kind['data_type'],
				'default'	 => isset($kind['column_default']) ? $kind['column_default'] : 'NULL'
			);
		}
		if ($this->read() === false) {
			if ($id > 0) {
				throw new DbObjectNotFound(DB_OBJ_NOT_FOUND, _('Object not found in table'));
			}
		}
	}

	/**
	 * Take last id from given table. If not exists, return false
	 */
	private function _setId() {
		if ($this->isNew()) {
			$this->db->query("SELECT max(" . $this->_idF . ") FROM " . $this->_tableN);
			$max = $this->db->fetchAll();
			$last = (int) $max[0]['max'];
			return $last > 0 ? $last : false;
		} else {
			return $this->_idV;
		}
	}

	/**
	 * Take column name and value.
	 * Return a value in correct format based on type in postgres,
	 * or false if no type found.
	 *
	 * @param string $col
	 * @param mix $value
	 */
	private function _formatParam($col, $value = '') {
		switch ($this->_dbColumns[$col]['type']) {
			case 'text':
			case 'character varying':
			case 'character':
				return "'" . pg_escape_string($value) . "'";
			case 'integer':
			case 'bigint':
			case 'smallint':
			case 'double precision':
				return is_numeric($value) ? $value : 0;
			case 'boolean':
				if ($value === 'f' OR $value === false OR (is_numeric($value) AND $value < 1)) {
					$value = 'false';
				} else {
					$value = 'true';
				}
				return $value;
			default:
				return false;
		}
	}

	/**
	 * Create query to update an alement in database
	 *
	 * @return string Update Query
	 */
	private function _prepareQueryUpdate() {
		$query = "UPDATE " . $this->_tableN . " SET ";
		$valArr = array();
		foreach ($this->_dbColumns as $name => $value) {
			if ($name != $this->_idF AND isset($value['value'])) {
				$valArr[] = $name . " = " . $this->_formatParam($name, $value['value']);
			}
		}
		$query .= join(',', $valArr) . " WHERE " . $this->_idF . " = " . (is_numeric($this->_idV) ? $this->_idV : "'" . $this->_idV . "'");
		return $query;
	}

	/**
	 * Create query to insert a new element in database
	 *
	 * @return string Insert Query
	 */
	private function _prepareQueryNew() {
		$query = "INSERT INTO " . $this->_tableN . " ";
		$valArr = $fieldArr = array();
		foreach ($this->_dbColumns as $name => $value) {
			if ($name != $this->_idF) {
				$fieldArr[] = $name;
				if (isset($value['value'])) {
					$valArr[] = $this->_formatParam($name, $value['value']);
				} else {
					$valArr[] = $value['default'];
				}
			}
		}
		$query .= "(" . join(',', $fieldArr) . ") VALUES (" . join(',', $valArr) . ")";
		return $query;
	}

	/**
	 * Keep in memory the changed fields during elaborations
	 *
	 * @param string $property
	 * @param type $value
	 */
	public function __set($property, $value) {
		$this->_dbColumns[$property]['value'] = $value;
	}

	/**
	 * Get values from correct array
	 *
	 * @param string $property (column name in table)
	 * @return value
	 */
	public function __get($property) {
		return $this->_dbColumns[$property]['value'];
	}

	/**
	 *
	 * @return id of table for specific row setted by calling  class
	 */
	public function getTableName() {
		return $this->_tableN;
	}

	/**
	 *
	 * @return id of table for specific row setted by calling  class
	 */
	public function getId() {
		return $this->_idV;
	}

	/**
	 * Check if instance is a new element for the table or not
	 * and return the correspondet answer.
	 *
	 * @return boolean
	 */
	public function isNew() {
		if ($this->_idV <= 0 AND is_numeric($this->
						_idV))
			return true;
		else
		if (!is_numeric($this->_idV) AND $this->_idV == '')
			return true;
		else
			return false;
	}

	/**
	 * Read and set property of the object based on table fields, if
	 * _idV is setted. If don't, return false.
	 *
	 * @return boolean
	 */
	public function read() {
		if ($this->isNew() === false) {
			$this->db->query_params("SELECT * FROM " . $this->_tableN . " WHERE " . $this->_idF . " = $1", array($this->_idV));
			$obj = $this->db->fetchAll();
			if ($obj !== false) {
				foreach ($obj[0] as $key => $value) {
					$this->$key = $value;
				}
				return true;
			} else {
				return false;
			}
		}
		return false;
	}

	/**
	 * Load data from Array into Db object preparing to save.
	 *
	 * @param array $data
	 */
	public function load($data = array()) {
		foreach ($data as $col => $value) {
			if (in_array($col, array_keys($this->_dbColumns))) {
				$this->$col = $value;
			}
		}
	}

	/**
	 * Recognise if you are saving a new one or update an existing element
	 * and return relative id.
	 *
	 * @return id of inserted/updated object
	 */
	public function save() {
		if ($this->isNew()) {
			$query = $this->_prepareQueryNew();
		} else {
			$query = $this->_prepareQueryUpdate();
		}
		$this->db->query($query);
		$this->_idV = $this->_setId();

// Set the new/updated data into array containing columns details
		$this->read();
// Set id in original column of table
		$fn = $this->_idF;
		$this->$fn = $this->_idV;
		return $this->_idV;
	}

	/**
	 * Delete object element
	 *
	 * @return results of query execution
	 */
	public function delete() {
		$query = "DELETE FROM " . $this->_tableN . " WHERE " . $this->_idF . " = " . (is_numeric($this->_idV) ? $this->_idV : "'" . $this->_idV . "'");
		return $this->db->query($query);
	}

	/**
	 * Return the object as an Array
	 *
	 * @return array of table fields
	 */
	public function toArray() {
		$data = array();
//$keys = array_keys($this->_fields);
		foreach ($this->_dbColumns as $key => $value) {
			$data[$key] = $value['value'];
		}
//$data[$this->_idF] = $this->_idV;
		return $data;
	}

}

?>
