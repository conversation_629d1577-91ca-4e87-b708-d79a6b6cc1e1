<?php

/**
 * Manage list of employee.
 *
 * <AUTHOR>
 */
class Institutes extends ListObjects {

	protected $_table = 'institute';
	protected $_fields = array(
		'institute_id',
		'name',
		'mechan_code',
		'fiscal_code',
		'def',
		'postal_account',
		'(SELECT description FROM contact,cities where contact.city_id=cities.city_id AND contact.contact_id = institute.contact_id) AS city',
		'(SELECT address FROM contact,cities where contact.city_id=cities.city_id AND contact.contact_id = institute.contact_id) AS address'
	);
	protected $_order = array('name');

}

?>
