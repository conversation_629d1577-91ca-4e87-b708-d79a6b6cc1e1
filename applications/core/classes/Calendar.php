<?php

/*
 * Class to manage calendar data.
 * Manage days, month and year switch.
 */

/**
 * Calendar
 *
 * <AUTHOR>
 */
class Calendar {

	public $week_day_start;
	public $week_day_end;
	public $date; // In timestamp (seconds)
	public $day;
	public $day_j;
	public $weekday;
	public $week;
	public $month;
	public $month_n;
	public $month_days;
	public $year;

	/**
	 * Calendar constructor
	 */
	public function __construct() {
		$this->week_day_start = 1; // By default "Monday"
		$this->week_day_end = 0;
		$this->setDate();
	}

	/**
	 * Sets the calendar active date.
	 *
	 * @param string $date to set on calendar
	 */
	public function setDate($date = 'now') {
		$this->date = strtotime($date);
		$this->day = date('d', $this->date);
		$this->day_j = date('j', $this->date);
		$this->weekday = date('N', $this->date);
		$this->week = date('W', $this->date);
		$this->month = date('m', $this->date);
		$this->month_n = date('n', $this->date);
		$this->month_days = date('t', $this->date);
		$this->year = date('Y', $this->date);
	}

	/**
	 * Set what is the first day in a week.
	 * Italian standard is Monday.
	 *
	 * @param integer $ds start day
	 */
	public function setDayStart($ds = 1) {
		$this->week_day_start = (int) $ds;
		$this->week_day_end = $this->week_day_start - 1;
		$this->week_day_end = $this->week_day_end < 0 ? 6 : $this->week_day_end;
	}

	/**
	 *
	 * @return integer rapresents first month day in week day format
	 */
	public function getMonthFirstDayWeek() {
		return date('w', strtotime("1-" . $this->month . "-" . $this->year));
	}

	/**
	 *
	 * @return integer rapresents last month day in week day format
	 */
	public function getMonthLastDayWeek() {
		return date('w', strtotime("1-" . $this->month . "-" . $this->year . ' +1 month -1 day'));
	}

	/**
	 *
	 * @return type
	 */
	public function getMonthBorderExtended() {
		// Take first week day of month
		$first_month_day = $this->getMonthFirstDayWeek();

		// If the difference between the start day and first day is negative,
		// it means that the first day is sunday, so -6 days has to disaplay in
		// calendar, else you have to display the difference between the values
		// in prev month
		$days_prev_month_base = $first_month_day - $this->week_day_start;
		$days_prev_month = $days_prev_month_base < 0 ? -6 : $days_prev_month_base * -1;

		//Make the same thing with end month
		$last_month_day = $this->getMonthLastDayWeek();
		$days_next_month_base = 7 - $last_month_day;
		$days_next_month = $days_next_month_base == 7 ? 0 : $days_next_month_base;

		// Get effective date start and date end for calendar
		$start_day = strtotime("1-" . $this->month . "-" . $this->year . ' ' . $days_prev_month . ' days');
		$end_day = strtotime("1-" . $this->month . "-" . $this->year . ' +1 month -1 day ' . $days_next_month . ' days');

		return array(
			'start'	 => $start_day,
			'end'	 => $end_day
		);
	}

	/**
	 * Check if passesd date is holiday (Festivity or Weekend) or not.
	 *
	 * @return boolean if it is holiday or not
	 */
	public function isHoliday($date = 0) {
		if ($this->isFestivity($date) || $this->isWeekend($date)) {
			return true;
		}
		return false;
	}

	/**
	 * Check if passesd date is festivity or not.
	 *
	 * @return boolean if it is festivity or not
	 */
	public function isFestivity($date = 0) {
		$db = Db::getInstance();

		if ($date > 0) {
			$day = date('j', $date);
			$month = date('n', $date);
			$weekday = date('N', $date);
		} else {
			$day = $this->day_j;
			$month = $this->month_n;
			$weekday = $this->weekday;
		}

		$db->query_params("SELECT * FROM calender_holidays WHERE month=$1 and day=$2", array($month, $day));
		if ($db->fetchAll() !== false) {
			return true;
		}
		return false;
	}

	/**
	 * Check if passesd date is weekend or not.
	 *
	 * @return boolean if it is weekend or not
	 */
	public function isWeekend($date = 0) {
		$db = Db::getInstance();

		if ($date > 0) {
			$day = date('j', $date);
			$month = date('n', $date);
			$weekday = date('N', $date);
		} else {
			$day = $this->day_j;
			$month = $this->month_n;
			$weekday = $this->weekday;
		}

		$db->query_params("SELECT * FROM calender_weekends WHERE week_day=$1 AND weekends='t'", array($weekday));
		if ($db->fetchAll() !== false) {
			return true;
		}
		return false;
	}

	/**
	 * Checks if the day has a timetable in it.
	 *
	 * @param timestamp $date the day to check
	 * @param integer $emp_id the employee id
	 * @return boolean True if this day has a timetable in it, false otherwise
	 */
	public function isWorkday($date, $emp_id) {
		$db = Db::getInstance();
		$date = date("Ymd", $date);
		$db->query_params("SELECT * FROM personnel_timetable
            WHERE (to_char(to_timestamp(date_start),'YYYYMMDD')=$1
            OR to_char(to_timestamp(date_end),'YYYYMMDD')=$2)
            AND employee_id=$3", array($date, $date, (int) $emp_id));
		if ($db->fetchAll() !== false) {
			return true;
		}
		return false;
	}

	/**
	 * Check if the date is in a leap year.
	 *
	 * @return boolean True if it's a leap year, False otherwise
	 */
	public function isLeapYear() {
		return date('L', $this->date) == 1 ? true : false;
	}

	/**
	 * If the passed day is holiday in table, it deletes it, otherwise it inserts it.
	 *
	 * @param integer $day
	 * @param integer $month
	 */
	public function changeDayHoliday($day, $month) {
		$db = Db::getInstance();
		$db->query_params("SELECT * FROM calender_holidays WHERE month=$1 and day=$2", array($month, $day));
		if ($db->fetchAll() === false) {
			$db->query_params("INSERT INTO calender_holidays (month, day) VALUES ($1, $2)", array($month, $day));
		} else {
			$db->query_params("DELETE FROM calender_holidays WHERE month=$1 and day=$2", array($month, $day));
		}
	}

	/**
	 * Update week day as holiday or not.
	 *
	 * @param integer $week_day
	 */
	public function changeWeekHoliday($week_day) {
		$db = Db::getInstance();
		$db->query_params("SELECT * FROM calender_weekends WHERE week_day=$1", array($week_day));
		$arr = $db->fetchAll();
		$db->query_params("
            UPDATE calender_weekends SET weekends = " . ($arr[0]['weekends'] == 'f' ? "'t'" : "'f'") . " WHERE week_day=$1", array($week_day));
	}

}

?>