<?php

/*
 * This class need to log every step of user in mastercom2 to
 * build an history about him in the program
 *
 */

/**
 * UserLog
 *
 * <AUTHOR>
 */
class UserLog
{

    public $user_id;

    public function __construct($user_id = -1)
    {
        if($user_id < 0){
            $this->user_id = (int) $_SESSION['uid'];
        } else {
            $this->user_id = (int) $user_id;
        }
        $this->user = new User($this->user_id);
    }

    public function writeLog($section, $operation = null, $table = null, $table_id = null)
    {
        $operation = $operation ? $operation : "view";
	$db = Db::getInstance();
	$db->query_params("
            INSERT INTO user_log (user_id, user_name, section, operation, used_table, used_id)
            VALUES ($1, $2, $3, $4, $5, $6)", array($this->user_id, $this->user->user_name ,$section, $operation, $table, $table_id));
	return true;
    }

}

?>
