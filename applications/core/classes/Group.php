<?php

/**
 * Group manage
 *
 * <AUTHOR>
 */
class Group extends DbObject {

	protected $_tableN = 'groups';
	protected $_idF = 'gid';

	/**
	 *
	 * Method checks if current group has permmission to execute passed permissions
	 * integer element.
	 *
	 * @param integer $element
	 * @return boolean true if user has permission, false otherwise
	 */
	public function hasPermission($permission_id) {

		$this->db->query_params(
				"SELECT * FROM auth_permission_group WHERE auth_permission =$1 and groups=$2", array($permission_id, (int) $this->gid)
		);

		if ($this->db->fetchAll() === false) {
			return true;
		} else {
			return false;
		}
	}

	public function __toString() {
		return isset($this->group_name) ? $this->group_name : '';
	}

}

?>
