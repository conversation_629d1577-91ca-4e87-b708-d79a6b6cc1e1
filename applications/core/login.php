<?php

/*
 * This file manage login to Mastercom 2
 */

//require_once '../../index.php';

// Index file for MC2
header('Content-type: text/html; charset=utf-8');
session_start();


// Imports INIT scripts
require_once dirname(__FILE__) . '/../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';
require_once PATH_ROOT . 'configurations/init-api.php';
require_once PATH_ROOT . 'applications/core/checkRequest.php';
// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");


// Logger path
require_once "/var/www/logger/logger.php";

$username = $request->username;
$password = $request->password;

$badPassword = false;
if( strlen($password)<8 ) {
    $badPassword = true;
}

$login = new \Core\Login($username, $password);
$auth = 2;
if($login->couchAuthenticationEnabled() && !$badPassword) {
    if(!$login->isCouchUser()) {
        
        $user = $login->authentication();
        if($user && !$user->isSuperUser()) {
            if(!$login->generateCouchUser($user)) {
                $user = false;
            }  else {
                $user = $login->couchAuthentication();
                $auth = 3;
            }
        } 
    } else {
        $user = $login->couchAuthentication();
    }
    
} else {
    $user = $login->authentication();
}

if(!$user || $user->isSuperUser() || $login->nexusUser) {
    $badPassword = false;
}

$data = [
    'success' => true,
];

/**
 * $auth
 * 0 false
 * 1 expired
 * 2 ok
 * 3 generate user on couch
 */
if ($user === false) {
    $auth = 0;
    $logger->log([
        'Level'   => 2,
        'Type'    => 'WARNING',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Login fallito: {$username}"
    ]);
} else {
    if ( ($user->expiration > -1 && $user->expiration <= time() && $auth != 3) || $badPassword) {
        $auth = 1;
        $_SESSION['uid'] = $user->uid;

        if($badPassword) {
            $data['force_mc2'] = 1;
        }

        $logger->log([
            'Level'   => 2,
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Credenziali scadute: {$username}"
        ]);
    } else {

        $_SESSION['uid'] = $user->uid;
        $_SESSION['user_name'] = $user->user_name;

        $_SESSION['auth']['uid'] = $user->uid;
        $_SESSION['auth']['user_name'] = $user->user_name;

        $logger->log([
            'Type'     => 'INFO',
            'Scope'    => __FILE__,
            'Event'    => basename(__FILE__, ".php"),
            'Message'  => "Login",
            'UserId'   => $_SESSION['uid'],
            'UserName' => $_SESSION['user_name']
        ]);
    }

    $data['uid'] = $user->uid;
    $data['username'] = $user->user_name;
}
$data['auth'] = $auth;

echo Response::Create($data);
