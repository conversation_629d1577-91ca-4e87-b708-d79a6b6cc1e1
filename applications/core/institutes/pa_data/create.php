<?php

require_once '../../../../index.php';

$institute_id = $request->institute_id ? (int) $request->institute_id : 0;
$code = $request->code ? $request->code : 'NULL';
$description = $request->description ? $request->description : 'NULL';

$res = false;

$db = Db::getInstance();
$r = $db->query("INSERT INTO pa_codes (institute_id, code, description) VALUES ({$institute_id}, {$code}, {$description});");

if ($r != false) {
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Creazione Codice PA: istituto {$institute_id}, {$code} - {$description}",
        'Context' => print_r($request, true)
    ));
    $res = true;
}

echo Response::Create(array(
    'success' => $res,
    'results' => $code
));
