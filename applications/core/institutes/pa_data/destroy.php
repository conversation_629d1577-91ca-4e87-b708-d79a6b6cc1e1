<?php

require_once '../../../../index.php';

$pa_code_id = $request->pa_code_id ? (int) $request->pa_code_id : 0;

$res = false;

$db = Db::getInstance();
$db->query("SELECT * FROM pa_codes WHERE id = {$pa_code_id};");
$code = $db->fetchAll();
$r = $db->query("DELETE FROM pa_codes WHERE id = {$pa_code_id};");

if ($r != false) {
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Cancellazione Codice PA: istituto {$code['institute_id']}, {$code['id']} - {$code['code']} - {$code['description']}",
        'Context' => print_r($request, true)
    ));
    $res = true;
}

echo Response::Create(array(
    'success' => $res,
    'results' => $pa_code_id
));
