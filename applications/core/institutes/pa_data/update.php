<?php

require_once '../../../../index.php';

$pa_code_id = $request->pa_code_id ? (int) $request->pa_code_id : 0;
$code = $request->code ? $request->code : 'NULL';
$description = $request->description ? $request->description : 'NULL';

$res = false;

$db = Db::getInstance();
$paCode = $db->query("UPDATE pa_codes SET code = {$code}, description = {$description} WHERE id = {$pa_code_id}");

if ($paCode != false) {
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Aggiornato Codice PA: {$pa_code_id} - {$code} - {$description}",
        'Context' => print_r($request, true)
    ));
    $res = true;
}

echo Response::Create(array(
    'success' => $res
));
