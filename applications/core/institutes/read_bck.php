<?php

/*
 * Load list institutes.
 * Depends of school name filter
 *
 */

use Core\InstituteQuery;

require_once '../../../index.php';

$institutes = new InstituteQuery();
$institutes->joinWith('Contact', Criteria::LEFT_JOIN)
        ->joinWith('Contact.CityKey', Criteria::LEFT_JOIN)
        ->withColumn('Contact.address', 'address')
        ->withColumn('CityKey.description', 'city')
        ->withColumn('CityKey.city_id', 'city_id')
        ->select(array('institute_id', 'name', 'mechan_code', 'fiscal_code', 'def', 'postal_account', 'address', 'city', 'city_id'));
$institutes->orderByDef(Criteria::DESC);
$institutes->orderByName();

if (isset($request->get['q_school_name']) && $request->get['q_school_name']) {
    $institutes->where("name ILIKE '%" . pg_escape_string($request->get['q_school_name']) . "%'");
}

$total = $institutes->count();
$institutes->limit((int) $request->get['limit']);
$institutes->offset((int) $request->get['start']);

$data = $institutes->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);

echo Response::Create(array(
    'success' => true,
    'results' => $data,
    'total'   => $total
));
