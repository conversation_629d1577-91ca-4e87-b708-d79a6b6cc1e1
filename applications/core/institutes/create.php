<?php

/*
 * Create a new school.
 *
 */

require_once '../../../index.php';

use \Core\InstituteQuery;

$institute_id = (int) $request->institute_id;

// Update the school as a selected one
if (isset($request->select) AND (bool) $request->select == true) {
    InstituteQuery::create()->filterByDef(true)->update(array('Def' => false));
    $institute = InstituteQuery::create()->filterByInstituteId($institute_id)->find()->getFirst();

    if ($institute !== null) {
        $institute->setDef(true);
        $institute->save();
    }
} else {
    $institute = InstituteQuery::create()->filterByInstituteId($request->post['institute_id'])->findOne();

    $institute->setName($request->post['name']);
    $institute->setFiscalCode($request->post['fiscal_code']);
    $institute->setMechanCode($request->post['mechan_code']);
    $institute->setPostalAccount((int) $request->post['postal_account']);

    $contact = $institute->getContact();
    $contact->setAddress($request->post['address']);
    $contact->setCityId($request->post['city_id']);
    $contact->save();

    $institute->save();
}

echo Response::Create(array(
    'success' => true,
    'results' => $institute_id
));

$logger->log(array(
    'Type'    => 'INFO',
    'Scope'   => __FILE__,
    'Event'   => basename(__FILE__, ".php"),
    'Message' => "{$message} assenza: Tipo {$data['ab_kind']}, Personale {$data['employee_id']} - dal {$data['start_date']} al {$data['end_date']} ",
    'Context' => print_r($request, true)
));
