<?php

/*
 * Load list institutes.
 * Depends of school name filter
 *
 */

require_once '../../../index.php';

$db = Db::getInstance();

$db->query("SELECT i.institute_id, i.name, i.mechan_code, i.school_fiscal_code, i.fiscal_code, i.school_type, i.postal_account, i.job_director_id, i.job_vice_director_id, i.job_dsga_id, i.job_personnel_id, i.job_accounting_id, i.job_warehouse_id, i.job_registry_id, i.contact_id, i.ipa_code, c.address, c.phone_num, c.fax, c.email, c.mobile, c.web, c.city_id, c.cap, i.ade_email, s.city_code, s.province, s.region, s.is_city FROM institute AS i LEFT JOIN contact AS c ON i.contact_id = c.contact_id LEFT JOIN cities AS s ON c.city_id = s.city_id WHERE i.def = 't';");
$institute = $db->fetchAll();
$institute = $institute[0];

$db->query("SELECT * from institute_role where institute_id = {$institute['institute_id']};");
$roles =  $db->fetchAll();
$institute['roles'] = $roles;

echo Response::Create(array(
    'success' => true,
    'results' => $institute
));
