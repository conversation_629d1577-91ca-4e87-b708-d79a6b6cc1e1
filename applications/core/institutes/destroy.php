<?php

/*
 * Update data for school passing by institute_id.
 * If parameter "select" is passed by GET, only the default school is setted
 */

require_once '../../../index.php';

use \Core\InstituteQuery;

$institute_id = (int) $request->institute_id;

// Update the school as a selected one
if (isset($request->select) AND (bool) $request->select == true) {
    InstituteQuery::create()->filterByDef(true)->update(array('Def' => false));
    $institute = InstituteQuery::create()->filterByInstituteId($institute_id)->find()->getFirst();

    if ($institute !== null) {
        $institute->setDef(true);
        $institute->save();
    }
} else {
    $institute = InstituteQuery::create()->filterByInstituteId($request->post['institute_id'])->findOne();

    $institute->setName($request->post['name']);
    $institute->setFiscalCode($request->post['fiscal_code']);
    $institute->setMechanCode($request->post['mechan_code']);
    $institute->setPostalAccount((int) $request->post['postal_account']);

    $contact = $institute->getContact();
    $contact->setAddress($request->post['address']);
    $contact->setCityId($request->post['city_id']);
    $contact->save();

    $institute->save();
}

echo Response::Create(array(
    'success' => true,
    'results' => $institute_id
));
