<?php

/*
 * Update data for school passing by institute_id.
 * If parameter "select" is passed by GET, only the default school is setted
 */

require_once '../../../index.php';

$res = false;
$institute_id = (int) $request->institute_id;
$city = $request->city_id ? (int) $request->city_id : 'NULL';
$director = $request->job_director_id ? (int) $request->job_director_id : NULL;
$viceDirector = $request->job_vice_director_id ? (int) $request->job_vice_director_id : NULL;
$dsga = $request->job_dsga_id ? (int) $request->job_dsga_id : NULL;
$personnel = $request->job_personnel_id ? (int) $request->job_personnel_id : NULL;
$accounting = $request->job_accounting_id ? (int) $request->job_accounting_id : NULL;
$warehouse = $request->job_warehouse_id ? (int) $request->job_warehouse_id : NULL;
$registry = $request->job_registry_id ? (int) $request->job_registry_id : NULL;

$protocol_manager = [
    'name' => $request->protocol_manager_name,
    'surname' => $request->protocol_manager_surname,
    'fiscal_code' => $request->protocol_manager_fiscal_code,
];

$db = Db::getInstance();

$contact = $db->query_params(""
    . "UPDATE contact "
    . "SET address = $1, phone_num = $2, fax = $3, email = $4, web = $5, cap = $6, city_id = $7 "
    . "WHERE contact_id = $8", [
    $request->address,
    $request->phone_num,
    $request->fax,
    $request->email,
    $request->web,
    $request->cap,
    $city,
    $request->contact_id
]);

$institute = $db->query_params("UPDATE institute "
    . "SET name = $1, "
    . "mechan_code = $2, "
    . "school_fiscal_code = $3, "
    . "fiscal_code = $4, "
    . "school_type = $5, "
    . "job_director_id = $6, "
    . "job_vice_director_id = $7, "
    . "job_dsga_id = $8, "
    . "job_personnel_id = $9, "
    . "job_accounting_id = $10, "
    . "job_warehouse_id = $11, "
    . "job_registry_id = $12, "
    . "ipa_code = $13, "
    . "ade_email = $14 "
    . "WHERE def = 't'", [
    $request->name,
    $request->mechan_code,
    $request->school_fiscal_code,
    $request->fiscal_code,
    $request->school_type,
    $director,
    $viceDirector,
    $dsga,
    $personnel,
    $accounting,
    $warehouse,
    $registry,
    $request->ipa_code,
    $request->ade_email
]);

# se tutti i valori di $protocol_manager sono vuoti, allora elimina la riga
if (empty($protocol_manager['name']) && empty($protocol_manager['surname']) && empty($protocol_manager['fiscal_code'])) {
    $db->query_params("DELETE FROM institute_role WHERE institute_id = $1 AND type = 'protocol_manager'", [$institute_id]);
} else {
    $db->query("SELECT * from institute_role where institute_id = {$institute_id} and type='protocol_manager';");
    $roles =  $db->fetchAll();
    $roles = $roles[0] ? $roles[0] : false;

    // If protocol manager already exists, update it
    if ($roles) {
        $db->query_params("UPDATE institute_role "
            . "SET name = $1, surname = $2, fiscal_code = $3 "
            . "WHERE institute_id = $4 AND type = 'protocol_manager'", [
            $protocol_manager['name'],
            $protocol_manager['surname'],
            $protocol_manager['fiscal_code'],
            $institute_id
        ]);
    } else {
        $db->query_params("INSERT INTO institute_role (institute_id, name, surname, fiscal_code, type) "
            . "VALUES ($1, $2, $3, $4, 'protocol_manager')", [
            $institute_id,
            $protocol_manager['name'],
            $protocol_manager['surname'],
            $protocol_manager['fiscal_code']
        ]);
    }
}

if ($institute != false) {
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Aggiornato istituto predefinito: {$request->institute_id}",
        'Context' => print_r($request, true)
    ));
    $res = true;
}

echo Response::Create(array(
    'success' => $res
));
