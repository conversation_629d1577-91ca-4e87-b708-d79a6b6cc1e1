<?php

require_once '../../../../index.php';

$db = Db::getInstance();

$parameter_id = (int) $request->parameter_id;
$value = pg_escape_string($request->value);

$res = $db->query("UPDATE parameter SET value = '{$value}' WHERE parameter_id = {$parameter_id};");

if ($res != false) {
    $logger->log([
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Modifica parametro: {$request->name} - {$request->value}",
        'Context' => print_r($request, true)
    ]);
    $res = true;
}

echo Response::Create([
    'success' => true
]);
