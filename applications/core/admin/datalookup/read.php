<?php

require_once '../../../../index.php';

$query_ids = explode(",", $request->query_ids);

// IDs formatting
foreach ($query_ids as $key => $query_id) {
    $id = trim($query_id);
    if ((int) $request->query_field_type === 1) {
        $id = pg_escape_string($id);
        $id = "'{$id}'";
    } else {
        $id = (int) $id;
    }
    $query_ids[$key] = $id;
}
$query_ids = implode(",", $query_ids);

$db = Db::getInstance();


// Gets table informations
//$db->query("SELECT column_name AS cname FROM information_schema.columns WHERE table_schema = 'public' AND table_name = '{$request->query_table}' ORDER BY ordinal_position;");
//$columns = $db->fetchAll();
$query_table = pg_escape_string($request->query_table);
$query_field = pg_escape_string($request->query_field);
$db->query("SELECT * FROM {$query_table} WHERE {$query_field} IN ({$query_ids});");
$rows = $db->fetchAll();

foreach ($rows as $idx => $columns) {
    foreach ($columns as $key => $value) {
        $results[$idx]['data'][] = strtoupper($key) . ": " . $value;
    }
    $results[$idx]['data'] = implode(" | ", $results[$idx]['data']);
}

echo Response::Create(array(
    'success' => true,
    'results' => $results
));
