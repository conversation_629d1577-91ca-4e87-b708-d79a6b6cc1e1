<?php

/*
 * Load cities
 */

require_once '../../../index.php';

$db = Db::getInstance();



$query = "SELECT city_id, description FROM cities";
if($_GET['city_id']) {
	$query = "SELECT c.city_id,description,province,zip_code FROM cities c, zip_codes where zip_codes.city_id=c.city_id and c.city_id={$_GET['city_id']}";	
}
$db->query($query);
$cities = $db->fetchAll();

if($_GET['city_id']) $cities = $cities[0];


echo Response::Create(
        array(
            'success' => true,
            'results' => $cities
        )
);
