<?php

/*
 * Update single group
 */

require_once '../../../index.php';

$gid = (int) $request->json['gid'];
$group = new Group($gid);
$group->delete();

$db = Db::getInstance();
//$db->query_params("DELETE FROM permission_interface_property WHERE group_id = $1", array($gid));
$db->query_params("DELETE FROM auth_permission_group WHERE groups = $1", array($gid));

echo Response::Create(array(
	'success' => true
));
