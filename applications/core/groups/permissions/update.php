<?php

/*
 * Update data about group permissions
 */

require_once '../../../../index.php';

$success = false;

$group = Auth\GroupsQuery::create()->findPk((int) $request->group_id);

if ($group !== null) {
	$p = Auth\AuthPermissionQuery::create()->findPk((int) $request->p_id);
	if ($p !== null) {
		$group->switchPermission($p, $request->allow);
		$success = true;
	}
}

echo Response::Create(
		array(
			'success' => $success
		)
);
