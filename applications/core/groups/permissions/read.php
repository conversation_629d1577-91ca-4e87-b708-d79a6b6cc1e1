<?php

/*
 * Read data about group permissions
 */

require_once '../../../../index.php';

$super_user = $request->user->isSuperUser() ? "t" : "f";
$group = (int) $request->get['group_id'] > 0 ? (int) $request->get['group_id'] : 0;

function reindex($array) {
    $array = array_values($array);
    foreach ($array as $key => $element) {
        if (isset($element['children'])) {
            $array[$key]['children'] = reindex($element['children']);
        }
    }
    return $array;
}

// Filters out super user only permissions if needed
$where = "WHERE super_user = 'f'";
if ($super_user == 't') {
    $where = "";
}

// Returns no records if group is not found or specified
$limit = '';
if ($group === 0) {
    $limit = "LIMIT 0";
}

// Gets the permissions tree of the specified group (add "c" columns if needed)
$sql = "SELECT
	id,
	c[1] AS section,
	c[2] AS c1,
	c[3] AS c2,
	c[4] AS c3,
 	allow,
	(CASE
		WHEN c[1] = 'Generale' THEN 0
		WHEN c[1] = 'Personale' THEN 1
		WHEN c[1] = 'Conti Corrente' THEN 2
		WHEN c[1] = 'Magazzino - Inventario' THEN 3
        WHEN c[1] = 'Segreteria Digitale' THEN 4
		WHEN c[1] = 'Protocollo Informatico' THEN 5
        WHEN c[1] = 'Albo Pretorio' THEN 6
        WHEN c[1] = 'Trasparenza Amministrativa' THEN 7
		WHEN c[1] = 'Impostazioni' THEN 8
		ELSE 0 END) AS section_order,
	(CASE
                WHEN c[2] = 'Stampe' THEN 99
                WHEN c[2] = 'Aggiorna pagina web' THEN 98
		WHEN c[2] = 'Attivo' THEN 0
		WHEN c[2] = 'Barra amministrazione' THEN 1
		WHEN c[2] = 'Anagrafica' THEN 1
		WHEN c[2] = 'Assenze' THEN 2
		WHEN c[2] = 'Orario' THEN 3
		WHEN c[2] = 'Timbrature' THEN 4
		WHEN c[2] = 'Progetti' THEN 5
		WHEN c[2] = 'Chiusura mese' THEN 6
		WHEN c[2] = 'Parametri' THEN 7
		WHEN c[2] = 'Calendario' THEN 8
		WHEN c[2] = 'Gestione monteore' THEN 9
		WHEN c[2] = 'Gestione progetti' THEN 10
		WHEN c[2] = 'Movimenti' THEN 1
                WHEN c[2] = 'Categorie' THEN 2
                WHEN c[2] = 'Addizionali' THEN 3
		WHEN c[2] = 'Tipi di Movimento' THEN 4
                WHEN c[2] = 'Pagamenti' THEN 5
                WHEN c[2] = 'Ricevute' THEN 6
                WHEN c[2] = 'Residui' THEN 7
                WHEN c[2] = 'Bollettini' THEN 8
		WHEN c[2] = 'Utenti' THEN 1
		WHEN c[2] = 'Gruppi' THEN 2
		WHEN c[2] = 'Istituto' THEN 3
        WHEN c[2] = 'Protocolli' THEN 1
        WHEN c[2] = 'Titolario' THEN 2
        WHEN c[2] = 'Corrispondenti' THEN 3
        WHEN c[2] = 'Tipi di Oggetto' THEN 4
        WHEN c[2] = 'Mezzi di Invio' THEN 5
        WHEN c[2] = 'Protocollazione Automatica' THEN 6
        WHEN c[2] = 'Riservato' THEN 7
        WHEN c[2] = 'Segreteria Digitale' THEN 1
        WHEN c[2] = 'Da esaminare' THEN 2
        WHEN c[2] = 'In entrata' THEN 4
        WHEN c[2] = 'Archivio' THEN 3
        WHEN c[2] = 'Fascicoli' THEN 5
        WHEN c[2] = 'Pubblicazioni' THEN 1
        WHEN c[2] = 'Aree' THEN 2
        WHEN c[2] = 'Categorie' THEN 3
        WHEN c[2] = 'Enti' THEN 4
        WHEN c[2] = 'Voci' THEN 1
		-- Other Permissions
		ELSE 0 END) AS c1_order,
	(CASE
                WHEN c[3] = 'Stampe singole' THEN 99
                WHEN c[3] = 'Stampare' THEN 98
		WHEN c[3] = 'Visualizzare' THEN 0
		WHEN c[3] = 'Aggiungere' THEN 1
		WHEN c[3] = 'Modificare' THEN 2
		WHEN c[3] = 'Eliminare' THEN 3
		WHEN c[3] = 'Copiare' THEN 4
		WHEN c[3] = 'Creare decreto' THEN 5
		WHEN c[3] = 'Bloccare/Sbloccare' THEN 1
		WHEN c[3] = 'Monteore' THEN 1
		WHEN c[3] = 'Tipo Assenza' THEN 2
		WHEN c[3] = 'Aggiungere & Modificare' THEN 1
		WHEN c[3] = 'Copiare Settimana' THEN 4
		WHEN c[3] = 'Eliminare Settimana' THEN 5
		WHEN c[3] = 'Eliminare Periodo' THEN 6
		WHEN c[3] = 'Reset' THEN 5
                WHEN c[3] = 'Quote' THEN 6
		WHEN c[3] = 'Progetto' THEN 1
		WHEN c[3] = 'Ora' THEN 2
                WHEN c[3] = 'Emettere' THEN 1
		WHEN c[3] = 'CRUD permessi' THEN 1
		WHEN c[3] = 'Cambiare password' THEN 4
		WHEN c[3] = 'Cambiare permessi' THEN 4
                WHEN c[3] = 'Banche' THEN 2
                WHEN c[3] = 'Gestione Istituti' THEN 3
                WHEN c[3] = 'Salvare' THEN 1
                WHEN c[3] = 'Annullare' THEN 4
                WHEN c[3] = 'Visualizzare storico' THEN 5
                WHEN c[3] = 'Archivio' THEN 1
                WHEN c[3] = 'Scaricare' THEN 4
                WHEN c[3] = 'Pubblicare' THEN 6
                WHEN c[3] = 'Prorogare' THEN 7
		ELSE 0 END) AS c2_order,
	(CASE
		WHEN c[4] = 'Visualizzare' THEN 0
		WHEN c[4] = 'Aggiungere' THEN 1
		WHEN c[4] = 'Modificare' THEN 2
		WHEN c[4] = 'Eliminare' THEN 3
		WHEN c[4] = 'Copiare' THEN 4
		WHEN c[4] = 'Abbinare' THEN 5
                WHEN c[4] = 'Reset' THEN 6
		ELSE 0 END) AS c3_order
FROM
	(SELECT
		ap.id,
		string_to_array(ap.title, ' | ') AS c,
		CASE WHEN (
			SELECT apg.auth_permission
			FROM auth_permission_group AS apg
			WHERE ap.id = apg.auth_permission
			AND apg.groups = {$group}
		) IS NULL THEN 't' ELSE 'f' END AS allow
	FROM auth_permissions AS ap {$where}) AS permissions
ORDER BY section_order, c1_order, c2_order, c3_order {$limit};";

$db = Db::getInstance();
$db->query($sql);
$permissions = $db->fetchAll();

$merges = array();

if (is_array($permissions)) {
    foreach ($permissions as $key => $permission) {
        $levels = 1;
        $ps = $permission['section_order'];
        $pc1 = $permission['c1_order'];
        $pc2 = $permission['c2_order'];
        $pc3 = $permission['c3_order'];

        $p0 = str_replace(" ", "_", $permission['section']);
        $p1 = str_replace(" ", "_", $permission['c1']);
        $p2 = str_replace(" ", "_", $permission['c2']);
        $p3 = str_replace(" ", "_", $permission['c3']);

        $title = $permission['c1'];

        if ($permission['c2'] != null) {
            $levels++;
            $title = $permission['c2'];

            if ($permission['c3'] != null) {
                $levels++;
                $title = $permission['c3'];
            }
        }

        // Leaf node
        $p = array(
            'p_id'  => (int) $permission['id'],
            'name'  => $title,
            'allow' => $permission['allow'] == 't' ? true : false,
            'leaf'  => true
        );

        $path = "root/" . $p0;

        // Section node
        $merges[$ps]['id'] = $path;
        $merges[$ps]['name'] = $permission['section'];

        // Possible intermediate nodes
        switch ($levels) {
            case 3:
                $path = $path . "/" . $p1;
                $merges[$ps]['children'][$pc1]['id'] = $path;
                $merges[$ps]['children'][$pc1]['name'] = $permission['c1'];

                $path = $path . "/" . $p2;
                $merges[$ps]['children'][$pc1]['children'][$pc2]['id'] = $path;
                $merges[$ps]['children'][$pc1]['children'][$pc2]['name'] = $permission['c2'];

                $p['id'] = $path . "/" . $permission['id'];
                $merges[$ps]['children'][$pc1]['children'][$pc2]['children'][$pc3] = $p;
                break;
            case 2:
                $path = $path . "/" . $p1;
                $merges[$ps]['children'][$pc1]['id'] = $path;
                $merges[$ps]['children'][$pc1]['name'] = $permission['c1'];

                $p['id'] = $path . "/" . $permission['id'];
                $merges[$ps]['children'][$pc1]['children'][$pc2] = $p;
                break;
            default:
                $p['id'] = $path . "/" . $permission['id'];
                $merges[$ps]['children'][$pc1] = $p;
                break;
        }
    }

    $merges = reindex($merges);
}

echo Response::Create(array(
    'success'  => true,
    'children' => $merges
));
