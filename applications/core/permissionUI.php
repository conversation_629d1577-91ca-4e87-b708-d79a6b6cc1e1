<?php

/*
 * Return all elements in user interface you have to disable or hide,
 * grouped by parent views.
 */

require_once '../../index.php';

$user = \Auth\UsersQuery::create()->findPk((int) $request->uid);
$elQuery = new \Auth\AuthElementQuery();
$elementsArray = $elQuery->getUserUnpermittedElements($user);

$uiPermission = array();
foreach ($elementsArray as $permission) {
	$uiPermission[$permission['control_interface']][$permission['name']] = $permission['state'];
}

//$uiPermission = $user->getUiPermissions();

echo Response::Create(array(
	'success'	 => true,
	'results'	 => $uiPermission
));
