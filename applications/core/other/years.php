<?php

/*
 * Load years
 */

require_once '../../../index.php';

$year = date("Y") + 1;

$startY = $year - 5;
$yearArr = $yearRes = array();


if (isset($request->get['school_year']) && $request->get['school_year'] == true) {
	$tp = new \Ccp\TassePeer();
	$dbName = $tp->getMastercomDbName();
	$arrDbName = explode('_', $dbName);

	try {
		$currentYear = $arrDbName[1] . '/' . $arrDbName[2];
		Propel::getConnection('mastercom_' . $arrDbName[1] . '_' . $arrDbName[2]);

		$yearRes[] = array(
			'year'		 => $currentYear,
			'current'	 => true
		);
	} catch (Exception $e) {
		// Do nothing
	}
	try {
		$prevYear = ($arrDbName[1] - 1) . '/' . ($arrDbName[2] - 1);
		Propel::getConnection('mastercom_' . ($arrDbName[1] - 1 ) . '_' . ($arrDbName[2] - 1));

		$yearRes[] = array(
			'year'		 => $prevYear,
			'current'	 => false
		);
	} catch (Exception $e) {
		// Do nothing
	}
} else if (isset($request->get['school_year_only']) && $request->get['school_year_only'] == true) {
	$tp = new \Ccp\TassePeer();
	$dbName = $tp->getMastercomDbName();
	$arrDbName = explode('_', $dbName);
	$yearRes = $arrDbName[1] . '/' . $arrDbName[2];
} else {
	for ($y = $startY; $y <= $year; $y++) {
		$yearArr['year'] = $y;
		$yearRes[] = $yearArr;
	}
}

echo Response::Create(array(
	'success'	 => true,
	'results'	 => $yearRes
));
