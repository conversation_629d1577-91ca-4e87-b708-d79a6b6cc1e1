<?php

/*
 * Load countries
 *
 */

require_once '../../../index.php';

$db = Db::getInstance();
$query = "
            SELECT
                country_id,
                code,
                description
            FROM countries WHERE code ILIKE 'Z%'
			UNION SELECT
			country_id,
                code,
                description
			FROM countries WHERE code = '0001' ORDER BY code,description
        "; // TODO => Delete the ugly ILIKE :@
$db->query($query);
$countries = $db->fetchAll();
echo Response::Create(array(
	'success'	 => true,
	'results'	 => $countries
));
?>
