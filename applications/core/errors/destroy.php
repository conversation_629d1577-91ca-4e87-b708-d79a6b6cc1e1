<?php

/*
 * Delete all data present in event_error table
 */

/**
 * Short description for destroy
 *
 * @category   Core
 * @package    Core\EventError
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * @license    http://www.mastertraining.it/license MTSRL License
 * @version    $Id:$
 * @link       http://www.mastertraining.it/products/PackageName
 * @since      File available since Release 1.0.4
 * <AUTHOR>
 */
use Core\EventErrorQuery;

require_once '../../../index.php';

EventErrorQuery::create()->find()->delete();

echo Response::Create(array(
	'success' => true
));