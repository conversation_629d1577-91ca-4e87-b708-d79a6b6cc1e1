<?php

/*
 * Destroy a \Auth\Users
 */

require_once '../../../index.php';

$uid = (int) $request->raw['user_id'];
$success = false;

$con = Propel::getConnection("mc2api");
$con->query("DELETE FROM session WHERE uid = " . $uid);

$user = Auth\UsersQuery::create()->findPk($uid);
if ($user != null) {
    // Cancellazione logica disabilitando
    $user->setEnabled(false);
    $user->save();
    $success = true;
}
echo Response::Create(array(
    'success' => $success
));
