<?php

/*
 * Create/Update single user
 */

require_once '../../../index.php';

use Auth\Users;

$uid = (int) $request->post['Uid'] > 0 ? (int) $request->post['Uid'] : -1;
$errors = array();


if ($uid < 0 && $request->post['UserPasswordR'] === $request->post['UserName']) {
    $errors = array(
        'UserPasswordR' => 'La password non può essere uguale allo username'
    );
    $success = false;
} else if ($uid < 0 && strlen($request->post['UserPasswordR']) < 8) {
    $errors = array(
        'UserPasswordR' => 'La Password deve essere composta da almeno 8 caratteri'
    );
    $success = false;
} else if ($request->post['UserPasswordR'] == $request->post['UserPassword']) {
    if ($uid > 0) {
        $user = Auth\UsersQuery::create()->findPk($uid);
    } else {
        unset($request->post['Uid']);
        $user = new Users();
    }
    $request->post['EmployeeId'] = (int) $request->post['employee_id'];
    $user->fromArray($request->post);

    if (!isset($request->post['Privelege']))
        $user->setPrivelege(0);

    if (!isset($request->post['Enabled']))
        $user->setEnabled(0);

    if ($uid < 0) {
        $pbkdf2 = MT\Utils\Pbkdf2::encode($request->post['UserPasswordR']);
        // $user->setUserPassword(md5($request->post['UserPasswordR']));
        $user->setUserPassword($pbkdf2);
    }

    if ($user->validate()) {
        try {
            $user->save();

            $update = [];
            if(isset($request->post['name'])) {
                $update[] = "name='{$request->post['name']}'";
            }
            if(isset($request->post['surname'])) {
                $update[] = "surname='{$request->post['surname']}'";
            }
            if(isset($request->post['fiscal_code'])) {
                $update[] = "fiscal_code='{$request->post['fiscal_code']}'";
            }
            if(!empty($update)) {
                $update = implode(', ', $update);
                $db = Db::getInstance();
                $query = "UPDATE users SET {$update} WHERE uid={$user->getUid()}";
                $db->query($query);
            }


            $success = true;
            $login = new \Core\Login($request->post['UserName'], $request->post['UserPasswordR']);
            if ($login->couchAuthenticationEnabled()) {
                if (!$login->isCouchUser()) {
                    $user = $login->authentication();
                    if (!$login->generateCouchUser($user, '')) {
                        $success = false;
                        $errors = ['Errore durante il salvataggio su couchdb'];
                    } else {
                        $success = true;
                    }
                } else {
                    $user = new \User($uid);
                    if (!$login->updateCouchUser($user)) {
                        $success = false;
                        $errors = ['Errore durante l\'aggiornamento su couchdb'];
                    } else {
                        $success = true;
                    }
                }
            }
        } catch (\Exception $e) {
            $success = false;
            $errors = [$e->getMessage()];
        }
    } else {
        $success = false;
        $errors = ['Errore durante la validazione dei dati'];
    }
} else {
    $errors = array(
        'UserPasswordR' => 'Password errata'
    );
    $success = false;
}

echo Response::Create(array(
    'success' => $success,
    'errors'  => $errors
));
