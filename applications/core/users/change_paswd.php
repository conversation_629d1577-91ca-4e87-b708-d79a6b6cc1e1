<?php

/*
 * Update User Password
 */

require_once '../../../index.php';
require_once '/var/www-source/mc2-api/module/CCP/src/CCP/Model/Mastercom.php';

$passwd = $request->passwd;
$passwdRepeat = $request->passwdRepeat;
$forceMc2 = (bool) $request->force_mc2;
$errors = array();

$user = new \User($request->user_id);
$sessionUser = new \User($_SESSION['uid']);

$msg = null;


if ($passwd !== $passwdRepeat) {
    $msg[] = 'Le due Password inserite risultano diverse';
}

if (
        md5($passwd) === $user->user_password ||
        (
        strpos($user->user_password, 'pbkdf2') !== false &&
        MT\Utils\Pbkdf2::isValid($passwd, $user->user_password)
        )
) {
    $msg[] = 'La Password non può essere uguale a quella attualmente in uso';
}

if ($passwd === $user->user_name) {
    $msg[] = 'La Password non può essere uguale al nome utente';
}

if (strlen($passwd) < 8) {
    $msg[] = 'La Password deve essere composta da almeno 8 caratteri';
}
$login = new \Core\Login($username, $password);
if($login->couchAuthenticationEnabled() && !$user->couch_id) {
    $msg[] = 'L\'utente non è abilitato all\'autenticazione su Couch';
}

if(!$sessionUser->isSuperUser() && !$sessionUser->isAdmin() && $request->user_id!=$_SESSION['uid']) {
    $msg[] = 'Non hai i permessi per modificare la password a questo utente. ';
} else {
    if($login->couchAuthenticationEnabled() && !$forceMc2) {
        $mcApi = new \CCP\Model\Mastercom();
        $mcApi->setToken($_SESSION['couch_tk']);

        if($request->user_id!=$_SESSION['uid']) {
            $tk = $mcApi->post("/login", [
                'uid' => $request->user_id,
                'type' => 'mc2'
            ], false);
            if (!$tk) {
                $msg = 'Errore durante la chiamata a Mastercom per l\'autenticazione dell\'utente. L\'utente potrebbe non essere abilitato all\'autenticazione su Couch';
                $errors = array(
                    'EditUserPaswd'           => $msg,
                    'LoginPasswordExpiredPwd' => $msg,
                );
                $success = false;
                echo Response::Create([
                    'success' => $success,
                    'errors'  => $errors
                ]);
                exit;
            }
            $mcApi->setToken($tk);

        }

        $res = $mcApi->post("/user/update_password", ['password'=>$passwd]);
        if(!$res || $res['success']!==true) {
            $msg[] = 'Errore durante la chiamata a Mastercom per l\'autenticazione dell\'utente';
        }
    }
}

if ($msg) {
    $msg = implode("<br />", $msg);
    $errors = array(
        'EditUserPaswd'           => $msg,
        'LoginPasswordExpiredPwd' => $msg,
    );
    $success = false;
} else {
    $user->setPassword($passwd);
    $user->setExpiration(strtotime("+1 year"));
    $user->save();
    $success = true;
}

echo Response::Create([
    'success' => $success,
    'errors'  => $errors
]);
