<?php

/*
 * Load users
 */

require_once '../../../index.php';

$db = Db::getInstance();
$query = "
            SELECT
                uid,
                user_type,
                user_name,
                user_password,
                enabled,
                expiration,
                employee_id,
                name,
                surname,
                fiscal_code,
                (SELECT group_name FROM groups WHERE gid = user_type ) AS user_type_str,
                privelege,
                email,
                modify_protocol,
                super_user
            FROM users WHERE super_user != 't' order by user_name
        ";
$db->query($query);
$users = $db->fetchAll();
echo Response::Create(array(
    'success' => true,
    'results' => $users
));
