<?php

/*
 * Checks if an area is active.
 */

require_once '../index.php';

// Generate a connection with db
$con = Propel::getConnection('mc2api');
$area = isset($request->post['area']) ? strtoupper($request->post['area']) : null;
$panel = null;
$title = '';

function checkAreaActivation($area, $panel) {
    if($area == 'AREA_HOME') {
        return $panel;
    }

    if ($area == null || $panel == null) {
        return false;
    }
    $active = \Core\ParameterQuery::create()->filterByName($area)->findOne();
    if (!$active) {
        return false;
    }
    return $active->getValue() == 't' ? $panel : false;
}

if ($area == 'AREA_HOME') {
    $panel = 'HomePnl';
    $title = 'Informazioni e Stampe';
} else if ($area == AREA_PERSONNEL) {
    $panel = 'EmployeePnl';
    $title = 'Personale';
} else if ($area == AREA_CCP) {
    $panel = 'CcpPnl';
    $title = 'Conti Correnti';
} else if ($area == AREA_WAREHOUSE) {
    $panel = 'WarehousePnl';
    $title = 'Magazzino e Inventario';
} else if ($area == AREA_INVENTORY) {
    $panel = 'InventoryPnl';
    $title = 'Magazzino e Inventario';
} else if ($area == AREA_PROTOCOL) {
    $panel = 'ProtocolPnl';
    $title = 'Protocollo Informatico';
} else if ($area == AREA_ARCHIVE) {
    $panel = 'ArchivePnl';
    $title = 'Archivio Documenti';
} else if ($area == AREA_ALBO) {
    $panel = 'AlboPnl';
    $title = 'Albo Pretorio';
} else if ($area == AREA_TRASPARENZA) {
    $panel = 'TrasparenzaPnl';
    $title = 'Trasparenza Amministrativa';
} else if ($area == AREA_SETTINGS) {
    $panel = 'SettingsPanel';
    $title = 'Impostazioni';
} else if ($area == AREA_MAIL_ACCOUNT) {
    $panel = 'MailAccountPnl';
    $title = 'Account di posta';
} else {
    $area = null;
}

$success = checkAreaActivation($area, $panel);

$data = array(
    'success' => $success,
    'title'   => $title,
    'message' => !$success ? _('Area still not active') : ''
);

echo Response::Create($data);
