<?php

/**
 * Print all data grouped by category.
 * It show category with total befor, during (variation) and at the end of the budget year.
 *
 * <AUTHOR>
 */
class PrintVariationClasses extends PrintStandardTemplate {

	const file_name = 'variazioni_classi';
	const print_title = 'CONSISTENZA E VARIAZIONI PATRIMONIALI DEGLI ISTITUTI SCOLASTICI';

	public $page_orientation = 'L';
	private $year;

	/**
	 * Set base data and objects.
	 * Than print title
	 *
	 */
	public function __construct() {
		$budget = new Budget();
		$this->year = $budget->getYear();
		parent::__construct(self::file_name);
		$this->__writePrintTitle(self::print_title . ' - Anno: ' . $this->year);
	}

	/**
	 * Create table header
	 */
	private function _setTableHeader() {
		$this->SetFont('Arial', 'B', 8);
		$this->Cell(60, 15, 'CATEGORIE', 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, 'CONSISTENZA AL 1° GENNAIO', 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, 'VAR. IN AUMENTO', 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, 'VAR. IN DIMINUZIONE', 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, 'VAR. RIVALUTATA', 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, 'CONSISTENZA AL 31 DICEMBRE', 1, 1, 'C', '', '', 1);
		$this->SetFont('Arial', '', 7);
	}

	/**
	 * Create table content row
	 */
	private function _setTableContent($row) {
		$this->Cell(60, 15, $row['title'], 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['consistence_start']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['var_more']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['var_less']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['var_rev']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['consistence_end']), 1, 1, 'C', '', '', 1);
	}

	/**
	 * Create table total content row
	 */
	private function _setTableTotal($row) {
		$this->SetFont('Arial', 'B', 8);
		$this->Cell(60, 15, $row['title'], 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['consistence_start']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['var_more']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['var_less']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['var_rev']), 1, 0, 'C', '', '', 1);
		$this->Cell(40, 15, money_format('%.2n', $row['consistence_end']), 1, 1, 'C', '', '', 1);
	}

	/**
	 * Prepare data to populate print table
	 *
	 * @return array inventory data
	 */
	public function _getData() {
		$inventory = new Inventory;
		$items = $inventory->getItems(false);

		$date_year_start = strtotime("1 January " . $this->year);
		$date_next_year_start = strtotime("1 January " . ($this->year + 1));

		$data = array(
			1	 => array(),
			2	 => array(),
			3	 => array(),
			4	 => array(),
			6	 => array(),
		);

		foreach ($data as $key => $field) {
			$data[$key]['var_less'] = 0;
			$data[$key]['consistence_start'] = 0;
			$data[$key]['var_rev'] = 0;
			$data[$key]['var_more'] = 0;
		}

		foreach ($items as $item) {

			switch ($item['class']) {
				case 'First class':
					$class = 1;
					$title = 'CATEGORIA I';
					break;
				case 'Second class':
				case '':
					$class = 2;
					$title = 'CATEGORIA II';
					break;
				case 'Third class':
					$class = 3;
					$title = 'CATEGORIA III';
					break;
				case 'Fourth class':
					$class = 4;
					$title = 'CATEGORIA IV';
					break;
				case 'Sixth class':
					$class = 6;
					$title = 'CATEGORIA VI';
					break;
			}


			$total = round($item['value'] * $item['quantity'], 2);
			$total_initial = round($item['initial_value'] * $item['quantity'], 2);
			if ($item['charge_date'] < $date_next_year_start) {
				if ($item['charge_date'] < $date_year_start) {
					$data[$class]['consistence_start'] += ($class == 1 or $class == 3) ? $total_initial : $total;
				} else {
					if (
						$item['inventory'] 	= 't' and
						$item['discharged'] < 1 and
						$item['inventory_kind'] != 3 and
						$item['kind'] != 241
					) {
						$data[$class]['var_rev'] += abs($total - $total_initial);
					}
					$data[$class]['var_more'] += $total_initial;
				}
				if ($item['discharged'] > 0) {
					if ($item['discharge_date'] < $date_year_start) {
						$data[$class]['consistence_start'] -= $total;
					} else {
						if ($item['discharge_date'] < $date_next_year_start)
						$data[$class]['var_less'] += $total;
					}
				}
			}


			$data[$class]['title'] = $title;
			$data[$class]['consistence_end'] = 0;
			// $data[$class]['consistence_end'] = $data[$class]['consistence_start'] + $data[$class]['var_more'] - $data[$class]['var_less'] - $data[$class]['var_rev'] ;
		}

		/*$data[1]['consistence_start'] = 124213.94;
		$data[2]['consistence_start'] = 30148.85;
		$data[3]['consistence_start'] = 168119.39;
		$data[6]['consistence_start'] = 60496.36;
		$data[2]['var_more'] = 240;*/
		foreach ($data as $key => $field) {
			$data[$key]['consistence_end'] = $data[$key]['consistence_start'] + $data[$key]['var_more'] - $data[$key]['var_less'] - $data[$key]['var_rev'] ;
		}

		return $data;
	}

	/**
	 * Generate print
	 */
	public function createPdf() {
		$data = $this->_getData();
		$this->_setTableHeader();
		$total = array();

		foreach ($data as $row) {
			if ($row['title']) {
				$this->_setTableContent($row);
				$total['consistence_start'] += $row['consistence_start'];
				$total['var_more'] += $row['var_more'];
				$total['var_less'] += $row['var_less'];
				$total['var_rev'] += $row['var_rev'];
				$total['consistence_end'] += $row['consistence_end'];
			}
		}

		$total['title'] = 'TOTALI';
		$this->_setTableTotal($total);

		return $this->native2pdf();
	}

	public function as_view() {
		$file_url = $this->createPdf();
		if ($file_url) {
			$data = array(
				"success"	 => true,
				"urlPath"	 => $file_url
			);
		} else {
			$data = array(
				"success" => false
			);
		}
		echo Response::Create($data);
	}

}
