<?php

/**
 * General management for Inventory
 *
 * <AUTHOR>
 */
class Inventory {

	private $_year;
	private $_query = "";
	private $_where_period = "";
	private $_start_date;
	private $_end_date;
	private $_itemArr = array();
	public $table_name = 'wh_item';

	/**
	 * Prepare string. If year passed, set start/end date to
	 * begin/end year timestamp
	 *
	 * @param integer $year
	 * @return string
	 */
	public function __construct($year = -1) {
		$this->db = Db::getInstance();
		$this->_query = "SELECT * FROM wh_item left join wh_item_kind ON wh_item.kind=wh_item_kind.id ";
		if ($year > 0) {
			$this->_year = $year;
			$this->_start_date = strtotime("1 January " . $this->_year);
			$this->_end_date = strtotime("1 January " . ($this->_year + 1));
			$this->_where_period = " AND charge_date >= " . $this->_start_date . "
                               AND charge_date < " . $this->_end_date;
		}
	}

	/**
	 * @return array of Item objects
	 */
	public function getItems($object = true) {
		$this->db->query($this->_query . $this->_where_period);
		$items = $this->db->fetchAll();

		if ($object) {
			foreach ($items as $value) {
				$item = new Item($value['item_id']);
				$this->_itemArr[] = $item;
			}
		} else {
			$this->_itemArr = $items;
		}

		return $this->_itemArr;
	}

}

?>
