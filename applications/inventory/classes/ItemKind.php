<?php

/**
 * Abstract class to define Items that could be Book, Inventory items, Facile consumo etc..
 *
 * <AUTHOR>
 */
class ItemKind extends DbObject
{

    protected $_tableN = 'wh_item_kind';
    protected $_idF = 'id';

    public function getMuDisplay()
    {
	$this->db->query("SELECT * FROM measure_units WHERE id = " . (int) $this->mu);
	$mu = $this->db->fetchAll();
	return $mu[0]['description'];
    }

    public function isInventariable()
    {
	return $this->inventory == 'f' ? false : true;
    }

    public function getClassNumber()
    {
	switch ($this->class)
	{
	    case 'First class':
		$class_number = 1;
		break;
	    case 'Second class':
		$class_number = 2;
		break;
	    case 'Third class':
		$class_number = 3;
		break;
	    case 'Fourth class':
		$class_number = 4;
		break;
	    case 'Sixth class':
		$class_number = 6;
		break;
	}
	return $class_number;
    }

}

?>
