<?php

/**
 * Class could be extended by Book, Inventory items, Consumable matiriala etc..
 *
 * <AUTHOR>
 */
class Item extends DbObject {

	protected $_tableN = 'wh_item';
	protected $_idF = 'item_id';

	public function __construct($id = -1) {
		parent::__construct($id);
		if ($id > 0) {
			$this->responsable = new WhResponsable($this->respons);
			$this->item_kind = new ItemKind($this->kind);
		}
	}

	public function getOrderRow() {
		return new WhOrderRow((int) $this->order_id);
	}

	public function getOrder() {
		$order_row = $this->getOrderRow();
		return new WhOrder((int) $order_row->header_id);
	}

	public function getResponsableName() {
		return $this->responsable->surname . ' ' . $this->responsable->name;
	}

	public function getLocationName() {
		return $this->responsable->location;
	}

	public function isDischarged() {
		if ($this->discharged > 0)
			return true;
		else
			return false;
	}

	/**
	 *
	 * @param integer $year
	 * @return boolean
	 *
	 */
	public function inPrevYearDischarged($year = -1) {
		if ($year < 0) {
			$budget = new Budget();
			$year = $budget->getYear();
		}
		$date = strtotime("1 January " . $year);
		if ($this->discharge_date < $date)
			return true;
		else
			return false;
	}

	public function inNextYearCharged($year = -1) {
		if ($year < 0) {
			$budget = new Budget();
			$year = $budget->getYear();
		}
		$date = strtotime("1 January " . ($year + 1));
		if ($this->charge_date >= $date)
			return true;
		else
			return false;
	}

	public function inPrevYearCharged($year = -1) {
		if ($year < 0) {
			$budget = new Budget();
			$year = $budget->getYear();
		}
		$date = strtotime("1 January " . $year);
		if ($this->charge_date < $date)
			return true;
		else
			return false;
	}

	public function totalValue() {
		return round($this->value * $this->quantity, 2);
	}

}
