<?php

/**
 * Main class to calculate day totals
 *
 * <AUTHOR>
 *
 */
class ExtRecDay {

	protected $_employee = null;
	protected $_day = null;

	/**
	 * ExtRecDay constructor
	 * @param Object $emp Employee
	 * @param integer $date Timestamp
	 */
	public function __construct($emp, $day) {
		$this->_day = strtotime(date("d-m-Y", $day));
		$this->_employee = $emp;
	}

	/**
	 * Checks if the record exists.
	 *
	 * @return type the record or null if not found
	 */
	public function checkStoredDayExists($emp_id, $day) {
		$storedDay = \Employee\StoredDayQuery::create()
				->filterByEmployeeId($emp_id)
				->filterByDate($day)
				->findOne();
		if (!$storedDay) {
			return null;
		} else {
			return $storedDay;
		}
	}

	/**
	 * Retrieves the presences of the day.
	 *
	 * @return \Presence the Presences array
	 */
	public function getPresences($filtered = true, $toArray = false) {
		// Takes all the presences within this day
		$presences = \Employee\PresenceQuery::create()
				->filterByDateEdit(array('min' => strtotime(date("d-m-Y", $this->_day)), 'max' => strtotime(date("d-m-Y", $this->_day) . " +1 day")))
				->filterByEmployeeId($this->_employee->getEmployeeId())
				->orderByDateEdit()
				->find();

		$presencesToGet = array();
		foreach ($presences as $key => $presence) {
			$presencesToGet[$key] = $presence;
		}
		if ($filtered) {
			$presencesToGet = $this->_filterPresences($presencesToGet);
		}
		foreach (array_keys($presencesToGet) as $key) {
			$presencesToGet[$key]->roundSeconds();
		}
		if ($toArray) {
			foreach ($presencesToGet as $key => $presence) {
				$presencesToGet[$key] = $presence->toArray(BasePeer::TYPE_FIELDNAME);
			}
		}
		return $presencesToGet;
	}

	/**
	 * Retrieves the presence corresponding to the given parameters.
	 *
	 * @param integer $timestamp the reference datetime to search against
	 * @param integer $offset searches for the Nth Presence; 0 - equals to the timestamp, negative - before the timestamp, positive - after the timestamp
	 * @param integer $kind Presence I/O
	 * @param integer $type Presence N/L/S
	 * @param string $mode Presence T/M
	 * @param boolean $recursive true to continue search on the previous/next days, false to limit the search on the datetime day
	 *
	 * @return \Presence The retrieved presence, or null if nothing was found
	 */
	public function getPresence($timestamp, $offset = 0, $kind = null, $type = null, $mode = null, $recursive = false) {
		// Takes all the presences within this day
		$presenceQ = new \Employee\PresenceQuery();
		$presenceQ->filterByEmployeeId($this->_employee->getEmployeeId());

		if ($mode) {
			$presenceQ->filterByInsertionMode($mode);
		}
		if ($type) {
			$presenceQ->filterByTypeEdit($type);
		}
		if ($kind) {
			$presenceQ->filterByOriginalInOutEdit($kind);
		}
		// After the timestamp
		if ($offset > 0) {
			if (!$recursive) {
				$presenceQ->filterByDateEdit(array('min' => $timestamp, 'max' => strtotime(date("d-m-Y", $timestamp) . " +1 day")));
			} else {
				$presenceQ->filterByDateEdit($timestamp, Criteria::GREATER_EQUAL);
			}
			$presenceQ->orderByDateEdit();
			$presenceQ->limit($offset);
			// Before the timestamp
		} elseif ($offset < 0) {
			if (!$recursive) {
				$presenceQ->filterByDateEdit(array('min' => strtotime(date("d-m-Y", $timestamp)), 'max' => $timestamp));
			} else {
				$presenceQ->filterByDateEdit($timestamp, Criteria::LESS_EQUAL);
			}
			$presenceQ->orderByDateEdit(Criteria::DESC);
			$presenceQ->limit(abs($offset));
			// On the timestamp
		} else {
			$presenceQ->filterByDateEdit($timestamp);
			$presenceQ->limit(1);
		}

		$presences = $presenceQ->find();

		if (count($presences) < 1 || count($presences) < abs($offset)) {
			return null;
		} else {
			return $presences[$offset == 0 ? 1 : abs($offset) - 1];
		}
	}

	/**
	 * Retrieves all the timetables of the day.
	 *
	 * @param bool $filtered if the timetables list should be filtered (no timetables for holiday or daily absence)
	 * @return \TimeTable the timetables array.
	 */
	public function getTimetables($filtered = true, $toArray = false) {
		// Takes all the timetables with end date within the day
		$timetables = \Employee\TimetableQuery::create()
				->filterByDateEnd(array('min' => strtotime(date("d-m-Y", $this->_day)), 'max' => strtotime(date("d-m-Y", $this->_day) . " +1 day")))
				->filterByEmployeeId($this->_employee->getEmployeeId())
				->orderByDateStart()
				->find();
		$timetablesToGet = array();
		foreach ($timetables as $key => $timetable) {
			$timetablesToGet[$key] = $timetable;
		}
		if ($filtered) {
			$timetablesToGet = $this->_filterTimetables($timetablesToGet);
		}
		if ($toArray) {
			foreach ($timetablesToGet as $key => $timetable) {
				$timetablesToGet[$key] = $timetable->toArray(BasePeer::TYPE_FIELDNAME);
			}
		}
		return $timetablesToGet;
	}

	/**
	 * Retrieves all the absences of the day.
	 *
	 * @return \Absence the absences array
	 */
	public function getAbsences($toArray = false) {
		// Takes all the absences within the day
		$absences = \Employee\AbsencesQuery::create()
				->filterByEndDate(strtotime(date("d-m-Y", $this->_day)), Criteria::GREATER_EQUAL)
				->filterByStartDate(strtotime(date("d-m-Y", $this->_day) . " +1 day"), Criteria::LESS_THAN)
				->filterByEmployeeId($this->_employee->getEmployeeId())
				->orderByStartDate()
				->find();
		$absencesToGet = array();
		if ($toArray) {
			$absencesToGet = $absences->toArray(null, false, BasePeer::TYPE_FIELDNAME);
		} else {
			foreach ($absences as $key => $absence) {
				$absencesToGet[$key] = $absence;
			}
		}
		return $absencesToGet;
	}

	/**
	 * Calculates the worked time in the day.
	 *
	 * Calculates the amount of time for total, normal, lunch, service and
	 * out intervals.
	 *
	 * @return array The worked time array; all values are in seconds.
	 */
	public function calcWorkedTime() {
		$arrDeltas = array(
			'total'		 => 0,
			'normal'	 => 0,
			'lunch'		 => 0,
			'service'	 => 0,
			'out'		 => 0
		);
		$presences = $this->getPresences();
		// Counts Normal, Lunch and Service elapsed times
		foreach ($presences as $key => $presence) {
			if (($key + 1) >= count($presences)) {
				break;
			}
			$elapsedTimeBetweenPresences = ($presences[$key + 1]->getDateEdit() - $presence->getDateEdit()) / 60;
			$arrDeltas['total'] += $elapsedTimeBetweenPresences;
			if ($presence->isEntrance()) {
				$arrDeltas['normal'] += $elapsedTimeBetweenPresences;
			} else if ($presence->isLunch()) {
				$arrDeltas['lunch'] += $elapsedTimeBetweenPresences;
			} else if ($presence->isService()) {
				$arrDeltas['service'] += $elapsedTimeBetweenPresences;
			} else {
				$arrDeltas['out'] += $elapsedTimeBetweenPresences;
			}
		}
		return $arrDeltas;
	}

	/**
	 * Calculates the contract worktime of the day.
	 *
	 * @param bool $filtered if the timetables list should contain only filtered timetables (not within a holiday or a daily absence) or all of them
	 * @return integer The contract worktime, in seconds
	 */
	public function calcContractTime($filtered = true) {
		$contractTime = 0;
		$timetables = $this->getTimetables($filtered);
		foreach ($timetables as $timetable) {
			$contractTime += $timetable->workDuration();
		}
		return $contractTime;
	}

	/**
	 * Calculates the pause done for recovering.
	 *
	 * @return int the calculated pause, in seconds
	 */
	public function calcRecoverDetractions() {
		$absPause = 0;
		$absences = $this->getAbsences();
		foreach ($absences as $absence) {
			$absKind = $absence->getAbsencesAbsenceKind();
			$absStack = $absKind->getAbsenceKindAbsenceStack();
			if ($absStack !== null) {
				if ($absStack->getRecover()) {
					$duration = $absence->getDuration();
					$absPause += $duration['time'];
				}
			}
		}
		return $absPause;
	}

	/**
	 * Calculates the total for the day.
	 *
	 * @return int the total for the day, in seconds
	 */
	public function calcDay() {
		$thisDay = $this->checkStoredDayExists($this->_employee->getEmployeeId(), $this->_day);
		if ($thisDay !== null) {
			return array(
				'authorized'	 => $thisDay->getAuthorized(),
				'extraordinary'	 => $thisDay->getExtraordinary());
		}

		// Binds presences to timetables
		$binds = $this->_bindPresencesToTimetables();

		// Calculates the total worked time
		$workedDay = $this->calcWorkedTime();

		// Calculates the total contract working time
		$contractWorkingDay = $this->calcContractTime();

		// Calculates the absences detraction
		$absDetraction = $this->_calcAbsencesDetractions();

		// Calculates the gross overtime
		$grossOvertime = $workedDay['normal'] + $workedDay['service'] - $contractWorkingDay;

		// Calculates the delta
		$deltaDay = $grossOvertime;
		if (isset($binds['binds'])) {
			if (count($binds['binds']) > 0) {
				$deltaDay = 0;
				foreach ($binds['binds'] as $bind) {
					$deltaDay += $this->_calcDeltaTimetable($bind);
				}
				foreach ($binds['free'] as $bind) {
					if (!$bind->isService() && !$bind->isLunch()) {
						$deltaDay = $bind->isExit() ? ($deltaDay + ($bind->getDateEdit() / 60) ) : ($deltaDay - ($bind->getDateEdit() / 60));
					}
				}
			}
		}

		// Calculates the effective overtime
		$netOvertime = $grossOvertime - ($grossOvertime - $deltaDay) + $absDetraction;

		// Filters parameters total day
		if ($netOvertime > 0) {
			$netOvertime = $this->_filterMin($netOvertime, $this->_employee->getMinExtraordinaryTotal());
			$netOvertime = $this->_filterMax($netOvertime, $this->_employee->getMaxExtraordinaryTotal());
			$netOvertime = $this->_filterSteps($netOvertime, $this->_employee->getStepTotalExtraordinary());
		} else {
			$netOvertime = $this->_filterMin($netOvertime, $this->_employee->getMinUndefinedTotal());
			$netOvertime = $this->_filterMax($netOvertime, $this->_employee->getMaxUndefinedTotal());
			$netOvertime = $this->_filterSteps($netOvertime, $this->_employee->getStepTotalUndefined());
		}

		// Calc delta pausa pranzo rispetto a pausa da timetable (con parametro T/F?)
		if ($workedDay['lunch'] == 0) {
			$netOvertime = $this->_filterContinuedWork($workedDay['normal'], $netOvertime, $this->_employee->getMaxContWork(), $this->_employee->getBreakAfterMaxWork());
		}

		// Filters parameter %/min
		$netOvertime = $this->_filterLimitForRetribution($netOvertime, $this->_employee->getRecoverHours(), $this->_employee->getUnitRecoverHours());

		$netOvertime = round($netOvertime);

		return array(
			'authorized'	 => $netOvertime,
			'extraordinary'	 => $netOvertime);
	}

	/**
	 * FARE FUNZIONE PER RITROVAMENTO ULTIMO BIP PRIMA / PRIMO BIP DOPO DI UNA DATA.
	 */

	/**
	 * Calculates the deltas (start, end, start pause, end pause) for a bind.
	 *
	 * @param array $bind the bind to calculate the deltas on
	 * @return array the updated bind with deltas, in seconds
	 */
	private function _calcDeltaTimetable($bind) {
		// If there is only timetable without presences, all must be put in negative
		if($bind['start']['t'] > 0 && !$bind['start']['p']) {
			$deltaStart = $bind['start']['t'] / 60;
		} else {
			// Delta Start
			$deltaStart = ($bind['start']['t'] - $bind['start']['p'] ) / 60;
			if ($deltaStart > 0) {
				$deltaStart = $this->_filterMin($deltaStart, $this->_employee->getMinExtraordinaryIn());
				$deltaStart = $this->_filterMax($deltaStart, $this->_employee->getMaxExtraordinaryIn());
				$deltaStart = $this->_filterSteps($deltaStart, $this->_employee->getStepIn());
			} else {
				$deltaStart = $this->_filterMin($deltaStart, $this->_employee->getMinUndefinedIn());
				$deltaStart = $this->_filterMax($deltaStart, $this->_employee->getMaxUndefinedIn());
				$deltaStart = $this->_filterSteps($deltaStart, $this->_employee->getStepInUnd());
			}
		}

		// Delta End
		if($bind['end']['t'] > 0 && !$bind['end']['p']) {
			$deltaEnd = $bind['end']['t']*-1 / 60;
		} else {
			$deltaEnd = ($bind['end']['p'] - $bind['end']['t']) / 60;
			if ($deltaEnd > 0) {
				$deltaEnd = $this->_filterMin($deltaEnd, $this->_employee->getMinExtraordinaryOut());
				$deltaEnd = $this->_filterMax($deltaEnd, $this->_employee->getMaxExtraordinaryOut());
				$deltaEnd = $this->_filterSteps($deltaEnd, $this->_employee->getStepOut());
			} else {
				$deltaEnd = $this->_filterMin($deltaEnd, $this->_employee->getMinUndefinedOut());
				$deltaEnd = $this->_filterMax($deltaEnd, $this->_employee->getMaxUndefinedOut());
				$deltaEnd = $this->_filterSteps($deltaEnd, $this->_employee->getStepOutUnd());
			}
		}

		// Delta Start Pause
		$deltaPauseStart = ($bind['start_pause']['p'] - $bind['start_pause']['t']) / 60;
		// Delta End Pause
		$deltaPauseEnd = ($bind['end_pause']['t'] - $bind['end_pause']['p']) / 60;

		return $deltaStart + $deltaEnd + $deltaPauseStart + $deltaPauseEnd;
	}


	/**
	 * Calculates the pause due to the absences of the day to substract to the worked time.
	 *
	 * @return int the calculated pause, in seconds
	 */
	private function _calcAbsencesDetractions() {
		$absPause = 0;
		$absences = $this->getAbsences();
		foreach ($absences as $absence) {
			$absKind = $absence->getAbsencesAbsenceKind();
			$absStack = $absKind->getAbsenceKindAbsenceStack();
			if ($absStack !== null) {
				if (!$absStack->isDaily() && $absence->isCountableOnDay($this->_day)) {
					$duration = $absence->getDuration();
					$absPause += $duration['time'];
				}
			}
		}
		return $absPause;
	}

	/**
	 * Binds the presences to the timetables.
	 *
	 * @return array list of binds (start, end, p start, p end) and free presences
	 */
	private function _bindPresencesToTimetables() {
		$binds = array(
			'free'	 => array(),
			'binds'	 => array()
		);
		$presences = $this->getPresences();
		if (empty($presences)) {
			return array();
		}
		$timetables = $this->getTimetables(true);
		foreach ($timetables as $timetable) {
			// Gets Start
			$p = $this->_searchNearestPresence($timetable->getDateStart(), PRESENCE_ENTRANCE, array(PRESENCE_NORMAL), null, null, $presences);
			$binds['binds'][$timetable->getPersonnelTimetableId()]['start'] = array(
				't'	 => $timetable->getDateStart(),
				'p'	 => $p !== null ? $p->getDateEdit() : null);
			$key = null;
			foreach ($presences as $k => $presence) {
				if ($binds['binds'][$timetable->getPersonnelTimetableId()]['start']['p'] == $presence->getDateEdit()) {
					$key = $k;
					break;
				}
			}
			if ($key !== null) {
				unset($presences[$key]);
			}
			// Gets End
			$p = $this->_searchNearestPresence($timetable->getDateEnd(), PRESENCE_EXIT, array(PRESENCE_NORMAL), null, null, $presences);
			$binds['binds'][$timetable->getPersonnelTimetableId()]['end'] = array(
				't'	 => $timetable->getDateEnd(),
				'p'	 => $p !== null ? $p->getDateEdit() : null);
			$key = null;
			foreach ($presences as $k => $presence) {
				if ($binds['binds'][$timetable->getPersonnelTimetableId()]['end']['p'] == $presence->getDateEdit()) {
					$key = $k;
					break;
				}
			}
			if ($key !== null) {
				unset($presences[$key]);
			}
			// Gets Start Pause
			$p = $this->_searchNearestPresence($timetable->getDateStartPause(), PRESENCE_EXIT, array(PRESENCE_NORMAL, PRESENCE_LUNCH), $timetable->getDateStart(), $timetable->getDateEnd(), $presences);
			$binds['binds'][$timetable->getPersonnelTimetableId()]['start_pause'] = array(
				't'	 => $timetable->getDateStartPause(),
				'p'	 => $p !== null ? $p->getDateEdit() : null);
			$key = null;
			foreach ($presences as $k => $presence) {
				if ($binds['binds'][$timetable->getPersonnelTimetableId()]['start_pause']['p'] == $presence->getDateEdit()) {
					$key = $k;
					break;
				}
			}
			if ($key !== null) {
				unset($presences[$key]);
			}
			// Gets End Pause
			$p = $this->_searchNearestPresence($timetable->getDateEndPause(), PRESENCE_ENTRANCE, array(PRESENCE_NORMAL, PRESENCE_LUNCH), $timetable->getDateStart(), $timetable->getDateEnd(), $presences);
			$binds['binds'][$timetable->getPersonnelTimetableId()]['end_pause'] = array(
				't'	 => $timetable->getDateEndPause(),
				'p'	 => $p !== null ? $p->getDateEdit() : null);
			$key = null;
			foreach ($presences as $k => $presence) {
				if ($binds['binds'][$timetable->getPersonnelTimetableId()]['end_pause']['p'] == $presence->getDateEdit()) {
					$key = $k;
					break;
				}
			}
			if ($key !== null) {
				unset($presences[$key]);
			}
		}
		$binds['free'] = $presences;
		return $binds;
	}

	/**
	 * Searches for the nearest bip of the given type and kind in the interval against the reference.
	 *
	 * @param type $reference the reference timestamp to search against
	 * @param type $intervalStart start timestamp for the search
	 * @param type $intervalEnd end timestamp for the search
	 * @param type $BipType bip type ('I', 'O')
	 * @param type $BipKind bip kind ('N', 'P', 'S')
	 *
	 * @return bip the bip if found, null otherwise
	 */
	private function _searchNearestPresence($reference, $bipType, $bipKind, $intervalStart, $intervalEnd, $presences) {
		$delta = -1;
		$presenceToReturn = null;
		foreach ($presences as $presence) {
			if (in_array($presence->getTypeEdit(), $bipKind) && $presence->getOriginalInOutEdit() == $bipType) {
				if ($intervalEnd == null || $intervalStart == null) {
					$delta_pres = abs($reference - $presence->getDateEdit());
					if ($delta == -1 || $delta > $delta_pres) {
						$delta = $delta_pres;
						$presenceToReturn = $presence;
					}
				} else {
					if ($presence->getDateEdit() >= $intervalStart && $presence->getDateEdit() <= $intervalEnd) {
						$delta_pres = abs($reference - $presence->getDateEdit());
						if ($delta == -1 || $delta > $delta_pres) {
							$delta = $delta_pres;
							$presenceToReturn = $presence;
						}
					}
				}
			}
		}
		return $presenceToReturn;
	}

	/**
	 * Filters the delta against the given minimum threshold.
	 *
	 * @param int $delta the delta to filter, in seconds
	 * @param int $min the min threshold, in seconds
	 * @param boolean $effective if the filtered delta has to be returned complete or only the over-the-minimum amount
	 * @return int the filtered delta, in seconds
	 */
	private function _filterMin($delta, $min, $effective = false) {
		if ($effective) {
			$over = abs($delta) - $min;
			return abs($delta) < $min ? 0 : $delta < 0 ? -$over : $over;
		}
		return abs($delta) < $min ? 0 : $delta;
	}

	/**
	 * Filters the delta against the given maximum threshold.
	 *
	 * @param int $delta the delta to filter, in seconds
	 * @param int $max the max threshold, in seconds
	 * @return int the filtered delta, in seconds
	 */
	private function _filterMax($delta, $max) {
		return $delta >= 0 ? min($delta, $max) : max($delta, -$max);
	}

	/**
	 * Filters the delta and rounds it to a multiple of the given step.
	 *
	 * @param int $delta the delta to filter, in seconds
	 * @param int $step the step, in seconds
	 * @return int the filtered delta, in seconds
	 */
	private function _filterSteps($delta, $step) {
		if ($step > 0) {
			$steps = (int) ($delta / $step);
			$delta = $steps * $step;
		}
		return $delta;
	}

	/**
	 * Filters the worked time without lunch pause against a threshold after which substract the given pause.
	 *
	 * @param int $contractWorkingDay the worked time to filter
	 * @param int $netOvertime the overtime done
	 * @param int $threshold the threshold after which to start the substraction, in seconds
	 * @param int $pause the pause to substract to the worked time, in seconds
	 * @return array the filtered array, in seconds
	 */
	private function _filterContinuedWork($contractWorkingDay, $netOvertime, $threshold, $pause) {
		if ($contractWorkingDay > $threshold) {
			return max(0, $netOvertime - $pause);
		}
		return $netOvertime;
	}

	/**
	 * Filters the extraordinary to cut out the amount that has to be paid.
	 *
	 * @param int $extraordinary the extraordinary to filter, in seconds.
	 * @param int $value the value to filter against, in second.
	 * @param int $unit the unit of the value (could be EMPLOYEE_PARAM_RETRIBUTION_UNIT_PERC or EMPLOYEE_PARAM_RETRIBUTION_UNIT_MINS)
	 * @param int $threshold the threshold from which to start to filter, in seconds.
	 * @return int the filtered extraordinary, in seconds.
	 */
	private function _filterLimitForRetribution($extraordinary, $value, $unit, $threshold = 0) {
		if ($extraordinary <= 0) {
			return $extraordinary;
		}

		$over = 0;
		if ($extraordinary > $threshold) {
			$over = $extraordinary - $threshold;
		}

		if ($unit == EMPLOYEE_PARAM_RETRIBUTION_UNIT_PERC) {
			$perc = ($over / 100) * $value;
			return $threshold + $perc;
		} else {
			if ($over > $value) {
				return $threshold + $value;
			}
			return $threshold + $over;
		}
	}

	/**
	 * Filters the timetables list removing timetables that are within a holiday
	 * day or a daily absence and are not referring to a previous night shift.
	 *
	 * @param array $timetables the raw timetables array
	 * @return array the updated timetables array
	 */
	private function _filterTimetables($timetables) {
		$cal = new Calendar();
		$cal->setDate(date('d-m-Y', $this->_day));
		$absDaily = false;
		$absences = $this->getAbsences();
		foreach ($absences as $absence) {
			$absKind = $absence->getAbsencesAbsenceKind();
			$absStack = $absKind->getAbsenceKindAbsenceStack();
			if ($absStack != null && $absStack->isDaily()) {
				$absDaily = true;
				break;
			}
		}
		if ($cal->isHoliday() || $absDaily) {
			foreach ($timetables as $key => $timetable) {
				// Only keeps night timetables
				if ($timetable->getDateStart() >= $this->_day) {
					unset($timetables[$key]);
				}
			}
		}
		return $timetables;
	}

	/**
	 * Filters Presences array to take into account of the night shifts.
	 *
	 * @param array $presences the unfiltered Presences array
	 * @return array the filtered presences array
	 */
	private function _filterPresences($presences) {
		// Removes the last presence if it's an entrance
		if (!empty($presences)) {
			if ($presences[count($presences) - 1]->isEntrance()) {
				array_pop($presences);
			}
		}
		// Retrieves the last presence of the previous day if the first presence of the current day
		// is an Exit.
		if (!empty($presences)) {
			if ($presences[0]->isExit()) {
				// Takes the last presence of the previous day
				$last_presence = \Employee\PresenceQuery::create()
						->filterByDateEdit(array('min' => strtotime(date("d-m-Y", $this->_day) . "-1 day"), 'max' => ($this->_day - 1)))
						->filterByEmployeeId($this->_employee->getEmployeeId())
						->orderByDateEdit(Criteria::DESC)
						->findOne();
				// If there are none, it deletes the first presence of the day
				if (!$last_presence) {
					array_shift($presences);
				} else {
					array_unshift($presences, $last_presence);
				}
			}
		}
		return $presences;
	}

}
