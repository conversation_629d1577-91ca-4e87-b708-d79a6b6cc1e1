<?php

/**
 * Prints the last locked month for all employees.
 *
 * <AUTHOR>
 */
class PrintPDFLastLockedMonth extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_employees;
	private $_path;

	public function __construct($request) {
		$this->_employees = json_decode($request['employees']);
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$this->SetTitle($this->_path);
		$this->setPageOrientation('P');
		$this->SetMargins(5, 30);

		$this->addPage();
		// Title
		$this->setFont('', 'B');
		$this->SetFontSize(12);
		$this->Cell(0, 5, 'Situazione chiusure mensili', 0, 1, 'L', false, '', 1);

		// Table header
		$this->SetFontSize(9);
		$this->Cell(120, 5, 'Personale', 1, 0, 'C');
		$this->Cell(60, 5, 'Ultimo mese chiuso', 1, 1, 'C');
		$this->Cell(0, 5, '', 0, 1, 'C');

		$this->setFont('', '');

		// For every employee
		$oddStyle = true;
		foreach ($this->_employees as $employeeId) {
			// Load Employee
			$employee = Employee\EmployeeQuery::create()
					->filterByEmployeeId($employeeId)
					->findOne();
			if ($employee !== null) {
				$dims = $this->getPageDimensions();
				if ($this->GetY() + 5 > $dims['hk'] - $dims['bm']) {
					$this->addPage();
					$this->setFont('', 'B');
					$this->SetFontSize(9);
					$this->Cell(120, 5, 'Personale', 1, 0, 'C');
					$this->Cell(60, 5, 'Ultimo mese chiuso', 1, 1, 'C');
					$this->Cell(0, 5, '', 0, 1, 'C');
					$this->setFont('', '');
				}
				// Change row style
				if ($oddStyle) {
					$this->SetFillColor(235, 235, 235);
				} else {
					$this->SetFillColor(250, 250, 250);
				}
				$oddStyle = !$oddStyle;

				// Load Data
				$extRecMonth = new ExtRecMonth($employee, strtotime("1-" . $this->_month . "-" . $this->_year));
				$lastLockedMonth = $extRecMonth->getLastStoredMonth();
				$lastLockedMonth = $lastLockedMonth ? _(date("F", $lastLockedMonth->getDateStart())) . " " . date("Y", $lastLockedMonth->getDateStart()) : ' -';
				$this->Cell(120, 5, $employee->getSurname() . ' ' . $employee->getName(), 1, 0, 'L', true, '', 1);
				$this->Cell(60, 5, $lastLockedMonth, 1, 1, 'L', true, '', 1);
			}
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {
		return 'Situazione chiusure mensili';
	}

}
