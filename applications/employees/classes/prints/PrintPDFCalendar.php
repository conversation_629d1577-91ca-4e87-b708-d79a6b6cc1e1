<?php

/**
 * Class to show the Entries and the exits in a givern period for a subsets of employees.
 *
 * <AUTHOR>
 */
class PrintPDFCalendar extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_year;
	private $_path;

	public function __construct($request) {
		$this->_year = strtotime($request['year']);
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$this->SetTitle($this->_path);
		$this->setPageOrientation('P');
		$this->SetMargins(5, 30);

		$this->addPage();
		// Title
		$this->setFont('', 'B');
		$this->SetFontSize(12);
		$this->Cell(0, 5, 'Calendario anno ' . $this->_year, 0, 1, 'L', false, '', 1);

		// Table header
		$this->SetFontSize(9);
		// Headers

		$this->setFont('', '');

		// For each month of the year
		// For each day in the month
		$day = $this->_start;
		for ($day = $this->_start; $day <= $this->_end; $day = strtotime(date("d-m-Y", $day) . " +1 day")) {
			// Day Header
			$this->setFont('', 'B');
			$this->SetFontSize(9);
			$this->Cell(0, 5, _(date("l", $day)) . " " . date("j", $day) . " " . _(date("F", $day)) . " " . date("Y", $day), 0, 1, 'C');


			$create = $this->createPdf();
			if ($create) {
				Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
			}
		}
	}

	static function getName($data) {
		return 'Calendario anno - ' . $data['year'];
	}

}
