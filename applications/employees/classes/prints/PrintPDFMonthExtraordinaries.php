<?php

/**
 * Print extraordinary in month, day per day, with all selected hours stack
 *
 * <AUTHOR>
 */
class PrintPDFMonthExtraordinaries extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_employees;
	private $_month;
	private $_year;
	private $_stacks;
	private $_path;

	public function __construct($request) {
		$this->_month = $request['month'];
		$this->_year = $request['year'];
		$this->_employees = json_decode($request['employees']);
		$this->_stacks = json_decode($request['stacks']);
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
        $this->_printFormat = $request['print_format'];
		parent::__construct();
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$this->SetTitle($this->_path);
		$this->setPageOrientation('L');
		$this->SetMargins(5, 30);
		$cal = new Calendar();

		// For every employee want to print

		foreach ($this->_employees as $employeeId) {

			// Start page for every employee
			$employee = Employee\EmployeeQuery::create()
					->filterByEmployeeId($employeeId)
					->findOne();
			if ($employee !== null) {
				$extRecMonth = new ExtRecMonth($employee, strtotime("1-" . $this->_month . "-" . $this->_year));
				$saved = $extRecMonth->checkStoredMonthExists($employeeId, strtotime("1-" . $this->_month . "-" . $this->_year));
				$savedStr = !$saved ? " (MESE NON CHIUSO)" : "";
				$data = $extRecMonth->calcMonth();

                $this->AddPage('', $this->_printFormat);

				// Header
				$this->setFont('', 'B');
				$this->SetFontSize(12);
				$this->Cell(0, 5, 'Riepilogo mensile. Mese: ' . $this->_month . '-' . $this->_year . '. Personale: ' . $employee->getSurname() . ' ' . $employee->getName() . $savedStr, 0, 1, 'L', false, '', 1);
				$this->SetFontSize(9);
				$this->MultiCell(15, 10, 'Giorno', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
				$this->MultiCell(50, 10, 'Timbrature', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
				$this->MultiCell(12, 10, 'Tot. timbr.', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
				$this->MultiCell(12, 10, 'Orario', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
				$this->MultiCell(12, 10, 'Effett. lav.', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
				$this->MultiCell(15, 10, 'Saldo', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
				foreach ($data['stacks'] as $i => $stack) {
					if (in_array($i, $this->_stacks)) {
						$this->MultiCell(20, 10, ucfirst(strtolower($stack['denomination'])), 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
					}
				}
				$this->MultiCell(0, 10, 'Note', 1, 'C', false, 1, '', '', true, 1, false, true, 10, 'M', true);
				// Totals - Start
				$this->Cell(101, 5, 'Totali al 1-' . $this->_month . '-' . $this->_year, 1, 0, 'R');
				$this->Cell(15, 5, $data['general']['totalStart'] ? $this->formatTime($data['general']['totalStart']) : '00:00', 1, 0, 'R', false, '', 1);
				foreach ($data['stacks'] as $i => $stack) {
					if (in_array($i, $this->_stacks)) {
						$this->Cell(20, 5, $this->formatTime($stack['totalStart'], true, $stack['unit']), 1, 0, 'R', false, '', 1);
					}
				}
				$this->Cell(0, 5, '', 0, 1, 'L', false, '', 1);
				$this->setFont('', '');

				// Loop all day of month for this $employee
				$oddStyle = true;
				foreach ($data['days'] as $day) {

					// Change style row by row
					if ($oddStyle) {
						$this->SetFillColor(235, 235, 235);
						$oddStyle = false;
					} else {
						$this->SetFillColor(250, 250, 250);
						$oddStyle = true;
					}

					$presencesArr = array();
					foreach ($day['presences'] as $presence) {
						$presencesArr[] = date("H:i", $presence->getDateEdit());
					}
					// Day totals
					if ($cal->isFestivity(strtotime($day['date']))) {
						$this->setFont('', 'B');
						$this->Cell(10, 4, _(date("D", strtotime($day['date']))), 1, 0, 'L', true);
						$this->Cell(5, 4, _(date("j", strtotime($day['date']))), 1, 0, 'C', true);
						$this->setFont('', '');
					} else {
						$this->Cell(10, 4, _(date("D", strtotime($day['date']))), 1, 0, 'L', true);
						$this->Cell(5, 4, _(date("j", strtotime($day['date']))), 1, 0, 'C', true);
					}
					$this->Cell(50, 4, implode(' - ', $presencesArr), 1, 0, 'C', true, '', 1);
					$this->Cell(12, 4, $this->formatTime(($day['worked']['normal'] + $day['worked']['service']), false), 1, 0, 'R', true);
					$this->Cell(12, 4, $this->formatTime($day['contract'], false), 1, 0, 'R', true);
					$this->Cell(12, 4, $this->formatTime(($day['contract'] + $day['total']), false), 1, 0, 'R', true);
					$this->Cell(15, 4, $this->formatTime($day['total'], false), 1, 0, 'R', true);
					foreach ($data['stacks'] as $i => $stack) {
						if (in_array($i, $this->_stacks)) {
							if ($day['stacks'][$i]) {
								$total = $this->formatTime($day['stacks'][$i], false, $stack['unit']);
							} else {
								$total = $this->formatTime(0, false, $stack['unit']);
							}
							$this->Cell(20, 4, $total, 1, 0, 'R', true);
						}
					}
					$abs = $day['absences'][0];
					$noteArr = array();
					if ($abs != null) {
						$absKind = $abs->getAbsencesAbsenceKind();
						$n = '(' . $absKind->getCode() . ') ' . $absKind->getDescription();
						if (strlen($n) > 18) {
							$n = substr($n, 0, 15) . '...';
						}
						$noteArr[] = $n;
					}
					if (trim($day['note'])) {
						$noteArr[] = $day['note'];
					}

					$this->Cell(0, 4, implode(' - ', $noteArr), 1, 1, 'L', true, '', 1);
				}
				// Totals - Month
				$this->setFont('', 'B');
				$this->Cell(101, 5, 'Totali nel mese', 1, 0, 'R');
				$this->Cell(15, 5, $this->formatTime($data['general']['totalMonth']), 1, 0, 'R', false);
				foreach ($data['stacks'] as $i => $stack) {
					if (in_array($i, $this->_stacks)) {
						$this->Cell(20, 5, $this->formatTime($stack['totalMonth'], true, $stack['unit']), 1, 0, 'R', false);
					}
				}
				$this->Cell(0, 5, '', 0, 1, 'L');
				// Totals - Month + Residuals
				$this->Cell(101, 5, 'Totali al ' . date("d-m-Y", $data['general']['dateEnd']), 1, 0, 'R');
				$this->Cell(15, 5, $this->formatTime($data['general']['totalEnd']), 1, 0, 'R');
				foreach ($data['stacks'] as $i => $stack) {
					if (in_array($i, $this->_stacks)) {
						$this->Cell(20, 5, $this->formatTime($stack['totalEnd'], true, $stack['unit']), 1, 0, 'R');
					}
				}
				$this->Cell(0, 5, $data['general']['note'], 1, 1, 'L', false, '', 2);
			}
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {
		return 'Riepilogo Generale - ' . _(date("F", strtotime("01-{$data['month']}-2000"))) . " {$data['year']}";
	}

}
