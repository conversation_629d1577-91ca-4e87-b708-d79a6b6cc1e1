<?php

/**
 * Print extraordinary in month, day per day, with all selected hours stack
 *
 * <AUTHOR>
 */
class PrintPDFAbsences extends PrintCore {

    const spool = true;

    // request data
    private $_print_id;
    private $_employees;
    private $_start;
    private $_end;
    private $_kinds;
    private $_ignore;
    private $_path;

    public function __construct($request) {
        $this->_ignore = $request['ignore'];
        $this->_start = strtotime($request['start']);
        $this->_end = strtotime($request['end']);
        $this->_employees = json_decode($request['employees']);
        $this->_kinds = json_decode($request['kinds']);
        $this->_path = $request['path'];
        $this->_print_id = $request['id'];
        foreach ($this->_kinds as $k => $kind) {
            $this->_kinds[$k] = "'" . $kind . "'";
        }
        parent::__construct();
    }

    private function calcDuration($employee_id, $absence, $day) {
        $cal = new Calendar();
        $countable = false;
        // Checks if the day is a festivity
        if ($cal->isHoliday($day)) {
            if ($absence['calc_festivities'] == 't') {
                $countable = true;
            }
        } else {
            $timetable = Employee\TimetableQuery::create()
                    ->filterByEmployeeId($employee_id)
                    ->filterByDateEnd(array('min' => $day, 'max' => ($day + 86399)))
                    ->findOne();
            if ($timetable != null) {
                $countable = true;
            } else {
                if ($absence['calc_ferials'] == 't') {
                    $countable = true;
                }
            }
        }
        // Counts
        $count = array(
            'days'  => 0,
            'hours' => 0,
        );
        if ($countable) {
            $count['days'] = 1;
            $count['hours'] = ($absence['end_date'] - $absence['start_date']) / 60;
            if ($absence['unit'] == ABS_STACK_UNIT_DAILY) {
                $count['hours'] = 0;
            } else if ($absence['unit'] == ABS_STACK_UNIT_HOURLY) {
                $count['days'] = 0;
            }
        }
        return $count;
    }

    private function drawListView($absences) {
        $this->setFont('', 'B');
        $this->SetFontSize(9);
        $this->MultiCell(20, 10, 'Data Inizio', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(20, 10, 'Ora Inizio', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(20, 10, 'Data Fine', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(20, 10, 'Ora fine', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(50, 10, 'Tipo Assenza', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(50, 10, 'Monteore', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(20, 10, 'Giorni (periodo)', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(20, 10, 'Ore (periodo)', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
        $this->MultiCell(0, 10, 'Note', 1, 'C', false, 1, '', '', true, 1, false, true, 10, 'M', true);
        if ($absences !== false) {
            $this->setFont('', '');
            foreach ($absences as $absence) {
                $dims = $this->getPageDimensions();
                if ($this->GetY() + 4 > $dims['hk'] - $dims['bm']) {
                    $this->addPage();
                    $this->setFont('', 'B');
                    $this->SetFontSize(9);
                    $this->MultiCell(20, 10, 'Data Inizio', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(20, 10, 'Ora Inizio', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(20, 10, 'Data Fine', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(20, 10, 'Ora fine', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(50, 10, 'Tipo Assenza', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(50, 10, 'Monteore', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(20, 10, 'Giorni (periodo)', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(20, 10, 'Ore (periodo)', 1, 'C', false, 0, '', '', true, 1, false, true, 10, 'M', true);
                    $this->MultiCell(0, 10, 'Note', 1, 'C', false, 1, '', '', true, 1, false, true, 10, 'M', true);
                    $this->setFont('', '');
                }
                $this->Cell(20, 4, $absence['start_day'], 1, 0, 'C');
                $this->Cell(20, 4, $absence['start_time'], 1, 0, 'C');
                $this->Cell(20, 4, $absence['end_day'], 1, 0, 'C');
                $this->Cell(20, 4, $absence['end_time'], 1, 0, 'C');
                $n = '(' . $absence['code'] . ') ' . $absence['kind'];
                if (strlen($n) > 40) {
                    $n = substr($n, 0, 37) . '...';
                }
                $this->Cell(50, 4, $n, 1, 0, 'L', false, '', 1);
                $this->Cell(50, 4, $absence['stack'], 1, 0, 'L', false, '', 1);
                if ($absence['unit'] == ABS_STACK_UNIT_DAILY) {
                    $this->Cell(20, 4, $this->formatTime($absence['total']['days'], true, $absence['unit']), 1, 0, 'R');
                    $this->Cell(20, 4, "", 1, 0, 'C');
                } else if ($absence['unit'] == ABS_STACK_UNIT_HOURLY) {
                    $this->Cell(20, 4, "", 1, 0, 'C');
                    $this->Cell(20, 4, $this->formatTime($absence['total']['hours'], true, $absence['unit']), 1, 0, 'R');
                } else {
                    $this->Cell(20, 4, $this->formatTime($absence['total']['days'], true, 'd'), 1, 0, 'R');
                    $this->Cell(20, 4, $this->formatTime($absence['total']['hours'], true, 'h'), 1, 0, 'R');
                }
                $not = $absence['note'];
                if (strlen($not) > 40) {
                    $not = substr($not, 0, 37) . '...';
                }
                $this->Cell(0, 4, $not, 1, 1, 'L', false, '', 1);
            }
        }
    }

    private function drawCalendarView($days) {
        $dims = $this->getPageDimensions();
        if ($this->GetY() + 19 > $dims['hk'] - $dims['bm']) {
            $this->addPage();
        }
        $this->setFont('', 'B');
        $this->SetFontSize(12);
        $this->Cell(220, 5, 'Calendario', 0, 1, 'L', false, '', 1);
        $this->SetFontSize(9);
        $this->MultiCell(20, 10, 'Mese', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '1', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '2', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '3', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '4', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '5', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '6', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '7', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '8', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '9', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '10', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '11', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '12', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '13', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '14', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '15', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '16', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '17', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '18', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '19', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '20', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '21', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '22', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '23', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '24', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '25', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '26', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '27', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '28', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '29', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '30', 1, 'C', false, 0, '', '', true, 0, false, true, 10, 'M', true);
        $this->MultiCell(8.5, 10, '31', 1, 'C', false, 1, '', '', true, 0, false, true, 10, 'M', true);
        $this->setFont('', '');
        $sDay = strtotime("01-" . date("m", $this->_start) . "-" . date("Y", $this->_start));
        $eDay = strtotime(date("t", $this->_end) . "-" . date("m", $this->_end) . "-" . date("Y", $this->_end));
        for ($d = $sDay; $d <= $eDay; $d = strtotime("+1 Day", $d)) {
            // Prepares the string to display in the cell
            $cell = "";
            if ($d >= $this->_start && $d < strtotime("+1 Day", $this->_end)) {
                if (isset($days[$d])) {
                    $cell = implode("-", $days[$d]);
                }
            } else {
                $cell = "###";
            }
            // First day draws the month too
            if ((int) date("j", $d) == 1) {
                $this->Cell(20, 4, _(date("F", $d)), 1, 0, 'L');
            }
            // Line feed if it is the last day of the month taking count of gaps
            $gap = 31 - (int) date("t", $d);
            $lf = 0;
            if ((int) date("j", strtotime("+1 Day", $d)) == 1 && $gap == 0) {
                $lf = 1;
            }
            $this->Cell(8.5, 4, $cell, 1, $lf, 'C', false, '', 1);
            if ((int) date("j", strtotime("+1 Day", $d)) == 1 && $gap != 0) {
                for ($gap; $gap > 1; $gap--) {
                    $this->Cell(8.5, 4, "###", 1, 0, 'C');
                }
                $this->Cell(8.5, 4, "###", 1, 1, 'C');
            }
        }
    }

    private function drawTotals($totals) {
        $dims = $this->getPageDimensions();
        if ($this->GetY() + 10 > $dims['hk'] - $dims['bm']) {
            $this->addPage();
        }
        $this->setFont('', 'B');
        $this->SetFontSize(9);
        $this->Cell(70, 5, 'Totali', 1, 0, 'C');
        $this->Cell(20, 5, 'Giorni', 1, 0, 'C');
        $this->Cell(20, 5, 'Ore', 1, 1, 'C');
        $oddStyle = true;
        foreach ($totals['stacks'] as $sname => $stack) {
            if ($oddStyle) {
                $this->SetFillColor(235, 235, 235);
            } else {
                $this->SetFillColor(250, 250, 250);
            }
            $this->setFont('', '');
            // Per Kind totals
            foreach ($stack['kinds'] as $kname => $ktotal) {
                $dims = $this->getPageDimensions();
                if ($this->GetY() + 5 > $dims['hk'] - $dims['bm']) {
                    $this->addPage();
                    $this->setFont('', 'B');
                    $this->SetFontSize(9);
                    $this->Cell(70, 5, 'Totali', 1, 0, 'C');
                    $this->Cell(20, 5, 'Giorni', 1, 0, 'C');
                    $this->Cell(20, 5, 'Ore', 1, 1, 'C');
                    $this->setFont('', '');
                }
                $n = $kname;
                if (strlen($n) > 40) {
                    $n = substr($n, 0, 37) . '...';
                }
                $this->Cell(70, 5, $n, 1, 0, 'L', true, '', 1);
                if ($stack['unit'] == ABS_STACK_UNIT_DAILY) {
                    $this->Cell(20, 5, $this->formatTime($ktotal, true, $stack['unit']), 1, 0, 'R', true);
                    $this->Cell(20, 5, '', 1, 1, 'C', true);
                } else {
                    $this->Cell(20, 5, '', 1, 0, 'C', true);
                    $this->Cell(20, 5, $this->formatTime($ktotal, true, $stack['unit']), 1, 1, 'R', true);
                }
            }
            $dims = $this->getPageDimensions();
            if ($this->GetY() + 5 > $dims['hk'] - $dims['bm']) {
                $this->addPage();
                $this->setFont('', 'B');
                $this->SetFontSize(9);
                $this->Cell(70, 5, 'Totali', 1, 0, 'C');
                $this->Cell(20, 5, 'Giorni', 1, 0, 'C');
                $this->Cell(20, 5, 'Ore', 1, 1, 'C');
                $this->setFont('', '');
            }
            // Per Stack totals
            $this->setFont('', 'B');
            $this->Cell(70, 5, $sname, 1, 0, 'R', true, '', 1);
            if ($stack['unit'] == ABS_STACK_UNIT_DAILY) {
                $this->Cell(20, 5, $this->formatTime($stack['total'], true, $stack['unit']), 1, 0, 'R', true);
                $this->Cell(20, 5, '', 1, 1, 'C', true);
            } else {
                $this->Cell(20, 5, '', 1, 0, 'C', true);
                $this->Cell(20, 5, $this->formatTime($stack['total'], true, $stack['unit']), 1, 1, 'R', true);
            }
            $oddStyle = !$oddStyle;
        }
        // Period totals
        $dims = $this->getPageDimensions();
        if ($this->GetY() + 10 > $dims['hk'] - $dims['bm']) {
            $this->addPage();
            $this->setFont('', 'B');
            $this->SetFontSize(9);
            $this->Cell(70, 5, 'Totali', 1, 0, 'C');
            $this->Cell(20, 5, 'Giorni', 1, 0, 'C');
            $this->Cell(20, 5, 'Ore', 1, 1, 'C');
            $this->setFont('', '');
        }
        $this->Cell(0, 5, '', 0, 1, 'L');
        $this->SetFillColor(250, 250, 250);
        $this->setFont('', 'B');
        $this->Cell(70, 5, 'Totale nel periodo', 1, 0, 'C');
        $this->Cell(20, 5, $this->formatTime($totals['days'], true, ABS_STACK_UNIT_DAILY), 1, 0, 'R');
        $this->Cell(20, 5, $this->formatTime($totals['hours'], true, ABS_STACK_UNIT_HOURLY), 1, 1, 'R');
    }

    /**
     * Base method to make a print
     */
    public function as_view() {
        $this->SetTitle($this->_path);
        $this->setPageOrientation('L');
        $this->SetMargins(5, 30);

        // For every employee
        foreach ($this->_employees as $employeeId) {

            // Gets employee infos
            $employee = Employee\EmployeeQuery::create()
                    ->filterByEmployeeId($employeeId)
                    ->findOne();
            if ($employee !== null) {

                // Gets all absences infos in the given period for the given kind
                $db = Db::getInstance();
                $sql = "SELECT "
                        . "unit, "
                        . "denomination AS stack, "
                        . "recover, "
                        . "code, "
                        . "description AS kind, "
                        . "calc_festivities, "
                        . "calc_ferials, "
                        . "start_date, "
                        . "end_date, "
                        . "date_of_req AS req_date, "
                        . "decreto, "
                        . "type_of_abs AS type, "
                        . "note, "
                        . "to_char(to_timestamp(start_date), 'DD-MM-YYYY') AS start_day, "
                        . "to_char(to_timestamp(end_date), 'DD-MM-YYYY')AS end_day, "
                        . "to_char(to_timestamp(start_date), 'HH24:MI') AS start_time, "
                        . "to_char(to_timestamp(end_date), 'HH24:MI') AS end_time "
                        . "FROM absences "
                        . "LEFT JOIN absence_kind ON (absences.ab_kind = absence_kind.code) "
                        . "LEFT JOIN absence_stack ON (absence_kind.absence_stack = absence_stack.id) "
                        . "WHERE employee_id = " . $employee->getEmployeeId() . " "
                        . "AND code IN (" . implode(",", $this->_kinds) . ") "
                        . "AND start_date < " . strtotime("+1 day", $this->_end) . " "
                        . "AND end_date >= " . $this->_start . " "
                        . "ORDER BY start_date;";
                $db->query($sql);
                $absences = $db->fetchAll();
                $days = array();
                $totals = array(
                    'days'   => 0,
                    'hours'  => 0,
                    'stacks' => array());

                if ($absences !== false) {
                    // Counts durations
                    for ($day = $this->_start; $day <= $this->_end; $day = strtotime("+1 Day", $day)) {
                        // Data for calendar and totals views
                        foreach ($absences as $akey => $absence) {
                            if ($day >= strtotime($absence['start_day']) && $day < strtotime("+1 day", strtotime($absence['end_day']))) {
                                $duration = $this->calcDuration($employee->getEmployeeId(), $absence, $day);
                                if ($duration['days'] > 0 || $duration['hours'] > 0) {
                                    $days[$day][] = $absence['code'];
                                }
                                $absences[$akey]['total']['days'] += $duration['days'];
                                $absences[$akey]['total']['hours'] += $duration['hours'];
                                // Does not count unlinked absences for the totals
                                if ($absence['stack']) {
                                    $totals['stacks'][strtoupper($absence['stack'])]['unit'] = $absence['unit'];
                                    if ($absence['unit'] == ABS_STACK_UNIT_DAILY) {
                                        $totals['days'] += $duration['days'];
                                        $totals['stacks'][strtoupper($absence['stack'])]['kinds']['(' . $absence['code'] . ") " . strtoupper($absence['kind'])] += $duration['days'];
                                        $totals['stacks'][strtoupper($absence['stack'])]['total'] += $duration['days'];
                                    } else if ($absence['unit'] == ABS_STACK_UNIT_HOURLY) {
                                        $totals['hours'] += $duration['hours'];
                                        $totals['stacks'][strtoupper($absence['stack'])]['kinds']['(' . $absence['code'] . ") " . strtoupper($absence['kind'])] += $duration['hours'];
                                        $totals['stacks'][strtoupper($absence['stack'])]['total'] += $duration['hours'];
                                    }
                                }
                            }
                        }
                    }
                }

                if ($absences !== false || ($absences === false && $this->_ignore != 'on')) {
                    $this->addPage();
                    // HEADER
                    $this->setFont('', 'B');
                    $this->SetFontSize(12);
                    $this->Cell(220, 5, 'Riepilogo Assenze. Periodo: ' . date("d/m/Y", $this->_start) . ' - ' . date("d/m/Y", $this->_end) . '. Personale: ' . $employee->getSurname() . ' ' . $employee->getName(), 0, 1, 'L', false, '', 1);
                    // LIST VIEW
                    $this->drawListView($absences);
                    $this->Cell(0, 5, '', 0, 1, 'L');
                    // CALENDAR VIEW
                    $this->drawCalendarView($days);
                    $this->Cell(0, 5, '', 0, 1, 'L');
                    // TOTALS VIEW
                    $this->drawTotals($totals);
                }
            }
        }

        $create = $this->createPdf();
        if ($create) {
            Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        return 'Riepilogo Assenze - ' . $data['start'] . ' / ' . $data['end'];
    }

}
