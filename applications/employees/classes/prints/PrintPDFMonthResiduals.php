<?php

/**
 * Class to show totals of Extraordinaries and a subset of stacks
 * in a month for a subset of employees.
 *
 * <AUTHOR>
 */
class PrintPDFMonthResiduals extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_employees;
	private $_month;
	private $_year;
	private $_stacks;
	private $_path;

	public function __construct($request) {
		$this->_month = $request['month'];
		$this->_year = $request['year'];
		$this->_employees = json_decode($request['employees']);
		$this->_stacks = json_decode($request['stacks']);
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$this->SetTitle($this->_path);
		$this->setPageOrientation('P');
		$this->SetMargins(5, 30);

		$this->addPage();
		// Title
		$this->setFont('', 'B');
		$this->SetFontSize(12);
		$this->Cell(0, 5, 'Riepilogo Residui e Monteore. Mese: ' . $this->_month . '-' . $this->_year, 0, 1, 'L', false, '', 1);

		// Table header
		$this->SetFontSize(9);
		$this->Cell(50, 5, 'Personale', 1, 0, 'C');
		$this->Cell(30, 5, '', 1, 0, 'C');
		$this->Cell(15, 5, 'Inizio', 1, 0, 'C');
		$this->Cell(15, 5, 'Fatti', 1, 0, 'C');
		$this->Cell(15, 5, 'Fine', 1, 0, 'C');
		$xNote = $this->GetX();
		$this->Cell(0, 5, 'Note', 1, 1, 'C');

		$this->setFont('', '');
		$rowHeight = 5 * count($this->_stacks);

		// For every employee
		foreach ($this->_employees as $employeeId) {
			// Load data
			$employee = Employee\EmployeeQuery::create()
					->filterByEmployeeId($employeeId)
					->findOne();
			if ($employee !== null) {
				$dims = $this->getPageDimensions();
				if ($this->GetY() + $rowHeight + 5 > $dims['hk'] - $dims['bm']) {
					$this->addPage();
					$this->setFont('', 'B');
					$this->SetFontSize(9);
					$this->Cell(50, 5, 'Personale', 1, 0, 'C');
					$this->Cell(30, 5, '', 1, 0, 'C');
					$this->Cell(15, 5, 'Inizio', 1, 0, 'C');
					$this->Cell(15, 5, 'Fatti', 1, 0, 'C');
					$this->Cell(15, 5, 'Fine', 1, 0, 'C');
					$this->Cell(0, 5, 'Note', 1, 1, 'C');
					$this->setFont('', '');
				}
				$extRecMonth = new ExtRecMonth($employee, strtotime("1-" . $this->_month . "-" . $this->_year));
				$saved = $extRecMonth->checkStoredMonthExists($employeeId, strtotime("1-" . $this->_month . "-" . $this->_year));
				$savedStr = !$saved ? "\n(MESE NON CHIUSO)" : "";
				$data = $extRecMonth->calcMonth();

				$this->Cell(0, 5, '', 0, 1);
				$this->setFont('', 'B');
				$this->SetFontSize(9);
				$this->MultiCell(50, $rowHeight, $employee->getSurname() . ' ' . $employee->getName() . $savedStr, 1, 'L', false, 0, $this->GetX(), $this->GetY(), true, 0, false, true, $rowHeight, 'M', true);
				$yNote = $this->GetY();
				$xDenominations = $this->GetX();
				$this->setFont('', '');
				$this->SetFontSize(7);
				$oddStyle = false;
				$cnt = 0;

				if (in_array(-1, $this->_stacks)) {
					$this->Cell(30, 5, 'Straordinari', 1, 0, 'L', false, '', 1);
					$this->Cell(15, 5, $this->formatTime($data['general']['totalStart']), 1, 0, 'R', false, '', 1);
					$this->Cell(15, 5, $this->formatTime($data['general']['totalMonth']), 1, 0, 'R', false, '', 1);
					$this->Cell(15, 5, $this->formatTime($data['general']['totalEnd']), 1, 2, 'R', false, '', 1);
				}

				foreach ($data['stacks'] as $i => $stack) {
					if (in_array($i, $this->_stacks)) {
						// Change row style
						if ($oddStyle) {
							$this->SetFillColor(235, 235, 235);
						} else {
							$this->SetFillColor(250, 250, 250);
						}
						$oddStyle = !$oddStyle;
						$this->SetX($xDenominations);
						$this->Cell(30, 5, $stack['denomination'], 1, 0, 'L', true, '', 1);
						$this->Cell(15, 5, $this->formatTime($stack['totalStart'], true, $stack['unit']), 1, 0, 'R', true, '', 1);
						$this->Cell(15, 5, $this->formatTime($stack['totalMonth'], true, $stack['unit']), 1, 0, 'R', true, '', 1);
						$this->Cell(15, 5, $this->formatTime($stack['totalEnd'], true, $stack['unit']), 1, 2, 'R', true, '', 1);
						$cnt++;
					}
				}

				$this->MultiCell(0, $rowHeight, $data['general']['note'], 1, 'L', false, 1, $xNote, $yNote, true, 0, false, true, $rowHeight, 'M', true);
			}
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {
		return 'Riepilogo Residui e Monteore - ' . $data['month'] . ' ' . $data['year'];
	}

}
