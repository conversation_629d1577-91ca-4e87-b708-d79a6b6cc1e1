<?php

/**
 * Class to show the Stacks and Absence kinds.
 *
 * <AUTHOR>
 */
class PrintPDFDay extends PrintCore {

	const spool = true;

	// request data
	private $_day;
	private $_employees;
	private $_timetables;
	private $_presences;
	private $_absences;
	private $_print_id;
	private $_path;

	public function __construct($request) {
		$this->_day = $request['day'];
		$this->_employees = json_decode($request['employees']);
		$this->_timetables = $request['timetables'];
		$this->_presences = $request['presences'];
		$this->_absences = $request['absences'];
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	private function formatTimetable($timetable) {
		$pause = ' - ';
		if ($timetable['date_start_pause'] || $timetable['date_end_pause']) {
			$pause = ' (' . date('H:i', $timetable['date_start_pause']) . ' - ' . date('H:i', $timetable['date_end_pause']) . ') ';
		}
		$result = '[' . date('H:i', $timetable['date_start']) . $pause . date('H:i', $timetable['date_end']) . ']';
		return $result;
	}

	private function formatPresence($presence) {
		$io = $presence['original_inout_edit'] == PRESENCE_ENTRANCE ? 'E' : 'U';
		return date('H:i', $presence['date_edit']) . "({$io})";
	}

	private function formatAbsence($absence) {
		$result = "({$absence['AbsencesAbsenceKind']['code']}) {$absence['AbsencesAbsenceKind']['description']}";
		if (strlen($result) > 28) {
			$result = substr($result, 0, 25) . '...';
		}
		return $result;
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$day = strtotime($this->_day);
		$nextDay = strtotime("+1 Day " . $this->_day);

		$this->SetTitle($this->_path);
		$this->setPageOrientation('L');
		$this->SetMargins(5, 30);

		$this->addPage();

		// Title
		$this->setFont('', 'B');
		$this->SetFontSize(12);
		$this->Cell(0, 5, "Riepilogo giornaliero {$this->_day}", 0, 1, 'L', false, '', 1);
		$this->Cell(0, 5, '', 0, 1, 'C');

		// Table header
		$this->SetFontSize(9);
		$this->Cell(80, 5, 'Personale', 1, 0, 'C');
		$this->Cell(50, 5, 'Orario', 1, 0, 'C');
		$this->Cell(50, 5, 'Timbrature', 1, 0, 'C');
		$this->Cell(100, 5, 'Assenze', 1, 1, 'C');
		$this->Cell(0, 5, '', 0, 1, 'C');
		$this->SetFont('', '');
		$oddStyle = false;

		// For every employee
		foreach ($this->_employees as $employeeId) {
			$employee = Employee\EmployeeQuery::create()
					->filterByEmployeeId($employeeId)
					->findOne();
			if ($employee !== null) {
				// Gets timetables
				$timetables = Employee\TimetableQuery::create()
						->filterByEmployeeId($employeeId)
						->filterByDateStart($day, Criteria::GREATER_EQUAL)
						->filterByDateStart($nextDay, Criteria::LESS_THAN)
						->orderByDateStart()
						->find()
						->toArray(null, false, BasePeer::TYPE_FIELDNAME);
				// Gets absences
				$absences = Employee\AbsencesQuery::create()
						->filterByEmployeeId($employeeId)
						->filterByStartDate($nextDay, Criteria::LESS_THAN)
						->filterByEndDate($day, Criteria::GREATER_EQUAL)
						->orderByStartDate()
						->find()
						->toArray(null, false, BasePeer::TYPE_FIELDNAME);
				// Gets presences
				$presences = Employee\PresenceQuery::create()
						->filterByEmployeeId($employeeId)
						->filterByDateEdit($day, Criteria::GREATER_EQUAL)
						->filterByDateEdit($nextDay, Criteria::LESS_THAN)
						->orderByDateEdit()
						->find()
						->toArray(null, false, BasePeer::TYPE_FIELDNAME);
				// Draws record if the filters conditions are met
				if ((($this->_timetables == 0 && count($timetables) == 0) || ($this->_timetables == 1 && count($timetables) > 0) || $this->_timetables == 2) && (($this->_absences == 0 && count($absences) == 0) || ($this->_absences == 1 && count($absences) > 0) || $this->_absences == 2) && (($this->_presences == 0 && count($presences) == 0) || ($this->_presences == 1 && count($presences) > 0) || $this->_presences == 2)) {
					$tts = array();
					foreach ($timetables as $key => $timetable) {
						$tts[] = $this->formatTimetable($timetable);
					}
					$bip = array();
					foreach ($presences as $key => $presence) {
						$bip[] = $this->formatPresence($presence);
					}
					$abs = array();
					foreach ($absences as $key => $absence) {
						$abs[] = $this->formatAbsence($absence);
					}

					$dims = $this->getPageDimensions();
					if ($this->GetY() + 10 > $dims['hk'] - $dims['bm']) {
						$this->addPage();
					}
					if ($oddStyle) {
						$this->SetFillColor(235, 235, 235);
					} else {
						$this->SetFillColor(250, 250, 250);
					}

					$this->MultiCell(80, 8, "{$employee->getSurname()} {$employee->getName()}", 1, 'L', true, 0, '', '', true, 0, false, true, 8, 'M', true);
					$this->MultiCell(50, 8, implode("  ", $tts), 1, 'C', true, 0, '', '', true, 0, false, true, 8, 'M', true);
					$this->MultiCell(50, 8, implode(" - ", $bip), 1, 'C', true, 0, '', '', true, 0, false, true, 8, 'M', true);
					$this->MultiCell(100, 8, implode(" - ", $abs), 1, 'L', true, 1, '', '', true, 0, false, true, 8, 'M', true);
					/* $this->Cell(80, 5, "{$employee->getSurname()} {$employee->getName()}", 1, 0, 'C');
					  $this->Cell(50, 5, implode("  ", $tts), 1, 0, 'C');
					  $this->Cell(50, 5, implode(" - ", $bip), 1, 0, 'C');
					  $this->Cell(100, 5, implode(" - ", $abs), 1, 1, 'C'); */
					$oddStyle = !$oddStyle;
				}
			}
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {

		return 'Riepilogo giornaliero - ' . $data['day'];
	}

}
