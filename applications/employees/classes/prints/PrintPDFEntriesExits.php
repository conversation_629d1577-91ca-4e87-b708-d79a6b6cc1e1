<?php

/**
 * Class to show the Entries and the exits in a givern period for a subsets of employees.
 *
 * <AUTHOR>
 */
class PrintPDFEntriesExits extends PrintCore {

    const spool = true;

    // request data
    private $_print_id;
    private $_employees;
    private $_start;
    private $_end;
    private $_showNotes;
    private $_path;

    public function __construct($request) {
        $this->_start = strtotime($request['start']);
        $this->_end = strtotime($request['end']);
        $this->_showNotes = $request['notes'];
        $this->_employees = json_decode($request['employees']);
        $this->_path = $request['path'];
        $this->_print_id = $request['id'];
        parent::__construct();
    }

    /**
     * Base method to make a print
     */
    public function as_view() {
        $this->SetTitle($this->_path);
        $this->setPageOrientation('P');
        $this->SetMargins(5, 30);

        $this->addPage();
        // Title
        $this->setFont('', 'B');
        $this->SetFontSize(12);
        $this->Cell(0, 5, 'Timbrature nel periodo ' . date("d-m-Y", $this->_start) . '-' . date("d-m-Y", $this->_end), 0, 1, 'L', false, '', 1);

        // Table header
        $this->SetFontSize(9);
        $this->Cell(60, 5, 'Personale', 1, 0, 'C');
        $this->Cell(40, 5, 'Orario', 1, 0, 'C');
        $this->Cell(0, 5, 'Timbrature', 1, 1, 'C');
        $this->Cell(0, 5, '', 0, 1, 'C');

        $this->setFont('', '');

        // For each day in the period
        $day = $this->_start;
        for ($day = $this->_start; $day <= $this->_end; $day = strtotime(date("d-m-Y", $day) . " +1 day")) {
            // Day Header
            $this->setFont('', 'B');
            $this->SetFontSize(9);
            $this->Cell(0, 5, _(date("l", $day)) . " " . date("j", $day) . " " . _(date("F", $day)) . " " . date("Y", $day), 0, 1, 'C');

            // For every employee
            $oddStyle = true;
            foreach ($this->_employees as $employeeId) {
                // Loads Employee
                $employee = Employee\EmployeeQuery::create()
                        ->filterByEmployeeId($employeeId)
                        ->findOne();
                if ($employee !== null) {
                    $dims = $this->getPageDimensions();
                    if ($this->GetY() + 5 > $dims['hk'] - $dims['bm']) {
                        $this->addPage();
                        $this->setFont('', 'B');
                        $this->SetFontSize(9);
                        $this->Cell(60, 5, 'Personale', 1, 0, 'C');
                        $this->Cell(40, 5, 'Orario', 1, 0, 'C');
                        $this->Cell(0, 5, 'Timbrature', 1, 1, 'C');
                        $this->Cell(0, 5, '', 0, 1, 'C');
                        $this->setFont('', '');
                    }
                    // Changes row style
                    if ($oddStyle) {
                        $this->SetFillColor(235, 235, 235);
                    } else {
                        $this->SetFillColor(250, 250, 250);
                    }
                    $oddStyle = !$oddStyle;

                    // Loads data
                    $extRecDay = new ExtRecDay($employee, $day);
                    $presences = $extRecDay->getPresences(false);
                    $timetables = $extRecDay->getTimetables(false);
                    // Formats data
                    $ps = array();
                    $notes = false;
                    foreach ($presences as $presence) {
                        if ($presence->getDescription()) {
                            $notes = true;
                        }
                        if ($this->_showNotes == 'on') {
                            $ps[] = $presence->toString(false, true, true, false, false, false, true);
                        } else {
                            $ps[] = $presence->toString(false, true, true);
                        }
                    }
                    $tts = array();
                    foreach ($timetables as $timetable) {
                        $tts[] = $timetable->toString();
                    }

                    $this->setFont('', 'B');
                    $this->SetFontSize(9);
                    $this->Cell(60, 5, $employee->getSurname() . ' ' . $employee->getName(), 1, 0, 'L', true);
                    $this->setFont('', '');
                    $this->SetFontSize(7);
                    $this->Cell(40, 5, implode(" - ", $tts), 1, 0, 'L', true, '', 1);
                    if ($this->_showNotes == 'on' && $notes) {
                        $this->MultiCell(0, 5, implode("\n", $ps), 1, 'L', true);
                    } else {
                        $this->Cell(0, 5, implode(" - ", $ps), 1, 1, 'L', true, '', 1);
                    }
                }
            }
        }

        $create = $this->createPdf();
        if ($create) {
            Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        return 'Riepilogo Timbrature - ' . $data['start'] . ' / ' . $data['end'];
    }

}
