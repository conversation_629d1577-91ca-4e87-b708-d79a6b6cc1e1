<?php

/**
 * Class to show the timetable in a given period for a subsets of employees.
 *
 * <AUTHOR>
 */
class PrintPDFTimeTables extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_employees;
	private $_year;
	private $_path;

	public function __construct($request) {
		$this->_year = $request['year'];
		$this->_employees = json_decode($request['employees']);
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	private function formatTimetable($timetable) {
		$pause = ' - ';
		if ($timetable['date_start_pause'] || $timetable['date_end_pause']) {
			$pause = ' (' . date('H:i', $timetable['date_start_pause']) . ' - ' . date('H:i', $timetable['date_end_pause']) . ') ';
		}
		$result = date('H:i', $timetable['date_start']) . $pause . date('H:i', $timetable['date_end']);
		return $result;
	}

	private function drawMonthView($month, $timetables) {
		$cal = new Calendar();
		$this->SetY($this->GetY() + 5);
		$year = $this->_year;
		if ($month < 9) {
			$year = $this->_year + 1;
		}
		$monthStr = sprintf("%02d", $month);
		$sDay = strtotime("01-{$monthStr}-{$year}");
		$eDay = strtotime(date("t", $sDay) . "-{$monthStr}-{$year}");
		$monthName = _(date("F", $sDay)) . " $year";

		$dims = $this->getPageDimensions();
		if ($this->GetY() + 50 > $dims['hk'] - $dims['bm']) {
			$this->addPage();
		}
		$this->SetFillColor(235, 235, 235);
		$this->setFont('', 'B');
		$this->SetFontSize(9);
		$this->MultiCell(196, 4, $monthName, 1, 'C', true, 1, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Lun', 1, 'C', true, 0, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Mar', 1, 'C', true, 0, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Mer', 1, 'C', true, 0, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Gio', 1, 'C', true, 0, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Ven', 1, 'C', true, 0, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Sab', 1, 'C', true, 0, '', '', true, 0, false, true, 4, 'M', true);
		$this->MultiCell(28, 4, 'Dom', 1, 'C', true, 1, '', '', true, 0, false, true, 4, 'M', true);
		$this->setFont('', '');

		$dim = date("t", $sDay);
		$nod = date("w", $sDay) - 1;
		$nod = $nod < 0 ? 6 : $nod;

		// Draws blank cells
		while ($nod > 0) {
			$this->MultiCell(28, 8, '', 0, 'C', false, 0, '', '', true, 0, false, true, 8, 'M', true);
			$nod--;
		}

		// Draws month's cells
		$this->setFont('', '');
		$this->SetFontSize(6);
		for ($d = $sDay; $d <= $eDay; $d = strtotime("+1 Day", $d)) {
			// Prepares the data to display in the cell
			$cell = is_array($timetables[$d]) ? implode("\r\n", $timetables[$d]) : '';
			// Line feed if it is a sunday
			$lf = 0;
			if (date("w", $d) == 0 || date("j", $d) == $dim) {
				$lf = 1;
			}
			$x = $this->GetX();
			$y = $this->GetY();
			if ($cal->isFestivity($d)) {
				$this->setFont('', 'B');
				$this->MultiCell(5, 3, date("j", $d), 1, 'C', true, 0, $x, $y, true, 0, false, true, 3, 'M', true);
				$this->setFont('', '');
			} else {
				$this->MultiCell(5, 3, date("j", $d), 1, 'C', true, 0, $x, $y, true, 0, false, true, 3, 'M', true);
			}
			$this->MultiCell(28, 8, $cell, 1, 'C', false, $lf, $x, $y, true, 0, false, true, 8, 'M', true);
		}
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$start = strtotime("01-09-{$this->_year}");
		$end = strtotime("01-09-" . ($this->_year + 1));

		$this->SetTitle($this->_path);
		$this->setPageOrientation('P');
		$this->SetMargins(5, 30);

		// For every employee
		foreach ($this->_employees as $employeeId) {
			$employee = Employee\EmployeeQuery::create()
					->filterByEmployeeId($employeeId)
					->findOne();
			if ($employee !== null) {
				$timetables = Employee\TimetableQuery::create()
						->filterByEmployeeId($employeeId)
						->filterByDateStart($start, Criteria::GREATER_EQUAL)
						->filterByDateStart($end, Criteria::LESS_THAN)
						->orderByDateStart()
						->find()
						->toArray(null, false, BasePeer::TYPE_FIELDNAME);
				$tts = array();
				foreach ($timetables as $key => $timetable) {
					$timestamp = strtotime(date("d-m-Y", $timetable['date_start']));
					$tts[$timestamp][] = $this->formatTimetable($timetable);
				}
				// HEADER
				$this->addPage();
				$this->setFont('', 'B');
				$this->SetFontSize(12);
				$this->Cell(0, 5, "Orario A.S. {$this->_year}/" . ($this->_year + 1) . ". Personale: {$employee->getSurname()} {$employee->getName()}", 0, 1, 'L', false, '', 1);
				// CALENDAR VIEW
				for ($month = 9; $month <= 12; $month++) {
					$this->drawMonthView($month, $tts);
				}
				for ($month = 1; $month <= 8; $month++) {
					$this->drawMonthView($month, $tts);
				}
			}
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {
		return "Orario A.S. - {$data['year']} / " . ($data['year'] + 1);
	}

}
