<?php

/**
 * Class to show which employees are inside the school at the time of print.
 *
 * <AUTHOR>
 */
class PrintPDFWhosInside extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_employees;
	private $_datetime;
	private $_path;

	public function __construct($request) {
		$this->_datetime = strtotime($request['date'] . " " . $request['time']);
		$this->_employees = json_decode($request['employees']);
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$this->SetTitle($this->_path);
		$this->setPageOrientation('P');
		$this->SetMargins(5, 30);

		$this->addPage();
		// Title
		$this->setFont('', 'B');
		$this->SetFontSize(12);
		$this->Cell(0, 5, 'Personale in servizio il ' . date("d-m-Y", $this->_datetime) . ' alle ' . date("H:i", $this->_datetime), 0, 1, 'L', false, '', 1);

		// Table header
		$this->SetFontSize(9);
		$this->Cell(60, 5, 'Personale', 1, 0, 'C');
		$this->Cell(60, 5, 'Timbratura d\'ingresso', 1, 1, 'C');
		$this->Cell(0, 5, '', 0, 1, 'C');

		$this->setFont('', '');

		// For every employee
		foreach ($this->_employees as $employeeId) {
			// Loads Employee
			$employee = Employee\EmployeeQuery::create()
					->filterByEmployeeId($employeeId)
					->findOne();
			if ($employee !== null) {
				$dims = $this->getPageDimensions();
				if ($this->GetY() + 5 > $dims['hk'] - $dims['bm']) {
					$this->addPage();
					$this->setFont('', 'B');
					$this->SetFontSize(9);
					$this->Cell(60, 5, 'Personale', 1, 0, 'C');
					$this->Cell(60, 5, 'Timbratura d\'ingresso', 1, 1, 'C');
					$this->Cell(0, 5, '', 0, 1, 'C');
					$this->setFont('', '');
				}
				// Loads data
				$today = new ExtRecDay($employee, strtotime(date("d-m-Y", $this->_datetime)));
				$presence = null;
				$date = "-";
				$time = "-";

				// Searches today
				// Gets the last presence before the given date
				$presence = $today->getPresence($this->_datetime, -1, null, null, null, true);
				if (!$presence) {
					// La persona è uscita
					$this->SetFillColor(255, 255, 255);
				} else {
					if ($presence->isExit()) {
						// La persona è uscita
						$this->SetFillColor(255, 255, 255);
					} else {
						// La persona è dentro
						$this->SetFillColor(200, 200, 200);
						$d = date("d-m-Y", $presence->getDateEdit());
						if ($d === date("d-m-Y", $this->_datetime)) {
							$date = "Oggi";
						} else if ($d === date("d-m-Y", $this->_datetime - 86400)) {
							$date = "Ieri";
						} else {
							$date = $d;
						}
						$time = $presence->toString(false, false, false, true);
					}
				}

				$this->setFont('', 'B');
				$this->SetFontSize(9);
				$this->Cell(60, 5, $employee->getSurname() . ' ' . $employee->getName(), 1, 0, 'L', true, '', 1);
				$this->setFont('', '');
				$this->SetFontSize(7);
				$this->Cell(30, 5, $date, 1, 0, 'C', true, '', 1);
				$this->Cell(30, 5, $time, 1, 1, 'C', true, '', 1);
			}
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {
		return 'Personale in servizio - ' . $data['date'] . '  ' . $data['time'];
	}

}
