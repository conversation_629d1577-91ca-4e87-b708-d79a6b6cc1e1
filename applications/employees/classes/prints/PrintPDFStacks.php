<?php

/**
 * Class to show the Stacks and Absence kinds.
 *
 * <AUTHOR>
 */
class PrintPDFStacks extends PrintCore {

	const spool = true;

	// request data
	private $_print_id;
	private $_path;

	public function __construct($request) {
		$this->_path = $request['path'];
		$this->_print_id = $request['id'];
		parent::__construct();
	}

	/**
	 * Base method to make a print
	 */
	public function as_view() {
		$this->SetTitle($this->_path);
		$this->setPageOrientation('L');
		$this->SetMargins(5, 30);

		$this->addPage();

		// Title
		$this->setFont('', 'B');
		$this->SetFontSize(12);
		$this->Cell(0, 5, 'Struttura Monteore - Tipi Assenza', 0, 1, 'L', false, '', 1);

		// Table header
		$this->SetFontSize(9);
		$this->Cell(100, 5, 'Monteore', 1, 0, 'C');
		$this->Cell(15, 5, 'Unità', 1, 0, 'C');
		$this->Cell(160, 5, 'Tipo Assenza', 1, 1, 'C');
		$this->Cell(0, 5, '', 0, 1, 'C');

		// Gets data
		$sql = "SELECT ak.*, am.* FROM absence_kind AS ak LEFT JOIN absence_stack AS am ON (ak.absence_stack = am.id) ORDER BY denomination, code;";
		$db = Db::getInstance();
		$db->query($sql);
		$aks = $db->fetchAll();

		// Generates structure
		$structure = array();
		foreach ($aks as $key => $ak) {
			if ($ak['id'] == null) {
				if ($ak['date_end'] == null) {
					$ak['id'] = 0;
					$structure[$ak['id']]['denomination'] = 'Non abbinati';
				} else {
					$ak['id'] = 1;
					$structure[$ak['id']]['denomination'] = 'Non più attivi';
				}
			} else {
				$ak['id'] = $ak['id'] + 1;
				$structure[$ak['id']]['denomination'] = $ak['denomination'];
			}
			$structure[$ak['id']]['recover'] = $ak['recover'] === 't' ? 'Sì' : '';
			$structure[$ak['id']]['unit'] = $ak['unit'] == 'd' ? 'Giorni' : 'Ore';
			$structure[$ak['id']]['kinds'][$ak['code']]['description'] = $ak['description'];
			$structure[$ak['id']]['kinds'][$ak['code']]['ferials'] = $ak['calc_ferials'] === 't' ? 'Sì' : '';
			$structure[$ak['id']]['kinds'][$ak['code']]['festivities'] = $ak['calc_festivities'] === 't' ? 'Sì' : '';
		}

		// Orders record
		ksort($structure);
		$na = array_shift($structure);
		$structure[] = $na;
		$na = array_shift($structure);
		$structure[] = $na;
		// For each record
		foreach ($structure as $stackId => $stackData) {
			$dims = $this->getPageDimensions();
			if ($this->GetY() + 5 + 5 * count($stackData['kinds']) > $dims['hk'] - $dims['bm']) {
				$this->addPage();
				$this->setFont('', 'B');
				$this->SetFontSize(9);
				$this->Cell(100, 5, 'Monteore', 1, 0, 'C');
				$this->Cell(15, 5, 'Unità', 1, 0, 'C');
				$this->Cell(160, 5, 'Tipo Assenza', 1, 1, 'C');
				$this->Cell(0, 5, '', 0, 1, 'C');
			}
			$this->setFont('', 'B');
			$this->SetFillColor(255, 255, 255);
			$this->MultiCell(100, 5, $stackData['denomination'], 1, 'L', true, 0, null, null, false, 0, false, false, 0, 'M', true);
			$x = $this->GetX();
			$this->MultiCell(15, 5, $stackData['unit'], 1, 'C', true, 0, null, null, true, 0, false, false, 0, 'M', true);
			$x = $this->GetX();
			$oddStyle = false;
			foreach ($stackData['kinds'] as $kindCode => $kindData) {
				if ($this->GetY() + 10 > $dims['hk'] - $dims['bm']) {
					$this->addPage();
					$this->setFont('', 'B');
					$this->SetFontSize(9);
					$this->Cell(100, 5, 'Monteore', 1, 0, 'C');
					$this->Cell(15, 5, 'Unità', 1, 0, 'C');
					$this->Cell(160, 5, 'Tipo Assenza', 1, 1, 'C');
					$this->Cell(0, 5, '', 0, 1, 'C');
				}
				$this->setFont('', '');
				$y = $this->GetY();
				if ($oddStyle) {
					$this->SetFillColor(235, 235, 235);
				} else {
					$this->SetFillColor(250, 250, 250);
				}
				$this->MultiCell(160, 5, "$kindCode - {$kindData['description']}", 1, 'L', true, 1, $x, $y, true, 0, false, false, 0, 'M', true);
				$oddStyle = !$oddStyle;
			}
			$this->Cell(0, 5, '', 0, 1, 'C');
		}

		$create = $this->createPdf();
		if ($create) {
			Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
		}
	}

	static function getName($data) {
		return 'Struttura Monteore - Tipi Assenza';
	}

}
