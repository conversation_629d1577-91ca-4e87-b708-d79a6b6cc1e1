<?php

/**
 * Main class to calculate month totals
 *
 * <AUTHOR>
 *
 */
class ExtRecMonth {

    protected $_employee = null;
    protected $_date = null;

    /**
     * ExtRecMonth constructor
     * @param Object $emp Employee
     * @param integer $date Timestamp
     */
    public function __construct($emp, $date) {
        $this->_date = strtotime(date("d-m-Y", $date));
        $this->_employee = $emp;
    }

    /**
     * Checks if the record exists.
     *
     * @param Object $empId the employee obj
     * @param integer $date the timestamp
     * @return type the record or null if not present
     */
    public function checkStoredMonthExists($emp_id, $date) {
        $storedMonth = \Employee\StoredMonthQuery::create()
                ->filterByEmployeeId($emp_id)
                ->filterByDateStart($date)
                ->findOne();
        if (!$storedMonth) {
            return null;
        } else {
            return $storedMonth;
        }
    }

    /**
     * Retrieves the stored stack details for this stored month.
     *
     * @param integer $stack_id the stack id bound to this stored month to look for in db
     * @return array The stored stack if found, null otherwise
     */
    public function getStoredStack($stack_id) {
        $storedMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), $this->_date);
        if ($storedMonth == null) {
            return null;
        }
        $storedStack = \Employee\StoredStackQuery::create()
                ->filterByStoredMonth($storedMonth->getStoredMonthId())
                ->filterByAbsenceStack((int) $stack_id)
                ->findOne();
        return $storedStack;
    }

    /**
     * Retrieves a list of all the stored stacks for this stored month.
     *
     * @return array the Stacks details list if any, false otherwise
     */
    public function getStoredStacks() {
        $storedMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), $this->_date);
        if ($storedMonth == null) {
            return array();
        }
        $storedStacks = \Employee\StoredStackQuery::create()
                ->filterByStoredMonth($storedMonth->getStoredMonthId())
                ->find();
        return $storedStacks;
    }

    /**
     * Saves this month and all data associated (Days and Stacks).
     *
     * @param type $monthData the month data array
     * @param type $days the days array
     * @param type $stacks the stacks array
     * @return int the newly stored month ID, or false if errors occurred
     */
    public function saveMonth($monthData, $days, $stacks) {
        $sMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), $this->_date);
        if ($sMonth !== null) {
            return false;
        } else {
            // Stores each day
            foreach ($days as $dayData) {
                $this->storeDay(strtotime($dayData['date']), $dayData['total'], $dayData['totalO'], $dayData['note']);
            }
            // Stores this month
            $storedMonth = $this->storeMonth($monthData['ext_start'], $monthData['ext_end'], $monthData['ext_start_o'], $monthData['ext_end_o'], $monthData['note']);
            // Stores each stack
            foreach ($stacks as $stackData) {
                $this->storeStack($storedMonth->getStoredMonthId(), $stackData['id'], $stackData['denomination'], $stackData['unit'], $stackData['recover'], $stackData['totalStart'], $stackData['totalEnd'], $stackData['totalStartO'], $stackData['totalEndO']);
            }
            return $storedMonth->getStoredMonthId();
        }
    }

    /**
     * Deletes this stored month and the corresponding stacks and days.
     *
     * @return boolean true if deleted, false otherwise
     */
    public function deleteMonth() {
        $storedMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), $this->_date);
        if ($storedMonth == null) {
            return false;
        }
        // Removes each stored day bound to this month
        $day = \Employee\StoredDayQuery::create()
                ->filterByEmployeeId($this->_employee->getEmployeeId())
                ->filterByDate(array('min' => $this->_date, 'max' => strtotime("+1 month -1 day", $this->_date)))
                ->delete();
        // Removes this stored month
        $month = \Employee\StoredMonthQuery::create()
                ->filterByEmployeeId($this->_employee->getEmployeeId())
                ->filterByDateStart($this->_date)
                ->delete();
        return true;
    }

    /**
     * Stores a day for this month.
     *
     * @param type $date
     * @param type $total
     * @param type $total0
     * @param type $note
     */
    public function storeDay($date, $total, $total0, $note) {
        $day = new \Employee\StoredDay();
        $day->setEmployeeId($this->_employee->getEmployeeId());
        $day->setDate($date);
        $day->setExtraordinary($total0);
        $day->setAuthorized($total);
        $day->setNote($note);
        $day->save();
        return $day;
    }

    /**
     * Stores the month.
     *
     * @param type $extStart
     * @param type $extEnd
     * @param type $extStart0
     * @param type $extEnd0
     * @param type $note
     * @return \Employee\StoredMonth
     */
    public function storeMonth($extStart, $extEnd, $extStart0, $extEnd0, $note) {
        $storedMonth = new Employee\StoredMonth();
        $storedMonth->setEmployeeId($this->_employee->getEmployeeId());
        $storedMonth->setDateStart($this->_date);
        $storedMonth->setDateEnd(strtotime("+1 month -1 day", $this->_date));
        $storedMonth->setExtStart($extStart);
        $storedMonth->setExtEnd($extEnd);
        $storedMonth->setExtStartOriginal($extStart0);
        $storedMonth->setExtEndOriginal($extEnd0);
        $storedMonth->setNote($note);
        $storedMonth->save();
        return $storedMonth;
    }

    /**
     * Stores a stack for this month.
     *
     * @param type $monthId
     * @param type $absenceStack
     * @param type $stackDenomination
     * @param type $unit
     * @param type $valueStart
     * @param type $valueEnd
     * @param type $valueStartO
     * @param type $valueEndO
     */
    public function storeStack($monthId, $absenceStack, $stackDenomination, $unit, $recover, $valueStart, $valueEnd, $valueStartO, $valueEndO) {
        $stack = new \Employee\StoredStack();
        $stack->setStoredMonth($monthId);
        $stack->setAbsenceStack($absenceStack);
        $stack->setStackDenomination($stackDenomination);
        $stack->setUnit($unit);
        $stack->setRecover($recover);
        $stack->setValueStart($valueStart);
        $stack->setValueEnd($valueEnd);
        $stack->setValueStartOriginal($valueStartO);
        $stack->setValueEndOriginal($valueEndO);
        $stack->save();
        return $stack;
    }

    /**
     * Checks if this month can be locked. The month can be locked if there aren't any month locked or if the previous month is locked already.
     *
     * @return boolean true if this month can be locked, false otherwise
     */
    public function canLock() {
        if ($this->_date > strtotime(date("d-m-Y"))) {
            return false;
        }
        $storedMonths = \Employee\StoredMonthQuery::create()
                ->filterByEmployeeId($this->_employee->getEmployeeId())
                ->find();
        if (!$storedMonths->isEmpty()) {
            $lastMonthStoredMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), strtotime(date("d-m-Y", $this->_date) . " -1 month"));
            if ($lastMonthStoredMonth == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * Checks if this month can be unlocked. The month can be unlocked if the next month is unlocked already.
     *
     * @return boolean true if this month can be unlocked, false otherwise
     */
    public function canUnlock() {
        $nextMonthStoredMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), strtotime(date("d-m-Y", $this->_date) . " +1 month"));
        if ($nextMonthStoredMonth !== null) {
            return false;
        }
        return true;
    }

    /**
     * Calculates the month for storage.
     *
     * @return array the array of all the data for this month
     */
    public function calcMonth() {
        $data = array();
        $end = strtotime(date("d-m-Y", $this->_date) . " +1 month");

        // Checks for last stored month
        $lastLockedMonth = $this->getLastStoredMonth();

        $data['general'] = array(
            'dateStart'       => $this->_date,
            'dateEnd'         => strtotime(date("d-m-Y", $this->_date) . ' +1 month -1 day'),
            'blocked'         => false,
            'lastLockedMonth' => $lastLockedMonth ? _(date("F", $lastLockedMonth->getDateStart())) . " " . date("Y", $lastLockedMonth->getDateStart()) : ''
        );

        $month_ext_rec = 0;
        $month_for_recover = 0;

        // Gets data for each day
        for ($i = $this->_date; $i < $end; $i = strtotime(date("d-m-Y", $i) . " +1 day")) {
            $extCurrDay = new ExtRecDay($this->_employee, $i);
            $ext = $extCurrDay->calcDay();

            $month_ext_rec += $ext['authorized'];
            $month_for_recover += $extCurrDay->calcRecoverDetractions();

            $dayStacks = array();
            $absences = $extCurrDay->getAbsences();
            foreach ($absences as $abs) {
                $absKind = $abs->getAbsencesAbsenceKind();
                $absStack = $absKind->getAbsenceKindAbsenceStack();
                if ($absStack !== null) {
                    $duration = $abs->getDuration();
                    if ($abs->isCountableOnDay($i)) {
                        if ($absStack->isDaily()) {
                            $duration = 1;
                        } else {
                            $duration = $duration['time'];
                        }
                        $dayStacks[$absStack->getId()] += $duration;
                    }
                }
            }

            $storedDay = $extCurrDay->checkStoredDayExists($this->_employee->getEmployeeId(), $i);
            $day = array(
                'date'      => date("r", $i),
                'presences' => $extCurrDay->getPresences(),
                'absences'  => $extCurrDay->getAbsences(),
                'worked'    => $extCurrDay->calcWorkedTime(),
                'contract'  => $extCurrDay->calcContractTime(),
                'total'     => $ext['authorized'],
                'totalO'    => $ext['extraordinary'],
                'stacks'    => $dayStacks,
                'note'      => $storedDay !== null ? $storedDay->getNote() : ''
            );

            // Checks for common errors and infos occurring in a day
            if (count($extCurrDay->getPresences()) == 0) {
                $day['warning']['nobip'] = "Non sono presenti timbrature";
            }
            if (count($extCurrDay->getPresences()) % 2 != 0) {
                $day['warning']['oddbip'] = "Le timbrature sono in numero dispari";
            }
            if (count($extCurrDay->getAbsences()) > 0) {
                $day['info']['abs'] = "Presenti giustificazioni d'assenza";
                if ($day['warning']['nobip']) {
                    unset($day['warning']['nobip']);
                    if (empty($day['warning'])) {
                        unset($day['warning']);
                    }
                }
            }
            $abss = $extCurrDay->getAbsences();
            // Checks for un-bound absences
            foreach ($abss as $absence) {
                $absKind = $absence->getAbsencesAbsenceKind();
                $absStack = $absKind->getAbsenceKindAbsenceStack();
                if ($absStack == null) {
                    $day['warning']['stk'] = "Presenti giustificazioni d'assenza non abbinate ad un monteore";
                    break;
                }
            }
            $cal = new Calendar();
            // Checks for non-working days
            if ($cal->isWeekend($i)) {
                $day['info']['wke'] = "Giorno non lavorativo";
                if ($day['warning']['nobip']) {
                    unset($day['warning']['nobip']);
                    if (empty($day['warning'])) {
                        unset($day['warning']);
                    }
                }
            }
            // Checks for festivities
            if ($cal->isFestivity($i)) {
                $day['info']['hol'] = "Festività";
                if ($day['info']['wke']) {
                    unset($day['info']['wke']);
                }
                if ($day['warning']['nobip']) {
                    unset($day['warning']['nobip']);
                    if (empty($day['warning'])) {
                        unset($day['warning']);
                    }
                }
            }

            $data['days'][$i] = $day;
        }

        // Gets data for this month
        $storedMonth = $this->checkStoredMonthExists($this->_employee->getEmployeeId(), $this->_date);
        if ($storedMonth == null) {
            // Gets all stacks
            $stacks = Employee\AbsenceStackQuery::create()
                    ->find();
            foreach ($stacks as $stack) {
                $data['stacks'][$stack->getId()]['id'] = $stack->getId();
                $data['stacks'][$stack->getId()]['unit'] = $stack->getUnit();
                $data['stacks'][$stack->getId()]['recover'] = $stack->getRecover();
                $data['stacks'][$stack->getId()]['denomination'] = $stack->getDenomination();
                $data['stacks'][$stack->getId()]['reset_type'] = $stack->getResetType();
                $data['stacks'][$stack->getId()]['reset_date'] = $stack->getResetDate();
                $data['stacks'][$stack->getId()]['reset_to_stack_id'] = $stack->getResetToStackId();
                $data['stacks'][$stack->getId()]['totalMonth'] = 0;
            }
            $lastMonth = $this->getLastStoredMonth($this->_date);

            $fromStacks = array();

            // Sets stacks to 0 or last known value if already stored in a previous month
            if ($lastMonth == null) {
                $data['general']['totalStart'] = 0;
                $data['general']['totalStartO'] = 0;

                // Applies Stacks reset logic
                foreach ($data['stacks'] as $k => $stack) {
                    $start = $this->_applyStackReset($stack, 0);
                    $data['stacks'][$k]['reset_type_applied'] = $start['applied'];
                    $data['stacks'][$k]['totalStart'] = $start['final'];
                    $data['stacks'][$k]['totalStartO'] = $start['final'];
                    $data['stacks'][$k]['totalEnd'] = $start['final'];
                    $data['stacks'][$k]['totalEndO'] = $start['final'];
                    if ($stack['reset_to_stack_id'] !== null) {
                        $fromStacks[$k][$stack['reset_to_stack_id']] = 0;
                    }
                }
            } else {
                $data['general']['totalStart'] = $lastMonth->getExtEnd();
                $data['general']['totalStartO'] = $lastMonth->getExtEnd();

                // Applies Stacks reset logic
                foreach ($data['stacks'] as $k => $stack) {
                    $lastMonthStack = \Employee\StoredStackQuery::create()
                            ->filterByAbsenceStack($stack['id'])
                            ->filterByStoredMonth($lastMonth->getStoredMonthId())
                            ->findOne();
                    $stackTotal = $lastMonthStack === null ? 0 : $lastMonthStack->getValueEnd();
                    $start = $this->_applyStackReset($stack, $stackTotal);
                    $data['stacks'][$k]['reset_type_applied'] = $start['applied'];
                    $data['stacks'][$k]['totalStart'] = $start['final'];
                    $data['stacks'][$k]['totalStartO'] = $start['final'];
                    $data['stacks'][$k]['totalEnd'] = $start['final'];
                    $data['stacks'][$k]['totalEndO'] = $start['final'];
                    if ($stack['reset_to_stack_id'] !== null && $start['applied'] !== STACK_RESET_MANUAL) {
                        $fromStacks[$k][$stack['reset_to_stack_id']] = $stackTotal;
                    }
                }
            }

            // Applies To Stacks reset logic
            foreach ($fromStacks as $toStacks) {
                foreach ($toStacks as $toStackId => $fromStackTotal) {
                    $data['stacks'][$toStackId]['totalStart'] += $fromStackTotal;
                    $data['stacks'][$toStackId]['totalStartO'] += $fromStackTotal;
                    $data['stacks'][$toStackId]['totalEnd'] += $fromStackTotal;
                    $data['stacks'][$toStackId]['totalEndO'] += $fromStackTotal;
                }
            }

            // Sets the totals extraordinaries for the month
            $data['general']['totalMonth'] = $month_ext_rec;
            $data['general']['totalEnd'] = $data['general']['totalStart'] + $month_ext_rec - $month_for_recover;
            $data['general']['totalEndO'] = $data['general']['totalEnd'];
            $data['general']['note'] = '';

            // Sets the totals for each stack by looping each day and
            // substracting the duration of the absences of the day from the relative stack
            foreach ($data['days'] as $day) {
                foreach ($day['stacks'] as $k => $stack) {
                    $data['stacks'][$k]['totalEnd'] -= $stack;
                    $data['stacks'][$k]['totalEndO'] -= $stack;
                    $data['stacks'][$k]['totalMonth'] += $stack;
                }
            }

            // Cleans the stacks array
            foreach ($data['stacks'] as $k => $stack) {
                unset($data['stacks'][$k]['reset_type']);
                unset($data['stacks'][$k]['reset_date']);
                unset($data['stacks'][$k]['reset_to_stack_id']);
            }
        } else {
            // Loads the already stored month
            $data['general']['totalStart'] = $storedMonth->getExtStart();
            $data['general']['totalEnd'] = $storedMonth->getExtEnd();
            $data['general']['totalMonth'] = $month_ext_rec;
            $data['general']['totalStartO'] = $storedMonth->getExtStartOriginal();
            $data['general']['totalEndO'] = $storedMonth->getExtEndOriginal();
            $data['general']['note'] = $storedMonth->getNote();
            $data['general']['blocked'] = true;

            // Loads the already stored stacks
            $storedStacks = $this->getStoredStacks();
            $storedStackNew = array();
            foreach ($storedStacks as $stack) {
                $storedStackNew[$stack->getAbsenceStack()]['id'] = $stack->getAbsenceStack();
                $storedStackNew[$stack->getAbsenceStack()]['unit'] = $stack->getUnit();
                $storedStackNew[$stack->getAbsenceStack()]['recover'] = $stack->getRecover();
                $storedStackNew[$stack->getAbsenceStack()]['denomination'] = $stack->getStackDenomination();
                $storedStackNew[$stack->getAbsenceStack()]['totalEnd'] = $stack->getValueEnd();
                $storedStackNew[$stack->getAbsenceStack()]['totalEndO'] = $stack->getValueEndOriginal();
                $storedStackNew[$stack->getAbsenceStack()]['totalStart'] = $stack->getValueStart();
                $storedStackNew[$stack->getAbsenceStack()]['totalStartO'] = $stack->getValueStartOriginal();
                $storedStackNew[$stack->getAbsenceStack()]['totalMonth'] = $stack->getValueEnd() - $stack->getValueStart();
                $storedStackNew[$stack->getAbsenceStack()]['reset_type_applied'] = $stack->getResetTypeApplied();
            }
            $data['stacks'] = $storedStackNew;
        }
        return $data;
    }

    /**
     * Retrieves the last stored month available till the date (if specified)
     * or without limit (if date is not specified).
     *
     * @param timestamp $dateLimit The upper limit for the starting date (performs a less than comparison)
     * @return null|\StoredMonth
     */
    public function getLastStoredMonth($dateLimit = null) {
        $lastStoredMonth = \Employee\StoredMonthQuery::create()
                ->filterByEmployeeId($this->_employee->getEmployeeId())
                ->orderByDateStart(Criteria::DESC);
        if ($dateLimit) {
            $lastStoredMonth->filterByDateStart($dateLimit, Criteria::LESS_THAN);
        }
        $lastStoredMonth = $lastStoredMonth->findOne();
        return $lastStoredMonth;
    }

    /**
     * Applies stack reset logic to the given stack.
     *
     * @param array $stack The stack object to apply the reset onto
     * @return Array 'final' => the final reset-applied month-end value of the stack; 'applied' => reset type applied
     */
    private function _applyStackReset($stack, $start) {
        $rType = $stack['reset_type'];

        $final = array(
            'final'   => $start,
            'applied' => STACK_RESET_MANUAL
        );

        // Checks reset date
        $annualDateConfirmed = date("n", strtotime($stack['reset_date'])) === date("n", $this->_date) ? true : false;

        // Retrieves quota
        $quota = 0;
        $personnelStack = \Employee\PersonnelStacksQuery::create()
                ->filterByEmployeeId($this->_employee->getEmployeeId())
                ->filterByStackId($stack['id'])
                ->findOne();
        if ($personnelStack !== null) {
            $quota = $personnelStack->getResetQuota();
        }

        // Applies reset logic
        if ((($rType === STACK_RESET_RESET_YEARLY || $rType === STACK_RESET_ADD_YEARLY || $rType === STACK_RESET_SUB_YEARLY) && $annualDateConfirmed) || ($rType === STACK_RESET_RESET_MONTHLY || $rType === STACK_RESET_ADD_MONTHLY || $rType === STACK_RESET_SUB_MONTHLY)) {
            $final['applied'] = $rType;
        }

        if ($final['applied'] !== STACK_RESET_MANUAL) {
            if ($rType === STACK_RESET_RESET_MONTHLY || $rType === STACK_RESET_RESET_YEARLY) {
                $final['final'] = $quota;
            } else if ($rType === STACK_RESET_SUB_MONTHLY || $rType === STACK_RESET_SUB_YEARLY) {
                $final['final'] -= $quota;
            } else if ($rType === STACK_RESET_ADD_MONTHLY || $rType === STACK_RESET_ADD_YEARLY) {
                $final['final'] += $quota;
            }
        }

        return $final;
    }

}
