<?php

/**
 * The class handle template about "Decreti"
 *
 * <AUTHOR>
 */
class Decreto
{

    public $table_name = 'decreti';

    public function __construct($absence_id = -1)
    {
	if ($absence_id < 0)
	    return;
	global $db;
	$this->db = $db;
	$this->absence_id = $absence_id;

	$this->absence = new Absence($this->absence_id);
	$this->employee = new Employee($this->absence->employee_id);

	$sql = "* FROM " . $this->table_name . "
                            WHERE
                              absence_kind = '" . $this->absence->ab_kind . "' AND
                              employee_kind = '" . $this->employee->getFiscalKind() . "' AND
                              employee_role ='" . $this->employee->getFiscalRole() . "'";
	$this->db->select($sql);
	$decreto = $this->db->fetchAll();

	foreach ($decreto[0] as $key => $value)
	{
	    $this->$key = $value;
	}
    }

    private function getPrevAbsences()
    {
	$to = $this->absence->start_date;
	switch ($this->time_back)
	{
	    case 'corrente anno':
		$from = strtotime("1 January " . date("Y", $this->absence->start_date));
		break;
	    case 'corrente mese':
		$from = strtotime("1 " . date("F", $this->absence->start_date) . " " . date("Y", $this->absence->start_date));
		break;
	    case 'ultimo triennio':
		$from = strtotime(date("d", $this->absence->start_date) . " " . date("F", $this->absence->start_date) . " " . date("Y", $this->absence->start_date) . ' -3 years');
		break;
	    default:
		$from = 0;
		$to = time();
		break;
	}
	$prevAbsences = new Absences();
	return $prevAbsences->filter(
			$this->employee->employee_id, $from, $to, explode(',', $this->prev_abs_kind));
    }

    public function getTemplateHTML()
    {
	$prev_absences = $this->getPrevAbsences();
	$str_abs_prev = '';
	$total_prev_days = 0;
	foreach ($prev_absences as $absence)
	{
	    if ($absence->absence_id == $this->absence_id)
	    {
		continue;
	    }
	    $str_abs_prev .= 'dal ' . date("d/m/Y", $absence->start_date) . ' al ' . date("d/m/Y", $absence->end_date) .
		    ' gg. ' . $absence->total_days . ' per ' . $absence->ab_kind_display . '<br />';
	    $total_prev_days += $absence->total_days;
	}
	if ($total_prev_days > 0)
	{
	    $str_abs_prev .= 'Per un totale di gg. ' . $total_prev_days . '<br />';
	}

	$institute = new InstituteData();
	$instituteData = $institute->getData();
	$directionalData = $institute->getDirectionData();

	$protocol_number = $this->absence->getProtocolNumber();

	$replaceArray = array(
	    '{{ surname }}' => $this->employee->surname,
	    '{{ name }}' => $this->employee->name,
	    '{{ protocol_number }}' => ($protocol_number > 0 ? $protocol_number : ''),
	    '{{ qualification }}' => $this->employee->getQualificationDisplay(),
	    '{{ birthplace_city }}' => $this->employee->birthplace_city_display,
	    '{{ birthdate }}' => date("d/m/Y", $this->employee->birthdate),
	    '{{ fiscal_code }}' => $this->employee->fiscal_code,
	    '{{ prev_absences }}' => $str_abs_prev,
	    '{{ this_tot_days }}' => $this->absence->total_days,
	    '{{ request_date }}' => date("d/m/Y", $this->absence->date_of_req),
	    '{{ this_start_date }}' => date("d/m/Y", $this->absence->start_date),
	    '{{ this_end_date }}' => date("d/m/Y", $this->absence->end_date),
	    '{{ institute_city }}' => $instituteData ['description'],
	    '{{ ds }}' => $directionalData['dir']['surname'] . ' ' . $directionalData['dir']['name'],
	    '{{ institute_name }}' => $instituteData['name'],
	    '{{ institute_region }}' => strtoupper(getRegionFromCode($instituteData['region'])),
	    '{{ institute_cf }}' => $instituteData['fiscal_code'],
	    '{{ institute_address }}' => $instituteData['address'],
	    '{{ institute_province }}' => $instituteData['province'],
	    '{{ today }}' => date("d/m/Y")
	);


	return str_replace(
			array_keys($replaceArray), array_values($replaceArray), $this->print);
    }

}

?>
