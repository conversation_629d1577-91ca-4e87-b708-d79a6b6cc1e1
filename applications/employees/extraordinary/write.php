<?php

/*
 * Writes extraordinary for one month for one person
 *
 */

require_once '../../../index.php';

$month = (int) $request->month;
$year = (int) $request->year;
$start = strtotime("1-" . $month . "-" . $year);
$employee_id = (int) $request->employee_id > 0 ? (int) $request->employee_id : null;

$data = array(
    'success' => false,
    'message' => 'No Employee found'
);

$msg = "Chiusura";

if ($employee_id != null) {
    $emp = \Employee\EmployeeQuery::create()
            ->filterByEmployeeId($employee_id)
            ->findOne();
    if ($emp != null) {
        $data = array(
            'success' => true
        );
        // Retrieves current month's days extraordinary data
        $extCurrMonth = new ExtRecMonth($emp, $start);
        // If the month is stored deletes it, otherwise saves it
        $check = $extCurrMonth->checkStoredMonthExists($employee_id, $start);
        if ($check === null) {
            if (!$extCurrMonth->saveMonth(json_decode($request->month_data, true), json_decode($request->days, true), json_decode($request->stacks, true))) {
                $data['success'] = false;
                $data['message'] = 'Error on month store';
            }
        } else {
            $msg = "Apertura";
            if (!$extCurrMonth->deleteMonth()) {
                $data['success'] = false;
                $data['message'] = 'Error on month deletion';
            }
        }
    }
}

if ($data['success'] === true) {
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "{$msg} del mese {$month} anno {$year} per la persona {$emp->getSurname()} {$emp->getName()}",
        'Context' => print_r($request, true)
    ));
}

echo Response::Create($data);
