<?php

require_once '../../../index.php';

$employee_id = (int) $request->employees > 0 ? (int) $request->employees : null;
$results = array(
    'year'  => -1,
    'month' => -1
);

if ($employee_id != null) {
    $emp = \Employee\EmployeeQuery::create()
            ->filterByEmployeeId($employee_id)
            ->findOne();
    if ($emp != null) {
        // Retrieves current month's days extraordinary data
        $extCurrMonth = new ExtRecMonth($emp, $start);
        $last = $extCurrMonth->getLastStoredMonth();
        if ($last) {
            $results['year'] = (int) date("Y", $last->getDateStart());
            $results['month'] = (int) date("n", $last->getDateStart());
        }
    }
}

$data = array(
    'success' => true,
    'results' => $results
);

echo Response::Create($data);
