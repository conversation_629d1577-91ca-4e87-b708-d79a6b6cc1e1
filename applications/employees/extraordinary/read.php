<?php

/*
 * Get extraordinary from one month for one person
 *
 */

require_once '../../../index.php';

$month = (int) $request->month;
$year = (int) $request->year;
$start = strtotime("1-" . $month . "-" . $year);
$employee_id = (int) $request->employee_id > 0 ? (int) $request->employee_id : null;
$results = array();
$can = false;

if ($employee_id != null) {
	$emp = \Employee\EmployeeQuery::create()
			->filterByEmployeeId($employee_id)
			->findOne();
	if ($emp != null) {
		// Retrieves current month's days extraordinary data
		$extCurrMonth = new ExtRecMonth($emp, $start);
		$results = $extCurrMonth->calcMonth();
		$check = $extCurrMonth->checkStoredMonthExists($employee_id, $start);
		if ($check === null) {
			$can = $extCurrMonth->canLock();
		} else {
			$can = $extCurrMonth->canUnlock();
		}
	}
}

$data = array(
	'success'	 => true,
	'results'	 => array_values($results['days']),
	'general'	 => $results['general'],
	'stacks'	 => array_values($results['stacks']),
	'can'		 => $can
);

echo Response::Create($data);
