<?php

/*
 * Read employees
 */

require_once '../../../index.php';


$employee_id = (int) $request->employee_id > 0 ? (int) $request->employee_id : null;
$limit = (int) $request->get['limit'] > 0 ? (int) $request->get['limit'] : 0;
$offset = (int) $request->get['start'] > 0 ? (int) $request->get['start'] : 0;

$q = "SELECT
		a.absence_id,
		a.employee_id,
		a.note,
		a.decreto,
		a.ab_kind,
		a.start_date AS o_start_date,
        a.end_date AS o_end_date,
		to_char(to_timestamp(a.start_date),'DD-MM-YYYY') AS start_date,
		to_char(to_timestamp(a.end_date),'DD-MM-YYYY') AS end_date,
		to_char(to_timestamp(a.date_of_req),'DD-MM-YYYY') AS date_of_req,
		to_char(to_timestamp(a.start_date),'HH24:MI') AS start_time,
		to_char(to_timestamp(a.end_date),'HH24:MI') AS end_time,
		ak.description,
		ak.calc_festivities,
		ak.calc_ferials,
		ak.code||' - '||ak.description AS description_code,
		at.id AS stack,
		at.denomination,
		at.unit,
        (SELECT count(a1.absence_id) FROM absences AS a1 WHERE a1.employee_id = a.employee_id AND a.start_date <= a1.end_date AND a.end_date >= a1.start_date) AS multiple
		FROM (absences AS a LEFT JOIN absence_kind AS ak ON a.ab_kind=ak.code)
		LEFT JOIN absence_stack AS at ON ak.absence_stack=at.id";

if ($employee_id) {
    $q .= " WHERE a.employee_id = {$employee_id}";
}

$q .= " ORDER BY o_start_date DESC";

if ($limit > 0) {
    $q .= " LIMIT {$limit}";
}
if ($offset > 0) {
    $q .= " OFFSET {$offset}";
}

$db = Db::getInstance();
$db->query($q);
$absences = $db->fetchAll();

// Total count
$q = "SELECT absence_id FROM absences";

if ($employee_id) {
    $q .= " WHERE employee_id = {$employee_id}";
}

$db->query($q);
$tot = $db->fetchAll();

// Final array logic
if ($absences != false) {
    $total = 0;
    if ($tot != false) {
        $total = count($tot);
    }
    $extMonth = new ExtRecMonth($employee_id, strtotime("1-" . date("m") . "-" . date("Y")));
    foreach ($absences as $key => $absence) {
        // Checks if the absence has to be locked
        $ys = date("Y", strtotime($absence['start_date']));
        $ms = date("m", strtotime($absence['start_date']));
        $ye = date("Y", strtotime($absence['end_date']));
        $me = date("m", strtotime($absence['end_date']));
        $checkStart = $extMonth->checkStoredMonthExists($employee_id, strtotime("1-" . $ms . "-" . $ys)) != null ? true : false;
        $checkEnd = $extMonth->checkStoredMonthExists($employee_id, strtotime("1-" . $me . "-" . $ye)) != null ? true : false;
        $locked = ($checkStart || $checkEnd);
        $absences[$key]['locked'] = $locked;
    }
} else {
    $absences = array();
}

$data = array(
    'success' => true,
    'results' => $absences,
    'total'   => $total
);

echo Response::Create($data);
