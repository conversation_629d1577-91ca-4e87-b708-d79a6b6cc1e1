<?php

/*
 * Create/Update single absence
 */

require_once '../../../index.php';

$data = $request->post;
$s = true;

$kind = Employee\AbsenceKindQuery::create()
		->filterByCode($data['ab_kind'])
		->findOne();
if ($kind != null) {
	$stack = Employee\AbsenceStackQuery::create()
			->filterById($kind->getAbsenceStack())
			->findOne();
	if ($stack != null) {
		$data['unit'] = $stack->getUnit();
	} else {
		$data['unit'] = ABS_STACK_UNIT_HOURLY;
	}

	$start = $data['start_date'];
	$end = $data['end_date'];

	// Loop to create multiple absences for each day if the stack unit is hourly
	if ($data['unit'] == ABS_STACK_UNIT_DAILY) {
		$end = $start;
	}
	for ($day = strtotime($start); $day <= strtotime($end); $day = strtotime("+1 Day", $day)) {
		if ($data['unit'] == ABS_STACK_UNIT_HOURLY) {
			$data['start_date'] = $data['end_date'] = date("d-m-Y", $day);
		}
		$absenceQuery = new Employee\AbsencesQuery();
		$success = $absenceQuery->write($data);
		if ($success === true) {
			$message = 'Creazione';
			if (!$absenceQuery->new) {
				$message = 'Aggiornamento';
			}

			$logger->log(array(
				'Type'		 => 'INFO',
				'Scope'		 => __FILE__,
				'Event'		 => basename(__FILE__, ".php"),
				'Message'	 => "{$message} assenza: Tipo {$data['ab_kind']}, Personale {$data['employee_id']} - dal {$data['start_date']} al {$data['end_date']} ",
				'Context'	 => print_r($request, true)
			));
		}
	}
}

echo Response::Create(array(
	'success' => $s
));
