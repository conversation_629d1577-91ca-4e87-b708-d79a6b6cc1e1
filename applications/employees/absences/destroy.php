<?php

/*
 * Delete single absence
 */

require_once '../../../index.php';


// Load data into absence and delete it
$absence_id = (int) $request->absence_id > 0 ? (int) $request->absence_id : null;
$absence = \Employee\AbsencesQuery::create()
		->filterByAbsenceId($absence_id)
		->findOne();

$from = date("d-m-Y H:i", $absence->getStartDate());
$to = date("d-m-Y H:i", $absence->getEndDate());

$logger->log(array(
	'Type'		 => 'INFO',
	'Scope'		 => __FILE__,
	'Event'		 => basename(__FILE__, ".php"),
	'Message'	 => "Cancellazione assenza: id {$absence_id} - {$absence->getAbKind()}, personale {$absence->getEmployeeId()} - dal {$from} al {$to}",
	'Context'	 => print_r($request, true)
));

$absence->delete();

echo Response::Create(array(
	'success' => true
));
