<?php

/**
 * Print absences for a particular employee in a specific period
 *
 * <AUTHOR>
 */
class PrintAbsencesEmployee extends PrintStandardTemplate {

	const file_name = 'stampa_assenze_dipendente';
	const print_title = 'Stampa assenze dipendente';

	public $page_orientation = 'P';
	private $year;

	/**
	 * Set base data and objects.
	 * Than print title
	 *
	 */
	public function __construct($request) {
		parent::__construct(self::file_name);
		$db = Db::getInstance();
		/* $this->employee = \Employee\EmployeeQuery::create()
		  ->filterByEmployeeId((int) $request['employee_id'])
		  ->findOne(); */

		$db->query("SELECT * FROM employee WHERE employee_id = " . (int) $request['employee_id']);
		$this->employee = $db->fetchAll();
		$this->__writePrintTitle(self::print_title . ' ' . $this->employee[0]['surname'] . ' ' . $this->employee[0]['name']);
	}

	/**
	 * Create first row
	 */
	private function _setTableHeader() {
		$this->Cell(30, 5, 'Data inizio', 1, 0, 'C', '', '', 1);
		$this->Cell(30, 5, 'Data fine', 1, 0, 'C', '', '', 1);
		$this->Cell(50, 5, 'Descrizione', 1, 0, 'C', '', '', 1);
		$this->Cell(20, 5, 'Codice', 1, 0, 'C', '', '', 1);
		$this->Cell(20, 5, 'Ora inizio', 1, 0, 'C', '', '', 1);
		$this->Cell(20, 5, 'Ora fine', 1, 0, 'C', '', '', 1);
		$this->Cell(10, 5, 'Giorni/Ore', 1, 1, 'C', '', '', 1);
	}

	/**
	 *
	 * Set content of any block
	 *
	 * @param array $values
	 */
	private function _setTableContent($values) {
		$this->Cell(30, 5, $values['date_start'], 1, 0, 'C', '', '', 1);
		$this->Cell(30, 5, $values['date_end'], 1, 0, 'C', '', '', 1);
		$this->Cell(50, 5, $values['description'], 1, 0, 'C', '', '', 1);
		$this->Cell(20, 5, $values['ab_kind'], 1, 0, 'C', '', '', 1);
		$this->Cell(20, 5, $values['hour_start'], 1, 0, 'C', '', '', 1);
		$this->Cell(20, 5, $values['hour_end'], 1, 0, 'C', '', '', 1);
		$this->Cell(10, 5, $values['total_days'], 1, 0, 'C', '', '', 1);
	}

	/**
	 * Take absence data for employee
	 *
	 * @return array consumable matirial view
	 */
	public function _getViewData() {
		$db = Db::getInstance();
		$db->query("
			SELECT
				absence_kind.description,
				to_char(to_timestamp(start_date), 'DD-MM-YYYY') AS date_start,
				to_char(to_timestamp(end_date), 'DD-MM-YYYY') AS date_end,
				to_char(to_timestamp(start_date), 'HH24:MI') AS hour_start,
				to_char(to_timestamp(end_date), 'HH24:MI') AS hour_end,
				total_days
			FROM absences LEFT JOIN absence_kind ON (absences.ab_kind = absence_kind.code)
			WHERE employee_id = " . $this->employee[0]['employee_id'] . "
			ORDER BY start_date;
		");
		$absences = $db->fetchAll();

		/* $absences = Employee\AbsencesQuery::create()
		  ->joinAbsencesAbsenceKind()
		  ->withColumn("Employee/AbsenceKind.Description", 'Description')
		  ->withColumn("to_char(to_timestamp(start_date), 'DD-MM-YYYY')", 'DateStart')
		  ->withColumn("to_char(to_timestamp(end_date), 'DD-MM-YYYY')", 'DateEnd')
		  ->withColumn("to_char(to_timestamp(start_date), 'HH24:MI')", 'HourStart')
		  ->withColumn("to_char(to_timestamp(end_date), 'HH24:MI')", 'HourEnd')
		  ->filterByEmployeeId((int) $this->employee->getEmployeeId())
		  ->orderByStartDate()
		  ->find(); */
		return $absences;
	}

	/**
	 * Generate print from native TCPDF library
	 */
	public function createPdf() {
		$absences = $this->_getViewData();
		$tot_days = 0;
		$this->_setTableHeader();
		$this->Ln(1);
		foreach ($absences as $absence) {
			//$tot_days += (int) $absence->getTotalDays();
			$tot_days += (int) $absence['total_days'];
			$this->_setTableContent($absence);
			$this->Ln(5);
		}
		//$this->_setGeneralTotalRow(money_format('%.2n', $tot_days));
		return $this->native2pdf();
	}

	public function as_view() {
		$file_url = $this->createPdf();
		if ($file_url) {
			$data = array(
				"success"	 => true,
				"urlPath"	 => $file_url
			);
		} else {
			$data = array(
				"success" => false
			);
		}
		echo Response::Create($data);
	}

}

