<?php

/*
 * Get HTML about decreto, based on absence_id passed by GET.
 *
 */

require_once '../../../../index.php';

$absence = \Employee\AbsencesQuery::create()->findPk((int) $request->get['absence_id']);

$decreto = \Employee\DecretiQuery::getDecreto($absence);

echo Response::Create(array(
	'success'	 => $decreto === null ? false : true,
	'results'	 => $decreto === null ? null : $decreto->toArray(BasePeer::TYPE_FIELDNAME))
);


