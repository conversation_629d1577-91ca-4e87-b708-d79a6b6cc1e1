<?php

/*
 * Create/Update single absence
 */

require_once '../../../index.php';

$employee_ids = json_decode($request->post['employee_ids']);
$absence_id = $request->post['absence_id'];

$absence = new Employee\AbsencesQuery();
$absence->filterByAbsenceId($absence_id);
$absence = $absence->findOne();

if ($absence != null) {
	$employees = new Employee\EmployeeQuery();
	$employees->filterByEmployeeId($employee_ids);
	$employees = $employees->find();

	$absence = $absence->toArray(\BasePeer::TYPE_FIELDNAME);
	unset($absence['absence_id']);
	$absEmp = $absence['employee_id'];

	foreach ($employees as $employee) {
		// Gets if the month is closed for this employee
		$extRecMonth = new ExtRecMonth($employee, $absence['start_date']);
		$saved = $extRecMonth->checkStoredMonthExists($employee->getEmployeeId(), strtotime("01-" . date("m-Y", $absence['start_date'])));
		if (!$saved) {
			$saved = $extRecMonth->checkStoredMonthExists($employee->getEmployeeId(), strtotime("01-" . date("m-Y", $absence['end_date'])));
		}

		if (((int) $employee->getEmployeeId() == (int) $absence['employee_id']) || ($saved)) {
			continue;
		}
		$absence['employee_id'] = $employee->getEmployeeId();
		$abs = new \Employee\Absences();
		$abs->fromArray($absence, \BasePeer::TYPE_FIELDNAME);
		$success = $abs->save();
		$absence['employee_id'] = $absEmp;

		$from = date("d-m-Y H:i", $abs->getStartDate());
		$to = date("d-m-Y H:i", $abs->getEndDate());

		if ($success > 0) {
			$message = 'Creazione';
			$logger->log(array(
				'Type'		 => 'INFO',
				'Scope'		 => __FILE__,
				'Event'		 => basename(__FILE__, ".php"),
				'Message'	 => "{$message} assenza: Tipo {$abs->getAbKind()}, Personale {$employee->getSurname()} {$employee->getName()} - dal {$from} al {$to}",
				'Context'	 => print_r($request, true)
			));
		}
	}
}

echo Response::Create(array(
	'success' => true
));
