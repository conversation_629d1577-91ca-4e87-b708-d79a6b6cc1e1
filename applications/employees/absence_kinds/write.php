<?php

/*
 * Write absence kinds for calculating on festivities or on ferials
 *
 */

use \Employee\AbsenceKindQuery;

require_once '../../../index.php';

$code = isset($request->code) ? $request->code : null;

if ($code) {
    $absKind = AbsenceKindQuery::create()
            ->filterByCode($code)
            ->findOne();

    if ($absKind !== null) {
        $absKind->setCalcFestivities((bool) $request->calc_festivities);
        $absKind->setCalcFerials((bool) $request->calc_ferials);
        $absKind->save();

        $logger->log(array(
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Modifica tipo assenza: {$absKind->getCode()} - C.Fer. {$absKind->getCalcFerials()}, C.Fest. {$absKind->getCalcFestivities()}",
            'Context' => print_r($request, true)
        ));
    }
}

$data = array(
    'success' => true
);

echo Response::Create($data);
