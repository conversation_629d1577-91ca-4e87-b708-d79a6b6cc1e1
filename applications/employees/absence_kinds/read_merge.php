<?php

/*
 * Read employees
 */

require_once '../../../index.php';

$id = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_STRING);
$disabled = filter_input(INPUT_GET, 'disabled', FILTER_VALIDATE_BOOLEAN);
$unlinked = filter_input(INPUT_GET, 'unlinked', FILTER_VALIDATE_BOOLEAN);
$checkable = filter_input(INPUT_GET, 'checkable', FILTER_VALIDATE_INT);

if ($id == "children") {
	$sql = "SELECT *
			FROM absence_stack
			ORDER BY denomination;";
} else {
	$code = str_replace("children/", "", $id);
	if ($code == 'unlinked') {
		$sql = "SELECT *, '$code' AS id
			FROM absence_kind
			WHERE absence_stack IS NULL
			AND date_end IS NULL
			ORDER BY code;";
	} else if ($code == 'disabled') {
		$sql = "SELECT *, '$code' AS id
			FROM absence_kind
			WHERE absence_stack IS NULL
			AND date_end IS NOT NULL
			ORDER BY code;";
	} else {
		$sql = "SELECT absence_kind.*, id
			FROM absence_kind, absence_stack
			WHERE id = absence_stack
			AND id = '$code'
			ORDER BY code;";
	}
}

$db = Db::getInstance();
$db->query($sql);
$absences = $db->fetchAll();

// Adds fake parent node "unlinked"
if ($id == "children") {
	if ($unlinked == true) {
		$absences[] = array(
			'id'			 => 'unlinked',
			'denomination'	 => 'Non abbinate'
		);
	}
	if ($disabled == true) {
		$absences[] = array(
			'id'			 => 'disabled',
			'denomination'	 => 'Non più attive'
		);
	}
}

$merges = array();

foreach ($absences as $absence) {
	if ($id == "children") {
		$element = array(
			'id'			 => 'children/' . $absence['id'],
			'code'			 => '',
			'description'	 => $absence['denomination'],
			'unit'			 => $absence['unit'],
			'recover'		 => $absence['recover'] == 't' ? 1 : 0,
			'expanded'		 => true
		);
	} else {
		$element = array(
			'id'				 => 'children/' . $absence['id'] . '/' . $absence['code'],
			'code'				 => $absence['code'],
			'description'		 => $absence['code'] . ' - ' . $absence['description'],
			'calc_ferials'		 => $absence['calc_ferials'] == 't' ? 1 : 0,
			'calc_festivities'	 => $absence['calc_festivities'] == 't' ? 1 : 0,
			'leaf'				 => true
		);
	}
	// Checkbox logic
	switch ($checkable) {
		// No checkboxes
		case 0:
			break;
		// Checkboxes only for the leaves
		case 1:
			if ($id == "children") {
				break;
			}
		// Checkboxes for all nodes
		default:
			$element['checked'] = false;
			break;
	}
	$merges[] = $element;
}

$data = array(
	'success'	 => true,
	'children'	 => $merges
);

echo Response::Create($data);
