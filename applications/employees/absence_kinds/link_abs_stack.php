<?php

/*
 * Write/Update absence kind
 */

require_once '../../../index.php';

$absence_stack = (int) $request->absence_stack;
$absence_kinds = json_decode(stripcslashes($request->absence_kinds));

// Unlinks all Kinds of this Stack
$outLinks = array();
$links = \Employee\AbsenceKindQuery::create()
        ->filterByAbsenceStack($absence_stack)
        ->find();
foreach ($links as $link) {
    $outLinks[] = $link->getCode();
    $link->setAbsenceStack(null);
    $link->save();
}
$outLinksS = join(", ", $outLinks);

// Links selected Kinds to this Stack
$inLinks = array();
$links = \Employee\AbsenceKindQuery::create()
        ->filterByCode($absence_kinds)
        ->find();
foreach ($links as $link) {
    $inLinks[] = $link->getCode();
    $link->setAbsenceStack($absence_stack);
    $link->save();
}
$inLinksS = join(", ", $inLinks);

$delta = sprintf("%+d", (count($inLinks) - count($outLinks)));

$logger->log(array(
    'Type'    => 'INFO',
    'Scope'   => __FILE__,
    'Event'   => basename(__FILE__, ".php"),
    'Message' => "Collegamento tipi ass. a monteore: Monteore {$absence_stack} - Tipi Ass.: {$delta}",
    'Context' => print_r($request, true)
));


$data = array(
    'success' => true
);

echo Response::Create($data);
