<?php

/*
 * Read absence kinds for absence insert or absence stack linking
 *
 */

use \Employee\AbsenceKindQuery;

require_once '../../../index.php';

//$asId = (int) isset($request->get['asId']) ? $request->get['asId'] : null;
$query = (int) isset($request->get['query']) ? $request->get['query'] : null;
$linked = (int) isset($request->get['linked']) ? $request->get['linked'] : null;

$absKinds = new AbsenceKindQuery();

$absKinds->condition('dateCond1', 'date_end > now()') // Already active
		->condition('dateCond2', 'date_end IS NULL') // Never deactive
		->where(array('dateCond1', 'dateCond2'), 'or')
		->joinWith('AbsenceKindAbsenceStack', Criteria::LEFT_JOIN)
		->withColumn('AbsenceKindAbsenceStack.unit', 'unit')
		->withColumn('AbsenceKindAbsenceStack.denomination', 'stack_text')
		->select(array(
			'code',
			'description',
			'absence_stack',
			'unit',
			'stack_text',
			'calc_festivities',
			'calc_ferials'
		));

// Filters by linked status
if ($linked !== null) {
	$absKinds->where('absence_stack IS NOT NULL');
}

// Filters by absence stack
/* if ($asId !== null) {
  $absKinds->condition('stackCond1', 'absence_stack = ' . $asId)
  ->condition('stackCond2', 'absence_stack IS NULL')
  ->where(array('stackCond1', 'stackCond2'), 'or');
  } */

// Filter by Description or Code
if ($query !== null) {
	$absKinds->condition('descrCond', "description ILIKE '%" . pg_escape_string($query) . "%'")
			->condition('codeCond', "code ILIKE '%" . pg_escape_string($query) . "%'")
			->where(array('descrCond', 'codeCond'), 'or');
}

$absKinds->orderBy('description');
$absKinds = $absKinds->find();

if (!$absKinds->isEmpty()) {
	$absKinds = $absKinds->toArray(null, false, BasePeer::TYPE_FIELDNAME);
}

$data = array(
	'success'	 => true,
	'results'	 => $absKinds
);

echo Response::Create($data);
