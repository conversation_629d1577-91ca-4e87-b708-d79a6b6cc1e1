<?php

/*
 * Read employee timetables fro calendar
 */

require_once '../../../../index.php';

$month = (int) $request->month > 0 ? (int) $request->month : date("m");

$budget = new Budget();

$calendar = new Calendar();
$calendar->setDate("1-" . $month . "-" . $budget->getYear());

$start_end = $calendar->getMonthBorderExtended();

$holidays = array(
	0	 => array(
		'day'		 => 'Lun',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
	1	 => array(
		'day'		 => 'Mar',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
	2	 => array(
		'day'		 => 'Mer',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
	3	 => array(
		'day'		 => 'Gio',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
	4	 => array(
		'day'		 => 'Ven',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
	5	 => array(
		'day'		 => 'Sab',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
	6	 => array(
		'day'		 => 'Dom',
		'css_class'	 => 'week-day-style',
		'holiday'	 => false,
		'month'		 => $month
	),
);

for ($day = $start_end['start']; $day <= $start_end['end']; $day = strtotime(date("Y-m-d", $day) . ' +1 days')) {
	$class = ' day-box-active ';

	if ($calendar->isFestivity($day)) {
		$class .= ' day-box-festivity ';
	} elseif ($calendar->isWeekend($day)) {
		$class .= ' day-box-weekend ';
	}

	if ($day < strtotime("1-" . $month . "-" . $budget->getYear()) OR $day >= strtotime("1-" . $month . "-" . $budget->getYear() . " +1 month")) {
		$class = ' day-box-disabled ';
		$header = ' day-header-disabled ';
	}

	$holidays[] = array(
		'day'		 => date('j', $day),
		'month'		 => date('m', $day),
		'css_class'	 => $class,
		'css_header' => $header
	);
}

$data = array(
	'success'	 => true,
	'results'	 => $holidays
);

echo Response::Create($data);