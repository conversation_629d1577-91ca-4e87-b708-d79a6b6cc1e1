<?php

/*
 * Create/Update single holiday (day or week)
 */

require_once '../../../../index.php';

$day = (int) $request->day;
$month = (int) $request->month;

$week = array(
	'Lun'	 => 1,
	'Mar'	 => 2,
	'Mer'	 => 3,
	'Gio'	 => 4,
	'Ven'	 => 5,
	'Sab'	 => 6,
	'Dom'	 => 7
);

$msg = "";
$calendar = new Calendar();
if ($day > 0) {
	$calendar->changeDayHoliday($day, $month);
	$msg = "Cambiato giorno {$day}/{$month}";
} else {
	$calendar->changeWeekHoliday((int) $week[$request->day]);
	$msg = "Cambiati i {$request->day}";
}

$logger->log(array(
	'Type'		 => 'INFO',
	'Scope'		 => __FILE__,
	'Event'		 => basename(__FILE__, ".php"),
	'Message'	 => "Modifica calendario festività: {$msg}",
	'Context'	 => print_r($request, true)
));

echo Response::Create(array(
	'success' => true
));

