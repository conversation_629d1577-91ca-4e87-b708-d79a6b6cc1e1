<?php

/*
 * Read employee timetables
 */

require_once '../../../index.php';

$ids = trim($request->ids);
if (!$ids) {
	$ids = -1;
	$missRows = 2;
} else {
	$ids = explode(',', $ids);
	$missRows = 2 - count($ids);
}

$timetables = Employee\TimetableQuery::create()
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStart),'YYYY-MM-DD')", "date_day")
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStart),'YYYY-MM-DD HH24:MI:SS')", 'date_s')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateEnd),'YYYY-MM-DD HH24:MI:SS')", 'date_e')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStartPause),'YYYY-MM-DD HH24:MI:SS')", 'date_s_p')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateEndPause),'YYYY-MM-DD HH24:MI:SS')", 'date_e_p')
		->filterByPersonnelTimetableId($ids)
		->orderByDateStart()
		->find()
		->toArray(null, false, BasePeer::TYPE_FIELDNAME);

for ($i = 0; $i < $missRows; $i++) {
	$timetables[] = array(
		'employee_id'	 => (int) $request->employee_id,
		'date_day'		 => $request->date
	);
}

$data = array(
	'success'	 => true,
	'results'	 => $timetables
);

echo Response::Create($data);