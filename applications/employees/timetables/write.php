<?php

/*
 * Create/Update employee timetables
 */

require_once '../../../index.php';

$json = $request->json;

if (count($json) > 2) {
    $json = array($json);
}

foreach ($json as $values) {
    $day = date("Y-m-d", strtotime($values['date_day']));
    $hour_start = date("H:i", strtotime($values['date_s']));
    $hour_end = date("H:i", strtotime($values['date_e']));
    $hour_start_pause = $values['date_s_p'] == null ? null : date("H:i", strtotime($values['date_s_p']));
    $hour_end_pause = $values['date_e_p'] == null ? null : date("H:i", strtotime($values['date_e_p']));

    $new_start = strtotime($day . ' ' . $hour_start);
    $new_end = strtotime($day . ' ' . $hour_end);
    $new_start_pause = $hour_start_pause !== null ? strtotime($day . ' ' . $hour_start_pause) : null;
    $new_end_pause = $hour_end_pause !== null ? strtotime($day . ' ' . $hour_end_pause) : null;
    if ($new_end < $new_start) {
        $new_end = strtotime(date("Y-m-d H:i", $new_end) . ' +1 day');
    }

    $msg = "";
    if ($values['personnel_timetable_id']) {
        $timetableToCreate = \Employee\TimetableQuery::create()
                ->findPk((int) $values['personnel_timetable_id']);
        $msg = "Aggiornamento";
    } else {
        $timetableToCreate = new \Employee\Timetable();
        $msg = "Creazione";
    }
    $timetableToCreate->setEmployeeId($values['employee_id']);
    $timetableToCreate->setDateStart($new_start);
    $timetableToCreate->setDateEnd($new_end);
    $timetableToCreate->setDateStartPause($new_start_pause);
    $timetableToCreate->setDateEndPause($new_end_pause);
    $timetableToCreate->save();

    $ds = date("d-m-Y H:i", $timetableToCreate->getDateStart());
    $de = date("d-m-Y H:i", $timetableToCreate->getDateEnd());
    $dsp = date("d-m-Y H:i", $timetableToCreate->getDateStartPause());
    $dep = date("d-m-Y H:i", $timetableToCreate->getDateEndPause());

    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "{$msg} Orario: {$timetableToCreate->getPersonnelTimetableId()}, Personale {$timetableToCreate->getEmployeeId()} - {$ds} > {$de}, pausa {$dsp} > {$dep}",
        'Context' => print_r($request, true)
    ));
}

$data = array(
    'success' => true
);

echo Response::Create($data);
