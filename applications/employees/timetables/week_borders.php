<?php

/*
 * Return list of week intervals groupped by month to
 * copy a week to one or more of these.
 *
 */
require_once '../../../index.php';

$year = date("Y") - 1;

$start = strtotime($year . '-09-01 last monday');
$end = strtotime(($year + 2) . '-08-31 next monday');

$weeks = array();
while ($start < $end) {
	$mon = $start;
	$sun = strtotime(date('Y-m-d', $start) . ' +6 days');
	$start = strtotime(date('Y-m-d', $start) . ' +7 days');

	$weeks[] = array(
		'month_year' => date('Y', $mon) . '-' . date('m', $mon) . '-' . date('d', $mon),
		'sunday'	 => date('Y-m-d', $sun),
		'monday'	 => date('Y-m-d', $mon)
	);
}

$data = array(
	'success'	 => true,
	'results'	 => $weeks
);

echo Response::Create($data);