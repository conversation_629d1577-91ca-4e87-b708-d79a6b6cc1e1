<?php

/*
 * Destroy single day timetables in calendar
 */

require_once '../../../../index.php';

$week = (bool) $request->week;
$period = (bool) $request->period;

$timetables = new Employee\TimetableQuery();

if ($week === true || $period == true) {
    if (date('N', strtotime($request->date)) == 1) {
        $start = strtotime($request->date);
    } else {
        $start = strtotime($request->date . ' last monday');
    }
    $end = strtotime($request->date . ' next monday');
    $empId = (int) $request->employee_id;

    if ($period == true) {
        $start = strtotime($request->post['from']);
        $end = strtotime($request->post['to'] . ' +1 day');
        $empId = json_decode($request->post['employees']);
    }

    if ($empId != '' && $start != false && $end != false) {
        $timetables->filterByEmployeeId($empId);
        $timetables->filterByDateStart((int) $start, Criteria::GREATER_EQUAL);
        $timetables->filterByDateEnd((int) $end, Criteria::LESS_THAN);
    }
} else {
    $ids = trim($request->ids);
    if (!$ids) {
        $ids = -1;
    }
    $idsArr = explode(',', $ids);
    $timetables->filterByPersonnelTimetableId($idsArr);
}

$tts = $timetables->find();

foreach ($tts as $timetable) {
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Cancellazione orario: {$timetable->getPersonnelTimetableId()}, Personale {$timetable->getEmployeeId()} - dalle {$timetable->getDateStart()} alle {$timetable->getDateEnd()}, inizio pausa {$timetable->getDateStartPause()} fine pausa {$timetable->getDateEndPause()}",
        'Context' => print_r($request, true)
    ));
}

$timetables->delete();

$data = array(
    'success' => true
);

echo Response::Create($data);
