<?php

/*
 * Take the weeks and employees selected and copy the timetable hour
 * on all employee for the weeks selected.
 *
 */

require_once '../../../../index.php';


$employees = json_decode($request->employees);
$weeks = json_decode(stripcslashes($request->weeks), true);
$original_emp = (int) $request->orig_e;
$original_day = $request->orig_d;

$db = Db::getInstance();
// Get start/end of week
if (date('N', strtotime($original_day)) == 1) {
	$startW = strtotime($original_day);
} else {
	$startW = strtotime($original_day . ' last monday');
}
$endW = strtotime($original_day . ' next monday');

$original_week = Employee\TimetableQuery::create()
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStart),'YYYY-MM-DD')", 'DateStartD')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateEnd),'YYYY-MM-DD')", 'DateEndD')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStartPause),'YYYY-MM-DD')", 'DateStartPauseD')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateEndPause),'YYYY-MM-DD')", 'DateEndPauseD')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStart),'HH24:MI')", 'DateStartH')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateEnd),'HH24:MI')", 'DateEndH')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateStartPause),'HH24:MI')", 'DateStartPauseH')
		->withColumn("to_char(to_timestamp(Employee\Timetable.DateEndPause),'HH24:MI')", 'DateEndPauseH')
		->filterByEmployeeId((int) $original_emp)
		->filterByDateStart($startW, Criteria::GREATER_EQUAL)
		->filterByDateEnd($endW, Criteria::LESS_THAN)
		->orderByDateStart()
		->find();

foreach ($employees as $emp) {
	foreach ($weeks as $week) {
		$monday = strtotime($week['monday']);
		$sunday = strtotime($week['sunday']);
		// Deletes Timetables in the week
		$timetablesToDelete = Employee\TimetableQuery::create()
				->filterByEmployeeId((int) $emp)
				->filterByDateStart($monday, Criteria::GREATER_EQUAL)
				->filterByDateEnd($sunday, Criteria::LESS_THAN)
				->find();

		foreach ($timetablesToDelete as $timetable) {
			$logger->log(array(
				'Type'		 => 'INFO',
				'Scope'		 => __FILE__,
				'Event'		 => basename(__FILE__, ".php"),
				'Message'	 => "Copia settimana, cancellazione orario vecchio: {$timetable->getPersonnelTimetableId()}, Personale {$timetable->getEmployeeId()} - dalle {$timetable->getDateStart()} alle {$timetable->getDateEnd()}, inizio pausa {$timetable->getDateStartPause()} fine pausa {$timetable->getDateEndPause()}",
				'Context'	 => print_r($request, true)
			));
		}

		$timetablesToDelete->delete();

		// For each day of the week
		for ($i = $monday; $i <= $sunday; $i = strtotime(date("Y-m-d", $i) . ' +1 day')) {
			foreach ($original_week as $o_w) {
				if (date("N", $o_w->getDateStart()) == date("N", $i)) {
					// Creates timetable for the day
					$dateStart = strtotime(date('Y-m-d', $i) . ' ' . $o_w->getDateStartH());
					$dateEnd = strtotime(date('Y-m-d', $o_w->getDateStartD() == $o_w->getDateEndD() ? $i : strtotime(date("Y-m-d", $i) . ' +1 day')) . ' ' . $o_w->getDateEndH());
					$dateStartPause = null;
					$dateEndPause = null;
					if ($o_w->getDateStartPause() != null) {
						$dateStartPause = strtotime(date('Y-m-d', $o_w->getDateStartD() == $o_w->getDateStartPauseD() ? $i : strtotime(date("Y-m-d", $i) . ' +1 day')) . ' ' . $o_w->getDateStartPauseH());
					}
					if ($o_w->getDateEndPause() != null) {
						$dateEndPause = strtotime(date('Y-m-d', $o_w->getDateStartD() == $o_w->getDateEndPauseD() ? $i : strtotime(date("Y-m-d", $i) . ' +1 day')) . ' ' . $o_w->getDateEndPauseH());
					}
					$timetableToCreate = new \Employee\Timetable();
					$timetableToCreate->setEmployeeId((int) $emp);
					$timetableToCreate->setDateStart($dateStart);
					$timetableToCreate->setDateEnd($dateEnd);
					$timetableToCreate->setDateStartPause($dateStartPause);
					$timetableToCreate->setDateEndPause($dateEndPause);
					$timetableToCreate->save();

					$logger->log(array(
						'Type'		 => 'INFO',
						'Scope'		 => __FILE__,
						'Event'		 => basename(__FILE__, ".php"),
						'Message'	 => "Copia settimana, creazione orario: {$timetableToCreate->getPersonnelTimetableId()}, Personale {$timetableToCreate->getEmployeeId()} - dalle {$timetableToCreate->getDateStart()} alle {$timetableToCreate->getDateEnd()}, inizio pausa {$timetableToCreate->getDateStartPause()} fine pausa {$timetableToCreate->getDateEndPause()}",
						'Context'	 => print_r($request, true)
					));
				}
			}
		}
	}
}

$data = array(
	'success' => true
);

echo Response::Create($data);
