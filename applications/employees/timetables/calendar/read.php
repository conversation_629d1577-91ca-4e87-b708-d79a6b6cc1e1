<?php

/*
 * Read employee timetables fro calendar
 */

require_once '../../../../index.php';

$employee_id = (int) $request->employee_id;
$month = (int) $request->month > 0 ? (int) $request->month : date("m");
$year = (int) $request->year > 0 ? (int) $request->year : date("Y");

$calendar = new Calendar();
$calendar->setDate("1-" . $month . "-" . $year);
$start_end = $calendar->getMonthBorderExtended();

$locked = new ExtRecMonth($employee_id, strtotime("1-" . $month . "-" . $year));
$locked = $locked->checkStoredMonthExists($employee_id, strtotime("1-" . $month . "-" . $year)) != null ? true : false;

$timetable = array(
	0	 => array(
		'day'		 => 'Lun',
		'css_class'	 => 'week-day-style'
	),
	1	 => array(
		'day'		 => 'Mar',
		'css_class'	 => 'week-day-style'
	),
	2	 => array(
		'day'		 => 'Mer',
		'css_class'	 => 'week-day-style'
	),
	3	 => array(
		'day'		 => 'Gio',
		'css_class'	 => 'week-day-style'
	),
	4	 => array(
		'day'		 => 'Ven',
		'css_class'	 => 'week-day-style'
	),
	5	 => array(
		'day'		 => 'Sab',
		'css_class'	 => 'week-day-style'
	),
	6	 => array(
		'day'		 => 'Dom',
		'css_class'	 => 'week-day-style'
	),
);
for ($day = $start_end['start']; $day <= $start_end['end']; $day = strtotime(date("Y-m-d", $day) . ' +1 day')) {
	$timetables = Employee\TimetableQuery::create()
			->withColumn("to_char(to_timestamp(Employee\Timetable.DateStart),'HH24:MI')||' - '||to_char(to_timestamp(Employee\Timetable.DateEnd),'HH24:MI')", 'Slot')
			->where('Employee\Timetable.EmployeeId = ?', $employee_id)
			->where("to_char(to_timestamp(Employee\Timetable.DateEnd),'DD-MM-YYYY') = ?", date('d-m-Y', $day))
			->orderByDateStart()
			->orderByDateEnd()
			->find();

	$class = ' day-box-active ';

	if ($calendar->isFestivity($day)) {
		$class .= ' day-box-festivity ';
	} elseif ($calendar->isWeekend($day)) {
		$class .= ' day-box-weekend ';
	}

	if (date('m', $day) != $month) {
		$class = ' day-box-disabled ';
		$header = ' day-header-disabled ';
	}

	$timeT = $slots = $ids = array();
	if (count($timetables) > 0) {
		$timeT['locked'] = $locked;
		$timeT['day'] = date('j', $day);
		$timeT['employee_id'] = $employee_id;
		$timeT['date'] = date('Y-m-d', $day);
		$timeT['css_class'] = $class;
		$timeT['css_header'] = $header;
		foreach ($timetables as $time) {
			if ((int) $time->getDateStartPause() > 0) {
				$timeT['css_box'] = 'day-hour-box-break';
			}
			$slots[] = $time->getSlot();
			$ids[] = $time->getPersonnelTimetableId();
		}
		$timeT['personnel_timetable_id'] = join(',', $ids);
		$timeT['slots'] = join('<br />', $slots);
		$timetable[] = $timeT;
	} else {
		$empty = array(
			'locked'		 => $locked,
			'day'			 => date('j', $day),
			'css_class'		 => $class,
			'css_header'	 => $header,
			'css_box'		 => 'day-hour-box-hide',
			'date'			 => date('Y-m-d', $day),
			'employee_id'	 => $employee_id
		);
		$timetable[] = $empty;
	}
}

$data = array(
	'success'	 => true,
	'results'	 => $timetable
);

echo Response::Create($data);