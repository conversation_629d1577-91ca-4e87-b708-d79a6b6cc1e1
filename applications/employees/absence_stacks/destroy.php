<?php

/*
 * Delete absence stack
 */

require_once '../../../index.php';

$stack_id = (int) $request->id;


$db = Db::getInstance();
$r = $db->query_params("SELECT denomination FROM absence_stack WHERE id = $1", array($stack_id));
if ($r) {
    $stack = $db->fetchAll();
    $db->query_params("DELETE FROM absence_stack WHERE id = $1", array($stack_id));
    $db->query_params("DELETE FROM personnel_stacks WHERE stack_id = $1", array($stack_id));

    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Cancellazione monteore: {$stack[0]['denomination']}",
        'Context' => print_r($request, true)
    ));
}

$data = array(
    'success' => true
);

echo Response::Create($data);
