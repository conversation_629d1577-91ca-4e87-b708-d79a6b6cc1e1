<?php

/*
 * Write absence stacks personnel links
 */

require_once '../../../../index.php';

$id = (int) $request->id > 0 ? (int) $request->id : null;
$reset_quota = $request->reset_quota == '' ? 0 : str_replace(",", ".", $request->reset_quota);


if ($id !== null) {
    $db = Db::getInstance();
    $db->query_params("UPDATE personnel_stacks SET reset_quota = $1 WHERE id = $2", array($reset_quota, $id));
}

$logger->log(array(
    'Type'    => 'INFO',
    'Scope'   => __FILE__,
    'Event'   => basename(__FILE__, ".php"),
    'Message' => ($id !== null ? 'Aggiornamento' : 'ERRORE agg.' ) . " quota personale {$request->employee_id} monteore {$request->stack_id}: {$reset_quota}",
    'Context' => print_r($request, true)
));

$data = array(
    'success' => true
);

echo Response::Create($data);
