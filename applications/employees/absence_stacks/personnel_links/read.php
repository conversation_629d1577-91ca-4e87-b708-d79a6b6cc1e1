<?php

/*
 * Read absence stacks personnel links
 */

require_once '../../../../index.php';

$employee_id = (int) $request->employee > 0 ? (int) $request->employee : null;
$items = false;


if ($employee_id !== null) {
    $db = Db::getInstance();
    $db->query("SELECT s.denomination, s.unit, ps.* FROM personnel_stacks AS ps LEFT JOIN absence_stack AS s ON s.id = ps.stack_id WHERE ps.employee_id = {$employee_id} ORDER BY s.recover DESC, s.denomination");
    $items = $db->fetchAll();
}

if (!$items) {
    $items = array();
} else {
    foreach ($items as $k => $i) {
        $items[$k]['id'] = (int) $i['id'];
        $items[$k]['employee_id'] = (int) $i['employee_id'];
        $items[$k]['stack_id'] = (int) $i['stack_id'];
        $items[$k]['reset_quota'] = $i['reset_quota'];
    }
}

$data = array(
    'success' => true,
    'results' => $items
);

echo Response::Create($data);
