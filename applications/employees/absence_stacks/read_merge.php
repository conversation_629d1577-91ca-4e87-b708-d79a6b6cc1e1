<?php

/*
 * Read absence stacks
 */

require_once '../../../index.php';

$db = Db::getInstance();
$db->query("SELECT a1.*, (SELECT a2.denomination AS reset_to_stack_denomination FROM absence_stack AS a2 WHERE a2.id = a1.reset_to_stack_id LIMIT 1) AS reset_to_stack_denomination FROM absence_stack AS a1 ORDER BY recover DESC, denomination");
$stacks = $db->fetchAll();

if (!$stacks) {
    $stacks = array();
} else {
    foreach ($stacks as $k => $s) {
        if ($s['reset_date'] !== '') {
            $s['reset_date'] = explode(" ", $s['reset_date']);
            $s['reset_date'] = $s['reset_date'][0];
        } else {
            $s['reset_date'] = null;
        }
        $stacks[$k]['id'] = (int) $s['id'];
        $stacks[$k]['recover'] = $s['recover'] == 't' ? true : false;
        $stacks[$k]['reset_type'] = (int) $s['reset_type'];
        $stacks[$k]['reset_date'] = $s['reset_date'];
        $stacks[$k]['reset_default_quota'] = $s['reset_default_quota'];
        $stacks[$k]['reset_to_stack_id'] = $s['reset_to_stack_id'] ? (int) $s['reset_to_stack_id'] : null;
    }

    array_unshift($stacks, array(
        'id'                          => -1,
        'unit'                        => 'h',
        'denomination'                => 'Residui',
        'recover'                     => false,
        'reset_type'                  => STACK_RESET_MANUAL,
        'reset_date'                  => null,
        'reset_default_quota'         => 0,
        'reset_to_stack_id'           => null,
        'reset_to_stack_denomination' => ''
    ));
}

$data = array(
    'success' => true,
    'results' => $stacks
);

echo Response::Create($data);
