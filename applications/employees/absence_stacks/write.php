<?php

/*
 * Write absence stacks personnel links
 */

require_once '../../../index.php';

$stack_id = (int) $request->id > 0 ? (int) $request->id : false;
$reset_date = $request->reset_date; //!== null ? "'{$request->reset_date}'" : null;
$reset_to_stack_id = (int) $request->reset_to_stack_id > 0 ? (int) $request->reset_to_stack_id : null;
$reset_default_quota = $request->reset_default_quota !== null ? str_replace(",", ".", $request->reset_default_quota) : 0;


$db = Db::getInstance();
$update = $stack_id ? true : false;

$params = array(
    $request->unit,
    $request->denomination,
    $request->recover,
    (int) $request->reset_type,
    $reset_date,
    $reset_default_quota
);

// Stacks logic
if ($update) {
    $params[] = $stack_id;
    $db->query_params("UPDATE absence_stack SET "
            . "unit = $1, "
            . "denomination = $2, "
            . "recover = $3, "
            . "reset_type = $4, "
            . "reset_date = $5, "
//            . "reset_to_stack_id = $7, "
            . "reset_default_quota = $6 "
            . "WHERE id = $7", $params);
} else {
    $params[] = $reset_to_stack_id;
    $db->query_params("INSERT INTO absence_stack (unit, denomination, recover, reset_type, reset_date, reset_default_quota, reset_to_stack_id) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id", $params);
    $stack_id = $db->fetchAll();
    if ($stack_id) {
        $stack_id = $stack_id[0]['id'];
    }
}

// Personnel - Stacks link logic
if ((int) $request->reset_type === STACK_RESET_MANUAL) {
    $db->query_params("DELETE FROM personnel_stacks WHERE stack_id = $1", array($stack_id));
} else {
    $r = $db->query_params("SELECT id FROM personnel_stacks WHERE stack_id = $1", array($stack_id));
    $ps_id = $r ? $db->fetchAll() : false;
    if ($ps_id === false || !$update) {
        $db->query("SELECT employee_id FROM employee");
        $emps = $db->fetchAll();
        if ($emps) {
            foreach ($emps as $emp) {
                $db->query_params("INSERT INTO personnel_stacks (employee_id, stack_id, reset_quota) VALUES ($1, $2, $3)", array($emp['employee_id'], $stack_id, $reset_default_quota));
            }
        }
    } elseif ($update && $request->update_quota === 'on') {
        $db->query_params("UPDATE personnel_stacks SET reset_quota = $1 WHERE stack_id = $2", array($reset_default_quota, $stack_id));
    }
}

// Logging
$logger->log(array(
    'Type'    => 'INFO',
    'Scope'   => __FILE__,
    'Event'   => basename(__FILE__, ".php"),
    'Message' => ($update ? 'Aggiornamento' : 'Creazione' ) . " monteore: {$request->denomination}",
    'Context' => print_r($request, true)
));

$data = array(
    'success' => true
);

echo Response::Create($data);
