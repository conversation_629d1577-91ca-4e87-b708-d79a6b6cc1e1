<?php

/*
 * Read employees
 */

require_once '../../../index.php';


$employee_id = (int) $request->employee_id > 0 ? (int) $request->employee_id : -1;
$month = (int) $request->month > 0 ? (int) $request->month : date('m');
$year = (int) $request->year > 0 ? (int) $request->year : date('Y');
$start = strtotime("1-" . $month . "-" . $year);
$end = strtotime("1-" . $month . "-" . $year . ' +1 month');

$presences = Employee\PresenceQuery::create()
		->withColumn("to_char(to_timestamp(Employee\Presence.DateEdit),'YYYY-MM-DD')", "date_day")
		->withColumn("to_char(to_timestamp(Employee\Presence.DateEdit),'HH24:MI')", "date_hour")
		->withColumn("true", "error")
		->filterByEmployeeId($employee_id)
		->filterByDateEdit($start, Criteria::GREATER_EQUAL)
		->filterByDateEdit($end, Criteria::LESS_THAN)
		->orderByDateEdit()
		->find();

$employee = Employee\EmployeeQuery::create()
		->filterByEmployeeId($employee_id)
		->findOne();
$ttp = (int) $employee->getMaxContWork() * 60;

if (!$presences->isEmpty()) {
	$errors = array();
	// Checks for Presences sequence errors
	foreach ($presences as $k => $p) {
		$current = $p;
		$previous = $presences[($k - 1)];
		$next = $presences[($k + 1)];
		$error = 0;

		// Checks current against next
		if ($next) {
			if ((int) $current->getOriginalInOutEdit() == (int) $next->getOriginalInOutEdit()) {
				if ($current->getOriginalInOutEdit() == PRESENCE_ENTRANCE) {
					$error = 1;
				} else {
					if (date('d', $current->getDateEdit()) == date('d', $next->getDateEdit())) {
						$error = 1;
					} else {
						$error = 0;
					}
				}
			} else {
				if ($current->getOriginalInOutEdit() == PRESENCE_ENTRANCE AND date('d', $current->getDateEdit()) != date('d', $next->getDateEdit()) AND ($next->getDateEdit() - $current->getDateEdit()) > $ttp) {
					$error = 1;
				}
			}
		}

		// Checks current against previous
		if ($previous) {
			if ((int) $current->getOriginalInOutEdit() == (int) $previous->getOriginalInOutEdit()) {
				if ($current->getOriginalInOutEdit() == PRESENCE_ENTRANCE) {
					if (date('d', $current->getDateEdit()) == date('d', $previous->getDateEdit())) {
						$error = 1;
					} else {
						$error = 0;
					}
				} else {
					$error = 1;
				}
			} else {
				if ($current->getOriginalInOutEdit() == PRESENCE_EXIT AND date('d', $current->getDateEdit()) != date('d', $previous->getDateEdit()) AND ($current->getDateEdit() - $previous->getDateEdit()) > $ttp) {
					$error = 1;
				}
			}
		}

		// Checks for auto-inserted Presence
		if ((int) $current->getDate() < 1) {
			$error = 2;
		}
		$errors[$k] = $error;
	}
	$presences = $presences->toArray(null, false, BasePeer::TYPE_FIELDNAME);
	$locked = new ExtRecMonth($employee_id, strtotime("1-" . $month . "-" . $year));
	$locked = $locked->checkStoredMonthExists($employee_id, strtotime("1-" . $month . "-" . $year)) != null ? true : false;
	foreach ($presences as $k => $p) {
		$presences[$k]['error'] = $errors[$k];
		$presences[$k]['locked'] = $locked;
	}
} else {
	$presences = array();
}

$data = array(
	'success'	 => true,
	'results'	 => $presences
);

echo Response::Create($data);