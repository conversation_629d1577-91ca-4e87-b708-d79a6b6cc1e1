<?php

/*
 * Create/Update single presences
 */

require_once '../../../index.php';

// Load data into presences table
$pId = (int) $request->personnel_presence_id > 0 ? (int) $request->personnel_presence_id : null;
$datetime = strtotime($request->date_day . ' ' . $request->date_hour);

$onlyNotes = true;
$new = false;

$presence = Employee\PresenceQuery::create()
        ->filterByPersonnelPresenceId($pId)
        ->findOne();
if (!$presence) {
    $onlyNotes = false;
    $new = true;
    $presence = new Employee\Presence();
    $presence->setDate($datetime);
    $presence->setType($request->type_edit);
    $presence->setOriginalInOut($request->original_inout_edit);
} else {
    if ((int) $presence->getDateEdit() != $datetime || $presence->getTypeEdit() != (int) $request->type_edit || $presence->getOriginalInOutEdit() != (int) $request->original_inout_edit) {
        $onlyNotes = false;
    }
}
$presence->setDateEdit($datetime);
$presence->setTypeEdit($request->type_edit);
$presence->setOriginalInOutEdit($request->original_inout_edit);
$presence->setEmployeeId($request->employee_id);
if ($new || (!$new && !$onlyNotes)) {
    $presence->setInsertionMode('M');
}
$presence->setDescription($request->description);
// Updates Original date for Empty BIP (only the first time)
if ($presence->getDate() == 0) {
    $presence->setDate($datetime);
}
$presence->save();

$date = date("d-m-Y H:i", $presence->getDateEdit());
$type = $presence->getTypeEdit() == 1 ? 'Normale' : 'P.Pranzo/Servizio';
$io = $presence->getOriginalInOutEdit() == 1 ? 'Entrata' : 'Uscita';

$logger->log(array(
    'Type'    => 'INFO',
    'Scope'   => __FILE__,
    'Event'   => basename(__FILE__, ".php"),
    'Message' => ($pId > 0 ? 'Aggiornamento' : 'Creazione') . " presenza: {$presence->getPersonnelPresenceId()} {$io} {$type} del {$date} - personale {$presence->getEmployeeId()}",
    'Context' => print_r($request, true)
));

echo Response::Create(array(
    'success' => true
));
