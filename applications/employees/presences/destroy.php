<?php

/*
 * Delete single presence
 */

require_once '../../../index.php';

// Load data into presence and delete it
$presenceTable = Employee\PresencePeer::getTableMap();
$presence = \Employee\PresenceQuery::create()
		->filterByPersonnelPresenceId((int) $request->personnel_presence_id)
		->findOne();

$date = date("d-m-Y H:i", $presence->getDateEdit());
$type = $presence->getTypeEdit() == 1 ? 'Normale' : 'P.Pranzo/Servizio';
$io = $presence->getOriginalInOutEdit() == 1 ? 'Entrata' : 'Uscita';

$logger->log(array(
	'Type'		 => 'INFO',
	'Scope'		 => __FILE__,
	'Event'		 => basename(__FILE__, ".php"),
	'Message'	 => "Cancellazione presenza: {$presence->getPersonnelPresenceId()} {$io} {$type} del {$date} - personale {$presence->getEmployeeId()}",
	'Context'	 => print_r($request, true)
));

$presence->delete();

echo Response::Create(array(
	'success' => true
));
