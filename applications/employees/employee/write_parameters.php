<?php

/*
 * Create/Update employee(s) parameters
 */

require_once '../../../index.php';


// Load data into employee
$employee_ids = json_decode($request->post['employee_ids']);

$employees = new Employee\EmployeeQuery();
$employees->filterByEmployeeId($employee_ids);
$employees = $employees->find();

foreach ($employees as $employee) {
    $employee->setBreakAfterMaxWork($request->break_after_max_work);
    $employee->setMaxContWork($request->max_cont_work);
    $employee->setMaxExtraordinaryIn($request->max_extraordinary_in);
    $employee->setMaxExtraordinaryOut($request->max_extraordinary_out);
    $employee->setMaxExtraordinaryTotal($request->max_extraordinary_total);
    $employee->setMaxUndefinedIn($request->max_undefined_in);
    $employee->setMaxUndefinedOut($request->max_undefined_out);
    $employee->setMaxUndefinedTotal($request->max_undefined_total);
    $employee->setMinExtraordinaryIn($request->min_extraordinary_in);
    $employee->setMinExtraordinaryOut($request->min_extraordinary_out);
    $employee->setMinExtraordinaryTotal($request->min_extraordinary_total);
    $employee->setMinUndefinedIn($request->min_undefined_in);
    $employee->setMinUndefinedOut($request->min_undefined_out);
    $employee->setMinUndefinedTotal($request->min_undefined_total);
    $employee->setRecoverHours($request->recover_hours);
    $employee->setStepIn($request->step_in);
    $employee->setStepInUnd($request->step_in_und);
    $employee->setStepOut($request->step_out);
    $employee->setStepOutUnd($request->step_out_und);
    $employee->setStepTotalExtraordinary($request->step_total_extraordinary);
    $employee->setStepTotalUndefined($request->step_total_undefined);
    $employee->setUnitRecoverHours($request->unit_recover_hours);
    $employee->setMaxWork($request->max_work);
    $employee->save();

    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Aggiornamento parametri personale: {$employee->getSurname()} {$employee->getName()}",
        'Context' => print_r($request, true)
    ));
}

echo Response::Create(array(
    'success' => true,
    'results' => array()
));
