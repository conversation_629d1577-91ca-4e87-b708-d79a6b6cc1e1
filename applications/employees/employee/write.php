<?php

/*
 * Create/Update single employee
 */

require_once '../../../index.php';



$db = Db::getInstance();

// Load data into employee
$employee_id = (int) $request->employee_id > 0 ? (int) $request->employee_id : null;
$employee = Employee\EmployeeQuery::create()->findPk($employee_id);
if ($employee_id == null) {
	$employee = new Employee\Employee();
}
$request->birthdate = $request->birthdate != "" ? strtotime($request->birthdate) : null;
$request->active = isset($request->active) ? true : false;

$employee->setLiquidGroup($request->liquid_group);
$employee->setActive($request->active);
$employee->setSurname($request->surname);
$employee->setName($request->name);
$employee->setBirthdate($request->birthdate);
$employee->setBirthplace((int) $request->birthplace);
$employee->setStateBirth($request->state_birth);
$employee->setGender($request->gender);
$employee->setSocialPosition((int) $request->social_position);
$employee->setFiscalCode($request->fiscal_code);
$employee->setBadgeNumber((int) $request->badge_number);



$contactData = [];
$contact_type = \Database\Core\CoreContactTypeQuery::create()->findOneByCode( 'Employee');
if ( is_object( $contact_type ) ){
	$contactData['contact_type_id'] = $contact_type->getId();

}


// Residence
$residence_id = (int) $request->residence_id > 0 ? (int) $request->residence_id : null;
$resArr = array(
	'address'	 => $request->res_address,
	'phone_num'	 => $request->res_phone_num,
	'fax'		 => $request->res_fax,
	'mobile'	 => $request->res_mobile,
	'cap'		 => $request->res_cap,
	'email'		 => $request->res_email,
	'city_id'	 => (int) $request->res_city_id > 0 ? (int) $request->res_city_id : null
);

if ( $request->res_email ) {
	$check = Core\ContactQuery::create()->filterByEmail( $request->res_email)->findOne();
	if ( is_object( $check ) && ($check->getContactId() != $residence_id) ) {
		if( $check->getContactId() != $residence_id  ){
			echo Response::Create(array(
				'success'	 => false,
				'message'	 => 'Email duplicata'
			));
			exit;
		}
	}
}



$contactData[ 'name' ] = $employee->getName() . ' ' . $employee->getSurname();
$contactData[ 'email' ] = $resArr[ 'email'];



$residence = Core\ContactQuery::create()->findPk($residence_id);

if ($residence == null) {
	$residence = new Core\Contact();
} else {
	$oldEmail = $residence->getEmail();


	$contact = \Database\Core\CoreContactsQuery::create()->filterByEmail( $oldEmail )->findOne();


	if ( is_object( $contact) && $contact->getId()){
		$contactData[ 'id' ] = $contact->getId();
	}
}

$contact = \Database\Core\CoreContactsQuery::create()->addNonExisting( $contactData, $force );
if ( ! $contact && $resArr['email']){
	echo Response::Create(array(
		'success'	 => false,
		'message'	 => 'Impossibile salvare i dati'
	));
	exit;
}

$residence->fromArray($resArr, \BasePeer::TYPE_FIELDNAME);
$residence->save();

$employee->setResidenceId($residence->getContactId());

// Dom
$address_id = (int) $request->address_id > 0 ? (int) $request->address_id : null;
$addArr = array(
	'address'	 => $request->add_address,
	'phone_num'	 => $request->add_phone_num,
	'fax'		 => $request->add_fax,
	'mobile'	 => $request->add_mobile,
	'cap'		 => $request->add_cap,
	'email'		 => $request->add_email,
	'city_id'	 => (int) $request->add_city_id ? (int) $request->add_city_id : null
);
$address = Core\ContactQuery::create()->findPk($address_id);
if ($address == null) {
	$address = new Core\Contact();
}
$address->fromArray($addArr, \BasePeer::TYPE_FIELDNAME);
$address->save();

$employee->setAddressId($address->getContactId());

// Salve employee data with updated address and residence data
$employee->save();

$results = $employee->toArray(BasePeer::TYPE_FIELDNAME, false, null);
$results['denomination'] = $results['surname'] . ' ' . $results['name'];
$results['birthdate'] = date('c', $employee->getBirthdate());

$r = $db->query_params("SELECT * FROM absence_stack WHERE id NOT in (SELECT stack_id FROM personnel_stacks WHERE employee_id = $1)", [$employee->getEmployeeId()]);
$absences_stack = $r ? $db->fetchAll() : false;

foreach ($absences_stack as $as){
    $db->query_params("INSERT INTO personnel_stacks (employee_id, stack_id, reset_quota) VALUES ($1, $2, $3)", [$employee->getEmployeeId(), $as['id'], $as['reset_default_quota']]);
}

$logger->log(array(
	'Type'		 => 'INFO',
	'Scope'		 => __FILE__,
	'Event'		 => basename(__FILE__, ".php"),
	'Message'	 => ($employee_id > 0 ? 'Aggiornamento' : 'Creazione' ) . " personale: {$employee->getSurname()} {$employee->getName()}",
	'Context'	 => print_r($request, true)
));

echo Response::Create(array(
	'success'	 => true,
	'results'	 => $results
));
