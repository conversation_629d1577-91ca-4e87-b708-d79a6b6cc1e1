<?php

/*
 * Read employees
 */

require_once '../../../index.php';

$id = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_STRING);
$active = filter_input(INPUT_GET, 'active', FILTER_SANITIZE_STRING) == 'true' ? true : false;

// Parent or Node query
if ($id == "children") {
    $sql = "SELECT distinct(liquid_group) FROM employee";
    if ($active === true) {
        $sql .= " WHERE active = 't'";
    }
    $sql .= " ORDER BY liquid_group DESC;";
} else {
    $trimmedId = str_replace("children/", "", $id);
    $sql = "SELECT employee_id, active, surname, name, liquid_group"
            . " FROM employee";
    if ($trimmedId != "0001" && $trimmedId != "0002") {
        $sql .= " WHERE ((liquid_group != '0001' AND liquid_group != '0002') OR (liquid_group IS NULL))";
    } else {
        $sql .= " WHERE liquid_group = '{$trimmedId}'";
    }
    if ($active === true) {
        $sql .= " AND active = 't'";
    }
    $sql .= " ORDER BY active DESC, surname, name;";
}

$db = Db::getInstance();
$db->query($sql);
$employees = $db->fetchAll();

// Parent or Node array creation
$merges = array();
foreach ($employees as $employee) {
    $group = "Non definiti";
    if ($employee['liquid_group'] == "0001") {
        $group = "Docenti";
    } else if ($employee['liquid_group'] == "0002") {
        $group = "ATA";
    } else {
        $employee['liquid_group'] = '0000';
    }
    if ($id == "children") {
        // Parents type are taken only once
        foreach ($merges as $record) {
            if ($record['id'] == "children/{$employee['liquid_group']}") {
                continue 2;
            }
        }
        $merges[] = array(
            'id'       => "children/{$employee['liquid_group']}",
            'surname'  => $group,
            'expanded' => true,
            'checked'  => false
        );
    } else {
        $e = $employee;
        $e['id'] = "children/{$employee['liquid_group']}/{$employee['employee_id']}";
        $e['leaf'] = true;
        $e['checked'] = false;
        $merges[] = $e;
    }
}

$data = array(
    'success'  => true,
    'children' => $merges
);

echo Response::Create($data);
