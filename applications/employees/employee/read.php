<?php

/*
 * Read employees
 */

require_once '../../../index.php';

$words = isset($request->q_name) ? $request->q_name : null;
if ($words) {
    $words = explode(" ", $words);
    foreach ($words as $key => $word) {
        $words[$key] = "%" . $word . "%";
    }
}

$c = new Criteria();

// JOINS
$c->addAlias('res', Core\ContactPeer::TABLE_NAME);
$c->addJoin(\Employee\EmployeePeer::RESIDENCE_ID, Core\ContactPeer::alias('res', Core\ContactPeer::CONTACT_ID), Criteria::LEFT_JOIN);
$c->addAlias('add', Core\ContactPeer::TABLE_NAME);
$c->addJoin(\Employee\EmployeePeer::ADDRESS_ID, Core\ContactPeer::alias('add', Core\ContactPeer::CONTACT_ID), Criteria::LEFT_JOIN);
$c->addAlias('cres', Core\CitiesPeer::TABLE_NAME);
$c->addJoin(Core\ContactPeer::alias('res', Core\ContactPeer::CITY_ID), Core\CitiesPeer::alias('cres', Core\CitiesPeer::CITY_ID), Criteria::LEFT_JOIN);
$c->addAlias('cadd', Core\CitiesPeer::TABLE_NAME);
$c->addJoin(Core\ContactPeer::alias('add', Core\ContactPeer::CITY_ID), Core\CitiesPeer::alias('cadd', Core\CitiesPeer::CITY_ID), Criteria::LEFT_JOIN);

// WHERES
if (isset($request->active)) {
    $c->addAnd(\Employee\EmployeePeer::ACTIVE, (bool) $request->active, Criteria::EQUAL);
}

if ($request->q_liquid_group == 'teacher') {
    $c->addAnd(\Employee\EmployeePeer::LIQUID_GROUP, '0001', Criteria::EQUAL);
} else if ($request->q_liquid_group == 'ata') {
    $c->addAnd(\Employee\EmployeePeer::LIQUID_GROUP, '0002', Criteria::EQUAL);
}

if ($request->q_id) {
    $c->addAnd(\Employee\EmployeePeer::EMPLOYEE_ID, $request->q_id, Criteria::EQUAL);
} 

if ($words) {
    $w_words = array();
    foreach ($words as $key => $word) {
        $w_words[$key] = $c->getNewCriterion(\Employee\EmployeePeer::SURNAME, $word, Criteria::ILIKE);
        $w_words[$key]->addOr($c->getNewCriterion(\Employee\EmployeePeer::NAME, $word, Criteria::ILIKE));
    }
    $w_w = $w_words[0];
    for ($i = 1; $i < count($w_words); $i++) {
        $w_w->addOr($w_words[$i]);
    }
    $c->addAnd($w_w);
}

// ORDERS
$c->addDescendingOrderByColumn(\Employee\EmployeePeer::ACTIVE);
$c->addAscendingOrderByColumn(\Employee\EmployeePeer::SURNAME);
$c->addAscendingOrderByColumn(\Employee\EmployeePeer::NAME);

// FIELDS
$c->clearSelectColumns();

// Employee
$c->addSelectColumn(\Employee\EmployeePeer::EMPLOYEE_ID);
$c->addSelectColumn(\Employee\EmployeePeer::ACTIVE);
$c->addSelectColumn(\Employee\EmployeePeer::SURNAME);
$c->addSelectColumn(\Employee\EmployeePeer::NAME);
$c->addSelectColumn(\Employee\EmployeePeer::GENDER);
$c->addSelectColumn(\Employee\EmployeePeer::BIRTHDATE);
$c->addSelectColumn(\Employee\EmployeePeer::BIRTHPLACE);
$c->addSelectColumn(\Employee\EmployeePeer::STATE_BIRTH);
$c->addSelectColumn(\Employee\EmployeePeer::FISCAL_CODE);
$c->addSelectColumn(\Employee\EmployeePeer::BADGE_NUMBER);
$c->addSelectColumn(\Employee\EmployeePeer::LIQUID_GROUP);
$c->addSelectColumn(\Employee\EmployeePeer::SOCIAL_POSITION);
$c->addSelectColumn(\Employee\EmployeePeer::RESIDENCE_ID);
$c->addSelectColumn(\Employee\EmployeePeer::ADDRESS_ID);

// Parameters
$c->addSelectColumn(\Employee\EmployeePeer::MAX_EXTRAORDINARY_IN);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_EXTRAORDINARY_OUT);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_EXTRAORDINARY_TOTAL);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_UNDEFINED_IN);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_UNDEFINED_OUT);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_UNDEFINED_TOTAL);
$c->addSelectColumn(\Employee\EmployeePeer::MIN_EXTRAORDINARY_IN);
$c->addSelectColumn(\Employee\EmployeePeer::MIN_EXTRAORDINARY_OUT);
$c->addSelectColumn(\Employee\EmployeePeer::MIN_EXTRAORDINARY_TOTAL);
$c->addSelectColumn(\Employee\EmployeePeer::MIN_UNDEFINED_IN);
$c->addSelectColumn(\Employee\EmployeePeer::MIN_UNDEFINED_OUT);
$c->addSelectColumn(\Employee\EmployeePeer::MIN_UNDEFINED_TOTAL);
$c->addSelectColumn(\Employee\EmployeePeer::STEP_IN);
$c->addSelectColumn(\Employee\EmployeePeer::STEP_IN_UND);
$c->addSelectColumn(\Employee\EmployeePeer::STEP_OUT);
$c->addSelectColumn(\Employee\EmployeePeer::STEP_OUT_UND);
$c->addSelectColumn(\Employee\EmployeePeer::STEP_TOTAL_EXTRAORDINARY);
$c->addSelectColumn(\Employee\EmployeePeer::STEP_TOTAL_UNDEFINED);
$c->addSelectColumn(\Employee\EmployeePeer::RECOVER_HOURS);
$c->addSelectColumn(\Employee\EmployeePeer::UNIT_RECOVER_HOURS);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_CONT_WORK);
$c->addSelectColumn(\Employee\EmployeePeer::BREAK_AFTER_MAX_WORK);
$c->addSelectColumn(\Employee\EmployeePeer::MAX_WORK);

// Residence
$c->addAsColumn('res_address', Core\ContactPeer::alias('res', Core\ContactPeer::ADDRESS));
$c->addAsColumn('res_city_id', Core\ContactPeer::alias('res', Core\ContactPeer::CITY_ID));
$c->addAsColumn('res_phone_num', Core\ContactPeer::alias('res', Core\ContactPeer::PHONE_NUM));
$c->addAsColumn('res_fax', Core\ContactPeer::alias('res', Core\ContactPeer::FAX));
$c->addAsColumn('res_email', Core\ContactPeer::alias('res', Core\ContactPeer::EMAIL));
$c->addAsColumn('res_mobile', Core\ContactPeer::alias('res', Core\ContactPeer::MOBILE));
$c->addAsColumn('res_web', Core\ContactPeer::alias('res', Core\ContactPeer::WEB));
$c->addAsColumn('res_cap', Core\ContactPeer::alias('res', Core\ContactPeer::CAP));

// Residence City
$c->addAsColumn('res_description', Core\CitiesPeer::alias('cres', Core\CitiesPeer::DESCRIPTION));
$c->addAsColumn('res_city_code', Core\CitiesPeer::alias('cres', Core\CitiesPeer::CITY_CODE));
$c->addAsColumn('res_province', Core\CitiesPeer::alias('cres', Core\CitiesPeer::PROVINCE));
$c->addAsColumn('res_region', Core\CitiesPeer::alias('cres', Core\CitiesPeer::REGION));
$c->addAsColumn('res_is_city', Core\CitiesPeer::alias('cres', Core\CitiesPeer::IS_CITY));

// Address
$c->addAsColumn('add_address', Core\ContactPeer::alias('add', Core\ContactPeer::ADDRESS));
$c->addAsColumn('add_city_id', Core\ContactPeer::alias('add', Core\ContactPeer::CITY_ID));
$c->addAsColumn('add_phone_num', Core\ContactPeer::alias('add', Core\ContactPeer::PHONE_NUM));
$c->addAsColumn('add_fax', Core\ContactPeer::alias('add', Core\ContactPeer::FAX));
$c->addAsColumn('add_email', Core\ContactPeer::alias('add', Core\ContactPeer::EMAIL));
$c->addAsColumn('add_mobile', Core\ContactPeer::alias('add', Core\ContactPeer::MOBILE));
$c->addAsColumn('add_web', Core\ContactPeer::alias('add', Core\ContactPeer::WEB));
$c->addAsColumn('add_cap', Core\ContactPeer::alias('add', Core\ContactPeer::CAP));

// Address City
$c->addAsColumn('add_description', Core\CitiesPeer::alias('cadd', Core\CitiesPeer::DESCRIPTION));
$c->addAsColumn('add_city_code', Core\CitiesPeer::alias('cadd', Core\CitiesPeer::CITY_CODE));
$c->addAsColumn('add_province', Core\CitiesPeer::alias('cadd', Core\CitiesPeer::PROVINCE));
$c->addAsColumn('add_region', Core\CitiesPeer::alias('cadd', Core\CitiesPeer::REGION));
$c->addAsColumn('add_is_city', Core\CitiesPeer::alias('cadd', Core\CitiesPeer::IS_CITY));

$params = array();
$sql = BasePeer::createSelectSql($c, $params);

foreach ($params as $key => $value) {
    if ($value['column'] == 'active') {
        $value['value'] = $value['value'] ? 't' : 'f';
    }
    $sql = str_replace(':p' . ($key + 1), "'" . $value['value'] . "'", $sql);
}

$_db = \Propel::getConnection('mc2api');
$stmt = $_db->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(\PDO::FETCH_ASSOC);

// Retrieves possible employee's job inside the school
$db = Db::getInstance();
$db->query("SELECT job_director_id, job_vice_director_id, job_dsga_id, job_personnel_id, job_accounting_id, job_warehouse_id, job_registry_id FROM institute WHERE def = 't';");
$institute = $db->fetchAll();
$institute = $institute[0];

// Fixes to Employee model
if (!empty($employees)) {
    foreach ($employees as $key => $employee) {
        // Fixes birthdate
        $employees[$key]['birthdate'] = $employee['birthdate'] != null ? date("d/m/Y", $employee['birthdate']) : '';
        // Fixes liquid group
        if ($employee['liquid_group'] != '0001' && $employee['liquid_group'] != '0002') {
            $employees[$key]['liquid_group'] = '0000';
        }
        // Checks jobs
        $employees[$key]['job'] = '';
        if ($employee['employee_id'] == $institute['job_director_id']) {
            $employees[$key]['job'] .= 'D';
            $employees[$key]['job_description'][] = 'Preside';
        }
        if ($employee['employee_id'] == $institute['job_vice_director_id']) {
            $employees[$key]['job'] .= 'V';
            $employees[$key]['job_description'][] = 'Vice Preside';
        }
        if ($employee['employee_id'] == $institute['job_dsga_id']) {
            $employees[$key]['job'] .= 'E';
            $employees[$key]['job_description'][] = 'DSGA';
        }
        if ($employee['employee_id'] == $institute['job_personnel_id']) {
            $employees[$key]['job'] .= 'P';
            $employees[$key]['job_description'][] = 'Resp. Segreteria';
        }
        if ($employee['employee_id'] == $institute['job_accounting_id']) {
            $employees[$key]['job'] .= 'A';
            $employees[$key]['job_description'][] = 'Resp. Contabilità';
        }
        if ($employee['employee_id'] == $institute['job_warehouse_id']) {
            $employees[$key]['job'] .= 'W';
            $employees[$key]['job_description'][] = 'Resp. Acquisti / Magazzino / Inventario';
        }
        if ($employee['employee_id'] == $institute['job_registry_id']) {
            $employees[$key]['job'] .= 'R';
            $employees[$key]['job_description'][] = 'Resp. Registro / Protocolli';
        }
        if (count($employees[$key]['job_description']) > 0) {
            $employees[$key]['job_description'] = join(" - ", $employees[$key]['job_description']);
        } else {
            $employees[$key]['job_description'] = '';
        }
    }
}

$data = array(
    'success' => true,
    'results' => $employees
);

echo Response::Create($data);
