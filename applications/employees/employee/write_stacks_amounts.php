<?php

/*
 * Copies employee Stacks Amounts to other employees
 */

require_once '../../../index.php';


// Load data into employee
$id = $request->post['id'];
$employee_ids = json_decode($request->post['employee_ids']);

$emps = is_array($employee_ids) ? implode(", ", $employee_ids) : $employee_ids;

$stack = new Employee\PersonnelStacksQuery();
$stack->filterById($id);
$stack = $stack->findOne();

if ($stack !== null) {
    $quota = $stack->getResetQuota();
    $ss = new Employee\PersonnelStacksQuery();
    $ss->filterByStackId($stack->getStackId());
    $ss->filterByEmployeeId($employee_ids);
    $ss = $ss->find();
    foreach ($ss as $s) {
        $s->setResetQuota($quota);
        $s->save();
    }

    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Aggiornamento quote monteore personale: ID {$stack->getId()} - Quota {$quota} - Personale {$emps}",
        'Context' => print_r($request, true)
    ));
}

echo Response::Create(array(
    'success' => true
));
