<?php

/*
 * Delete single employee
 */

require_once '../../../index.php';


// Load data into employee and delete it
$employee_id = (int) $request->json['employee_id'] > 0 ? (int) $request->json['employee_id'] : null;
$employee = Employee\EmployeeQuery::create()
		->filterByEmployeeId($employee_id)
		->findOne();

$logger->log(array(
	'Type'		 => 'INFO',
	'Scope'		 => __FILE__,
	'Event'		 => basename(__FILE__, ".php"),
	'Message'	 => "Cancellazione personale: {$employee->getSurname()} {$employee->getName()}",
	'Context'	 => print_r($request, true)
));

$employee->delete();

echo Response::Create(array(
	'success' => true
));
