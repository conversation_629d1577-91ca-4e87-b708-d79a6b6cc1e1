<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Protocol
 *
 * <AUTHOR>
 */
class Protocol extends DbObject
{

    protected $_tableN = 'protocol';
    protected $_idF = 'protocol_id';

    public function save()
    {
	if ($this->isNew())
	{
	    $protocols = new Protocols;
	    $this->prot_num = $protocols->getMaxNumber();
	}
	return parent::save();
    }

}

?>
