<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Protocol
 *
 * <AUTHOR>
 */
class Protocols
{

    protected $_tableN = 'protocol';

    public function __construct($year = -1)
    {
	$this->db = Db::getInstance();
	if ((int) $year < 1)
	{
	    $bdg = new Budget();
	    $year = $bdg->getYear();
	}
	$this->start_date = strtotime("1 January " . $year);
	$this->end_date = strtotime("1 January " . $year . " +1 year");
    }

    /**
     * Return max protocol number in budget year
     *
     * @return integer
     *
     * @assert () == 1
     * @assert () == 2
     */
    public function getMaxNumber()
    {
	$query = "SELECT max(prot_num) FROM " . $this->_tableN . "
                WHERE date >= " . $this->start_date . "  AND date < " . $this->end_date;
	$this->db->query($query);
	$max = $this->db->fetchAll();
	return (int) $max[0]['max'] > 0 ? (int) $max[0]['max'] + 1 : 1;
    }

}

?>
