<?php

require_once '../../../index.php';

$kinds = \Protocol\SubjectKindQuery::create()->find();

$kinds = $kinds->toArray(null, false, BasePeer::TYPE_FIELDNAME);

foreach ($kinds as $key => $kind) {
    if (false /* CHECK IF THE ITEM IS IN USE WITHIN A PROTOCOL */) {
        $kinds[$key]['locked'] = true;
    } else {
        $kinds[$key]['locked'] = false;
    }
}

$data = array(
    'success' => true,
    'results' => $kinds
);

echo Response::Create($data);
