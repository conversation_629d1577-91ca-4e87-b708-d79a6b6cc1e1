<?php

require_once '../../../index.php';

$id = (int) $request->id > 0 ? (int) $request->id : null;
$title = $request->title != "" ? $request->title : null;
$oldTitle = "";

if ($id !== null && $title !== null) {
    $kind = Protocol\SubjectKindQuery::create()->findOneById($id);
    if ($kind !== null) {
        $oldTitle = $kind->getTitle();
        $kind->setTitle($title);
        $kind->save();
        // Logging
        $logger->log(array(
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Aggiornamento Tipo di oggetto: {$oldTitle} -> {$kind->getTitle()}",
            'Context' => print_r($request, true)
        ));
    }
}

$data = array(
    'success' => true
);

echo Response::Create($data);
