<?php

require_once '../../../index.php';

$id = (int) $request->id > 0 ? (int) $request->id : null;

if ($id !== null) {
    $kind = \Protocol\SubjectKindQuery::create()->findOneById($id);
    if ($kind !== null) {
        $kind->delete();
        // Logging
        $logger->log(array(
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Cancellazione Tipo Oggetto: {$kind->getTitle()}",
            'Context' => print_r($request, true)
        ));
    }
}

$data = array(
    'success' => true,
);

echo Response::Create($data);
