<?php

require_once '../../../index.php';

$title = $request->title != "" ? $request->title : null;
$kind = array();

if ($title !== null) {
    $kind = new Protocol\SubjectKind();
    $kind->setTitle($title);
    $kind->save();
    // Logging
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Creazione Tipo di oggetto: {$kind->getTitle()}",
        'Context' => print_r($request, true)
    ));
    $kind = $kind->toArray(BasePeer::TYPE_FIELDNAME);
}

$data = array(
    'success' => true,
    'results' => $kind
);

echo Response::Create($data);
