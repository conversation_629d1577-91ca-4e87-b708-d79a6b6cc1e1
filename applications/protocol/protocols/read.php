<?php

/*
 * Read protocol
 */

require_once '../../../index.php';


$idStr = $limitStr = $startStr = "";
if ($request->employee_id > 0)
{
    $idStr = " WHERE protocol_id = " . $request->employee_id . " ";
}
if ($request->limit)
{
    $limitStr = "LIMIT " . $request->limit . " ";
}
if ($request->start)
{
    $startStr = " OFFSET " . $request->start . " ";
}
$query = "
    SELECT
        protocol_id,
        prot_num,
        to_char(to_timestamp(date), 'DD-MM-YYYY') as date,
        description,
        type_id,
        (SELECT code_1||'-'||code_2||'-'||code_3 FROM protocol_type WHERE protocol_type.type_id = protocol.type_id) AS type_display,
        (CASE WHEN direction_type = 'incoming' THEN 'Entrata' ELSE 'Uscita' END) as direction_type,
        sender_receiver,
        canceled AS cancelled,
        object,
        (SELECT count(*) from assoc_prot_doc where assoc_prot_doc.protocol_id = protocol.protocol_id) AS documents,
        source_doc,
        reserved
    FROM protocol " . $idStr . " ORDER BY budget_id desc, prot_num DESC, date DESC " . $limitStr . $startStr . "
";
$db = Db::getInstance();
$db->query($query);
$protocols = $db->fetchAll();

$data = array(
    'success' => true,
    'results' => $protocols,
    'total' => $db->countRowsInTable('protocol')
);

echo Response::Create($data);



