<?php

require_once '../../../index.php';

$id = (int) $request->id > 0 ? (int) $request->id : null;
$active = $request->active == "on" ? true : false;
$comment = $request->comment != "" ? $request->comment : null;
$type_id = (int) $request->type_id > 0 ? $request->type_id : null;
$oldActive = null;
$oldComment = "";
$oldType = null;

if ($id !== null && $comment !== null) {
    $action = Protocol\ActionsQuery::create()->findOneById($id);
    if ($action !== null) {
        $oldActive = $action->getActive();
        $oldComment = $action->getComment();
        $oldType = $action->getType();
        $action->setActive($active);
        $action->setComment($comment);
        $action->setTypeId($type_id);
        $action->save();
        // Logging
        $verb = $oldActive ? "DISATTIVATO" : "ATTIVATO";
        $oldActive = $oldActive == $active ? "" : "{$verb};";
        $oldComment = $oldComment == $comment ? "" : "{$oldComment} -> {$comment}";
        $oldType = $oldType == $type_id ? "" : "{$oldType} -> {$type_id};";
        $logger->log(array(
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Aggiornamento Protocollazione Automatica: {$action->getDescription()} - {$oldActive} {$oldType} {$oldComment};",
            'Context' => print_r($request, true)
        ));
    }
}

$data = array(
    'success' => true
);

echo Response::Create($data);
