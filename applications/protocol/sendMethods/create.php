<?php

require_once '../../../index.php';

$title = $request->title != "" ? $request->title : null;
$method = array();

if ($title !== null) {
    $method = new Protocol\SendMethod();
    $method->setTitle($title);
    $method->save();
    // Logging
    $logger->log(array(
        'Type'    => 'INFO',
        'Scope'   => __FILE__,
        'Event'   => basename(__FILE__, ".php"),
        'Message' => "Creazione Mezzo di invio: {$method->getTitle()}",
        'Context' => print_r($request, true)
    ));
    $method = $method->toArray(BasePeer::TYPE_FIELDNAME);
}

$data = array(
    'success' => true,
    'results' => $method
);

echo Response::Create($data);
