<?php

require_once '../../../index.php';

$id = (int) $request->id > 0 ? (int) $request->id : null;
$title = $request->title != "" ? $request->title : null;
$oldTitle = "";

if ($id !== null && $title !== null) {
    $method = Protocol\SendMethodQuery::create()->findOneById($id);
    if ($method !== null) {
        $oldTitle = $method->getTitle();
        $method->setTitle($title);
        $method->save();
        // Logging
        $logger->log(array(
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Aggiornamento Mezzo di invio: {$oldTitle} -> {$method->getTitle()}",
            'Context' => print_r($request, true)
        ));
    }
}

$data = array(
    'success' => true
);

echo Response::Create($data);
