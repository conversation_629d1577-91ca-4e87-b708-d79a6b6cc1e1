<?php

require_once '../../../index.php';

$methods = \Protocol\SendMethodQuery::create()->find();

$methods = $methods->toArray(null, false, BasePeer::TYPE_FIELDNAME);

foreach ($methods as $key => $method) {
    if (false /* CHECK IF THE ITEM IS IN USE WITHIN A PROTOCOL */) {
        $methods[$key]['locked'] = true;
    } else {
        $methods[$key]['locked'] = false;
    }
}

$data = array(
    'success' => true,
    'results' => $methods
);

echo Response::Create($data);
