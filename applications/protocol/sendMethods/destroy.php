<?php

require_once '../../../index.php';

$id = (int) $request->id > 0 ? (int) $request->id : null;

if ($id !== null) {
    $method = \Protocol\SendMethodQuery::create()->findOneById($id);
    if ($method !== null) {
        $method->delete();
        // Logging
        $logger->log(array(
            'Type'    => 'INFO',
            'Scope'   => __FILE__,
            'Event'   => basename(__FILE__, ".php"),
            'Message' => "Cancellazione Mezzo di invio: {$method->getTitle()}",
            'Context' => print_r($request, true)
        ));
    }
}

$data = array(
    'success' => true,
);

echo Response::Create($data);
