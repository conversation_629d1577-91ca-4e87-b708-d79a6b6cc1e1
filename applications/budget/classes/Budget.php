<?php

/*
 * Object manage all general budget thigs.
 * Residuals, competence, general data about budget year.
 * *
 */

/**
 * Budget class to manage specific budget year
 *
 * <AUTHOR>
 */
class Budget {

    private $_id;
    private $_year;

    /**
     *
     * @param integer $year
     */
    public function __construct($year = -1) {
        $db = Db::getInstance();
        if ($year < 0) {
            $sql      = "SELECT budget_year FROM settings LIMIT 1";
            $db->query($sql);
            $yearData = $db->fetchAll();
            $selYear  = (int) $yearData[0]['budget_year'];
            if (!$selYear) {
                $sql        = "SELECT year FROM bdg_budget ORDER BY year DESC";
                $db->query($sql);
                $selYearArr = $db->fetchAll();
                $selYear    = (int) $selYearArr[0]['year'];
            }
            $this->_year = $selYear > 0 ? $selYear : date("Y");
        } else {
            $this->_year = (int) $year;
        }
        $sql        = "SELECT budget_id FROM bdg_budget WHERE year = " . $this->_year . " LIMIT 1";
        $db->query($sql);
        $budgetData = $db->fetchAll();
        $this->_id  = (int) $budgetData[0]['budget_id'];
    }

    /**
     *
     * @return interger
     *
     */
    public function getId() {
        return (int) $this->_id;
    }

    /**
     *
     * @return integer
     */
    public function getYear() {
        return (int) $this->_year;
    }

}

?>
