<?php

/*
 * Read activities from current budget year
 */

require_once '../../../index.php';


$budget = new Budget();

$query = "
    SELECT
        activ_id,
        aggreg_code,
        aggreg_nr,
        description
    FROM
        bdg_activities
    WHERE
        budget_year = " . $budget->getYear() . "
";

$db = Db::getInstance();
$db->query($query);
$activities = $db->fetchAll();

$data = array(
    'success' => true,
    'results' => $activities
);

echo Response::Create($data);



