<?php

/**
 * WarehouseJournal print data and movements about warehouse and in
 * particular Facile Consumo.
 *
 * It take all Consumable matirials ordered by date (ASC).
 * If date is equal, it oreders by order header (group for order header).
 * If also that is equal, it orders by charged (it means charge and then discharge)
 *
 * <AUTHOR>
 */
class PrintWarehouseJournal extends PrintStandardTemplate {

    const file_name = 'giornale_magazzino';
    const print_title = 'Giornale di magazzino';

    public $page_orientation = 'L';
    private $year;

    /**
     * Set base data and objects.
     * Than print title
     *
     */
    public function __construct() {
        login_required();
        $budget = new Budget();
        $this->year = $budget->getYear();
        parent::__construct(self::file_name);
        $this->__writePrintTitle(self::print_title . ' - Anno: ' . $this->year);
    }

    /**
     * Create total row at the end of any charged or discharged block
     *
     * @param float $total
     */
    private function _setGeneralTotalRow($total) {
        $this->Cell(150, 5, '', 0, 0, 'C', '', '', 1);
        $this->Cell(40, 5, 'Totale generale al ' . date("d/m/Y"), 1, 0, 'C', '', '', 1);
        $this->Cell(85, 5, $total, 1, 1, 'C', '', '', 1);
    }

    /**
     *
     * Create end total row, to print difference between charged and discharged
     *
     * @param float $total
     * @param string $title
     */
    private function _setTableTotalRow($total, $title = '') {
        $this->Cell(170, 5, '', 0, 0, 'C', '', '', 1);
        $this->Cell(20, 5, $title ? $title : 'Tot. Movimento', 1, 0, 'C', '', '', 1);
        $this->Cell(85, 5, $total, 1, 1, 'C', '', '', 1);
    }

    /**
     * Create first row of any block.
     */
    private function _setTableHeader() {
        $this->Cell(10, 5, 'Riga', 1, 0, 'C', '', '', 1);
        $this->Cell(80, 5, 'Denominazione o descrizione dell\'oggetto', 1, 0, 'C', '', '', 1);
        $this->Cell(10, 5, 'Un. mis.', 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, 'Quantità', 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, 'Importo €', 1, 0, 'C', '', '', 1);
        $this->Cell(10, 5, 'IVA', 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, 'Imp. Compl. €', 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, 'Giacenza att.', 1, 0, 'C', '', '', 1);
        $this->Cell(85, 5, 'Note', 1, 1, 'C', '', '', 1);
    }

    /**
     *
     * Set content of any block
     *
     * @param array $values
     */
    private function _setTableContent($values) {
        $this->Cell(10, 5, $values['aq_row_number'], 1, 0, 'C', '', '', 1);
        // set cell padding
        //$this->MultiCell(80, 10, $values['description'], 1, 'C', 0, 0,  '', '', true, 0, false, false, 0, 'M');
        $this->Cell(80, 5, strlen($values['description']) > 80 ? substr($values['description'], 0, 80) . '...' : $values['description'], 1, 0, 'C', '', '', 1);
        $this->Cell(10, 5, $values['mu'], 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, $values['quantity'], 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, $values['imp'], 1, 0, 'C', '', '', 1);
        $this->Cell(10, 5, $values['vat'], 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, $values['imp_comp'], 1, 0, 'C', '', '', 1);
        $this->Cell(20, 5, $values['giac'], 1, 0, 'C', '', '', 1);
        $this->Cell(85, 5, $values['note'], 1, 1, 'C', '', '', 1);
    }

    /**
     * Take a date from the specific view
     *
     * @return array consumable matirial view
     */
    public function _getViewData() {
        $db = Db::getInstance();
        //View return facile consumo data in chronological order and with order number
        $sql = "SELECT * FROM view_facile_consumo_chronological WHERE movement >= '" . $this->year . "-01-01'";
        $db->query($sql);
        return $db->fetchAll();
    }

    /**
     * Generate print from native TCPDF library
     */
    public function createPdf() {
        $facileConsumoArr = $this->_getViewData();
        $data = array();
        $current_order = -1;
        $current_taking = -1;
        $row = 0;
        $aq_number = 1;
        $tk_number = 1;
        $aq_row_number = 1;
        $tk_row_number = 1;
        $total_charged = 0;
        $total_discharged = 0;
        $giac = array();
        $total = 0.00;

        foreach ($facileConsumoArr as $key => $itemFc) {
            // Take the next alement of array for checks and instance new item and dorder
            $nextFc = $facileConsumoArr[$key + 1];

            $row++;
            $consumable = new Consumable((int) $itemFc['item_id']);
            $order = $consumable->getOrder();
            $orderRow = $consumable->getOrderRow();
            if ((int) $itemFc['is_charged'] > 0) { // Is it charged?
                // If it is a new order rows, you have to insert an header order
                if ($current_order != (int) $itemFc['order_h_id']) {
                    $this->Cell(100, 5, 'Acquisto n°' . $aq_number . ' del ' . date("d/m/Y", $consumable->charge_date), 0, 1, 'L', '', '', 1);
                    $this->Cell(0, 5, 'Buono d\'ordine: ' . ($order->int_id . '/' . $order->year) . ' Fornitore: ' . $order->getSupplierName(), 0, 1, 'L', '', '', 1);
                    $this->_setTableHeader();
                    $total_charged = 0;
                    $aq_number++;
                }

                $giac[$order->id] += $consumable->quantity_begin;
                $data = array(
                    'aq_row_number' => $aq_row_number,
                    'mu'            => $consumable->item_kind->getMuDisplay(),
                    'description'   => $consumable->item_kind->name . ' - ' . $consumable->description,
                    'quantity'      => $consumable->quantity_begin,
                    'vat'           => $orderRow->vat,
                    'imp'           => money_format('%.2n', $consumable->value),
                    'imp_comp'      => money_format('%.2n', ($consumable->value * $consumable->quantity_begin)),
                    'giac'          => $order->id ? $giac[$order->id] : $consumable->quantity_begin,
                    'note'          => ''
                );
                $total += ($consumable->value * $consumable->quantity_begin);
                $aq_row_number++;
                $total_charged += ($consumable->value * $consumable->quantity_begin);
                $this->_setTableContent($data);
                if ($nextFc['is_charged'] != $itemFc['is_charged'] OR
                        $nextFc['order_h_id'] != $itemFc['order_h_id']) {
                    $aq_row_number = 1;
                    $this->_setTableTotalRow(money_format('%.2n', $total_charged));
                }
                $current_order = $itemFc['order_h_id'];
            } else {
                if ($current_taking != (int) $itemFc['order_h_id']) {
                    $this->Cell(0, 5, 'Prelevamento n°' . $tk_number . ' del ' . date("d/m/Y", $consumable->discharge_date), 0, 1, 'L', '', '', 1);
                    $this->Cell(0, 5, 'Reparto: ' . $consumable->getLocationName() . ' Responsabile: ' . $consumable->getResponsableName(), 0, 1, 'L', '', '', 1);
                    $this->_setTableHeader();
                    $total_discharged = 0;
                    $tk_number++;
                }
                $giac[$order->id] -= $consumable->quantity_begin;
                $data = array(
                    'aq_row_number' => $tk_row_number,
                    'mu'            => $consumable->item_kind->getMuDisplay(),
                    'description'   => $consumable->item_kind->name . ' - ' . $consumable->description,
                    'quantity'      => $consumable->quantity_begin,
                    'vat'           => $orderRow->vat,
                    'imp'           => money_format('%.2n', $consumable->value),
                    'imp_comp'      => money_format('%.2n', ($consumable->value * $consumable->quantity_begin)),
                    'giac'          => $order->id ? $giac[$order->id] : 0,
                    'note'          => ''
                );
                $total -= ($consumable->value * $consumable->quantity_begin);
                $tk_row_number++;
                $total_discharged += ($consumable->value * $consumable->quantity_begin);
                $this->_setTableContent($data);
                if ($nextFc['is_charged'] != $itemFc['is_charged'] OR
                        $nextFc['order_h_id'] != $itemFc['order_h_id']) {
                    $tk_row_number = 1;
                    $this->_setTableTotalRow(money_format('%.2n', $total_discharged));
                }
                $current_taking = $itemFc['order_h_id'];
            }
        }
        $this->Ln(1);
        $this->_setGeneralTotalRow(money_format('%.2n', $total));
        return $this->native2pdf();
    }

    public function as_view() {
        $file_url = $this->createPdf();
        if ($file_url) {
            $data = array(
                "success" => true,
                "urlPath" => $file_url
            );
        } else {
            $data = array(
                "success" => false
            );
        }
        echo Response::Create($data);
    }

}

