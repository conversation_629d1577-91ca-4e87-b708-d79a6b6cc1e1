<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Supplier
 *
 * <AUTHOR>
 */
class WhResponsable
{

    public $table_name = 'wh_responsible';
    private $_id_name = 'id';

    public function __construct($id = -1)
    {
	$this->db = Db::getInstance();
	$this->id = $id;
	if ($this->id > 0)
	{
	    $this->db->query("SELECT * FROM " . $this->table_name . " WHERE " . $this->_id_name . " = " . $this->id);
	    $order_rows = $this->db->fetchAll();
	    foreach ($order_rows[0] as $key => $value)
	    {
		$this->$key = $value;
	    }
	}
    }

}

?>
