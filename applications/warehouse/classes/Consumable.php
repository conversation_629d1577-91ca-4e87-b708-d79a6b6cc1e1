<?php

/*
 * Items rapresent Consumable matirials in warehouse.
 */

/**
 * The object extend Item and have a methods and
 * property of consumable matirials
 *
 * <AUTHOR>
 */
class Consumable extends Item
{

    public function __construct($id)
    {
	parent::__construct($id);
	if ($this->item_kind->isInventariable())
	{
	    // TODO: Put Exception to return a notification to the client and write a log
	    exit(1);
	}
    }

}

?>
