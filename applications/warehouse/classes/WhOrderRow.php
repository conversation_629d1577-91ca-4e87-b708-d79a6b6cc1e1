<?php

/**
 * WhOrderRow class
 *
 * <AUTHOR>
 */
class WhOrderRow
{

    public $table_name = 'wh_order';
    private $_id_name = 'order_id';

    public function __construct($id = -1)
    {
	$this->db = Db::getInstance();
	$this->id = $id;
	if ($this->id > 0)
	{
	    $this->db->query("SELECT * FROM " . $this->table_name . " WHERE " . $this->_id_name . " = " . $this->id);
	    $order_rows = $this->db->fetchAll();
	    foreach ($order_rows[0] as $key => $value)
	    {
		$this->$key = $value;
	    }
	}
	$this->item_kind = new ItemKind($this->kind);
    }

}

?>
