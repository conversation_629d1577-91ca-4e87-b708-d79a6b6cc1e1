<?php

/**
 * Get max versamento number from table tasse
 *
 * @package    Ccp.Tasse
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
require_once '../../../index.php';

use Ccp\TasseQuery;

$sy = TasseQuery::create()->getSchoolYears();

foreach ($sy as $key => $y) {
    $sy[$key]['id'] = $sy[$key]['text'];
}

$sy = array_values($sy);

array_unshift($sy, array(
    'id'   => 'all',
    'text' => 'Tutti'
));

echo Response::Create(
        array(
            'success' => true,
            'results' => $sy
        )
);
