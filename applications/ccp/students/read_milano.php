<?php

/*
 * API to read students from Milan
 *
 */

/**
 * <AUTHOR>
 */
require_once '../../../index.php';

$api = new Core\ExternalApi;
$filter = array(
    'name' => $request->get['name'],
);
$data = $api->get("/school/" . SCHOOL_ID . "/student", $filter);

$employees = \Employee\EmployeeQuery::create()
        ->where("surname||' '||name ILIKE '%" . pg_escape_string($request->get['name']) . "%'")
        ->withColumn('employee_id', 'employee_id')
        ->withColumn('surname', 'cognome')
        ->withColumn('name', 'nome')
        ->select("nome", "cognome")
        ->find()
        ->toArray();

$list = array();
foreach ($employees as $employee) {
    $employee['Tipo'] = 'I';
    $list[] = $employee;
}

$data['results'] = array_merge($data['results'], $list);

if ($data === false || $data['success'] === false) {
    $data = array(
        'success' => false
    );
}

echo Response::Create($data);
