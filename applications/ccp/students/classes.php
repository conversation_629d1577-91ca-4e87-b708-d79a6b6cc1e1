<?php

/**
 * Search classes based on correct db for school year
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
//use Ccp\ClassiQuery;

require_once '../../../index.php';

//$classes = ClassiQuery::create()->searchClassi();

$api = new Core\ExternalApi;

$data = $api->get("/school/" . SCHOOL_ID . "/class");

if ($data === false || $data['success'] === false) {
    $data = array(
        'success' => false
    );
}


echo Response::Create($data);
