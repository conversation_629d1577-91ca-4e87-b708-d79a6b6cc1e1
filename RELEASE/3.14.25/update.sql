ALTER TABLE ccp_type ADD COLUMN centro_costo_ricavo CHARACTER VARYING(50);
ALTER TABLE audit.ccp_type ADD COLUMN centro_costo_ricavo CHARACTER VARYING(50);

ALTER TABLE ccp_type ADD COLUMN id_importazione CHARACTER VARYING(50);
ALTER TABLE audit.ccp_type ADD COLUMN id_importazione CHARACTER VARYING(50);

ALTER TABLE ccp_payment_method ADD COLUMN causale_contabile CHARACTER VARYING(50);
ALTER TABLE audit.ccp_payment_method ADD COLUMN causale_contabile CHARACTER VARYING(50);

ALTER TABLE ccp_payment_method ADD COLUMN causale_contabile_uscite CHARACTER VARYING(50);
ALTER TABLE audit.ccp_payment_method ADD COLUMN causale_contabile_uscite CHARACTER VARYING(50);


ALTER TABLE ccp_payment_method ADD COLUMN data_raggruppamento CHARACTER VARYING(255) DEFAULT NULL;
ALTER TABLE audit.ccp_payment_method ADD COLUMN data_raggruppamento CHARACTER VARYING(255);


ALTER TABLE ccp_payment_method ADD COLUMN tipo_raggruppamento CHARACTER VARYING(255) DEFAULT NULL;
ALTER TABLE audit.ccp_payment_method ADD COLUMN tipo_raggruppamento CHARACTER VARYING(255);

ALTER TABLE ccp_payment_method ADD COLUMN piano_conti_da_tipo BOOLEAN default 'f';
ALTER TABLE audit.ccp_payment_method ADD COLUMN piano_conti_da_tipo BOOLEAN;

INSERT INTO parameter (name,value) VALUES ('EASY_MODEL_INCASSI','Easy');
