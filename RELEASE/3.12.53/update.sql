ALTER TABLE ccp_movement ADD COLUMN last_update TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT NOW();
ALTER TABLE audit.ccp_movement ADD COLUMN last_update TIMESTAMP WITH TIME ZONE;
ALTER TABLE ccp_movement ADD COLUMN last_sync TIMESTAMP WITH TIME ZONE;
ALTER TABLE audit.ccp_movement ADD COLUMN last_sync TIMESTAMP WITH TIME ZONE;

ALTER TABLE ccp_payment ADD COLUMN last_update TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW();
ALTER TABLE audit.ccp_payment ADD COLUMN last_update TIMESTAMP WITH TIME ZONE;
ALTER TABLE ccp_payment ADD COLUMN last_sync TIMESTAMP WITH TIME ZONE;
ALTER TABLE audit.ccp_payment ADD COLUMN last_sync TIMESTAMP WITH TIME ZONE;

CREATE OR REPLACE FUNCTION movement_last_update_refresh() returns trigger as $$
  BEGIN
    NEW.last_update := NOW();
    RETURN NEW;
  END;
$$ LANGUAGE plpgsql;

create TRIGGER movement_last_update_refresh BEFORE update on ccp_movement for each row execute
procedure movement_last_update_refresh();

CREATE OR REPLACE FUNCTION payment_last_update_refresh() returns trigger as $$
  BEGIN
    NEW.last_update := NOW();
    RETURN NEW;
  END;
$$ LANGUAGE plpgsql;

create TRIGGER payment_last_update_refresh BEFORE update on ccp_payment for each row execute
procedure payment_last_update_refresh();

