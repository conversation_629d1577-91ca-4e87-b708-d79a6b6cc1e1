-- Fixes to audit tables
ALTER TABLE audit.absence_stack ALTER COLUMN id DROP DEFAULT;
ALTER TABLE audit.employee ALTER COLUMN dom_first_curr_year DROP DEFAULT;
ALTER TABLE audit.storage_personnel_stack ALTER COLUMN id DROP DEFAULT;
ALTER TABLE audit.tasse ALTER COLUMN id_tasse DROP DEFAULT;
ALTER TABLE audit.tax_residuals ALTER COLUMN id DROP DEFAULT;
ALTER TABLE audit.tipi_tasse ALTER COLUMN id_tipo_tassa DROP DEFAULT;


-- Removal of all audit sequences
DROP SEQUENCE IF EXISTS audit.absence_stack_id_seq;
DROP SEQUENCE IF EXISTS audit.employee_dom_first_curr_year_seq;
DROP SEQUENCE IF EXISTS audit.storage_personnel_stack_id_seq;
DROP SEQUENCE IF EXISTS audit.tasse_id_tasse_seq;
DROP SEQUENCE IF EXISTS audit.tax_residuals_id_seq;
DROP SEQUENCE IF EXISTS audit.tipi_tasse_id_tipo_tassa_seq;


-- Tax_residuals fixes
DROP TABLE audit.tax_residuals;
DROP TRIGGER tax_residuals_audit ON tax_residuals;

UPDATE tax_residuals SET tasse='0' WHERE tasse IS NULL OR trim(tasse)='';
ALTER TABLE tax_residuals ALTER COLUMN tasse TYPE float USING (replace(tasse,',','.')::float);
ALTER TABLE tax_residuals ALTER COLUMN tasse SET DEFAULT 0.00;

UPDATE tax_residuals SET contributi='0' WHERE contributi IS NULL OR trim(contributi)='';
ALTER TABLE tax_residuals ALTER COLUMN contributi TYPE float USING (replace(contributi,',','.')::float);
ALTER TABLE tax_residuals ALTER COLUMN contributi SET DEFAULT 0.00;

UPDATE tax_residuals SET quote='0' WHERE quote IS NULL OR trim(quote)='';
ALTER TABLE tax_residuals ALTER COLUMN quote TYPE float USING (replace(quote,',','.')::float);
ALTER TABLE tax_residuals ALTER COLUMN quote SET DEFAULT 0.00;

UPDATE tax_residuals SET diversi='0' WHERE diversi IS NULL OR trim(diversi)='';
ALTER TABLE tax_residuals ALTER COLUMN diversi TYPE float USING (replace(diversi,',','.')::float);
ALTER TABLE tax_residuals ALTER COLUMN diversi SET DEFAULT 0.00;

UPDATE tax_residuals SET debito='0' WHERE debito IS NULL OR trim(debito)='';
ALTER TABLE tax_residuals ALTER COLUMN debito TYPE float USING (replace(debito,',','.')::float);
ALTER TABLE tax_residuals ALTER COLUMN debito SET DEFAULT 0.00;

CREATE TABLE audit.tax_residuals
(
  op_action character varying(1) NOT NULL,
  op_date timestamp with time zone NOT NULL DEFAULT now(),
  id_residual integer NOT NULL,
  year integer NOT NULL,
  tasse double precision DEFAULT 0.00,
  contributi double precision DEFAULT 0.00,
  quote double precision DEFAULT 0.00,
  diversi double precision DEFAULT 0.00,
  debito double precision DEFAULT 0.00
);

CREATE TRIGGER tax_residuals_audit
  AFTER INSERT OR UPDATE OR DELETE
  ON tax_residuals
  FOR EACH ROW
  EXECUTE PROCEDURE audit.process_audit();


-- New Employee max worked time parameter
ALTER TABLE employee ADD COLUMN max_work integer NOT NULL DEFAULT 999;
ALTER TABLE audit.employee ADD COLUMN max_work integer NOT NULL DEFAULT 999;


-- "Show exit types" parameter to all schools
CREATE OR REPLACE FUNCTION setOutgoMode() RETURNS integer
    LANGUAGE plpgsql
    AS $_$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM parameter WHERE name = 'ata_show_outgo_types') THEN
                INSERT INTO parameter (name, value) VALUES ('ata_show_outgo_types', 't');
            END IF;
            RETURN 1;
        END;
    $_$;
SELECT setOutgoMode();
DROP FUNCTION IF EXISTS setOutgoMode();


-- Fix bdg_var missing transfer column
DO $$
	BEGIN
	    ALTER TABLE bdg_var ADD COLUMN transfer smallint NOT NULL DEFAULT 1;
        UPDATE bdg_var SET transfer = 2 WHERE number < 0;
	EXCEPTION
	    WHEN duplicate_column THEN RAISE NOTICE 'Column transfer already exists in bdg_var.';
	END;
$$;


-- New Area parameters
INSERT INTO parameter (name, value) VALUES ('VERSION_UI', '1.0.0');
INSERT INTO parameter (name, value) VALUES ('AREA_PERSONNEL', 't');
INSERT INTO parameter (name, value) VALUES ('AREA_CCP', 't');
INSERT INTO parameter (name, value) VALUES ('AREA_WAREHOUSE', 'f');
INSERT INTO parameter (name, value) VALUES ('AREA_ARCHIVE', 't');
INSERT INTO parameter (name, value) VALUES ('AREA_PROTOCOL', 't');
INSERT INTO parameter (name, value) VALUES ('AREA_ALBO', 't');
INSERT INTO parameter (name, value) VALUES ('AREA_TRASPARENZA', 't');

-- Sets CCP area as active only if there are already taxes
CREATE OR REPLACE FUNCTION setCCPActive() RETURNS integer
    LANGUAGE plpgsql
    AS $_$
        BEGIN
            IF (SELECT count(id_tasse) FROM tasse) > 0 THEN
                UPDATE parameter SET value = 't' WHERE name = 'AREA_CCP';
            END IF;
            RETURN 1;
        END;
    $_$;
SELECT setCCPActive();
DROP FUNCTION IF EXISTS setCCPActive();


-- New permissions

-- Protocol
INSERT INTO auth_section (id, title) VALUES (10, 'Protocollo');

INSERT INTO auth_permission (id, title, auth_section) VALUES (400, 'Protocollo Informatico | Attivo', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (400, 'MenuProtocolCnt', 'MainMenuPnl', 400);
INSERT INTO auth_path (id, path, auth_permission) VALUES (400, '', 400);

INSERT INTO auth_permission (id, title, auth_section) VALUES (401, 'Protocollo Informatico | Protocolli | Aggiungere', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (401, 'ProtocolNewBtn', 'ProtocolMainPnl', 401);
INSERT INTO auth_path (id, path, auth_permission) VALUES (401, '', 401);

INSERT INTO auth_permission (id, title, auth_section) VALUES (402, 'Protocollo Informatico | Protocolli | Modificare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (402, 'contextProtocolProtocolEdit', 'ProtocolProtocolEditMn', 402);
INSERT INTO auth_path (id, path, auth_permission) VALUES (402, '', 402);

INSERT INTO auth_permission (id, title, auth_section) VALUES (403, 'Protocollo Informatico | Protocolli | Annullare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (403, 'contextProtocolProtocolCancel', 'ProtocolProtocolEditMn', 403);
INSERT INTO auth_path (id, path, auth_permission) VALUES (403, '', 403);

INSERT INTO auth_permission (id, title, auth_section) VALUES (404, 'Protocollo Informatico | Protocolli | Eliminare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (404, 'contextProtocolProtocolDelete', 'ProtocolProtocolEditMn', 404);
INSERT INTO auth_path (id, path, auth_permission) VALUES (404, '', 404);

INSERT INTO auth_permission (id, title, auth_section) VALUES (405, 'Protocollo Informatico | Protocolli | Visualizzare storico', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (405, 'contextProtocolProtocolHistory', 'ProtocolProtocolEditMn', 405);
INSERT INTO auth_path (id, path, auth_permission) VALUES (405, '', 405);

INSERT INTO auth_permission (id, title, auth_section) VALUES (406, 'Protocollo Informatico | Protocolli | Stampe singole', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (406, 'contextProtocolProtocolPrints', 'ProtocolProtocolEditMn', 406);
INSERT INTO auth_path (id, path, auth_permission) VALUES (406, '', 406);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (407, 'ArchiveQueueActionProtocolColumn', 'ArchiveQueueTab', 401);
INSERT INTO auth_path (id, path, auth_permission) VALUES (407, '', 401);

-- Titolario
INSERT INTO auth_permission (id, title, auth_section) VALUES (410, 'Protocollo Informatico | Titolario | Visualizzare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (410, 'ProtocolTypesBtn', 'ProtocolRightPnl', 410);
INSERT INTO auth_path (id, path, auth_permission) VALUES (410, '', 410);

INSERT INTO auth_permission (id, title, auth_section) VALUES (411, 'Protocollo Informatico | Titolario | Aggiungere', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (411, 'ProtocolTypeNewBtn', 'ProtocolTypesTreeGrid', 411);
INSERT INTO auth_path (id, path, auth_permission) VALUES (411, '', 411);

INSERT INTO auth_permission (id, title, auth_section) VALUES (412, 'Protocollo Informatico | Titolario | Modificare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (412, 'contextProtocolTypeEdit', 'ProtocolTypeEditMn', 412);
INSERT INTO auth_path (id, path, auth_permission) VALUES (412, '', 412);

INSERT INTO auth_permission (id, title, auth_section) VALUES (413, 'Protocollo Informatico | Titolario | Eliminare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (413, 'contextProtocolTypeDelete', 'ProtocolTypeEditMn', 413);
INSERT INTO auth_path (id, path, auth_permission) VALUES (413, '', 413);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (414, 'contextProtocolTypeAdd', 'ProtocolTypeEditMn', 411);
INSERT INTO auth_path (id, path, auth_permission) VALUES (414, '', 411);

-- Correspondents
INSERT INTO auth_permission (id, title, auth_section) VALUES (420, 'Protocollo Informatico | Corrispondenti | Visualizzare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (420, 'ProtocolCorrespondentsBtn', 'ProtocolRightPnl', 420);
INSERT INTO auth_path (id, path, auth_permission) VALUES (420, '', 420);

INSERT INTO auth_permission (id, title, auth_section) VALUES (421, 'Protocollo Informatico | Corrispondenti | Aggiungere', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (421, 'ProtocolCorrespondentNewBtn', 'ProtocolCorrespondentsGrid', 421);
INSERT INTO auth_path (id, path, auth_permission) VALUES (421, '', 421);

INSERT INTO auth_permission (id, title, auth_section) VALUES (422, 'Protocollo Informatico | Corrispondenti | Modificare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (422, 'contextProtocolCorrespondentEdit', 'ProtocolCorrespondentsEditMn', 422);
INSERT INTO auth_path (id, path, auth_permission) VALUES (422, '', 422);

INSERT INTO auth_permission (id, title, auth_section) VALUES (423, 'Protocollo Informatico | Corrispondenti | Eliminare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (423, 'contextProtocolCorrespondentDelete', 'ProtocolCorrespondentsEditMn', 423);
INSERT INTO auth_path (id, path, auth_permission) VALUES (423, '', 423);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (424, 'ProtocolProtocolNewCorrespondentNewBtn', 'ProtocolProtocolNewWin', 421);
INSERT INTO auth_path (id, path, auth_permission) VALUES (424, '', 421);

-- Subject Kinds
INSERT INTO auth_permission (id, title, auth_section) VALUES (430, 'Protocollo Informatico | Tipi di Oggetto | Visualizzare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (430, 'ProtocolSubjectKindsBtn', 'ProtocolRightPnl', 430);
INSERT INTO auth_path (id, path, auth_permission) VALUES (430, '', 430);

INSERT INTO auth_permission (id, title, auth_section) VALUES (431, 'Protocollo Informatico | Tipi di Oggetto | Aggiungere', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (431, 'ProtocolSubjetKindNewBtn', 'ProtocolSubjectKindsGrid', 431);
INSERT INTO auth_path (id, path, auth_permission) VALUES (431, '', 431);

INSERT INTO auth_permission (id, title, auth_section) VALUES (432, 'Protocollo Informatico | Tipi di Oggetto | Modificare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (432, 'contextProtocolSubjectKindEdit', 'ProtocolSubjectKindsEditMn', 432);
INSERT INTO auth_path (id, path, auth_permission) VALUES (432, '', 432);

INSERT INTO auth_permission (id, title, auth_section) VALUES (433, 'Protocollo Informatico | Tipi di Oggetto | Eliminare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (433, 'contextProtocolSubjectKindDelete', 'ProtocolSubjectKindsEditMn', 433);
INSERT INTO auth_path (id, path, auth_permission) VALUES (433, '', 433);

-- Send Methods
INSERT INTO auth_permission (id, title, auth_section) VALUES (440, 'Protocollo Informatico | Mezzi di Invio | Visualizzare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (440, 'ProtocolSendMethodsBtn', 'ProtocolRightPnl', 440);
INSERT INTO auth_path (id, path, auth_permission) VALUES (440, '', 440);

INSERT INTO auth_permission (id, title, auth_section) VALUES (441, 'Protocollo Informatico | Mezzi di Invio | Aggiungere', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (441, 'ProtocolSendMethodNewBtn', 'ProtocolSendMethodsGrid', 441);
INSERT INTO auth_path (id, path, auth_permission) VALUES (441, '', 441);

INSERT INTO auth_permission (id, title, auth_section) VALUES (442, 'Protocollo Informatico | Mezzi di Invio | Modificare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (442, 'contextProtocolSendMethodEdit', 'ProtocolSendMethodsEditMn', 442);
INSERT INTO auth_path (id, path, auth_permission) VALUES (442, '', 442);

INSERT INTO auth_permission (id, title, auth_section) VALUES (443, 'Protocollo Informatico | Mezzi di Invio | Eliminare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (443, 'contextProtocolSendMethodDelete', 'ProtocolSendMethodsEditMn', 443);
INSERT INTO auth_path (id, path, auth_permission) VALUES (443, '', 443);

-- Actions
INSERT INTO auth_permission (id, title, auth_section) VALUES (450, 'Protocollo Informatico | Protocollazione Automatica | Visualizzare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (450, 'ProtocolActionsBtn', 'ProtocolRightPnl', 450);
INSERT INTO auth_path (id, path, auth_permission) VALUES (450, '', 450);

INSERT INTO auth_permission (id, title, auth_section) VALUES (451, 'Protocollo Informatico | Protocollazione Automatica | Modificare', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (451, 'contextProtocolActionEdit', 'ProtocolActionsEditMn', 451);
INSERT INTO auth_path (id, path, auth_permission) VALUES (451, '', 451);

-- Prints
INSERT INTO auth_permission (id, title, auth_section) VALUES (460, 'Protocollo Informatico | Stampe', 10);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (460, 'ProtocolPrintsBtn', 'ProtocolRightPnl', 460);
INSERT INTO auth_path (id, path, auth_permission) VALUES (460, '', 460);


-- Archive
INSERT INTO auth_section (id, title) VALUES (11, 'Documenti');

INSERT INTO auth_permission (id, title, auth_section) VALUES (500, 'Documenti | Attivo', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (500, 'MenuArchiveCnt', 'MainMenuPnl', 500);
INSERT INTO auth_path (id, path, auth_permission) VALUES (500, '', 500);

INSERT INTO auth_permission (id, title, auth_section) VALUES (501, 'Documenti | Documenti | Archiviare', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (501, 'ArchiveNewBtn', 'ArchiveArchivedPnl', 501);
INSERT INTO auth_path (id, path, auth_permission) VALUES (501, '', 501);

INSERT INTO auth_permission (id, title, auth_section) VALUES (502, 'Documenti | Documenti | Modificare', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (502, 'contextArchiveDocumentsQueueEdit', 'ArchiveDocumentsQueueEditMn', 502);
INSERT INTO auth_path (id, path, auth_permission) VALUES (502, '', 502);

INSERT INTO auth_permission (id, title, auth_section) VALUES (503, 'Documenti | Documenti | Eliminare', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (503, 'contextArchiveDocumentsQueueDelete', 'ArchiveDocumentsQueueEditMn', 503);
INSERT INTO auth_path (id, path, auth_permission) VALUES (503, '', 503);

INSERT INTO auth_permission (id, title, auth_section) VALUES (504, 'Documenti | Documenti | Scaricare', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (504, 'contextDocumentDownload', 'ArchiveEditMn', 504);
INSERT INTO auth_path (id, path, auth_permission) VALUES (504, '', 504);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (510, 'ArchiveQueueActionArchiveColumn', 'ArchiveQueuePnl', 501);
INSERT INTO auth_path (id, path, auth_permission) VALUES (510, '', 501);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (511, 'ProtocolProtocolNewArchiveNewBtn', 'ProtocolProtocolNewWin', 501);
INSERT INTO auth_path (id, path, auth_permission) VALUES (511, '', 501);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (512, 'contextDocumentEdit', 'ArchiveEditMn', 502);
INSERT INTO auth_path (id, path, auth_permission) VALUES (512, '', 502);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (513, 'contextDocumentDelete', 'ArchiveEditMn', 503);
INSERT INTO auth_path (id, path, auth_permission) VALUES (513, '', 503);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (514, 'ProtocolLinkedDocumentsDownloadColumn', 'ProtocolLinkedDocumentsWin', 504);
INSERT INTO auth_path (id, path, auth_permission) VALUES (514, '', 504);

-- Queue
INSERT INTO auth_permission (id, title, auth_section) VALUES (520, 'Documenti | Da esaminare | Visualizzare', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (520, 'ArchiveQueueTab', 'ArchiveMainPnl', 520);
INSERT INTO auth_path (id, path, auth_permission) VALUES (520, '', 520);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (521, 'ArchiveQueuePnl', 'ArchiveMainPnl', 520);
INSERT INTO auth_path (id, path, auth_permission) VALUES (521, '', 520);

-- Archived
INSERT INTO auth_permission (id, title, auth_section) VALUES (530, 'Documenti | Archiviati | Visualizzare', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (530, 'ArchiveArchivedTab', 'ArchiveMainPnl', 530);
INSERT INTO auth_path (id, path, auth_permission) VALUES (530, '', 530);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (531, 'ArchiveArchivedPnl', 'ArchiveMainPnl', 530);
INSERT INTO auth_path (id, path, auth_permission) VALUES (531, '', 530);

-- Prints
INSERT INTO auth_permission (id, title, auth_section) VALUES (540, 'Documenti | Stampe', 11);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (540, 'ArchivePrintsBtn', 'ArchiveRightPnl', 540);
INSERT INTO auth_path (id, path, auth_permission) VALUES (540, '', 540);