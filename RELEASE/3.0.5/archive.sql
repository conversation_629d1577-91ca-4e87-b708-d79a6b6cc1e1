-- New Parameters
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_URL', 'https://docubank.kpnqwest.it/backend/v1.0/public/script/');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_VERSION', '1.0');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_USERNAME', '');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_PASSWORD', '');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_ORGANIZATION', '');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_PAGES', '');


-- New Archive Tables

-- Archive Class
CREATE TABLE archive_class
(
    id serial NOT NULL,
    name character varying(30) NOT NULL,
    code character varying(20) NOT NULL,
    description character varying(255),
    format character varying(5) NOT NULL,
    action character(1) NOT NULL,
    CONSTRAINT archive_class_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE archive_class IS 'Document type';
COMMENT ON COLUMN archive_class.code IS 'Remote code';
COMMENT ON COLUMN archive_class.format IS 'File extension, possible values: PDF, TXT, ALL';
COMMENT ON COLUMN archive_class.action IS 'File management, possible values: (A)rchive, (C)onserve, (N)one';

CREATE TABLE audit.archive_class
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(30) NOT NULL,
    code character varying(20) NOT NULL,
    description character varying(255),
    format character varying(5) NOT NULL,
    action character(1) NOT NULL
);

CREATE TRIGGER archive_class_audit
    AFTER INSERT OR UPDATE OR DELETE ON archive_class
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

INSERT INTO archive_class (name, code, description, format, action) VALUES ('Locale', 'local', 'Documenti caricati sul server locale', 'ALL', 'N');

-- Archive User
CREATE TABLE archive_user
(
    id serial NOT NULL,
    name character varying(30) NOT NULL,
    username character varying(50) NOT NULL,
    password character varying(50),
    alias character varying(50),
    pin character varying(50),
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT archive_user_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE archive_user IS 'Remote user';

CREATE TABLE audit.archive_user
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(30) NOT NULL,
    username character varying(50) NOT NULL,
    password character varying(50),
    alias character varying(50),
    pin character varying(50),
    active boolean NOT NULL DEFAULT true
);

CREATE TRIGGER archive_user_audit
    AFTER INSERT OR UPDATE OR DELETE ON archive_user
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Archive Metadata
CREATE TABLE archive_metadata
(
    id serial NOT NULL,
    name character varying(30) NOT NULL,
    code character varying(30) NOT NULL,
    description character varying(255),
    optional boolean NOT NULL DEFAULT true,
    sequential boolean NOT NULL DEFAULT false,
    preservation_date boolean NOT NULL DEFAULT false,
    kind character(1) NOT NULL DEFAULT 'S'::bpchar,
    class_id integer NOT NULL,
    CONSTRAINT archive_metadata_pkey PRIMARY KEY (id),
    CONSTRAINT archive_metadata_class_id_fkey
        FOREIGN KEY (class_id)
        REFERENCES archive_class (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT archive_metadata_id_name_key UNIQUE (id, name)
);

COMMENT ON TABLE archive_metadata IS 'Metadata for a document, bound to specific document type';
COMMENT ON COLUMN archive_metadata.kind IS 'Metadata type, possible values: (D)ate, (I)nteger, (S)tring';

CREATE INDEX fki_archive_metadata_class_id_fkey
    ON archive_metadata
    USING btree (class_id);

CREATE TABLE audit.archive_metadata
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(30) NOT NULL,
    code character varying(30) NOT NULL,
    description character varying(255),
    optional boolean NOT NULL DEFAULT true,
    sequential boolean NOT NULL DEFAULT false,
    preservation_date boolean NOT NULL DEFAULT false,
    kind character(1) NOT NULL DEFAULT 'S'::bpchar,
    class_id integer NOT NULL
);

CREATE TRIGGER archive_metadata_audit
    AFTER INSERT OR UPDATE OR DELETE ON archive_metadata
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Archive Origin
CREATE TABLE archive_origin
(
    id serial NOT NULL,
    name character varying(10) NOT NULL,
    code character varying(5) NOT NULL,
    description character varying(255),
    CONSTRAINT archive_origin_pkey PRIMARY KEY (id)
);

CREATE TABLE audit.archive_origin
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(10) NOT NULL,
    code character varying(5) NOT NULL,
    description character varying(255)
);

CREATE TRIGGER archive_origin_audit
    AFTER INSERT OR UPDATE OR DELETE ON archive_origin
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

INSERT INTO archive_origin (name, code, description) VALUES ('Manuale', 'man', 'Documenti caricati manualmente');

-- Archive Privilege
CREATE TABLE archive_privilege
(
    id serial NOT NULL,
    user_id integer NOT NULL,
    class_id integer NOT NULL,
    view boolean NOT NULL DEFAULT false,
    upload boolean NOT NULL DEFAULT false,
    delete boolean NOT NULL DEFAULT false,
    resend boolean NOT NULL DEFAULT false,
    CONSTRAINT archive_privilege_pkey PRIMARY KEY (id),
    CONSTRAINT archive_privilege_class_id_fkey
        FOREIGN KEY (class_id)
        REFERENCES archive_class (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT archive_privilege_user_id_fkey
        FOREIGN KEY (user_id)
        REFERENCES archive_user (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

COMMENT ON TABLE archive_privilege IS 'User privilege over a document class';

CREATE INDEX fki_archive_privilege_class_id_fkey
    ON archive_privilege
    USING btree (class_id);

CREATE INDEX fki_archive_privilege_user_id_fkey
    ON archive_privilege
    USING btree (user_id);

CREATE TABLE audit.archive_privilege
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    user_id integer NOT NULL,
    class_id integer NOT NULL,
    view boolean NOT NULL DEFAULT false,
    upload boolean NOT NULL DEFAULT false,
    delete boolean NOT NULL DEFAULT false,
    resend boolean NOT NULL DEFAULT false
);

CREATE TRIGGER archive_privilege_audit
    AFTER INSERT OR UPDATE OR DELETE ON archive_privilege
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Archive Document
ALTER TABLE protocol_document RENAME TO archive_document;

ALTER TABLE archive_document RENAME COLUMN protdoc_id TO id;
ALTER TABLE archive_document RENAME COLUMN original_filename TO filename;
ALTER TABLE archive_document RENAME COLUMN upload_user_id TO user_id;
ALTER TABLE archive_document RENAME COLUMN original_filetype TO filetype;

ALTER TABLE archive_document ALTER COLUMN filename SET NOT NULL;
ALTER TABLE archive_document ALTER COLUMN upload_date SET NOT NULL;
ALTER TABLE archive_document ALTER COLUMN filetype SET NOT NULL;

ALTER TABLE archive_document ADD COLUMN archive_user_id integer;
ALTER TABLE archive_document ADD COLUMN path character varying(255);
ALTER TABLE archive_document ADD COLUMN size integer;
ALTER TABLE archive_document ADD COLUMN metadata text NOT NULL DEFAULT '';
ALTER TABLE archive_document ADD COLUMN class_id integer NOT NULL DEFAULT 1;
ALTER TABLE archive_document ADD COLUMN token character varying(255);
ALTER TABLE archive_document ADD COLUMN action_archive_date bigint;
ALTER TABLE archive_document ADD COLUMN action_protocol boolean NOT NULL DEFAULT false;
ALTER TABLE archive_document ADD COLUMN action_protocol_date bigint;
ALTER TABLE archive_document ADD COLUMN action_albo boolean NOT NULL DEFAULT false;
ALTER TABLE archive_document ADD COLUMN action_albo_date bigint;
ALTER TABLE archive_document ADD COLUMN action_trasparenza boolean NOT NULL DEFAULT false;
ALTER TABLE archive_document ADD COLUMN action_trasparenza_date bigint;
ALTER TABLE archive_document ADD COLUMN conserved boolean NOT NULL DEFAULT false;
ALTER TABLE archive_document ADD COLUMN origin_id integer NOT NULL DEFAULT 1;

ALTER TABLE archive_document ALTER COLUMN metadata DROP DEFAULT;
ALTER TABLE archive_document ALTER COLUMN class_id DROP DEFAULT;
ALTER TABLE archive_document ALTER COLUMN origin_id DROP DEFAULT;

ALTER TABLE archive_document DROP COLUMN upload_method;

ALTER TABLE archive_document ADD UNIQUE (token);

ALTER TABLE archive_document
    ADD FOREIGN KEY (user_id)
    REFERENCES users (uid)
    ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE archive_document
    ADD FOREIGN KEY (archive_user_id)
    REFERENCES archive_user (id)
    ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE archive_document
    ADD FOREIGN KEY (class_id)
    REFERENCES archive_class (id)
    ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE archive_document
    ADD FOREIGN KEY (origin_id)
    REFERENCES archive_origin (id)
    ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE archive_document DROP CONSTRAINT protocol_document_pkey;

ALTER TABLE archive_document ADD PRIMARY KEY (id);

ALTER TABLE protocol_document_protdoc_id_seq RENAME TO archive_document_id_seq;

COMMENT ON TABLE archive_document IS 'Reference to archived documents';
COMMENT ON COLUMN archive_document.metadata IS 'Base 64 encoded serialization of MetadataList object';

CREATE INDEX fki_archive_document_archive_user_id_fkey
    ON archive_document
    USING btree (archive_user_id);

CREATE INDEX fki_archive_document_class_id_fkey
    ON archive_document
    USING btree (class_id);

CREATE INDEX fki_archive_document_user_id_fkey
    ON archive_document
    USING btree (user_id);

CREATE INDEX fki_archive_document_origin_id_fkey
    ON archive_document
    USING btree (origin_id);

CREATE TABLE audit.archive_document
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    filename text NOT NULL,
    short_description text,
    description text,
    upload_date bigint NOT NULL,
    user_id integer,
    filetype text NOT NULL,
    archive_user_id integer,
    path character varying(255),
    size integer,
    metadata text NOT NULL,
    class_id integer NOT NULL,
    token character varying(255),
    action_archive_date bigint,
    action_protocol boolean NOT NULL,
    action_protocol_date bigint,
    action_albo boolean NOT NULL,
    action_albo_date bigint,
    action_trasparenza boolean NOT NULL,
    action_trasparenza_date bigint,
    conserved boolean NOT NULL,
    origin_id integer NOT NULL
);

CREATE TRIGGER archive_document_audit
    AFTER INSERT OR UPDATE OR DELETE ON archive_document
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Updates archive_document missing fields values
UPDATE archive_document SET path = to_char(to_timestamp(upload_date), 'YYYY') || '/' || to_char(to_timestamp(upload_date), 'MM') || '/' || 'mc2_document_' || to_char(to_timestamp(upload_date), 'DD-MM-YYYY_FMHH24-MI-SS');

UPDATE archive_document SET action_archive_date = upload_date;
UPDATE archive_document SET action_protocol = 't';
UPDATE archive_document SET action_protocol_date = upload_date;