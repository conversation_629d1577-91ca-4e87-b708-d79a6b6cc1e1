#!/bin/bash

###############################
#
# Creates Documents folder and
# moves documents from old
# folder to the new one
#
###############################

P_FILES=/var/files
P_MC2=$P_FILES/mc2
P_DRAFTS=$P_MC2/drafts
P_DOCUMENTS=/usr/share/mastercom2/files/protocolDocuments

echo "Creating new Documents folder if missing"

if [ ! -d $P_FILES ];
then
    mkdir $P_FILES && chown root:www-data $P_FILES && chmod 750 $P_FILES
fi

if [ ! -d $P_MC2 ];
then
    mkdir $P_MC2 && chown root:www-data $P_MC2 && chmod 770 $P_MC2
fi

if [ ! -d $P_DRAFTS ];
then
    mkdir $P_DRAFTS && chown root:www-data $P_DRAFTS && chmod 770 $P_DRAFTS
fi

echo "Moving documents to new Documents folder"

if [ -d $P_DOCUMENTS ]
then
    for f in $P_DOCUMENTS/*
    do
        FILENAME=${f##*/}
        FILEDATA=$(stat -c %y $f)
        FOLDER=$P_MC2/$(echo $FILEDATA | cut -d '-' -f1)/$(echo $FILEDATA | cut -d '-' -f2)
        NEWFILENAME=$(echo $FILENAME | sed 's/_protocol_/_/')

        echo "Moving "$FILENAME

        if [ ! -d $FOLDER ];
        then
            mkdir -p $FOLDER && chown root:www-data $FOLDER && chmod 770 $FOLDER
        fi

        mv $f $FOLDER/$NEWFILENAME
    done
fi

echo "Done"
