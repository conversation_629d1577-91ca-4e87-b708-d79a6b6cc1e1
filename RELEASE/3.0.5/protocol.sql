-- Alterations of existing protocol tables

-- protocol_correspondent
ALTER TABLE protocol_corrispondenti RENAME TO protocol_correspondent;
ALTER TABLE protocol_correspondent RENAME COLUMN id_corr TO id;
ALTER TABLE protocol_correspondent RENAME COLUMN codice_fiscale TO fiscal_code;
ALTER TABLE protocol_correspondent ALTER COLUMN fiscal_code TYPE character varying(50);
ALTER TABLE protocol_correspondent ALTER COLUMN title SET NOT NULL;
ALTER TABLE protocol_correspondent DROP CONSTRAINT protocol_corrispondenti_pkey;
ALTER TABLE protocol_correspondent ADD PRIMARY KEY (id);
ALTER TABLE protocol_corrispondenti_id_corr_seq RENAME TO protocol_correspondent_id_seq;
ALTER TABLE protocol_correspondent ADD COLUMN city_id integer;
ALTER TABLE protocol_correspondent ADD COLUMN zipcode character varying(5);
ALTER TABLE protocol_correspondent ADD COLUMN address character varying(255);
ALTER TABLE protocol_correspondent ADD COLUMN phone character varying(64);
ALTER TABLE protocol_correspondent ADD COLUMN fax character varying(64);
ALTER TABLE protocol_correspondent ADD COLUMN mobile character varying(64);
ALTER TABLE protocol_correspondent ADD COLUMN email character varying(64);
ALTER TABLE protocol_correspondent ADD COLUMN web character varying(128);

UPDATE protocol_correspondent SET
	city_id = (SELECT city_id FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	zipcode = (SELECT cap FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	address = (SELECT address FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	phone = (SELECT phone_num FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	fax = (SELECT fax FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	mobile = (SELECT mobile FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	email = (SELECT email FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id),
	web = (SELECT web FROM contact WHERE contact.contact_id=protocol_correspondent.contact_id);
ALTER TABLE protocol_correspondent DROP COLUMN contact_id;

ALTER TABLE protocol_correspondent ALTER COLUMN person_type TYPE boolean USING (replace(replace(person_type, 'individual', 'f'), 'legal', 't')::boolean);
ALTER TABLE protocol_correspondent ALTER COLUMN person_type SET DEFAULT 'f';
ALTER TABLE protocol_correspondent RENAME COLUMN person_type TO legal_person;
COMMENT ON COLUMN protocol_correspondent.legal_person IS 'Persona fisica = FALSE / giuridica = TRUE';

ALTER TABLE protocol_correspondent ALTER COLUMN correspondent_type TYPE character varying(1) USING (
    regexp_replace(
        replace(
            replace(
                replace(
                    replace(
                        replace(correspondent_type, 'student', 'S'),
                        'school', 'I'),
                    'supplier', 'F'),
                'employee', 'P'),
            'manual', 'M'),
        '.*[t].*', 'T'));
ALTER TABLE protocol_correspondent ALTER COLUMN correspondent_type SET DEFAULT 'M';
COMMENT ON COLUMN protocol_correspondent.correspondent_type IS 'Type Student = S / Personnel = P / Tutor = T / Institute = I / Supplier = F / Manual = M';


-- protocol_protocol
ALTER TABLE protocol RENAME TO protocol_protocol;
ALTER TABLE protocol_protocol RENAME COLUMN protocol_id TO id;
ALTER TABLE protocol_protocol RENAME COLUMN mezzo TO send_method_id;
ALTER TABLE protocol_protocol RENAME COLUMN assoc_prot_doc_id TO document_id;
ALTER TABLE protocol_protocol RENAME COLUMN item_kind TO subject_kind_id;
ALTER TABLE protocol_protocol RENAME COLUMN prot_num TO protocol_number;
ALTER TABLE protocol_protocol RENAME COLUMN description TO note;
ALTER TABLE protocol_protocol RENAME COLUMN fascicolo  TO dossier;
ALTER TABLE protocol_protocol RENAME COLUMN object TO description;
ALTER TABLE protocol_protocol RENAME COLUMN sender_receiver TO correspondents_text;
ALTER TABLE protocol_protocol ALTER COLUMN date SET NOT NULL;
ALTER TABLE protocol_protocol ALTER COLUMN type_id SET NOT NULL;
ALTER TABLE protocol_protocol ALTER COLUMN protocol_number SET NOT NULL;
ALTER TABLE protocol_protocol DROP CONSTRAINT protocol_pkey;
ALTER TABLE protocol_protocol ADD PRIMARY KEY (id);
ALTER TABLE protocol_id_seq RENAME TO protocol_protocol_id_seq;


-- protocol_protocol_correspondent
ALTER TABLE protocol_link_corrispondenti RENAME TO protocol_protocol_correspondent;
ALTER TABLE protocol_protocol_correspondent RENAME COLUMN id_protocol TO protocol_id;
ALTER TABLE protocol_protocol_correspondent RENAME COLUMN id_corrispondent TO correspondent_id;
ALTER TABLE protocol_protocol_correspondent ALTER COLUMN protocol_id DROP DEFAULT;
ALTER TABLE protocol_protocol_correspondent ALTER COLUMN correspondent_id DROP DEFAULT;

DELETE FROM protocol_protocol_correspondent WHERE id NOT IN (
    SELECT MAX(dup.id) FROM protocol_protocol_correspondent AS dup GROUP BY dup.protocol_id, dup.correspondent_id
);

DELETE FROM protocol_protocol_correspondent WHERE correspondent_id NOT IN (
    SELECT id FROM protocol_correspondent
);

DELETE FROM protocol_protocol_correspondent WHERE protocol_id NOT IN (
    SELECT id FROM protocol_protocol
);

ALTER TABLE protocol_protocol_correspondent DROP COLUMN id;

INSERT INTO protocol_protocol_correspondent
    SELECT id as protocol_id, correspondent as correspondent_id
        FROM protocol_protocol
        WHERE id NOT IN (
            SELECT p.id
                FROM protocol_protocol p, protocol_protocol_correspondent c
                WHERE p.id = c.protocol_id
                    AND p.correspondent = c.correspondent_id
                    AND p.correspondent > 0
        )
        AND correspondent > 0;

ALTER TABLE protocol_protocol DROP COLUMN correspondent;

ALTER TABLE protocol_protocol_correspondent ADD PRIMARY KEY (protocol_id, correspondent_id);


-- protocol_protocol_document
ALTER TABLE assoc_prot_doc RENAME TO protocol_protocol_document;
ALTER TABLE protocol_protocol_document RENAME COLUMN protdoc_id TO document_id;

DELETE FROM protocol_protocol_document WHERE assoc_prot_doc_id NOT IN (
    SELECT MAX(dup.assoc_prot_doc_id) FROM protocol_protocol_document AS dup GROUP BY dup.protocol_id, dup.document_id
);

DELETE FROM protocol_protocol_document WHERE document_id NOT IN (
    SELECT id FROM archive_document
);

DELETE FROM protocol_protocol_document WHERE protocol_id NOT IN (
    SELECT id FROM protocol_protocol
);

ALTER TABLE protocol_protocol_document DROP COLUMN assoc_prot_doc_id;
ALTER TABLE protocol_protocol_document ADD PRIMARY KEY (protocol_id, document_id);
COMMENT ON TABLE protocol_protocol_document IS NULL;


-- protocol_send_method
ALTER TABLE protocol_mezzi RENAME TO protocol_send_method;
ALTER TABLE protocol_send_method RENAME COLUMN id_mezzi TO id;
ALTER TABLE protocol_send_method ALTER COLUMN title TYPE character varying(100);
ALTER TABLE protocol_send_method ALTER COLUMN title SET NOT NULL;
ALTER TABLE protocol_send_method DROP CONSTRAINT protocol_mezzi_pkey;
ALTER TABLE protocol_send_method ADD PRIMARY KEY (id);
ALTER TABLE protocol_mezzi_id_mezzi_seq RENAME TO protocol_send_method_id_seq;


-- protocol_subject_kind
ALTER TABLE protocol_items_kind RENAME TO protocol_subject_kind;
ALTER TABLE protocol_subject_kind RENAME COLUMN protocol_item_id TO id;
ALTER TABLE protocol_subject_kind RENAME COLUMN description TO title;
ALTER TABLE protocol_subject_kind ALTER COLUMN title TYPE character varying(100);
ALTER TABLE protocol_subject_kind ALTER COLUMN title DROP DEFAULT;
ALTER TABLE protocol_subject_kind DROP CONSTRAINT protocol_items_kind_pkey;
ALTER TABLE protocol_subject_kind ADD PRIMARY KEY (id);
ALTER TABLE protocol_items_kind_protocol_item_id_seq RENAME TO protocol_subject_kind_id_seq;


-- protocol_type
UPDATE protocol_type SET code_1 = code_2 WHERE code_2 IS NOT NULL;
UPDATE protocol_type SET code_1 = code_3 WHERE code_3 IS NOT NULL;
UPDATE protocol_type SET parent_type_id = NULL WHERE parent_type_id = 0;
ALTER TABLE protocol_type DROP COLUMN IF EXISTS upper;
ALTER TABLE protocol_type DROP COLUMN IF EXISTS code_3;
ALTER TABLE protocol_type DROP COLUMN IF EXISTS code_2;
ALTER TABLE protocol_type RENAME COLUMN code_1 TO code;
ALTER TABLE protocol_type RENAME COLUMN type_id TO id;
ALTER TABLE protocol_type ALTER COLUMN code SET NOT NULL;
ALTER TABLE protocol_type ALTER COLUMN parent_type_id DROP DEFAULT;
ALTER TABLE protocol_type ADD UNIQUE (code, parent_type_id);
ALTER TABLE protocol_type_type_id_seq RENAME TO protocol_type_id_seq;


-- New Protocol Tables

-- Protocol Action
CREATE TABLE protocol_action
(
    id serial NOT NULL,
    description character varying(100) NOT NULL,
    comment character varying(255),
    active boolean NOT NULL DEFAULT false,
    type_id bigint,
    CONSTRAINT protocol_action_pkey PRIMARY KEY (id),
    CONSTRAINT protocol_action_type_id_fkey
        FOREIGN KEY (type_id)
        REFERENCES protocol_type (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE SET NULL
);

COMMENT ON TABLE protocol_action IS 'Protocol Action';


-- Protocol Protocol-History
CREATE TABLE protocol_protocol_history
(
    id serial NOT NULL,
    action character varying(1) NOT NULL,
    date bigint NOT NULL,
    protocol_id integer NOT NULL,
    user_id integer NOT NULL,
    CONSTRAINT protocol_protocol_history_pkey PRIMARY KEY (id),
    CONSTRAINT protocol_protocol_history_protocol_id_fkey
        FOREIGN KEY (protocol_id)
        REFERENCES protocol_protocol (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT protocol_protocol_history_user_id_fkey
        FOREIGN KEY (user_id)
        REFERENCES users (uid)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE SET NULL
);

COMMENT ON TABLE protocol_protocol_history IS 'Protocol History';
COMMENT ON COLUMN protocol_protocol_history.action IS 'I: Insertion, E: Edit, D: Deletion, C: Cancelation, R: Set reserved, N: Unset reserved';


-- Protocol Protocol-Protocol
CREATE TABLE protocol_protocol_protocol
(
    protocol_1_id integer NOT NULL,
    protocol_2_id integer NOT NULL,
    parent_relationship boolean NOT NULL DEFAULT false,
    CONSTRAINT protocol_protocol_protocol_pkey PRIMARY KEY (protocol_1_id, protocol_2_id),
    CONSTRAINT protocol_protocol_protocol_protocol_1_id_fkey
        FOREIGN KEY (protocol_1_id)
        REFERENCES protocol_protocol (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT protocol_protocol_protocol_protocol_2_id_fkey
        FOREIGN KEY (protocol_2_id)
        REFERENCES protocol_protocol (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

COMMENT ON COLUMN protocol_protocol_protocol.parent_relationship IS 'Parent relationship: 1 parent of 2.';


-- New Protocol audit Tables

-- Protocol Action
CREATE TABLE audit.protocol_action
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    description character varying(100) NOT NULL,
    comment character varying(255),
    active boolean NOT NULL DEFAULT false,
    type_id bigint
);


-- Protocol Correspondent
CREATE TABLE audit.protocol_correspondent
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    title text NOT NULL,
    note text,
    person_type text,
    correspondent_type_id bigint DEFAULT (-1),
    correspondent_type text NOT NULL DEFAULT 'manual'::text,
    fiscal_code character varying(50),
    city_id integer,
    zipcode character varying(5),
    address character varying(255),
    phone character varying(64),
    fax character varying(64),
    mobile character varying(64),
    email character varying(64),
    web character varying(128)
);


-- Protocol Protocol
CREATE TABLE audit.protocol_protocol
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id bigint NOT NULL,
    date bigint NOT NULL,
    note text,
    budget_id smallint,
    type_id integer NOT NULL,
    rec_id integer,
    rec_type smallint,
    protocol_number integer NOT NULL,
    obj_id integer,
    direction character varying(1),
    description text,
    correspondent_text text,
    canceled boolean NOT NULL DEFAULT false,
    external_act_number text,
    send_method_id bigint,
    dossier text,
    subject_kind_id integer NOT NULL,
    reserved boolean NOT NULL DEFAULT false
);


-- Protocol Protocol-Correspondent
CREATE TABLE audit.protocol_protocol_correspondent
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    protocol_id bigint NOT NULL,
    correspondent_id bigint NOT NULL
);


-- Protocol Protocol-Document
CREATE TABLE audit.protocol_protocol_document
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    protocol_id integer NOT NULL,
    document_id integer NOT NULL
);


-- Protocol Protocol-History
CREATE TABLE audit.protocol_protocol_history
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    action character varying(1) NOT NULL,
    date bigint NOT NULL,
    protocol_id integer NOT NULL,
    user_id integer NOT NULL
);


-- Protocol Protocol-Protocol
CREATE TABLE audit.protocol_protocol_protocol
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    protocol_1_id integer NOT NULL,
    protocol_2_id integer NOT NULL,
    parent_relationship boolean NOT NULL DEFAULT false
);


-- Protocol Send Method
CREATE TABLE audit.protocol_send_method
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    title character varying(100) NOT NULL
);


-- Protocol Subject Kind
CREATE TABLE audit.protocol_subject_kind
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    title character varying(100) NOT NULL
);


-- Protocol Type
CREATE TABLE audit.protocol_type
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    code character varying(20) NOT NULL,
    description character varying(255),
    parent_type_id bigint
);


-- Fixes to constraints
INSERT INTO protocol_type
    SELECT distinct type_id AS id, 'XXX' AS code
    FROM protocol_protocol
    WHERE type_id NOT IN (SELECT id FROM protocol_type);

INSERT INTO protocol_correspondent
    SELECT distinct correspondent_id AS id, 'XXX' AS title
    FROM protocol_protocol_correspondent
    WHERE correspondent_id NOT IN (SELECT id FROM protocol_correspondent);


-- New Constraints
ALTER TABLE protocol_protocol
    ADD CONSTRAINT protocol_protocol_type_id_fkey
    FOREIGN KEY (type_id)
    REFERENCES protocol_type (id)
    MATCH SIMPLE ON UPDATE NO ACTION ON DELETE SET NULL;

ALTER TABLE protocol_protocol_correspondent
    ADD CONSTRAINT protocol_protocol_correspondent_correspondent_id_fkey
    FOREIGN KEY (correspondent_id)
    REFERENCES protocol_correspondent (id)
    ON UPDATE NO ACTION ON DELETE CASCADE;

ALTER TABLE protocol_protocol_correspondent
    ADD CONSTRAINT protocol_protocol_correspondent_protocol_id_fkey
    FOREIGN KEY (protocol_id)
    REFERENCES protocol_protocol (id)
    ON UPDATE NO ACTION ON DELETE CASCADE;

ALTER TABLE protocol_protocol_document
    ADD CONSTRAINT protocol_protocol_document_document_id_fkey
    FOREIGN KEY (document_id)
    REFERENCES archive_document (id)
    ON UPDATE NO ACTION ON DELETE CASCADE;

ALTER TABLE protocol_protocol_document
    ADD CONSTRAINT protocol_protocol_document_protocol_id_fkey
    FOREIGN KEY (protocol_id)
    REFERENCES protocol_protocol (id)
    ON UPDATE NO ACTION ON DELETE CASCADE;

ALTER TABLE protocol_type
    ADD CONSTRAINT protocol_type_parent_type_id_fkey
    FOREIGN KEY (parent_type_id)
    REFERENCES protocol_type (id)
    MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE;


-- New Indexes
CREATE INDEX protocol_action_type_id_fki
    ON protocol_action
    USING btree (type_id);

CREATE INDEX protocol_protocol_type_id_fki
    ON protocol_protocol
    USING btree (type_id);

CREATE INDEX protocol_type_parent_type_id_fki
    ON protocol_type
    USING btree (parent_type_id);

CREATE INDEX protocol_protocol_correspondent_correspondent_id_fki
    ON protocol_protocol_correspondent
    USING btree (correspondent_id);

CREATE INDEX protocol_protocol_correspondent_protocol_id_fki
    ON protocol_protocol_correspondent
    USING btree (protocol_id);

CREATE INDEX protocol_protocol_document_document_id_fki
    ON protocol_protocol_document
    USING btree (document_id);

CREATE INDEX protocol_protocol_document_protocol_id_fki
    ON protocol_protocol_document
    USING btree (protocol_id);

CREATE INDEX protocol_protocol_history_protocol_id_fki
    ON protocol_protocol_history
    USING btree (protocol_id);

CREATE INDEX protocol_protocol_history_user_id_fki
    ON protocol_protocol_history
    USING btree (user_id);

CREATE INDEX protocol_protocol_protocol_protocol_1_id_fki
    ON protocol_protocol_protocol
    USING btree (protocol_1_id);

CREATE INDEX protocol_protocol_protocol_protocol_2_id_fki
    ON protocol_protocol_protocol
    USING btree (protocol_2_id);


-- New Triggers
CREATE TRIGGER protocol_action_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_action
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_correspondent_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_correspondent
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_protocol_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_protocol_correspondent_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_correspondent
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_protocol_document_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_document
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_protocol_history_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_history
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_send_method_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_send_method
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_subject_kind_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_subject_kind
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER protocol_type_audit
    AFTER INSERT OR UPDATE OR DELETE ON protocol_type
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- NEW Protocol Actions
INSERT INTO protocol_action (description, comment, active)
    VALUES ('Permesso per assenza', 'Permesso per assenza', 't');

INSERT INTO protocol_action (description, comment, active)
    VALUES ('Ordine di Magazzino', 'Ordine di Magazzino', 't');

INSERT INTO protocol_action (description, comment, active)
    VALUES ('Conferma di Magazzino', 'Ordine di Magazzino confermato', 't');

INSERT INTO protocol_action (description, comment, active)
    VALUES ('Scarico Inventario', 'Oggetto scaricato dall''inventario', 't');

INSERT INTO protocol_action (description, comment, active)
    VALUES ('Scarico Beni Durevoli', 'Oggetto scaricato da Beni Durevoli', 't');

INSERT INTO protocol_action (description, comment, active)
    VALUES ('Scarico Facile Consumo', 'Oggetto scaricato da Facile Consumo', 't');


-- Sets all action to FALSE
UPDATE protocol_action SET active = 'f';


-- Fixes to protocol_protocol
ALTER TABLE protocol_protocol ALTER COLUMN direction_type TYPE character varying(1) USING (
    replace(
        replace(
            replace(direction_type, 'outgoing', 'O'),
            'incoming', 'I'),
        'internal', 'L'));
ALTER TABLE protocol_protocol ALTER COLUMN direction_type SET DEFAULT 'L';
ALTER TABLE protocol_protocol RENAME COLUMN direction_type TO direction;
COMMENT ON COLUMN protocol_protocol.direction IS 'Interno = L / Entrante = I / Uscente = O';


-- Record creation for protocol_protocol_protocol
INSERT INTO protocol_protocol_protocol (protocol_1_id, protocol_2_id) (SELECT id AS protocol_1_id, source_doc AS protocol_2_id FROM protocol_protocol WHERE source_doc > 0);
ALTER TABLE protocol_protocol DROP COLUMN source_doc;


-- Record creation for protocol_protocol_document
INSERT INTO protocol_protocol_document (protocol_id, document_id) (SELECT id AS protocol_id, document_id FROM protocol_protocol WHERE document_id > 0);
ALTER TABLE protocol_protocol DROP COLUMN document_id;


-- DROP of obsolete Tables
DROP TABLE protocol_modules;
DROP TABLE protocol_modules_del;
DROP TABLE protocol_corrispondenti_del;
DROP TABLE protocol_document_del;
DROP TABLE protocol_items_kind_del;
DROP TABLE protocol_link_corrispondenti_del;
DROP TABLE protocol_mezzi_del;
DROP TABLE protocol_type_del;
DROP TABLE assoc_prot_doc_del;


-- DROP of obsolete sequences
DROP SEQUENCE IF EXISTS protocol_link_corrispondenti_id_seq;
DROP SEQUENCE IF EXISTS assoc_prot_doc_assoc_prot_doc_id_seq;