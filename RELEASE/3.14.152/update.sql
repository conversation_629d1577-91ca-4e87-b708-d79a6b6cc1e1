-- Campo short_description per tipi movimento

ALTER TABLE ccp_type
  ADD COLUMN short_desc CHARACTER VARYING(20);

  COMMENT ON COLUMN ccp_type.short_desc IS 'Short description for the type of movement';

ALTER TABLE audit_ccp_type
  ADD COLUMN short_desc CHARACTER VARYING(20);


INSERT INTO parameter(name, value) VALUES ('TYPE_SHORT_DESC', 'f');



-- Campo note fattura
ALTER TABLE ccp_invoice
  ADD COLUMN invoice_notes TEXT;

COMMENT ON COLUMN ccp_invoice.invoice_notes IS 'Notes for the invoice';

ALTER TABLE audit_ccp_invoice
  ADD COLUMN invoice_notes TEXT;



-- Campo cartella posta inviata
ALTER TABLE archive_mail_account
  ADD COLUMN sent_folder CHARACTER VARYING(255);

COMMENT ON COLUMN archive_mail_account.sent_folder IS 'Folder for sent emails';

ALTER TABLE audit_archive_mail_account
  ADD COLUMN sent_folder CHARACTER VARYING(255);

-- <PERSON> posta in arrivo
ALTER TABLE archive_mail
  ADD COLUMN is_sent BOOLEAN DEFAULT false;

COMMENT ON COLUMN archive_mail.is_sent IS 'Indicates if the email is sent (true) or received (false)';

ALTER TABLE audit_archive_mail
  ADD COLUMN is_sent BOOLEAN;

