#!/usr/bin/php
<?php

$rootPath = '/var/www/mc2';
$confMilanoPath = '/etc/mastercom2/conf_milano.php';
$md5FilePath = '/etc/mastercom2/md5_actual_schema';

// Write the api configuration from file to database only if the config file still exists.
// Otherwise there will be already in database
if (file_exists($confMilanoPath)) {

    // Load init conf file for database connection and global variables
    require_once $rootPath . '/configurations/init-vars.php';
    require_once $rootPath . '/configurations/init-autoload.php';
    require_once $rootPath . '/configurations/init-db.php';
    require_once $rootPath . '/configurations/init-locale.php';

    $db = Db::getInstance();
    include $confMilanoPath;

    $query =  "UPDATE parameter SET value = '".API_URL."' WHERE name = 'API_URL';";
    $query .= "UPDATE parameter SET value = '".API_ROOT."' WHERE name = 'API_ROOT';";
    $query .= "UPDATE parameter SET value = '".API_PORT."' WHERE name = 'API_PORT';";
    $query .= "UPDATE parameter SET value = '".SCHOOL_ID."' WHERE name = 'SCHOOL_ID';";
    $query .= "UPDATE parameter SET value = '".SEAT_ID."' WHERE name = 'SEAT_ID';";

    $db->query($query);

    exec("rm -rf /etc/mastercom2/");

}
