-- New API parameters
DELETE FROM parameter WHERE name IN ('API_URL','API_ROOT','API_PORT','SCHOOL_ID','SEAT_ID','MD5_SCHEMA_DB');

INSERT INTO parameter (name, value) VALUES ('API_URL', 'https://api-scuole.registroelettronico.com');
INSERT INTO parameter (name, value) VALUES ('API_ROOT', '/api/1.2');
INSERT INTO parameter (name, value) VALUES ('API_PORT', '');
INSERT INTO parameter (name, value) VALUES ('SCHOOL_ID', '0');
INSERT INTO parameter (name, value) VALUES ('SEAT_ID', '0');

-- New MD5 parameter
INSERT INTO parameter (name, value) VALUES ('MD5_SCHEMA_DB', '');

-- New Versions parameter
INSERT INTO parameter (name, value) VALUES ('VERSION_OLD', '0.0.1');