/* Tables */

CREATE TABLE "public"."core_contacts" ( 
	"id" serial NOT NULL,
	"name" Character Varying( 255 ) NOT NULL,
	"email" Character Varying( 255 ) NOT NULL,
	"contact_type_id" Integer NOT NULL,
	"user_id" Integer,
	PRIMARY KEY ( "id" ),
	CONSTRAINT "core_contacts_email_key" UNIQUE( "email" ) );
 ;

CREATE TABLE "audit"."core_contacts" ( 
	"op_action" Character Varying( 1 ) NOT NULL,
	"op_date" Timestamp With Time Zone DEFAULT now() NOT NULL,
	"id" integer NOT NULL,
	"name" Character Varying( 255 ) NOT NULL,
	"email" Character Varying( 255 ) NOT NULL,
	"contact_type_id" Integer NOT NULL,
	"user_id" Integer )		
 ;


CREATE TABLE "public"."core_contact_type" ( 
	"id" serial NOT NULL,
	"name" Character Varying( 255 ) NOT NULL,
	"code" Character Varying( 20 ) NOT NULL,
	PRIMARY KEY ( "id" ) 
);
 
 

CREATE TABLE "audit"."core_contact_type" (
	"op_action" Character Varying( 1 ) NOT NULL,
	"op_date" Timestamp With Time Zone DEFAULT now() NOT NULL,
	"id" integer NOT NULL,
	"name" Character Varying( 255 ) NOT NULL,
	"code" Character Varying( 20 ) NOT NULL
 );


CREATE TABLE "public"."core_contactgroups" ( 
	"id" serial NOT NULL,
	"name" Character Varying( 255 ) NOT NULL,
	"user_id" Integer NOT NULL,
	PRIMARY KEY ( "id" ) 
);
 

CREATE TABLE "audit"."core_contactgroups" ( 
	"op_action" Character Varying( 1 ) NOT NULL,
	"op_date" Timestamp With Time Zone DEFAULT now() NOT NULL,
	"id" Integer NOT NULL,
	"name" Character Varying( 255 ) NOT NULL,
	"user_id" Integer NOT NULL	
);


CREATE TABLE "public"."core_contacts_contactgroups" ( 
	"id" serial NOT NULL,
	"contact_id" Integer NOT NULL,
	"group_id" Integer NOT NULL,
	PRIMARY KEY ( "id" ),
	CONSTRAINT "unique_contact_id_group_id" UNIQUE( "contact_id", "group_id" ) )
 ;

 CREATE TABLE "audit"."core_contacts_contactgroups" ( 
	"op_action" Character Varying( 1 ) NOT NULL,
	"op_date" Timestamp With Time Zone DEFAULT now() NOT NULL, 
	"id" Integer  NOT NULL,
	"contact_id" Integer NOT NULL,
	"group_id" Integer NOT NULL )
 ;






/* Relations */
-- -------------------------------------------------------------
ALTER TABLE "public"."core_contacts"
	ADD CONSTRAINT "lnk_core_contact_type_core_contacts" FOREIGN KEY ( "contact_type_id" )
	REFERENCES "public"."core_contact_type" ( "id" ) MATCH FULL
	ON DELETE Cascade
	ON UPDATE No Action;


-- -------------------------------------------------------------

ALTER TABLE "public"."core_contacts"
	ADD CONSTRAINT "lnk_users_core_contacts" FOREIGN KEY ( "user_id" )
	REFERENCES "public"."users" ( "uid" ) MATCH FULL
	ON DELETE Cascade
	ON UPDATE No Action;

-- -------------------------------------------------------------
ALTER TABLE "public"."core_contactgroups"
	ADD CONSTRAINT "lnk_users_core_contactgroups" FOREIGN KEY ( "user_id" )
	REFERENCES "public"."users" ( "uid" ) MATCH FULL
	ON DELETE Cascade
	ON UPDATE No Action;

-- -------------------------------------------------------------
ALTER TABLE "public"."core_contacts_contactgroups"
	ADD CONSTRAINT "lnk_core_contacts_core_contacts_contactgroups" FOREIGN KEY ( "contact_id" )
	REFERENCES "public"."core_contacts" ( "id" ) MATCH FULL
	ON DELETE Cascade
	ON UPDATE No Action;

-- -------------------------------------------------------------
ALTER TABLE "public"."core_contacts_contactgroups"
	ADD CONSTRAINT "lnk_core_contactgroups_core_contacts_contactgroups" FOREIGN KEY ( "group_id" )
	REFERENCES "public"."core_contactgroups" ( "id" ) MATCH FULL
	ON DELETE Cascade
	ON UPDATE No Action;





/* Triggers */


CREATE TRIGGER core_contacts_audit AFTER INSERT OR UPDATE OR DELETE ON core_contacts FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();
CREATE TRIGGER core_contact_type_audit AFTER INSERT OR UPDATE OR DELETE ON core_contact_type FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();
CREATE TRIGGER core_contact_groups_audit AFTER INSERT OR UPDATE OR DELETE ON "public"."core_contactgroups" FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();
CREATE TRIGGER core_contacts_contactgroups_audit AFTER INSERT OR UPDATE OR DELETE ON core_contacts_contactgroups FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


/* Data insert */

INSERT INTO core_contact_type( id, name, code ) SELECT '1','Email','Email' WHERE NOT EXISTS (SELECT id FROM core_contact_type WHERE id = 1) RETURNING id;
INSERT INTO core_contact_type( id, name, code ) SELECT '2','Mittenti/destinatari protocollo','Protocol' WHERE NOT EXISTS (SELECT id FROM core_contact_type WHERE id = 2) RETURNING id;
INSERT INTO core_contact_type( id, name, code ) SELECT '3','Personale','Employee' WHERE NOT EXISTS (SELECT id FROM core_contact_type WHERE id = 3) RETURNING id;
INSERT INTO core_contact_type( id, name, code ) SELECT '4','Indirizzi aggiuntivi','Custom' WHERE NOT EXISTS (SELECT id FROM core_contact_type WHERE id = 4) RETURNING id;



INSERT INTO parameter(name,value) VALUES ('JSON_API_BASETOKEN','');


INSERT INTO parameter (name, value) VALUES ('SIGN_TYPE', 'REMOTE');

DROP VIEW ccp_view_payment;
CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.expiration_date, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_seat, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, m.expiration_date as expiration_date, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_seat, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

 INSERT INTO archive_mail_security (name) VALUES ('notls');

 ALTER TABLE archive_mail_account ADD COLUMN outname Character Varying( 255 );
ALTER TABLE audit.archive_mail_account ADD COLUMN outname Character Varying( 255 );


ALTER TABLE protocol_protocol ADD COLUMN header_position Character Varying( 255 );
ALTER TABLE audit.protocol_protocol ADD COLUMN header_position Character Varying( 255 );




-- CHANGE "TEXT" OF "VIEW "protocol_view_protocol" -------------
CREATE OR REPLACE VIEW "public"."protocol_view_protocol" AS  SELECT p.id,
    p.date,
    p.note,
    p.budget_id,
    p.type_id,
    p.rec_id,
    p.rec_type,
    p.protocol_number,
    p.obj_id,
    p.direction,
    p.description,
    p.correspondents_text,
    p.canceled,
    p.external_act_number,
    p.send_method_id,
    p.dossier,
    p.subject_kind_id,
    p.reserved,
    sm.title AS send_method_text,
    sk.title AS subject_kind_text,
    ( SELECT btrim((((((c.c3)::text || ' '::text) || (c.c2)::text) || ' '::text) || (c.c1)::text), ' '::text) AS btrim
           FROM ( SELECT
                        CASE
                            WHEN (p3.code IS NULL) THEN ''::character varying
                            ELSE p3.code
                        END AS c3,
                        CASE
                            WHEN (p2.code IS NULL) THEN ''::character varying
                            ELSE p2.code
                        END AS c2,
                        CASE
                            WHEN (p1.code IS NULL) THEN ''::character varying
                            ELSE p1.code
                        END AS c1
                   FROM ((protocol_type p1
                     LEFT JOIN protocol_type p2 ON ((p1.parent_type_id = p2.id)))
                     LEFT JOIN protocol_type p3 ON ((p2.parent_type_id = p3.id)))
                  WHERE (p1.id = p.type_id)) c) AS type_text,
    ( SELECT count(pd.document_id) AS count
           FROM protocol_protocol_document pd
          WHERE (pd.protocol_id = p.id)) AS count_documents,
    ( SELECT string_agg((pd.document_id)::text, ','::text) AS string_agg
           FROM protocol_protocol_document pd
          WHERE (pd.protocol_id = p.id)) AS linked_documents,
    ( SELECT count(pc.correspondent_id) AS count
           FROM protocol_protocol_correspondent pc
          WHERE (pc.protocol_id = p.id)) AS count_correspondents,
    ( SELECT string_agg((pc.correspondent_id)::text, ','::text) AS string_agg
           FROM protocol_protocol_correspondent pc
          WHERE (pc.protocol_id = p.id)) AS linked_correspondents,
    ( SELECT count(pp.protocol_1_id) AS count
           FROM protocol_protocol_protocol pp
          WHERE ((pp.protocol_1_id = p.id) OR (pp.protocol_2_id = p.id))) AS count_protocols,
    ( SELECT string_agg((protocols.id)::text, ','::text) AS string_agg
           FROM ( SELECT pp.protocol_1_id AS id
                   FROM protocol_protocol_protocol pp
                  WHERE (pp.protocol_2_id = p.id)
                UNION
                 SELECT pp.protocol_2_id AS id
                   FROM protocol_protocol_protocol pp
                  WHERE (pp.protocol_1_id = p.id)) protocols) AS linked_protocols,
   p.header_position
   FROM (((protocol_protocol p
     LEFT JOIN protocol_type pt ON ((p.type_id = pt.id)))
     LEFT JOIN protocol_send_method sm ON ((p.send_method_id = sm.id)))
     LEFT JOIN protocol_subject_kind sk ON ((p.subject_kind_id = sk.id)));;
-- -------------------------------------------------------------



INSERT INTO absence_kind (code, description) VALUES ('A043', 'ASSENZA GIORNALIERA VISITA MEDICA');
INSERT INTO absence_kind (code, description) VALUES ('A042', 'ASSENZA ORARIA VISITA MEDICA');


INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (83, 4, 'Dati ulteriori', '', 23);


INSERT INTO parameter (name, value) VALUES ('PROTOCOL_HEADER', '{codice_meccanografico} Prot. n {numero_protocollo} del {data} - Tit. {titolario}');
