ALTER TABLE public.core_bank_account
    ADD COLUMN agency_name character varying(50);

COMMENT ON COLUMN public.core_bank_account.agency_name
    IS 'denominazione agenzia bancaria';


ALTER TABLE public.core_bank_account
    ADD COLUMN agency_city character varying(50);

COMMENT ON COLUMN public.core_bank_account.agency_city
    IS 'località dell''agenzia';

ALTER TABLE public.core_bank_account
    ADD COLUMN agency_zip_code character varying(10);

COMMENT ON COLUMN public.core_bank_account.agency_zip_code
    IS 'cap agenzia ';


ALTER TABLE audit.core_bank_account
    ADD COLUMN agency_name character varying(50);

ALTER TABLE audit.core_bank_account
    ADD COLUMN agency_city character varying(50);


ALTER TABLE audit.core_bank_account
    ADD COLUMN agency_zip_code character varying(10);