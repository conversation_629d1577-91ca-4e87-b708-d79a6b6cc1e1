ALTER TABLE ccp_payment_intents ADD COLUMN subject_data text;
ALTER TABLE ccp_payment_intents ADD COLUMN subject_school_address text;
ALTER TABLE ccp_payment_intents ADD COLUMN subject_school_address_code text;
ALTER TABLE ccp_payment_intents ADD COLUMN subject_class text;

ALTER TABLE ccp_payment_intents ADD COLUMN payer_surname text;
ALTER TABLE ccp_payment_intents ADD COLUMN payer_name text;
ALTER TABLE ccp_payment_intents ADD COLUMN payer_fiscal_code text;
ALTER TABLE ccp_payment_intents ADD COLUMN payer_address text;
ALTER TABLE ccp_payment_intents ADD COLUMN payer_city text;
ALTER TABLE ccp_payment_intents ADD COLUMN payer_province text;
ALTER TABLE ccp_payment_intents ADD COLUMN payer_zip_code text;

ALTER TABLE IF EXISTS audit_ccp_payment_intents
    ADD COLUMN subject_data text;
ALTER TABLE IF EXISTS audit_ccp_payment_intents
    ADD COLUMN subject_school_address text;
ALTER TABLE IF EXISTS audit_ccp_payment_intents
    ADD COLUMN subject_school_address_code text;
ALTER TABLE IF EXISTS audit_ccp_payment_intents
    ADD COLUMN subject_class text;

ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_surname text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_name text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_fiscal_code text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_address text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_city text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_province text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_zip_code text;


ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN subject_data text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN subject_school_address text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN subject_school_address_code text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN subject_class text;

ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_surname text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_name text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_fiscal_code text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_address text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_city text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_province text;
ALTER TABLE IF EXISTS audit.ccp_payment_intents
    ADD COLUMN payer_zip_code text;