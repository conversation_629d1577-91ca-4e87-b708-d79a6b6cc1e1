CREATE TABLE institute_role (
    id serial NOT NULL,
    institute_id integer NOT NULL,
    name character varying(255) NOT NULL,
    surname character varying(255) NOT NULL,
    fiscal_code character varying(16),
    type character varying(255) NOT NULL,

    CONSTRAINT institute_role_institute_id_fkey FOREIGN KEY (institute_id) REFERENCES institute(institute_id) ON DELETE CASCADE ON UPDATE CASCADE
);

-- INSERISCE SOLO SE E' POPOLATO IL RUOLO NEL VECCHIO MODO
INSERT INTO institute_role (institute_id, name, surname, fiscal_code, type)
SELECT i.institute_id, e.name, e.surname, e.fiscal_code, 'protocol_manager'
FROM institute i
JOIN employee e ON i.job_registry_id = e.employee_id
WHERE i.def = 't'
  AND i.job_registry_id IS NOT NULL
LIMIT 1;

ALTER TABLE users ADD column fiscal_code character varying(16);

-- INSERISCE SOLO SE E' POPOLATO l'EMPLOYEE ID DELL'UTENTE
UPDATE users SET name = (SELECT name FROM employee WHERE employee.employee_id = users.employee_id);
UPDATE users SET surname = (SELECT surname FROM employee WHERE employee.employee_id = users.employee_id);
UPDATE users SET fiscal_code = (SELECT fiscal_code FROM employee WHERE employee.employee_id = users.employee_id);