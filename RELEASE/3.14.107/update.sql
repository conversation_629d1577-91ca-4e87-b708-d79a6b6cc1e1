CREATE TABLE ccp_reminder_subjects (
    id serial PRIMARY KEY,
    ccp_reminder_id INT NOT NULL,
    subject_id INT NOT NULL,
    CONSTRAINT fk_ccp_reminder_subjects_ccp_reminder
        FOREIGN KEY (ccp_reminder_id) 
        REFERENCES ccp_reminder(id)
        ON DELETE CASCADE
);

CREATE TABLE audit.ccp_reminder_subjects (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id INT,
    ccp_reminder_id INT,
    subject_id INT
);

CREATE INDEX idx_ccp_reminder_subjects ON ccp_reminder_subjects(ccp_reminder_id);

CREATE TRIGGER ccp_reminder_subjects_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_reminder_subjects
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();