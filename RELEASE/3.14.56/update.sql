CREATE TABLE ccp_ae_category (
    id SERIAL NOT NULL PRIMARY KEY,
    description TEXT NOT NULL,
    row INTEGER NOT NULL,
    incoming BOOLEAN NOT NULL
);


INSERT INTO ccp_ae_category(row, description, incoming) VALUES (5, 'Tasse scolastiche sostenute nell`anno solare di riferimento' , true);
INSERT INTO ccp_ae_category(row, description, incoming) VALUES (6, 'Importo del rimborso erogato nell’anno di riferimento relativo a contributi obbligatori e volontari ed erogazioni liberali deliberati dagli istituti scolastici' , true);
INSERT INTO ccp_ae_category(row, description, incoming) VALUES (7, 'Erogazioni liberali non deliberate dagli istituti scolastici sostenute nell`anno solare di riferimento' , true);


INSERT INTO ccp_ae_category(row, description, incoming) VALUES (5, 'Importo del rimborso erogato nell’anno di riferimento relativo a tasse scolastiche' , false);
INSERT INTO ccp_ae_category(row, description, incoming) VALUES (6, 'Importo del rimborso erogato nell’anno di riferimento relativo a contributi obbligatori e volontari ed erogazioni liberali deliberati dagli istituti scolastici' , false);
INSERT INTO ccp_ae_category(row, description, incoming) VALUES (7, 'Importo del rimborso erogato nell’anno di riferimento relativo a erogazioni liberali non deliberate dagli istituti scolastici' , false);


ALTER TABLE ccp_type ADD COLUMN ccp_ae_category_id INTEGER;
ALTER TABLE audit.ccp_type ADD COLUMN ccp_ae_category_id INTEGER;

ALTER TABLE ccp_type
    ADD FOREIGN KEY (ccp_ae_category_id)
    REFERENCES ccp_ae_category (id)
    ON UPDATE CASCADE ON DELETE SET NULL;