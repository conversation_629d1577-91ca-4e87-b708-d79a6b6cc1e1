/*
DROP TABLE invoice_expiration;
DROP TABLE invoice;
DROP TABLE supplier;men

DROP TABLE audit.supplier;
DROP TABLE audit.invoice;
DROP TABLE audit.invoice_expiration;
*/

CREATE TABLE supplier (
	fiscal_code CHARACTER VARYING(255) PRIMARY KEY,
	vat_number	CHARACTER VARYING(255),
	name CHARACTER VARYING(255) NOT NULL,
	address CHARACTER VARYING(255) NOT NULL,
	city_id INTEGER,
	city_name CHARACTER VARYING(255) NOT NULL,
	city_province CHARACTER VARYING(255) NOT NULL,
	city_postal_code	INTEGER NOT NULL,
	CONSTRAINT fk_supplier_city
		FOREIGN KEY(city_id) 
	  	REFERENCES cities(city_id)
);

CREATE TABLE invoice (
	id SERIAL PRIMARY KEY,
	fiscal_code CHARACTER VARYING(255),
	vat_number	CHARACTER VARYING(255),
	name CHARACTER VARYING(255) NOT NULL,
	address CHARACTER VARYING(255) NOT NULL,
	city_id INTEGER,
	city_name CHARACTER VARYING(255) NOT NULL,
	city_province CHARACTER VARYING(255) NOT NULL,
	city_postal_code INTEGER NOT NULL,
	total DOUBLE PRECISION NOT NULL,
	xml	TEXT,
	CONSTRAINT fk_invoice_supplier
		FOREIGN KEY(fiscal_code) 
	  	REFERENCES supplier(fiscal_code)
);

CREATE TABLE invoice_expiration (
	id SERIAL PRIMARY KEY,
	invoice_id INTEGER NOT NULL,
	expiration TIMESTAMP WITH TIME ZONE NOT NULL,
	CONSTRAINT fk_invoice_expiration_invoice
		FOREIGN KEY(invoice_id) 
	  	REFERENCES invoice(id) ON DELETE CASCADE
);


/* AUDIT */
CREATE TABLE audit.supplier (
	op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	fiscal_code CHARACTER VARYING(255) ,
	vat_number	CHARACTER VARYING(255),
	name CHARACTER VARYING(255),
	address CHARACTER VARYING(255),
	city_id INTEGER,
	city_name CHARACTER VARYING(255),
	city_province CHARACTER VARYING(255),
	city_postal_code INTEGER
);

CREATE TABLE audit.invoice (
	op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id SERIAL,
	fiscal_code CHARACTER VARYING(255),
	vat_number	CHARACTER VARYING(255),
	name CHARACTER VARYING(255),
	address CHARACTER VARYING(255),
	city_id INTEGER,
	city_name CHARACTER VARYING(255),
	city_province CHARACTER VARYING(255),
	city_postal_code INTEGER,
	total DOUBLE PRECISION,
	xml	TEXT
);

CREATE TABLE audit.invoice_expiration (
	op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id INTEGER,
	invoice_id INTEGER,
	expiration TIMESTAMP WITH TIME ZONE
);

CREATE TRIGGER invoice
    AFTER INSERT OR UPDATE OR DELETE ON invoice
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER supplier
    AFTER INSERT OR UPDATE OR DELETE ON supplier
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TRIGGER invoice_expiration
    AFTER INSERT OR UPDATE OR DELETE ON invoice_expiration
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();
/* END AUDIT */

