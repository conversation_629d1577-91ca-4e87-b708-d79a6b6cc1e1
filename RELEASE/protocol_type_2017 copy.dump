ALTER TABLE protocol_protocol DROP CONSTRAINT protocol_protocol_type_id_fkey;
UPDATE protocol_protocol SET type_id = type_id+300;
UPDATE protocol_type SET id = id+300;
ALTER TABLE protocol_protocol ADD CONSTRAINT protocol_protocol_type_id_fkey FOREIGN KEY (type_id) REFERENCES protocol_type(id);

INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (184, 'I', 'AMMINISTRAZIONE', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (185, '1', 'Normativa e disposizioni attuative', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (186, '2', 'Organigramma e funzionigramma', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (187, '3', 'Audit, statistica e sicurezza di dati e informazioni', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (188, '4', 'Archivio, accesso, privacy, trasparenza e relazioni con il pubblico', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (189, '5', 'Qualità, carta dei servizi, valutazione e autovalutazione', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (190, '6', 'Elezioni e nomine', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (191, '7', 'Eventi, cerimoniale, patrocini, concorsi, editoria e stampa', 184);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (192, 'II', 'ORGANI E ORGANISMI', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (193, '1', 'Consiglio di istituto, Consiglio di circolo', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (194, '2', 'Consiglio di classe e di interclasse', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (195, '3', 'Collegio dei docenti', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (196, '4', 'Giunta esecutiva', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (197, '5', 'Dirigente scolastico DS', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (198, '6', 'Direttore dei servizi generali e amministrativi DSGA', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (199, '7', 'Comitato di valutazione del servizio dei docenti', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (200, '8', 'Comitato dei genitori, Comitato studentesco e rapporti scuola‐famiglia', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (201, '9', 'Reti scolastiche', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (202, '10', 'Rapporti sindacali, contrattazione e Rappresentanza sindacale unitaria (RSU)', 192);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (203, 'III', 'ATTIVITÀ GIURIDICO‐LEGALE', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (204, '1', 'Contenzioso', 203);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (205, '2', 'Violazioni amministrative e reati', 203);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (206, '3', 'Responsabilità civile, penale e amm.va', 203);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (207, '4', 'Pareri e consulenze', 203);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (208, 'IV', 'DIDATTICA', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (209, '1', 'Piano dell’offerta formativa POF', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (210, '2', 'Attività extracurricolari', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (211, '3', 'Registro di classe, dei docenti e dei profili', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (212, '4', 'Libri di testo', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (213, '5', 'Progetti e materiali didattici', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (214, '6', 'Viaggi di istruzione, scambi, stage e tirocini', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (215, '7', 'Biblioteca, emeroteca, videoteca e sussidi', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (216, '8', 'Salute e prevenzione', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (217, '9', 'Attività sportivo‐ricreative e rapporti con il Centro Scolastico Sportivo', 208);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (218, 'V', 'STUDENTI E DIPLOMATI', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (219, '1', 'Orientamento e placement', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (220, '2', 'Ammissioni e iscrizioni', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (221, '3', 'Anagrafe studenti e formazione delle classi', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (222, '4', 'Cursus studiorum', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (223, '5', 'Procedimenti disciplinari', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (224, '6', 'Diritto allo studio e servizi agli studenti (trasporti, mensa, buoni libro, etc)', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (225, '7', 'Tutela della salute e farmaci', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (226, '8', 'Esoneri', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (227, '9', 'Prescuola e attività parascolastiche', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (228, '10', 'Disagio e diverse abilità – DSA', 218);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (229, 'VI', 'FINANZA E PATRIMONIO', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (230, '1', 'Entrate e finanziamenti del progetto', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (231, '2', 'Uscite e piani di spesa', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (232, '3', 'Bilancio, tesoreria, cassa, istituti di credito e verifiche contabili', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (233, '4', 'Imposte, tasse, ritenute previdenziali e assistenziali', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (234, '5', 'Assicurazioni', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (235, '6', 'Utilizzo beni terzi, comodato', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (236, '7', 'Inventario e rendiconto patrimoniale', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (237, '8', 'Infrastrutture e logistica (plessi, succursali)', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (238, '9', 'DVR e sicurezza', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (239, '10', 'Beni mobili e servizi', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (240, '11', 'Sistemi informatici, telematici e fonia', 229);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (241, 'VII', 'PERSONALE', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (242, '1', 'Organici, lavoratori socialmente utili, graduatorie', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (243, '2', 'Carriera', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (244, '3', 'Trattamento giuridico‐economico', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (245, '4', 'Assenze', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (246, '5', 'Formazione, aggiornamento e sviluppo professionale', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (247, '6', 'Obiettivi, incarichi, valutazione e disciplina', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (248, '7', 'Sorveglianza sanitaria', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (249, '8', 'Collaboratori esterni', 241);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (250, 'VIII', 'OGGETTI DIVERSI', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (251, '1', 'Senza ulteriori suddivisioni in classi', 250);


SELECT pg_catalog.setval('protocol_type_id_seq', (SELECT max(id) + 1 FROM protocol_type), true);
