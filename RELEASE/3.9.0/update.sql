-- CREAZIONE TABELLE DI BASE PER LA NUOVA STRUTTURA
CREATE TABLE dossier (
    id SERIAL PRIMARY KEY,
    name CHARACTER VARYING(255)
);

CREATE TABLE archive_document_dossier (
    id SERIAL PRIMARY KEY,
    archive_document INTEGER NOT NULL references archive_document(id),
    dossier INTEGER NOT NULL references dossier(id)
);

-- TRASFERIMENTO DATI DA VECCHIA GESTIONE FASCICOLI ALLA NUOVA MODALITA' MOLTI A MOLTI


INSERT INTO dossier (name) (SELECT DISTINCT(dossier) FROM archive_document WHERE dossier IS NOT NULL AND dossier != '');
INSERT INTO archive_document_dossier (archive_document, dossier) SELECT id, (SELECT id FROM dossier where name = archive_document.dossier) FROM archive_document WHERE dossier IS NOT NULL AND dossier != '';

ALTER TABLE archive_document DROP COLUMN dossier;

ALTER TABLE dossier ADD COLUMN type CHARACTER VARYING (255) NOT NULL DEFAULT 'Amministrativo';
UPDATE dossier SET name = replace(name, '/', '-');




CREATE TABLE ccp_type_step (
    id serial primary key,
    ccp_type integer REFERENCES ccp_type(id) NOT NULL,
    expiration character varying(255),
    value float
);

INSERT INTO ccp_type_step (ccp_type, expiration, value) (SELECT id, '01/09', amount FROM ccp_type);

UPDATE ccp_movement SET note = note ||' - '||miscellaneous;

INSERT INTO parameter (name, value) VALUES ('SINGLE_SEAT_DB', 't');


UPDATE  ccp_movement SET expiration_date=creation_date where expiration_date is null;

UPDATE ccp_type_step SET expiration=null where expiration='01/09';



CREATE TABLE ccp_invoice (
    id SERIAL NOT NULL PRIMARY KEY,
    number INTEGER NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    accountholder TEXT NOT NULL,
    rows TEXT NOT NULL,
    total FLOAT NOT NULL
);

CREATE TABLE audit.ccp_invoice (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id SERIAL NOT NULL PRIMARY KEY,
    number INTEGER NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    accountholder TEXT NOT NULL,
    rows TEXT NOT NULL,
    total FLOAT NOT NULL
);

ALTER TABLE ccp_movement ADD COLUMN invoice_id INTEGER;
ALTER TABLE audit.ccp_movement ADD COLUMN invoice_id INTEGER;

ALTER TABLE ccp_movement ADD CONSTRAINT ccp_movement_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES ccp_invoice (id) ON UPDATE CASCADE ON DELETE SET NULL;

DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT mv.id, mv.type_id, mv.subject_type, mv.subject_id, mv.miscellaneous, mv.number, mv.note, mv.school_year, mv.subject_data, mv.subject_seat, mv.subject_class, mv.amount::numeric(14,2) AS amount, mv.creation_date, mv.expiration_date, mv.type_text, mv.incoming, mv.category_id, mv.category_text, mv.total_payments, mv.count_payments, mv.positive_additionals_euro, mv.negative_additionals_euro, mv.positive_additionals_perc, mv.negative_additionals_perc, mv.count_additionals, mv.linked_additionals, mv.linked_payments, (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total, mv.invoice_id
   FROM ( SELECT m.id, m.type_id, m.invoice_id, m.subject_type, m.subject_id, m.miscellaneous, m.number, m.note, m.school_year, m.subject_data, m.subject_seat, m.subject_class, m.amount, m.creation_date, m.expiration_date, t.name AS type_text, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments, ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals, ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals, ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m, ccp_type t, ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;


ALTER TABLE core_bank_account ADD COLUMN invoice_default BOOLEAN DEFAULT false;
ALTER TABLE audit.core_bank_account ADD COLUMN invoice_default BOOLEAN DEFAULT false;


ALTER TABLE ccp_invoice ADD COLUMN bank TEXT NOT NULL DEFAULT '{}';
ALTER TABLE audit.ccp_invoice ADD COLUMN bank TEXT NOT NULL DEFAULT '{}';


ALTER TABLE ccp_invoice ADD COLUMN expiration_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE audit.ccp_invoice ADD COLUMN expiration_date TIMESTAMP WITH TIME ZONE;

CREATE TRIGGER ccp_invoice_audit AFTER INSERT OR UPDATE OR DELETE ON ccp_invoice FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

DROP TABLE audit.ccp_invoice;

CREATE TABLE audit.ccp_invoice (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id INTEGER,
    number INTEGER NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    accountholder TEXT NOT NULL,
    rows TEXT NOT NULL,
    total FLOAT NOT NULL,
    bank TEXT,
    expiration_date timestamp with time zone
);


ALTER TABLE ccp_type ADD COLUMN section character varying(255);
ALTER TABLE audit.ccp_type ADD COLUMN section character varying(255);
-- INSERT INTO ccp_category (name) VALUES ('MASTERCOM');
-- INSERT INTO ccp_type (name, school_year, category_id, incoming, section) VALUES ('Mensa', 'TUTTI', (SELECT max(id) FROM ccp_category), 't', 'MCM');


  DROP VIEW ccp_view_movement;
  CREATE OR REPLACE VIEW ccp_view_movement AS
  SELECT mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.expiration_date,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.positive_additionals_euro,
    mv.negative_additionals_euro,
    mv.positive_additionals_perc,
    mv.negative_additionals_perc,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total,
    mv.invoice_id
   FROM ( SELECT m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.amount,
            m.creation_date,
            m.expiration_date,
            t.name AS type_text,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments,
            ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals,
            ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals,
            ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;



DROP VIEW ccp_view_payment;
CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.expiration_date, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_seat, pay.type_id, pay.section AS type_section, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, m.expiration_date as expiration_date, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_seat, m.type_id, t.section, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


ALTER TABLE ccp_type ADD COLUMN online_payment BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.ccp_type ADD COLUMN online_payment BOOLEAN NOT NULL DEFAULT false;


INSERT INTO parameter (name, value) VALUES ('STRIPE_SECRET_KEY', '');


INSERT INTO parameter (name, value) VALUES ('INVOICE_TRANSMISSION_ID', '1');
INSERT INTO parameter (name, value) VALUES ('FATTURAPA_EMAIL', '<EMAIL>');

ALTER TABLE ccp_invoice ADD COLUMN incoming boolean not null default false;
ALTER TABLE audit.ccp_invoice ADD COLUMN incoming boolean;
ALTER TABLE ccp_invoice ADD COLUMN xml_name character varying(255);
ALTER TABLE audit.ccp_invoice ADD COLUMN xml_name character varying(255);

CREATE TABLE ccp_invoice_transmission (
    id serial PRIMARY KEY NOT NULL,
    transmission_id character varying(5) NOT NULL,
    ccp_invoice integer REFERENCES ccp_invoice (id) ON DELETE CASCADE,
    "date" TIMESTAMP not null,
    status character varying(2) not null,
    description character varying(255) not null
);

ALTER TABLE archive_mail_account ADD COLUMN fatturapa boolean not null default false;
ALTER TABLE audit.archive_mail_account ADD COLUMN fatturapa boolean;

ALTER TABLE archive_mail_security ALTER COLUMN name TYPE Character Varying( 20 );
ALTER TABLE audit.archive_mail_security ALTER COLUMN name TYPE Character Varying( 20 );

INSERT INTO archive_mail_security(name) VALUES ('novalidate-cert');

ALTER TABLE ccp_invoice ADD column expiration_text TEXT;
ALTER TABLE ccp_invoice ADD column table_text TEXT;
ALTER TABLE audit.ccp_invoice ADD column expiration_text TEXT;
ALTER TABLE audit.ccp_invoice ADD column table_text TEXT;


INSERT INTO parameter (name, value) VALUES ('CCP_EXPIRATION_TEXT', '');
INSERT INTO parameter (name, value) VALUES ('CCP_TABLE_TEXT', '');
