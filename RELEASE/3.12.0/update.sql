-- add column amount
ALTER TABLE public.ccp_additional
    ADD COLUMN amount double precision DEFAULT 0;

-- add column amount to audit table
ALTER TABLE audit.ccp_additional
    ADD COLUMN amount double precision DEFAULT 0;

-- create table ccp_additional_templates
CREATE TABLE public.ccp_additional_templates
(
    id serial PRIMARY KEY,
    additional_id integer,
    type_id integer,
    CONSTRAINT additional_id FOREIGN KEY (additional_id)
        REFERENCES public.ccp_additional (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT type_id FOREIGN KEY (type_id)
        REFERENCES public.ccp_type (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);

-- create audit table ccp_additional_templates
CREATE TABLE audit.ccp_additional_templates
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer,
    additional_id integer,
    type_id integer
);

-- create trigger
CREATE TRIGGER ccp_additional_templates_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_additional_templates
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- add subject_type, subject_id and id serial to table ccp_type_additional
ALTER TABLE ccp_type_additional
	DROP CONSTRAINT IF EXISTS ccp_type_additional_pkey,
	ADD column subject_type character varying(1),
	ADD column subject_id integer,
	ADD column id serial PRIMARY KEY
;

-- add fields to audit ccp_type_additional_table
ALTER TABLE audit.ccp_type_additional
    ADD column subject_type character varying(1),
	ADD column subject_id integer,
	ADD column id integer
;

-- add new DISCOUNT_CALC_MODEL parameter

INSERT INTO parameter (name, value) VALUES ('DISCOUNT_CALC_MODEL', 'PROGRESSIVE');