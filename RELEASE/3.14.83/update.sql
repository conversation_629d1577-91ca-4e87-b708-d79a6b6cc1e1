-- parametro per permettere la visualizzazione del bollo come riga della fattura
INSERT INTO parameter(name, value) VALUES('INVOICE_BOLLO_IN_ELENCO','f');


-- campo per permettere al cliente di inserire una descrizione del conto bancario
ALTER TABLE core_bank_account ADD COLUMN customer_desc varchar(255) NOT NULL DEFAULT '';
ALTER TABLE audit.core_bank_account ADD COLUMN customer_desc varchar(255) NOT NULL DEFAULT '';

