ALTER TABLE ccp_invoice_deposit_slip ADD COLUMN iban TEXT;
ALTER TABLE ccp_invoice_deposit_slip ADD COLUMN codice_rid TEXT;
ALTER TABLE ccp_invoice_deposit_slip ADD COLUMN data_mandato_rid TEXT;
ALTER TABLE ccp_invoice_deposit_slip ADD COLUMN first_sepa TEXT;

ALTER TABLE audit.ccp_invoice_deposit_slip ADD COLUMN iban TEXT;
ALTER TABLE audit.ccp_invoice_deposit_slip ADD COLUMN codice_rid TEXT;
ALTER TABLE audit.ccp_invoice_deposit_slip ADD COLUMN data_mandato_rid TEXT;
ALTER TABLE audit.ccp_invoice_deposit_slip ADD COLUMN first_sepa TEXT;

-- SCRIPT UPDATE OLD DEPOSIT SLIP ROWS
update ccp_invoice_deposit_slip set iban = (select REPLACE(REPLACE((accountholder::json->0->'iban')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
update ccp_invoice_deposit_slip set codice_rid = (select REPLACE(REPLACE((accountholder::json->0->'codice_rid')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
update ccp_invoice_deposit_slip set data_mandato_rid = (select REPLACE(REPLACE((accountholder::json->0->'data_mandato_rid')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
update ccp_invoice_deposit_slip set first_sepa = (select REPLACE(REPLACE((accountholder::json->0->'first_sepa')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);

