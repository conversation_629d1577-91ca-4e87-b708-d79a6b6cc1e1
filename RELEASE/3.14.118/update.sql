

CREATE FUNCTION process_audit() R<PERSON><PERSON><PERSON> trigger
    LANGUAGE plpgsql
    AS $_$
    BEGIN
    	CASE TG_OP
		WHEN 'INSERT' THEN
			EXECUTE format('insert into audit_%I select $1,$2,$3,($4).*',TG_TABLE_NAME) using nextval(pg_get_serial_sequence('audit_' || TG_TABLE_NAME, 'op_id')), 'I', now(), NEW;
			RETURN NEW;
		WHEN 'UPDATE' THEN
			EXECUTE format('insert into audit_%I select $1,$2,$3,($4).*',TG_TABLE_NAME) using nextval(pg_get_serial_sequence('audit_' || TG_TABLE_NAME, 'op_id')), 'U', now(), NEW;
			RETURN NEW;
		WHEN 'DELETE' THEN
			EXECUTE format('insert into audit_%I select $1,$2,$3,($4).*',TG_TABLE_NAME) using nextval(pg_get_serial_sequence('audit_' || TG_TABLE_NAME, 'op_id')), 'D', now(), OLD;
			RETURN NEW;
	END CASE;
    END ;
$_$;


CREATE TABLE audit_absence_kind (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    code character varying(10) NOT NULL,
    description text NOT NULL,
    absence_stack bigint,
    date_start timestamp without time zone,
    date_end timestamp without time zone,
    calc_festivities boolean DEFAULT false NOT NULL,
    calc_ferials boolean DEFAULT false NOT NULL
);


ALTER TABLE audit_absence_kind OWNER TO postgres;

--
-- Name: absence_stack; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_absence_stack (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    unit character varying(1) DEFAULT 'h'::character varying NOT NULL,
    denomination character varying(255) NOT NULL,
    recover boolean DEFAULT false NOT NULL,
    reset_type smallint DEFAULT 0 NOT NULL,
    reset_date timestamp without time zone,
    reset_to_stack_id bigint,
    reset_default_quota double precision DEFAULT 0.00 NOT NULL
);


ALTER TABLE audit_absence_stack OWNER TO postgres;

--
-- Name: absences; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_absences (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    absence_id bigint,
    start_date bigint,
    end_date bigint,
    ab_kind character varying(10),
    total_days integer,
    employee_id integer,
    date_of_req bigint,
    protocol_id bigint,
    type_of_abs smallint,
    decreto integer,
    note text
);


ALTER TABLE audit_absences OWNER TO postgres;

--
-- Name: albo_area; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_albo_area (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text
);


ALTER TABLE audit_albo_area OWNER TO postgres;

--
-- Name: albo_category; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_albo_category (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    duration smallint NOT NULL
);


ALTER TABLE audit_albo_category OWNER TO postgres;

--
-- Name: albo_entity; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_albo_entity (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text
);


ALTER TABLE audit_albo_entity OWNER TO postgres;

--
-- Name: albo_publication; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_albo_publication (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    number integer,
    title text NOT NULL,
    description text,
    start_date bigint NOT NULL,
    expiration_date bigint NOT NULL,
    extended_expiration_date bigint,
    publication_date bigint,
    extension_date bigint,
    cancelation_date bigint,
    category_id integer NOT NULL,
    entity_id integer NOT NULL,
    area_id integer NOT NULL,
    omissis boolean NOT NULL,
    internal boolean NOT NULL
);


ALTER TABLE audit_albo_publication OWNER TO postgres;

--
-- Name: albo_publication_document; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_albo_publication_document (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    publication_id integer NOT NULL,
    document_id integer NOT NULL
);


ALTER TABLE audit_albo_publication_document OWNER TO postgres;

--
-- Name: albo_publication_history; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_albo_publication_history (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    action character varying(1) NOT NULL,
    date bigint NOT NULL,
    publication_id integer NOT NULL,
    user_id integer NOT NULL
);


ALTER TABLE audit_albo_publication_history OWNER TO postgres;

--
-- Name: archive_check_document; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_check_document (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    archive_document bigint NOT NULL,
    "user" bigint NOT NULL,
    checked timestamp with time zone
);


ALTER TABLE audit_archive_check_document OWNER TO postgres;

--
-- Name: archive_class; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_class (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(30) NOT NULL,
    code character varying(20),
    description character varying(255),
    format character varying(5) NOT NULL,
    action character varying(1) NOT NULL,
    editable boolean
);


ALTER TABLE audit_archive_class OWNER TO postgres;

--
-- Name: archive_class_step; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_class_step (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    archive_class integer,
    sort integer,
    user_id integer,
    protocol boolean,
    albo boolean,
    trasparenza boolean,
    sign boolean,
    archive boolean DEFAULT false,
    type character varying(1)
);


ALTER TABLE audit_archive_class_step OWNER TO postgres;

--
-- Name: archive_document; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_document (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    short_description text,
    description text,
    upload_date bigint NOT NULL,
    user_id integer,
    archive_user_id integer,
    metadata text NOT NULL,
    class_id integer,
    action_archive_date bigint,
    action_protocol boolean NOT NULL,
    action_protocol_date bigint,
    action_albo boolean NOT NULL,
    action_albo_date bigint,
    action_trasparenza boolean NOT NULL,
    action_trasparenza_date bigint,
    conserved boolean NOT NULL,
    origin_id integer NOT NULL,
    action_sign boolean DEFAULT false NOT NULL,
    action_sign_date bigint,
    class_name character varying(30),
    action_archive boolean DEFAULT false,
    remote_class_code character varying(64),
    assign_to_office integer,
    assign_to_user integer,
    assign_from_user integer,
    completed timestamp with time zone,
    model integer,
    expiration_date timestamp without time zone,
    dossier character varying(255)
);


ALTER TABLE audit_archive_document OWNER TO postgres;

--
-- Name: archive_document_file; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_document_file (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    archive_document integer,
    filename text NOT NULL,
    filetype text NOT NULL,
    path character varying(255),
    size integer,
    token character varying(255),
    metadata text,
    external_data text
);


ALTER TABLE audit_archive_document_file OWNER TO postgres;

--
-- Name: archive_document_model; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_document_model (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    name character varying(255),
    template text
);


ALTER TABLE audit_archive_document_model OWNER TO postgres;

--
-- Name: archive_document_step; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_document_step (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    archive_document integer,
    sort integer,
    user_id integer,
    protocol boolean,
    albo boolean,
    trasparenza boolean,
    sign boolean,
    archive boolean DEFAULT false,
    type character varying(1)
);


ALTER TABLE audit_archive_document_step OWNER TO postgres;

--
-- Name: archive_mail; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_mail (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id character varying(32),
    account integer,
    date timestamp without time zone,
    "from" text,
    "to" text,
    cc text,
    ccn text,
    subject text,
    message text,
    raw text,
    deleted boolean,
    assign_to_office integer,
    assign_to_user integer,
    assign_from_user integer
);


ALTER TABLE audit_archive_mail OWNER TO postgres;

--
-- Name: archive_mail_account; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_mail_account (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    name character varying(255),
    protocol integer,
    host character varying(255),
    port integer,
    security integer,
    username character varying(255),
    password character varying(255),
    active boolean,
    "out" boolean DEFAULT false NOT NULL,
    outname character varying(255),
    fatturapa boolean,
    connection_class character varying(255),
    reminder boolean DEFAULT false NOT NULL,
    imap_encoding character varying(50) DEFAULT ''::character varying NOT NULL,
    params text,
    authentication text DEFAULT ''::text NOT NULL,
    vendor text DEFAULT ''::text NOT NULL
);


ALTER TABLE audit_archive_mail_account OWNER TO postgres;

--
-- Name: archive_mail_attachment; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_mail_attachment (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    mail character varying(32),
    account integer,
    name character varying(255),
    path character varying(255)
);


ALTER TABLE audit_archive_mail_attachment OWNER TO postgres;

--
-- Name: archive_mail_document; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_mail_document (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    mail character varying(32) NOT NULL,
    account integer NOT NULL,
    document integer NOT NULL,
    sent timestamp without time zone
);


ALTER TABLE audit_archive_mail_document OWNER TO postgres;

--
-- Name: archive_mail_protocol; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_mail_protocol (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    name character varying(8)
);


ALTER TABLE audit_archive_mail_protocol OWNER TO postgres;

--
-- Name: archive_mail_security; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_mail_security (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    name character varying(20)
);


ALTER TABLE audit_archive_mail_security OWNER TO postgres;

--
-- Name: archive_office; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_office (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint NOT NULL,
    name character varying(255)
);


ALTER TABLE audit_archive_office OWNER TO postgres;

--
-- Name: archive_office_user; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_office_user (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint NOT NULL,
    office bigint NOT NULL,
    "user" bigint NOT NULL
);


ALTER TABLE audit_archive_office_user OWNER TO postgres;

--
-- Name: archive_origin; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_origin (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(10) NOT NULL,
    code character varying(5) NOT NULL,
    description character varying(255)
);


ALTER TABLE audit_archive_origin OWNER TO postgres;

--
-- Name: archive_privilege; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_privilege (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    user_id integer NOT NULL,
    class_id integer NOT NULL,
    view boolean DEFAULT false NOT NULL,
    upload boolean DEFAULT false NOT NULL,
    delete boolean DEFAULT false NOT NULL,
    resend boolean DEFAULT false NOT NULL
);


ALTER TABLE audit_archive_privilege OWNER TO postgres;

--
-- Name: archive_user; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_archive_user (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(30) NOT NULL,
    username character varying(50) NOT NULL,
    password character varying(50),
    alias character varying(50),
    pin character varying(50),
    active boolean DEFAULT true NOT NULL
);


ALTER TABLE audit_archive_user OWNER TO postgres;

--
-- Name: bdg_activities; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_bdg_activities (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    activ_id integer NOT NULL,
    aggreg_code character varying(1) NOT NULL,
    aggreg_nr smallint NOT NULL,
    description character varying(200) NOT NULL,
    ext_desc text,
    start_date bigint,
    end_date bigint,
    suspend boolean DEFAULT false,
    avm boolean DEFAULT false,
    notsdate boolean DEFAULT false,
    notedate boolean DEFAULT false,
    responsibles text,
    objectives text,
    human_resources text,
    goods_services text,
    durata text,
    budget_year integer DEFAULT 2008 NOT NULL,
    residui double precision DEFAULT 0 NOT NULL,
    hours_insertions_end_date bigint
);


ALTER TABLE audit_bdg_activities OWNER TO postgres;

--
-- Name: ccp_additional; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_additional (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    positive boolean NOT NULL,
    percentual boolean NOT NULL,
    payment boolean NOT NULL,
    amount double precision DEFAULT 0,
    code character varying(20) DEFAULT ''::character varying,
    on_gross boolean,
    codice_conto character varying(255)
);


ALTER TABLE audit_ccp_additional OWNER TO postgres;

--
-- Name: ccp_additional_templates; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_additional_templates (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    additional_id integer,
    type_id integer
);


ALTER TABLE audit_ccp_additional_templates OWNER TO postgres;

--
-- Name: ccp_category; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_category (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    initial_balance double precision NOT NULL
);


ALTER TABLE audit_ccp_category OWNER TO postgres;

--
-- Name: ccp_category_banks; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_category_banks (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    category_id integer,
    bank_id integer,
    initial_balance double precision
);


ALTER TABLE audit_ccp_category_banks OWNER TO postgres;

--
-- Name: ccp_credits; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_credits (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    subject_id integer,
    subject_type character varying(1),
    credit_type_id integer DEFAULT 0 NOT NULL
);


ALTER TABLE audit_ccp_credits OWNER TO postgres;

--
-- Name: ccp_credits_type; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_credits_type (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    description character varying(255),
    is_default boolean DEFAULT false,
    active boolean DEFAULT true,
    discount boolean DEFAULT false NOT NULL,
    dote integer DEFAULT 0 NOT NULL,
    exclude_corrispettivi boolean DEFAULT false,
    movement boolean,
    piano_conti character varying(255),
    causale_contabile character varying(255),
    causale_contabile_uscite character varying(255),
    ccp_payment_method integer,
    show_on_site boolean
);


ALTER TABLE audit_ccp_credits_type OWNER TO postgres;

--
-- Name: ccp_deposit_slip; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_deposit_slip (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    number integer NOT NULL,
    date timestamp with time zone,
    bank_account integer,
    bank_account_iban character varying(255),
    bank_account_name character varying(255),
    payment_method integer,
    payment_method_name character varying(255),
    bank_account_cuc character varying(255),
    creditor_identifier character varying(255),
    school_year character varying(9)
);


ALTER TABLE audit_ccp_deposit_slip OWNER TO postgres;

--
-- Name: ccp_deposits; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_deposits (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    operation_date bigint,
    accountable_date bigint,
    amount double precision,
    credits_id integer DEFAULT 0 NOT NULL,
    payment_method_id integer,
    payer_type character varying(1),
    payer_id character varying(30),
    payer_name character varying(50),
    payer_surname character varying(50),
    payer_fiscal_code character varying(20),
    payer_address character varying(80),
    payer_city character varying(30),
    payer_province character varying(2),
    payer_zip_code character varying(5),
    description character varying(255),
    deposit_type character varying(1),
    tx_id character varying(255)
);


ALTER TABLE audit_ccp_deposits OWNER TO postgres;

--
-- Name: ccp_invoice; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_invoice (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    number integer NOT NULL,
    date timestamp with time zone NOT NULL,
    accountholder text NOT NULL,
    rows text NOT NULL,
    total double precision NOT NULL,
    bank text,
    expiration_date timestamp with time zone,
    incoming boolean,
    xml_name character varying(255),
    expiration_text text,
    table_text text,
    ds_name character varying(255),
    ds_id character varying(255),
    credit_note boolean DEFAULT false,
    payment_method integer,
    publication_path character varying(255),
    header text,
    suffix character varying(255)
);


ALTER TABLE audit_ccp_invoice OWNER TO postgres;

--
-- Name: ccp_invoice_deposit_slip; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_invoice_deposit_slip (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    ccp_invoice integer,
    ccp_deposit_slip integer,
    unpaid_date timestamp with time zone,
    unpaid_note text,
    row_number integer,
    total double precision DEFAULT 0 NOT NULL,
    collection_cost double precision DEFAULT 0 NOT NULL,
    bollo double precision DEFAULT 0 NOT NULL,
    movements_total double precision DEFAULT 0 NOT NULL,
    iban text,
    codice_rid text,
    data_mandato_rid text,
    first_sepa text,
    surname character varying(255),
    name character varying(255),
    address character varying(255),
    city character varying(255),
    province character varying(255),
    zip_code character varying(255),
    fiscal_code character varying(255)
);


ALTER TABLE audit_ccp_invoice_deposit_slip OWNER TO postgres;

--
-- Name: ccp_movement; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_movement (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    type_id integer,
    miscellaneous text,
    number integer,
    note text,
    school_year character varying(255) DEFAULT ''::character varying,
    subject_id integer,
    subject_data text,
    subject_seat integer,
    subject_class character varying(64),
    amount double precision NOT NULL,
    creation_date bigint NOT NULL,
    subject_type character varying(1) NOT NULL,
    expiration_date bigint,
    tmp_number text,
    invoice_id integer,
    subject_school_address_code character varying(255),
    subject_school_address character varying(255),
    invoice_code character varying(255),
    description character varying(255) DEFAULT ''::character varying NOT NULL,
    da_ratei timestamp with time zone,
    a_ratei timestamp with time zone,
    ccp_easy_select integer,
    subject_school_year character varying(255),
    payment_intent_token character varying(255) DEFAULT ''::character varying,
    payment_intent_id integer,
    locked boolean,
    date_published timestamp with time zone
);


ALTER TABLE audit_ccp_movement OWNER TO postgres;

--
-- Name: ccp_movement_additional; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_movement_additional (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    movement_id integer NOT NULL,
    additional_id integer NOT NULL,
    amount double precision NOT NULL,
    subject_type character varying(1),
    subject_id integer,
    id integer,
    abs_amount double precision DEFAULT 0 NOT NULL
);


ALTER TABLE audit_ccp_movement_additional OWNER TO postgres;

--
-- Name: ccp_payment; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_payment (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    movement_id integer NOT NULL,
    operation_date bigint NOT NULL,
    accountable_date bigint NOT NULL,
    amount double precision NOT NULL,
    payment_method_id integer NOT NULL,
    bollettino character varying(50),
    account_id integer NOT NULL,
    account_reference character varying(50),
    payer_type character varying(1) NOT NULL,
    payer_id character varying(30),
    payer_name character varying(50) DEFAULT ''::character varying NOT NULL,
    payer_surname character varying(50) DEFAULT ''::character varying NOT NULL,
    payer_fiscal_code character varying(20),
    payer_address character varying(255),
    payer_city character varying(255),
    payer_province character varying(2),
    payer_zip_code character varying(5),
    receipt_id integer,
    ccp_credit integer,
    ccp_deposit_slip integer,
    easy_import_protocol integer,
    covered_movement_id integer,
    payment_group integer
);


ALTER TABLE audit_ccp_payment OWNER TO postgres;

--
-- Name: ccp_payment_additional; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_payment_additional (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    payment_id integer NOT NULL,
    additional_id integer NOT NULL,
    amount double precision NOT NULL
);


ALTER TABLE audit_ccp_payment_additional OWNER TO postgres;

--
-- Name: ccp_payment_method; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_payment_method (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    easy_code integer,
    piano_conti character varying(255),
    easy_by_bank boolean DEFAULT false,
    easy_export_grouped boolean DEFAULT true,
    massive_payment_group boolean DEFAULT false,
    causale_contabile character varying(50),
    causale_contabile_uscite character varying(50),
    data_raggruppamento character varying(255),
    tipo_raggruppamento character varying(255),
    piano_conti_da_tipo boolean,
    piano_conti_uscite_da_tipo boolean
);


ALTER TABLE audit_ccp_payment_method OWNER TO postgres;

--
-- Name: ccp_receipt; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_receipt (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    number integer NOT NULL,
    date bigint NOT NULL,
    receipt boolean,
    publication_path character varying(255),
    groupment character varying(255)
);


ALTER TABLE audit_ccp_receipt OWNER TO postgres;

--
-- Name: ccp_reminder_subjects; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_reminder_subjects (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    ccp_reminder_id integer,
    subject_id integer
);


ALTER TABLE audit_ccp_reminder_subjects OWNER TO postgres;

--
-- Name: ccp_type; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_type (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    amount double precision NOT NULL,
    governative boolean NOT NULL,
    expiration_date bigint,
    cumulative integer NOT NULL,
    school_year character varying(9) NOT NULL,
    category_id integer NOT NULL,
    incoming boolean NOT NULL,
    section character varying(255),
    online_payment boolean DEFAULT false NOT NULL,
    invoice_code character varying(255),
    easy_code character varying(255),
    da_ratei timestamp with time zone,
    a_ratei timestamp with time zone,
    include_vat boolean,
    vat double precision,
    bollo boolean DEFAULT false NOT NULL,
    vat_code_id integer,
    easy_description character varying(20),
    payment_mail character varying(255),
    online_payment_status integer DEFAULT 0,
    exclude_corrispettivi boolean DEFAULT false,
    ccp_credits_type integer,
    centro_costo_ricavo character varying(50),
    id_importazione character varying(50),
    ccp_ae_category_id integer,
    pubblica_pagati_online boolean,
    include_bollo boolean,
    easy_code_contropartita character varying(255)
);


ALTER TABLE audit_ccp_type OWNER TO postgres;

--
-- Name: ccp_type_additional; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_type_additional (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    type_id integer NOT NULL,
    additional_id integer NOT NULL,
    amount double precision NOT NULL,
    subject_type character varying(1),
    subject_id integer,
    id integer,
    discount_order integer
);


ALTER TABLE audit_ccp_type_additional OWNER TO postgres;

--
-- Name: ccp_type_step; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_type_step (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    ccp_type integer NOT NULL,
    expiration character varying(255),
    value double precision,
    description character varying(255) DEFAULT ''::character varying NOT NULL,
    da_ratei timestamp with time zone,
    a_ratei timestamp with time zone
);


ALTER TABLE audit_ccp_type_step OWNER TO postgres;

--
-- Name: ccp_vat_code; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_ccp_vat_code (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    code character varying(20),
    description character varying(255),
    amount double precision DEFAULT 0.00 NOT NULL,
    exemption boolean,
    sdi_code character varying(255),
    easy_code character varying(50)
);


ALTER TABLE audit_ccp_vat_code OWNER TO postgres;

--
-- Name: contact; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_contact (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    contact_id integer,
    address text,
    phone_num text,
    fax text,
    city_id integer,
    email text,
    mobile text,
    web text,
    cap character varying(5) DEFAULT ''::character varying
);


ALTER TABLE audit_contact OWNER TO postgres;

--
-- Name: core_bank_account; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_core_bank_account (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    country_code character varying(2),
    check_code character varying(2),
    bban character varying(100) NOT NULL,
    denomination character varying(100) NOT NULL,
    initial_balance double precision NOT NULL,
    type character varying(1) NOT NULL,
    ise_id integer NOT NULL,
    ise_type character varying(1) NOT NULL,
    invoice_default boolean DEFAULT false,
    online_payment_default boolean DEFAULT false NOT NULL,
    cuc character varying(255),
    creditor_identifier character varying(255),
    pvr_number character varying(20) DEFAULT ''::character varying NOT NULL,
    agency_name character varying(50),
    agency_city character varying(50),
    agency_zip_code character varying(10),
    qr_iban character varying(27),
    qr_bank_account character varying(27),
    collection_cost double precision DEFAULT 0.00 NOT NULL,
    sepa_regex character varying(255),
    sepa_accent boolean,
    piano_conti character varying(255),
    customer_desc character varying(255) DEFAULT ''::character varying NOT NULL
);


ALTER TABLE audit_core_bank_account OWNER TO postgres;

--
-- Name: core_contact_type; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_core_contact_type (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    code character varying(20) NOT NULL
);


ALTER TABLE audit_core_contact_type OWNER TO postgres;

--
-- Name: core_contactgroups; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_core_contactgroups (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    user_id integer NOT NULL
);


ALTER TABLE audit_core_contactgroups OWNER TO postgres;

--
-- Name: core_contacts; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_core_contacts (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    contact_type_id integer NOT NULL,
    user_id integer
);


ALTER TABLE audit_core_contacts OWNER TO postgres;

--
-- Name: core_contacts_contactgroups; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_core_contacts_contactgroups (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    contact_id integer NOT NULL,
    group_id integer NOT NULL
);


ALTER TABLE audit_core_contacts_contactgroups OWNER TO postgres;

--
-- Name: core_forced_action; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_core_forced_action (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint,
    date timestamp without time zone,
    section character varying(16),
    code character varying(32),
    description character varying(255),
    "table" character varying(32),
    id_value integer
);


ALTER TABLE audit_core_forced_action OWNER TO postgres;

--
-- Name: employee; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_employee (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    employee_id integer NOT NULL,
    name character varying(100) NOT NULL,
    surname character varying(50) NOT NULL,
    gender character varying(10),
    birthdate bigint,
    fiscal_code character varying(30),
    residence_id integer,
    address_id integer,
    part_spesa character varying(16),
    bank integer DEFAULT '-1'::integer,
    liq_office character varying(4),
    inps character varying(17),
    insur_qual character varying(1),
    fore boolean,
    asl character varying(10),
    adm_code character varying(16),
    way_pay character varying(2),
    liquid_group character varying(4),
    contr_code character varying(2),
    contr_type character varying(5),
    contr_cat smallint,
    ssp_frm_pmnt smallint,
    personal_data smallint DEFAULT '-1'::integer,
    susp boolean,
    payment_group integer,
    priv_ret_type character varying(5),
    social_position smallint,
    active boolean DEFAULT true,
    statal_code character varying(16),
    fiscal_city_code character varying(16),
    birthplace text,
    income bigint DEFAULT 0,
    state_birth character varying(5),
    citizenship character varying(5),
    id_sissi character varying(5),
    dom_first_prev_year integer,
    dom_last_prev_year integer,
    dom_first_curr_year integer,
    qualification text,
    liquid_office_id integer DEFAULT 0 NOT NULL,
    badge_number bigint,
    tolerance_in integer DEFAULT 0 NOT NULL,
    tolerance_out integer DEFAULT 0 NOT NULL,
    flexibility integer DEFAULT 0 NOT NULL,
    generic_tolerance integer DEFAULT 0 NOT NULL,
    negative_round integer DEFAULT 0 NOT NULL,
    recover_hours integer DEFAULT 100 NOT NULL,
    max_extraordinary_in integer DEFAULT 999 NOT NULL,
    max_extraordinary_out integer DEFAULT 999 NOT NULL,
    min_extraordinary_in integer DEFAULT 0 NOT NULL,
    min_extraordinary_out integer DEFAULT 0 NOT NULL,
    step_out integer DEFAULT 0 NOT NULL,
    step_in integer DEFAULT 0 NOT NULL,
    max_break integer DEFAULT 0 NOT NULL,
    max_cont_work integer DEFAULT 720 NOT NULL,
    simplified_ata_settings boolean DEFAULT false NOT NULL,
    tolerance_in_und integer DEFAULT 0 NOT NULL,
    tolerance_out_und integer DEFAULT 0 NOT NULL,
    max_undefined_in integer DEFAULT 999 NOT NULL,
    max_undefined_out integer DEFAULT 999 NOT NULL,
    min_undefined_in integer DEFAULT 0 NOT NULL,
    min_undefined_out integer DEFAULT 0 NOT NULL,
    step_out_und integer DEFAULT 0 NOT NULL,
    step_in_und integer DEFAULT 0 NOT NULL,
    undefined_parameter_active boolean DEFAULT false NOT NULL,
    min_extraordinary_total integer DEFAULT 0 NOT NULL,
    max_extraordinary_total integer DEFAULT 999 NOT NULL,
    min_undefined_total integer DEFAULT 0 NOT NULL,
    max_undefined_total integer DEFAULT 999 NOT NULL,
    step_total_undefined integer DEFAULT 0 NOT NULL,
    step_total_extraordinary integer DEFAULT 0 NOT NULL,
    lunch_duration integer DEFAULT 0 NOT NULL,
    lunch_deductible boolean DEFAULT false NOT NULL,
    service_deductible boolean DEFAULT false NOT NULL,
    min_undefined_lunch integer DEFAULT 0 NOT NULL,
    min_extraordinary_lunch integer DEFAULT 0 NOT NULL,
    max_undefined_lunch integer DEFAULT 0 NOT NULL,
    max_extraordinary_lunch integer DEFAULT 0 NOT NULL,
    step_lunch_undefined integer DEFAULT 0 NOT NULL,
    step_lunch_extraordinary integer DEFAULT 0 NOT NULL,
    break_after_max_work integer DEFAULT 0 NOT NULL,
    unit_recover_hours character varying(3) DEFAULT '%'::character varying NOT NULL,
    max_work integer DEFAULT 999 NOT NULL
);


ALTER TABLE audit_employee OWNER TO postgres;

--
-- Name: extraordinary_stored; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_extraordinary_stored (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    extraordinary_stored_id integer NOT NULL,
    employee_id bigint NOT NULL,
    date bigint NOT NULL,
    recover_hours integer DEFAULT 0 NOT NULL,
    extraordinary integer DEFAULT 0 NOT NULL,
    max_extraordinary integer DEFAULT 0 NOT NULL,
    to_recover integer DEFAULT 0 NOT NULL,
    to_pay integer DEFAULT 0 NOT NULL,
    authorized integer DEFAULT 0 NOT NULL,
    authorized_undefined integer DEFAULT 0 NOT NULL,
    undefined integer DEFAULT 0 NOT NULL,
    note text DEFAULT ''::text NOT NULL
);


ALTER TABLE audit_extraordinary_stored OWNER TO postgres;

--
-- Name: invoice; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_invoice (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    fiscal_code character varying(255),
    vat_number character varying(255),
    name character varying(255),
    address character varying(255),
    city_id integer,
    city_name character varying(255),
    city_province character varying(255),
    city_postal_code integer,
    total double precision,
    xml text,
    number character varying(255),
    date timestamp with time zone,
    archive_id character varying(255)
);


ALTER TABLE audit_invoice OWNER TO postgres;

--
-- Name: invoice_expiration; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_invoice_expiration (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer,
    invoice_id integer,
    expiration timestamp with time zone
);


ALTER TABLE audit_invoice_expiration OWNER TO postgres;


--
-- Name: personnel_presences; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_personnel_presences (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    personnel_presence_id integer NOT NULL,
    employee_id bigint DEFAULT '-1'::integer NOT NULL,
    project_id bigint,
    project_edit_id bigint,
    date bigint,
    date_edit bigint,
    type integer DEFAULT 1 NOT NULL,
    type_edit integer DEFAULT 1 NOT NULL,
    original_inout integer DEFAULT '-1'::integer NOT NULL,
    original_inout_edit integer DEFAULT 1 NOT NULL,
    description character varying(2000) DEFAULT ''::character varying NOT NULL,
    hour_type_id bigint DEFAULT '-1'::integer NOT NULL,
    hour_type_edit_id bigint DEFAULT '-1'::integer NOT NULL,
    insertion_mode character varying(1) DEFAULT 'T'::character varying NOT NULL
);


ALTER TABLE audit_personnel_presences OWNER TO postgres;

--
-- Name: personnel_projects; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_personnel_projects (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    personnel_projects_id integer NOT NULL,
    project_id bigint,
    personnel_id bigint
);


ALTER TABLE audit_personnel_projects OWNER TO postgres;

--
-- Name: personnel_stacks; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_personnel_stacks (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint NOT NULL,
    employee_id bigint DEFAULT '-1'::integer NOT NULL,
    stack_id bigint DEFAULT '-1'::integer NOT NULL,
    reset_quota double precision DEFAULT 0.00 NOT NULL
);


ALTER TABLE audit_personnel_stacks OWNER TO postgres;


--
-- Name: personnel_timetable; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_personnel_timetable (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    personnel_timetable_id integer NOT NULL,
    employee_id bigint NOT NULL,
    date_start bigint NOT NULL,
    date_end bigint NOT NULL,
    date_start_pause bigint,
    date_end_pause bigint
);


ALTER TABLE audit_personnel_timetable OWNER TO postgres;

--
-- Name: protocol_action; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_action (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    description character varying(100) NOT NULL,
    comment character varying(255),
    active boolean DEFAULT false NOT NULL,
    type_id bigint
);


ALTER TABLE audit_protocol_action OWNER TO postgres;

--
-- Name: protocol_correspondent; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_correspondent (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    title text NOT NULL,
    note text,
    person_type text,
    correspondent_type_id bigint DEFAULT '-1'::integer,
    correspondent_type text DEFAULT 'manual'::text NOT NULL,
    fiscal_code character varying(50),
    city_id integer,
    zipcode character varying(5),
    address character varying(255),
    phone character varying(64),
    fax character varying(64),
    mobile character varying(64),
    email character varying(64),
    web character varying(128)
);


ALTER TABLE audit_protocol_correspondent OWNER TO postgres;

--
-- Name: protocol_protocol; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_protocol (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint NOT NULL,
    date bigint NOT NULL,
    note text,
    budget_id smallint,
    type_id integer,
    rec_id integer,
    rec_type smallint,
    protocol_number integer NOT NULL,
    obj_id integer,
    direction character varying(1),
    description text,
    correspondent_text text,
    canceled boolean DEFAULT false NOT NULL,
    external_act_number text,
    send_method_id bigint,
    dossier text,
    subject_kind_id integer,
    reserved boolean DEFAULT false NOT NULL,
    header_position character varying(255),
    mail_sending timestamp without time zone,
    mail_error text
);


ALTER TABLE audit_protocol_protocol OWNER TO postgres;

--
-- Name: protocol_protocol_correspondent; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_protocol_correspondent (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    protocol_id bigint NOT NULL,
    correspondent_id bigint NOT NULL
);


ALTER TABLE audit_protocol_protocol_correspondent OWNER TO postgres;

--
-- Name: protocol_protocol_document; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_protocol_document (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    protocol_id integer NOT NULL,
    document_id integer NOT NULL
);


ALTER TABLE audit_protocol_protocol_document OWNER TO postgres;

--
-- Name: protocol_protocol_history; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_protocol_history (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    action character varying(1) NOT NULL,
    date bigint NOT NULL,
    protocol_id integer NOT NULL,
    user_id integer NOT NULL,
    note text
);


ALTER TABLE audit_protocol_protocol_history OWNER TO postgres;

--
-- Name: protocol_protocol_protocol; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_protocol_protocol (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    protocol_1_id integer NOT NULL,
    protocol_2_id integer NOT NULL,
    parent_relationship boolean DEFAULT false NOT NULL
);


ALTER TABLE audit_protocol_protocol_protocol OWNER TO postgres;

--
-- Name: protocol_send_method; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_send_method (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    title character varying(100) NOT NULL
);


ALTER TABLE audit_protocol_send_method OWNER TO postgres;

--
-- Name: protocol_subject_kind; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_subject_kind (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    title character varying(100) NOT NULL
);


ALTER TABLE audit_protocol_subject_kind OWNER TO postgres;

--
-- Name: protocol_type; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_protocol_type (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    code character varying(20) NOT NULL,
    description character varying(255),
    parent_type_id bigint
);


ALTER TABLE audit_protocol_type OWNER TO postgres;

--
-- Name: report; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_report (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id bigint NOT NULL,
    date_created timestamp with time zone NOT NULL,
    print_key character varying(255) NOT NULL,
    print_type character varying(255) NOT NULL,
    subject_id integer,
    subject_type character varying(1),
    school_year character varying(255),
    template_id integer,
    subject_data character varying(255),
    subject_class character varying(100),
    subject_school_address_code character varying(20),
    published boolean DEFAULT false,
    status character varying(20),
    print_params text,
    last_result text,
    description character varying(255),
    filepath character varying(255),
    report_key character varying(255)
);


ALTER TABLE audit_report OWNER TO postgres;

--
-- Name: storage_personnel_presences; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_storage_personnel_presences (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    storage_personnel_presences_id integer NOT NULL,
    employee_id integer DEFAULT 0 NOT NULL,
    date_start bigint DEFAULT 0 NOT NULL,
    date_end bigint DEFAULT 0 NOT NULL,
    extraordinary_authorized integer DEFAULT 0 NOT NULL,
    permission_remain integer DEFAULT 0 NOT NULL,
    vacation_remain integer DEFAULT 0 NOT NULL,
    to_define bigint DEFAULT 0 NOT NULL,
    period_vacation bigint DEFAULT 0 NOT NULL,
    period_permission bigint DEFAULT 0 NOT NULL,
    ext_start_o integer DEFAULT 0 NOT NULL,
    ext_start integer DEFAULT 0 NOT NULL,
    ext_end_o integer DEFAULT 0 NOT NULL,
    ext_end integer DEFAULT 0 NOT NULL,
    note text
);


ALTER TABLE audit_storage_personnel_presences OWNER TO postgres;

--
-- Name: storage_personnel_stack; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_storage_personnel_stack (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    storage_personnel_presences bigint,
    absence_stack bigint,
    stack_denomination character varying(255) NOT NULL,
    value_start_o double precision DEFAULT 0 NOT NULL,
    value_start double precision DEFAULT 0 NOT NULL,
    value_end_o double precision DEFAULT 0 NOT NULL,
    value_end double precision DEFAULT 0 NOT NULL,
    unit character varying(1) DEFAULT 'h'::character varying NOT NULL,
    recover boolean DEFAULT false NOT NULL,
    reset_type_applied smallint DEFAULT 0 NOT NULL
);


ALTER TABLE audit_storage_personnel_stack OWNER TO postgres;

--
-- Name: supplier; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_supplier (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    fiscal_code character varying(255),
    vat_number character varying(255),
    name character varying(255),
    address character varying(255),
    city_id integer,
    city_name character varying(255),
    city_province character varying(255),
    city_postal_code integer
);


ALTER TABLE audit_supplier OWNER TO postgres;

--
-- Name: tax_residuals; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_tax_residuals (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id_residual integer NOT NULL,
    year integer NOT NULL,
    tasse double precision DEFAULT 0.00,
    contributi double precision DEFAULT 0.00,
    quote double precision DEFAULT 0.00,
    diversi double precision DEFAULT 0.00,
    debito double precision DEFAULT 0.00
);


ALTER TABLE audit_tax_residuals OWNER TO postgres;

--
-- Name: trasparenza_voice; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_trasparenza_voice (
    op_id SERIAL PRIMARY KEY ,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    content text,
    reference text,
    parent_voice_id integer,
    published boolean NOT NULL,
    last_update bigint,
    index integer NOT NULL
);


ALTER TABLE audit_trasparenza_voice OWNER TO postgres;

--
-- Name: trasparenza_voice_document; Type: TABLE; Schema: audit; Owner: postgres
--

CREATE TABLE audit_trasparenza_voice_document (
    op_id SERIAL PRIMARY KEY,
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone DEFAULT now() NOT NULL,
    voice_id integer NOT NULL,
    document_id integer NOT NULL
);


ALTER TABLE audit_trasparenza_voice_document OWNER TO postgres;


--
-- Cancello i vecchi trigger che puntavano alla vecchia funzione
--
DROP TRIGGER absence_kind_audit ON absence_kind;
DROP TRIGGER absence_stack_audit ON absence_stack;
DROP TRIGGER absences_audit ON absences;
DROP TRIGGER albo_area_audit ON albo_area;
DROP TRIGGER albo_category_audit ON albo_category;
DROP TRIGGER albo_entity_audit ON albo_entity;
DROP TRIGGER albo_publication_audit ON albo_publication;
-- DROP TRIGGER albo_publication_document_audit ON albo_publication_document;
DROP TRIGGER albo_publication_history_audit ON albo_publication_history;
DROP TRIGGER archive_check_document_audit ON archive_check_document;
DROP TRIGGER archive_class_audit ON archive_class;
DROP TRIGGER archive_class_step_audit ON archive_class_step;
DROP TRIGGER archive_document_audit ON archive_document;
DROP TRIGGER archive_document_file_audit ON archive_document_file;
DROP TRIGGER archive_document_model_audit ON archive_document_model;
DROP TRIGGER archive_document_step_audit ON archive_document_step;
DROP TRIGGER archive_mail_audit ON archive_mail;
DROP TRIGGER archive_mail_account_audit ON archive_mail_account;
DROP TRIGGER archive_mail_attachment_audit ON archive_mail_attachment;
DROP TRIGGER archive_mail_document_audit ON archive_mail_document;
DROP TRIGGER archive_mail_protocol_audit ON archive_mail_protocol;
DROP TRIGGER archive_mail_security_audit ON archive_mail_security;
DROP TRIGGER archive_office_audit ON archive_office;
DROP TRIGGER archive_office_user_audit ON archive_office_user;
DROP TRIGGER archive_origin_audit ON archive_origin;
DROP TRIGGER archive_privilege_audit ON archive_privilege;
DROP TRIGGER archive_user_audit ON archive_user;
DROP TRIGGER bdg_activities_audit ON bdg_activities;
DROP TRIGGER ccp_additional_audit ON ccp_additional;
DROP TRIGGER ccp_additional_templates_audit ON ccp_additional_templates;
DROP TRIGGER ccp_category_audit ON ccp_category;
DROP TRIGGER ccp_category_banks_audit ON ccp_category_banks;
DROP TRIGGER ccp_credits_audit ON ccp_credits;
DROP TRIGGER ccp_credits_type_audit ON ccp_credits_type;
DROP TRIGGER ccp_deposit_slip_audit ON ccp_deposit_slip;
DROP TRIGGER ccp_deposits_audit ON ccp_deposits;
DROP TRIGGER ccp_invoice_audit ON ccp_invoice;
DROP TRIGGER ccp_invoice_deposit_slip_audit ON ccp_invoice_deposit_slip;
DROP TRIGGER ccp_movement_audit ON ccp_movement;
DROP TRIGGER ccp_movement_additional_audit ON ccp_movement_additional;
DROP TRIGGER ccp_payment_audit ON ccp_payment;
DROP TRIGGER ccp_payment_additional_audit ON ccp_payment_additional;
DROP TRIGGER ccp_payment_method_audit ON ccp_payment_method;
DROP TRIGGER ccp_receipt_audit ON ccp_receipt;
DROP TRIGGER ccp_reminder_subjects_audit ON ccp_reminder_subjects;
DROP TRIGGER ccp_type_audit ON ccp_type;
DROP TRIGGER ccp_type_additional_audit ON ccp_type_additional;
DROP TRIGGER ccp_type_step_audit ON ccp_type_step;
DROP TRIGGER ccp_vat_code ON ccp_vat_code;
DROP TRIGGER contact_audit ON contact;
DROP TRIGGER core_bank_account_audit ON core_bank_account;
DROP TRIGGER core_contact_type_audit ON core_contact_type;
DROP TRIGGER core_contact_groups_audit ON core_contactgroups;
DROP TRIGGER core_contacts_audit ON core_contacts;
DROP TRIGGER core_contacts_contactgroups_audit ON core_contacts_contactgroups;
DROP TRIGGER core_forced_action_audit ON core_forced_action;
DROP TRIGGER employee_audit ON employee;
DROP TRIGGER extraordinary_stored_audit ON extraordinary_stored;
DROP TRIGGER invoice ON invoice;
DROP TRIGGER invoice_expiration ON invoice_expiration;
DROP TRIGGER personnel_presences_audit ON personnel_presences;
DROP TRIGGER personnel_projects_audit ON personnel_projects;
DROP TRIGGER personnel_stacks_audit ON personnel_stacks;
DROP TRIGGER personnel_timetable_audit ON personnel_timetable;
DROP TRIGGER protocol_action_audit ON protocol_action;
DROP TRIGGER protocol_correspondent_audit ON protocol_correspondent;
DROP TRIGGER protocol_protocol_audit ON protocol_protocol;
DROP TRIGGER protocol_protocol_correspondent_audit ON protocol_protocol_correspondent;
DROP TRIGGER protocol_protocol_document_audit ON protocol_protocol_document;
DROP TRIGGER protocol_protocol_history_audit ON protocol_protocol_history;
-- DROP TRIGGER protocol_protocol_protocol_audit ON protocol_protocol_protocol;
DROP TRIGGER protocol_send_method_audit ON protocol_send_method;
DROP TRIGGER protocol_subject_kind_audit ON protocol_subject_kind;
DROP TRIGGER protocol_type_audit ON protocol_type;
DROP TRIGGER report_audit ON report;
DROP TRIGGER storage_personnel_presences_audit ON storage_personnel_presences;
DROP TRIGGER storage_personnel_stack_audit ON storage_personnel_stack;
DROP TRIGGER supplier ON supplier;
DROP TRIGGER tax_residuals_audit ON tax_residuals;
DROP TRIGGER trasparenza_voice_audit ON trasparenza_voice;
-- DROP TRIGGER trasparenza_voice_document_audit ON trasparenza_voice_document;

--
-- Ricreo i nuovi trigger che puntano alla nuova funzione e li chiamo tutti "audit" perchè tanto il nome deve essere univoco all'interno della tabella
--
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON absence_kind FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON absence_stack FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON absences FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON albo_area FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON albo_category FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON albo_entity FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON albo_publication FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON albo_publication_document FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON albo_publication_history FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_check_document FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_class FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_class_step FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_document FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_file FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_model FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_step FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_account FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_attachment FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_document FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_protocol FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_security FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_office FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_office_user FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_origin FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_privilege FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON archive_user FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON bdg_activities FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_additional FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_additional_templates FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_category FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_category_banks FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_credits FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_credits_type FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_deposit_slip FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_deposits FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_invoice FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_invoice_deposit_slip FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_movement FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_movement_additional FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_payment FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_payment_additional FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_payment_method FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_receipt FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_reminder_subjects FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_type FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_type_additional FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_type_step FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON ccp_vat_code FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON contact FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON core_bank_account FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON core_contact_type FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON core_contactgroups FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON core_contacts FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON core_contacts_contactgroups FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON core_forced_action FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON employee FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON extraordinary_stored FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON invoice FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON invoice_expiration FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON personnel_presences FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON personnel_projects FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON personnel_stacks FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON personnel_timetable FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_action FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_correspondent FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_correspondent FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_document FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_history FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_protocol_protocol FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_send_method FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_subject_kind FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON protocol_type FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON report FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON storage_personnel_presences FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON storage_personnel_stack FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON supplier FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON tax_residuals FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON trasparenza_voice FOR EACH ROW EXECUTE PROCEDURE public.process_audit();
CREATE TRIGGER audit AFTER INSERT OR UPDATE OR DELETE ON trasparenza_voice_document FOR EACH ROW EXECUTE PROCEDURE public.process_audit();


alter table audit.ccp_movement alter COLUMN number  type integer using (number::integer);

---
--- Copiamo i dati di movimenti e pagamenti che sono utilizzati lato codice
---

INSERT INTO audit_ccp_movement (
    op_action,
    op_date,
    id,
    type_id,
    miscellaneous,
    number,
    note,
    school_year,
    subject_id,
    subject_data,
    subject_seat,
    subject_class,
    amount,
    creation_date,
    subject_type,
    expiration_date,
    tmp_number,
    invoice_id,
    subject_school_address_code,
    subject_school_address,
    invoice_code,
    description,
    da_ratei,
    a_ratei,
    ccp_easy_select,
    subject_school_year,
    payment_intent_token,
    payment_intent_id,
    locked,
    date_published
    )
SELECT
    op_action,
    op_date,
    id,
    type_id,
    miscellaneous,
    number,
    note,
    school_year,
    subject_id,
    subject_data,
    subject_seat,
    subject_class,
    amount,
    creation_date,
    subject_type,
    expiration_date,
    tmp_number,
    invoice_id,
    subject_school_address_code,
    subject_school_address,
    invoice_code,
    description,
    da_ratei,
    a_ratei,
    ccp_easy_select,
    subject_school_year,
    payment_intent_token,
    payment_intent_id,
    locked,
    date_published
     FROM audit.ccp_movement where op_date>='2023-01-01';


INSERT INTO audit_ccp_payment (
    op_action,
    op_date,
    id,
    movement_id,
    operation_date,
    accountable_date,
    amount,
    payment_method_id,
    bollettino,
    account_id,
    account_reference,
    payer_type,
    payer_id,
    payer_name,
    payer_surname,
    payer_fiscal_code,
    payer_address,
    payer_city,
    payer_province,
    payer_zip_code,
    receipt_id,
    ccp_credit,
    ccp_deposit_slip,
    easy_import_protocol,
    covered_movement_id,
    payment_group
) SELECT
    op_action,
    op_date,
    id,
    movement_id,
    operation_date,
    accountable_date,
    amount,
    payment_method_id,
    bollettino,
    account_id,
    account_reference,
    payer_type,
    payer_id,
    payer_name,
    payer_surname,
    payer_fiscal_code,
    payer_address,
    payer_city,
    payer_province,
    payer_zip_code,
    receipt_id,
    ccp_credit,
    ccp_deposit_slip,
    easy_import_protocol,
    covered_movement_id,
    payment_group
 FROM audit.ccp_payment where op_date>='2023-01-01';


--- DELETE USELESS STUFF

DROP FUNCTION IF EXISTS mt_str_replace(text,text,text,text,boolean);
DROP FUNCTION IF EXISTS st_mode_h_outgo(integer);
DROP FUNCTION IF EXISTS st_mode_n_income(integer);
DROP FUNCTION IF EXISTS st_mode_n_outgo(integer);


-- Rigenera abi table con le colonne ordinate
ALTER TABLE abi DROP CONSTRAINT abi_pkey;
DROP SEQUENCE IF EXISTS abi_id_seq cascade;
ALTER TABLE abi RENAME TO abi_old;
CREATE TABLE abi (
    id SERIAL NOT NULL,
    code character varying(5),
    descr text,
    date_act integer,
    substitute integer
);
INSERT INTO abi (id, code, descr, date_act, substitute) SELECT id, code, descr, date_act, substitute FROM abi_old;

SELECT setval('abi_id_seq', COALESCE(MAX(id), 0) + 1, false) FROM abi;
CREATE OR REPLACE VIEW view_abi AS
    SELECT abi.id, CASE WHEN (length((abi.code)::text) < 5) THEN (('0'::text || (abi.code)::text))::character varying ELSE abi.code END AS code, abi.descr FROM abi ORDER BY CASE WHEN (length((abi.code)::text) < 5) THEN (('0'::text || (abi.code)::text))::character varying ELSE abi.code END;
CREATE OR REPLACE VIEW view_school_bank AS
    SELECT DISTINCT ba.account, ba.cin, ba.account_id AS bank_id, ba.institute_id, ba.abi, ba.cab, a.descr FROM institute i, bank_account ba, abi a WHERE (((i.def = true) AND (i.institute_id = ba.institute_id)) AND (ltrim((ba.abi)::text, '0'::text) = ltrim((a.code)::text, '0'::text))) ORDER BY ba.institute_id, ba.abi, a.descr, ba.account, ba.cin, ba.account_id, ba.cab;
DROP TABLE abi_old cascade;


COMMENT ON COLUMN public.absence_stack.reset_type IS NULL;
COMMENT ON COLUMN public.absence_stack.reset_date IS NULL;
COMMENT ON COLUMN public.absence_stack.reset_to_stack_id IS NULL;
COMMENT ON COLUMN public.absence_stack.reset_default_quota IS NULL;
COMMENT ON TABLE public.albo_area IS NULL;
COMMENT ON TABLE public.albo_category IS NULL;
COMMENT ON COLUMN public.albo_category.duration IS NULL;
COMMENT ON TABLE public.albo_entity IS NULL;
COMMENT ON TABLE public.albo_publication IS NULL;
COMMENT ON TABLE public.albo_publication_history IS NULL;
COMMENT ON COLUMN public.albo_publication_history.action IS NULL;
COMMENT ON TABLE public.archive_class IS NULL;
COMMENT ON COLUMN public.archive_class.code IS NULL;
COMMENT ON COLUMN public.archive_class.format IS NULL;
COMMENT ON COLUMN public.archive_class.action IS NULL;
COMMENT ON TABLE public.archive_document IS NULL;
COMMENT ON COLUMN public.archive_document.metadata IS NULL;
COMMENT ON TABLE public.archive_metadata IS NULL;
COMMENT ON COLUMN public.archive_metadata.kind IS NULL;
COMMENT ON TABLE public.archive_privilege IS NULL;
COMMENT ON TABLE public.archive_user IS NULL;
COMMENT ON TABLE public.asl IS NULL;
COMMENT ON TABLE public.ata_project_hour_types IS NULL;
COMMENT ON TABLE public.ata_project_htypes_projects IS NULL;
COMMENT ON TABLE public.authority_type IS NULL;
COMMENT ON TABLE public.bank_acc IS NULL;
COMMENT ON TABLE public.bank_account IS NULL;
COMMENT ON COLUMN public.bank_account.cin IS NULL;
COMMENT ON COLUMN public.bank_account.abi IS NULL;
COMMENT ON COLUMN public.bank_account.cab IS NULL;
COMMENT ON COLUMN public.bank_account.iban IS NULL;
COMMENT ON TABLE public.bdg_activities IS NULL;
COMMENT ON TABLE public.bdg_asses IS NULL;
COMMENT ON TABLE public.bdg_base_activities IS NULL;
COMMENT ON TABLE public.bdg_budg_act IS NULL;
COMMENT ON TABLE public.bdg_budg_voice IS NULL;
COMMENT ON TABLE public.bdg_budget IS NULL;
COMMENT ON COLUMN public.bdg_budget.pred IS NULL;
COMMENT ON COLUMN public.bdg_budget.prop IS NULL;
COMMENT ON TABLE public.bdg_charge_emp IS NULL;
COMMENT ON TABLE public.bdg_engage_header IS NULL;
COMMENT ON TABLE public.bdg_engage_items IS NULL;
COMMENT ON TABLE public.bdg_modal IS NULL;
COMMENT ON TABLE public.bdg_orders IS NULL;
COMMENT ON TABLE public.bdg_rever IS NULL;
COMMENT ON TABLE public.bdg_rp_rel IS NULL;
COMMENT ON TABLE public.bdg_var IS NULL;
COMMENT ON TABLE public.bdg_var_voice IS NULL;
COMMENT ON TABLE public.bdg_voice_var_a IS NULL;
COMMENT ON TABLE public.bdg_voices IS NULL;
COMMENT ON TABLE public.ccp_additional IS NULL;
COMMENT ON TABLE public.ccp_category IS NULL;
COMMENT ON COLUMN public.ccp_movement.subject_type IS NULL;
COMMENT ON TABLE public.ccp_payment IS NULL;
COMMENT ON COLUMN public.ccp_payment.payer_type IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.token IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.payment_id IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.payment_object_id IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.payment_status IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.charge_status IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.payer_type IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.payer_id IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.subject_id IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.date_created IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.date_succeeded IS NULL;
COMMENT ON COLUMN public.ccp_payment_intents.date_processed IS NULL;
COMMENT ON TABLE public.ccp_receipt IS NULL;
COMMENT ON COLUMN public.ccp_type.easy_description IS NULL;
COMMENT ON COLUMN public.core_bank_account.type IS NULL;
COMMENT ON COLUMN public.core_bank_account.ise_type IS NULL;
COMMENT ON COLUMN public.core_bank_account.agency_name IS NULL;
COMMENT ON COLUMN public.core_bank_account.agency_city IS NULL;
COMMENT ON COLUMN public.core_bank_account.agency_zip_code IS NULL;
COMMENT ON TABLE public.cedolino_data IS NULL;
COMMENT ON COLUMN public.city_perc.date IS NULL;
COMMENT ON TABLE public.codici_casse IS NULL;
COMMENT ON TABLE public.codici_cessazione IS NULL;
COMMENT ON TABLE public.codici_tipo_impiego IS NULL;
COMMENT ON TABLE public.codici_tipo_servizio IS NULL;
COMMENT ON TABLE public.core_print_spool IS NULL;
COMMENT ON TABLE public.cud_data IS NULL;
COMMENT ON TABLE public.dma_data IS NULL;
COMMENT ON COLUMN public.employee.part_spesa IS NULL;
COMMENT ON COLUMN public.employee.liq_office IS NULL;
COMMENT ON COLUMN public.employee.adm_code IS NULL;
COMMENT ON COLUMN public.employee.way_pay IS NULL;
COMMENT ON COLUMN public.employee.contr_cat IS NULL;
COMMENT ON COLUMN public.employee.ssp_frm_pmnt IS NULL;
COMMENT ON TABLE public.fiscal_data IS NULL;
COMMENT ON TABLE public.fiscal_documents IS NULL;
COMMENT ON TABLE public.fml_members IS NULL;
COMMENT ON COLUMN public.fml_members.brth_state IS NULL;
COMMENT ON TABLE public.fml_relation IS NULL;
COMMENT ON TABLE public.gate_presences IS NULL;
COMMENT ON COLUMN public.institute.school_fiscal_code IS NULL;
COMMENT ON COLUMN public.institute.inpdap_code IS NULL;
COMMENT ON COLUMN public.institute.assicurazioni_sanitarie IS NULL;
COMMENT ON TABLE public.inv_initial IS NULL;
COMMENT ON TABLE public.personnel_stacks IS NULL;
COMMENT ON COLUMN public.personnel_stacks.reset_quota IS NULL;
COMMENT ON TABLE public.protocol_action IS NULL;
COMMENT ON TABLE public.protocol_correspondent IS NULL;
COMMENT ON COLUMN public.protocol_correspondent.legal_person IS NULL;
COMMENT ON COLUMN public.protocol_correspondent.correspondent_type IS NULL;
COMMENT ON COLUMN public.protocol_protocol.direction IS NULL;
COMMENT ON TABLE public.protocol_protocol_history IS NULL;
COMMENT ON COLUMN public.protocol_protocol_history.action IS NULL;
COMMENT ON COLUMN public.protocol_protocol_protocol.parent_relationship IS NULL;
COMMENT ON TABLE public.protocol_send_method IS NULL;
COMMENT ON TABLE public.protocol_type IS NULL;
COMMENT ON COLUMN public.regions.code IS NULL;
COMMENT ON COLUMN public.regions.name IS NULL;
COMMENT ON TABLE public.revocation IS NULL;
COMMENT ON TABLE public.rtr_charges IS NULL;
COMMENT ON TABLE public.rtr_sal_pcheck IS NULL;
COMMENT ON COLUMN public.rtr_sal_pcheck.month_tfr IS NULL;
COMMENT ON TABLE public.service_state IS NULL;
COMMENT ON COLUMN public.service_state.role_type IS NULL;
COMMENT ON TABLE public.service_termination IS NULL;
COMMENT ON TABLE public.service_type IS NULL;
COMMENT ON TABLE public.tax_residuals IS NULL;
COMMENT ON TABLE public.tfr_coef IS NULL;
COMMENT ON TABLE public.tfr_data IS NULL;
COMMENT ON TABLE public.tipi_di_tasse_voices IS NULL;
COMMENT ON TABLE public.trasparenza_voice IS NULL;
COMMENT ON COLUMN public.users.expiration IS NULL;
COMMENT ON TABLE public.wh_item IS NULL;
COMMENT ON TABLE public.wh_order IS NULL;
COMMENT ON CONSTRAINT absence_stack_absence_stack_fkey ON public.absence_stack IS NULL;


