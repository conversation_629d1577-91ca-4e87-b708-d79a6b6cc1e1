
UPDATE parameter SET value = '3.3.0' WHERE name = 'VERSION';

-- Personnel Projects overhaul

-- New tables

-- Projects
CREATE TABLE personnel_project
(
   id serial NOT NULL,
   name character varying(200) NOT NULL,
   aggregate_code character varying(1),
   aggregate_number smallint,
   start_date bigint,
   end_date bigint,
   hour_insertions_end_date bigint,
   year integer NOT NULL,
   suspended BOOLEAN NOT NULL DEFAULT FALSE,
   description text,
   objectives text,
   responsibles text,
   goods_services text,
   human_resources text,
   CONSTRAINT personnel_project_pkey PRIMARY KEY (id),
   CONSTRAINT personnel_project_name_key UNIQUE (name)
);

COMMENT ON TABLE personnel_project IS 'Personnel Projects.';

CREATE TABLE audit.personnel_project
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   name character varying(200) NOT NULL,
   aggregate_code character varying(1),
   aggregate_number smallint,
   start_date bigint,
   end_date bigint,
   hour_insertions_end_date bigint,
   year integer NOT NULL,
   suspended BOOLEAN NOT NULL,
   description text,
   objectives text,
   responsibles text,
   goods_services text,
   human_resources text
);

CREATE TRIGGER personnel_project_audit
    AFTER INSERT OR UPDATE OR DELETE ON personnel_project
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Hour Types
CREATE TABLE personnel_hour_type
(
   id serial NOT NULL,
   name character varying(50) NOT NULL,
   description character varying(100),
   price double precision NOT NULL DEFAULT 0.00,
   inpdap_perc double precision NOT NULL DEFAULT 0.00,
   inps_perc double precision NOT NULL DEFAULT 0.00,
   irap_perc double precision NOT NULL DEFAULT 0.00,
   CONSTRAINT personnel_hour_type_pkey PRIMARY KEY (id),
   CONSTRAINT personnel_hour_type_name_key UNIQUE (name),
   CONSTRAINT personnel_hour_type_inpdap_perc_check CHECK (inpdap_perc >= 0 AND inpdap_perc <= 100),
   CONSTRAINT personnel_hour_type_inps_perc_check CHECK (inps_perc >= 0 AND inps_perc <= 100),
   CONSTRAINT personnel_hour_type_irap_perc_check CHECK (irap_perc >= 0 AND irap_perc <= 100)
);

COMMENT ON TABLE personnel_hour_type IS 'Hour Types for Projects.';

CREATE TABLE audit.personnel_hour_type
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   name character varying(50) NOT NULL,
   description character varying(100),
   price double precision NOT NULL,
   inpdap_perc double precision NOT NULL,
   inps_perc double precision NOT NULL,
   irap_perc double precision NOT NULL
);

CREATE TRIGGER personnel_hour_type_audit
    AFTER INSERT OR UPDATE OR DELETE ON personnel_hour_type
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Projects - Hour Types
CREATE TABLE personnel_project_hour_type
(
   project_id integer NOT NULL,
   hour_type_id integer NOT NULL,
   duration integer NOT NULL DEFAULT 0,
   CONSTRAINT personnel_project_hour_type_pkey PRIMARY KEY (project_id, hour_type_id),
   CONSTRAINT personnel_project_hour_type_project_id_fkey FOREIGN KEY (project_id) REFERENCES personnel_project (id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT personnel_project_hour_type_hour_type_id_fkey FOREIGN KEY (hour_type_id) REFERENCES personnel_hour_type (id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE TABLE audit.personnel_project_hour_type
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   project_id integer NOT NULL,
   hour_type_id integer NOT NULL,
   duration integer NOT NULL
);

CREATE TRIGGER personnel_project_hour_type_audit
    AFTER INSERT OR UPDATE OR DELETE ON personnel_project_hour_type
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Personnel - Projects
CREATE TABLE personnel_personnel_project
(
   personnel_id integer NOT NULL,
   project_id integer NOT NULL,
   closed boolean NOT NULL DEFAULT FALSE,
   CONSTRAINT personnel_personnel_project_pkey PRIMARY KEY (personnel_id, project_id),
   CONSTRAINT personnel_personnel_project_personnel_id_fkey FOREIGN KEY (personnel_id) REFERENCES employee (employee_id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT personnel_personnel_project_project_id_fkey FOREIGN KEY (project_id) REFERENCES personnel_project (id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE TABLE audit.personnel_personnel_project
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   personnel_id integer NOT NULL,
   project_id integer NOT NULL,
   closed boolean NOT NULL
);

CREATE TRIGGER personnel_personnel_project_audit
    AFTER INSERT OR UPDATE OR DELETE ON personnel_personnel_project
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Data normalization
UPDATE bdg_activities SET budget_year = 0 WHERE budget_year IS NULL;
UPDATE bdg_activities SET budget_year = 0 WHERE budget_year < 0;


-- Data migration
INSERT INTO personnel_project (id, name, aggregate_code, aggregate_number, start_date, end_date, hour_insertions_end_date,
year, suspended, description, objectives, responsibles, goods_services, human_resources) SELECT activ_id, description, aggreg_code, aggreg_nr, start_date, end_date, hours_insertions_end_date, budget_year, suspend, ext_desc, objectives, responsibles, goods_services, human_resources FROM bdg_activities;
INSERT INTO personnel_hour_type (id, name, price, inpdap_perc, inps_perc, irap_perc) SELECT ata_project_hour_type_id, description, price_per_hour, inpdap_perc, inps_perc, irap_perc FROM ata_project_hour_types;
INSERT INTO personnel_project_hour_type (project_id, hour_type_id, duration) SELECT project_id, type_id, dedicated_time FROM ata_project_htypes_projects;
INSERT INTO personnel_personnel_project (personnel_id, project_id) SELECT personnel_id, project_id FROM personnel_projects;


-- Update of Seqs
SELECT setval('personnel_project_id_seq', (SELECT COALESCE(max(id), 0) + 1 FROM personnel_project), FALSE);
SELECT setval('personnel_hour_type_id_seq', (SELECT COALESCE(max(id), 0) + 1 FROM personnel_hour_type), FALSE);


-- Update related constraints
ALTER TABLE personnel_presences DROP CONSTRAINT foreign_bdg_activities_personnel_presences;
ALTER TABLE personnel_presences ADD CONSTRAINT personnel_presence_personnel_id_fkey FOREIGN KEY (project_edit_id) REFERENCES personnel_project (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE SET NULL;


-- Set Closed status for expired projects
--if end_date expired then set locked true



-- Remove obsolete tables
-- DROP TABLE personnel_projects;
-- DROP TABLE personnel_projects_del;
-- DROP TABLE audit.personnel_projects;
--
-- DROP TABLE ata_project_hour_types;
-- DROP TABLE ata_project_hour_types_del;
--
-- DROP TABLE ata_project_htypes_projects;
--DROP TABLE ata_project_htypes_projects_del;


-- Project View

-- DROP VIEW personnel_view_project;

CREATE OR REPLACE VIEW personnel_view_project AS
SELECT pr.id, pr.name, pr.aggregate_code, pr.aggregate_number, pr.start_date, pr.end_date, pr.hour_insertions_end_date, pr.year, pr.suspended, pr.description, pr.objectives, pr.responsibles, pr.goods_services, pr.human_resources, pr.count_personnel, pr.linked_personnel, pr.count_hour_types, pr.linked_hour_types
FROM (
    SELECT p.id, p.name, p.aggregate_code, p.aggregate_number, p.start_date, p.end_date, p.hour_insertions_end_date, p.year, p.suspended, p.description, p.objectives, p.responsibles, p.goods_services, p.human_resources,
           (
            SELECT count(personnel_id) AS count
            FROM personnel_personnel_project
            WHERE project_id = p.id
           ) AS count_personnel,
           (
            SELECT string_agg(lp::TEXT, ','::text) AS string_agg
            FROM (
                    SELECT personnel_id || '-' || closed AS lp
                    FROM personnel_personnel_project
                    WHERE project_id = p.id
                    ORDER BY closed, personnel_id
            ) ppp
           ) AS linked_personnel,
           (
            SELECT count(hour_type_id) AS count
            FROM personnel_project_hour_type
            WHERE project_id = p.id
           ) AS count_hour_types,
           (
            SELECT string_agg(lht::TEXT, ','::text) AS string_agg
            FROM (
                    SELECT hour_type_id || '-' || duration AS lht
                    FROM personnel_project_hour_type
                    WHERE project_id = p.id
                    ORDER BY duration DESC, hour_type_id
            ) ppht
           ) AS linked_hour_types
    FROM personnel_project p
) pr;