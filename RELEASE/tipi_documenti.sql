INSERT INTO archive_document_model (id, name, template) VALUES (949, 'Prestazioni rese dal Personale: anagrafe', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (815, 'Consigli di classe: Convocazioni e deliberazioni', '{"protocol":{"type":194,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (817, 'Contenzioso: procedimento disciplinare contestazione di addebiti', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (825, 'Dichiarazioni fiscali 770 IRAP-IQ IVA', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (827, 'Didattica: concorsi, progetti, convegni, manifestazioni promossi da altri enti e da terzi ', '{"protocol":{"type":210,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (828, 'Didattica: concorsi, progetti, convegni, manifestazioni promossi da Regioni, Province, Comuni', '{"protocol":{"type":210,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (775, 'Bilancio: determina del Dirigente Scolastico', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (778, 'Bilancio: programma annuale - approvazione', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[65]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (786, 'Bilancio: variazioni al Programma Annuale', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[61]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (820, 'Contrattazione: Contratto Collettivo Decentrato di Istituto', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[42]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1010, 'Curriculum personali per Trasparenza', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[35]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (738, 'Alunni diversamente abili, BES bisogni educativi speciali', '{"protocol":{"type":228,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (742, 'Archivio: scarto atti', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (747, 'Assenze: assenza infermità', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (749, 'Assenze: congedo biennale art 80 legge 338-2000 richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (752, 'Assenze: ferie - richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (755, 'Assenze: malattia - richiesta, certificazione medica/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (757, 'Assenze: permessi diritto allo studio', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (760, 'Assenze: permesso per cure idrotermali', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (764, 'Assenze: permesso retribuito – richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (766, 'Assenze: permesso retribuito, L.816/1985, per svolgimento Carica Amministrativa', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (769, 'Assenze: statistiche e rilevazioni dati', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (772, 'Attività sindacale: Iscrizione/revoca Sindacale Docente', '{"protocol":{"type":202,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (780, 'Bilancio: radiazione residui', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (783, 'Bilancio: servizio di tesoreria OIL ordinativi informatici', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (787, 'Carriera: dichiarazione dei servizi', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (790, 'Carriera: proposta conferma in ruolo', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (793, 'Carriera: riconoscimento infermita'' per causa di servizio', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (795, 'Carriera: ricostruzione di carriera', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (800, 'Certificazioni AICA ECDL', '{"protocol":{"type":210,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (803, 'Certificazioni varie Docenti', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (805, 'Cessazione dal Servizio: Docente', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (809, 'Collegio dei Revisori dei Conti', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (811, 'Commissioni Esami di Stato abilitazione esercizio libere professioni', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (834, 'DURC - Documento Unico di Regolarit Contributiva', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (837, 'Esami d''idoneità', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (841, 'Esami di stato primo ciclo: invio elenco candidati licenziati e richiesta relativi diplomi', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (844, 'Esami di stato secondo ciclo: certificato sostitutivo diploma', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (846, 'Esami di stato secondo ciclo: domanda di ammissione', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (848, 'Esami di stato secondo ciclo: fabbisogno diplomi ', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (851, 'Esami di stato secondo ciclo: ricorsi', '{"protocol":{"type":"VIII  1 - Senza ulteriori suddivisioni in classi","object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (853, 'Fascicolo alunno: conferma titolo di studio', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (856, 'Fascicolo alunno: esonero lezioni educazioni fisica', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (860, 'Fascicolo alunno: iscrizione alunno - infanzia', '{"protocol":{"type":220,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (862, 'Fascicolo alunno: obbligo formativo alunni - comunicazione', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (865, 'Fascicolo alunno: richiesta deroga dall''obbligo della frequenza - richiesta parere in merito secondaria', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (867, 'Fascicolo personale: accusa ricevuta Docenti', '{"protocol":{"type":206,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (870, 'Fascicolo personale: assunzione di servizio ATA', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (873, 'Fascicolo personale: contratto di lavoro a tempo indeterminato/determinato  ATA', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (876, 'Fascicolo personale: convocazione per nomine a tempo indeterminato docenti e ATA', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (878, 'Fascicolo personale: liquidazione trattamento provvisorio di pensione', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (881, 'Fascicolo personale: richiesta/trasmissione fascicolo ATA', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (883, 'Fascicolo personale: schede insegnanti DOA', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (890, 'Formazione/aggiornamento: adesione Corso', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (892, 'Formazione/aggiornamento: domanda di iscrizione', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (889, 'Formazione/aggiornamento:  specializzazione Docenti Sostegno', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (895, 'Graduatorie: attestato relativo conseguimento titolo di specializzazione polivalente', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (898, 'Graduatorie: domanda di supplenza', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (901, 'Graduatorie: reclamo/risposta', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (903, 'Infortunio: ATA', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (907, 'Inventario: carico beni mobili', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (910, 'Inventario: passaggio di consegne', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (913, 'Inventario: verbale di consegna', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (917, 'Manutenzione edifici scolastici richiesta interventi', '{"protocol":{"type":237,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (919, 'Mobilità: assegnazione provvisoria', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (950, 'Preventivi e indagini di mercato per effettuazione lavori, forniture di beni e servizi ', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (952, 'Privacy: trattamento dei dati personali incarichi/autorizzazioni', '{"protocol":{"type":188,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (955, 'Provincia: forniture dirette per le scuole', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (958, 'Registro di classe', '{"protocol":{"type":211,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (968, 'Retribuzioni: indennità di direzione', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (964, 'Retribuzioni: incarico attività personale ATA', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (965, 'Retribuzioni: incarico attività personale Docente', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (971, 'Retribuzioni: rilevazioni e statistiche', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (974, 'Riscatto: Indennità Buonuscita TFS INPS', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (976, 'Riscatto: studi universitari ai fini pensionistici', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (979, 'Scambi culturali: gestione didattica', '{"protocol":{"type":214,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (982, 'Sciopero: rilevazione dati', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (985, 'Software di gestione e amministrazione della scuola', '{"protocol":{"type":240,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (988, 'TFR: modello Tfr-1', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (991, 'Valutazioni annuali - scrutini', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (995, 'Viaggi di istruzione: gestione didattica', '{"protocol":{"type":214,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (871, 'Fascicolo personale: assunzione di servizio Docenti', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (872, 'Fascicolo personale: assunzione di servizio per incarico di dirigenza/presidenza', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (874, 'Fascicolo personale: contratto di lavoro a tempo indeterminato/determinato  Docenti', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (875, 'Fascicolo personale: convocazione per nomine a tempo determinato docenti e ATA', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (877, 'Fascicolo personale: iscrizione albo professionale - libera professione ', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (879, 'Fascicolo personale: provvedimento di decadenza', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (880, 'Fascicolo personale: richiesta ispezione didattica', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (882, 'Fascicolo personale: richiesta/trasmissione fascicolo Docenti', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1008, 'Contratto: accordo di rete', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (926, 'Organico: invio modello 3', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (929, 'Organico: sostegno (adeguamento al fatto)', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (934, 'Organizzazione: Orario Scolastico', '{"protocol":{"type":209,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (938, 'Pagellino', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (941, 'Personale: conferimento incarico di missione', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (944, 'POF – Piano dell’offerta formativa (attività e progetti)', '{"protocol":{"type":209,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (947, 'Prestazioni previdenziali/assistenziali: mutuo Inps domanda/concessione', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1001, 'Bando di gara: costituzione commissione tecnica', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1013, 'Esperti esterni: Curriculum per pubblicazione amministrazione trasparente', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (1021, 'Inventario: provvedimento passaggio di consegna', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (884, 'Fascicolo personale: Trattamento di fine rapporto (TFR)', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (737, 'Affari generali: ordinamento delle scuole', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (885, 'Fatture e notule per effettuazione lavori, forniture di beni e servizi', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (739, 'Alunni: assemblee di classe: richieste, verbali', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (740, 'Alunni: servizio di Mensa Alunni', '{"protocol":{"type":224,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (741, 'Alunni: servizio di Trasporto Alunni', '{"protocol":{"type":224,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (891, 'Formazione/aggiornamento: attestato di partecipazione', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (886, 'Formazione/aggiornamento:  autorizzazione', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (743, 'Assegni, Borse di studio', '{"protocol":{"type":224,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (744, 'Assemblee sindacali: richiesta e concessione', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (887, 'Formazione/aggiornamento:  richiesta finanziamento corso', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (888, 'Formazione/aggiornamento:  riconversione professionale', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (746, 'Assenze: aspettativa per motivi di famiglia, di lavoro, personali e di studio - richiesta/decreto ', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (748, 'Assenze: astensione facoltativa e obbligatoria maternità', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (750, 'Assenze: congedo Maternità e parentale - richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (751, 'Assenze: congedo straordinario retribuito – richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (753, 'Assenze: gravi patologie - richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (754, 'Assenze: malattia - accertamento medico', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (756, 'Assenze: partecipazione a concorsi od esami richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (996, 'Assenze - festività soppresse ex L. 937/1977 - richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (893, 'Gestione calore: accessione riscaldamento', '{"protocol":{"type":239,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (894, 'Giunta Esecutiva di Istituto: Convocazioni e deliberazioni', '{"protocol":{"type":195,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (896, 'Graduatorie: classi di concorso esaurite', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (758, 'Assenze: permessi L104/92 - richiesta/documenti/decreto ', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (897, 'Graduatorie: domanda di messa a disposizione fuori graduatoria', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (759, 'Assenze: permesso carica amministrativa - richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (761, 'Assenze: permesso per donazione sangue', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (899, 'Graduatorie: gestione', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (762, 'Assenze: permesso per lutto richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (900, 'Graduatorie: pubblicazione all''albo', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (763, 'Assenze: permesso per motivi personali o familiari richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1029, 'Viaggi di istruzione: monitoraggio', '{"protocol":{"type":214,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[8]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (814, 'Concorsi: disponibilità per partecipazione a Commissione Concorso', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (818, 'Contenzioso: procedimento disciplinare ricorsi', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (765, 'Assenze: permesso retribuito, art.3 DPR 23/8/1988 n.395: Corso Universitario ', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (902, 'Incarichi al personale (RSPP, RLS, ASPP, Preposti alla sicurezza, Medico competente) ', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (767, 'Assenze: permesso sindacale richiesta/decreto', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (768, 'Assenze: recupero compensativo servizio straordinario', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (904, 'Infortunio: disposizioni generali', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (905, 'Infortunio: docenti', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (745, 'Assenze: festività richiesta/decreto ', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (770, 'ASU Attività socialmente utili gestione', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (771, 'Attività sindacale: Iscrizione/revoca Sindacale ATA ', '{"protocol":{"type":202,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (906, 'Inventario: biblioteca', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (773, 'Avviamento alla Pratica Sportiva', '{"protocol":{"type":217,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (774, 'Bilancio: appalti per la gestione di servizi ausiliari (pulizie)', '{"protocol":{"type":239,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (908, 'Inventario: furti, atti vandalici, polizza assicurazione (denunce, relazioni)', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (909, 'Inventario: nomine sub consegnatari', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (776, 'Bilancio: licenza gratuita abbonamento Rai - TV', '{"protocol":{"type":240,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (777, 'Bilancio: monitoraggi rilevazioni flussi finanziari', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (911, 'Inventario: ricognizione e rivalutazione', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (912, 'Inventario: scarico beni mobili dello Stato', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (781, 'Bilancio: richiesta acquisto materiale di arredamento', '{"protocol":{"type":239,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (782, 'Bilancio: richiesta acquisto materiale didattico', '{"protocol":{"type":230,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (914, 'Libri di testo: proposte dei Docenti', '{"protocol":{"type":212,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (784, 'Bilancio: servizio di tesoreria OIL ricevute aggregate ordinativi informatici', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (915, 'Libri di Testo: trasmissione modelli compilati', '{"protocol":{"type":212,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (785, 'Bilancio: trasmissione prospetto oneri sostenuti dalle amministrazioni locali', '{"protocol":{"type":230,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (916, 'Locali Scolastici: richiesta/autorizzazione/nulla osta utilizzazione spazi', '{"protocol":{"type":237,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (918, 'Missioni: rimborso spese', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (920, 'Mobilità: domanda di utilizzazione e/o assegnazione provvisoria', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (921, 'Mobilità: domanda di utilizzazione presso UST/USR/altri enti', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (923, 'Mobilità: mobilità del personale docente ed ata', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (922, 'Mobilità: mobilità intercompartimentale volontaria del personale scolasticoo', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (925, 'Ore eccedenti: richiesta/assegnazione', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (931, 'Organico: adeguamento dell''organico di diritto alla situazione di fatto', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (927, 'Organico: organico di diritto', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1006, 'Contratto con esperto esterno', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[3]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1023, 'Obbiettivi accessibilità dei dati', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (928, 'Organico: rilevazione DOP', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (930, 'Organico: sostegno (diritto)', '{"protocol":{"type":242,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (932, 'Organigramma', '{"protocol":{"type":186,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (933, 'Organizzazione: Calendario Scolastico', '{"protocol":{"type":209,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (935, 'Orientamento scolastico', '{"protocol":{"type":"V  1 - Orientamento e placement","object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (936, 'Pagella', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (937, 'Pagella di religione/Materia alternativa', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (939, 'Partecipazione a concorso corso abilitante TFA', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (940, 'Patrimonio: patrimonio immobiliare scolastico - anagrafe', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (788, 'Carriera: passaggio di cattedra', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (789, 'Carriera: passaggio di ruolo', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (791, 'Carriera: ricongiunzione periodi/servizi ex art.113 T.U.1092/73', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (792, 'Carriera: ricongiunzione periodi/servizi ex legge 29/1979 art. 2', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (794, 'Carriera: riconoscimento servizio pre ruolo ai fini economici e di carriera', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (797, 'Certificati di servizio ATA', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (798, 'Certificati di servizio Docenti', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (942, 'Personale: Orario di servizio', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (796, 'Carriera: computo/riscatto servizi e periodi ex T.U. 1092/73', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (943, 'Piano di emergenza ed evacuazione dei locali scolastici', '{"protocol":{"type":238,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (799, 'Certificato  iscrizione/frequenza', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (945, 'Polizza assicurazione infortuni RCT', '{"protocol":{"type":234,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (801, 'Certificazioni casellario', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (802, 'Certificazioni e sicurezza per gli edifici scolastici (CPI-certificati prevenzione incendi-NOP Nulla osta provvisori, ecc.)', '{"protocol":{"type":237,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (804, 'Cessazione dal Servizio: ATA', '{"protocol":{"type":243,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (807, 'CO - Comunicazioni obbligatorie Centri per l''impiego', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (808, 'Codice di comportamento dei dipendenti pubblici', '{"protocol":{"type":185,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (946, 'Prestazioni previdenziali/assistenziali: Fondo credito prestiti domanda/concessione', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (810, 'Collegio docenti: convocazioni e deliberazioni', '{"protocol":{"type":195,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (813, 'Comune: richiesta intervento', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (821, 'Contratti: dichiarazione sostitutiva documentazione di rito', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (823, 'Contratti: trasmissione marche da bollo per regolarizzazione  documentazione', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (822, 'Contratti: individuazione personale', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (819, 'Conto Consuntivo: analisi - approvazione', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[65]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (948, 'Prestazioni rese dal Personale Amministrazione Pubblica: autorizzazione e concessione', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (951, 'Previdenza complementare: adesione Fondo ESPERO', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (824, 'DDT Documento di trasporto per effettuazione lavori, forniture di beni e servizi', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (826, 'Didattica: concorsi, progetti, convegni manifestazioni promossi da MIUR, USR, AST, Scuole', '{"protocol":{"type":210,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (829, 'Dirigenza: delega di funzioni', '{"protocol":{"type":197,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (830, 'Dirigenza: esonero dall''insegnamento del Docente Collaboratore', '{"protocol":{"type":186,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (831, 'Dirigenza: nomina docente Collaboratore del Dirigente Scolastico', '{"protocol":{"type":186,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (832, 'Distacco sindacale: richiesta e concessione', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (833, 'Domanda di incarico e supplenze', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (835, 'EDMS: Gestione documentale', '{"protocol":{"type":240,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (836, 'Erasmus', '{"protocol":{"type":210,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (838, 'Esami di qualifica regionale', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (839, 'Esami di stato primo ciclo: calendario', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (840, 'Esami di stato primo ciclo: decreto di nomina a Presidente della Commissione', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (842, 'Esami di stato primo ciclo: relazione finale del Dirigente Scolastico', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (843, 'Esami di stato secondo ciclo: atti commissione e pubblicazione risultati', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (845, 'Esami di stato secondo ciclo: docenti nominati rappresentanti delle V classi', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (847, 'Esami di stato secondo ciclo: domanda/nomina a componente di commissione per gli esami di stato del secondo ciclo', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (849, 'Esami di stato secondo ciclo: programma di esame', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (850, 'Esami di stato secondo ciclo: proposta formazione commissioni', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (852, 'Esami di stato secondo ciclo: trasmissione registri e atti vari', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (854, 'Fascicolo alunno: domanda di nulla osta per trasferimento alunni altra scuola', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (855, 'Fascicolo alunno: esonero dalle lezioni di religione cattolica', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (857, 'Fascicolo alunno: infortunio alunno', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (858, 'Fascicolo alunno: invio diploma di licenza media', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (859, 'Fascicolo alunno: iscrizione alunno - secondaria', '{"protocol":{"type":220,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (861, 'Fascicolo alunno: iscrizione alunno - primaria', '{"protocol":{"type":220,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (863, 'Fascicolo alunno: passaggio alunni equipollenza titoli di studio', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (864, 'Fascicolo alunno: richiesta deroga dall''obbligo della frequenza - richiesta parere in merito primaria', '{"protocol":{"type":221,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (866, 'Fascicolo personale: accusa ricevuta ATA', '{"protocol":{"type":206,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (868, 'Fascicolo personale: anno di formazione Docenti ', '{"protocol":{"type":246,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1027, 'Retribuzioni: conferimento incarico di relatore', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[6]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (869, 'Fascicolo personale: assegno nucleo familiare - richiesta e/o concessione', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (953, 'Programma di studio', '{"protocol":{"type":222,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (954, 'Promozione: Comunicato stampa', '{"protocol":{"type":191,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (956, 'Provincia: richiesta intervento', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (957, 'RAV Rapporto di autovalutazione', '{"protocol":{"type":189,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (959, 'Registro per materia', '{"protocol":{"type":211,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (960, 'Registro voti', '{"protocol":{"type":211,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (961, 'Regolamento di Istituto - Statuti', '{"protocol":{"type":185,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (962, 'Retribuzioni: domanda di accreditamento compensi in conti correnti', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (963, 'Retribuzioni: fondo per miglioramento dell''offerta formativa (MOF)', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (966, 'Retribuzioni: incarico di Funzione Strumentale al personale Docente', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (967, 'Retribuzioni: incarico specifico al personale ATA ', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (969, 'Retribuzioni: indennità e compensi esami', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (970, 'Retribuzioni: liquidazione stipendi personale a tempo determinato', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (972, 'Retribuzioni: trasmissione Modello CU', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (973, 'Riscatto: computo/riscatto ai fini quiescenza e buonuscita', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (975, 'Riscatto: riscatto/computo ai fini del TFR', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (977, 'Riscatto: trattamento di quiescenza', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (978, 'RSU Elezioni, nomine, convocazioni e verbali', '{"protocol":{"type":202,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (980, 'Sciopero: comunicazione individuale sciopero', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (981, 'Sciopero: indizione sciopero', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (983, 'Sicurezza e Salute dei Lavoratori sul luogo di Lavoro: normativa', '{"protocol":{"type":238,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (984, 'Sistema regionale di Istruzione e formazione professionale IeFP', '{"protocol":{"type":219,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (986, 'Statistiche: modelli ISTAT', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (987, 'TFR: accertamento servizi', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (989, 'TFR: modello Tfr-2 (riliquidazione Tfr)', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (990, 'Tirocinio: attività di alternanza scuola lavoro', '{"protocol":{"type":214,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (992, 'Verbali collaudo effettuazione lavori, forniture di beni', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (993, 'Versamenti Conto Bancoposta', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (994, 'Versamento contributi previdenziali assistenziali INPS', '{"protocol":{"type":233,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (998, 'AVCP', '{"protocol":{"type":240,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (736, 'Accesso atti amministrativi', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[24,25]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (997, 'Attività sindacale: richiesta contrattazione integrativa', '{"protocol":{"type":202,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[43]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (999, 'Avviso pubblico proposte di incarico (L. 107/2015 )', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1000, 'Bando di gara: atto di aggiudicazione provvisoria', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1017, 'Indicatore tempestitvità pagamenti', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (1002, 'Bando di gara: determina del Dirigent e Scolastico - lettera di invito', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1003, 'Bando di gara: determina di aggiudicazione definitiva Dirigente Scolastico', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1004, 'Bando di gara: verbale apertura buste', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[11]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (779, 'Bilancio: programma di sviluppo delle tecnologie didattiche - finanziamenti ', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[61]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (806, 'Circolari interne e ordini di servizio', '{"protocol":{"type":185,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[25]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (812, 'Comune: forniture per le scuole', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[64]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (816, 'Consiglio Istituto: Convocazioni e deliberazioni', '{"protocol":{"type":193,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[61]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (924, 'Ordini e contratti per effettuazione lavori, forniture di beni e servizi', '{"protocol":{"type":231,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1005, 'Assegnazione delle funzioni aggiuntive al personale ATA', '{"protocol":{"type":247,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1007, 'Individuazione proposta di assunzione con contratto a tempo determinato', '{"protocol":{"type":201,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1009, 'Contratto: utilizzo attrezzature scolastiche', '{"protocol":{"type":235,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[68]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1011, 'Determina acquisti', '{"protocol":{"type":204,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[23]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1012, 'Esami di stato: domanda accesso agli atti', '{"protocol":{"type":249,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[3]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1014, 'Incarichi: compenso prestazione occasionale', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[3]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1015, 'Incompatibilità: autorizzazione del DS incarico retribuito', '{"protocol":{"type":244,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1016, 'Incompatibilità: autorizzazione libera professione', '{"protocol":{"type":232,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[74]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1018, 'Inventario: decreto alienazione beni', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1019, 'Inventario: individuazione sub consegnatario', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1020, 'Inventario: provvedimento di riconsegna materiale', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[62]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1022, 'Monitoraggi', '{"protocol":{"type":236,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[67]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1025, 'Programma Annuale: analisi revisori', '{"protocol":{"type":209,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[25]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1024, 'Piano di lavoro', '{"protocol":{"type":251,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[64]}}');
INSERT INTO archive_document_model (id, name, template) VALUES (1026, 'PTOF: Piano Triennale Offerta Formativa', NULL);
INSERT INTO archive_document_model (id, name, template) VALUES (1028, 'RSPP - Responsabile della sicurezza', '{"protocol":{"type":245,"object":""},"albo":{"title":"","start":"","total_days":"","category":"","area":"","ente":""},"trasparenza":{"voices":[25]}}');

SELECT pg_catalog.setval('archive_document_model_id_seq', 1029, true);
