/* QUERY AGGIUNTA RIGA IN AUTH_PERMISSION */
INSERT INTO auth_permission(id,title,super_user,auth_section) 
    VALUES( 470,'Protocollo Informatico | Riservato | Attivo','f',10);

/* QUERY UPDATE TUTTI I GRUPPI PER NASCONDERE RISERVATO */
INSERT INTO auth_permission_group(groups,auth_permission) 
    (SELECT a.gid groups,b.id auth_permission 
        FROM auth_permission b 
        RIGHT JOIN groups a ON a.gid > 0 
        WHERE b.id = 470);


/* Alter protocol_register table */ 
-- -------------------------------------------------------------
ALTER TABLE "public"."protocol_register" ADD COLUMN "archive_document_id" Integer;
-- -------------------------------------------------------------

-- -------------------------------------------------------------
ALTER TABLE "public"."protocol_register"
	ADD CONSTRAINT "lnk_archive_document_protocol_register" FOREIGN KEY ( "archive_document_id" )
	REFERENCES "public"."archive_document" ( "id" ) MATCH FULL
	ON DELETE No Action
	ON UPDATE No Action;


INSERT INTO archive_origin( id,name,code,description) SELECT '4','Reg. Prot.','rp', 'Registri di protocollo in conservazione diretta' WHERE NOT EXISTS (SELECT id FROM archive_origin WHERE id = 4) RETURNING id;