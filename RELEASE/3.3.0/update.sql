-- MAIL 

CREATE TABLE archive_mail_security (
	id serial PRIMARY KEY,
	"name" character varying(8) NOT NULL
);

INSERT INTO archive_mail_security (name) VALUES ('ssl');
INSERT INTO archive_mail_security (name) VALUES ('tls');

CREATE TABLE archive_mail_protocol (
	id serial PRIMARY KEY,
	"name" character varying(8) NOT NULL
);

INSERT INTO archive_mail_protocol (name) VALUES ('imap');
INSERT INTO archive_mail_protocol (name) VALUES ('pop3');
INSERT INTO archive_mail_protocol (name) VALUES ('smtp');

CREATE TABLE archive_mail_account (
	id serial PRIMARY KEY,	
	"name" character varying(255) NOT NULL,
	protocol integer NOT NULL,
	host character varying(255) NOT NULL,
	port integer NOT NULL,
	"security" integer NOT NULL,
	username character varying(255) NOT NULL,
	"password" character varying(255) NOT NULL,
	active boolean NOT NULL DEFAULT true,
	CONSTRAINT archive_mail_account_protocol_fkey FOREIGN KEY (protocol)
	      REFERENCES archive_mail_protocol (id),	
	CONSTRAINT archive_mail_account_security_fkey FOREIGN KEY ("security")
	      REFERENCES archive_mail_security (id) 	
);

CREATE TABLE archive_mail (    
	id serial,
	account integer REFERENCES archive_mail_account (id) NOT NULL,
	"date" TIMESTAMP NOT NULL,  
	"from" text NOT NULL,
	"to" text NOT NULL,
	cc text,
	ccn text,
	subject text,
	message text,
	"raw" text NOT NULL,   
    deleted boolean NOT NULL DEFAULT false, 
    CONSTRAINT archive_mail_pkey PRIMARY KEY (id, account) 
);

CREATE TABLE archive_mail_attachment (	
    id serial NOT NULL,
    mail integer NOT NULL,
    account	INTEGER NOT NULL,	
	"name" character varying(255) NOT NULL,
	path character varying(255) NOT NULL,
    CONSTRAINT archive_mail_attachment_pkey PRIMARY KEY (mail, account),
    FOREIGN KEY (mail, account) REFERENCES archive_mail (id, account)
);

-- FLUSSI

-- DROP TABLE archive_class_step;

CREATE TABLE archive_class_step
(
  id serial NOT NULL,
  archive_class integer NOT NULL,
  sort integer NOT NULL,
  user_id integer,  
  protocol boolean NOT NULL DEFAULT false,
  albo boolean NOT NULL DEFAULT false,
  trasparenza boolean NOT NULL DEFAULT false,
  sign boolean NOT NULL DEFAULT false,
  CONSTRAINT archive_class_step_pkey PRIMARY KEY (id),
  CONSTRAINT archive_class_step_archive_class_fkey FOREIGN KEY (archive_class)
      REFERENCES archive_class (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE,
  CONSTRAINT user_uid_fkey FOREIGN KEY (user_id)
      REFERENCES users (uid) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE
);  

-- DROP TABLE archive_document_step;

CREATE TABLE archive_document_step
(
    id serial NOT NULL,
    archive_document integer NOT NULL,
    sort integer NOT NULL,    
    user_id integer,
    protocol boolean NOT NULL DEFAULT false,
    albo boolean NOT NULL DEFAULT false,
    trasparenza boolean NOT NULL DEFAULT false,
    sign boolean NOT NULL DEFAULT false,
    CONSTRAINT archive_document_step_pkey PRIMARY KEY (id),
    CONSTRAINT archive_document_step_archive_document_fkey FOREIGN KEY (archive_document)
        REFERENCES archive_document (id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE,
    CONSTRAINT user_uid_fkey FOREIGN KEY (user_id)
        REFERENCES users (uid) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE
);

-- DROP TABLE archive_document_file;

CREATE TABLE archive_document_file
(
    id serial NOT NULL,
    archive_document INTEGER,
    filename text NOT NULL,           
    filetype text NOT NULL,    
    path character varying(255),
    size integer,   
    CONSTRAINT archive_document_file_pkey PRIMARY KEY (id),
    CONSTRAINT archive_document_file_archive_document_id_fkey FOREIGN KEY (archive_document)
        REFERENCES archive_document (id) MATCH SIMPLE
        ON UPDATE CASCADE ON DELETE CASCADE
);

SELECT setval('archive_document_file_id_seq', (SELECT max(max) + 1 FROM 
(
    SELECT max(document_id) FROM protocol_protocol_document 
    UNION
    SELECT max(document_id) FROM trasparenza_voice_document 
    UNION
    SELECT max(document_id) FROM albo_publication_document
) m
));

-- INSERICE TUTTI I FILE NELLA NUOVA TABELLA CON LA NUOVA STRUTTURA
INSERT INTO archive_document_file (archive_document, filename, filetype, path, size) SELECT id, filename, filetype, path, size FROM archive_document;

ALTER TABLE archive_document DROP COLUMN filename;
ALTER TABLE archive_document DROP COLUMN filetype;
ALTER TABLE archive_document DROP COLUMN path;
ALTER TABLE archive_document DROP COLUMN size;

ALTER TABLE audit.archive_document DROP COLUMN filename;
ALTER TABLE audit.archive_document DROP COLUMN filetype;
ALTER TABLE audit.archive_document DROP COLUMN path;
ALTER TABLE audit.archive_document DROP COLUMN size;


ALTER TABLE archive_document ADD COLUMN action_sign BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE archive_document ADD COLUMN action_sign_date BIGINT;
ALTER TABLE audit.archive_document ADD COLUMN action_sign BOOLEAN  NOT NULL DEFAULT false;
ALTER TABLE audit.archive_document ADD COLUMN action_sign_date BIGINT;

INSERT INTO archive_origin VALUES (2, 'Mail', 'mail', 'Documenti importati da account di posta elettronica');

-- Fix previous protocol_document link data to new file table
ALTER TABLE protocol_protocol_document DROP CONSTRAINT protocol_protocol_document_document_id_fkey;
UPDATE protocol_protocol_document SET document_id = (SELECT id FROM archive_document_file WHERE archive_document = document_id);
ALTER TABLE protocol_protocol_document ADD CONSTRAINT protocol_protocol_document_file_document_id_fkey  FOREIGN KEY (document_id)
      REFERENCES archive_document_file (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE;

ALTER TABLE archive_class ADD COLUMN editable BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE audit.archive_class ADD COLUMN editable BOOLEAN;

INSERT INTO archive_class (id, name, code, editable, format, action, description) VALUES (2, 'Protocollo', 'PRT', false, 'ALL', 'N', 'Protocollo');
INSERT INTO archive_class_step (archive_class, sort, protocol)  VALUES (2, 1, true);

INSERT INTO archive_class (id, name, code, editable, format, action, description) VALUES (3, 'Albo', 'ALB', false, 'ALL', 'N', 'Albo');
INSERT INTO archive_class_step (archive_class, sort, albo)  VALUES (3, 1, true);

INSERT INTO archive_class (id, name, code, editable, format, action, description) VALUES (4, 'Trasparenza', 'TSP', false, 'ALL', 'N', 'Trasparenza');
INSERT INTO archive_class_step (archive_class, sort, trasparenza)  VALUES (4, 1, true);


-- TRASPARENZA
ALTER TABLE trasparenza_voice_document DROP CONSTRAINT trasparenza_voice_document_document_id_fkey;
UPDATE trasparenza_voice_document SET document_id = (SELECT id FROM archive_document_file WHERE archive_document = document_id);
ALTER TABLE trasparenza_voice_document ADD CONSTRAINT trasparenza_voice_document_document_id_fkey  FOREIGN KEY (document_id)
      REFERENCES archive_document_file (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE;

-- ALBO
ALTER TABLE albo_publication_document DROP CONSTRAINT albo_publication_document_document_id_fkey;
UPDATE albo_publication_document SET document_id = (SELECT id FROM archive_document_file WHERE archive_document = document_id);
ALTER TABLE albo_publication_document ADD CONSTRAINT albo_publication_document_document_id_fkey  FOREIGN KEY (document_id)
      REFERENCES archive_document_file (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE;


-- DROP TABLE core_forced_action;

CREATE TABLE core_forced_action
(
    id serial PRIMARY KEY,
    date TIMESTAMP,
    section character varying(16),
    code character varying(32),
    description character varying(255),
    "table" character varying(32),
    id_value integer
);

ALTER TABLE audit.archive_document ALTER COLUMN class_id DROP NOT NULL;
ALTER TABLE archive_document ALTER COLUMN class_id DROP NOT NULL;

ALTER TABLE archive_document ADD COLUMN class_name CHARACTER VARYING (30);
ALTER TABLE audit.archive_document ADD COLUMN class_name CHARACTER VARYING (30);
-- Fix data
UPDATE archive_document SET class_name = (SELECT name FROM archive_class WHERE id=archive_document.class_id);


-- Add step for existing archive document fixed as protocol on all user
INSERT INTO archive_document_step (archive_document, sort, protocol) (SELECT id ,1, true FROM archive_document);

-- AUDIT TABLES
CREATE TABLE audit.archive_mail_security (
    op_action character varying(1) NOT NULL,    
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id  BIGINT,
	"name" character varying(8)
);

CREATE TRIGGER archive_mail_security_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_security FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail_protocol (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id BIGINT,
	"name" character varying(8)
);

CREATE TRIGGER archive_mail_protocol_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_protocol FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail_account (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id  BIGINT,	
	"name" character varying(255),
	protocol integer,
	host character varying(255),
	port integer,
	"security" integer,
	username character varying(255),
	"password" character varying(255),
	active boolean	
);

CREATE TRIGGER archive_mail_account_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_account FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail (    
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id BIGINT,
	account integer,
	"date" TIMESTAMP,  
	"from" text,
	"to" text,
	cc text,
	ccn text,
	subject text,
	message text,
	"raw" text,
    deleted boolean 
);

CREATE TRIGGER archive_mail_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail_attachment (	
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id BIGINT,
    mail integer,
    account INTEGER,	
    "name" character varying(255),
    path character varying(255)
);

CREATE TRIGGER archive_mail_attachment_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_attachment FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_class_step
(
  op_action character varying(1) NOT NULL,
  op_date timestamp with time zone NOT NULL DEFAULT now(),
  id  BIGINT,
  archive_class integer,
  sort integer,
  user_id integer,  
  protocol boolean,
  albo boolean,
  trasparenza boolean,
  sign boolean
); 

CREATE TRIGGER archive_class_step_audit AFTER INSERT OR UPDATE OR DELETE ON archive_class_step FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_document_step
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id BIGINT,
    archive_document integer,
    sort integer,    
    user_id integer,
    protocol boolean,
    albo boolean,
    trasparenza boolean,
    sign boolean
);

CREATE TRIGGER archive_document_step_audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_step FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_document_file
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id serial NOT NULL,
    archive_document INTEGER,
    filename text NOT NULL,           
    filetype text NOT NULL,    
    path character varying(255),
    size integer    
);

CREATE TRIGGER archive_document_file_audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_file FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.core_forced_action
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id BIGINT,
    date TIMESTAMP,
    section character varying(16),
    code character varying(32),
    description character varying(255),
    "table" character varying(32),
    id_value integer
);

CREATE TRIGGER core_forced_action_audit AFTER INSERT OR UPDATE OR DELETE ON core_forced_action FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Permissions change
UPDATE auth_section SET title ='Segreteria Digitale' WHERE id = 11;
UPDATE auth_permission SET title = replace(title, 'Documenti |','Segreteria Digitale |') WHERE id in (500,501, 502, 503, 504, 520, 530, 540);
UPDATE auth_permission SET title = replace(title, 'Archiviati','Archivio') WHERE id in (530);

-- Add type (out/in) mail account
ALTER TABLE archive_mail_account ADD COLUMN "out" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.archive_mail_account ADD COLUMN "out" BOOLEAN NOT NULL DEFAULT false;

CREATE TABLE archive_mail_document (
	id serial PRIMARY KEY NOT NULL,
	mail INTEGER NOT NULL,
	account INTEGER NOT NULL,
	document INTEGER NOT NULL,
	CONSTRAINT archive_mail_document_mail_fkey FOREIGN KEY (mail, account)
	      REFERENCES archive_mail (id, account),	
	CONSTRAINT archive_mail_document_document_fkey FOREIGN KEY (document)
	      REFERENCES archive_document (id)
);

ALTER TABLE archive_mail ALTER COLUMN raw DROP NOT NULL;
ALTER TABLE audit.archive_mail ALTER COLUMN raw DROP NOT NULL;

ALTER TABLE archive_mail_attachment DROP CONSTRAINT archive_mail_attachment_pkey;
ALTER TABLE archive_mail_attachment ADD CONSTRAINT archive_mail_attachment_pkey PRIMARY KEY (id);

ALTER TABLE archive_document DROP COLUMN token;
ALTER TABLE audit.archive_document DROP COLUMN token;

ALTER TABLE archive_document_file ADD COLUMN token character varying(255);
ALTER TABLE audit.archive_document_file ADD COLUMN token character varying(255);

UPDATE parameter set value = 'https://conservazionecl.infocert.it/ws' where name = 'ARCHIVE_API_URL'; 

ALTER TABLE archive_class_step ADD COLUMN archive BOOLEAN DEFAULT false;
ALTER TABLE audit.archive_class_step ADD COLUMN archive BOOLEAN DEFAULT false;

ALTER TABLE archive_document_step ADD COLUMN archive BOOLEAN DEFAULT false;
ALTER TABLE audit.archive_document_step ADD COLUMN archive BOOLEAN DEFAULT false;

ALTER TABLE archive_document ADD COLUMN action_archive BOOLEAN DEFAULT false;
ALTER TABLE audit.archive_document ADD COLUMN action_archive BOOLEAN DEFAULT false;

UPDATE archive_document SET action_archive_date = NULL;

    UPDATE archive_class set code=id::text;
    ALTER TABLE archive_class ADD UNIQUE (code);

--
-- Aggiunta campo subject_seat alla view
--
DROP VIEW ccp_view_payment;

CREATE OR REPLACE VIEW ccp_view_payment AS 
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_seat, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_seat, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


-- ADD PARAMETER FOR INFOCERT BUCKET 3.3.10
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_BUCKET', '');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_POLICY', '');

DROP TABLE audit.archive_metadata;
DROP TABLE archive_metadata;

CREATE TABLE archive_metadata
(
  id serial NOT NULL,
  name character varying(30) NOT NULL,
  slug character varying(30) NOT NULL,
  required boolean NOT NULL DEFAULT false,
  kind character(1) NOT NULL DEFAULT 'S'::bpchar, -- Metadata type, possible values: (D)ate, (I)nteger, (S)tring
  remote_class character varying(30) NOT NULL,
  CONSTRAINT archive_metadata_pkey PRIMARY KEY (id),
  CONSTRAINT archive_metadata_remote_class_fkey FOREIGN KEY (remote_class)
      REFERENCES archive_class (code) MATCH SIMPLE
      ON UPDATE CASCADE ON DELETE SET NULL
);

COMMENT ON TABLE archive_metadata
  IS 'Metadata for a document, bound to specific document type';
COMMENT ON COLUMN archive_metadata.kind IS 'Metadata type, possible values: (D)ate, (I)nteger, (S)tring';



UPDATE archive_class set code = 'ALB' WHERE name = 'Albo' and editable=false;
UPDATE archive_class set code = 'TRP' WHERE name = 'Trasparenza' and editable=false;
UPDATE archive_class set code = 'PRT' WHERE name = 'Protocollo' and editable=false;

CREATE TABLE audit.archive_mail_document
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id serial NOT NULL,
    mail character varying(32) NOT NULL,
    account integer NOT NULL,
    document integer NOT NULL  
);

CREATE TRIGGER archive_mail_document_audit
  AFTER INSERT OR UPDATE OR DELETE
  ON archive_mail_document
  FOR EACH ROW
  EXECUTE PROCEDURE audit.process_audit();

ALTER TABLE archive_mail_attachment DROP CONSTRAINT archive_mail_attachment_mail_fkey;
ALTER TABLE archive_mail_document DROP CONSTRAINT archive_mail_document_mail_fkey;
ALTER TABLE archive_mail DROP CONSTRAINT archive_mail_pkey;

ALTER TABLE archive_mail ALTER COLUMN id TYPE character varying(32);
ALTER TABLE archive_mail_document ALTER COLUMN mail TYPE character varying(32);
ALTER TABLE archive_mail_attachment ALTER COLUMN mail TYPE character varying(32);
ALTER TABLE audit.archive_mail ALTER COLUMN id TYPE character varying(32);
ALTER TABLE audit.archive_mail_document ALTER COLUMN mail TYPE character varying(32);
ALTER TABLE audit.archive_mail_attachment ALTER COLUMN mail TYPE character varying(32);

ALTER TABLE archive_mail ALTER COLUMN id DROP DEFAULT;


UPDATE archive_mail SET id = md5(id);
UPDATE archive_mail_document SET mail = md5(mail);
UPDATE archive_mail_attachment SET mail = md5(mail);

ALTER TABLE archive_mail ADD PRIMARY KEY (id, account);
ALTER TABLE archive_mail_document ADD CONSTRAINT archive_mail_document_mail_fkey FOREIGN KEY (mail, account) REFERENCES archive_mail (id, account) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE archive_mail_attachment ADD CONSTRAINT archive_mail_attachment_mail_fkey FOREIGN KEY (mail, account) REFERENCES archive_mail (id, account) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION;



ALTER TABLE archive_class ALTER COLUMN code DROP NOT NULL;
ALTER TABLE audit.archive_class ALTER COLUMN code DROP NOT NULL;

ALTER TABLE archive_class DROP CONSTRAINT IF EXISTS archive_class_code_uid;

DELETE FROM archive_metadata;

CREATE TABLE archive_remote_class (
	code CHARACTER VARYING (64) NOT NULL PRIMARY KEY,
	name CHARACTER VARYING (255) NOT NULL	
);

INSERT INTO archive_remote_class (code, name) VALUES ('cad_regprot', 'Registro di protocollo');
INSERT INTO archive_remote_class (code, name) VALUES ('ae_gen', 'Documento generico');

ALTER TABLE archive_metadata DROP CONSTRAINT archive_metadata_remote_class_fkey;
ALTER TABLE archive_metadata ADD CONSTRAINT archive_metadata_archive_remote_class_fkey FOREIGN KEY (remote_class) REFERENCES archive_remote_class (code) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE CASCADE;

INSERT INTO archive_metadata (name, slug, required, kind, remote_class) VALUES ('Data documento','__data_documento_dt',true, 'D', 'ae_gen');
INSERT INTO archive_metadata (name, slug, required, kind, remote_class) VALUES ('Data documento','__data_documento_dt',true, 'D', 'cad_regprot');
INSERT INTO archive_metadata (name, slug, required, kind, remote_class) VALUES ('Oggetto','oggetto_s',true, 'S', 'cad_regprot');
  
ALTER TABLE archive_document ADD COLUMN remote_class_code CHARACTER VARYING(64);
ALTER TABLE audit.archive_document ADD COLUMN remote_class_code CHARACTER VARYING(64);

ALTER TABLE archive_mail_document DROP CONSTRAINT archive_mail_document_document_fkey;
ALTER TABLE archive_mail_document ADD CONSTRAINT archive_mail_document_document_fkey FOREIGN KEY (document) REFERENCES archive_document (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE CASCADE;



ALTER TABLE ccp_movement ADD COLUMN tmp_number text;
ALTER TABLE audit.ccp_movement ADD COLUMN tmp_number text;

UPDATE ccp_movement SET note = '' WHERE note IS NULL;
UPDATE ccp_movement SET tmp_number = "number", note = note || ' ### Vecchio numero: ' || "number" WHERE id in (SELECT id FROM ccp_movement WHERE "number" !~ '^[0-9]+$' or char_length(number) > 9);
UPDATE ccp_movement SET "number" = NULL WHERE id in (SELECT id FROM ccp_movement WHERE "number" !~ '^[0-9]+$' or char_length(number) > 9);
UPDATE ccp_movement SET note = NULL WHERE note = '';


DROP VIEW ccp_view_movement;
DROP VIEW ccp_view_payment;

ALTER TABLE ccp_movement ALTER COLUMN "number" TYPE INTEGER USING ("number"::numeric);

CREATE OR REPLACE VIEW ccp_view_movement AS
 SELECT mv.id, mv.type_id, mv.subject_type, mv.subject_id, mv.miscellaneous, mv.number, mv.note, mv.school_year, mv.subject_data, mv.subject_seat, mv.subject_class, mv.amount::numeric(14,2) AS amount, mv.creation_date, mv.expiration_date, mv.type_text, mv.incoming, mv.category_id, mv.category_text, mv.total_payments, mv.count_payments, mv.positive_additionals_euro, mv.negative_additionals_euro, mv.positive_additionals_perc, mv.negative_additionals_perc, mv.count_additionals, mv.linked_additionals, mv.linked_payments, (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT m.id, m.type_id, m.subject_type, m.subject_id, m.miscellaneous, m.number, m.note, m.school_year, m.subject_data, m.subject_seat, m.subject_class, m.amount, m.creation_date, m.expiration_date, t.name AS type_text, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments, ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals, ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals, ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m, ccp_type t, ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;

CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_seat, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_seat, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


INSERT INTO parameter (name, value) VALUES ('ARCHIVE_MAIL_SYNC_ENABLED', 't');

CREATE INDEX personnel_timetable_employee_id_idx ON personnel_timetable (employee_id);

ALTER TABLE archive_document_file ADD COLUMN metadata TEXT;
ALTER TABLE audit.archive_document_file ADD COLUMN metadata TEXT;



DELETE FROM archive_remote_class;
-- Codici/Nomi classi remote + metadati
INSERT INTO archive_remote_class (code, name) VALUES ('ae_ddta' , 'DDT emesso');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ddt', '__numero_documento_l', 'D', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione', 'denominazione_s', 'S', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale', 'codice_fiscale_s', 'S', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Partita IVA', 'partita_iva_s', 'S', 'ae_ddta', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_ddtp' , 'DDT ricevuto');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione', 'denominazione_s', 'S', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale', 'codice_fiscale_s', 'S', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Partita IVA', 'partita_iva_s', 'S', 'ae_ddtp', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_lgio' , 'Libro giornale');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data prima registrazione', 'data_inizio_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lgio', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_lmas' , 'Libro mastro');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lmas', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_lces' , 'Libro cespiti');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data prima registrazione', 'data_inizio_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lces', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_linv' , 'Libro inventari');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_linv', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('fatp_pa' , 'Fattura elettronica PA passiva');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale emittente', 'codice_fiscale_emittente_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale', 'codice_fiscale_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione emittente', 'denominazione_emittente_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione', 'denominazione_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Identificativo SDI', 'identificativoSdi_s', 'N', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero documento/fattura', 'numero_documento_s', 'N', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Partita IVA emittente', 'partita_iva_emittente_s', 'S', 'fatp_pa', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('notifica_pa_pass' , 'Notifica fattura elettronica PA passiva');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'notifica_pa_pass', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Identificativo SDI', 'identificativoSdi_s', 'N', 'notifica_pa_pass', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('cad_contratto' , 'Contratto');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Contraente', 'contraente_s', 'S', 'cad_contratto', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'cad_contratto', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Oggetto del contratto', 'oggetto_s', 'S', 'cad_contratto', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero di repertorio/registro', 'repertorio_i', 'N', 'cad_contratto', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('cad_regprot' , 'Registro giornaliero di protocollo');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data di chiusura', '__data_documento_dt', 'D', 'cad_regprot', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Oggetto', 'oggetto_s', 'S', 'cad_regprot', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', 'prot_fine_i', 'N', 'cad_regprot', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', 'prot_inizio_i', 'N', 'cad_regprot', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('cad_docprot' , 'Documento protocollato');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'cad_docprot', 't');

ALTER TABLE albo_publication ALTER COLUMN title TYPE text;
ALTER TABLE audit.albo_publication ALTER COLUMN title TYPE text;