ALTER TABLE ccp_credits_type ADD COLUMN ccp_payment_method INTEGER; 
ALTER TABLE audit.ccp_credits_type ADD COLUMN ccp_payment_method INTEGER; 

ALTER TABLE ccp_credits_type
    ADD CONSTRAINT fk_ccp_payment_method FOREIGN KEY (ccp_payment_method) REFERENCES ccp_payment_method (id);


ALTER TABLE ccp_credits_type ADD COLUMN show_on_site BOOLEAN DEFAULT false; 
ALTER TABLE ccp_credits_type ALTER COLUMN show_on_site SET NOT NULL;
ALTER TABLE audit.ccp_credits_type ADD COLUMN show_on_site BOOLEAN; 