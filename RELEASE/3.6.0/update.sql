CREATE TABLE archive_document_model (
    id SERIAL primary key,
    name <PERSON><PERSON><PERSON><PERSON><PERSON> VARYING(255) NOT NULL,
    template TEXT 
);

CREATE TABLE audit.archive_document_model (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id INTEGER,
    name <PERSON><PERSON>AC<PERSON>R VARYING(255),
    template TEXT 
);

CREATE TRIGGER archive_document_model_audit AFTER INSERT OR DELETE OR UPDATE ON archive_document_model FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

ALTER TABLE archive_document ADD COLUMN model INTEGER REFERENCES archive_document_model (id);
ALTER TABLE audit.archive_document ADD COLUMN model INTEGER;

ALTER TABLE archive_document ADD COLUMN expiration_date TIMESTAMP;
ALTER TABLE audit.archive_document ADD COLUMN expiration_date TIMESTAMP;

ALTER TABLE archive_document ADD COLUMN dossier CHARACTER VARYING(255);
ALTER TABLE audit.archive_document ADD COLUMN dossier CHARACTER VARYING(255);


INSERT INTO archive_mail_security (id, name) VALUES (3, 'nessuna');
SELECT pg_catalog.setval('archive_mail_security_id_seq', 3, true);

DROP INDEX IF EXISTS archive_mail_date_idx;
DROP INDEX IF EXISTS archive_mail_deleted_idx;
CREATE INDEX ON archive_mail (date);
CREATE INDEX ON archive_mail (deleted);

select  setval('archive_class_id_seq', (SELECT max(id) from archive_class));

INSERT INTO archive_origin (id, name, code, description) VALUES (3, 'Mastercom', 'mc', 'Documenti inviati dal mastercom');
INSERT INTO archive_class (name, code, description, editable, format, action) VALUES ('Pagelle Mastercom', 'PAGMC', 'Pagelle caricate dal Mastercom', 'f', 'ALL', 'N');
INSERT INTO archive_class_step (archive_class, sort, sign, archive) VALUES ((SELECT id FROM archive_class ORDER BY id DESC LIMIT 1), 1, 't', 't');


select setval('auth_permission_id_seq', (SELECT max(id) FROM auth_permission)) ;
select setval('auth_element_id_seq', (SELECT max(id) FROM auth_element)) ;

INSERT INTO auth_permission (title, super_user, auth_section) VALUES ('Segreteria Digitale | In entrata | Visualizzare', 'f', 11);
INSERT INTO auth_element (name,control_interface,auth_permission,state) VALUES ('ArchiveIncomingTab', 'ArchiveMainPnl', 751, 'hide');
INSERT INTO auth_element (name,control_interface,auth_permission,state) VALUES ('ArchiveIncomingPnl', 'ArchiveMainPnl', 751, 'hide');



CREATE TABLE ccp_category_banks
(
    "category_id" INTEGER NOT NULL,
    "bank_id" INTEGER NOT NULL,
    "initial_balance" DOUBLE PRECISION DEFAULT 0.00 NOT NULL,
    PRIMARY KEY ("category_id","bank_id")
);

CREATE TABLE audit.ccp_category_banks
(
    "op_action" character varying(1) NOT NULL,
    "op_date" timestamp with time zone NOT NULL DEFAULT now(),
    "category_id" INTEGER,
    "bank_id" INTEGER,
    "initial_balance" DOUBLE PRECISION
);

CREATE TRIGGER ccp_category_banks_audit AFTER INSERT OR UPDATE OR DELETE ON ccp_category_banks FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


INSERT INTO ccp_category_banks
(
    SELECT c.id category_id, b.id bank_id
        FROM 
            ccp_category c 
        RIGHT JOIN 
            core_bank_account b 
            ON b.id > 0 
    ORDER BY c.id,b.id
);

ALTER TABLE archive_document_file ADD COLUMN external_data TEXT;
ALTER TABLE audit.archive_document_file ADD COLUMN external_data TEXT;

