
-- Print category

CREATE TABLE ccp_print_category (
    id serial NOT NULL,
    name character varying(255) NOT NULL,
    ordering INTEGER NOT NULL,
    CONSTRAINT ccp_print_category_pkey PRIMARY KEY (id)
);

CREATE TABLE ccp_print_category_movement_type (
    id serial NOT NULL,
    ccp_print_category_id integer NOT NULL,
    ccp_type_id integer NOT NULL,
    CONSTRAINT ccp_print_category_movement_type_pkey PRIMARY KEY (id),
    CONSTRAINT ccp_print_category_movement_type_unique UNIQUE (ccp_print_category_id, ccp_type_id),
    CONSTRAINT ccp_print_category_movement_type_ccp_print_category_id_fkey FOREIGN KEY (ccp_print_category_id)
        REFERENCES ccp_print_category (id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE,
    CONSTRAINT ccp_print_category_movement_type_ccp_type_id_fkey FOREIGN KEY (ccp_type_id)
        REFERENCES ccp_type (id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE
);
    
