-- Table: public.ccp_payment_intents

-- DROP TABLE public.ccp_payment_intents;

CREATE TABLE public.ccp_payment_intents
(
    id bigint NOT NULL,
    token character varying(255) COLLATE pg_catalog."default",
    payment_id bigint DEFAULT 0,
    payment_object_type character varying(20) COLLATE pg_catalog."default" DEFAULT ''::character varying,
    payment_object_id bigint DEFAULT 0,
    payment_status character varying(255) COLLATE pg_catalog."default",
    charge_status character varying(20) COLLATE pg_catalog."default" DEFAULT ''::character varying,
    amount numeric(15,2) DEFAULT 0,
    payer_type character varying(20) COLLATE pg_catalog."default" DEFAULT ''::character varying,
    payer_id character varying(30) COLLATE pg_catalog."default" DEFAULT ''::character varying,
    school_year character varying(9) COLLATE pg_catalog."default" DEFAULT ''::character varying,
    subject_id bigint DEFAULT 0,
    date_created timestamp with time zone,
    date_succeeded timestamp with time zone,
    date_canceled timestamp with time zone,
    date_failed timestamp with time zone,
    date_charged timestamp with time zone,
    CONSTRAINT ccp_payment_intents_pkey PRIMARY KEY (id)
);

COMMENT ON COLUMN public.ccp_payment_intents.token
    IS 'payment intent token di stripe';

COMMENT ON COLUMN public.ccp_payment_intents.date_created
    IS 'data creazione dell''intent';

COMMENT ON COLUMN public.ccp_payment_intents.payment_object_id
    IS 'Id dell''articolo in pagamento';

COMMENT ON COLUMN public.ccp_payment_intents.payment_status
    IS 'stato Stripe del tentativo di pagamento';

COMMENT ON COLUMN public.ccp_payment_intents.payer_type
    IS 'tipo utente pagante';

COMMENT ON COLUMN public.ccp_payment_intents.payer_id
    IS 'Id dell''utente pagante';

COMMENT ON COLUMN public.ccp_payment_intents.subject_id
    IS 'id studente';

COMMENT ON COLUMN public.ccp_payment_intents.date_succeeded
    IS 'data evento succeeded';

COMMENT ON COLUMN public.ccp_payment_intents.charge_status
    IS 'stato stripe dell''addebito ';

COMMENT ON COLUMN public.ccp_payment_intents.payment_id
    IS 'Id del pagamento generato';
