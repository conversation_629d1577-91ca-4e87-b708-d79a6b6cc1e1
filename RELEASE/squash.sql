-- VERSION 3.2.1
-- Ccp Receipt
CREATE TABLE ccp_receipt
(
   id serial NOT NULL,
   number integer NOT NULL,
   date bigint NOT NULL,
   CONSTRAINT ccp_receipt_pkey PRIMARY KEY (id),
   CONSTRAINT ccp_receipt_number_key UNIQUE (number)
);

COMMENT ON TABLE ccp_receipt IS 'A receipt containing payments';

CREATE TABLE audit.ccp_receipt
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   number integer NOT NULL,
   date bigint NOT NULL
);

CREATE TRIGGER ccp_receipt_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_receipt
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Update to Ccp Payment schema
ALTER TABLE ccp_payment ADD COLUMN receipt_id INTEGER;
ALTER TABLE audit.ccp_payment ADD COLUMN receipt_id INTEGER;

ALTER TABLE ccp_payment ADD CONSTRAINT ccp_payment_receipt_id_fkey FOREIGN KEY (receipt_id) REFERENCES ccp_receipt (id) ON UPDATE CASCADE ON DELETE SET NULL;

-- Update to Ccp Movement schema
ALTER TABLE ccp_movement ADD COLUMN expiration_date BIGINT;
ALTER TABLE audit.ccp_movement ADD COLUMN expiration_date BIGINT;

ALTER TABLE ccp_movement RENAME date TO creation_date;
ALTER TABLE audit.ccp_movement RENAME date TO creation_date;

ALTER TABLE ccp_movement RENAME anno_scolastico TO school_year;
ALTER TABLE audit.ccp_movement RENAME anno_scolastico TO school_year;

-- Update Ccp Type schema
ALTER TABLE audit.ccp_type ADD COLUMN incoming BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE ccp_type ADD COLUMN incoming BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE audit.ccp_type ALTER COLUMN incoming DROP DEFAULT;

ALTER TABLE ccp_type ALTER COLUMN expiration_date DROP NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN expiration_date DROP NOT NULL;

UPDATE ccp_type SET expiration_date = NULL WHERE expiration_date = 0;

INSERT INTO ccp_type (name, category_id, incoming) SELECT 'Prelievo - ' || name, id, false FROM ccp_category;
UPDATE ccp_movement AS m SET type_id = (SELECT id FROM ccp_type AS t WHERE t.category_id = m.exit_category_id AND t.incoming = false AND name LIKE 'Prelievo - %') WHERE incoming = false;

ALTER TABLE ccp_movement DROP COLUMN exit_category_id;
ALTER TABLE ccp_movement DROP COLUMN incoming;
ALTER TABLE audit.ccp_movement DROP COLUMN exit_category_id;
ALTER TABLE audit.ccp_movement DROP COLUMN incoming;

-- Update to Ccp Payment
CREATE TABLE ccp_payment_additional
(
   payment_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL DEFAULT 0.00,
   CONSTRAINT ccp_payment_additional_pkey PRIMARY KEY (payment_id, additional_id),
   CONSTRAINT ccp_payment_additional_payment_id_fkey FOREIGN KEY (payment_id) REFERENCES ccp_payment (id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT ccp_payment_additional_additional_id_fkey FOREIGN KEY (additional_id) REFERENCES ccp_additional (id) ON UPDATE CASCADE ON DELETE NO ACTION,
   CONSTRAINT ccp_payment_additional_amount_check CHECK (amount >= 0)
);

CREATE INDEX ccp_payment_additional_payment_id_fki ON ccp_payment_additional USING btree (payment_id);
CREATE INDEX ccp_payment_additional_additional_id_fki ON ccp_payment_additional USING btree (additional_id);

CREATE TABLE audit.ccp_payment_additional
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   payment_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL
);

CREATE TRIGGER ccp_payment_additional_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_payment_additional
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Update to Ccp Additionals
ALTER TABLE audit.ccp_additional ADD COLUMN payment BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE ccp_additional ADD COLUMN payment BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.ccp_additional ALTER COLUMN payment DROP DEFAULT;

-- CCP Movement view

--DROP VIEW ccp_view_movement;

CREATE OR REPLACE VIEW ccp_view_movement AS
 SELECT mv.id, mv.type_id, mv.subject_type, mv.subject_id, mv.miscellaneous, mv.number, mv.note, mv.school_year, mv.subject_data, mv.subject_seat, mv.subject_class, mv.amount::numeric(14,2) AS amount, mv.creation_date, mv.expiration_date, mv.type_text, mv.incoming, mv.category_id, mv.category_text, mv.total_payments, mv.count_payments, mv.positive_additionals_euro, mv.negative_additionals_euro, mv.positive_additionals_perc, mv.negative_additionals_perc, mv.count_additionals, mv.linked_additionals, mv.linked_payments, (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT m.id, m.type_id, m.subject_type, m.subject_id, m.miscellaneous, m.number, m.note, m.school_year, m.subject_data, m.subject_seat, m.subject_class, m.amount, m.creation_date, m.expiration_date, t.name AS type_text, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments, ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals, ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals, ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m, ccp_type t, ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;

-- View: ccp_view_movement_category

-- DROP VIEW ccp_view_movement_category;

CREATE OR REPLACE VIEW ccp_view_movement_category AS
 SELECT mm.year, mm.category_id AS id, mm.category_text AS text, sum(mm.total_positive) AS positive, sum(mm.total_negative) AS negative, sum(mm.total) AS balance
   FROM ( SELECT date_part('year'::text, to_timestamp(mv.creation_date::double precision)) AS year, mv.category_id, mv.category_text, mv.incoming,
                CASE
                    WHEN mv.incoming = true THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN mv.incoming = false THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) * (-1)::numeric
                    ELSE 0::numeric(14,2)
                END AS total_negative,
                CASE
                    WHEN mv.incoming = true THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) * (-1)::numeric
                END AS total
           FROM ( SELECT m.amount, m.creation_date, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                   FROM ccp_movement m, ccp_type t, ccp_category c
                  WHERE m.type_id = t.id AND t.category_id = c.id) mv) mm
  GROUP BY mm.year, mm.category_id, mm.category_text
  ORDER BY mm.year DESC, mm.category_text;

-- Bank Profile updates
ALTER TABLE bank_profile ADD COLUMN initial_balance double precision NOT NULL DEFAULT 0.00;
ALTER TABLE bank_profile ADD COLUMN type character varying(1) NOT NULL DEFAULT 'B';

ALTER TABLE bank_profile ADD CONSTRAINT bank_profile_type_check CHECK (type::text = ANY (ARRAY['P'::character varying::text, 'B'::character varying::text]));


-- Fix CCP Permissions
UPDATE auth_section SET title = 'Conti Correnti' WHERE id = 8;

UPDATE auth_permission SET title = 'Conti Correnti | Attivo' WHERE id = 21;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Aggiungere' WHERE id = 200;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Modificare' WHERE id = 201;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Eliminare' WHERE id = 202;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Copiare' WHERE id = 203;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Stampe singole' WHERE id = 204;

UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Visualizzare' WHERE id = 210;
UPDATE auth_permission SET title = 'Conti Correnti | Bollettini | Visualizzare' WHERE id = 211;
UPDATE auth_permission SET title = 'Conti Correnti | Stampe | Visualizzare' WHERE id = 212;

UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Aggiungere' WHERE id = 220;
UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Modificare' WHERE id = 221;
UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Eliminare' WHERE id = 222;

UPDATE auth_element SET name = 'CcpMovementNewBtn', control_interface = 'CcpMovementsPnl' WHERE id = 200;
UPDATE auth_element SET name = 'contextCcpMovementEdit', control_interface = 'CcpMovementEditMn' WHERE id = 201;
UPDATE auth_element SET name = 'contextCcpMovementDelete', control_interface = 'CcpMovementEditMn' WHERE id = 202;
UPDATE auth_element SET name = 'contextCcpMovementCopy', control_interface = 'CcpMovementEditMn' WHERE id = 203;
UPDATE auth_element SET name = 'contextCcpMovementPrints', control_interface = 'CcpMovementEditMn' WHERE id = 204;

UPDATE auth_element SET name = 'CcpTypeBtn', control_interface = 'CcpToolbar' WHERE id = 210;

UPDATE auth_element SET name = 'CcpTypeNewBtn', control_interface = 'CcpTypesWin' WHERE id = 220;
UPDATE auth_element SET name = 'contextCcpTypeEdit', control_interface = 'CcpTypeEditMn' WHERE id = 221;
UPDATE auth_element SET name = 'contextCcpTypeDelete', control_interface = 'CcpTypeEditMn' WHERE id = 222;


-- New CCP Permissions
-- Categories
INSERT INTO auth_permission (id, title, auth_section) VALUES (230, 'Conti Correnti | Categorie | Visualizzare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (230, 'CcpCategoryBtn', 'CcpToolbar', 230);
INSERT INTO auth_path (id, path, auth_permission) VALUES (230, '', 230);

INSERT INTO auth_permission (id, title, auth_section) VALUES (231, 'Conti Correnti | Categorie | Aggiungere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (231, 'CcpCategoryNewBtn', 'CcpCategoriesToolbar', 231);
INSERT INTO auth_path (id, path, auth_permission) VALUES (231, '', 231);

INSERT INTO auth_permission (id, title, auth_section) VALUES (232, 'Conti Correnti | Categorie | Modificare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (232, 'contextCcpCategoryEdit', 'CcpCategoryEditMn', 232);
INSERT INTO auth_path (id, path, auth_permission) VALUES (232, '', 232);

INSERT INTO auth_permission (id, title, auth_section) VALUES (233, 'Conti Correnti | Categorie | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (233, 'contextCcpCategoryDelete', 'CcpCategoryEditMn', 233);
INSERT INTO auth_path (id, path, auth_permission) VALUES (233, '', 233);

-- Additionals
INSERT INTO auth_permission (id, title, auth_section) VALUES (240, 'Conti Correnti | Addizionali | Visualizzare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (240, 'CcpAdditionalBtn', 'CcpToolbar', 240);
INSERT INTO auth_path (id, path, auth_permission) VALUES (240, '', 240);

INSERT INTO auth_permission (id, title, auth_section) VALUES (241, 'Conti Correnti | Addizionali | Aggiungere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (241, 'CcpAdditionalNewBtn', 'CcpAdditionalsToolbar', 241);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (223, 'CcpTypeEditNewAdditionalBtn', 'CcpTypeEditAdditionalsToolbar', 241);
INSERT INTO auth_path (id, path, auth_permission) VALUES (241, '', 241);
INSERT INTO auth_path (id, path, auth_permission) VALUES (223, '', 241);

INSERT INTO auth_permission (id, title, auth_section) VALUES (242, 'Conti Correnti | Addizionali | Modificare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (242, 'contextCcpAdditionalEdit', 'CcpAdditionalEditMn', 242);
INSERT INTO auth_path (id, path, auth_permission) VALUES (242, '', 242);

INSERT INTO auth_permission (id, title, auth_section) VALUES (243, 'Conti Correnti | Addizionali | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (243, 'contextCcpAdditionalDelete', 'CcpAdditionalEditMn', 243);
INSERT INTO auth_path (id, path, auth_permission) VALUES (243, '', 243);

-- Residuals
INSERT INTO auth_permission (id, title, auth_section) VALUES (250, 'Conti Correnti | Residui | Visualizzare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (250, 'CcpResidualBtn', 'CcpToolbar', 250);
INSERT INTO auth_path (id, path, auth_permission) VALUES (250, '', 250);

-- Payments
INSERT INTO auth_permission (id, title, auth_section) VALUES (261, 'Conti Correnti | Pagamenti | Aggiungere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (261, 'CcpPaymentNewBtn', 'CcpPaymentsToolbar', 261);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (205, 'contextCcpMovementPay', 'CcpMovementEditMn', 261);
INSERT INTO auth_path (id, path, auth_permission) VALUES (261, '', 261);
INSERT INTO auth_path (id, path, auth_permission) VALUES (205, '', 261);

INSERT INTO auth_permission (id, title, auth_section) VALUES (262, 'Conti Correnti | Pagamenti | Modificare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (262, 'contextCcpPaymentEdit', 'CcpPaymentsMn', 262);
INSERT INTO auth_path (id, path, auth_permission) VALUES (262, '', 262);

INSERT INTO auth_permission (id, title, auth_section) VALUES (263, 'Conti Correnti | Pagamenti | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (263, 'contextCcpPaymentDelete', 'CcpPaymentsMn', 263);
INSERT INTO auth_path (id, path, auth_permission) VALUES (263, '', 263);

INSERT INTO auth_permission (id, title, auth_section) VALUES (264, 'Conti Correnti | Pagamenti | Stampe singole', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (264, 'contextCcpPaymentPrints', 'CcpPaymentsMn', 264);
INSERT INTO auth_path (id, path, auth_permission) VALUES (264, '', 264);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (265, 'CcpPaymentEditNewAdditionalBtn', 'CcpPaymentEditAdditionalsToolbar', 241);
INSERT INTO auth_path (id, path, auth_permission) VALUES (265, '', 241);

-- Trigger to delete
CREATE OR REPLACE FUNCTION ccp_receipt_removal_from_payment_delete() RETURNS trigger AS
$BODY$
    BEGIN
        DELETE FROM ccp_receipt WHERE id = OLD.receipt_id;
        RETURN OLD;
    END;
$BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100;

CREATE TRIGGER ccp_receipt_removal_from_payment_delete AFTER DELETE ON ccp_payment
    FOR EACH ROW EXECUTE PROCEDURE ccp_receipt_removal_from_payment_delete();

-- New bank profile fields and fixes
ALTER TABLE bank_profile RENAME TO core_bank_account;

ALTER SEQUENCE bank_profile_id_seq RENAME TO core_bank_account_id_seq;

ALTER TABLE core_bank_account ALTER COLUMN bban SET NOT NULL;
ALTER TABLE core_bank_account ALTER COLUMN denomination SET NOT NULL;
ALTER TABLE core_bank_account ADD COLUMN ise_id integer NOT NULL DEFAULT 0;
ALTER TABLE core_bank_account ADD COLUMN ise_type character varying(1) NOT NULL DEFAULT 'I';

ALTER TABLE core_bank_account DROP CONSTRAINT bank_profile_type_check;
ALTER TABLE core_bank_account ADD CONSTRAINT core_bank_account_type_check CHECK (type::text = ANY (ARRAY['P'::character varying::text, 'B'::character varying::text]));
ALTER TABLE core_bank_account ADD CONSTRAINT core_bank_account_ise_type_check CHECK (ise_type::text = ANY (ARRAY['I'::character varying::text, 'S'::character varying::text, 'E'::character varying::text]));

COMMENT ON COLUMN core_bank_account.type IS 'P - Post, B - Bank';
COMMENT ON COLUMN core_bank_account.ise_type IS 'I - Institute, S - Supplier, E - Employee';

-- Transfer linked institute id from linkking table to bank account table
UPDATE core_bank_account SET ise_id = institute_bank_profile.institute FROM institute_bank_profile WHERE core_bank_account.id = institute_bank_profile.bank_profile;

-- Removal of obsolete table
DROP TABLE institute_bank_profile;

-- Removal of unnecessary fields "default" property
ALTER TABLE core_bank_account ALTER COLUMN type DROP DEFAULT;
ALTER TABLE core_bank_account ALTER COLUMN ise_id DROP DEFAULT;
ALTER TABLE core_bank_account ALTER COLUMN ise_type DROP DEFAULT;

-- Insertion of CCP account
INSERT INTO core_bank_account (denomination, bban, type, ise_id, ise_type) VALUES ('Conto Corrente Postale', COALESCE((SELECT postal_account FROM institute WHERE def = 't' LIMIT 1), 0)::TEXT, 'P', (SELECT institute_id FROM institute WHERE def = 't' LIMIT 1), 'I');

UPDATE ccp_payment set account_id='ccp_0' where account_id is null or account_id='';

-- Fix account_id value to the corresponding real (Integer) id
UPDATE ccp_payment SET account_id = (
    SELECT
        CASE account_id
            WHEN 'ccp_0' THEN
                (SELECT id FROM core_bank_account WHERE denomination = 'Conto Corrente Postale')::text
            ELSE
                substring(account_id FROM (position('_' IN account_id) + 1))
        END
);
ALTER TABLE ccp_payment ALTER COLUMN account_id TYPE integer USING(account_id::integer);

-- Foreign key constraint for payments
ALTER TABLE ccp_payment ADD CONSTRAINT ccp_payment_payment_account_id_fkey FOREIGN KEY (account_id)
    REFERENCES core_bank_account (id) MATCH SIMPLE
    ON UPDATE CASCADE ON DELETE NO ACTION;

-- Audit table
CREATE TABLE audit.core_bank_account
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   country_code character varying(2),
   check_code character varying(2),
   bban character varying(100) NOT NULL,
   denomination character varying(100) NOT NULL,
   initial_balance double precision NOT NULL,
   type character varying(1) NOT NULL,
   ise_id integer NOT NULL,
   ise_type character varying(1) NOT NULL
);

CREATE TRIGGER core_bank_account_audit
    AFTER INSERT OR UPDATE OR DELETE ON core_bank_account
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Triggers to delete a bank account after an ise record deletion
CREATE OR REPLACE FUNCTION core_bank_account_removal_by_ise_delete() RETURNS trigger AS
$BODY$
    BEGIN
        CASE TG_TABLE_NAME
            WHEN 'institute' THEN DELETE FROM core_bank_account WHERE ise_id = OLD.institute_id AND ise_type = 'I';
            WHEN 'wh_suppliers' THEN DELETE FROM core_bank_account WHERE ise_id = OLD.id AND ise_type = 'S';
            WHEN 'employee' THEN DELETE FROM core_bank_account WHERE ise_id = OLD.employee_id AND ise_type = 'E';
        END CASE;
        RETURN OLD;
    END;
$BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100;

CREATE TRIGGER core_bank_account_removal_by_institute_delete AFTER DELETE ON institute
    FOR EACH ROW EXECUTE PROCEDURE core_bank_account_removal_by_ise_delete();

CREATE TRIGGER core_bank_account_removal_by_supplier_delete AFTER DELETE ON wh_suppliers
    FOR EACH ROW EXECUTE PROCEDURE core_bank_account_removal_by_ise_delete();

CREATE TRIGGER core_bank_account_removal_by_employee_delete AFTER DELETE ON employee
    FOR EACH ROW EXECUTE PROCEDURE core_bank_account_removal_by_ise_delete();

-- Payments View
CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.movement_id, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.number AS movement_number, m.subject_type, m.subject_id, m.subject_data, m.type_id, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

-- Receipts View
CREATE OR REPLACE VIEW ccp_view_receipt AS
 SELECT r.id, r.number, r.date, ( SELECT count(ccp_payment.id) AS count
           FROM ccp_payment
          WHERE ccp_payment.receipt_id = r.id) AS count_payments, ( SELECT string_agg(ccp_payment.id::text, ','::text) AS string_agg
           FROM ccp_payment
          WHERE ccp_payment.receipt_id = r.id) AS linked_payments, (( SELECT COALESCE(sum(pay.total)::double precision, 0::double precision) AS sum
           FROM ( SELECT pa.id, pa.receipt_id, (pa.amount::double precision + pa.positive_additionals_euro::double precision - pa.negative_additionals_euro::double precision + pa.amount::double precision / 100::double precision * pa.positive_additionals_perc - pa.amount::double precision / 100::double precision * pa.negative_additionals_perc)::numeric(14,2) AS total
                   FROM ( SELECT p.id, p.receipt_id, p.amount::numeric(14,2) AS amount, (p.payer_surname::text || ' '::text) || p.payer_name::text AS payer_data, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                           FROM ccp_payment p) pa) pay
          WHERE pay.receipt_id = r.id))::numeric(14,2) AS total
   FROM ccp_receipt r;

-- View Payment Account

-- DROP VIEW ccp_view_payment_account;

CREATE OR REPLACE VIEW ccp_view_payment_account AS
 SELECT pp.year, pp.id, pp.text, count(pp.payment_id) AS elements, sum(pp.total_positive) AS credit, sum(pp.total_negative) AS debit
   FROM ( SELECT pv.payment_id, date_part('year'::text, to_timestamp(pv.operation_date::double precision)) AS year, pv.id, pv.text, pv.incoming,
                CASE
                    WHEN pv.incoming = true THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN pv.incoming = false THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_negative
           FROM ( SELECT p.id AS payment_id, p.operation_date, p.accountable_date, p.amount::numeric(14,2) AS amount, p.account_id AS id, a.denomination AS text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_perc, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_perc
                   FROM ccp_payment p, core_bank_account a, ccp_movement m, ccp_type t
                  WHERE p.movement_id = m.id AND m.type_id = t.id AND p.account_id = a.id) pv) pp
  GROUP BY pp.year, pp.id, pp.text
  ORDER BY pp.year DESC, pp.text;

-- View Payment Category

-- DROP VIEW ccp_view_payment_category;

CREATE OR REPLACE VIEW ccp_view_payment_category AS
 SELECT pp.year, pp.id, pp.text, count(pp.payment_id) AS elements, sum(pp.total_positive) AS credit, sum(pp.total_negative) AS debit
   FROM ( SELECT pv.payment_id, date_part('year'::text, to_timestamp(pv.operation_date::double precision)) AS year, pv.id, pv.text, pv.incoming,
                CASE
                    WHEN pv.incoming = true THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN pv.incoming = false THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_negative
           FROM ( SELECT p.id AS payment_id, p.operation_date, p.accountable_date, p.amount::numeric(14,2) AS amount, t.category_id AS id, c.name AS text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_perc, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_perc
                   FROM ccp_payment p, ccp_movement m, ccp_type t, ccp_category c
                  WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id) pv) pp
  GROUP BY pp.year, pp.id, pp.text
  ORDER BY pp.year DESC, pp.text;

-- View Movement Category

DROP VIEW ccp_view_movement_category;

CREATE OR REPLACE VIEW ccp_view_movement_category AS
 SELECT mm.year, mm.id, mm.text, count(mm.movement_id) AS elements, sum(mm.total_positive) AS credit, sum(mm.total_negative) AS debit
   FROM ( SELECT mv.movement_id, date_part('year'::text, to_timestamp(mv.creation_date::double precision)) AS year, mv.id, mv.text, mv.incoming,
                CASE
                    WHEN mv.incoming = true THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN mv.incoming = false THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_negative
           FROM ( SELECT m.id AS movement_id, m.amount, m.creation_date, t.incoming, c.id, c.name AS text, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                   FROM ccp_movement m, ccp_type t, ccp_category c
                  WHERE m.type_id = t.id AND t.category_id = c.id) mv) mm
  GROUP BY mm.year, mm.id, mm.text
  ORDER BY mm.year DESC, mm.text;

-- Fix CCP Permissions
UPDATE auth_element SET control_interface = 'CcpLinkedPaymentsToolbar' WHERE id = 261;
UPDATE auth_element SET control_interface = 'CcpPaymentsPnl' WHERE id = 250;

SELECT * FROM mt_str_replace(cerca := 'Conti Correnti', sostituisci := 'Conti Corrente', tabella := 'auth_permission', campo := 'title', modifica := true) AS (tabella text, colonna text, condizione text, trova text, sostituisci text);

-- New CCP Permissions
-- Payments
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (266, 'contextCcpLinkedPaymentEdit', 'CcpLinkedPaymentsMn', 262);
INSERT INTO auth_path (id, path, auth_permission) VALUES (266, '', 262);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (267, 'contextCcpLinkedPaymentDelete', 'CcpLinkedPaymentsMn', 263);
INSERT INTO auth_path (id, path, auth_permission) VALUES (267, '', 263);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (268, 'contextCcpLinkedPaymentPrints', 'CcpLinkedPaymentsMn', 264);
INSERT INTO auth_path (id, path, auth_permission) VALUES (268, '', 264);

-- Receipts
INSERT INTO auth_permission (id, title, auth_section) VALUES (270, 'Conti Corrente | Ricevute | Emettere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (270, 'CcpReceiptNewBtn', 'CcpReceiptsPnl', 270);
INSERT INTO auth_path (id, path, auth_permission) VALUES (270, '', 270);

INSERT INTO auth_permission (id, title, auth_section) VALUES (271, 'Conti Corrente | Ricevute | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (271, 'contextCcpReceiptDelete', 'CcpReceiptsMn', 271);
INSERT INTO auth_path (id, path, auth_permission) VALUES (271, '', 271);

INSERT INTO auth_permission (id, title, auth_section) VALUES (272, 'Conti Corrente | Ricevute | Stampare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (272, 'contextCcpReceiptPrint', 'CcpReceiptsMn', 272);
INSERT INTO auth_path (id, path, auth_permission) VALUES (272, '', 272);

-- Comment for mt_string_replace function
COMMENT ON FUNCTION mt_str_replace(text, text, text, text, boolean) IS 'Procedura Pg/PLSQL

Descrizione: La funzione effettua un replace di una stringa contenuta in un qualsiasi campo di testo presente in tutte le tabelle del DBMS.
mt_str_replace(cerca, sostituisci, tabella, campo, modifica);

cerca:            la stringa da cercare per la sostituzione.
sostituisci:   la stringa per la sostituzione.
tabella:         nome_tabella | null  specifica il nome della tabella nella quale cercare/modificare. Se null cerca/modifica su tutte le tabelle.
campo:         nome_campo-colonna | null  specifica il nome della colonna nella quale cercare/modificare. Se null cerca/modifica su tutte le colonne della tabella specificata.
modifica:      true | false se effettuare l''update o no.

Esempio:
SELECT * FROM
mt_str_replace(''cerca'', ''sostituisci'', null, null, false) AS (tabella text, colonna text, condizione text, trova text, sostituisci text);

Autore:        Basso Alberto <<EMAIL>>
Data:            19/12/2011
Nota:            Per abilitare Pl/PgSQL eseguire (LANCOMPILER opzionale): CREATE PROCEDURAL LANGUAGE ''plpgsql'' HANDLER plpgsql_call_handler LANCOMPILER ''PL/PgSql internal'';';

-- New Version parameters
INSERT INTO parameter (name, value) VALUES ('VERSION_MIDDLE', '3.2.10');
INSERT INTO parameter (name, value) VALUES ('VERSION_DOCS', '3.1.9');

-- Update to existing Version parameters
UPDATE parameter SET value = '0.19.0' WHERE name = 'VERSION_OLD';
UPDATE parameter SET value = '3.2.10' WHERE name = 'VERSION_UI';

-- Update to API Root parameter
UPDATE parameter SET value = '/api/1.3' WHERE name = 'API_ROOT';


UPDATE parameter SET value = '3.2.12' WHERE name = 'VERSION';

UPDATE ccp_payment_method SET name = 'Home Banking' WHERE name = 'Home banking';
UPDATE ccp_payment_method SET name = 'Carta Prepagata' WHERE name = 'Carta prepagata';
UPDATE ccp_payment_method SET name = 'Carta di Credito' WHERE name = 'Carta di credito';
UPDATE ccp_payment_method SET name = 'Bollettino Postale' WHERE name = 'Bollettino postale';


CREATE INDEX ON ccp_payment (movement_id);


INSERT INTO parameter (name, value) VALUES ('CCP_NUMBER_AUTOINCREMENT', 'f');


ALTER TABLE public.ccp_payment ALTER COLUMN payer_name SET DEFAULT '';
ALTER TABLE public.ccp_payment ALTER COLUMN payer_surname SET DEFAULT '';
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_name SET DEFAULT '';
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_surname SET DEFAULT '';


UPDATE public.ccp_payment SET payer_name = '' WHERE payer_name IS NULL;
UPDATE public.ccp_payment SET payer_surname = '' WHERE payer_surname IS NULL;
UPDATE audit.ccp_payment SET payer_name = '' WHERE payer_name IS NULL;
UPDATE audit.ccp_payment SET payer_surname = '' WHERE payer_surname IS NULL;

ALTER TABLE public.ccp_payment ALTER COLUMN payer_name SET NOT NULL;
ALTER TABLE public.ccp_payment ALTER COLUMN payer_surname SET NOT NULL;
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_name SET NOT NULL;
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_surname SET NOT NULL;

ALTER TABLE institute_del ADD COLUMN job_registry_id integer;
ALTER TABLE institute_del ADD COLUMN job_warehouse_id integer;
ALTER TABLE institute_del ADD COLUMN job_accounting_id integer;
ALTER TABLE institute_del ADD COLUMN job_personnel_id integer;
ALTER TABLE institute_del ADD COLUMN job_dsga_id integer;
ALTER TABLE institute_del ADD COLUMN job_vice_director_id integer;
ALTER TABLE institute_del ADD COLUMN job_director_id integer;

ALTER TABLE users ADD UNIQUE (user_name);

CREATE TABLE protocol_register
(
  id serial NOT NULL,
  code integer NOT NULL,
  close_date timestamp without time zone,
  creator_software character varying(255) NOT NULL DEFAULT 'MasterCom2'::character varying,
  creator_person character varying(255) NOT NULL,
  recipient character varying(255),
  hash character varying(32) NOT NULL,
  ipa_code character varying(255) NOT NULL,
  school_name character varying(255) NOT NULL,
  area_code integer NOT NULL DEFAULT 1,
  responsible character varying(255) NOT NULL,
  subject character varying(255) NOT NULL DEFAULT 'Registro giornaliero di protocollo'::character varying,
  register_code integer NOT NULL DEFAULT 1,
  register_number integer NOT NULL,
  year integer NOT NULL,
  first_registration_number integer,
  last_registration_number integer,
  first_registration_date timestamp without time zone,
  last_registration_date timestamp without time zone,
  file text NOT NULL DEFAULT ''::text,
  archived timestamp without time zone,
  CONSTRAINT protocol_register_pkey PRIMARY KEY (id),
  CONSTRAINT protocol_register_code_key UNIQUE (code),
  CONSTRAINT protocol_register_register_number_key UNIQUE (register_number)
)
WITH (
  OIDS=FALSE
);

ALTER TABLE institute ADD column ipa_code character varying(255);

ALTER TABLE users ALTER COLUMN employee_id DROP NOT NULL;
ALTER TABLE users ALTER COLUMN employee_id DROP DEFAULT;

UPDATE users set employee_id = NULL where employee_id <= 0;



DROP VIEW ccp_view_payment;

CREATE OR REPLACE VIEW ccp_view_payment AS 
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.type_id, pay.type_text, pay.category_id,  pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;



ALTER TABLE ccp_receipt DROP CONSTRAINT IF EXISTS ccp_receipt_number_key;


INSERT INTO absence_kind (code,description,calc_festivities,calc_ferials) VALUES ('RECFSOAP', 'Recupero festività soppresse anno precedente', false, false);

DROP VIEW ccp_view_payment;

CREATE OR REPLACE VIEW ccp_view_payment AS 
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

-- VERSION 3.3.0
-- MAIL 

CREATE TABLE archive_mail_security (
	id serial PRIMARY KEY,
	"name" character varying(8) NOT NULL
);

INSERT INTO archive_mail_security (name) VALUES ('ssl');
INSERT INTO archive_mail_security (name) VALUES ('tls');

CREATE TABLE archive_mail_protocol (
	id serial PRIMARY KEY,
	"name" character varying(8) NOT NULL
);

INSERT INTO archive_mail_protocol (name) VALUES ('imap');
INSERT INTO archive_mail_protocol (name) VALUES ('pop3');
INSERT INTO archive_mail_protocol (name) VALUES ('smtp');

CREATE TABLE archive_mail_account (
	id serial PRIMARY KEY,	
	"name" character varying(255) NOT NULL,
	protocol integer NOT NULL,
	host character varying(255) NOT NULL,
	port integer NOT NULL,
	"security" integer NOT NULL,
	username character varying(255) NOT NULL,
	"password" character varying(255) NOT NULL,
	active boolean NOT NULL DEFAULT true,
	CONSTRAINT archive_mail_account_protocol_fkey FOREIGN KEY (protocol)
	      REFERENCES archive_mail_protocol (id),	
	CONSTRAINT archive_mail_account_security_fkey FOREIGN KEY ("security")
	      REFERENCES archive_mail_security (id) 	
);

CREATE TABLE archive_mail (    
	id serial,
	account integer REFERENCES archive_mail_account (id) NOT NULL,
	"date" TIMESTAMP NOT NULL,  
	"from" text NOT NULL,
	"to" text NOT NULL,
	cc text,
	ccn text,
	subject text,
	message text,
	"raw" text NOT NULL,   
    deleted boolean NOT NULL DEFAULT false, 
    CONSTRAINT archive_mail_pkey PRIMARY KEY (id, account) 
);

CREATE TABLE archive_mail_attachment (	
    id serial NOT NULL,
    mail integer NOT NULL,
    account	INTEGER NOT NULL,	
	"name" character varying(255) NOT NULL,
	path character varying(255) NOT NULL,
    CONSTRAINT archive_mail_attachment_pkey PRIMARY KEY (mail, account),
    FOREIGN KEY (mail, account) REFERENCES archive_mail (id, account)
);

-- FLUSSI

-- DROP TABLE archive_class_step;

CREATE TABLE archive_class_step
(
  id serial NOT NULL,
  archive_class integer NOT NULL,
  sort integer NOT NULL,
  user_id integer,  
  protocol boolean NOT NULL DEFAULT false,
  albo boolean NOT NULL DEFAULT false,
  trasparenza boolean NOT NULL DEFAULT false,
  sign boolean NOT NULL DEFAULT false,
  CONSTRAINT archive_class_step_pkey PRIMARY KEY (id),
  CONSTRAINT archive_class_step_archive_class_fkey FOREIGN KEY (archive_class)
      REFERENCES archive_class (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE,
  CONSTRAINT user_uid_fkey FOREIGN KEY (user_id)
      REFERENCES users (uid) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE
);  

-- DROP TABLE archive_document_step;

CREATE TABLE archive_document_step
(
    id serial NOT NULL,
    archive_document integer NOT NULL,
    sort integer NOT NULL,    
    user_id integer,
    protocol boolean NOT NULL DEFAULT false,
    albo boolean NOT NULL DEFAULT false,
    trasparenza boolean NOT NULL DEFAULT false,
    sign boolean NOT NULL DEFAULT false,
    CONSTRAINT archive_document_step_pkey PRIMARY KEY (id),
    CONSTRAINT archive_document_step_archive_document_fkey FOREIGN KEY (archive_document)
        REFERENCES archive_document (id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE,
    CONSTRAINT user_uid_fkey FOREIGN KEY (user_id)
        REFERENCES users (uid) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE
);

-- DROP TABLE archive_document_file;

CREATE TABLE archive_document_file
(
    id serial NOT NULL,
    archive_document INTEGER,
    filename text NOT NULL,           
    filetype text NOT NULL,    
    path character varying(255),
    size integer,   
    CONSTRAINT archive_document_file_pkey PRIMARY KEY (id),
    CONSTRAINT archive_document_file_archive_document_id_fkey FOREIGN KEY (archive_document)
        REFERENCES archive_document (id) MATCH SIMPLE
        ON UPDATE CASCADE ON DELETE CASCADE
);

SELECT setval('archive_document_file_id_seq', (SELECT max(max) + 1 FROM 
(
    SELECT max(document_id) FROM protocol_protocol_document 
    UNION
    SELECT max(document_id) FROM trasparenza_voice_document 
    UNION
    SELECT max(document_id) FROM albo_publication_document
) m
));

-- INSERICE TUTTI I FILE NELLA NUOVA TABELLA CON LA NUOVA STRUTTURA
INSERT INTO archive_document_file (archive_document, filename, filetype, path, size) SELECT id, filename, filetype, path, size FROM archive_document;

ALTER TABLE archive_document DROP COLUMN filename;
ALTER TABLE archive_document DROP COLUMN filetype;
ALTER TABLE archive_document DROP COLUMN path;
ALTER TABLE archive_document DROP COLUMN size;

ALTER TABLE audit.archive_document DROP COLUMN filename;
ALTER TABLE audit.archive_document DROP COLUMN filetype;
ALTER TABLE audit.archive_document DROP COLUMN path;
ALTER TABLE audit.archive_document DROP COLUMN size;


ALTER TABLE archive_document ADD COLUMN action_sign BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE archive_document ADD COLUMN action_sign_date BIGINT;
ALTER TABLE audit.archive_document ADD COLUMN action_sign BOOLEAN  NOT NULL DEFAULT false;
ALTER TABLE audit.archive_document ADD COLUMN action_sign_date BIGINT;

INSERT INTO archive_origin VALUES (2, 'Mail', 'mail', 'Documenti importati da account di posta elettronica');

-- Fix previous protocol_document link data to new file table
ALTER TABLE protocol_protocol_document DROP CONSTRAINT protocol_protocol_document_document_id_fkey;
UPDATE protocol_protocol_document SET document_id = (SELECT id FROM archive_document_file WHERE archive_document = document_id);
ALTER TABLE protocol_protocol_document ADD CONSTRAINT protocol_protocol_document_file_document_id_fkey  FOREIGN KEY (document_id)
      REFERENCES archive_document_file (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE;

ALTER TABLE archive_class ADD COLUMN editable BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE audit.archive_class ADD COLUMN editable BOOLEAN;

INSERT INTO archive_class (id, name, code, editable, format, action, description) VALUES (2, 'Protocollo', 'PRT', false, 'ALL', 'N', 'Protocollo');
INSERT INTO archive_class_step (archive_class, sort, protocol)  VALUES (2, 1, true);

INSERT INTO archive_class (id, name, code, editable, format, action, description) VALUES (3, 'Albo', 'ALB', false, 'ALL', 'N', 'Albo');
INSERT INTO archive_class_step (archive_class, sort, albo)  VALUES (3, 1, true);

INSERT INTO archive_class (id, name, code, editable, format, action, description) VALUES (4, 'Trasparenza', 'TSP', false, 'ALL', 'N', 'Trasparenza');
INSERT INTO archive_class_step (archive_class, sort, trasparenza)  VALUES (4, 1, true);


-- TRASPARENZA
ALTER TABLE trasparenza_voice_document DROP CONSTRAINT trasparenza_voice_document_document_id_fkey;
UPDATE trasparenza_voice_document SET document_id = (SELECT id FROM archive_document_file WHERE archive_document = document_id);
ALTER TABLE trasparenza_voice_document ADD CONSTRAINT trasparenza_voice_document_document_id_fkey  FOREIGN KEY (document_id)
      REFERENCES archive_document_file (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE;

-- ALBO
ALTER TABLE albo_publication_document DROP CONSTRAINT albo_publication_document_document_id_fkey;
UPDATE albo_publication_document SET document_id = (SELECT id FROM archive_document_file WHERE archive_document = document_id);
ALTER TABLE albo_publication_document ADD CONSTRAINT albo_publication_document_document_id_fkey  FOREIGN KEY (document_id)
      REFERENCES archive_document_file (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE;


-- DROP TABLE core_forced_action;

CREATE TABLE core_forced_action
(
    id serial PRIMARY KEY,
    date TIMESTAMP,
    section character varying(16),
    code character varying(32),
    description character varying(255),
    "table" character varying(32),
    id_value integer
);

ALTER TABLE audit.archive_document ALTER COLUMN class_id DROP NOT NULL;
ALTER TABLE archive_document ALTER COLUMN class_id DROP NOT NULL;

ALTER TABLE archive_document ADD COLUMN class_name CHARACTER VARYING (30);
ALTER TABLE audit.archive_document ADD COLUMN class_name CHARACTER VARYING (30);
-- Fix data
UPDATE archive_document SET class_name = (SELECT name FROM archive_class WHERE id=archive_document.class_id);


-- Add step for existing archive document fixed as protocol on all user
INSERT INTO archive_document_step (archive_document, sort, protocol) (SELECT id ,1, true FROM archive_document);

-- AUDIT TABLES
CREATE TABLE audit.archive_mail_security (
    op_action character varying(1) NOT NULL,    
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id  BIGINT,
	"name" character varying(8)
);

CREATE TRIGGER archive_mail_security_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_security FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail_protocol (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id BIGINT,
	"name" character varying(8)
);

CREATE TRIGGER archive_mail_protocol_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_protocol FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail_account (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id  BIGINT,	
	"name" character varying(255),
	protocol integer,
	host character varying(255),
	port integer,
	"security" integer,
	username character varying(255),
	"password" character varying(255),
	active boolean	
);

CREATE TRIGGER archive_mail_account_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_account FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail (    
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
	id BIGINT,
	account integer,
	"date" TIMESTAMP,  
	"from" text,
	"to" text,
	cc text,
	ccn text,
	subject text,
	message text,
	"raw" text,
    deleted boolean 
);

CREATE TRIGGER archive_mail_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_mail_attachment (	
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id BIGINT,
    mail integer,
    account INTEGER,	
    "name" character varying(255),
    path character varying(255)
);

CREATE TRIGGER archive_mail_attachment_audit AFTER INSERT OR UPDATE OR DELETE ON archive_mail_attachment FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_class_step
(
  op_action character varying(1) NOT NULL,
  op_date timestamp with time zone NOT NULL DEFAULT now(),
  id  BIGINT,
  archive_class integer,
  sort integer,
  user_id integer,  
  protocol boolean,
  albo boolean,
  trasparenza boolean,
  sign boolean
); 

CREATE TRIGGER archive_class_step_audit AFTER INSERT OR UPDATE OR DELETE ON archive_class_step FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_document_step
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id BIGINT,
    archive_document integer,
    sort integer,    
    user_id integer,
    protocol boolean,
    albo boolean,
    trasparenza boolean,
    sign boolean
);

CREATE TRIGGER archive_document_step_audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_step FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.archive_document_file
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id serial NOT NULL,
    archive_document INTEGER,
    filename text NOT NULL,           
    filetype text NOT NULL,    
    path character varying(255),
    size integer    
);

CREATE TRIGGER archive_document_file_audit AFTER INSERT OR UPDATE OR DELETE ON archive_document_file FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE audit.core_forced_action
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id BIGINT,
    date TIMESTAMP,
    section character varying(16),
    code character varying(32),
    description character varying(255),
    "table" character varying(32),
    id_value integer
);

CREATE TRIGGER core_forced_action_audit AFTER INSERT OR UPDATE OR DELETE ON core_forced_action FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Permissions change
UPDATE auth_section SET title ='Segreteria Digitale' WHERE id = 11;
UPDATE auth_permission SET title = replace(title, 'Documenti |','Segreteria Digitale |') WHERE id in (500,501, 502, 503, 504, 520, 530, 540);
UPDATE auth_permission SET title = replace(title, 'Archiviati','Archivio') WHERE id in (530);

-- Add type (out/in) mail account
ALTER TABLE archive_mail_account ADD COLUMN "out" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.archive_mail_account ADD COLUMN "out" BOOLEAN NOT NULL DEFAULT false;

CREATE TABLE archive_mail_document (
	id serial PRIMARY KEY NOT NULL,
	mail INTEGER NOT NULL,
	account INTEGER NOT NULL,
	document INTEGER NOT NULL,
	CONSTRAINT archive_mail_document_mail_fkey FOREIGN KEY (mail, account)
	      REFERENCES archive_mail (id, account),	
	CONSTRAINT archive_mail_document_document_fkey FOREIGN KEY (document)
	      REFERENCES archive_document (id)
);

ALTER TABLE archive_mail ALTER COLUMN raw DROP NOT NULL;
ALTER TABLE audit.archive_mail ALTER COLUMN raw DROP NOT NULL;

ALTER TABLE archive_mail_attachment DROP CONSTRAINT archive_mail_attachment_pkey;
ALTER TABLE archive_mail_attachment ADD CONSTRAINT archive_mail_attachment_pkey PRIMARY KEY (id);

ALTER TABLE archive_document DROP COLUMN token;
ALTER TABLE audit.archive_document DROP COLUMN token;

ALTER TABLE archive_document_file ADD COLUMN token character varying(255);
ALTER TABLE audit.archive_document_file ADD COLUMN token character varying(255);

UPDATE parameter set value = 'https://conservazionecl.infocert.it/ws' where name = 'ARCHIVE_API_URL'; 

ALTER TABLE archive_class_step ADD COLUMN archive BOOLEAN DEFAULT false;
ALTER TABLE audit.archive_class_step ADD COLUMN archive BOOLEAN DEFAULT false;

ALTER TABLE archive_document_step ADD COLUMN archive BOOLEAN DEFAULT false;
ALTER TABLE audit.archive_document_step ADD COLUMN archive BOOLEAN DEFAULT false;

ALTER TABLE archive_document ADD COLUMN action_archive BOOLEAN DEFAULT false;
ALTER TABLE audit.archive_document ADD COLUMN action_archive BOOLEAN DEFAULT false;

UPDATE archive_document SET action_archive_date = NULL;

    UPDATE archive_class set code=id::text;
    ALTER TABLE archive_class ADD UNIQUE (code);

--
-- Aggiunta campo subject_seat alla view
--
DROP VIEW ccp_view_payment;

CREATE OR REPLACE VIEW ccp_view_payment AS 
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_seat, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_seat, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


-- ADD PARAMETER FOR INFOCERT BUCKET 3.3.10
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_BUCKET', '');
INSERT INTO parameter (name, value) VALUES ('ARCHIVE_API_POLICY', '');

DROP TABLE audit.archive_metadata;
DROP TABLE archive_metadata;

CREATE TABLE archive_metadata
(
  id serial NOT NULL,
  name character varying(30) NOT NULL,
  slug character varying(30) NOT NULL,
  required boolean NOT NULL DEFAULT false,
  kind character(1) NOT NULL DEFAULT 'S'::bpchar, -- Metadata type, possible values: (D)ate, (I)nteger, (S)tring
  remote_class character varying(30) NOT NULL,
  CONSTRAINT archive_metadata_pkey PRIMARY KEY (id),
  CONSTRAINT archive_metadata_remote_class_fkey FOREIGN KEY (remote_class)
      REFERENCES archive_class (code) MATCH SIMPLE
      ON UPDATE CASCADE ON DELETE SET NULL
);

COMMENT ON TABLE archive_metadata
  IS 'Metadata for a document, bound to specific document type';
COMMENT ON COLUMN archive_metadata.kind IS 'Metadata type, possible values: (D)ate, (I)nteger, (S)tring';



UPDATE archive_class set code = 'ALB' WHERE name = 'Albo' and editable=false;
UPDATE archive_class set code = 'TRP' WHERE name = 'Trasparenza' and editable=false;
UPDATE archive_class set code = 'PRT' WHERE name = 'Protocollo' and editable=false;

CREATE TABLE audit.archive_mail_document
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id serial NOT NULL,
    mail character varying(32) NOT NULL,
    account integer NOT NULL,
    document integer NOT NULL  
);

CREATE TRIGGER archive_mail_document_audit
  AFTER INSERT OR UPDATE OR DELETE
  ON archive_mail_document
  FOR EACH ROW
  EXECUTE PROCEDURE audit.process_audit();

ALTER TABLE archive_mail_attachment DROP CONSTRAINT archive_mail_attachment_mail_fkey;
ALTER TABLE archive_mail_document DROP CONSTRAINT archive_mail_document_mail_fkey;
ALTER TABLE archive_mail DROP CONSTRAINT archive_mail_pkey;

ALTER TABLE archive_mail ALTER COLUMN id TYPE character varying(32);
ALTER TABLE archive_mail_document ALTER COLUMN mail TYPE character varying(32);
ALTER TABLE archive_mail_attachment ALTER COLUMN mail TYPE character varying(32);
ALTER TABLE audit.archive_mail ALTER COLUMN id TYPE character varying(32);
ALTER TABLE audit.archive_mail_document ALTER COLUMN mail TYPE character varying(32);
ALTER TABLE audit.archive_mail_attachment ALTER COLUMN mail TYPE character varying(32);

ALTER TABLE archive_mail ALTER COLUMN id DROP DEFAULT;


UPDATE archive_mail SET id = md5(id);
UPDATE archive_mail_document SET mail = md5(mail);
UPDATE archive_mail_attachment SET mail = md5(mail);

ALTER TABLE archive_mail ADD PRIMARY KEY (id, account);
ALTER TABLE archive_mail_document ADD CONSTRAINT archive_mail_document_mail_fkey FOREIGN KEY (mail, account) REFERENCES archive_mail (id, account) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE archive_mail_attachment ADD CONSTRAINT archive_mail_attachment_mail_fkey FOREIGN KEY (mail, account) REFERENCES archive_mail (id, account) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION;



ALTER TABLE archive_class ALTER COLUMN code DROP NOT NULL;
ALTER TABLE audit.archive_class ALTER COLUMN code DROP NOT NULL;

ALTER TABLE archive_class DROP CONSTRAINT IF EXISTS archive_class_code_uid;

DELETE FROM archive_metadata;

CREATE TABLE archive_remote_class (
	code CHARACTER VARYING (64) NOT NULL PRIMARY KEY,
	name CHARACTER VARYING (255) NOT NULL	
);

INSERT INTO archive_remote_class (code, name) VALUES ('cad_regprot', 'Registro di protocollo');
INSERT INTO archive_remote_class (code, name) VALUES ('ae_gen', 'Documento generico');

ALTER TABLE archive_metadata DROP CONSTRAINT archive_metadata_remote_class_fkey;
ALTER TABLE archive_metadata ADD CONSTRAINT archive_metadata_archive_remote_class_fkey FOREIGN KEY (remote_class) REFERENCES archive_remote_class (code) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE CASCADE;

INSERT INTO archive_metadata (name, slug, required, kind, remote_class) VALUES ('Data documento','__data_documento_dt',true, 'D', 'ae_gen');
INSERT INTO archive_metadata (name, slug, required, kind, remote_class) VALUES ('Data documento','__data_documento_dt',true, 'D', 'cad_regprot');
INSERT INTO archive_metadata (name, slug, required, kind, remote_class) VALUES ('Oggetto','oggetto_s',true, 'S', 'cad_regprot');
  
ALTER TABLE archive_document ADD COLUMN remote_class_code CHARACTER VARYING(64);
ALTER TABLE audit.archive_document ADD COLUMN remote_class_code CHARACTER VARYING(64);

ALTER TABLE archive_mail_document DROP CONSTRAINT archive_mail_document_document_fkey;
ALTER TABLE archive_mail_document ADD CONSTRAINT archive_mail_document_document_fkey FOREIGN KEY (document) REFERENCES archive_document (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE CASCADE;



ALTER TABLE ccp_movement ADD COLUMN tmp_number text;
ALTER TABLE audit.ccp_movement ADD COLUMN tmp_number text;

UPDATE ccp_movement SET note = '' WHERE note IS NULL;
UPDATE ccp_movement SET tmp_number = "number", note = note || ' ### Vecchio numero: ' || "number" WHERE id in (SELECT id FROM ccp_movement WHERE "number" !~ '^[0-9]+$' or char_length(number) > 9);
UPDATE ccp_movement SET "number" = NULL WHERE id in (SELECT id FROM ccp_movement WHERE "number" !~ '^[0-9]+$' or char_length(number) > 9);
UPDATE ccp_movement SET note = NULL WHERE note = '';


DROP VIEW ccp_view_movement;
DROP VIEW ccp_view_payment;

ALTER TABLE ccp_movement ALTER COLUMN "number" TYPE INTEGER USING ("number"::numeric);

CREATE OR REPLACE VIEW ccp_view_movement AS
 SELECT mv.id, mv.type_id, mv.subject_type, mv.subject_id, mv.miscellaneous, mv.number, mv.note, mv.school_year, mv.subject_data, mv.subject_seat, mv.subject_class, mv.amount::numeric(14,2) AS amount, mv.creation_date, mv.expiration_date, mv.type_text, mv.incoming, mv.category_id, mv.category_text, mv.total_payments, mv.count_payments, mv.positive_additionals_euro, mv.negative_additionals_euro, mv.positive_additionals_perc, mv.negative_additionals_perc, mv.count_additionals, mv.linked_additionals, mv.linked_payments, (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT m.id, m.type_id, m.subject_type, m.subject_id, m.miscellaneous, m.number, m.note, m.school_year, m.subject_data, m.subject_seat, m.subject_class, m.amount, m.creation_date, m.expiration_date, t.name AS type_text, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments, ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals, ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals, ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m, ccp_type t, ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;

CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_seat, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_seat, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


INSERT INTO parameter (name, value) VALUES ('ARCHIVE_MAIL_SYNC_ENABLED', 't');

CREATE INDEX personnel_timetable_employee_id_idx ON personnel_timetable (employee_id);

ALTER TABLE archive_document_file ADD COLUMN metadata TEXT;
ALTER TABLE audit.archive_document_file ADD COLUMN metadata TEXT;



DELETE FROM archive_remote_class;
-- Codici/Nomi classi remote + metadati
INSERT INTO archive_remote_class (code, name) VALUES ('ae_ddta' , 'DDT emesso');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ddt', '__numero_documento_l', 'D', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione', 'denominazione_s', 'S', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale', 'codice_fiscale_s', 'S', 'ae_ddta', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Partita IVA', 'partita_iva_s', 'S', 'ae_ddta', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_ddtp' , 'DDT ricevuto');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione', 'denominazione_s', 'S', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale', 'codice_fiscale_s', 'S', 'ae_ddtp', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Partita IVA', 'partita_iva_s', 'S', 'ae_ddtp', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_lgio' , 'Libro giornale');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data prima registrazione', 'data_inizio_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lgio', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_lmas' , 'Libro mastro');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lmas', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lmas', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_lces' , 'Libro cespiti');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data prima registrazione', 'data_inizio_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lces', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('ae_linv' , 'Libro inventari');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_linv', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_linv', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('fatp_pa' , 'Fattura elettronica PA passiva');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale emittente', 'codice_fiscale_emittente_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Codice fiscale', 'codice_fiscale_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione emittente', 'denominazione_emittente_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Denominazione', 'denominazione_s', 'S', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Identificativo SDI', 'identificativoSdi_s', 'N', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero documento/fattura', 'numero_documento_s', 'N', 'fatp_pa', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Partita IVA emittente', 'partita_iva_emittente_s', 'S', 'fatp_pa', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('notifica_pa_pass' , 'Notifica fattura elettronica PA passiva');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'notifica_pa_pass', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Identificativo SDI', 'identificativoSdi_s', 'N', 'notifica_pa_pass', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('cad_contratto' , 'Contratto');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Contraente', 'contraente_s', 'S', 'cad_contratto', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'cad_contratto', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Oggetto del contratto', 'oggetto_s', 'S', 'cad_contratto', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero di repertorio/registro', 'repertorio_i', 'N', 'cad_contratto', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('cad_regprot' , 'Registro giornaliero di protocollo');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data di chiusura', '__data_documento_dt', 'D', 'cad_regprot', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Oggetto', 'oggetto_s', 'S', 'cad_regprot', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', 'prot_fine_i', 'N', 'cad_regprot', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', 'prot_inizio_i', 'N', 'cad_regprot', 't');


INSERT INTO archive_remote_class (code, name) VALUES ('cad_docprot' , 'Documento protocollato');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'cad_docprot', 't');

ALTER TABLE albo_publication ALTER COLUMN title TYPE text;
ALTER TABLE audit.albo_publication ALTER COLUMN title TYPE text;
