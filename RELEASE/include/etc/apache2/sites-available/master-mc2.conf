#
# MasterCom 2 - Legacy & Backend (No framework)
#

Alias /mastercom2 /usr/share/mastercom2/

<LocationMatch ^/mastercom2/>

    RewriteEngine on
    RewriteCond %{HTTPS} off
    RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI}

    <IfModule mod_php5.c>
        # 30711 = "E_ALL & ~E_NOTICE & ~E_STRICT"
        php_value error_reporting 30711
        php_value display_errors Off
        php_value html_errors Off
        php_value memory_limit 256M
        php_value upload_max_filesize 16M
    </IfModule>

</LocationMatch>

<LocationMatch ^/mastercom2/server_ata/>

    RewriteEngine off

</LocationMatch>