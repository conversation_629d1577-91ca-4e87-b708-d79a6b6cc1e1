    #!/bin/bash

    ###################################################################################################
    #                                                                                                 #
    # Installs locale IT if does not exists                                                           #
    #                                                                                                 #
    ###################################################################################################

    echo "Installing IT locale"

    MC2API_LOCALE=`locale -a | grep it_IT`

    if [ -z "$MC2API_LOCALE" ]
    then
        if [ -a /etc/locale.gen ]
        then
            echo 'it_IT.UTF-8 UTF-8' >> /etc/locale.gen
            locale-gen
        else
            locale-gen it_IT.UTF-8
        fi
    fi


    ###################################################################################################
    #                                                                                                 #
    # Dumps the schema to check against the base                                                      #
    #                                                                                                 #
    ###################################################################################################

    echo "Generating DB Schema MD5 code"

    su - postgres -c 'pg_dump -F p -s -O -x mastercom2 > mc2_schema.sql'

    md5sum /var/lib/postgresql/mc2_schema.sql | awk '{ print $1 }' > /tmp/md5_actual_schema
    chown www-data:www-data /tmp/md5_actual_schema
    rm /var/lib/postgresql/mc2_schema.sql

    MD5_DB_SCHEMA=`cat /tmp/md5_actual_schema`
    rm /tmp/md5_actual_schema

    su - postgres -c "psql -d mastercom2 -c \"UPDATE parameter SET value ='$MD5_DB_SCHEMA' WHERE name = 'MD5_SCHEMA_DB';\""


    ###################################################################################################
    #                                                                                                 #
    # Sets up the script to clean DB and TMP print file folders                                       #
    #                                                                                                 #
    ###################################################################################################

    crontab -l > tmpCrontab
    crontab -r

    if [ $(grep "/applications/core/print_spool/clean.php" tmpCrontab | wc -l) = "0" ];
    then
            echo "Setting up crontab print spool job"
        echo "" >> tmpCrontab
        echo "# MC2 - Pulitura file temporanei e DB stampe MC2" >> tmpCrontab
        echo "0 0 * * * /bin/php -f "$APP_PATH"/applications/core/print_spool/clean.php > /dev/null 2> &1" >> tmpCrontab
    fi




    #if [ $(grep "/var/www/mc2-api/index.php albo-engine" tmpCrontab | wc -l) = "0" ];
    #then
    #       echo "Setting up crontab albo html generation job"
    #	echo "" >> tmpCrontab
    #	echo "# MC2 - Creazione pagina html per Albo Pretorio" >> tmpCrontab
    #	echo "10 0 * * * /bin/php -f /var/www/mc2-api/index.php albo-generate > /dev/null 2> &1" >> tmpCrontab
    #fi
    #
    #if [ $(grep "/var/www/mc2-api/index.php trasparenza-engine" tmpCrontab | wc -l) = "0" ];
    #then
    #       echo "Setting up crontab trasparenza html generation job"
    #	echo "" >> tmpCrontab
    #	echo "# MC2 - Creazione pagina html per Trasparenza Amministrativa" >> tmpCrontab
    #	echo "20 0 * * * /bin/php -f /var/www/mc2-api/index.php trasparenza-generate > /dev/null 2> &1" >> tmpCrontab
    #fi

    sed -ie '/index.php sync-mail/d' tmpCrontab
    echo "Setting up crontab sync mail job"
    echo "*/2 * * * * php -d max_execution_time=3600 /var/www/mc2-api/index.php sync-mail > /dev/null" >> tmpCrontab


    sed -ie '/index.php delete-old-emails/d' tmpCrontab
    sed -ie '/Setting up email cleaner/d' tmpCrontab
    echo "Setting up email cleaner"
    echo "# Setting up email cleaner" >> tmpCrontab
    echo "50 5 * * * php -f /var/www/mc2-api/index.php delete-old-emails delete 90 > /dev/null 2>&1" >> tmpCrontab


    sed -ie '/index.php check-invoice-notification/d' tmpCrontab
    sed -ie '/Setting up Fattura PA notification mail check/d' tmpCrontab
    echo "Setting up Fattura PA notification mail check"
    echo "# Setting up Fattura PA notification mail check" >> tmpCrontab
    echo "*/2 * * * * php /var/www/mc2-api/index.php check-invoice-notification > /dev/null 2>&1" >> tmpCrontab

    sed -ie '/send-reminders/d' tmpCrontab
    sed -ie '/Setting up reminder send/d' tmpCrontab
    echo "Setting up reminder send"
    echo "# Setting up reminder send" >> tmpCrontab
    echo "* * * * * php /var/www/mc2-api/index.php send-reminders > /dev/null 2>&1" >> tmpCrontab

    sed -ie '/send-movements-magister/d' tmpCrontab
    sed -ie '/Setting up magister sync/d' tmpCrontab
    echo "Setting up magister sync"
    echo "# Setting up magister sync" >> tmpCrontab
    echo "* * * * * bash -c 'source /etc/profile.d/mc2_magister.sh && php /var/www/mc2-api/index.php send-movements-magister > /dev/null 2>&1'" >> tmpCrontab

    sed -ie '/get-invoices-ds-status/d' tmpCrontab
    sed -ie '/Setting up bluenext check status/d' tmpCrontab
    echo "Setting up bluenext check status"
    echo "# Setting up bluenext check status" >> tmpCrontab
    echo "0 * * * * php /var/www/mc2-api/index.php get-invoices-ds-status > /dev/null 2>&1" >> tmpCrontab

    sed -ie '/Setting up prometheus controller/d' tmpCrontab
    sed -ie '/prometheus/d' tmpCrontab
    echo "Setting up prometheus controller"
    echo "# Setting up prometheus controller" >> tmpCrontab
    echo "0 0 * * * php /var/www/mc2-api/index.php prometheus > /dev/null 2>&1" >> tmpCrontab

    sed -ie '/set-sepa-recurrent/d' tmpCrontab
    sed -ie '/Setting up check sepa parents data/d' tmpCrontab
    echo "Deleting up check sepa parents data"

    crontab tmpCrontab
    rm -f tmpCrontab


    ###################################################################################################
    #                                                                                                 #
    # Adds needed folders and updates ownerships and permissions                                      #
    #                                                                                                 #
    ###################################################################################################

    echo "Updating folder structure, ownerships and permissions"

    FOLDER_FILES=/var/files
    FOLDER_FILES_MC2=$FOLDER_FILES/mc2
    FOLDER_DOCS=$FOLDER_FILES_MC2/docs
    FOLDER_PR_DOCS_ORIGINAL=$FOLDER_DOCS/original
    FOLDER_DRAFTS=$FOLDER_FILES_MC2/drafts
    FOLDER_ALBO=$FOLDER_FILES_MC2/albo
    FOLDER_ALBO_DOCS=$FOLDER_ALBO/docs
    FOLDER_INVOICES=$FOLDER_FILES_MC2/invoices
    FOLDER_INVOICES_DOCS=$FOLDER_INVOICES/docs
    FOLDER_TRASPARENZA=$FOLDER_FILES_MC2/trasparenza
    FOLDER_TRASPARENZA_DOCS=$FOLDER_TRASPARENZA/docs
    FOLDER_MAIL_ATTACHMENT_DOCS=$FOLDER_FILES_MC2/mail_attachments

    FOLDER_REPORTS=$FOLDER_FILES_MC2/reports
    FOLDER_REPORTS_PUB=$FOLDER_REPORTS/pub

    FOLDER_RECEIPTS=$FOLDER_FILES_MC2/receipts
    FOLDER_RECEIPTS_DOCS=$FOLDER_RECEIPTS/docs

    FOLDER_EXPORTS=$FOLDER_FILES_MC2/exports
    FOLDER_EXPORTS_EASY=$FOLDER_EXPORTS/easy

    mkdir -p /var/www/.gnome2
    chown www-data:www-data /var/www/.gnome2

    #mkdir -p /var/www/.inkscape
    #chown www-data:www-data /var/www/.inkscape

    mkdir -p /var/www/.config/inkscape
    chown www-data:www-data /var/www/.config/inkscape

    chmod 755 /usr/share/fonts/truetype/mastercom2

    #chown -R www-data:www-data $APP_PATH
    mkdir -p $FOLDER_DOCS
    mkdir -p $FOLDER_PR_DOCS_ORIGINAL
    mkdir -p $FOLDER_DRAFTS
    mkdir -p $FOLDER_ALBO_DOCS
    mkdir -p $FOLDER_INVOICES_DOCS
    mkdir -p $FOLDER_TRASPARENZA_DOCS
    mkdir -p $FOLDER_MAIL_ATTACHMENT_DOCS

    mkdir -p $FOLDER_REPORTS
    mkdir -p $FOLDER_REPORTS_PUB

    mkdir -p $FOLDER_RECEIPTS
    mkdir -p $FOLDER_RECEIPTS_DOCS

    mkdir -p $FOLDER_EXPORTS
    mkdir -p $FOLDER_EXPORTS_EASY

    chown root:www-data $FOLDER_FILES
    chmod 770 $FOLDER_FILES

    chown root:www-data $FOLDER_FILES_MC2
    chmod 770 $FOLDER_FILES_MC2

    chown -R root:www-data $FOLDER_DOCS
    chmod -R 770 $FOLDER_DOCS

    chown -R www-data:www-data $FOLDER_PR_DOCS_ORIGINAL
    chmod -R 770 $FOLDER_PR_DOCS_ORIGINAL

    chown -R www-data:www-data $FOLDER_DOCS/20*
    chmod -R 770 $FOLDER_DOCS/20*

    chown -R root:www-data $FOLDER_DRAFTS
    chmod -R 770 $FOLDER_DRAFTS

    chown -R root:www-data $FOLDER_ALBO
    chmod -R 770 $FOLDER_ALBO

    chown -R root:www-data $FOLDER_ALBO_DOCS
    chmod -R 770 $FOLDER_ALBO_DOCS

    chown -R root:www-data $FOLDER_INVOICES
    chmod -R 770 $FOLDER_INVOICES

    chown -R root:www-data $FOLDER_INVOICES_DOCS
    chmod -R 770 $FOLDER_INVOICES_DOCS

    chown -R root:www-data $FOLDER_TRASPARENZA
    chmod -R 770 $FOLDER_TRASPARENZA

    chown -R root:www-data $FOLDER_TRASPARENZA_DOCS
    chmod -R 770 $FOLDER_TRASPARENZA_DOCS

    chown -R root:www-data $FOLDER_MAIL_ATTACHMENT_DOCS
    chmod -R 770 $FOLDER_MAIL_ATTACHMENT_DOCS

    chown -R root:www-data $FOLDER_REPORTS
    chmod -R 770 $FOLDER_REPORTS

    chown -R root:www-data $FOLDER_REPORTS_PUB
    chmod -R 770 $FOLDER_REPORTS_PUB

    chown -R root:www-data $FOLDER_RECEIPTS
    chmod -R 770 $FOLDER_RECEIPTS

    chown -R root:www-data $FOLDER_RECEIPTS_DOCS
    chmod -R 770 $FOLDER_RECEIPTS_DOCS

    chown -R root:www-data $FOLDER_EXPORTS
    chmod -R 770 $FOLDER_EXPORTS

    chown -R root:www-data $FOLDER_EXPORTS_EASY
    chmod -R 770 $FOLDER_EXPORTS_EASY

    ###################################################################################################
    #                                                                                                 #
    # Recaches fonts                                                                                  #
    #                                                                                                 #
    ###################################################################################################

    echo "Recaching fonts"

    fc-cache -fv


    ###################################################################################################
    #                                                                                                 #
    # Deletes /var/www/mc2api folder                                                                  #
    #                                                                                                 #
    ###################################################################################################

    if [ -d /var/www/mc2api ];
    then
        rm -rf /var/www/mc2api
    fi


    ###################################################################################################
    #                                                                                                 #
    # Enables mc2 site if is not                                                                      #
    #                                                                                                 #
    ###################################################################################################

    if [ ! -L /etc/apache2/sites-enabled/master-$APP_NAME.conf ];
    then
        echo "Enabling apache site configuration for package master-"$APP_NAME
        a2ensite master-$APP_NAME.conf # Reload always performed by package.ini
    fi

    echo "Done"
