-- Creazione tabella tipi di credito (credits_type)
CREATE TABLE public.ccp_credits_type
(
    id serial NOT NULL PRIMARY KEY,
    description character varying(255),
    is_default boolean DEFAULT false,
    active boolean DEFAULT true,
    dote boolean DEFAULT false,
    subject_type character varying(255)
);
-- audit
CREATE TABLE audit.ccp_credits_type
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer not null,
    description character varying(255),
    is_default boolean DEFAULT false,
    active boolean DEFAULT true,
    dote boolean DEFAULT false,
    subject_type character varying(255)
);
-- trigger
CREATE TRIGGER ccp_credits_type_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_credits_type
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Creazione tabella versamenti (deposits)
CREATE TABLE public.ccp_deposits
(
    id serial NOT NULL PRIMARY KEY,
    operation_date bigint NOT NULL,
    accountable_date bigint,
    amount double precision NOT NULL DEFAULT 0.00,
    credits_id integer NOT NULL DEFAULT 0,
    payment_method_id integer,
    payer_type character varying(1) NOT NULL,
    payer_id character varying(30),
    payer_name character varying(50),
    payer_surname character varying(50),
    payer_fiscal_code character varying(20),
    payer_address character varying(80),
    payer_city character varying(30),
    payer_province character varying(2),
    payer_zip_code character varying(5),
    -- CONSTRAINT ccp_deposits_payment_method_id_fkey FOREIGN KEY (payment_method_id) REFERENCES ccp_payment_method (id) ON UPDATE CASCADE ON DELETE NO ACTION,
    CONSTRAINT ccp_credits_id_fkey FOREIGN KEY (credits_id) REFERENCES ccp_credits (id) ON UPDATE CASCADE ON DELETE NO ACTION
    -- CONSTRAINT ccp_deposits_payer_type_check CHECK (payer_type IN ('S', 'P', 'T', 'E', 'O'))
);
-- audit
CREATE TABLE audit.ccp_deposits
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    operation_date bigint,
    accountable_date bigint,
    amount double precision,
    credits_id integer NOT NULL DEFAULT 0,
    payment_method_id integer,
    payer_type character varying(1),
    payer_id character varying(30),
    payer_name character varying(50),
    payer_surname character varying(50),
    payer_fiscal_code character varying(20),
    payer_address character varying(80),
    payer_city character varying(30),
    payer_province character varying(2),
    payer_zip_code character varying(5)
);
-- trigger
CREATE TRIGGER ccp_deposits_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_deposits
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Modifica campi tabella crediti
-- (lega tipo di credito a id soggetto (parente/studente))
ALTER TABLE public.ccp_credits
    ADD COLUMN subject_id integer NOT NULL DEFAULT 0,
    ADD COLUMN subject_type character varying(1),
    ADD COLUMN credit_type_id integer NOT NULL DEFAULT 0;



--audit
ALTER TABLE audit.ccp_credits
    ADD COLUMN subject_id integer,
    ADD COLUMN subject_type character varying(1),
    ADD COLUMN credit_type_id integer NOT NULL DEFAULT 0;


-- Popolazione tabella tipi di credito con distinct su tabella crediti
-- Aggiungo una riga tipo di credito = 'credito generico' default = true
INSERT INTO
    ccp_credits_type (
        description,
        is_default
    )
VALUES (
    'credito generico',
    true
);

-- Select distinct da tabella crediti esistente

INSERT INTO
    ccp_credits_type (
        description,
        dote
    )
SELECT DISTINCT
    description, dote
FROM
    ccp_credits;


-- Aggiornamento tabella crediti con id_tipo_credito
UPDATE
    ccp_credits
SET
    credit_type_id = (
        SELECT
            id
        FROM
            ccp_credits_type
        WHERE
            ccp_credits_type.description = ccp_credits.description
    ),
    subject_id = student_id,
    subject_type = 'S';



-- Popolazione tabella versamenti con saldi iniziali
INSERT INTO
    ccp_deposits(
        credits_id,
        operation_date,
        amount,
        payer_type
    )
    SELECT
        id,
        extract(epoch FROM NOW())::integer,
        amount,
        'O'
        FROM
        ccp_credits;

-- Aggiunta CONSTRAINT foreign key su ccp_credits -> ccp_credits_type
ALTER TABLE ccp_credits
    ALTER COLUMN description DROP NOT NULL,
    ALTER COLUMN student_id DROP NOT NULL,
    ADD CONSTRAINT ccp_credits_subject_type_check CHECK (subject_type IN ('S', 'P', 'T', 'E', 'O')),
    ADD CONSTRAINT
        ccp_credits_credit_type_id_fkey
        FOREIGN KEY (
            credit_type_id
        )
        REFERENCES
            ccp_credits_type (id)
        ON UPDATE CASCADE ON DELETE NO ACTION;


ALTER TABLE audit.ccp_credits
    ALTER COLUMN description DROP NOT NULL,
    ALTER COLUMN student_id DROP NOT NULL;