INSERT INTO parameter (name,value) VALUES ('NEXT_API_HOST', 'localhost');

DROP TABLE magister_sync;
CREATE TABLE magister_sync (
    id SERIAL,
    id_esterno CHARACTER VARYING(255) NOT NULL,
    modalita_causale  CHARACTER VARYING(255) NOT NULL,
    causale INTEGER, -- PER ORA INUTILE
    causale_codice CHARACTER VARYING(255) NOT NULL,
    importo double precision NOT NULL,
    data_scadenza TIMESTAMP WITH TIME ZONE,
    data_operazione TIMESTAMP WITH TIME ZONE,
    descrizione TEXT,
    
    -- CAMPI CONTROLLO
    sent_date TIMESTAMP WITH TIME ZONE,
    creation_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    error   TEXT

);

DELETE FROM parameter WHERE name = 'MATTHAEUS_CONTO_FATTURE_DA_EMETTERE';
UPDATE  parameter set name = 'MATTHAEUS_CAUSALE_CONTABILE_FATTURE' WHERE name = 'MATTHAEUS_CONTO_FATTURE_EMESSE';