ALTER TABLE ccp_additional
    ADD COLUMN code CHARACTER VARYING(20) DEFAULT '';

ALTER TABLE audit.ccp_additional
    ADD COLUMN code CHARACTER VARYING(20) DEFAULT '';


INSERT INTO ccp_additional (name, positive, percentual, payment, amount, code) VALUES ('sconto sul totale', false, false, false, 0, 'ABS_DISCOUNT');


-- Campo di controllo per descrizione easy. Definisce che campo inviare ad easy come descrizione
ALTER TABLE ccp_type ADD COLUMN easy_description CHARACTER VARYING(20); -- NULL, TIPO, <PERSON>OVIMENTO
COMMENT ON COLUMN ccp_type.easy_description IS 'Campo inviare ad easy come descrizione. Possibili valori: NULL, TIPO, MOVIMENTO';
ALTER TABLE audit.ccp_type ADD COLUMN easy_description CHARACTER VARYING(20);