
-- Aggiunta tabelle di uffici ed utenti con relativa storicizzazione
CREATE TABLE archive_office (
    id serial PRIMARY KEY,
    name character varying(255)    
);

CREATE TABLE audit.archive_office (
  op_action character varying(1) NOT NULL,
  op_date timestamp with time zone NOT NULL DEFAULT now(),
  id bigint NOT NULL,
  name character varying(255)  
);

CREATE TRIGGER archive_office_audit AFTER INSERT OR UPDATE OR DELETE ON archive_office FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

CREATE TABLE archive_office_user (
    id serial PRIMARY KEY,
    office integer REFERENCES archive_office (id),
    "user" integer REFERENCES users (uid)
);

CREATE TABLE audit.archive_office_user (
  op_action character varying(1) NOT NULL,
  op_date timestamp with time zone NOT NULL DEFAULT now(),
  id bigint NOT NULL,
  office bigint NOT NULL,
  "user" bigint NOT NULL
);

CREATE TRIGGER archive_office_user_audit AFTER INSERT OR UPDATE OR DELETE ON archive_office_user FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Creazione tabella per la presa visione
CREATE TABLE archive_check_document (  
    archive_document integer REFERENCES archive_document (id),
    "user" integer REFERENCES users (uid),    
    checked timestamp with time zone,
    PRIMARY KEY (archive_document, "user")
);

CREATE TABLE audit.archive_check_document (  
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    archive_document bigint NOT NULL,
    "user" bigint NOT NULL, 
    checked timestamp with time zone    
);

CREATE TRIGGER archive_check_document_audit AFTER INSERT OR UPDATE OR DELETE ON archive_check_document FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Creazione tabella log
CREATE TABLE archive_log (
    id serial PRIMARY KEY,
    "date" timestamp with time zone NOT NULL DEFAULT now(),
    "user" integer NOT NULL,
    document integer NOT NULL,
    description text
);

-- Aggiunta colonne assegnazione all'interno della mail
ALTER TABLE archive_mail ADD COLUMN assign_to_office INTEGER REFERENCES archive_office (id);
ALTER TABLE audit.archive_mail ADD COLUMN assign_to_office INTEGER;

ALTER TABLE archive_mail ADD COLUMN assign_to_user INTEGER REFERENCES users (uid);
ALTER TABLE audit.archive_mail ADD COLUMN assign_to_user INTEGER;

ALTER TABLE archive_mail ADD COLUMN assign_from_user INTEGER REFERENCES users (uid);
ALTER TABLE audit.archive_mail ADD COLUMN assign_from_user INTEGER;

-- Aggiunta colonne assegnazione all'interno del gruppo di documenti
ALTER TABLE archive_document ADD COLUMN assign_to_office INTEGER REFERENCES archive_office (id);
ALTER TABLE audit.archive_document ADD COLUMN assign_to_office INTEGER;

ALTER TABLE archive_document ADD COLUMN assign_to_user INTEGER REFERENCES users (uid);
ALTER TABLE audit.archive_document ADD COLUMN assign_to_user INTEGER;

ALTER TABLE archive_document ADD COLUMN assign_from_user INTEGER REFERENCES users (uid);
ALTER TABLE audit.archive_document ADD COLUMN assign_from_user INTEGER;

ALTER TABLE archive_document ADD COLUMN completed timestamp with time zone;
ALTER TABLE audit.archive_document ADD COLUMN completed timestamp with time zone;


ALTER TABLE archive_class_step ADD COLUMN "type" CHARACTER VARYING (1);
ALTER TABLE audit.archive_class_step ADD COLUMN "type" CHARACTER VARYING (1);
UPDATE archive_class_step SET type = 'U' WHERE user_id IS NOT NULL;

ALTER TABLE archive_document_step ADD COLUMN "type" CHARACTER VARYING (1);
ALTER TABLE audit.archive_document_step ADD COLUMN "type" CHARACTER VARYING (1);
UPDATE archive_document_step SET type = 'U' WHERE user_id IS NOT NULL;

ALTER TABLE archive_document_step DROP CONSTRAINT user_uid_fkey;
ALTER TABLE archive_class_step DROP CONSTRAINT user_uid_fkey;

UPDATE parameter SET value = 'http://conservazione.infocert.it/ws' WHERE name = 'ARCHIVE_API_URL';
ALTER TABLE archive_class DROP CONSTRAINT archive_class_code_key;

DELETE FROM archive_metadata WHERE remote_class IN ('ae_lces', 'ae_lgio');

INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data prima registrazione', 'data_inizio_dt', 'D', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lgio', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lgio', 't');

INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data documento', '__data_documento_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data inizio periodo di imposta', '__data_inizio_numerazione_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Data prima registrazione', 'data_inizio_dt', 'D', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Periodo di imposta', '__anno_fiscale_i', 'N', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero prima registrazione', '__progr_inizio_l', 'N', 'ae_lces', 't');
INSERT INTO archive_metadata (name, slug, kind, remote_class, required) VALUES ('Numero ultima registrazione', '__progr_fine_l', 'N', 'ae_lces', 't');
