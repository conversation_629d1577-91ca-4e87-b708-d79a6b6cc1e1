-- Ccp Receipt
CREATE TABLE ccp_receipt
(
   id serial NOT NULL,
   number integer NOT NULL,
   date bigint NOT NULL,
   CONSTRAINT ccp_receipt_pkey PRIMARY KEY (id),
   CONSTRAINT ccp_receipt_number_key UNIQUE (number)
);

COMMENT ON TABLE ccp_receipt IS 'A receipt containing payments';

CREATE TABLE audit.ccp_receipt
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   number integer NOT NULL,
   date bigint NOT NULL
);

CREATE TRIGGER ccp_receipt_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_receipt
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Update to Ccp Payment schema
ALTER TABLE ccp_payment ADD COLUMN receipt_id INTEGER;
ALTER TABLE audit.ccp_payment ADD COLUMN receipt_id INTEGER;

ALTER TABLE ccp_payment ADD CONSTRAINT ccp_payment_receipt_id_fkey FOREIGN KEY (receipt_id) REFERENCES ccp_receipt (id) ON UPDATE CASCADE ON DELETE SET NULL;

-- Update to Ccp Movement schema
ALTER TABLE ccp_movement ADD COLUMN expiration_date BIGINT;
ALTER TABLE audit.ccp_movement ADD COLUMN expiration_date BIGINT;

ALTER TABLE ccp_movement RENAME date TO creation_date;
ALTER TABLE audit.ccp_movement RENAME date TO creation_date;

ALTER TABLE ccp_movement RENAME anno_scolastico TO school_year;
ALTER TABLE audit.ccp_movement RENAME anno_scolastico TO school_year;

-- Update Ccp Type schema
ALTER TABLE audit.ccp_type ADD COLUMN incoming BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE ccp_type ADD COLUMN incoming BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE audit.ccp_type ALTER COLUMN incoming DROP DEFAULT;

ALTER TABLE ccp_type ALTER COLUMN expiration_date DROP NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN expiration_date DROP NOT NULL;

UPDATE ccp_type SET expiration_date = NULL WHERE expiration_date = 0;

INSERT INTO ccp_type (name, category_id, incoming) SELECT 'Prelievo - ' || name, id, false FROM ccp_category;
UPDATE ccp_movement AS m SET type_id = (SELECT id FROM ccp_type AS t WHERE t.category_id = m.exit_category_id AND t.incoming = false AND name LIKE 'Prelievo - %') WHERE incoming = false;

ALTER TABLE ccp_movement DROP COLUMN exit_category_id;
ALTER TABLE ccp_movement DROP COLUMN incoming;
ALTER TABLE audit.ccp_movement DROP COLUMN exit_category_id;
ALTER TABLE audit.ccp_movement DROP COLUMN incoming;

-- Update to Ccp Payment
CREATE TABLE ccp_payment_additional
(
   payment_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL DEFAULT 0.00,
   CONSTRAINT ccp_payment_additional_pkey PRIMARY KEY (payment_id, additional_id),
   CONSTRAINT ccp_payment_additional_payment_id_fkey FOREIGN KEY (payment_id) REFERENCES ccp_payment (id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT ccp_payment_additional_additional_id_fkey FOREIGN KEY (additional_id) REFERENCES ccp_additional (id) ON UPDATE CASCADE ON DELETE NO ACTION,
   CONSTRAINT ccp_payment_additional_amount_check CHECK (amount >= 0)
);

CREATE INDEX ccp_payment_additional_payment_id_fki ON ccp_payment_additional USING btree (payment_id);
CREATE INDEX ccp_payment_additional_additional_id_fki ON ccp_payment_additional USING btree (additional_id);

CREATE TABLE audit.ccp_payment_additional
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   payment_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL
);

CREATE TRIGGER ccp_payment_additional_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_payment_additional
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Update to Ccp Additionals
ALTER TABLE audit.ccp_additional ADD COLUMN payment BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE ccp_additional ADD COLUMN payment BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.ccp_additional ALTER COLUMN payment DROP DEFAULT;

-- CCP Movement view

--DROP VIEW ccp_view_movement;

CREATE OR REPLACE VIEW ccp_view_movement AS
 SELECT mv.id, mv.type_id, mv.subject_type, mv.subject_id, mv.miscellaneous, mv.number, mv.note, mv.school_year, mv.subject_data, mv.subject_seat, mv.subject_class, mv.amount::numeric(14,2) AS amount, mv.creation_date, mv.expiration_date, mv.type_text, mv.incoming, mv.category_id, mv.category_text, mv.total_payments, mv.count_payments, mv.positive_additionals_euro, mv.negative_additionals_euro, mv.positive_additionals_perc, mv.negative_additionals_perc, mv.count_additionals, mv.linked_additionals, mv.linked_payments, (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT m.id, m.type_id, m.subject_type, m.subject_id, m.miscellaneous, m.number, m.note, m.school_year, m.subject_data, m.subject_seat, m.subject_class, m.amount, m.creation_date, m.expiration_date, t.name AS type_text, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments, ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals, ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals, ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m, ccp_type t, ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;

-- View: ccp_view_movement_category

-- DROP VIEW ccp_view_movement_category;

CREATE OR REPLACE VIEW ccp_view_movement_category AS
 SELECT mm.year, mm.category_id AS id, mm.category_text AS text, sum(mm.total_positive) AS positive, sum(mm.total_negative) AS negative, sum(mm.total) AS balance
   FROM ( SELECT date_part('year'::text, to_timestamp(mv.creation_date::double precision)) AS year, mv.category_id, mv.category_text, mv.incoming,
                CASE
                    WHEN mv.incoming = true THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN mv.incoming = false THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) * (-1)::numeric
                    ELSE 0::numeric(14,2)
                END AS total_negative,
                CASE
                    WHEN mv.incoming = true THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) * (-1)::numeric
                END AS total
           FROM ( SELECT m.amount, m.creation_date, t.incoming, t.category_id, c.name AS category_text, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                   FROM ccp_movement m, ccp_type t, ccp_category c
                  WHERE m.type_id = t.id AND t.category_id = c.id) mv) mm
  GROUP BY mm.year, mm.category_id, mm.category_text
  ORDER BY mm.year DESC, mm.category_text;

-- Bank Profile updates
ALTER TABLE bank_profile ADD COLUMN initial_balance double precision NOT NULL DEFAULT 0.00;
ALTER TABLE bank_profile ADD COLUMN type character varying(1) NOT NULL DEFAULT 'B';

ALTER TABLE bank_profile ADD CONSTRAINT bank_profile_type_check CHECK (type::text = ANY (ARRAY['P'::character varying::text, 'B'::character varying::text]));


-- Fix CCP Permissions
UPDATE auth_section SET title = 'Conti Correnti' WHERE id = 8;

UPDATE auth_permission SET title = 'Conti Correnti | Attivo' WHERE id = 21;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Aggiungere' WHERE id = 200;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Modificare' WHERE id = 201;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Eliminare' WHERE id = 202;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Copiare' WHERE id = 203;
UPDATE auth_permission SET title = 'Conti Correnti | Movimenti | Stampe singole' WHERE id = 204;

UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Visualizzare' WHERE id = 210;
UPDATE auth_permission SET title = 'Conti Correnti | Bollettini | Visualizzare' WHERE id = 211;
UPDATE auth_permission SET title = 'Conti Correnti | Stampe | Visualizzare' WHERE id = 212;

UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Aggiungere' WHERE id = 220;
UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Modificare' WHERE id = 221;
UPDATE auth_permission SET title = 'Conti Correnti | Tipi di Movimento | Eliminare' WHERE id = 222;

UPDATE auth_element SET name = 'CcpMovementNewBtn', control_interface = 'CcpMovementsPnl' WHERE id = 200;
UPDATE auth_element SET name = 'contextCcpMovementEdit', control_interface = 'CcpMovementEditMn' WHERE id = 201;
UPDATE auth_element SET name = 'contextCcpMovementDelete', control_interface = 'CcpMovementEditMn' WHERE id = 202;
UPDATE auth_element SET name = 'contextCcpMovementCopy', control_interface = 'CcpMovementEditMn' WHERE id = 203;
UPDATE auth_element SET name = 'contextCcpMovementPrints', control_interface = 'CcpMovementEditMn' WHERE id = 204;

UPDATE auth_element SET name = 'CcpTypeBtn', control_interface = 'CcpToolbar' WHERE id = 210;

UPDATE auth_element SET name = 'CcpTypeNewBtn', control_interface = 'CcpTypesWin' WHERE id = 220;
UPDATE auth_element SET name = 'contextCcpTypeEdit', control_interface = 'CcpTypeEditMn' WHERE id = 221;
UPDATE auth_element SET name = 'contextCcpTypeDelete', control_interface = 'CcpTypeEditMn' WHERE id = 222;


-- New CCP Permissions
-- Categories
INSERT INTO auth_permission (id, title, auth_section) VALUES (230, 'Conti Correnti | Categorie | Visualizzare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (230, 'CcpCategoryBtn', 'CcpToolbar', 230);
INSERT INTO auth_path (id, path, auth_permission) VALUES (230, '', 230);

INSERT INTO auth_permission (id, title, auth_section) VALUES (231, 'Conti Correnti | Categorie | Aggiungere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (231, 'CcpCategoryNewBtn', 'CcpCategoriesToolbar', 231);
INSERT INTO auth_path (id, path, auth_permission) VALUES (231, '', 231);

INSERT INTO auth_permission (id, title, auth_section) VALUES (232, 'Conti Correnti | Categorie | Modificare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (232, 'contextCcpCategoryEdit', 'CcpCategoryEditMn', 232);
INSERT INTO auth_path (id, path, auth_permission) VALUES (232, '', 232);

INSERT INTO auth_permission (id, title, auth_section) VALUES (233, 'Conti Correnti | Categorie | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (233, 'contextCcpCategoryDelete', 'CcpCategoryEditMn', 233);
INSERT INTO auth_path (id, path, auth_permission) VALUES (233, '', 233);

-- Additionals
INSERT INTO auth_permission (id, title, auth_section) VALUES (240, 'Conti Correnti | Addizionali | Visualizzare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (240, 'CcpAdditionalBtn', 'CcpToolbar', 240);
INSERT INTO auth_path (id, path, auth_permission) VALUES (240, '', 240);

INSERT INTO auth_permission (id, title, auth_section) VALUES (241, 'Conti Correnti | Addizionali | Aggiungere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (241, 'CcpAdditionalNewBtn', 'CcpAdditionalsToolbar', 241);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (223, 'CcpTypeEditNewAdditionalBtn', 'CcpTypeEditAdditionalsToolbar', 241);
INSERT INTO auth_path (id, path, auth_permission) VALUES (241, '', 241);
INSERT INTO auth_path (id, path, auth_permission) VALUES (223, '', 241);

INSERT INTO auth_permission (id, title, auth_section) VALUES (242, 'Conti Correnti | Addizionali | Modificare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (242, 'contextCcpAdditionalEdit', 'CcpAdditionalEditMn', 242);
INSERT INTO auth_path (id, path, auth_permission) VALUES (242, '', 242);

INSERT INTO auth_permission (id, title, auth_section) VALUES (243, 'Conti Correnti | Addizionali | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (243, 'contextCcpAdditionalDelete', 'CcpAdditionalEditMn', 243);
INSERT INTO auth_path (id, path, auth_permission) VALUES (243, '', 243);

-- Residuals
INSERT INTO auth_permission (id, title, auth_section) VALUES (250, 'Conti Correnti | Residui | Visualizzare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (250, 'CcpResidualBtn', 'CcpToolbar', 250);
INSERT INTO auth_path (id, path, auth_permission) VALUES (250, '', 250);

-- Payments
INSERT INTO auth_permission (id, title, auth_section) VALUES (261, 'Conti Correnti | Pagamenti | Aggiungere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (261, 'CcpPaymentNewBtn', 'CcpPaymentsToolbar', 261);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (205, 'contextCcpMovementPay', 'CcpMovementEditMn', 261);
INSERT INTO auth_path (id, path, auth_permission) VALUES (261, '', 261);
INSERT INTO auth_path (id, path, auth_permission) VALUES (205, '', 261);

INSERT INTO auth_permission (id, title, auth_section) VALUES (262, 'Conti Correnti | Pagamenti | Modificare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (262, 'contextCcpPaymentEdit', 'CcpPaymentsMn', 262);
INSERT INTO auth_path (id, path, auth_permission) VALUES (262, '', 262);

INSERT INTO auth_permission (id, title, auth_section) VALUES (263, 'Conti Correnti | Pagamenti | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (263, 'contextCcpPaymentDelete', 'CcpPaymentsMn', 263);
INSERT INTO auth_path (id, path, auth_permission) VALUES (263, '', 263);

INSERT INTO auth_permission (id, title, auth_section) VALUES (264, 'Conti Correnti | Pagamenti | Stampe singole', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (264, 'contextCcpPaymentPrints', 'CcpPaymentsMn', 264);
INSERT INTO auth_path (id, path, auth_permission) VALUES (264, '', 264);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (265, 'CcpPaymentEditNewAdditionalBtn', 'CcpPaymentEditAdditionalsToolbar', 241);
INSERT INTO auth_path (id, path, auth_permission) VALUES (265, '', 241);

-- Trigger to delete
CREATE OR REPLACE FUNCTION ccp_receipt_removal_from_payment_delete() RETURNS trigger AS
$BODY$
    BEGIN
        DELETE FROM ccp_receipt WHERE id = OLD.receipt_id;
        RETURN OLD;
    END;
$BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100;

CREATE TRIGGER ccp_receipt_removal_from_payment_delete AFTER DELETE ON ccp_payment
    FOR EACH ROW EXECUTE PROCEDURE ccp_receipt_removal_from_payment_delete();

-- New bank profile fields and fixes
ALTER TABLE bank_profile RENAME TO core_bank_account;

ALTER SEQUENCE bank_profile_id_seq RENAME TO core_bank_account_id_seq;

ALTER TABLE core_bank_account ALTER COLUMN bban SET NOT NULL;
ALTER TABLE core_bank_account ALTER COLUMN denomination SET NOT NULL;
ALTER TABLE core_bank_account ADD COLUMN ise_id integer NOT NULL DEFAULT 0;
ALTER TABLE core_bank_account ADD COLUMN ise_type character varying(1) NOT NULL DEFAULT 'I';

ALTER TABLE core_bank_account DROP CONSTRAINT bank_profile_type_check;
ALTER TABLE core_bank_account ADD CONSTRAINT core_bank_account_type_check CHECK (type::text = ANY (ARRAY['P'::character varying::text, 'B'::character varying::text]));
ALTER TABLE core_bank_account ADD CONSTRAINT core_bank_account_ise_type_check CHECK (ise_type::text = ANY (ARRAY['I'::character varying::text, 'S'::character varying::text, 'E'::character varying::text]));

COMMENT ON COLUMN core_bank_account.type IS 'P - Post, B - Bank';
COMMENT ON COLUMN core_bank_account.ise_type IS 'I - Institute, S - Supplier, E - Employee';

-- Transfer linked institute id from linkking table to bank account table
UPDATE core_bank_account SET ise_id = institute_bank_profile.institute FROM institute_bank_profile WHERE core_bank_account.id = institute_bank_profile.bank_profile;

-- Removal of obsolete table
DROP TABLE institute_bank_profile;

-- Removal of unnecessary fields "default" property
ALTER TABLE core_bank_account ALTER COLUMN type DROP DEFAULT;
ALTER TABLE core_bank_account ALTER COLUMN ise_id DROP DEFAULT;
ALTER TABLE core_bank_account ALTER COLUMN ise_type DROP DEFAULT;

-- Insertion of CCP account
INSERT INTO core_bank_account (denomination, bban, type, ise_id, ise_type) VALUES ('Conto Corrente Postale', COALESCE((SELECT postal_account FROM institute WHERE def = 't' LIMIT 1), 0)::TEXT, 'P', (SELECT institute_id FROM institute WHERE def = 't' LIMIT 1), 'I');

UPDATE ccp_payment set account_id='ccp_0' where account_id is null or account_id='';

-- Fix account_id value to the corresponding real (Integer) id
UPDATE ccp_payment SET account_id = (
    SELECT
        CASE account_id
            WHEN 'ccp_0' THEN
                (SELECT id FROM core_bank_account WHERE denomination = 'Conto Corrente Postale')::text
            ELSE
                substring(account_id FROM (position('_' IN account_id) + 1))
        END
);
ALTER TABLE ccp_payment ALTER COLUMN account_id TYPE integer USING(account_id::integer);

-- Foreign key constraint for payments
ALTER TABLE ccp_payment ADD CONSTRAINT ccp_payment_payment_account_id_fkey FOREIGN KEY (account_id)
    REFERENCES core_bank_account (id) MATCH SIMPLE
    ON UPDATE CASCADE ON DELETE NO ACTION;

-- Audit table
CREATE TABLE audit.core_bank_account
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   country_code character varying(2),
   check_code character varying(2),
   bban character varying(100) NOT NULL,
   denomination character varying(100) NOT NULL,
   initial_balance double precision NOT NULL,
   type character varying(1) NOT NULL,
   ise_id integer NOT NULL,
   ise_type character varying(1) NOT NULL
);

CREATE TRIGGER core_bank_account_audit
    AFTER INSERT OR UPDATE OR DELETE ON core_bank_account
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Triggers to delete a bank account after an ise record deletion
CREATE OR REPLACE FUNCTION core_bank_account_removal_by_ise_delete() RETURNS trigger AS
$BODY$
    BEGIN
        CASE TG_TABLE_NAME
            WHEN 'institute' THEN DELETE FROM core_bank_account WHERE ise_id = OLD.institute_id AND ise_type = 'I';
            WHEN 'wh_suppliers' THEN DELETE FROM core_bank_account WHERE ise_id = OLD.id AND ise_type = 'S';
            WHEN 'employee' THEN DELETE FROM core_bank_account WHERE ise_id = OLD.employee_id AND ise_type = 'E';
        END CASE;
        RETURN OLD;
    END;
$BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100;

CREATE TRIGGER core_bank_account_removal_by_institute_delete AFTER DELETE ON institute
    FOR EACH ROW EXECUTE PROCEDURE core_bank_account_removal_by_ise_delete();

CREATE TRIGGER core_bank_account_removal_by_supplier_delete AFTER DELETE ON wh_suppliers
    FOR EACH ROW EXECUTE PROCEDURE core_bank_account_removal_by_ise_delete();

CREATE TRIGGER core_bank_account_removal_by_employee_delete AFTER DELETE ON employee
    FOR EACH ROW EXECUTE PROCEDURE core_bank_account_removal_by_ise_delete();

-- Payments View
CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.movement_id, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.number AS movement_number, m.subject_type, m.subject_id, m.subject_data, m.type_id, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

-- Receipts View
CREATE OR REPLACE VIEW ccp_view_receipt AS
 SELECT r.id, r.number, r.date, ( SELECT count(ccp_payment.id) AS count
           FROM ccp_payment
          WHERE ccp_payment.receipt_id = r.id) AS count_payments, ( SELECT string_agg(ccp_payment.id::text, ','::text) AS string_agg
           FROM ccp_payment
          WHERE ccp_payment.receipt_id = r.id) AS linked_payments, (( SELECT COALESCE(sum(pay.total)::double precision, 0::double precision) AS sum
           FROM ( SELECT pa.id, pa.receipt_id, (pa.amount::double precision + pa.positive_additionals_euro::double precision - pa.negative_additionals_euro::double precision + pa.amount::double precision / 100::double precision * pa.positive_additionals_perc - pa.amount::double precision / 100::double precision * pa.negative_additionals_perc)::numeric(14,2) AS total
                   FROM ( SELECT p.id, p.receipt_id, p.amount::numeric(14,2) AS amount, (p.payer_surname::text || ' '::text) || p.payer_name::text AS payer_data, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                           FROM ccp_payment p) pa) pay
          WHERE pay.receipt_id = r.id))::numeric(14,2) AS total
   FROM ccp_receipt r;

-- View Payment Account

-- DROP VIEW ccp_view_payment_account;

CREATE OR REPLACE VIEW ccp_view_payment_account AS
 SELECT pp.year, pp.id, pp.text, count(pp.payment_id) AS elements, sum(pp.total_positive) AS credit, sum(pp.total_negative) AS debit
   FROM ( SELECT pv.payment_id, date_part('year'::text, to_timestamp(pv.operation_date::double precision)) AS year, pv.id, pv.text, pv.incoming,
                CASE
                    WHEN pv.incoming = true THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN pv.incoming = false THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_negative
           FROM ( SELECT p.id AS payment_id, p.operation_date, p.accountable_date, p.amount::numeric(14,2) AS amount, p.account_id AS id, a.denomination AS text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_perc, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_perc
                   FROM ccp_payment p, core_bank_account a, ccp_movement m, ccp_type t
                  WHERE p.movement_id = m.id AND m.type_id = t.id AND p.account_id = a.id) pv) pp
  GROUP BY pp.year, pp.id, pp.text
  ORDER BY pp.year DESC, pp.text;

-- View Payment Category

-- DROP VIEW ccp_view_payment_category;

CREATE OR REPLACE VIEW ccp_view_payment_category AS
 SELECT pp.year, pp.id, pp.text, count(pp.payment_id) AS elements, sum(pp.total_positive) AS credit, sum(pp.total_negative) AS debit
   FROM ( SELECT pv.payment_id, date_part('year'::text, to_timestamp(pv.operation_date::double precision)) AS year, pv.id, pv.text, pv.incoming,
                CASE
                    WHEN pv.incoming = true THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN pv.incoming = false THEN ((pv.amount + pv.positive_additionals_euro - pv.negative_additionals_euro)::double precision + pv.amount::double precision / 100::double precision * pv.positive_additionals_perc::double precision - pv.amount::double precision / 100::double precision * pv.negative_additionals_perc::double precision)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_negative
           FROM ( SELECT p.id AS payment_id, p.operation_date, p.accountable_date, p.amount::numeric(14,2) AS amount, t.category_id AS id, c.name AS text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_perc, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                           FROM ccp_payment_additional
                          WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_perc
                   FROM ccp_payment p, ccp_movement m, ccp_type t, ccp_category c
                  WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id) pv) pp
  GROUP BY pp.year, pp.id, pp.text
  ORDER BY pp.year DESC, pp.text;

-- View Movement Category

DROP VIEW ccp_view_movement_category;

CREATE OR REPLACE VIEW ccp_view_movement_category AS
 SELECT mm.year, mm.id, mm.text, count(mm.movement_id) AS elements, sum(mm.total_positive) AS credit, sum(mm.total_negative) AS debit
   FROM ( SELECT mv.movement_id, date_part('year'::text, to_timestamp(mv.creation_date::double precision)) AS year, mv.id, mv.text, mv.incoming,
                CASE
                    WHEN mv.incoming = true THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_positive,
                CASE
                    WHEN mv.incoming = false THEN (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
                    ELSE 0::numeric(14,2)
                END AS total_negative
           FROM ( SELECT m.id AS movement_id, m.amount, m.creation_date, t.incoming, c.id, c.name AS text, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                           FROM ccp_movement_additional
                          WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                                   FROM ccp_additional
                                  WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                   FROM ccp_movement m, ccp_type t, ccp_category c
                  WHERE m.type_id = t.id AND t.category_id = c.id) mv) mm
  GROUP BY mm.year, mm.id, mm.text
  ORDER BY mm.year DESC, mm.text;

-- Fix CCP Permissions
UPDATE auth_element SET control_interface = 'CcpLinkedPaymentsToolbar' WHERE id = 261;
UPDATE auth_element SET control_interface = 'CcpPaymentsPnl' WHERE id = 250;

SELECT * FROM mt_str_replace(cerca := 'Conti Correnti', sostituisci := 'Conti Corrente', tabella := 'auth_permission', campo := 'title', modifica := true) AS (tabella text, colonna text, condizione text, trova text, sostituisci text);

-- New CCP Permissions
-- Payments
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (266, 'contextCcpLinkedPaymentEdit', 'CcpLinkedPaymentsMn', 262);
INSERT INTO auth_path (id, path, auth_permission) VALUES (266, '', 262);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (267, 'contextCcpLinkedPaymentDelete', 'CcpLinkedPaymentsMn', 263);
INSERT INTO auth_path (id, path, auth_permission) VALUES (267, '', 263);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (268, 'contextCcpLinkedPaymentPrints', 'CcpLinkedPaymentsMn', 264);
INSERT INTO auth_path (id, path, auth_permission) VALUES (268, '', 264);

-- Receipts
INSERT INTO auth_permission (id, title, auth_section) VALUES (270, 'Conti Corrente | Ricevute | Emettere', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (270, 'CcpReceiptNewBtn', 'CcpReceiptsPnl', 270);
INSERT INTO auth_path (id, path, auth_permission) VALUES (270, '', 270);

INSERT INTO auth_permission (id, title, auth_section) VALUES (271, 'Conti Corrente | Ricevute | Eliminare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (271, 'contextCcpReceiptDelete', 'CcpReceiptsMn', 271);
INSERT INTO auth_path (id, path, auth_permission) VALUES (271, '', 271);

INSERT INTO auth_permission (id, title, auth_section) VALUES (272, 'Conti Corrente | Ricevute | Stampare', 8);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (272, 'contextCcpReceiptPrint', 'CcpReceiptsMn', 272);
INSERT INTO auth_path (id, path, auth_permission) VALUES (272, '', 272);

-- Comment for mt_string_replace function
COMMENT ON FUNCTION mt_str_replace(text, text, text, text, boolean) IS 'Procedura Pg/PLSQL

Descrizione: La funzione effettua un replace di una stringa contenuta in un qualsiasi campo di testo presente in tutte le tabelle del DBMS.
mt_str_replace(cerca, sostituisci, tabella, campo, modifica);

cerca:            la stringa da cercare per la sostituzione.
sostituisci:   la stringa per la sostituzione.
tabella:         nome_tabella | null  specifica il nome della tabella nella quale cercare/modificare. Se null cerca/modifica su tutte le tabelle.
campo:         nome_campo-colonna | null  specifica il nome della colonna nella quale cercare/modificare. Se null cerca/modifica su tutte le colonne della tabella specificata.
modifica:      true | false se effettuare l''update o no.

Esempio:
SELECT * FROM
mt_str_replace(''cerca'', ''sostituisci'', null, null, false) AS (tabella text, colonna text, condizione text, trova text, sostituisci text);

Autore:        Basso Alberto <<EMAIL>>
Data:            19/12/2011
Nota:            Per abilitare Pl/PgSQL eseguire (LANCOMPILER opzionale): CREATE PROCEDURAL LANGUAGE ''plpgsql'' HANDLER plpgsql_call_handler LANCOMPILER ''PL/PgSql internal'';';

-- New Version parameters
INSERT INTO parameter (name, value) VALUES ('VERSION_MIDDLE', '3.2.10');
INSERT INTO parameter (name, value) VALUES ('VERSION_DOCS', '3.1.9');

-- Update to existing Version parameters
UPDATE parameter SET value = '0.19.0' WHERE name = 'VERSION_OLD';
UPDATE parameter SET value = '3.2.10' WHERE name = 'VERSION_UI';

-- Update to API Root parameter
UPDATE parameter SET value = '/api/1.3' WHERE name = 'API_ROOT';


UPDATE parameter SET value = '3.2.12' WHERE name = 'VERSION';

UPDATE ccp_payment_method SET name = 'Home Banking' WHERE name = 'Home banking';
UPDATE ccp_payment_method SET name = 'Carta Prepagata' WHERE name = 'Carta prepagata';
UPDATE ccp_payment_method SET name = 'Carta di Credito' WHERE name = 'Carta di credito';
UPDATE ccp_payment_method SET name = 'Bollettino Postale' WHERE name = 'Bollettino postale';


CREATE INDEX ON ccp_payment (movement_id);


INSERT INTO parameter (name, value) VALUES ('CCP_NUMBER_AUTOINCREMENT', 'f');


ALTER TABLE public.ccp_payment ALTER COLUMN payer_name SET DEFAULT '';
ALTER TABLE public.ccp_payment ALTER COLUMN payer_surname SET DEFAULT '';
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_name SET DEFAULT '';
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_surname SET DEFAULT '';


UPDATE public.ccp_payment SET payer_name = '' WHERE payer_name IS NULL;
UPDATE public.ccp_payment SET payer_surname = '' WHERE payer_surname IS NULL;
UPDATE audit.ccp_payment SET payer_name = '' WHERE payer_name IS NULL;
UPDATE audit.ccp_payment SET payer_surname = '' WHERE payer_surname IS NULL;

ALTER TABLE public.ccp_payment ALTER COLUMN payer_name SET NOT NULL;
ALTER TABLE public.ccp_payment ALTER COLUMN payer_surname SET NOT NULL;
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_name SET NOT NULL;
ALTER TABLE audit.ccp_payment ALTER COLUMN payer_surname SET NOT NULL;

ALTER TABLE institute_del ADD COLUMN job_registry_id integer;
ALTER TABLE institute_del ADD COLUMN job_warehouse_id integer;
ALTER TABLE institute_del ADD COLUMN job_accounting_id integer;
ALTER TABLE institute_del ADD COLUMN job_personnel_id integer;
ALTER TABLE institute_del ADD COLUMN job_dsga_id integer;
ALTER TABLE institute_del ADD COLUMN job_vice_director_id integer;
ALTER TABLE institute_del ADD COLUMN job_director_id integer;

ALTER TABLE users ADD UNIQUE (user_name);

CREATE TABLE protocol_register
(
  id serial NOT NULL,
  code integer NOT NULL,
  close_date timestamp without time zone,
  creator_software character varying(255) NOT NULL DEFAULT 'MasterCom2'::character varying,
  creator_person character varying(255) NOT NULL,
  recipient character varying(255),
  hash character varying(32) NOT NULL,
  ipa_code character varying(255) NOT NULL,
  school_name character varying(255) NOT NULL,
  area_code integer NOT NULL DEFAULT 1,
  responsible character varying(255) NOT NULL,
  subject character varying(255) NOT NULL DEFAULT 'Registro giornaliero di protocollo'::character varying,
  register_code integer NOT NULL DEFAULT 1,
  register_number integer NOT NULL,
  year integer NOT NULL,
  first_registration_number integer,
  last_registration_number integer,
  first_registration_date timestamp without time zone,
  last_registration_date timestamp without time zone,
  file text NOT NULL DEFAULT ''::text,
  archived timestamp without time zone,
  CONSTRAINT protocol_register_pkey PRIMARY KEY (id),
  CONSTRAINT protocol_register_code_key UNIQUE (code),
  CONSTRAINT protocol_register_register_number_key UNIQUE (register_number)
)
WITH (
  OIDS=FALSE
);

ALTER TABLE institute ADD column ipa_code character varying(255);

ALTER TABLE users ALTER COLUMN employee_id DROP NOT NULL;
ALTER TABLE users ALTER COLUMN employee_id DROP DEFAULT;

UPDATE users set employee_id = NULL where employee_id <= 0;



DROP VIEW ccp_view_payment;

CREATE OR REPLACE VIEW ccp_view_payment AS 
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.type_id, pay.type_text, pay.category_id,  pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;



ALTER TABLE ccp_receipt DROP CONSTRAINT IF EXISTS ccp_receipt_number_key;


INSERT INTO absence_kind (code,description,calc_festivities,calc_ferials) VALUES ('RECFSOAP', 'Recupero festività soppresse anno precedente', false, false);

DROP VIEW ccp_view_payment;

CREATE OR REPLACE VIEW ccp_view_payment AS 
 SELECT pay.id, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.type_id, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.type_id, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;
