CREATE TABLE public.report
(
    id SERIAL,
    date_created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL,
    print_key CHARACTER VARYING(255) NOT NULL,
    print_type CHARACTER VARYING(255) NOT NULL,
    subject_id INTEGER,
    subject_type CHARACTER VARYING(1),
    school_year CHARACTER VARYING(255),
    template_id INTEGER,
    subject_data CHARACTER VARYING(255),
    subject_class CHARACTER VARYING(10),
    subject_school_address_code CHARACTER VARYING(20),
    published BOOLEAN DEFAULT false,
    status CHARACTER VARYING(20),
    print_params TEXT,
    last_result TEXT,
    description CHARACTER VARYING(255),
    filepath CHARACTER VARYING(255),
    report_key CHARACTER VARYING(255)
);


CREATE TABLE audit.report
(
    op_action CHARACTER VARYING(1) NOT NULL,
    op_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    id BIGINT NOT NULL,
    date_created TIMES<PERSON>MP WITH TIME ZONE NOT NULL,
    print_key CHARACTER VARYING(255) NOT NULL,
    print_type CHARACTER VARYING(255) NOT NULL,
    subject_id INTEGER,
    subject_type CHARACTER VARYING(1),
    school_year CHARACTER VARYING(255),
    template_id INTEGER,
    subject_data CHARACTER VARYING(255),
    subject_class CHARACTER VARYING(10),
    subject_school_address_code CHARACTER VARYING(20),
    published BOOLEAN DEFAULT false,
    status CHARACTER VARYING(20),
    print_params TEXT,
    last_result TEXT,
    description CHARACTER VARYING(255),
    filepath CHARACTER VARYING(255),
    report_key CHARACTER VARYING(255)
);


CREATE TRIGGER report_audit
    AFTER INSERT OR UPDATE OR DELETE ON report
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();
