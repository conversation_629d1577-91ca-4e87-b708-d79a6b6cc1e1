-- Tabelle generiche per esportazioni verso software esterni

CREATE TABLE external_software (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL
);

INSERT INTO external_software (name) VALUES ('Mexal');

CREATE TABLE external_export (
    id SERIAL PRIMARY KEY,
    external_software_id INTEGER NOT NULL,
    creation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (external_software_id) REFERENCES external_software(id) ON DELETE CASCADE
);

CREATE TABLE external_export_row (
    id SERIAL PRIMARY KEY,
    external_export_id INTEGER NOT NULL,
    object_type CHARACTER VARYING(255) NOT NULL,
    object_id INTEGER NOT NULL,
    FOREIGN KEY (external_export_id) REFERENCES external_export(id) ON DELETE CASCADE
);

-- Tipo sconto con calcolo sull'imponibile (on_gross = true) o sul netto (on_gross = false)

ALTER TABLE ccp_additional ADD COLUMN on_gross BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE ccp_additional ADD COLUMN codice_conto CHARACTER VARYING(255);
ALTER TABLE audit.ccp_additional ADD COLUMN on_gross BOOLEAN;
ALTER TABLE audit.ccp_additional ADD COLUMN codice_conto CHARACTER VARYING(255);

