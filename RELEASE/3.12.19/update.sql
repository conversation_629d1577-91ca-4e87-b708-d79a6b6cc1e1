drop view ccp_view_receipt;
CREATE OR REPLACE VIEW ccp_view_receipt AS
 SELECT r.id, (select subject_id from ccp_movement where id  in (SELECT movement_id from ccp_payment where receipt_id = r.id limit 1)) as subject_id,r.number, r.date, ( SELECT count(ccp_payment.id) AS count
           FROM ccp_payment
          WHERE ccp_payment.receipt_id = r.id) AS count_payments, ( SELECT string_agg(ccp_payment.id::text, ','::text) AS string_agg
           FROM ccp_payment
          WHERE ccp_payment.receipt_id = r.id) AS linked_payments, (( SELECT COALESCE(sum(pay.total)::double precision, 0::double precision) AS sum
           FROM ( SELECT pa.id, pa.receipt_id, (pa.amount::double precision + pa.positive_additionals_euro::double precision - pa.negative_additionals_euro::double precision + pa.amount::double precision / 100::double precision * pa.positive_additionals_perc - pa.amount::double precision / 100::double precision * pa.negative_additionals_perc)::numeric(14,2) AS total
                   FROM ( SELECT p.id, p.receipt_id, p.amount::numeric(14,2) AS amount, (p.payer_surname::text || ' '::text) || p.payer_name::text AS payer_data, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                                   FROM ccp_payment_additional
                                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                                           FROM ccp_additional
                                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc
                           FROM ccp_payment p) pa) pay
          WHERE pay.receipt_id = r.id))::numeric(14,2) AS total
   FROM ccp_receipt r;

INSERT INTO parameter (name, value) VALUES ('PRINT_CLASS_DECALARATION', 'Declaration');
INSERT INTO parameter (name, value) VALUES ('CCP_DECLARATION_PREFIX_NUMBER', '');
INSERT INTO parameter (name, value) VALUES ('CCP_DECLARATION_FOOTER', 'Dichiarazione rilasciata ai fini delle detrazioni previste dall''articolo 15, co. 1, lett. e-bis del TUIR');

