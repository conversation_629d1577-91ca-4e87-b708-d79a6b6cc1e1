#!/bin/bash

###################################################################################################
#                                                                                                 #
# Disables site if it is not                                                                      #
#                                                                                                 #
###################################################################################################

if [ -L /etc/apache2/sites-enabled/master-$APP_NAME.conf ];
then
    echo "Disabling apache site configuration for package master-"$APP_NAME
    a2dissite master-$APP_NAME.conf && service apache2 reload
fi
