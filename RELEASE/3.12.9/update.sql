CREATE TABLE public.ccp_vat_code
(
    id serial PRIMARY KEY,
    code character varying(20),
    description character varying(255),
    amount double precision NOT NULL DEFAULT 0.00,
    exemption boolean DEFAULT 't'
);

CREATE TABLE audit.ccp_vat_code
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer,
    code character varying(20),
    description character varying(255),
    amount double precision NOT NULL DEFAULT 0.00,
    exemption boolean
);

CREATE TRIGGER ccp_vat_code
    AFTER INSERT OR UPDATE OR DELETE ON ccp_vat_code
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();



-- inserimento codice esenzioni iva
INSERT INTO ccp_vat_code
(code, description, exemption) VALUES
('EX ART 15', 'Operazioni escluse ex articolo 15', 't'),
('NON SOG', 'Operazioni non soggette', 't'),
('NON IMP', 'Operazioni non imponibili', 't'),
('ART 10', 'Operazioni esenti articolo 10', 't'),
('MARGINE', 'Operazioni nel regime del margine', 't'),
('REVCHRG', 'Operazioni in "Reverse Charge" articolo 17 c.6 lett. a-ter', 't'),
('UE', 'IVA assolta in altro stato UE (ex art. 40 c. 3 e 4 e art. 41 c.1 lett. b, DL 331/93; ex art. 7-sexies let', 't');

ALTER TABLE public.ccp_type
    ADD COLUMN bollo boolean NOT NULL DEFAULT false;


ALTER TABLE public.ccp_type
    ADD COLUMN vat_code_id integer;


ALTER TABLE audit.ccp_type
    ADD COLUMN bollo boolean NOT NULL DEFAULT false;


ALTER TABLE audit.ccp_type
    ADD COLUMN vat_code_id integer;


