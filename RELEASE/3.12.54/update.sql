ALTER TABLE ccp_movement DROP COLUMN matthaeus_id;
ALTER TABLE ccp_movement DROP COLUMN matthaeus_year;
ALTER TABLE ccp_movement DROP COLUMN last_update;
ALTER TABLE ccp_movement DROP COLUMN last_sync;
ALTER TABLE audit.ccp_movement DROP COLUMN matthaeus_id;
ALTER TABLE audit.ccp_movement DROP COLUMN matthaeus_year;
ALTER TABLE audit.ccp_movement DROP COLUMN last_update;
ALTER TABLE audit.ccp_movement DROP COLUMN last_sync;

ALTER TABLE ccp_payment DROP COLUMN matthaeus_id;
ALTER TABLE ccp_payment DROP COLUMN last_update;
ALTER TABLE ccp_payment DROP COLUMN last_sync;
ALTER TABLE audit.ccp_payment DROP COLUMN matthaeus_id;
ALTER TABLE audit.ccp_payment DROP COLUMN last_update;
ALTER TABLE audit.ccp_payment DROP COLUMN last_sync;

DROP TRIGGER movement_last_update_refresh ON ccp_movement ;
DROP TRIGGER payment_last_update_refresh ON ccp_payment ;
DROP FUNCTION movement_last_update_refresh();
DROP FUNCTION payment_last_update_refresh();

CREATE TABLE magister_sync (
    id serial not null,
    obj_id integer not null,
    obj_type character(1), --- m=movement   p=payment i=add 
    amount double precision,  --- "+" o "-" identifica i piani dei conti da usare
    avere_code character varying(255) NOT NULL,
    dare_code character varying(255) NOT NULL,
    description text,

    sent_date timestamp with time zone,
    creation_date timestamp with time zone not null default NOW(),
    error text
);

INSERT INTO parameter (name,value) VALUES ('MATTHAEUS_CONTO_FATTURE_DA_EMETTERE', '10.01.0001');
INSERT INTO parameter (name,value) VALUES ('MATTHAEUS_CONTO_FATTURE_EMESSE', '09.01.0001');
