-- Drops obsolete table and function.
DROP TABLE IF EXISTS gate_presences_del;
DROP FUNCTION IF EXISTS public.plpgsql_call_handler();


-- Updates tables
ALTER TABLE wh_order ALTER COLUMN description TYPE text;
ALTER TABLE wh_order_del ALTER COLUMN description TYPE text;


-- Adds the new tables and fields needed for the stacks reset logic
CREATE TABLE personnel_stacks
(
  id serial NOT NULL,
  employee_id bigint NOT NULL DEFAULT (-1),
  stack_id bigint NOT NULL DEFAULT (-1),
  reset_quota float NOT NULL DEFAULT 0.00,
  CONSTRAINT personnel_stacks_pkey PRIMARY KEY (id ),
  CONSTRAINT personnel_stacks_absence_stacks_fkey FOREIGN KEY (stack_id)
      REFERENCES absence_stack (id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE,
  CONSTRAINT personnel_stacks_employee_fkey FOREIGN KEY (employee_id)
      REFERENCES employee (employee_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE
)
WITH (
  OIDS=FALSE
);
COMMENT ON TABLE personnel_stacks IS 'Parameters of each stack per personnel.';
COMMENT ON COLUMN personnel_stacks.reset_quota IS 'The quota to be used to Add/Substract/Reset to the current value of the stack for this employee. It expresses minutes or hours based on the unit of the stack.';


CREATE TABLE audit.personnel_stacks (
  op_action character varying(1) NOT NULL,
  op_date timestamp with time zone NOT NULL DEFAULT now(),
  id bigint NOT NULL,
  employee_id bigint NOT NULL DEFAULT (-1),
  stack_id bigint NOT NULL DEFAULT (-1),
  reset_quota float NOT NULL DEFAULT 0.00
)
WITH (
  OIDS=FALSE
);
COMMENT ON TABLE audit.personnel_stacks IS 'Audit table for relation personnel_stacks.';


CREATE TRIGGER personnel_stacks_audit AFTER INSERT OR UPDATE OR DELETE ON personnel_stacks FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Alters tables for stacks reset logic
ALTER TABLE absence_stack ADD COLUMN reset_type smallint NOT NULL DEFAULT 0;
ALTER TABLE absence_stack ADD COLUMN reset_date timestamp without time zone;
ALTER TABLE absence_stack ADD COLUMN reset_to_stack_id bigint;
ALTER TABLE absence_stack ADD COLUMN reset_default_quota float NOT NULL DEFAULT 0.00;

ALTER TABLE storage_personnel_stack ADD COLUMN reset_type_applied smallint NOT NULL DEFAULT 0;

ALTER TABLE audit.absence_stack ADD COLUMN reset_type smallint NOT NULL DEFAULT 0;
ALTER TABLE audit.absence_stack ADD COLUMN reset_date timestamp without time zone;
ALTER TABLE audit.absence_stack ADD COLUMN reset_to_stack_id bigint;
ALTER TABLE audit.absence_stack ADD COLUMN reset_default_quota float NOT NULL DEFAULT 0.00;

ALTER TABLE audit.storage_personnel_stack ADD COLUMN reset_type_applied smallint NOT NULL DEFAULT 0;

COMMENT ON COLUMN absence_stack.reset_type IS 'Type of reset (0 = Manual, 1 = Reset - Yearly, 2 = Reset - Monthly, 3 = Addition - Yearly, 4 = Addition - Monthly, 5 = Substraction - Yearly, 6 = Substraction - Monthly).';
COMMENT ON COLUMN absence_stack.reset_date IS 'Month at which to perform the reset procedure.';
COMMENT ON COLUMN absence_stack.reset_to_stack_id IS 'Stack to pass the current value of this stack to, upon reset.';
COMMENT ON COLUMN absence_stack.reset_default_quota IS 'Default quota to assign to each personnel for this stack.';

ALTER TABLE absence_stack ADD CONSTRAINT absence_stack_absence_stack_fkey FOREIGN KEY (reset_to_stack_id) REFERENCES absence_stack (id) ON UPDATE CASCADE ON DELETE SET NULL;
COMMENT ON CONSTRAINT absence_stack_absence_stack_fkey ON absence_stack IS 'References the stack this stack is bound to.';
CREATE INDEX fki_absence_stack_absence_stack_fkey ON absence_stack(reset_to_stack_id);


-- Sets FERIE ANNO PRECEDENTE stack to reset into FERIE stack only if the two Kinds are not in the same stack
UPDATE absence_stack SET reset_to_stack_id = (SELECT absence_stack FROM absence_kind WHERE code = 'FERAPR' LIMIT 1) WHERE id = (SELECT absence_stack FROM absence_kind WHERE code = 'FERIE' LIMIT 1) AND id != (SELECT absence_stack FROM absence_kind WHERE code = 'FERAPR' LIMIT 1);


-- Adds new permissions
-- Stacks
INSERT INTO auth_permission(id, title, auth_section) VALUES (153, 'Personale | Parametri | Quote | Modificare', 5);
INSERT INTO auth_element(id, name, control_interface, auth_permission) VALUES (153, 'contextPersonnelStackLinkEdit', 'EmpSettingsStacksMn', 153);
INSERT INTO auth_path(id, path, auth_permission) VALUES (153, '', 153);
INSERT INTO auth_permission(id, title, auth_section) VALUES (154, 'Personale | Parametri | Quote | Copiare', 5);
INSERT INTO auth_element(id, name, control_interface, auth_permission) VALUES (154, 'contextPersonnelStackLinkCopy', 'EmpSettingsStacksMn', 154);
INSERT INTO auth_path(id, path, auth_permission) VALUES (154, '', 154);
INSERT INTO auth_permission(id, title, auth_section) VALUES (155, 'Personale | Parametri | Quote | Reset', 5);
INSERT INTO auth_element(id, name, control_interface, auth_permission) VALUES (155, 'contextPersonnelStackLinkReset', 'EmpSettingsStacksMn', 155);
INSERT INTO auth_path(id, path, auth_permission) VALUES (155, '', 155);
-- Receipt print
INSERT INTO auth_permission(id, title, auth_section) VALUES (204, 'Conti correnti | Movimenti | Stampe singole', 8);
INSERT INTO auth_element(id, name, control_interface, auth_permission) VALUES (204, 'contextTaxPrints', 'TaxEditMn', 204);
INSERT INTO auth_path(id, path, auth_permission) VALUES (204, '', 204);