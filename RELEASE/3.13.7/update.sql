INSERT INTO parameter (name, value) VALUES ('EASY_PIANO_CONTI_INCASSI', '');


ALTER TABLE core_bank_account ADD COLUMN piano_conti CHARACTER VARYING(255);
ALTER TABLE audit.core_bank_account ADD COLUMN piano_conti CHARACTER VARYING(255);

ALTER TABLE ccp_payment_method ADD COLUMN easy_by_bank BOOLEAN DEFAULT 'f';
ALTER TABLE ccp_payment_method ADD COLUMN easy_export_grouped BOOLEAN DEFAULT 't';
ALTER TABLE audit.ccp_payment_method ADD COLUMN easy_by_bank BOOLEAN DEFAULT 'f';
ALTER TABLE audit.ccp_payment_method ADD COLUMN easy_export_grouped BOOLEAN DEFAULT 't';

