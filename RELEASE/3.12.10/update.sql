-- Cancella valori presenti in ccp_vat_code

DELETE FROM ccp_vat_code;

-- add SDI_CODE in ccp_vat_code
ALTER TABLE public.ccp_vat_code
    ADD COLUMN sdi_code character varying(255);

ALTER TABLE audit.ccp_vat_code
    ADD COLUMN sdi_code character varying(255);

-- aggiungi righe esenzioni iva con codice sdi
INSERT INTO ccp_vat_code
(code, sdi_code, description, exemption) VALUES
('EX ART 15', 'N1', 'Operazioni escluse ex articolo 15', 't'),
('NON SOG', 'N2', 'Operazioni non soggette', 't'),
('NON IMP', 'N3',  'Operazioni non imponibili', 't'),
('ART 10', 'N4', 'Operazioni esenti articolo 10', 't'),
('MARGINE', 'N5', 'Operazioni nel regime del margine', 't'),
('REVCHRG', 'N6', '<PERSON><PERSON><PERSON> in "Reverse Charge" articolo 17 c.6 lett. a-ter', 't'),
('UE', 'N7', 'IVA assolta in altro stato UE (ex art. 40 c. 3 e 4 e art. 41 c.1 lett. b, DL 331/93; ex art. 7-sexies let', 't');