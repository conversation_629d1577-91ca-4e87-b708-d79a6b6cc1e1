INSERT INTO parameter(name, value) VALUES ('EASY_CONTI_RICAVI_RISCONTI', 'f');


ALTER TABLE ccp_type_step
    ADD COLUMN codice_conto_ricavi VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_risconti VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_crediti VARCHAR(255) DEFAULT NULL;


ALTER TABLE ccp_movement
    ADD COLUMN codice_conto_ricavi VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_risconti VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_crediti VARCHAR(255) DEFAULT NULL;


ALTER TABLE audit_ccp_movement
    ADD COLUMN codice_conto_ricavi VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_risconti VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_crediti VARCHAR(255) DEFAULT NULL;

ALTER TABLE audit_ccp_type_step
    ADD COLUMN codice_conto_ricavi VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_risconti VARCHAR(255) DEFAULT NULL,
    ADD COLUMN codice_conto_crediti VARCHAR(255) DEFAULT NULL;