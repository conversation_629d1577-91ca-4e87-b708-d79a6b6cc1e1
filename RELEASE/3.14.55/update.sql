update ccp_invoice_deposit_slip set iban = (select REPLACE(REPLACE((accountholder::json->0->'iban')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
update ccp_invoice_deposit_slip set codice_rid = (select REPLACE(REPLACE((accountholder::json->0->'codice_rid')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
update ccp_invoice_deposit_slip set data_mandato_rid = (select REPLACE(REPLACE((accountholder::json->0->'data_mandato_rid')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
update ccp_invoice_deposit_slip set first_sepa = (select REPLACE(REPLACE((accountholder::json->0->'first_sepa')::text,'"',''),' ','') from ccp_invoice where id= ccp_invoice_deposit_slip.ccp_invoice);
