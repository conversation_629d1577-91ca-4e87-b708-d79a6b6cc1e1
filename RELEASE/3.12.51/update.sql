-- COLLECTION COST ON BANK ACCOUNT
ALTER TABLE core_bank_account ADD COLUMN collection_cost DOUBLE PRECISION NOT NULL DEFAULT 0.00;
ALTER TABLE audit.core_bank_account ADD COLUMN collection_cost DOUBLE PRECISION NOT NULL DEFAULT 0.00;
UPDATE core_bank_account SET collection_cost = (SELECT value FROM parameter WHERE name = 'COLLECTION_COST' LIMIT 1)::DOUBLE PRECISION;
DELETE FROM parameter WHERE name = 'COLLECTION_COST';
-- END COLLECTION COST ON BANK ACCOUNT

-- CREDIT TYPE DOTE FIELD AN CLEAN USELESS FIELDS IN CREDITS
ALTER TABLE ccp_credits_type DROP COLUMN dote;
ALTER TABLE audit.ccp_credits_type DROP COLUMN dote;
ALTER TABLE ccp_credits_type DROP COLUMN subject_type;
ALTER TABLE audit.ccp_credits_type DROP COLUMN subject_type;

ALTER TABLE ccp_credits_type ADD COLUMN dote INTEGER NOT NULL DEFAULT 0;
ALTER TABLE audit.ccp_credits_type ADD COLUMN dote INTEGER NOT NULL DEFAULT 0;

ALTER TABLE  audit.ccp_credits DROP CONSTRAINT IF EXISTS ccp_credits_pkey;
ALTER TABLE  audit.ccp_credits ALTER COLUMN id DROP DEFAULT ;
UPDATE ccp_credits set dote=0 where dote is null;
UPDATE ccp_credits_type ct set dote=(
    SELECT case when (SELECT count(*) from ccp_credits c where c.credit_type_id=ct.id) > 0 THEN (SELECT c.dote from ccp_credits c where c.credit_type_id=ct.id order by c.dote desc limit 1)
    ELSE 0 END
);

ALTER TABLE ccp_credits DROP COLUMN description;
ALTER TABLE audit.ccp_credits DROP COLUMN description;
ALTER TABLE ccp_credits DROP COLUMN student_id;
ALTER TABLE audit.ccp_credits DROP COLUMN student_id;
ALTER TABLE ccp_credits DROP COLUMN amount;
ALTER TABLE audit.ccp_credits DROP COLUMN amount;
ALTER TABLE ccp_credits DROP COLUMN dote;
ALTER TABLE audit.ccp_credits DROP COLUMN dote;

-- END CREDIT TYPE DOTE FIELD AN CLEAN USELESS FIELDS IN CREDITS