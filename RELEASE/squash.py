#!/usr/bin/env python3

import os

# This script is used to squash filtered release in the current folder.
# Every folder with [0-9]*.[0-9]*.[0-9]* is a folder that contains a release.

# Create a sorted list with all the folders that contain a release (regex [0-9]*.[0-9]*.[0-9]*) and sort by number
folders = sorted([folder for folder in os.listdir() if os.path.isdir(folder) and folder.count(".") == 2 and all([part.isdigit() for part in folder.split(".")])], key=lambda x: tuple(map(int, x.split("."))))

print("Releases in current folders:")
print(folders)

# Read from prompt the starting folder (version) to squash
start = input("Enter the starting folder (version) to squash: ")

# Read from prompt the ending folder (version) to squash
end = input("Enter the ending folder (version) to squash: ")

# Verify that the starting folder exists
if start not in folders:
    print(f"The starting folder {start} does not exist")
    exit(1)

# Verify that the ending folder exists
if end not in folders:
    print(f"The ending folder {end} does not exist")
    exit(1)

# Loop and filter between start and end
start = folders.index(start)
end = folders.index(end)
folders = folders[start:end+1]

print("Squashing all the releases in the following folders:")
print(folders)

confirm = input("Confirm? [y/n] ")
if confirm != "y":
    print("Aborted")
    exit(0)


# Verify that into the folders there is only one file and the file is update.sql
for folder in folders:
    if len(os.listdir(folder)) != 1:
        print(f"Merge is only possible between folders that contain ONLY the file update.sql. {folder} contains other files")
        exit(1)

    if "update.sql" not in os.listdir(folder):
        print(f"The folder {folder} does not contain the file update.sql")
        exit(1)

# Create the file squash.sql and append all the content of the update.sql files
with open("squash.sql", "w") as squash:
    for folder in folders:
        with open(f"{folder}/update.sql") as update:
            squash.write(f"-- VERSION {folder}\n")
            squash.write(update.read())
            squash.write("\n")

print("Squash completed. The file squash.sql contains the squashed content of the releases")
