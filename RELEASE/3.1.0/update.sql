-- Trasparenza Category
CREATE TABLE trasparenza_voice
(
    id serial NOT NULL,
    title character varying(255) NOT NULL,
    content text,
    reference text,
    parent_voice_id integer,
    published boolean NOT NULL DEFAULT true,
    last_update bigint,
    index integer NOT NULL DEFAULT 99,
    CONSTRAINT trasparenza_voice_pkey PRIMARY KEY (id),
    CONSTRAINT trasparenza_voice_parent_voice_id_fkey
        FOREIGN KEY (parent_voice_id)
        REFERENCES  trasparenza_voice (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

COMMENT ON TABLE trasparenza_voice IS 'Trasparenza voices';

CREATE INDEX trasparenza_voice_parent_voice_id_fki
    ON trasparenza_voice
    USING btree (parent_voice_id);

CREATE TABLE audit.trasparenza_voice
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    content text,
    reference text,
    parent_voice_id integer,
    published boolean NOT NULL,
    last_update bigint,
    index integer NOT NULL
);

CREATE TRIGGER trasparenza_voice_audit
    AFTER INSERT OR UPDATE OR DELETE ON trasparenza_voice
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Trasparenza Voice Document
CREATE TABLE trasparenza_voice_document
(
    voice_id integer NOT NULL,
    document_id integer NOT NULL,
    CONSTRAINT trasparenza_voice_document_pkey PRIMARY KEY (voice_id, document_id),
    CONSTRAINT trasparenza_voice_document_voice_id_fkey
        FOREIGN KEY (voice_id)
        REFERENCES trasparenza_voice (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT trasparenza_voice_document_document_id_fkey
        FOREIGN KEY (document_id)
        REFERENCES archive_document (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX trasparenza_voice_document_voice_id_fki
    ON trasparenza_voice_document
    USING btree (voice_id);

CREATE INDEX trasparenza_voice_document_document_id_fki
    ON trasparenza_voice_document
    USING btree (document_id);

CREATE TABLE audit.trasparenza_voice_document
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    voice_id integer NOT NULL,
    document_id integer NOT NULL
);

-- New Records
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (1, 1, 'Disposizioni Generali', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (2, 2, 'Organizzazione', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (3, 3, 'Consulenti e collaboratori', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (4, 4, 'Personale', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (5, 5, 'Bandi di Concorso', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (6, 6, 'Performance', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (7, 7, 'Enti controllati', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (8, 8, 'Attività e procedimenti', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (9, 9, 'Provvedimenti', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (10, 10, 'Controlli sulle imprese', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (11, 11, 'Bandi di gara e contratti', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (12, 12, 'Sovvenzioni, contributi, sussidi, vantaggi economici', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (13, 13, 'Bilanci', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (14, 14, 'Beni immobili e gestione patrimonio', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (15, 15, 'Controlli e rilievi sull''amministrazione', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (16, 16, 'Servizi erogati', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (17, 17, 'Pagamenti dell''amministrazione', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (18, 18, 'Opere pubbliche', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (19, 19, 'Pianificazione e governo del territorio', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (20, 20, 'Informazioni ambientali', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (21, 21, 'Strutture sanitarie private accreditate', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (22, 22, 'Interventi straordinari e di emergenza', '');
INSERT INTO trasparenza_voice (id, index, title, reference) VALUES (23, 23, 'Altri contenuti', '');

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (24, 1, 'Programma per la Trasparenza e l''Integrità', '', 1);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (25, 2, 'Atti Generali', '', 1);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (26, 3, 'Oneri informativi per cittadini e imprese', '', 1);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (27, 4, 'Scadenzario obblighi amministrativi', '', 1);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (28, 5, 'Burocrazia zero', '', 1);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (29, 6, 'Attestazioni OIV o di struttura analoga', '', 1);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (30, 1, 'Organi di indirizzo politico-amministrativo', '', 2);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (31, 2, 'Sanzioni per mancata comunicazione dei dati', '', 2);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (32, 3, 'Rendiconti gruppi consiliari regionali/provinciali', '', 2);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (33, 4, 'Articolazione degli uffici', '', 2);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (34, 5, 'Telefono e posta elettronica', '', 2);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (35, 1, 'Incarichi amministrativi di vertice', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (36, 2, 'Dirigenti', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (37, 3, 'Posizioni organizzative', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (38, 4, 'Dotazione organica', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (39, 5, 'Personale non a tempo indeterminato', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (40, 6, 'Tassi di assenza', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (41, 7, 'Incarichi conferiti e autorizzati ai dipendenti', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (42, 8, 'Contrattazione collettiva', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (43, 9, 'Contrattazione integrativa', '', 4);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (44, 10, 'OIV', '', 4);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (45, 1, 'Sistema di misurazione e valutazione della Performance', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (46, 2, 'Piano della Performance', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (47, 3, 'Relazione sulla Performance', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (48, 4, 'Documento dell''OIV di validazione della Relazione sulla Performance', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (49, 5, 'Relazione dell''OIV sul funzionamento complessivo del Sistema di valutazione, trasparenza e integrità dei controlli interni', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (50, 6, 'Ammontare complessivo dei premi', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (51, 7, 'Dati relativi ai premi', '', 6);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (52, 8, 'Benessere organizzativo', '', 6);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (53, 1, 'Enti pubblici vigilati', '', 7);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (54, 2, 'Società partecipate', '', 7);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (55, 3, 'Enti di diritto privato controllati', '', 7);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (56, 4, 'Rappresentazione grafica', '', 7);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (57, 1, 'Dati aggregati attività amministrativa', '', 8);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (58, 2, 'Tipologie di procedimento', '', 8);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (59, 3, 'Monitoraggio tempi procedimentali', '', 8);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (60, 4, 'Dichiarazioni sostitutive e acquisizione d''ufficio dei dati', '', 8);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (61, 1, 'Provvedimenti organi indirizzo-politico', '', 9);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (62, 2, 'Provvedimenti dirigenti', '', 9);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (63, 1, 'Criteri e modalità', '', 12);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (64, 2, 'Atti di concessione', '', 12);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (65, 1, 'Bilancio preventivo e consuntivo', '', 13);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (66, 2, 'Piano degli indicatori e risultati attesi di bilancio', '', 13);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (67, 1, 'Patrimonio immobiliare', '', 14);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (68, 2, 'Canoni di locazione o affitto', '', 14);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (69, 1, 'Carta dei servizi e standard di qualità', '', 16);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (70, 2, 'Class action', '', 16);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (71, 3, 'Costi contabilizzati', '', 16);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (72, 4, 'Tempi medi di erogazione dei servizi', '', 16);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (73, 5, 'Liste attesa', '', 16);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (74, 1, 'Indicatore di tempestività dei pagamenti', '', 17);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (75, 2, 'IBAN e pagamenti informatici', '', 17);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (76, 3, 'Elenco debiti scaduti', '', 17);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (77, 4, 'Piano dei pagamenti', '', 17);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (78, 5, 'Elenco debiti comunicati ai creditori', '', 17);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (79, 1, 'Altri contenuti - Corruzione', '', 23);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (80, 2, 'Altri contenuti - Accesso civico', '', 23);
INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (81, 3, 'Altri contenuti - Accessibilità e Catalogo di dati, metadati e banche dati', '', 23);

INSERT INTO trasparenza_voice (id, index, title, reference, parent_voice_id) VALUES (82, 1, 'Scadenzario dei nuovi obblighi amministrativi', '', 26);


-- New permissions

-- Trasparenza
INSERT INTO auth_section (id, title) VALUES (12, 'Trasparenza Amministrativa');

INSERT INTO auth_permission (id, title, auth_section) VALUES (600, 'Trasparenza Amministrativa | Attivo', 12);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (600, 'MenuTrasparenzaCnt', 'MainMenuPnl', 600);
INSERT INTO auth_path (id, path, auth_permission) VALUES (600, '', 600);

INSERT INTO auth_permission (id, title, auth_section) VALUES (601, 'Trasparenza Amministrativa | Voci | Modificare', 12);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (601, 'contextTrasparenzaVoiceEdit', 'TrasparenzaVoiceEditMn', 601);
INSERT INTO auth_path (id, path, auth_permission) VALUES (601, '', 601);

INSERT INTO auth_permission (id, title, auth_section) VALUES (602, 'Trasparenza Amministrativa | Voci | Pubblicare', 12);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (602, 'contextTrasparenzaVoiceToggle', 'TrasparenzaVoiceEditMn', 602);
INSERT INTO auth_path (id, path, auth_permission) VALUES (602, '', 602);

INSERT INTO auth_permission (id, title, auth_section) VALUES (603, 'Trasparenza Amministrativa | Voci | Stampe singole', 12);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (603, 'contextTrasparenzaVoicePrints', 'TrasparenzaVoiceEditMn', 603);
INSERT INTO auth_path (id, path, auth_permission) VALUES (603, '', 603);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (604, 'ArchiveQueueActionTrasparenzaColumn', 'ArchiveQueueTab', 601);
INSERT INTO auth_path (id, path, auth_permission) VALUES (604, '', 601);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (515, 'TrasparenzaLinkedDocumentsDownloadColumn', 'TrasparenzaLinkedDocumentsWin', 504);
INSERT INTO auth_path (id, path, auth_permission) VALUES (515, '', 504);

-- Update web page
INSERT INTO auth_permission (id, title, auth_section) VALUES (610, 'Trasparenza Amministrativa | Aggiorna pagina web', 12);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (610, 'TrasparenzaUpdateWebBtn', 'TrasparenzaToolbar', 610);
INSERT INTO auth_path (id, path, auth_permission) VALUES (610, '', 610);

-- Prints
INSERT INTO auth_permission (id, title, auth_section) VALUES (620, 'Trasparenza Amministrativa | Stampe', 12);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (620, 'TrasparenzaPrintsBtn', 'TrasparenzaToolbar', 620);
INSERT INTO auth_path (id, path, auth_permission) VALUES (620, '', 620);
---- END TRASPARENZA


-- Albo Area
CREATE TABLE albo_area
(
    id serial NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    CONSTRAINT albo_area_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE albo_area IS 'Albo Areas';

CREATE TABLE audit.albo_area
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text
);

CREATE TRIGGER albo_area_audit
    AFTER INSERT OR UPDATE OR DELETE ON albo_area
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Albo Entity
CREATE TABLE albo_entity
(
    id serial NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    CONSTRAINT albo_entity_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE albo_entity IS 'Albo Entities';

CREATE TABLE audit.albo_entity
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text
);

CREATE TRIGGER albo_entity_audit
    AFTER INSERT OR UPDATE OR DELETE ON albo_entity
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Albo Category
CREATE TABLE albo_category
(
    id serial NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    duration smallint NOT NULL DEFAULT 15,
    CONSTRAINT albo_category_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE albo_category IS 'Albo Categories';
COMMENT ON COLUMN albo_category.duration IS 'Default duration of this kind of publication, in days';

CREATE TABLE audit.albo_category
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    duration smallint NOT NULL
);

CREATE TRIGGER albo_category_audit
    AFTER INSERT OR UPDATE OR DELETE ON albo_category
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Albo Publication
CREATE TABLE albo_publication
(
    id serial NOT NULL,
    number integer,
    title character varying(255) NOT NULL,
    description text,
    start_date bigint NOT NULL,
    expiration_date bigint NOT NULL,
    extended_expiration_date bigint,
    publication_date bigint,
    extension_date bigint,
    cancelation_date bigint,
    category_id integer NOT NULL,
    entity_id integer NOT NULL,
    area_id integer NOT NULL,
    omissis boolean NOT NULL DEFAULT false,
    internal boolean NOT NULL DEFAULT false,
    CONSTRAINT albo_publication_pkey PRIMARY KEY (id),
    CONSTRAINT albo_publication_category_id_fkey
        FOREIGN KEY (category_id)
        REFERENCES  albo_category (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT albo_publication_entity_id_fkey
        FOREIGN KEY (entity_id)
        REFERENCES  albo_entity (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT albo_publication_area_id_fkey
        FOREIGN KEY (area_id)
        REFERENCES  albo_area (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

COMMENT ON TABLE albo_publication IS 'Albo Publications';

CREATE INDEX albo_publication_category_id_fki
    ON albo_publication
    USING btree (category_id);

CREATE INDEX albo_publication_entity_id_fki
    ON albo_publication
    USING btree (entity_id);

CREATE INDEX albo_publication_area_id_fki
    ON albo_publication
    USING btree (area_id);

CREATE TABLE audit.albo_publication
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    number integer,
    title character varying(255) NOT NULL,
    description text,
    start_date bigint NOT NULL,
    expiration_date bigint NOT NULL,
    extended_expiration_date bigint,
    publication_date bigint,
    extension_date bigint,
    cancelation_date bigint,
    category_id integer NOT NULL,
    entity_id integer NOT NULL,
    area_id integer NOT NULL,
    omissis boolean NOT NULL,
    internal boolean NOT NULL
);

CREATE TRIGGER albo_publication_audit
    AFTER INSERT OR UPDATE OR DELETE ON albo_publication
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Albo Publication History
CREATE TABLE albo_publication_history
(
    id serial NOT NULL,
    action character varying(1) NOT NULL,
    date bigint NOT NULL,
    publication_id integer NOT NULL,
    user_id integer NOT NULL,
    CONSTRAINT albo_publication_history_pkey PRIMARY KEY (id),
    CONSTRAINT albo_publication_history_publication_id_fkey
        FOREIGN KEY (publication_id)
        REFERENCES albo_publication (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT albo_publication_history_user_id_fkey
        FOREIGN KEY (user_id)
        REFERENCES users (uid)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE SET NULL
);

COMMENT ON TABLE albo_publication_history IS 'Albo Publication History';
COMMENT ON COLUMN albo_publication_history.action IS 'I: Insertion, E: Edit, D: Deletion, C: Cancelation, P: Publication, X: Extension';

CREATE TABLE audit.albo_publication_history
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    action character varying(1) NOT NULL,
    date bigint NOT NULL,
    publication_id integer NOT NULL,
    user_id integer NOT NULL
);

CREATE INDEX albo_publication_history_publication_id_fki
    ON albo_publication_history
    USING btree (publication_id);

CREATE INDEX albo_publication_history_user_id_fki
    ON albo_publication_history
    USING btree (user_id);

CREATE TRIGGER albo_publication_history_audit
    AFTER INSERT OR UPDATE OR DELETE ON albo_publication_history
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Albo Publication Document
CREATE TABLE albo_publication_document
(
    publication_id integer NOT NULL,
    document_id integer NOT NULL,
    CONSTRAINT albo_publication_document_pkey PRIMARY KEY (publication_id, document_id),
    CONSTRAINT albo_publication_document_publication_id_fkey
        FOREIGN KEY (publication_id)
        REFERENCES albo_publication (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT albo_publication_document_document_id_fkey
        FOREIGN KEY (document_id)
        REFERENCES archive_document (id)
        MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX albo_publication_document_publication_id_fki
    ON albo_publication_document
    USING btree (publication_id);

CREATE INDEX albo_publication_document_document_id_fki
    ON albo_publication_document
    USING btree (document_id);

CREATE TABLE audit.albo_publication_document
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    publication_id integer NOT NULL,
    document_id integer NOT NULL
);


-- New permissions

-- Publication
INSERT INTO auth_section (id, title) VALUES (13, 'Albo Pretorio');

INSERT INTO auth_permission (id, title, auth_section) VALUES (700, 'Albo Pretorio | Attivo', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (700, 'MenuAlboCnt', 'MainMenuPnl', 700);
INSERT INTO auth_path (id, path, auth_permission) VALUES (700, '', 700);

INSERT INTO auth_permission (id, title, auth_section) VALUES (701, 'Albo Pretorio | Pubblicazioni | Aggiungere', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (701, 'AlboPublicationNewBtn', 'AlboPublicationsToolbar', 701);
INSERT INTO auth_path (id, path, auth_permission) VALUES (701, '', 701);

INSERT INTO auth_permission (id, title, auth_section) VALUES (702, 'Albo Pretorio | Pubblicazioni | Modificare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (702, 'contextAlboPublicationEdit', 'AlboPublicationEditMn', 702);
INSERT INTO auth_path (id, path, auth_permission) VALUES (702, '', 702);

INSERT INTO auth_permission (id, title, auth_section) VALUES (703, 'Albo Pretorio | Pubblicazioni | Eliminare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (703, 'contextAlboPublicationDelete', 'AlboPublicationEditMn', 703);
INSERT INTO auth_path (id, path, auth_permission) VALUES (703, '', 703);

INSERT INTO auth_permission (id, title, auth_section) VALUES (704, 'Albo Pretorio | Pubblicazioni | Pubblicare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (704, 'contextAlboPublicationPublish', 'AlboPublicationEditMn', 704);
INSERT INTO auth_path (id, path, auth_permission) VALUES (704, '', 704);

INSERT INTO auth_permission (id, title, auth_section) VALUES (705, 'Albo Pretorio | Pubblicazioni | Annullare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (705, 'contextAlboPublicationCancel', 'AlboPublicationEditMn', 705);
INSERT INTO auth_path (id, path, auth_permission) VALUES (705, '', 705);

INSERT INTO auth_permission (id, title, auth_section) VALUES (706, 'Albo Pretorio | Pubblicazioni | Prorogare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (706, 'contextAlboPublicationExtend', 'AlboPublicationEditMn', 706);
INSERT INTO auth_path (id, path, auth_permission) VALUES (706, '', 706);

INSERT INTO auth_permission (id, title, auth_section) VALUES (707, 'Albo Pretorio | Pubblicazioni | Visualizzare storico', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (707, 'contextAlboPublicationHistory', 'AlboPublicationEditMn', 707);
INSERT INTO auth_path (id, path, auth_permission) VALUES (707, '', 707);

INSERT INTO auth_permission (id, title, auth_section) VALUES (708, 'Albo Pretorio | Pubblicazioni | Stampe singole', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (708, 'contextAlboPublicationPrints', 'AlboPublicationEditMn', 708);
INSERT INTO auth_path (id, path, auth_permission) VALUES (708, '', 708);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (709, 'ArchiveQueueActionAlboColumn', 'ArchiveQueueTab', 701);
INSERT INTO auth_path (id, path, auth_permission) VALUES (709, '', 701);

INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (516, 'AlboPublicationLinkedDocumentsDownloadColumn', 'AlboPublicationLinkedDocumentsWin', 504);
INSERT INTO auth_path (id, path, auth_permission) VALUES (516, '', 504);

-- Area
INSERT INTO auth_permission (id, title, auth_section) VALUES (710, 'Albo Pretorio | Aree | Visualizzare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (710, 'AlboAreasBtn', 'AlboToolbar', 710);
INSERT INTO auth_path (id, path, auth_permission) VALUES (710, '', 710);

INSERT INTO auth_permission (id, title, auth_section) VALUES (711, 'Albo Pretorio | Aree | Aggiungere', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (711, 'AlboAreaNewBtn', 'AlboAreasToolbar', 711);
INSERT INTO auth_path (id, path, auth_permission) VALUES (711, '', 711);

INSERT INTO auth_permission (id, title, auth_section) VALUES (712, 'Albo Pretorio | Aree | Modificare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (712, 'contextAlboAreaEdit', 'AlboAreaEditMn', 712);
INSERT INTO auth_path (id, path, auth_permission) VALUES (712, '', 712);

INSERT INTO auth_permission (id, title, auth_section) VALUES (713, 'Albo Pretorio | Aree | Eliminare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (713, 'contextAlboAreaDelete', 'AlboAreaEditMn', 713);
INSERT INTO auth_path (id, path, auth_permission) VALUES (713, '', 713);

-- Category
INSERT INTO auth_permission (id, title, auth_section) VALUES (720, 'Albo Pretorio | Categorie | Visualizzare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (720, 'AlboCategoriesBtn', 'AlboToolbar', 720);
INSERT INTO auth_path (id, path, auth_permission) VALUES (720, '', 720);

INSERT INTO auth_permission (id, title, auth_section) VALUES (721, 'Albo Pretorio | Categorie | Aggiungere', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (721, 'AlboCategoryNewBtn', 'AlboCategoriesToolbar', 721);
INSERT INTO auth_path (id, path, auth_permission) VALUES (721, '', 721);

INSERT INTO auth_permission (id, title, auth_section) VALUES (722, 'Albo Pretorio | Categorie | Modificare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (722, 'contextAlboCategoryEdit', 'AlboCategoryEditMn', 722);
INSERT INTO auth_path (id, path, auth_permission) VALUES (722, '', 722);

INSERT INTO auth_permission (id, title, auth_section) VALUES (723, 'Albo Pretorio | Categorie | Eliminare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (723, 'contextAlboCategoryDelete', 'AlboCategoryEditMn', 723);
INSERT INTO auth_path (id, path, auth_permission) VALUES (723, '', 723);

-- Entity
INSERT INTO auth_permission (id, title, auth_section) VALUES (730, 'Albo Pretorio | Enti | Visualizzare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (730, 'AlboEntitiesBtn', 'AlboToolbar', 730);
INSERT INTO auth_path (id, path, auth_permission) VALUES (730, '', 730);

INSERT INTO auth_permission (id, title, auth_section) VALUES (731, 'Albo Pretorio | Enti | Aggiungere', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (731, 'AlboEntityNewBtn', 'AlboEntitiesToolbar', 731);
INSERT INTO auth_path (id, path, auth_permission) VALUES (731, '', 731);

INSERT INTO auth_permission (id, title, auth_section) VALUES (732, 'Albo Pretorio | Enti | Modificare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (732, 'contextAlboEntityEdit', 'AlboEntityEditMn', 732);
INSERT INTO auth_path (id, path, auth_permission) VALUES (732, '', 732);

INSERT INTO auth_permission (id, title, auth_section) VALUES (733, 'Albo Pretorio | Enti | Eliminare', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (733, 'contextAlboEntityDelete', 'AlboEntityEditMn', 733);
INSERT INTO auth_path (id, path, auth_permission) VALUES (733, '', 733);

-- Update web page
INSERT INTO auth_permission (id, title, auth_section) VALUES (740, 'Albo Pretorio | Aggiorna pagina web', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (740, 'AlboUpdateWebBtn', 'AlboToolbar', 740);
INSERT INTO auth_path (id, path, auth_permission) VALUES (740, '', 740);

-- Prints
INSERT INTO auth_permission (id, title, auth_section) VALUES (750, 'Albo Pretorio | Stampe', 13);
INSERT INTO auth_element (id, name, control_interface, auth_permission) VALUES (750, 'AlboPrintsBtn', 'AlboToolbar', 750);
INSERT INTO auth_path (id, path, auth_permission) VALUES (750, '', 750);


---- END ALBO






-- New Password for mc2master
UPDATE users set user_password = 'c15e7227aeaa8382286fa4afab5a6da5' WHERE user_name = 'mc2master';

-- New Kinds
INSERT INTO absence_kind (code, description, date_start) VALUES ('AA07', 'CONGEDO PER CURE PER INVALIDI', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('AN14', 'ASSENZA PER MALATTIA (SUPPLENZA ANNUALE O FINO AL TERMINE DELLE ATTIVITA'')', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('AN15', 'CONGEDO PER CURE PER INVALIDI (SUPPLENZA ANNUALE O FINO AL TERMINE DELLE ATTIVITA'')', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('AN16', 'CONGEDO PER CURE PER INVALIDI (SUPPLENZA BREVE)', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('AN17', 'CONGEDO PER GRAVI E DOCUMENTATI MOTIVI FAMILIARI', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('AN18', 'CONGEDO STRAORDINARIO PER MOTIVI DI STUDIO SENZA ASSEGNI', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('B023', 'ASTENSIONE PER AFFIDAMENTO', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('B024', 'RIPOSO GIORNALIERO NEL PRIMO ANNO DI VITA DEL BAMBINO', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('EN01', 'ESONERO PER PARTECIPAZIONE A CORSI D''AGGIORNAMENTO E FORMAZIONE', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('HH23', 'ASTENSIONE PER AFFIDAMENTO', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('HH24', 'RIPOSO GIORNALIERO NEL PRIMO ANNO DI VITA DEL BAMBINO', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('PE18', 'PERMESSO NON RETRIBUITO PER ASSISTENZA FAMILIARI CON HANDICAP IN SITUAZIONE DI GRAVITA''', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('PE19', 'PERMESSO ORARIO NON RECUPERATO', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('PE20', 'PERMESSO ORARIO RETRIBUITO PER DIRITTO ALLO STUDIO', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('PN15', 'PERMESSO ORARIO RETRIBUITO PER DIRITTO ALLO STUDIO', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('PN16', 'PERMESSO ORARIO NON RECUPERATO', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('PN17', 'PERMESSO NON RETRIBUITO PER ASSISTENZA FAMILIARI CON HANDICAP IN SITUAZIONE DI GRAVITA''', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('PN18', 'PERMESSO NON RETRIBUITO PER MANDATO AMMINISTRATIVO', '2013-09-17 00:00:00');

INSERT INTO absence_kind (code, description, date_start) VALUES ('SN01', 'SOSPENSIONE DALL''INSEGNAMENTO FINO AD UN MESE', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('SN02', 'SOSPENSIONE DALL''INSEGNAMENTO DA 1 MESE A 6 MESI', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('SN03', 'SOSPENSIONE DAL LAVORO E DALLA RETRIBUZIONE FINO A DIECI GIORNI', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('SN04', 'SOSPENSIONE CAUTELARE OBBLIGATORIA', '2013-09-17 00:00:00');
INSERT INTO absence_kind (code, description, date_start) VALUES ('SN05', 'SOSPENSIONE CAUTELARE FACOLTATIVA', '2013-09-17 00:00:00');


-- Old Kinds
UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'AN05';
UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'AN11';

UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'B008';
UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'B011';

UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'HH10';

UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'R008';
UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'R012';

UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'S010';
UPDATE absence_kind SET date_end = '2013-09-01 00:00:00' WHERE code = 'S011';

-- Existing Kinds
UPDATE absence_kind SET description = 'ASPETTATIVA NON RETRIBUITA PER SERVIZIO MILITARE E SERVIZIO EQUIPARATO' WHERE code = 'A005';
UPDATE absence_kind SET description = 'ASPETTATIVA RETRIBUITA PER RICHIAMO ALLE ARMI' WHERE code = 'A006';
UPDATE absence_kind SET description = 'ASPETTATIVA NON RETRIBUITA PER RICHIAMO ALLE ARMI' WHERE code = 'A007';
UPDATE absence_kind SET description = 'ASPETTATIVA NON RETRIBUITA PER MANDATO AMMINISTRATIVO' WHERE code = 'A009';
UPDATE absence_kind SET description = 'PERMESSO RETRIBUITO PER MANDATO AMMINISTRATIVO' WHERE code = 'A010';
UPDATE absence_kind SET description = 'PERMESSO NON RETRIBUITO PER MANDATO AMMINISTRATIVO' WHERE code = 'A011';
UPDATE absence_kind SET description = 'ASPETTATIVA RETRIBUITA PER MANDATO PARLAMENTARE' WHERE code = 'A014';
UPDATE absence_kind SET description = 'DISTACCO SINDACALE' WHERE code = 'A016';
UPDATE absence_kind SET description = 'ASPETTATIVA NON RETRIBUITA PER MANDATO PARLAMENTARE' WHERE code = 'A020';
UPDATE absence_kind SET description = 'ASSENZA ALLE VISITE DI CONTROLLO SENZA GIUSTIFICATO MOTIVO' WHERE code = 'A021';

UPDATE absence_kind SET description = 'ASSENZA ALLE VISITE DI CONTROLLO SENZA GIUSTIFICATO MOTIVO' WHERE code = 'AN10';

UPDATE absence_kind SET description = 'ASTENSIONE PER ADOZIONE' WHERE code = 'B013';
UPDATE absence_kind SET description = 'PROLUNGAMENTO DEL CONGEDO PER MINORE CON HANDICAP IN SITUAZIONE DI GRAVITA''' WHERE code = 'B016';
UPDATE absence_kind SET description = 'CONGEDO PARENTALE NEI PRIMI OTTO ANNI DI VITA DEL BAMBINO' WHERE code = 'B018';
UPDATE absence_kind SET description = 'CONGEDO PER LA MALATTIA DEL FIGLIO DI ETÀ NON SUPERIORE A TRE ANNI' WHERE code = 'B019';
UPDATE absence_kind SET description = 'CONGEDO PER LA MALATTIA DEL FIGLIO DI ETÀ COMPRESA TRA I TRE E GLI OTTO ANNI' WHERE code = 'B020';
UPDATE absence_kind SET description = 'CONGEDO PER ADOZIONE E AFFIDAMENTO PREADOTTIVO INTERNAZIONALE' WHERE code = 'B021';

UPDATE absence_kind SET description = 'CONGEDO PARENTALE NEI PRIMI OTTO ANNI DI VITA DEL BAMBINO' WHERE code = 'HH13';
UPDATE absence_kind SET description = 'CONGEDO PER LA MALATTIA DEL FIGLIO DI ETÀ NON SUPERIORE A TRE ANNI' WHERE code = 'HH14';
UPDATE absence_kind SET description = 'CONGEDO PER LA MALATTIA DEL FIGLIO DI ETÀ COMPRESA TRA I TRE E GLI OTTO ANNI' WHERE code = 'HH15';
UPDATE absence_kind SET description = 'ASTENSIONE PER ADOZIONE' WHERE code = 'HH17';
UPDATE absence_kind SET description = 'CONGEDO NON RETRIBUITO PER ADOZIONE E AFFIDAMENTO PREADOTTIVO INTERNAZIONALE' WHERE code = 'HH18';

UPDATE absence_kind SET description = 'ESONERO PER PARTECIPAZIONE A CORSI D''AGGIORNAMENTO E FORMAZIONE' WHERE code = 'P015';
UPDATE absence_kind SET description = 'ESONERO PER INCARICO DI COORDINATORE DI EDUCAZIONE FISICA E SPORTIVA' WHERE code = 'P042';

UPDATE absence_kind SET description = 'CONGEDO PARENTALE INTERAMENTE RETRIBUITO' WHERE code = 'PE08';
UPDATE absence_kind SET description = 'ASSENZA RETRIBUITA PER LA MALATTIA DEL FIGLIO DI ETÀ NON SUPERIORE A TRE ANNI' WHERE code = 'PE09';

UPDATE absence_kind SET description = 'CONGEDO PARENTALE INTERAMENTE RETRIBUITO' WHERE code = 'PN09';
UPDATE absence_kind SET description = 'ASSENZA RETRIBUITA PER LA MALATTIA DEL FIGLIO DI ETÀ NON SUPERIORE A TRE ANNI' WHERE code = 'PN10';

-- Fix old Permissions IDs
UPDATE auth_element SET control_interface = 'EmployeeToolbar' WHERE control_interface = 'EmployeeRightPnl';
UPDATE auth_element SET control_interface = 'CcpToolbar' WHERE control_interface = 'CcpRightPnl';
UPDATE auth_element SET control_interface = 'ArchiveToolbar' WHERE control_interface = 'ArchiveRightPnl';
UPDATE auth_element SET control_interface = 'ProtocolToolbar' WHERE control_interface = 'ProtocolRightPnl';

-- New areas parameters
INSERT INTO parameter (name, value) VALUES ('AREA_HOME', 't');
INSERT INTO parameter (name, value) VALUES ('AREA_SETTINGS', 't');



-- Update existing Absence Kind
UPDATE absence_kind SET description = 'PROLUNGAMENTO DEL CONGEDO PER MINORE CON HANDICAP IN SITUAZIONE DI GRAVITA''', date_start = '2013-09-17 00:00:00' WHERE code = 'HH19';

-- New Document metadata
INSERT INTO archive_metadata (name, code, kind, class_id) values ('Note', 'note', 'S', 1);
INSERT INTO archive_metadata (name, code, kind, class_id) values ('Numero', 'number', 'I', 1);
INSERT INTO archive_metadata (name, code, kind, description, class_id) values ('Data', 'date', 'D', 'AAAA-MM-GG', 1);

-- Update Document class
UPDATE archive_class SET name = 'Documento generico' WHERE name = 'Locale';

-- Fix protocol table
ALTER TABLE audit.protocol_protocol ALTER COLUMN type_id DROP NOT NULL;
ALTER TABLE audit.protocol_protocol ALTER COLUMN subject_kind_id DROP NOT NULL;

ALTER TABLE protocol_protocol ALTER COLUMN type_id DROP NOT NULL;
ALTER TABLE protocol_protocol ALTER COLUMN subject_kind_id DROP NOT NULL;
ALTER TABLE protocol_protocol ALTER COLUMN subject_kind_id DROP DEFAULT;

ALTER TABLE audit.protocol_protocol_history ADD COLUMN note text;

ALTER TABLE protocol_protocol_history ADD COLUMN note text;

-- Fix permissions
UPDATE auth_element SET "name" = 'SettingsInstitutesMenuTab' WHERE id = 320;
UPDATE auth_element SET control_interface = 'InstituteToolbar' WHERE id = 321;
UPDATE auth_element SET control_interface = 'InstituteBanksToolbar' WHERE id = 322;
UPDATE auth_element SET control_interface = 'InstituteToolbar', "name" = 'SettingsInstitutesBtn' WHERE id = 330;


-- Add legislation to some Trasparenza Voices
UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 10 Comma 8 Lettera A</i></p><p>8. Ogni amministrazione ha l''obbligo di pubblicare sul proprio sito istituzionale nella sezione: «Amministrazione trasparente» di cui all''articolo 9:</p><p><span>a. il Programma triennale per la trasparenza e l''integrità ed il relativo stato di attuazione</span></p>' WHERE title ILIKE 'Programma per la Trasparenza%';

UPDATE trasparenza_voice SET reference = '<span style="color: rgb(0, 0, 0); font-family: tahoma, arial, verdana, sans-serif; font-size: 12px;">​</span><font face="tahoma, arial, verdana, sans-serif"><b>Dlgs 14 marzo 2013, n. 33</b></font><div><font face="tahoma, arial, verdana, sans-serif"><br></font></div><div><font face="tahoma, arial, verdana, sans-serif"><i>12 Comma 1,2</i></font></div><div><font face="tahoma, arial, verdana, sans-serif"><br></font></div><div><font face="tahoma, arial, verdana, sans-serif">Obblighi di pubblicazione concernenti gli atti di carattere normativo e amministrativo generale.</font></div><div><font face="tahoma, arial, verdana, sans-serif"><br></font></div><div><font face="tahoma, arial, verdana, sans-serif">Fermo restando quanto previsto per le pubblicazioni nella Gazzetta Ufficiale della Repubblica italiana dalla legge 11 dicembre 1984, n. 839, e dalle relative norme di attuazione, le pubbliche</font></div><div><font face="tahoma, arial, verdana, sans-serif">amministrazioni pubblicano sui propri siti istituzionali i riferimenti normativi con i relativi link alle norme di legge statale pubblicate nella banca dati «Normattiva» che ne regolano l''istituzione, l''organizzazione e l''attività. Sono altresì pubblicati le direttive, le circolari, i programmi e le istruzioni emanati dall''amministrazione e ogni atto che dispone in generale sulla organizzazione, sulle funzioni, sugli obiettivi, sui procedimenti ovvero nei quali si determina l''interpretazione di norme giuridiche che le riguardano o si dettano disposizioni per l''applicazione di esse, ivi compresi i codici di condotta.</font></div><div><font face="tahoma, arial, verdana, sans-serif">Con riferimento agli statuti e alle norme di legge regionali, che regolano le funzioni, l''organizzazione e lo svolgimento delle attività di competenza dell''amministrazione, sono pubblicati gli estremi degli atti e dei testi ufficiali aggiornati.</font></div>' WHERE title ILIKE 'Atti Generali';

UPDATE trasparenza_voice SET reference = '<div><b>Dlgs 14 marzo 2013, n. 33</b></div><div><br></div><div><i>Articolo 34 Comma 1,2</i></div><div><br></div><div>Trasparenza degli oneri informativi</div><div><br></div><div>I regolamenti ministeriali o interministeriali, nonchè i provvedimenti amministrativi a carattere generale adottati dalle amministrazioni dello Stato per regolare l''esercizio di poteri autorizzatori, concessori o certificatori, nonchè l''accesso ai servizi pubblici ovvero la concessione di benefici, recano in allegato l''elenco di tutti gli oneri informativi gravanti sui cittadini e sulle imprese introdotti o eliminati con gli atti medesimi. Per onere informativo si intende qualunque obbligo informativo o adempimento che comporti la raccolta, l''elaborazione, la trasmissione, la conservazione e la produzione di informazioni e documenti alla pubblica amministrazione.</div><div>Ferma restando, ove prevista, la pubblicazione nella Gazzetta Ufficiale, gli atti di cui al comma 1 sono pubblicati sui siti istituzionali delle amministrazioni, secondo i criteri e le modalità definite con il regolamento di cui all''articolo 7, commi 2 e 4, della legge 11 novembre 2011, n. 180.</div>' WHERE title ILIKE 'Oneri informativi per cittadini e imprese';

UPDATE trasparenza_voice SET reference = '<div><b>Dlgs 14 marzo 2013, n. 33</b></div><div><br></div><div><i>Articolo 13 Comma1 Lettera A</i></div><div><br></div><div>Obblighi di pubblicazione concernenti l''organizzazione delle pubbliche amministrazioni</div><div><br></div><div>Le pubbliche amministrazioni pubblicano e aggiornano le informazioni e i dati concernenti la propria organizzazione, corredati dai documenti anche normativi di riferimento. Sono pubblicati, tra gli altri, i dati relativi:</div><div>agli organi di indirizzo politico e di amministrazione e gestione, con l''indicazione delle rispettive competenze;</div><div><br></div><div><br></div><div><b>DLgs 14 marzo 2013, n. 33</b></div><div><br></div><div><i>Articolo 14 Comma 1,2</i></div><div><br></div><div>Obblighi di pubblicazione concernenti i componenti degli organi di indirizzo politico</div><div><br></div><div><br></div><div><br></div><div>Con riferimento ai titolari di incarichi politici, di carattere elettivo o comunque di esercizio di poteri di indirizzo politico, di livello statale regionale e locale, le pubbliche amministrazioni pubblicano con riferimento a tutti i propri componenti, i seguenti documenti ed informazioni:</div><div>l''atto di nomina o di proclamazione, con l''indicazione della durata dell''incarico o del mandato elettivo;</div><div>il curriculum;</div><div>i compensi di qualsiasi natura connessi all''assunzione della carica; gli importi di viaggi di servizio e missioni pagati con fondi pubblici;</div><div>i dati relativi all''assunzione di altre cariche, presso enti pubblici o privati, ed i relativi compensi a qualsiasi titolo corrisposti;</div><div>gli altri eventuali incarichi con oneri a carico della finanza pubblica e l''indicazione dei compensi spettanti;</div><div>le dichiarazioni di cui all''articolo 2, della legge 5 luglio 1982, n. 441, nonchè le attestazioni e dichiarazioni di cui agli articoli 3 e 4 della medesima legge, come modificata dal presente decreto, limitatamente al soggetto, al coniuge non separato e ai parenti entro il secondo grado, ove gli stessi vi consentano. Viene in ogni caso data evidenza al mancato consenso. Alle informazioni di cui alla presente lettera concernenti soggetti diversi dal titolare dell''organo di indirizzo politico non si applicano le disposizioni di cui all''articolo 7.</div><div>Le pubbliche amministrazioni pubblicano i dati cui al comma 1 entro tre mesi dalla elezione o dalla nomina e per i tre anni successivi dalla cessazione del mandato o dell''incarico dei soggetti, salve le informazioni concernenti la situazione patrimoniale e, ove consentita, la dichiarazione del coniuge non separato e dei parenti entro il secondo grado, che vengono pubblicate fino alla cessazione dell''incarico o del mandato. Decorso il termine di pubblicazione ai sensi del presente comma le informazioni e i dati concernenti la situazione patrimoniale non vengono trasferiti nelle sezioni di archivio</div><div><br></div><div><br></div><div>La Delibera CIVIT n. 65/2013 in "Applicazione dell''art. 14, comma 1, lettera f), del d.lgs n. 33/2013 ai Comuni" recita: "sono soggetti agli obblighi di pubblicazione relativamente alla situazione reddituale e patrimoniale dei titolari di cariche elettive i comuni con popolazione superiore ai 15.000 abitanti"</div>' WHERE title ILIKE 'Organi di indirizzo politico%';

UPDATE trasparenza_voice SET reference = '<div><b>Dlgs 14 marzo 2013, n. 33</b></div><div><br></div><div><i>Articolo 47</i></div><div><br></div><div>Sanzioni per casi specifici</div><div><br></div><div>La mancata o incompleta comunicazione delle informazioni e dei dati di cui all''articolo 14, concernenti la situazione patrimoniale complessiva del titolare dell''incarico al momento dell''assunzione in carica, la titolarità di imprese, le partecipazioni azionarie proprie, del coniuge e dei parenti entro il secondo grado, nonchè tutti i compensi cui da diritto l''assunzione della carica, da'' luogo a una sanzione amministrativa pecuniaria da 500 a 10.000 euro a carico del responsabile della mancata comunicazione e il relativo provvedimento è pubblicato sul sito internet dell''amministrazione o organismo interessato.</div><div>La violazione degli obblighi di pubblicazione di cui all''articolo 22, comma 2, da'' luogo ad una sanzione amministrativa pecuniaria da 500 a 10.000 euro a carico del responsabile della violazione. La stessa sanzione si applica agli amministratori societari che non comunicano ai soci pubblici il proprio incarico ed il relativo compenso entro trenta giorni dal conferimento ovvero, per le indennità di risultato, entro trenta giorni dal percepimento.</div><div>Le sanzioni di cui ai commi 1 e 2 sono irrogate dall''autorità amministrativa competente in base a quanto previsto dalla legge 24 novembre 1981, n. 689.</div>' WHERE title ILIKE 'Sanzioni per mancata comunicazione dei dati';

UPDATE trasparenza_voice SET reference = '<div><b>Dlgs 14 marzo 2013, n. 33</b></div><div><br></div><div><i>28 Comma 1</i></div><div><br></div><div>Pubblicità dei rendiconti dei gruppi consiliari regionali e provinciali</div><div><br></div><div>Le regioni, le province autonome di Trento e Bolzano e le province pubblicano i rendiconti di cui all''articolo 1, comma 10, del decreto-legge 10 ottobre 2012, n. 174, convertito, con modificazioni, dalla legge 7 dicembre 2012, n. 213, dei gruppi consiliari regionali e provinciali, con evidenza delle risorse trasferite o assegnate a ciascun gruppo, con indicazione del titolo di trasferimento e dell''impiego delle risorse utilizzate. Sono altresì pubblicati gli atti e le relazioni degli organi di controllo.</div>' WHERE title ILIKE 'Rendiconti gruppi consiliari regionali/provinciali';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>articolo 13 Comma 1 Lettera B,C</i></p><p>Obblighi di pubblicazione concernenti l''organizzazione delle pubbliche amministrazioni</p><p>1. Le pubbliche amministrazioni pubblicano e aggiornano le informazioni e i dati concernenti la propria organizzazione, corredati dai documenti anche normativi di riferimento. Sono pubblicati, tra gli altri, i dati relativi:</p><p>b. all''articolazione degli uffici, le competenze e le risorse a disposizione di ciascun ufficio, anche di livello dirigenziale non generale, i nomi dei dirigenti responsabili dei singoli uffici;</p><p><span>c. all''illustrazione in forma semplificata, ai fini della piena accessibilità e comprensibilità dei dati, dell''organizzazione dell''amministrazione, mediante l''organigramma o analoghe rappresentazioni grafiche;</span></p>' WHERE title ILIKE 'Articolazione degli uffici';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><b><i>articolo 13 Comma1 Lettera D</i></b></p><p>Obblighi di pubblicazione concernenti l''organizzazione delle pubbliche amministrazioni</p><p>1. Le pubbliche amministrazioni pubblicano e aggiornano le informazioni e i dati concernenti la propria organizzazione, corredati dai documenti anche normativi di riferimento. Sono pubblicati, tra gli altri, i dati relativi:</p><p><span>d. all''elenco dei numeri di telefono nonchè delle caselle di posta elettronica istituzionali e delle caselle di posta elettronica certificata dedicate, cui il cittadino possa rivolgersi per qualsiasi richiesta inerente i compiti istituzionali.</span></p>' WHERE title ILIKE 'Telefono e posta elettronica';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 15 Comma 1,2,5</i></p><p>Obblighi di pubblicazione concernenti i titolari di incarichi dirigenziali e di collaborazione o consulenza</p><p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Fermi restando gli obblighi di comunicazione di cui all''articolo 17, comma 22, della legge 15 maggio 1997, n. 127, le pubbliche amministrazioni pubblicano e aggiornano le seguenti informazioni relative ai titolari di incarichi amministrativi di vertice e di incarichi dirigenziali, a qualsiasi titolo conferiti, nonchè di collaborazione o consulenza:</span></li><ol style="list-style-type:lower-latin;"><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">gli estremi dell''atto di conferimento dell''incarico;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">il curriculum vitae;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">i dati relativi allo svolgimento di incarichi o la titolarità di cariche in enti di diritto privato regolati o finanziati dalla pubblica amministrazione o lo svolgimento di attività professionali;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">i compensi, comunque denominati, relativi al rapporto di lavoro, di consulenza o di collaborazione, con specifica evidenza delle eventuali componenti variabili o legate alla valutazione del risultato.</span></li></ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">La pubblicazione degli estremi degli atti di conferimento di incarichi dirigenziali a soggetti estranei alla pubblica amministrazione, di collaborazione o di consulenza a soggetti esterni a qualsiasi titolo per i quali è previsto un compenso, completi di indicazione dei soggetti percettori, della ragione dell''incarico e dell''ammontare erogato, nonchè la comunicazione alla Presidenza del Consiglio dei Ministri - Dipartimento della funzione pubblica dei relativi dati ai sensi dell''articolo 53, comma 14, secondo periodo, del decreto legislativo 30 marzo 2001, n. 165 e successive modificazioni, sono condizioni per l''acquisizione dell''efficacia dell''atto e per la liquidazione dei relativi compensi.</span><br></li></ol></p><p>Le amministrazioni pubblicano e mantengono aggiornati sui rispettivi siti istituzionali gli elenchi dei propri consulenti indicando l''oggetto, la durata e il compenso dell''incarico. Il Dipartimento della funzione pubblica consente la consultazione, anche per nominativo, dei dati di cui al presente comma.</p><p>5. Le pubbliche amministrazioni pubblicano e mantengono aggiornato l''elenco delle posizioni dirigenziali, integrato dai relativi titoli e curricula, attribuite a persone, anche esterne alle pubbliche</p><p>amministrazioni, individuate discrezionalmente dall''organo di indirizzo politico senza procedure pubbliche di selezione, di cui all''articolo 1, commi 39 e 40, della legge 6 novembre 2012, n. 190.</p><p><br></p><p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>articolo 10 Comma 8 Lettera D</i></p><p>8. Ogni amministrazione ha l''obbligo di pubblicare sul proprio sito istituzionale nella sezione: «Amministrazione trasparente» di cui all''articolo 9:</p><p>d. i curricula e i compensi dei soggetti di cui all''articolo 15, comma 1, nonchè i curricula dei titolari di posizioni organizzative, redatti in conformità al vigente modello europeo.</p>' WHERE title ILIKE 'Dirigenti';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>articolo 10 Comma 8 Lettera D</i></p><p>8. Ogni amministrazione ha l''obbligo di pubblicare sul proprio sito istituzionale nella sezione: «Amministrazione trasparente» di cui all''articolo 9:</p><p><span>d. i curricula e i compensi dei soggetti di cui all''articolo 15, comma 1, nonchè i curricula dei titolari di posizioni organizzative, redatti in conformità al vigente modello europeo.</span></p>' WHERE title ILIKE 'Posizioni organizzative';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 16 Comma 1,2</i></p><p>Obblighi di pubblicazione concernenti la dotazione organica e il costo del personale con rapporto di lavoro a tempo indeterminato.</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano il conto annuale del personale e delle relative spese sostenute, di cui all''articolo 60, comma 2, del decreto legislativo 30 marzo 2001, n. 165, nell''ambito del quale sono rappresentati i dati relativi alla dotazione organica e al personale effettivamente in servizio e al relativo costo, con l''indicazione della sua distribuzione tra le diverse qualifiche e aree professionali, con particolare riguardo al personale assegnato agli uffici di diretta collaborazione con gli organi di indirizzo politico.</span></li><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni, nell''ambito delle pubblicazioni di cui al comma 1, evidenziano separatamente, i dati relativi al costo complessivo del personale a tempo indeterminato in servizio, articolato per aree professionali, con particolare riguardo al personale assegnato agli uffici di diretta collaborazione con gli organi di indirizzo politico.</span></li></ol></p>' WHERE title ILIKE 'Dotazione organica';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 17 Comma 1,2</i></p><p>Obblighi di pubblicazione dei dati relativi al personale non a tempo indeterminato</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano annualmente, nell''ambito di quanto previsto dall''articolo 16, comma 1, i dati relativi al personale con rapporto di lavoro non a tempo indeterminato, con la indicazione delle diverse tipologie di rapporto, della distribuzione di questo personale tra le diverse qualifiche e aree professionali, ivi compreso il personale assegnato agli uffici di diretta collaborazione con gli organi di indirizzo politico. La pubblicazione comprende l''elenco dei titolari dei contratti a tempo determinato.</span></li><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano trimestralmente i dati relativi al costo complessivo del personale di cui al comma 1, articolato per aree professionali, con particolare riguardo al personale assegnato agli uffici di diretta collaborazione con gli organi di indirizzo politico.</span></li></ol></p>' WHERE title ILIKE 'Personale non a tempo indeterminato';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>16 Comma 3</i></p><p><span>3. Le pubbliche amministrazioni pubblicano trimestralmente i dati relativi ai tassi di assenza del personale distinti per uffici di livello dirigenziale.</span></p>' WHERE title ILIKE 'Tassi di assenza';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33 - DLgs n. 165/2001</b></p><p><i>articolo 18, articolo 53 Comma 1, Comma 14</i></p><p>Obblighi di pubblicazione dei dati relativi agli incarichi conferiti ai dipendenti pubblici</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano l''elenco degli incarichi conferiti o autorizzati a ciascuno dei propri dipendenti, con l''indicazione della durata e del compenso spettante per ogni incarico.</span><br></li></ol></p>' WHERE title ILIKE 'Incarichi conferiti e autorizzati ai dipendenti';

UPDATE trasparenza_voice SET reference = '<p style="line-height: inherit;"><span><b>DLgs 14 marzo 2013, n. 33</b></span></p><p style="line-height: inherit;"><i>21 Comma 1</i></p><p style="line-height: inherit;">Obblighi di pubblicazione concernenti i dati sulla contrattazione collettiva</p><p style="line-height: inherit;"><ol><li><span style="line-height: inherit;">Le pubbliche amministrazioni pubblicano i riferimenti necessari per la consultazione dei contratti e accordi collettivi nazionali, che si applicano loro, nonchè le eventuali interpretazioni autentiche.</span><br></li></ol></p>' WHERE title ILIKE 'Contrattazione collettiva';

UPDATE trasparenza_voice SET reference = '<p style="line-height: inherit;"><span><b>DLgs 14 marzo 2013, n. 33 - Art. 55, c. 4, DLgs n. 150/2009</b></span></p><p style="line-height: inherit;"><span><i>21 Comma 2</i></span></p><p style="line-height: inherit;"><span>2. Fermo restando quanto previsto dall''articolo 47, comma 8, del decreto legislativo 30 marzo 2001, n. 165, le pubbliche amministrazioni pubblicano i contratti integrativi stipulati, con la relazione tecnico-finanziaria e quella illustrativa certificate dagli organi di controllo di cui all''articolo 40-bis, comma 1, del decreto legislativo n. 165 del 2001, nonchè le informazioni trasmesse annualmente ai sensi del comma 3 dello stesso articolo. La relazione illustrativa, fra l''altro, evidenzia gli effetti attesi in esito alla sottoscrizione del contratto integrativo in materia di produttività ed efficienza dei servizi erogati, anche in relazione alle richieste dei cittadini.</span></p>' WHERE title ILIKE 'Contrattazione integrativa';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33 - Art. 14.2, delib. CiVIT n. 12/2013</b></p><p><i>articolo 10 Comma 8 Lettera C</i></p><p><span>c. i nominativi ed i curricula dei componenti degli organismi indipendenti di valutazione di cui all''articolo 14 del decreto legislativo n. 150 del 2009</span></p>' WHERE title ILIKE 'OIV';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33 - Art. 1, c. 16, lett. d), l. n. 190/2012</b></p><p><i>articolo 19 Comma 1,2</i></p><p>Bandi di concorso</p><p><ol><li><span style="line-height: 1.42857143;">Fermi restando gli altri obblighi di pubblicità legale, le pubbliche amministrazioni pubblicano i bandi di concorso per il reclutamento, a qualsiasi titolo, di personale presso l''amministrazione.</span></li><li><span style="line-height: 1.42857143;">&nbsp;Le pubbliche amministrazioni pubblicano e tengono costantemente aggiornato l''elenco dei bandi in corso, nonchè quello dei bandi espletati nel corso dell''ultimo triennio, accompagnato dall''indicazione, per ciascuno di essi, del numero dei dipendenti assunti e delle spese effettuate.</span></li></ol></p>' WHERE title ILIKE 'Bandi di Concorso';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>articolo 22</i></p><p>Obblighi di pubblicazione dei dati relativi agli enti pubblici vigilati, e agli enti di diritto privato in controllo pubblico, nonchè alle partecipazioni in società di diritto privato.</p><p><br></p><p></p><ol><li><span style="line-height: 1.42857143;">Ciascuna amministrazione pubblica e aggiorna annualmente</span></li><ol style="list-style-type:lower-latin;"><li><span style="line-height: 1.42857143;">l''elenco degli enti pubblici, comunque denominati, istituiti, vigilati e finanziati dalla amministrazione medesima ovvero per i quali l''amministrazione abbia il potere di nomina degli amministratori dell''ente, con l''elencazione delle funzioni attribuite e delle attività svolte in favore dell''amministrazione o delle</span></li></ol></ol><p></p><p>attività di servizio pubblico affidate;b) l''elenco delle società di cui detiene direttamente quote di partecipazione anche minoritaria indicandone l''entità, con l''indicazione delle funzioni attribuite e delle attività svolte in favore dell''amministrazione o delle attività di servizio pubblico affidate; c) l''elenco degli enti di diritto privato, comunque denominati, in controllo dell''amministrazione, con l''indicazione delle funzioni attribuite e delle attività svolte in favore dell''amministrazione o delle attività di servizio pubblico affidate. Ai fini delle presenti disposizioni sono enti di diritto privato in controllo pubblico gli enti di diritto privato sottoposti a controllo da parte di amministrazioni pubbliche, oppure gli enti costituiti o vigilati da pubbliche amministrazioni nei quali siano a queste riconosciuti, anche in assenza di una partecipazione azionaria, poteri di nomina dei vertici o dei componenti degli organi; d) una o più rappresentazioni grafiche che evidenziano i rapporti tra l''amministrazione e gli enti di cui al precedente comma.</p><p>2. Per ciascuno degli enti di cui alle lettere da a) a c) del comma 1 sono pubblicati i dati relativi alla ragione sociale, alla misura della eventuale partecipazione dell''amministrazione, alla durata dell''impegno, all''onere complessivo a qualsiasi titolo gravante per l''anno sul bilancio dell''amministrazione, al numero dei rappresentanti dell''amministrazione negli organi di governo, al trattamento economico complessivo a ciascuno di essi spettante, ai risultati di bilancio degli ultimi tre esercizi finanziari. Sono altresì pubblicati i dati relativi agli incarichi di amministratore dell''ente e il relativo trattamento economico complessivo.</p><p>3. Nel sito dell''amministrazione è inserito il collegamento con i siti istituzionali degli enti di cui al comma 1, nei quali sono pubblicati i dati relativi ai componenti degli organi di indirizzo e ai soggetti titolari di incarico, in applicazione degli articoli 14 e 15.</p><p>4. Nel caso di mancata o incompleta pubblicazione dei dati relativi agli enti di cui al comma 1, è vietata l''erogazione in loro favore di somme a qualsivoglia titolo da parte dell''amministrazione interessata.</p><p>5. Le amministrazioni titolari di partecipazioni di controllo promuovono l''applicazione dei principi di trasparenza di cui ai commi 1, lettera b), e 2, da parte delle società direttamente controllate nei confronti delle società indirettamente controllate dalle medesime amministrazioni.</p><p><span>6. Le disposizioni di cui al presente articolo non trovano applicazione nei confronti delle società, partecipate da amministrazioni pubbliche, quotate in mercati regolamentati e loro controllate</span></p>' WHERE title ILIKE 'Enti controllati';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>articolo 22 Comma 1 Lettera A</i></p><p>Obblighi di pubblicazione dei dati relativi agli enti pubblici vigilati, e agli enti di diritto privato in controllo pubblico, nonchè alle partecipazioni in società di diritto privato.</p><p></p><ol><li><span style="line-height: 1.42857143;">Ciascuna amministrazione pubblica e aggiorna annualmente:</span></li><ol style="list-style-type:lower-latin;"><li><span style="line-height: 1.42857143;">l''elenco degli enti pubblici, comunque denominati, istituiti, vigilati e finanziati dalla amministrazione medesima ovvero per i quali l''amministrazione abbia il potere di nomina degli amministratori dell''ente, con l''elencazione delle funzioni attribuite e delle attività svolte in favore dell''amministrazione o delle attività di servizio pubblico affidate;</span></li></ol></ol>' WHERE title ILIKE 'Enti pubblici vigilati';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 22 Comma 1 Lettera B</i></p><p><span>b. l''elenco delle società di cui detiene direttamente quote di partecipazione anche minoritaria indicandone l''entità, con l''indicazione delle funzioni attribuite e delle attività svolte in favore dell''amministrazione o delle attività di servizio pubblico affidate;</span></p>' WHERE title ILIKE 'Società partecipate';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><span><i>articolo 22 Comma 1 Lettera B</i></span></p><p><span>b. l''elenco delle società di cui detiene direttamente quote di partecipazione anche minoritaria indicandone l''entità, con l''indicazione delle funzioni attribuite e delle attività svolte in favore dell''amministrazione o delle attività di servizio pubblico affidate;</span></p>' WHERE title ILIKE 'Enti di diritto privato controllati';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>22 Comma 1 Lettera D</i></p><p><span>d. una o più rappresentazioni grafiche che evidenziano i rapporti tra l''amministrazione e gli enti di cui al precedente comma.</span></p>' WHERE title ILIKE 'Rappresentazione grafica';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>24 Comma 1</i></p><p>Obblighi di pubblicazione dei dati aggregati relativi all''attività amministrativa</p><p><br></p><p><span>Le pubbliche amministrazioni che organizzano, a fini conoscitivi e statistici, i dati relativi alla propria attività amministrativa, in forma aggregata, per settori di attività, per competenza degli organi e degli uffici, per tipologia di procedimenti, li pubblicano e li tengono costantemente aggiornati.</span></p>' WHERE title ILIKE 'Dati aggregati attività amministrativa';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 35 Comma 1,2</i></p><p></p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano i dati relativi alle tipologie di procedimento di propria competenza. Per ciascuna tipologia di procedimento sono pubblicate le seguenti informazioni:</span></li><ol style="list-style-type:lower-latin;"><li><span style="line-height: 1.42857143;">una breve descrizione del procedimento con indicazione di tutti i riferimenti normativi utili;</span></li><li><span style="line-height: 1.42857143;">l''unità organizzativa responsabile dell''istruttoria;</span></li><li><span style="line-height: 1.42857143;">il nome del responsabile del procedimento, unitamente ai recapiti telefonici e alla casella di posta elettronica istituzionale, nonchè, ove diverso, l''ufficio competente all''adozione del provvedimento finale, con l''indicazione del nome del responsabile dell''ufficio, unitamente ai rispettivi recapiti telefonici e alla casella di posta elettronica istituzionale;</span></li><li><span style="line-height: 1.42857143;">per i procedimenti ad istanza di parte, gli atti e i documenti da allegare all''istanza e la modulistica necessaria, compresi i fac-simile per le autocertificazioni, anche se la produzione a corredo dell''istanza è prevista da norme di legge, regolamenti o atti pubblicati nella Gazzetta Ufficiale, nonchè gli uffici ai quali rivolgersi per informazioni, gli orari e le modalità di accesso con indicazione degli indirizzi, dei recapiti telefonici e delle caselle di posta elettronica istituzionale, a cui presentare le istanze;</span></li><li><span style="line-height: 1.42857143;">le modalità con le quali gli interessati possono ottenere le informazioni relative ai procedimenti in corso che li riguardino;</span></li><li><span style="line-height: 1.42857143;">il termine fissato in sede di disciplina normativa del procedimento per la conclusione con l''adozione di un provvedimento espresso e ogni altro termine procedimentale rilevante;</span></li><li><span style="line-height: 1.42857143;">i procedimenti per i quali il provvedimento dell''amministrazione può essere sostituito da una dichiarazione dell''interessato, ovvero il procedimento può concludersi con il silenzio assenso dell''amministrazione;</span></li><li><span style="line-height: 1.42857143;">gli strumenti di tutela, amministrativa e giurisdizionale, riconosciuti dalla legge in favore dell''interessato, nel corso del procedimento e nei confronti del provvedimento finale ovvero nei casi di adozione del provvedimento oltre il termine predeterminato per la sua conclusione e i modi per attivarli;</span></li><li><span style="line-height: 1.42857143;">il link di accesso al servizio on line, ove sia già disponibile in rete, o i tempi previsti per la sua attivazione;</span></li><li><span style="line-height: 1.42857143;">le modalità per l''effettuazione dei pagamenti eventualmente necessari, con le informazioni di cui all''articolo 36;</span></li><li><span style="line-height: 1.42857143;">il nome del soggetto a cui è attribuito, in caso di inerzia, il potere sostitutivo, nonchè le modalità per attivare tale potere, con indicazione dei recapiti telefonici e delle caselle di posta elettronica istituzionale;</span></li><li><span style="line-height: 1.42857143;">i risultati delle indagini di customer satisfaction condotte sulla qualità dei servizi erogati attraverso diversi canali, facendone rilevare il relativo andamento.</span></li></ol><li><span style="line-height: 1.42857143;">&nbsp;Le pubbliche amministrazioni non possono richiedere l''uso di moduli e formulari che non siano stati pubblicati; in caso di omessa pubblicazione, i relativi procedimenti possono essere avviati anche in assenza dei suddetti moduli o formulari. L''amministrazione non può respingere l''istanza adducendo il mancato utilizzo dei moduli o formulari o la mancata produzione di tali atti o documenti, e deve invitare l''istante a integrare la documentazione in un termine congruo.</span><br></li></ol>' WHERE title ILIKE 'Tipologie di procedimento';

UPDATE trasparenza_voice SET reference = '<b style="line-height: inherit; color: rgb(68, 68, 68);">DLgs 14 marzo 2013, n. 33</b><p><b><br></b><p style="line-height: inherit;"><span><i>24 Comma 2</i></span></p><p style="line-height: inherit;"><span>Le amministrazioni pubblicano e rendono consultabili i risultati del monitoraggio periodico concernente il rispetto dei tempi procedimentali effettuato ai sensi dell''articolo 1, comma 28, della legge 6 novembre 2012, n. 190.</span></p></p>' WHERE title ILIKE 'Monitoraggio tempi procedimentali';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>35 Comma 3</i></p><p>Le pubbliche amministrazioni pubblicano nel sito istituzionale:</p><p>a. i recapiti telefonici e la casella di posta elettronica istituzionale dell''ufficio responsabile per le attività volte a gestire, garantire e verificare la trasmissione dei dati o l''accesso diretto agli stessi da parte delle amministrazioni procedenti ai sensi degli articoli 43, 71 e 72 del decreto del Presidente della</p><p>Repubblica 28 dicembre 2000, n. 445;</p><p>b. le convenzioni-quadro volte a disciplinare le modalità di accesso ai dati di cui all''articolo 58 del codice dell''amministrazione digitale, di cui al decreto legislativo 7 marzo 2005, n. 82;</p><p><span>c. le ulteriori modalità per la tempestiva acquisizione d''ufficio dei dati nonchè per lo svolgimento dei controlli sulle dichiarazioni sostitutive da parte delle amministrazioni procedenti</span></p>' WHERE title ILIKE 'Dichiarazioni sostitutive e acquisizione d''ufficio dei dati';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 23 Comma 1,2</i></p><p>Obblighi di pubblicazione concernenti i provvedimenti amministrativi</p><p></p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano e aggiornano ogni sei mesi, in distinte partizioni della sezione «Amministrazione trasparente», gli elenchi dei provvedimenti adottati dagli organi di indirizzo politico e dai dirigenti, con particolare riferimento ai provvedimenti finali dei procedimenti di:</span></li><ol style="list-style-type:lower-latin;"><li><span style="line-height: 1.42857143;">autorizzazione o concessione;</span></li><li><span style="line-height: 1.42857143;">scelta del contraente per l''affidamento di lavori, forniture e servizi, anche con riferimento alla modalità di selezione prescelta ai sensi del codice dei contratti pubblici, relativi a lavori,servizi e forniture, di cui al decreto legislativo 12 aprile 2006, n. 163;</span></li><li><span style="line-height: 1.42857143;">concorsi e prove selettive per l''assunzione del personale e progressioni di carriera di cui all''articolo 24 del decreto legislativo n. 150 del 2009;</span></li><li><span style="line-height: 1.42857143;">accordi stipulati dall''amministrazione con soggetti privati o con altre amministrazioni pubbliche.</span></li></ol><li><span style="line-height: 1.42857143;">Per ciascuno dei provvedimenti compresi negli elenchi di cui al comma 1 sono pubblicati il contenuto, l''oggetto, la eventuale spesa prevista e gli estremi relativi ai principali documenti contenuti nel fascicolo relativo al procedimento. La pubblicazione avviene nella forma di una scheda sintetica, prodotta automaticamente in sede di formazione del documento che contiene l''atto.</span><br></li></ol>' WHERE title ILIKE 'Provvedimenti organi indirizzo-politico';

UPDATE trasparenza_voice SET reference = '<span style="color: rgb(68, 68, 68); line-height: inherit;"><b>Dlgs 14 marzo 2013, n. 33</b></span><p><b><br></b><p style="line-height: inherit;"><span><i>articolo 23 Comma 1,2</i></span></p><p style="line-height: inherit;"><span>Obblighi di pubblicazione concernenti i provvedimenti amministrativi</span></p><p style="line-height: inherit;"><ol><li><span style="line-height: inherit;">Le pubbliche amministrazioni pubblicano e aggiornano ogni sei mesi, in distinte partizioni della sezione «Amministrazione trasparente», gli elenchi dei provvedimenti adottati dagli organi di indirizzo politico e dai dirigenti, con particolare riferimento ai provvedimenti finali dei procedimenti di:</span></li><ol style="list-style-type:lower-latin;"><li><span style="line-height: inherit;">autorizzazione o concessione;</span></li><li><span style="line-height: inherit;">scelta del contraente per l''affidamento di lavori, forniture e servizi, anche con riferimento alla modalità di selezione prescelta ai sensi del codice dei contratti pubblici, relativi a lavori,servizi e forniture, di cui al decreto legislativo 12 aprile 2006, n. 163;</span></li><li><span style="line-height: inherit;">concorsi e prove selettive per l''assunzione del personale e progressioni di carriera di cui all''articolo 24 del decreto legislativo n. 150 del 2009;</span></li><li><span style="line-height: inherit;">accordi stipulati dall''amministrazione con soggetti privati o con altre amministrazioni pubbliche.</span></li></ol><li><span style="line-height: inherit;">Per ciascuno dei provvedimenti compresi negli elenchi di cui al comma 1 sono pubblicati il contenuto, l''oggetto, la eventuale spesa prevista e gli estremi relativi ai principali documenti contenuti nel fascicolo relativo al procedimento. La pubblicazione avviene nella forma di una scheda sintetica, prodotta automaticamente in sede di formazione del documento che contiene l''atto.</span><br></li></ol></p></p>' WHERE title ILIKE 'Provvedimenti dirigenti';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><span><i>25</i></span></p><p>Obblighi di pubblicazione concernenti i controlli sulle imprese</p><p></p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni, in modo dettagliato e facilmente comprensibile, pubblicano sul proprio sito istituzionale e sul sito: www.impresainungiorno.gov.it:</span></li><ol style="list-style-type:lower-latin;"><li><span style="line-height: 1.42857143;">l''elenco delle tipologie di controllo a cui sono assoggettate le imprese in ragione della dimensione e del settore di attività, indicando per ciascuna di esse i criteri e le relative modalità di svolgimento;</span></li><li><span style="line-height: 1.42857143;">l''elenco degli obblighi e degli adempimenti oggetto delle attività di controllo che le imprese sono tenute a rispettare per ottemperare alle disposizioni normative.</span></li></ol></ol>' WHERE title ILIKE 'Controlli sulle imprese';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 37 Comma 1,2</i></p><p>Obblighi di pubblicazione concernenti i contratti pubblici di lavori, servizi e forniture</p><p><ol><li><span style="line-height: 1.42857143;">Fermi restando gli altri obblighi di pubblicità legale e, in particolare, quelli previsti dall''articolo 1, comma 32, della legge 6 novembre 2012, n. 190, ciascuna amministrazione pubblica, secondo quanto previsto dal decreto legislativo 12 aprile 2006, n. 163, e, in particolare, dagli articoli 63, 65, 66, 122, 124, 206 e 223, le informazioni relative alle procedure per l''affidamento e l''esecuzione di opere e lavori pubblici, servizi e forniture.</span></li><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni sono tenute altresì a pubblicare, nell''ipotesi di cui all''articolo 57, comma 6, del decreto legislativo 12 aprile 2006, n. 163, la delibera a contrarre.</span></li></ol></p>' WHERE title ILIKE 'Bandi di gara e contratti';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 27</i></p><p></p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">La pubblicazione di cui all''articolo 26, comma 2, comprende necessariamente, ai fini del comma 3 del medesimo articolo:</span></li><ol style="list-style-type:lower-latin;"><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">il nome dell''impresa o dell''ente e i rispettivi dati fiscali o il nome di altro soggetto beneficiario;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">l''importo del vantaggio economico corrisposto;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">la norma o il titolo a base dell''attribuzione;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">l''ufficio e il funzionario o dirigente responsabile del relativo procedimento amministrativo;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">la modalità seguita per l''individuazione del beneficiario;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">il link al progetto selezionato e al curriculum del soggetto incaricato.</span></li></ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le informazioni di cui al comma 1 sono riportate, nell''ambito della sezione «Amministrazione trasparente» e secondo modalità di facile consultazione, in formato tabellare aperto che ne consente l''esportazione, il trattamento e il riutilizzo ai sensi dell''articolo 7 e devono essere organizzate annualmente in unico elenco per singola amministrazione.</span><br></li></ol><p></p><p><br></p><p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 26 Comma 2</i></p><p>2. Le pubbliche amministrazioni pubblicano gli atti di concessione delle sovvenzioni, contributi, sussidi ed ausili finanziari alle imprese, e comunque di vantaggi economici di qualunque genere a persone ed enti pubblici e privati ai sensi del citato articolo 12 della legge n. 241 del 1990, di importo superiore a mille euro.</p><p><br></p><p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 26 Comma 1</i></p><p></p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano gli atti con i quali sono determinati, ai sensi dell''articolo 12 della legge 7 agosto 1990, n. 241, i criteri e le modalità cui le amministrazioni stesse devono attenersi per la concessione di sovvenzioni, contributi, sussidi ed ausili finanziari e per l''attribuzione di vantaggi economici di qualunque genere a persone ed enti pubblici e privati.</span><br></li></ol>' WHERE title ILIKE 'Sovvenzioni, contributi, sussidi, vantaggi economici';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 26 Comma 2</i></p><p>2. Le pubbliche amministrazioni pubblicano gli atti di concessione delle sovvenzioni, contributi, sussidi ed ausili finanziari alle imprese, e comunque di vantaggi economici di qualunque genere a persone ed enti pubblici e privati ai sensi del citato articolo 12 della legge n. 241 del 1990, di importo superiore a mille euro.</p><p><b><br></b></p><p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 26 Comma 1</i></p><p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano gli atti con i quali sono determinati, ai sensi dell''articolo 12 della legge 7 agosto 1990, n. 241, i criteri e le modalità cui le amministrazioni stesse devono attenersi per la concessione di sovvenzioni, contributi, sussidi ed ausili finanziari e per l''attribuzione di vantaggi economici di qualunque genere a persone ed enti pubblici e privati.</span><br></li></ol></p>' WHERE title ILIKE 'Criteri e modalità';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 27</i></p><p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">La pubblicazione di cui all''articolo 26, comma 2, comprende necessariamente, ai fini del comma 3 del medesimo articolo:</span></li><ol style="list-style-type:lower-latin;"><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">il nome dell''impresa o dell''ente e i rispettivi dati fiscali o il nome di altro soggetto beneficiario;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">l''importo del vantaggio economico corrisposto;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">la norma o il titolo a base dell''attribuzione;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">l''ufficio e il funzionario o dirigente responsabile del relativo procedimento amministrativo;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">la modalità seguita per l''individuazione del beneficiario;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">il link al progetto selezionato e al curriculum del soggetto incaricato.</span></li></ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le informazioni di cui al comma 1 sono riportate, nell''ambito della sezione «Amministrazione trasparente» e secondo modalità di facile consultazione, in formato tabellare aperto che ne consente l''esportazione, il trattamento e il riutilizzo ai sensi dell''articolo 7 e devono essere organizzate annualmente in unico elenco per singola amministrazione.</span><br></li></ol></p><p><br></p><p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 26 Comma 2</i></p><p>2. Le pubbliche amministrazioni pubblicano gli atti di concessione delle sovvenzioni, contributi, sussidi ed ausili finanziari alle imprese, e comunque di vantaggi economici di qualunque genere a persone ed enti pubblici e privati ai sensi del citato articolo 12 della legge n. 241 del 1990, di importo superiore a mille euro.</p><p><br></p><p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 26 Comma 1</i></p><p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano gli atti con i quali sono determinati, ai sensi dell''articolo 12 della legge 7 agosto 1990, n. 241, i criteri e le modalità cui le amministrazioni stesse devono attenersi per la concessione di sovvenzioni, contributi, sussidi ed ausili finanziari e per l''attribuzione di vantaggi economici di qualunque genere a persone ed enti pubblici e privati.</span><br></li></ol></p>' WHERE title ILIKE 'Atti di concessione';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 29 Comma 1</i></p><p>Obblighi di pubblicazione del bilancio, preventivo e consuntivo, e del Piano degli indicatori e risultati attesi di bilancio, nonchè dei dati concernenti il monitoraggio degli obiettivi</p><p><br></p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano i dati relativi al bilancio di previsione e a quello consuntivo di ciascun anno in forma sintetica, aggregata e semplificata, anche con il ricorso a rappresentazioni grafiche, al fine di assicurare la piena accessibilità e comprensibilità.</span><br></li></ol></p>' WHERE title ILIKE 'Bilancio preventivo e consuntivo';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 29 Comma 2</i></p><p><span>2. Le pubbliche amministrazioni pubblicano il Piano di cui all''articolo 19 del decreto legislativo 31 maggio 2011, n. 91, con le integrazioni e gli aggiornamenti di cui all''articolo 22 del medesimo decreto legislativo n. 91 del 2011.</span></p>' WHERE title ILIKE 'Piano degli indicatori e risultati attesi di bilancio';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 30</i></p><p>Obblighi di pubblicazione concernenti i beni immobili e la gestione del patrimonio.</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano le informazioni identificative degli immobili posseduti, nonchè i canoni di locazione o di affitto versati o percepiti.</span><br></li></ol></p>' WHERE title ILIKE 'Patrimonio immobiliare';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 30</i></p><p>Obblighi di pubblicazione concernenti i beni immobili e la gestione del patrimonio.</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano le informazioni identificative degli immobili posseduti, nonchè i canoni di locazione o di affitto versati o percepiti.</span><br></li></ol></p>' WHERE title ILIKE 'Canoni di locazione o affitto';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>31 Comma 1</i></p><p>Obblighi di pubblicazione concernenti i dati relativi ai controlli sull''organizzazione e sull''attività dell''amministrazione.</p><p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano, unitamente agli atti cui si riferiscono, i rilievi non recepiti degli organi di controllo interno, degli organi di revisione amministrativa e contabile e tutti i rilievi ancorchè recepiti della Corte dei conti, riguardanti l''organizzazione e l''attività dell''amministrazione o di singoli uffici.</span><br></li></ol></p>' WHERE title ILIKE 'Controlli e rilievi sull''amministrazione';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>33</i></p><p>Obblighi di pubblicazione concernenti i tempi di pagamento dell''amministrazione</p><p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano, con cadenza annuale, un indicatore dei propri tempi medi di pagamento relativi agli acquisti di beni, servizi e forniture, denominato: «indicatore di tempestività dei pagamenti».</span><br></li></ol></p>' WHERE title ILIKE 'Indicatore di tempestività dei pagamenti';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>articolo 36</i></p><p>Pubblicazione delle informazioni necessarie per l''effettuazione di pagamenti informatici</p><p><br></p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano e specificano nelle richieste di pagamento i dati e le informazioni di cui all''articolo 5 del decreto legislativo 7 marzo 2005, n. 82.</span><br></li></ol></p>' WHERE title ILIKE 'IBAN e pagamenti informatici';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33 - DLgs 12 aprile 2006, n. 163</b></p><p><i>articolo 38</i></p><p>Pubblicità dei processi di pianificazione, realizzazione e valutazione delle opere pubbliche</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano tempestivamente sui propri siti istituzionali: i documenti di programmazione anche pluriennale delle opere pubbliche di competenza dell''amministrazione, le linee guida per la valutazione degli investimenti; le relazioni annuali; ogni altro documento predisposto nell''ambito della valutazione, ivi inclusi i pareri dei valutatori che si discostino dalle scelte delle amministrazioni e gli esiti delle valutazioni ex post che si discostino dalle valutazioni ex ante; le informazioni relative ai Nuclei di valutazione e verifica degli investimenti pubblici di cui all''articolo 1 della legge 17 maggio 1999, n. 144, incluse le funzioni e i compiti specifici ad essi attribuiti, le procedure e i criteri di individuazione dei componenti e i loro nominativi.</span></li><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano, fermi restando gli obblighi di pubblicazione di cui all''articolo 128 del decreto legislativo 12 aprile 2006, n. 163, le informazioni relative ai tempi, ai costi unitari e agli indicatori di realizzazione delle opere pubbliche completate. Le informazioni sui costi sono pubblicate sulla base di uno schema tipo redatto dall''Autorità per la vigilanza sui contratti pubblici di lavori, servizi e forniture, che ne cura altresì la raccolta e la pubblicazione nel proprio sito web istituzionale al fine di consentirne una agevole comparazione.</span></li></ol></p>' WHERE title ILIKE 'Opere pubbliche';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>39</i></p><p>Trasparenza dell''attività di pianificazione e governo del territorio</p><p></p><ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Le pubbliche amministrazioni pubblicano:</span></li><ol style="list-style-type:lower-latin;"><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">gli atti di governo del territorio, quali, tra gli altri, piani territoriali, piani di coordinamento, piani paesistici, strumenti urbanistici, generali e di attuazione, nonchè le loro varianti;</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">per ciascuno degli atti di cui alla lettera a) sono pubblicati, tempestivamente, gli schemi di provvedimento prima che siano portati all''approvazione; le delibere di adozione o approvazione; i relativi allegati tecnici.</span></li></ol><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">La documentazione relativa a ciascun procedimento di presentazione e approvazione delle proposte di trasformazione urbanistica d''iniziativa privata o pubblica in variante allo strumento urbanistico generale comunque denominato vigente nonchè delle proposte di trasformazione urbanistica d''iniziativa privata o pubblica in attuazione dello strumento urbanistico generale vigente che comportino premialità edificatorie a fronte dell''impegno dei privati alla realizzazione di opere di urbanizzazione extra oneri o della cessione di aree o volumetrie per finalità di pubblico interesse è pubblicata in una sezione apposita nel sito del comune interessato, continuamente aggiornata.</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">La pubblicità degli atti di cui al comma 1, lettera a), è'' condizione per l''acquisizione dell''efficacia degli atti stessi.</span></li><li><span style="color: rgb(51, 51, 51); line-height: 1.42857143;">Restano ferme le discipline di dettaglio previste dalla vigente legislazione statale e regionale.</span></li></ol>' WHERE title ILIKE 'Pianificazione e governo del territorio';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>40</i></p><p>Pubblicazione e accesso alle informazioni ambientali</p><p><ol><li><span style="line-height: 1.42857143;">In materia di informazioni ambientali restano ferme le disposizioni di maggior tutela già previste dall''articolo 3-sexies del decreto legislativo 3 aprile 2006 n. 152, dalla legge 16 marzo 2001, n. 108, nonchè dal decreto legislativo 19 agosto 2005 n. 195.</span></li><li><span style="line-height: 1.42857143;">Le amministrazioni di cui all''articolo 2, comma 1, lettera b), del decreto legislativo n. 195 del 2005, pubblicano, sui propri siti istituzionali e in conformità a quanto previsto dal presente decreto, le informazioni ambientali di cui all''articolo 2, comma 1, lettera a), del decreto legislativo 19 agosto 2005, n. 195, che detengono ai fini delle proprie attività istituzionali, nonchè le relazioni di cui all''articolo 10 del medesimo decreto legislativo. Di tali informazioni deve essere dato specifico rilievo all''interno di un''apposita sezione detta «Informazioni ambientali».</span></li><li><span style="line-height: 1.42857143;">Sono fatti salvi i casi di esclusione del diritto di accesso alle informazioni ambientali di cui all''articolo 5 del decreto legislativo 19 agosto 2005, n. 195.</span></li><li><span style="line-height: 1.42857143;">L''attuazione degli obblighi di cui al presente articolo non è in alcun caso subordinata alla stipulazione degli accordi di cui all''articolo 11 del decreto legislativo 19 agosto 2005, n. 195. Sono fatti salvi gli effetti degli accordi eventualmente già stipulati, qualora assicurino livelli di informazione ambientale superiori a quelli garantiti dalle disposizioni del presente decreto. Resta fermo il potere di stipulare ulteriori accordi ai sensi del medesimo articolo 11, nel rispetto dei livelli di informazione ambientale garantiti dalle disposizioni del presente decreto.</span></li></ol></p>' WHERE title ILIKE 'Informazioni ambientali';

UPDATE trasparenza_voice SET reference = '<p><b>DLgs 14 marzo 2013, n. 33</b></p><p><i>41 Comma 4</i></p><p><span>E'' pubblicato e annualmente aggiornato l''elenco delle strutture sanitarie private accreditate. Sono altresì pubblicati gli accordi con esse intercorsi.</span></p>' WHERE title ILIKE 'Strutture sanitarie private accreditate';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 14 marzo 2013, n. 33</b></p><p><i>42</i></p><p>Obblighi di pubblicazione concernenti gli interventi straordinari e di emergenza che comportano deroghe alla legislazione vigente.</p><p><ol><li><span style="line-height: 1.42857143;">Le pubbliche amministrazioni che adottano provvedimenti contingibili e urgenti e in generale provvedimenti di carattere straordinario in caso di calamità naturali o di altre emergenze, ivi</span><br></li></ol></p><p>comprese le amministrazioni commissariali e straordinarie costituite in base alla legge 24 febbraio 1992, n. 225, o a provvedimenti legislativi di urgenza, pubblicano:</p><p>a. i provvedimenti adottati, con la indicazione espressa delle norme di legge eventualmente derogate e dei motivi della deroga, nonché l''indicazione di eventuali atti amministrativi o giurisdizionali intervenuti;</p><p>b. i termini temporali eventualmente fissati per l''esercizio dei poteri di adozione dei provvedimenti straordinari;</p><p>c. il costo previsto degli interventi e il costo effettivo sostenuto dall''amministrazione;</p><p><span>d. le particolari forme di partecipazione degli interessati ai procedimenti di adozione dei provvedimenti straordinari.</span></p>' WHERE title ILIKE 'Interventi straordinari e di emergenza';

UPDATE trasparenza_voice SET reference = '<p><b>Delibera Civit n. 50/2013</b></p><p><i>Allegato 1 Delibera Civit n. 50/2013</i></p><p><span>Occorre pubblicare i seguenti contenuti: Piano triennale di prevenzione della corruzione, Responsabile della prevenzione della corruzione, Responsabile della trasparenza, Regolamenti per la prevenzione e la repressione della corruzione e dell''illegalità, Relazione del responsabile della corruzione, Atti di adeguamento a provvedimenti CiVIT (ora ANAC), Atti di accertamento delle violazioni</span></p>' WHERE title ILIKE 'Altri contenuti - Corruzione';

UPDATE trasparenza_voice SET reference = '<p><b>Dlgs 33/2013, Delibera Civit n. 50/2013</b></p><p><i>articolo 5 - Allegato 1 Delibera Civit n. 50/2013</i></p><p>Occorre Pubblicare: nome del Responsabile della trasparenza cui è presentata la richiesta di accesso civico, nonchè modalità per l''esercizio di tale diritto, con indicazione dei recapiti telefonici e delle caselle di posta elettronica istituzionale; nome del titolare del potere sostitutivo, attivabile nei casi di ritardo o mancata risposta, con indicazione dei recapiti telefonici e delle caselle di posta elettronica istituzionale.</p>' WHERE title ILIKE 'Altri contenuti - Accesso civico';

-- New Prints parameters
-- Alignments: L, C, R
-- Logos: Base64 of the image
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_LOGO', 'data:image/png;base64,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');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_1ROW_TEXT', 'MINISTERO DELL''ISTRUZIONE, DELL''UNIVERSITÀ E DELLA RICERCA');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_1ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_2ROW_TEXT', (SELECT 'UFFICIO SCOLASTICO REGIONALE PER LA REGIONE ' || upper(r.name) FROM institute AS i, contact AS c, cities AS cc, regions AS r WHERE def = 't' AND i.contact_id = c.contact_id AND c.city_id = cc.city_id AND cc.region = r.code));
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_2ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_3ROW_TEXT', (SELECT upper(name) FROM institute WHERE def = 't'));
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_3ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_4ROW_TEXT', (SELECT upper(c.address) || ', ' || c.cap || ' ' || upper(cc.description) || ' (' || upper(cc.province) || ') C.F. ' || i.fiscal_code FROM institute AS i, contact AS c, cities AS cc, regions AS r WHERE def = 't' AND i.contact_id = c.contact_id AND c.city_id = cc.city_id AND cc.region = r.code));
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_HEADER_4ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_1ROW_TEXT', '');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_1ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_2ROW_TEXT', '');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_2ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_3ROW_TEXT', '');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_3ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_4ROW_TEXT', '');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_4ROW_ALIGNMENT', 'L');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_PAGENUMBER_TEXT', 'Pagina # di @');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_FOOTER_PAGENUMBER_ALIGNMENT', 'C');

INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_MARGIN_TOP', '5');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_MARGIN_RIGHT', '5');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_MARGIN_BOTTOM', '5');
INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_MARGIN_LEFT', '5');

-- New Password for admin
ALTER TABLE users ADD COLUMN expiration bigint NOT NULL DEFAULT -1;
ALTER TABLE users_del ADD COLUMN expiration bigint DEFAULT -1;
COMMENT ON COLUMN users.expiration IS '-1 To set as not expirable.';

UPDATE users set expiration = 1441058400 WHERE user_name != 'mc2master';
UPDATE users set expiration = 0 WHERE user_name = 'admin' AND user_password = '21232f297a57a5a743894a0e4a801fc3';

-- Protocol constraints
UPDATE protocol_protocol SET send_method_id = NULL WHERE send_method_id = 0 OR send_method_id = -1;
UPDATE protocol_protocol SET subject_kind_id = NULL WHERE subject_kind_id = 0 OR subject_kind_id = -1;

ALTER TABLE protocol_protocol
    ADD CONSTRAINT protocol_protocol_send_method_id_fkey
    FOREIGN KEY (send_method_id)
    REFERENCES protocol_send_method (id)
    MATCH SIMPLE ON UPDATE NO ACTION ON DELETE SET NULL;

ALTER TABLE protocol_protocol
    ADD CONSTRAINT protocol_protocol_subject_kind_id_fkey
    FOREIGN KEY (subject_kind_id)
    REFERENCES protocol_subject_kind (id)
    MATCH SIMPLE ON UPDATE NO ACTION ON DELETE SET NULL;

CREATE INDEX protocol_protocol_send_method_id_fki
  ON protocol_protocol
  USING btree
  (send_method_id);

CREATE INDEX protocol_protocol_subject_kind_id_fki
  ON protocol_protocol
  USING btree
  (subject_kind_id); 

CREATE INDEX protocol_protocol_date_fki
  ON protocol_protocol
  USING btree
  (date);

CREATE INDEX protocol_protocol_canceled_fki
  ON protocol_protocol
  USING btree
  (canceled);


-- Function: mt_str_replace(text, text, text, text, boolean)

-- DROP FUNCTION mt_str_replace(text, text, text, text, boolean);

CREATE OR REPLACE FUNCTION mt_str_replace(cerca text, sostituisci text, tabella text, campo text, modifica boolean)
  RETURNS SETOF record AS
$BODY$
DECLARE
    tabelle  RECORD;
    colonne  RECORD;
    ricerca  RECORD;
    _tabella text := '';
    _campo   text := '';
    sql1     text := '';
    sql2     text := '';
    sql3     text := '';
BEGIN

    IF tabella IS NOT NULL THEN
	_tabella := 'AND c.relname = '|| quote_literal(tabella);
    END IF;

    sql1:= 'SELECT c.relname::text AS nome
            FROM pg_catalog.pg_class c
            LEFT JOIN pg_catalog.pg_user u ON u.usesysid = c.relowner
            LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
            WHERE c.relkind IN (''r'')
                AND n.nspname NOT IN (''pg_catalog'', ''pg_toast'')
                AND pg_catalog.pg_table_is_visible(c.oid)
                AND c.relkind = ''r''
                '|| _tabella ||'
            ORDER BY 1';

    -- Tabelle
    FOR tabelle IN EXECUTE(sql1) LOOP

	IF campo IS NOT NULL THEN
	    _campo := 'AND a.attname = '|| quote_literal(campo);
	END IF;

	sql2:= 'SELECT a.attname::text AS colonna
                FROM pg_catalog.pg_attribute a
                WHERE
                    a.attnum > 0
                    '|| _campo ||'
                    AND (
			 pg_catalog.format_type(a.atttypid, a.atttypmod) IN (''text'', ''character varying'')
			 OR
			 pg_catalog.format_type(a.atttypid, a.atttypmod) LIKE ''character varying(%)''
		    )
                    AND NOT a.attisdropped
                    AND a.attrelid = (
                        SELECT c.oid
                        FROM pg_catalog.pg_class c
                        LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
                        WHERE c.relname ~ '|| quote_literal('^('|| tabelle.nome ||')$') ||'
			    AND pg_catalog.pg_table_is_visible(c.oid)
                    )
                ORDER BY 1';

        -- Colonne tabella
        FOR colonne IN EXECUTE(sql2) LOOP

            sql3:= 'SELECT
			'|| quote_literal(tabelle.nome) ||'::text AS tabella,
			'|| quote_literal(colonne.colonna) ||'::text AS colonna,
			'|| quote_literal('%'||cerca||'%') ||'::text AS condizione,
			'|| colonne.colonna ||'::text AS trova,
                        trim( BOTH '|| quote_literal(' ') ||' FROM
                            replace('|| colonne.colonna ||',
                                    '|| quote_literal(cerca) ||',
                                    '|| quote_literal(sostituisci) ||')
                        )::text AS sostituisci
                    FROM '|| tabelle.nome ||'
                    WHERE '|| colonne.colonna ||'
			LIKE '|| quote_literal('%'||cerca||'%');

            -- Update
            FOR ricerca IN EXECUTE(sql3) LOOP

                IF ricerca.trova <> ricerca.sostituisci THEN  -- fix: per evitare update dovuti a regexp errate

                    IF modifica = true THEN

                        EXECUTE 'UPDATE '|| tabelle.nome ||'
                                 SET '|| colonne.colonna ||' = '|| quote_literal(ricerca.sostituisci) ||'
                                 WHERE '|| colonne.colonna ||' = '|| quote_literal(ricerca.trova);

                    END IF;

                    RETURN NEXT ricerca;

                END IF;

            END LOOP;  -- End update

	END LOOP;  -- End colonne

    END LOOP;  -- End tabelle

END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;



-- View: protocol_view_protocol

-- DROP VIEW protocol_view_protocol;

CREATE OR REPLACE VIEW protocol_view_protocol AS
SELECT p.id, p.date, p.note, p.budget_id, p.type_id, p.rec_id, p.rec_type, p.protocol_number, p.obj_id, p.direction, p.description, p.correspondents_text, p.canceled, p.external_act_number, p.send_method_id, p.dossier, p.subject_kind_id, p.reserved, sm.title AS send_method_text, sk.title AS subject_kind_text,
(
    SELECT btrim((((c.c3::text || ' '::text) || c.c2::text) || ' '::text) || c.c1::text, ' '::text) AS btrim
    FROM
        (SELECT CASE WHEN p3.code IS NULL THEN ''::character varying ELSE p3.code END AS c3,
            CASE WHEN p2.code IS NULL THEN ''::character varying ELSE p2.code END AS c2,
            CASE WHEN p1.code IS NULL THEN ''::character varying ELSE p1.code END AS c1
         FROM protocol_type p1
         LEFT JOIN protocol_type p2 ON p1.parent_type_id = p2.id
         LEFT JOIN protocol_type p3 ON p2.parent_type_id = p3.id
         WHERE p1.id = p.type_id) c
) AS type_text,
(
    SELECT count(pd.document_id) AS count
    FROM protocol_protocol_document pd
    WHERE pd.protocol_id = p.id
) AS count_documents,
(
    SELECT string_agg(pd.document_id::text, ','::text) AS string_agg
    FROM protocol_protocol_document pd
    WHERE pd.protocol_id = p.id
) AS linked_documents,
(
    SELECT count(pc.correspondent_id) AS count
    FROM protocol_protocol_correspondent pc
    WHERE pc.protocol_id = p.id
) AS count_correspondents,
(
    SELECT string_agg(pc.correspondent_id::text, ','::text) AS string_agg
    FROM protocol_protocol_correspondent pc
    WHERE pc.protocol_id = p.id
) AS linked_correspondents,
(
    SELECT count(pp.protocol_1_id) AS count
    FROM protocol_protocol_protocol pp
    WHERE pp.protocol_1_id = p.id OR pp.protocol_2_id = p.id
) AS count_protocols,
(
    SELECT string_agg(protocols.id::text, ','::text) AS string_agg
    FROM
    (
        SELECT pp.protocol_1_id AS id
        FROM protocol_protocol_protocol pp
        WHERE pp.protocol_2_id = p.id
        UNION
        SELECT pp.protocol_2_id AS id
        FROM protocol_protocol_protocol pp
        WHERE pp.protocol_1_id = p.id
    ) protocols
) AS linked_protocols
FROM protocol_protocol p
LEFT JOIN protocol_type AS pt ON p.type_id = pt.id
LEFT JOIN protocol_send_method AS sm ON p.send_method_id = sm.id
LEFT JOIN protocol_subject_kind AS sk ON p.subject_kind_id = sk.id;

ALTER TABLE protocol_protocol ALTER COLUMN id SET DEFAULT nextval(('public.protocol_protocol_id_seq'::text)::regclass);

