ALTER TABLE ccp_movement_additional ADD COLUMN abs_amount DOUBLE PRECISION NOT NULL DEFAULT 0;
ALTER TABLE audit.ccp_movement_additional ADD COLUMN abs_amount DOUBLE PRECISION NOT NULL DEFAULT 0;


DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT
    mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.invoice_number,
    mv.expiration_date,
    mv.da_ratei,
    mv.a_ratei,
    mv.description,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    mv.total_additionals,
    (
        mv.amount +
        mv.total_additionals
    )    AS total,
    mv.invoice_id,
    mv.invoice_code
   FROM (
       SELECT
            m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.invoice_code,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            m.da_ratei,
            m.a_ratei,
            (
                SELECT number from ccp_invoice where id = m.invoice_id LIMIT 1
            ) AS invoice_number,
            t.name AS type_text,
            m.description AS description,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (
                SELECT COALESCE(SUM(abs_amount)::numeric(14,2) , 0::double precision) FROM ccp_movement_additional where movement_id=m.id
            ) AS total_additionals,
            (
                (
                    SELECT
                        COALESCE(
                            sum(ccp_payment.amount), 0::double precision
                        ) AS sum
                    FROM
                        ccp_payment
                    WHERE
                        ccp_payment.movement_id = m.id
                )
            )::numeric(14,2) AS total_payments,
            (
                SELECT
                    count(ccp_payment.id) AS count
                FROM
                    ccp_payment
                WHERE
                    ccp_payment.movement_id = m.id
            ) AS count_payments,
            (
                SELECT
                    count(ccp_movement_additional.movement_id) AS count
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id
            ) AS count_additionals,
            (
                SELECT
                    string_agg(cma.additional_id::text, ','::text) AS string_agg
                FROM
                    ccp_movement_additional cma
                WHERE
                    cma.movement_id = m.id
            ) AS linked_additionals,
            (
                SELECT
                    string_agg(cp.id::text, ','::text) AS string_agg
                FROM
                    ccp_payment cp
                WHERE
                    cp.movement_id = m.id
            ) AS linked_payments

       FROM
            ccp_movement m,
            ccp_type t,
            ccp_category c
       WHERE
            m.type_id = t.id AND
            t.category_id = c.id
  ) mv;

