ALTER TABLE ccp_payment ADD COLUMN payment_group INTEGER;

ALTER TABLE audit.ccp_payment ADD COLUMN payment_group INTEGER;


DROP VIEW public.ccp_view_payment;

CREATE OR REPLACE VIEW public.ccp_view_payment AS
 SELECT pay.id,
    pay.expiration_date,
    pay.movement_id,
    pay.creation_date,
    pay.operation_date,
    pay.accountable_date,
    pay.amount,
    pay.payment_method_id,
    pay.bollettino,
    pay.account_reference,
    pay.ccp_credit,
    pay.covered_movement_id,
    pay.payer_type,
    pay.payer_id,
    pay.payer_name,
    pay.payer_surname,
    pay.payer_fiscal_code,
    pay.payer_address,
    pay.payer_city,
    pay.payer_province,
    pay.payer_zip_code,
    pay.account_id,
    pay.receipt_id,
    pay.payment_group,
    pay.payer_data,
    pay.receipt_number,
    pay.receipt_date,
    pay.payment_method_text,
    pay.account_text,
    pay.movement_number,
    pay.subject_type,
    pay.subject_id,
    pay.subject_data,
    pay.subject_class,
    pay.subject_school_address_code,
    pay.subject_school_address,
    pay.subject_seat,
    pay.type_id,
    pay.section AS type_section,
    pay.type_text,
    pay.description,
    pay.category_id,
    pay.category_text,
    pay.incoming,
    pay.positive_additionals_euro,
    pay.negative_additionals_euro,
    pay.positive_additionals_perc,
    pay.negative_additionals_perc,
    pay.count_additionals,
    pay.note,
    pay.miscellaneous,
    pay.school_year,
    pay.subject_school_year,
    pay.linked_additionals,
    (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id,
            m.expiration_date,
            p.movement_id,
            p.operation_date,
            p.accountable_date,
            p.amount,
            p.payment_method_id,
            p.bollettino,
            p.account_reference,
            p.ccp_credit,
            p.covered_movement_id,
            p.payer_type,
            p.payer_id,
            p.payer_name,
            p.payer_surname,
            p.payer_fiscal_code,
            p.payer_address,
            p.payer_city,
            p.payer_province,
            p.payer_zip_code,
            p.account_id,
            p.receipt_id,
            p.payer_data,
            p.receipt_number,
            p.receipt_date,
            p.payment_group,
            w.name AS payment_method_text,
            a.denomination AS account_text,
            m.school_year,
            m.subject_school_year,
            m.number AS movement_number,
            m.creation_date,
            m.subject_type,
            m.subject_id,
            m.subject_data,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.subject_seat,
            m.description,
            m.type_id,
            t.section,
            m.note,
            m.miscellaneous,
            t.name AS type_text,
            t.category_id,
            c.name AS category_text,
            t.incoming,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals,
            ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id,
                    cp.movement_id,
                    cp.operation_date,
                    cp.accountable_date,
                    cp.amount::numeric(14,2) AS amount,
                    cp.payment_method_id,
                    cp.bollettino,
                    cp.account_reference,
                    cp.ccp_credit,
                    cp.covered_movement_id,
                    cp.payer_type,
                    cp.payer_id,
                    cp.payer_name,
                    cp.payer_surname,
                    cp.payer_fiscal_code,
                    cp.payer_address,
                    cp.payer_city,
                    cp.payer_province,
                    cp.payer_zip_code,
                    cp.account_id,
                    cp.receipt_id,
                    cp.payment_group,
                    (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data,
                    cr.number AS receipt_number,
                    cr.date AS receipt_date
                   FROM ccp_payment cp
                     LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p,
            ccp_payment_method w,
            core_bank_account a,
            ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

