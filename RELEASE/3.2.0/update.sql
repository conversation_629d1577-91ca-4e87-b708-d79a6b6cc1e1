-- CCP overhaul

-- Additional
CREATE TABLE ccp_additional
(
   id serial NOT NULL,
   name character varying(50) NOT NULL,
   positive boolean NOT NULL DEFAULT true,
   percentual boolean NOT NULL DEFAULT false,
   CONSTRAINT ccp_additional_pkey PRIMARY KEY (id),
   CONSTRAINT ccp_additional_name_key UNIQUE (name)
);

COMMENT ON TABLE ccp_additional IS 'Additional of a movement.';

CREATE TABLE audit.ccp_additional
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   name character varying(50) NOT NULL,
   positive boolean NOT NULL,
   percentual boolean NOT NULL
);

CREATE TRIGGER ccp_additional_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_additional
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Movement Type Category
CREATE TABLE ccp_category
(
   id serial NOT NULL,
   name character varying(50) NOT NULL,
   initial_balance double precision NOT NULL DEFAULT 0.00,
   CONSTRAINT ccp_category_pkey PRIMARY KEY (id),
   CONSTRAINT ccp_category_name_key UNIQUE (name)
);

COMMENT ON TABLE ccp_category IS 'Macro-category of a movement type.';

CREATE TABLE audit.ccp_category
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   name character varying(50) NOT NULL,
   initial_balance double precision NOT NULL
);

CREATE TRIGGER ccp_category_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_category
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Data insertion
INSERT INTO ccp_category (name, initial_balance) VALUES ('Tasse', (SELECT
  CASE WHEN EXISTS (SELECT tasse FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals))
    THEN (
        SELECT CASE WHEN tasse IS NULL THEN 0.00 ELSE tasse END
        FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals)
      )
    ELSE 0.00
  END));
INSERT INTO ccp_category (name, initial_balance) VALUES ('Contributi', (SELECT
  CASE WHEN EXISTS (SELECT contributi FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals))
    THEN (
        SELECT CASE WHEN contributi IS NULL THEN 0.00 ELSE contributi END
        FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals)
      )
    ELSE 0.00
  END));
INSERT INTO ccp_category (name, initial_balance) VALUES ('Quote', (SELECT
  CASE WHEN EXISTS (SELECT quote FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals))
    THEN (
        SELECT CASE WHEN quote IS NULL THEN 0.00 ELSE quote END
        FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals)
      )
    ELSE 0.00
  END));
INSERT INTO ccp_category (name, initial_balance) VALUES ('Diversi', (SELECT
  CASE WHEN EXISTS (SELECT diversi FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals))
    THEN (
        SELECT CASE WHEN diversi IS NULL THEN 0.00 ELSE diversi END
        FROM tax_residuals WHERE year = (SELECT min(year) FROM tax_residuals)
      )
    ELSE 0.00
  END));


-- Movement Type
ALTER TABLE tipi_tasse RENAME TO ccp_type;
ALTER TABLE audit.tipi_tasse RENAME TO ccp_type;

DROP TRIGGER tipi_tasse_audit ON ccp_type;

ALTER TABLE ccp_type RENAME id_tipo_tassa TO id;
ALTER TABLE ccp_type RENAME descrizione TO name;
ALTER TABLE ccp_type RENAME importo_base TO amount;
ALTER TABLE ccp_type RENAME cumulativa TO cumulative;
ALTER TABLE ccp_type RENAME tassa_governativa TO governative;
ALTER TABLE ccp_type RENAME data_scadenza  TO expiration_date;
ALTER TABLE ccp_type RENAME anno_scolastico_riferimento TO school_year;
ALTER TABLE audit.ccp_type RENAME id_tipo_tassa TO id;
ALTER TABLE audit.ccp_type RENAME descrizione TO name;
ALTER TABLE audit.ccp_type RENAME importo_base TO amount;
ALTER TABLE audit.ccp_type RENAME cumulativa TO cumulative;
ALTER TABLE audit.ccp_type RENAME tassa_governativa TO governative;
ALTER TABLE audit.ccp_type RENAME data_scadenza  TO expiration_date;
ALTER TABLE audit.ccp_type RENAME anno_scolastico_riferimento TO school_year;

ALTER TABLE ccp_type ALTER COLUMN name TYPE character varying(255);
ALTER TABLE ccp_type ALTER COLUMN name DROP DEFAULT;
ALTER TABLE ccp_type ALTER COLUMN name SET NOT NULL;
ALTER TABLE ccp_type ALTER COLUMN amount SET NOT NULL;
ALTER TABLE ccp_type ALTER COLUMN cumulative SET NOT NULL;
ALTER TABLE ccp_type ALTER COLUMN governative SET NOT NULL;
ALTER TABLE ccp_type ALTER COLUMN expiration_date SET NOT NULL;
ALTER TABLE ccp_type ALTER COLUMN expiration_date DROP DEFAULT;
ALTER TABLE ccp_type ALTER COLUMN school_year TYPE character varying(9);
ALTER TABLE ccp_type ALTER COLUMN school_year SET DEFAULT 'TUTTI';
ALTER TABLE ccp_type ALTER COLUMN school_year SET NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN name TYPE character varying(255);
ALTER TABLE audit.ccp_type ALTER COLUMN name DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN name SET NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN amount SET NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN amount DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN cumulative SET NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN cumulative DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN governative SET NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN governative DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN expiration_date SET NOT NULL;
ALTER TABLE audit.ccp_type ALTER COLUMN expiration_date DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN school_year TYPE character varying(9);
ALTER TABLE audit.ccp_type ALTER COLUMN school_year DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN school_year SET NOT NULL;

ALTER TABLE ccp_type ADD COLUMN category_id integer NOT NULL DEFAULT 0;
ALTER TABLE audit.ccp_type ADD COLUMN category_id integer NOT NULL DEFAULT 0;

UPDATE ccp_type SET category_id = (SELECT id FROM ccp_category WHERE ccp_type.tipologia = upper(ccp_category.name));
UPDATE audit.ccp_type SET category_id = (SELECT id FROM ccp_category WHERE audit.ccp_type.tipologia = upper(ccp_category.name));

ALTER TABLE ccp_type DROP COLUMN tipologia;
ALTER TABLE audit.ccp_type DROP COLUMN tipologia;

ALTER TABLE ccp_type ALTER COLUMN category_id DROP DEFAULT;
ALTER TABLE audit.ccp_type ALTER COLUMN category_id DROP DEFAULT;

ALTER TABLE ccp_type DROP CONSTRAINT tipo_tassa_pkey;
ALTER TABLE ccp_type ADD CONSTRAINT ccp_type_pkey PRIMARY KEY (id);
ALTER TABLE ccp_type ADD CONSTRAINT ccp_type_category_id_fkey FOREIGN KEY (category_id) REFERENCES ccp_category (id) ON UPDATE CASCADE ON DELETE NO ACTION;

DROP INDEX tipo_tassa_id_key;

CREATE INDEX ccp_type_category_id_fki ON ccp_type USING btree (category_id);

CREATE TRIGGER ccp_type_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_type
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

ALTER SEQUENCE tipi_tasse_id_seq RENAME TO ccp_type_id_seq;


-- Movement
DROP INDEX tasse_id_key;

ALTER TABLE tasse RENAME TO ccp_movement;
ALTER TABLE audit.tasse RENAME TO ccp_movement;

DROP TRIGGER tasse_audit ON ccp_movement;

ALTER TABLE ccp_movement RENAME id_tasse TO id;
ALTER TABLE ccp_movement RENAME id_tipo_tassa TO type_id;
ALTER TABLE ccp_movement RENAME sede_ufficio_postale TO miscellaneous;
ALTER TABLE ccp_movement RENAME numero_versamento TO "number";
ALTER TABLE ccp_movement RENAME importo_versato TO amount;
ALTER TABLE ccp_movement RENAME id_studente TO subject_student_id;
ALTER TABLE ccp_movement RENAME employee_id TO subject_employee_id;
ALTER TABLE ccp_movement RENAME dati_debitore TO subject_data;
ALTER TABLE ccp_movement RENAME sede_studente TO subject_seat;
ALTER TABLE ccp_movement RENAME classe TO subject_class;
ALTER TABLE ccp_movement RENAME is_incoming TO incoming;
ALTER TABLE audit.ccp_movement RENAME id_tasse TO id;
ALTER TABLE audit.ccp_movement RENAME id_tipo_tassa TO type_id;
ALTER TABLE audit.ccp_movement RENAME sede_ufficio_postale TO miscellaneous;
ALTER TABLE audit.ccp_movement RENAME numero_versamento TO "number";
ALTER TABLE audit.ccp_movement RENAME importo_versato TO amount;
ALTER TABLE audit.ccp_movement RENAME id_studente TO subject_student_id;
ALTER TABLE audit.ccp_movement RENAME employee_id TO subject_employee_id;
ALTER TABLE audit.ccp_movement RENAME dati_debitore TO subject_data;
ALTER TABLE audit.ccp_movement RENAME sede_studente TO subject_seat;
ALTER TABLE audit.ccp_movement RENAME classe TO subject_class;
ALTER TABLE audit.ccp_movement RENAME is_incoming TO incoming;

ALTER TABLE ccp_movement ALTER COLUMN type_id DROP DEFAULT;
ALTER TABLE ccp_movement ALTER COLUMN miscellaneous TYPE text;
ALTER TABLE ccp_movement ALTER COLUMN miscellaneous DROP DEFAULT;
ALTER TABLE ccp_movement ALTER COLUMN "number" DROP DEFAULT;
--ALTER TABLE ccp_movement ALTER COLUMN "number" SET NOT NULL;
ALTER TABLE ccp_movement ALTER COLUMN note DROP DEFAULT;
ALTER TABLE ccp_movement ALTER COLUMN amount SET NOT NULL;
ALTER TABLE ccp_movement ALTER COLUMN subject_student_id DROP DEFAULT;
ALTER TABLE ccp_movement ALTER COLUMN subject_employee_id DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN type_id DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN miscellaneous TYPE text;
ALTER TABLE audit.ccp_movement ALTER COLUMN miscellaneous DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN "number" DROP DEFAULT;
--ALTER TABLE audit.ccp_movement ALTER COLUMN "number" SET NOT NULL;
ALTER TABLE audit.ccp_movement ALTER COLUMN note DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN amount SET NOT NULL;
ALTER TABLE audit.ccp_movement ALTER COLUMN amount DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN subject_student_id DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN subject_employee_id DROP DEFAULT;

ALTER TABLE ccp_movement ADD COLUMN exit_category_id integer;
ALTER TABLE ccp_movement ADD COLUMN date bigint NOT NULL DEFAULT 0;
ALTER TABLE ccp_movement ADD COLUMN subject_type character varying(1) NOT NULL DEFAULT 'O';
ALTER TABLE audit.ccp_movement ADD COLUMN exit_category_id integer;
ALTER TABLE audit.ccp_movement ADD COLUMN date bigint NOT NULL DEFAULT 0;
ALTER TABLE audit.ccp_movement ADD COLUMN subject_type character varying(1) NOT NULL DEFAULT 'O';

COMMENT ON COLUMN ccp_movement.subject_type IS 'S - Student, E - Employee, O - Other';

-- Updating fields
UPDATE ccp_movement SET exit_category_id = (SELECT id FROM ccp_category WHERE ccp_movement.tipologia_uscita = upper(ccp_category.name));
UPDATE audit.ccp_movement SET exit_category_id = (SELECT id FROM ccp_category WHERE audit.ccp_movement.tipologia_uscita = upper(ccp_category.name));
UPDATE ccp_movement SET subject_student_id = NULL WHERE subject_student_id = -1;
UPDATE ccp_movement SET subject_employee_id = NULL WHERE subject_employee_id = -1;
UPDATE ccp_movement SET type_id = NULL WHERE type_id = -1;

UPDATE ccp_movement SET date = data_estratto_conto;
UPDATE audit.ccp_movement SET date = data_estratto_conto;

UPDATE ccp_movement SET number = NULL WHERE number = '';

UPDATE ccp_movement SET subject_type = 'E' WHERE subject_employee_id IS NOT NULL;
UPDATE ccp_movement SET subject_type = 'S' WHERE subject_student_id IS NOT NULL;

UPDATE ccp_movement SET subject_employee_id = subject_student_id WHERE subject_student_id IS NOT NULL;

-- Drop obsolete constraints

ALTER TABLE ccp_movement DROP CONSTRAINT tasse_pkey;
ALTER TABLE ccp_movement DROP CONSTRAINT tasse_modalita_pagamento_fkey;

-- New constraints

ALTER TABLE ccp_movement ADD CONSTRAINT ccp_movement_pkey PRIMARY KEY (id);
ALTER TABLE ccp_movement ADD CONSTRAINT ccp_movement_type_id_fkey FOREIGN KEY (type_id) REFERENCES ccp_type (id) ON UPDATE CASCADE ON DELETE NO ACTION;
ALTER TABLE ccp_movement ADD CONSTRAINT ccp_movement_exit_category_id_fkey FOREIGN KEY (exit_category_id) REFERENCES ccp_category (id) ON UPDATE CASCADE ON DELETE NO ACTION;

ALTER TABLE ccp_movement ADD CONSTRAINT ccp_movement_subject_type_check CHECK (subject_type::text = ANY (ARRAY['S'::character varying, 'E'::character varying, 'O'::character varying]::text[]));

CREATE TRIGGER ccp_movement_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_movement
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Drop of obsolete fields

ALTER TABLE ccp_movement ALTER COLUMN date DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN date DROP DEFAULT;

ALTER TABLE ccp_movement ALTER COLUMN subject_type DROP DEFAULT;
ALTER TABLE audit.ccp_movement ALTER COLUMN subject_type DROP DEFAULT;

ALTER TABLE ccp_movement DROP COLUMN tipologia_uscita;
ALTER TABLE audit.ccp_movement DROP COLUMN tipologia_uscita;

ALTER TABLE ccp_movement DROP COLUMN subject_student_id;
ALTER TABLE audit.ccp_movement DROP COLUMN subject_student_id;

ALTER TABLE ccp_movement RENAME subject_employee_id TO subject_id;
ALTER TABLE audit.ccp_movement RENAME subject_employee_id TO subject_id;

ALTER SEQUENCE tasse_id_seq RENAME TO ccp_movement_id_seq;

-- Payment Method
CREATE TABLE ccp_payment_method
(
   id serial NOT NULL,
   name character varying(50) NOT NULL,
   code character varying(50),
   CONSTRAINT ccp_payment_method_pkey PRIMARY KEY (id),
   CONSTRAINT ccp_payment_method_name_key UNIQUE (name)
);

CREATE TABLE audit.ccp_payment_method
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   name character varying(50) NOT NULL,
   code character varying(50)
);

CREATE TRIGGER ccp_payment_method_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_payment_method
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

INSERT INTO ccp_payment_method (code, name) SELECT id, text FROM payment_ways;


-- Payment
CREATE TABLE ccp_payment
(
   id serial NOT NULL,
   movement_id integer NOT NULL,
   operation_date bigint NOT NULL,
   accountable_date bigint NOT NULL,
   amount double precision NOT NULL DEFAULT 0.00,
   payment_method_id integer NOT NULL,
   bollettino character varying(50),
   account_id character varying(10) NOT NULL,
   account_reference character varying(50),
   payer_type character varying(1) NOT NULL,
   payer_id character varying(30),
   payer_name character varying(50) NOT NULL,
   payer_surname character varying(50) NOT NULL,
   payer_fiscal_code character varying(20),
   payer_address character varying(80),
   payer_city character varying(30),
   payer_province character varying(2),
   payer_zip_code character varying(5),
   CONSTRAINT ccp_payment_pkey PRIMARY KEY (id),
   CONSTRAINT ccp_payment_movement_id_fkey FOREIGN KEY (movement_id) REFERENCES ccp_movement (id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT ccp_payment_payment_method_id_fkey FOREIGN KEY (payment_method_id) REFERENCES ccp_payment_method (id) ON UPDATE CASCADE ON DELETE NO ACTION,
   CONSTRAINT ccp_payment_amount_check CHECK (amount >= 0),
   CONSTRAINT ccp_payment_payer_type_check CHECK (payer_type IN ('S', 'P', 'T', 'E', 'O'))
);

COMMENT ON COLUMN ccp_payment.payer_type IS 'S - Student, P - Parent, T - Tutor, E - Employee, O - Other';
COMMENT ON TABLE ccp_payment IS 'A single payment for a movement.';

CREATE TABLE audit.ccp_payment
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   id integer NOT NULL,
   movement_id integer NOT NULL,
   operation_date bigint NOT NULL,
   accountable_date bigint NOT NULL,
   amount double precision NOT NULL,
   payment_method_id integer NOT NULL,
   bollettino character varying(50),
   account_id character varying(10) NOT NULL,
   account_reference character varying(50),
   payer_type character varying(1) NOT NULL,
   payer_id character varying(30),
   payer_name character varying(50) NOT NULL,
   payer_surname character varying(50) NOT NULL,
   payer_fiscal_code character varying(20),
   payer_address character varying(80),
   payer_city character varying(30),
   payer_province character varying(2),
   payer_zip_code character varying(5)
);

CREATE TRIGGER ccp_payment_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_payment
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- Insertion of ccp_payment rows

INSERT INTO ccp_payment (movement_id, operation_date, accountable_date, amount, bollettino, account_id, account_reference, payer_name, payer_surname, payer_type, payer_id, payment_method_id)
SELECT m.id, m.data_versamento, m.data_estratto_conto, m.amount, m.numero_bollettino, m.destinazione_pagamento, m.riferimento_estratto_conto, '', m.subject_data, m.subject_type, m.subject_id,
(CASE
    WHEN p.id IS NULL THEN (SELECT id FROM ccp_payment_method WHERE name = 'Contanti')
    ELSE p.id
END)
FROM ccp_movement AS m
LEFT JOIN ccp_payment_method AS p ON m.modalita_pagamento = p.code;

-- Drop of obsolete fields and tables

ALTER TABLE ccp_movement DROP COLUMN modalita_pagamento;
ALTER TABLE ccp_movement DROP COLUMN destinazione_pagamento;
ALTER TABLE ccp_movement DROP COLUMN numero_bollettino;
ALTER TABLE ccp_movement DROP COLUMN data_estratto_conto;
ALTER TABLE ccp_movement DROP COLUMN data_versamento;
ALTER TABLE ccp_movement DROP COLUMN riferimento_estratto_conto;
ALTER TABLE audit.ccp_movement DROP COLUMN modalita_pagamento;
ALTER TABLE audit.ccp_movement DROP COLUMN destinazione_pagamento;
ALTER TABLE audit.ccp_movement DROP COLUMN numero_bollettino;
ALTER TABLE audit.ccp_movement DROP COLUMN data_estratto_conto;
ALTER TABLE audit.ccp_movement DROP COLUMN data_versamento;
ALTER TABLE audit.ccp_movement DROP COLUMN riferimento_estratto_conto;

ALTER TABLE ccp_payment_method DROP COLUMN code;
ALTER TABLE audit.ccp_payment_method DROP COLUMN code;

DROP TABLE payment_ways;

-- Type - Additional
CREATE TABLE ccp_type_additional
(
   type_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL DEFAULT 0.00,
   CONSTRAINT ccp_type_additional_pkey PRIMARY KEY (type_id, additional_id),
   CONSTRAINT ccp_type_additional_type_id_fkey FOREIGN KEY (type_id) REFERENCES ccp_type (id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT ccp_type_additional_additional_id_fkey FOREIGN KEY (additional_id) REFERENCES ccp_additional (id) ON UPDATE CASCADE ON DELETE NO ACTION,
   CONSTRAINT ccp_type_additional_amount_check CHECK (amount >= 0)
);

CREATE INDEX ccp_type_additional_type_id_fki ON ccp_type_additional USING btree (type_id);
CREATE INDEX ccp_type_additional_additional_id_fki ON ccp_type_additional USING btree (additional_id);

CREATE TABLE audit.ccp_type_additional
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   type_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL
);

CREATE TRIGGER ccp_type_additional_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_type_additional
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();


-- Movement - Additional
CREATE TABLE ccp_movement_additional
(
   movement_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL DEFAULT 0.00,
   CONSTRAINT ccp_movement_additional_pkey PRIMARY KEY (movement_id, additional_id),
   CONSTRAINT ccp_movement_additional_movement_id_fkey FOREIGN KEY (movement_id) REFERENCES ccp_movement (id) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT ccp_movement_additional_additional_id_fkey FOREIGN KEY (additional_id) REFERENCES ccp_additional (id) ON UPDATE CASCADE ON DELETE NO ACTION,
   CONSTRAINT ccp_movement_additional_amount_check CHECK (amount >= 0)
);

CREATE INDEX ccp_movement_additional_movement_id_fki ON ccp_movement_additional USING btree (movement_id);
CREATE INDEX ccp_movement_additional_additional_id_fki ON ccp_movement_additional USING btree (additional_id);

CREATE TABLE audit.ccp_movement_additional
(
   op_action character varying(1) NOT NULL,
   op_date timestamp with time zone NOT NULL DEFAULT now(),
   movement_id integer NOT NULL,
   additional_id integer NOT NULL,
   amount double precision NOT NULL
);

CREATE TRIGGER ccp_movement_additional_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_movement_additional
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();