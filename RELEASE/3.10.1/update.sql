ALTER TABLE ccp_movement ADD COLUMN subject_school_address_code CHARACTER VARYING(255);
ALTER TABLE audit.ccp_movement ADD COLUMN subject_school_address_code CHARACTER VARYING(255);
ALTER TABLE ccp_movement ADD COLUMN subject_school_address CHARACTER VARYING(255);
ALTER TABLE audit.ccp_movement ADD COLUMN subject_school_address CHARACTER VARYING(255);

DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.expiration_date,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.positive_additionals_euro,
    mv.negative_additionals_euro,
    mv.positive_additionals_perc,
    mv.negative_additionals_perc,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total,
    mv.invoice_id
   FROM ( SELECT m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            t.name AS type_text,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments,
            ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals,
            ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals,
            ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;



   DROP VIEW ccp_view_payment;
CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.expiration_date, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_school_address_code, pay.subject_school_address,pay.subject_seat, pay.type_id, pay.section AS type_section, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, m.expiration_date as expiration_date, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_school_address_code, m.subject_school_address,m.subject_seat, m.type_id, t.section, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


INSERT INTO parameter (name, value) VALUES ('CCP_RECEIPT_FOOTER_IVA', 'Operazione esente IVA a norma dell''art. 10 n.20 del DPR 633 del 26/10/1972 e succ mod. BOLLO SULL''ORIGINALE');
select setval('auth_element_id_seq', (SELECT max(id) FROM auth_element)) ;
select setval('auth_permission_id_seq', (SELECT max(id) FROM auth_permission)) ;

INSERT INTO auth_permission (title, super_user, auth_section) VALUES ('Segreteria Digitale | Fascicoli | Visualizzare', 'f', 11);
INSERT INTO auth_element (name,control_interface,auth_permission,state) VALUES ('ArchiveDossierTab', 'ArchiveMainPnl', (SELECT max(id) FROM auth_permission), 'hide');

-- DROP TABLE ccp_deposit_slip;

CREATE TABLE ccp_deposit_slip (
    id serial PRIMARY KEY,
    "number" INTEGER NOT NULL,
    "date" TIMESTAMP WITH TIME ZONE NOT NULL,
    bank_account INTEGER REFERENCES core_bank_account(id) ON UPDATE NO ACTION ON DELETE NO ACTION,
    bank_account_iban CHARACTER VARYING(255) NOT NULL,
    bank_account_name CHARACTER VARYING(255) NOT NULL,
    payment_method INTEGER REFERENCES ccp_payment_method ON UPDATE NO ACTION ON DELETE NO ACTION,
    payment_method_name CHARACTER VARYING(255) NOT NULL
);

-- DROP TABLE audit.ccp_deposit_slip;

CREATE TABLE audit.ccp_deposit_slip (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),

    id INTEGER,
    "number" INTEGER NOT NULL,
    "date" TIMESTAMP WITH TIME ZONE,
    bank_account INTEGER,
    bank_account_iban CHARACTER VARYING(255),
    bank_account_name CHARACTER VARYING(255),
    payment_method INTEGER,
    payment_method_name CHARACTER VARYING(255)
);

CREATE TRIGGER ccp_deposit_slip_audit AFTER INSERT OR UPDATE OR DELETE ON ccp_deposit_slip FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

-- DROP TABLE ccp_invoice_deposit_slip;

CREATE TABLE ccp_invoice_deposit_slip (
    id serial PRIMARY KEY,
    ccp_invoice INTEGER REFERENCES ccp_invoice(id) ON UPDATE NO ACTION ON DELETE CASCADE,
    ccp_deposit_slip INTEGER REFERENCES ccp_deposit_slip(id) ON UPDATE NO ACTION ON DELETE CASCADE,
    unpaid_date TIMESTAMP WITH TIME ZONE,
    unpaid_note TEXT NOT NULL DEFAULT '',
    row_number INTEGER NOT NULL
);

-- DROP TABLE audit.ccp_invoice_deposit_slip;

CREATE TABLE audit.ccp_invoice_deposit_slip (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),

    id serial PRIMARY KEY,
    ccp_invoice INTEGER REFERENCES ccp_invoice(id) ON UPDATE NO ACTION ON DELETE CASCADE,
    ccp_deposit_slip INTEGER REFERENCES ccp_deposit_slip(id) ON UPDATE NO ACTION ON DELETE CASCADE,
    unpaid_date TIMESTAMP WITH TIME ZONE,
    unpaid_note TEXT NOT NULL DEFAULT '',
    row_number INTEGER NOT NULL
);

CREATE TRIGGER ccp_invoice_deposit_slip_audit AFTER INSERT OR UPDATE OR DELETE ON ccp_invoice_deposit_slip FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();
DROP TABLE audit.ccp_invoice_deposit_slip;

CREATE TABLE audit.ccp_invoice_deposit_slip (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),

    id INTEGER,
    ccp_invoice INTEGER,
    ccp_deposit_slip INTEGER ,
    unpaid_date TIMESTAMP WITH TIME ZONE,
    unpaid_note TEXT,
    row_number INTEGER
);

ALTER TABLE ccp_invoice ADD COLUMN ds_name CHARACTER VARYING(255);
ALTER TABLE audit.ccp_invoice ADD COLUMN ds_name CHARACTER VARYING(255);
ALTER TABLE ccp_invoice ADD COLUMN ds_id CHARACTER VARYING(255);
ALTER TABLE audit.ccp_invoice ADD COLUMN ds_id CHARACTER VARYING(255);


INSERT INTO parameter (name, value) VALUES ('DATA_SERVICE_URL', 'https://servizi-demo.bluenext.it');
INSERT INTO parameter (name, value) VALUES ('DATA_SERVICE_USERNAME', '');
INSERT INTO parameter (name, value) VALUES ('DATA_SERVICE_PASSWORD', '');


-- INVOICE TRANSMISSION
ALTER TABLE ccp_invoice_transmission ALTER COLUMN description TYPE TEXT;

INSERT INTO ccp_payment_method VALUES (9, 'Addebito automatico conto corrente');
ALTER TABLE public.archive_mail_account ADD COLUMN connection_class CHARACTER VARYING(255);


ALTER TABLE audit.archive_mail_account ADD COLUMN connection_class CHARACTER VARYING(255);

ALTER TABLE ccp_invoice ADD COLUMN credit_note BOOLEAN DEFAULT false;
ALTER TABLE audit.ccp_invoice ADD COLUMN credit_note BOOLEAN DEFAULT false;


ALTER TABLE core_bank_account add column online_payment_default BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.core_bank_account add column online_payment_default BOOLEAN NOT NULL DEFAULT false;

INSERT INTO ccp_payment_method VALUES (10, 'Carta di credito (online)');


ALTER TABLE core_bank_account ADD COLUMN cuc CHARACTER VARYING(255);
ALTER TABLE audit.core_bank_account ADD COLUMN cuc CHARACTER VARYING(255);


ALTER TABLE ccp_deposit_slip ADD COLUMN bank_account_cuc CHARACTER VARYING(255);
ALTER TABLE audit.ccp_deposit_slip ADD COLUMN bank_account_cuc CHARACTER VARYING(255);


-- DROP TABLE ccp_credits;

CREATE TABLE ccp_credits (
    id SERIAL PRIMARY KEY,
    description CHARACTER VARYING(255) NOT NULL,
    student_id INTEGER NOT NULL,
    amount  FLOAT
);

-- DROP TABLE audit.ccp_credits;

CREATE TABLE audit.ccp_credits (
    id SERIAL PRIMARY KEY,
    description CHARACTER VARYING(255) NOT NULL,
    student_id INTEGER NOT NULL,
    amount  FLOAT
);

INSERT INTO ccp_payment_method VALUES (11, 'Credito scolastico');
ALTER TABLE ccp_type ADD COLUMN invoice_code CHARACTER VARYING(255);
ALTER TABLE audit.ccp_type ADD COLUMN invoice_code CHARACTER VARYING(255);

ALTER TABLE ccp_movement ADD COLUMN invoice_code CHARACTER VARYING(255);
ALTER TABLE audit.ccp_movement ADD COLUMN invoice_code CHARACTER VARYING(255);



DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.expiration_date,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.positive_additionals_euro,
    mv.negative_additionals_euro,
    mv.positive_additionals_perc,
    mv.negative_additionals_perc,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total,
    mv.invoice_id,
    mv.invoice_code
   FROM ( SELECT m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.invoice_code,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            t.name AS type_text,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments,
            ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals,
            ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals,
            ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;
ALTER TABLE ccp_deposit_slip ADD COLUMN creditor_identifier CHARACTER VARYING(255);
ALTER TABLE audit.ccp_deposit_slip ADD COLUMN creditor_identifier CHARACTER VARYING(255);

ALTER TABLE core_bank_account ADD COLUMN creditor_identifier CHARACTER VARYING(255);
ALTER TABLE audit.core_bank_account ADD COLUMN creditor_identifier CHARACTER VARYING(255);

ALTER TABLE ccp_payment ADD column ccp_credit INTEGER;
ALTER TABLE audit.ccp_payment ADD column ccp_credit INTEGER;


ALTER TABLE ccp_credits ADD COLUMN dote BOOLEAN DEFAULT 'f';
ALTER TABLE audit.ccp_credits ADD COLUMN dote BOOLEAN;

UPDATE ccp_credits SET dote = 't';

INSERT INTO parameter (name, value) VALUES ('EASY_CODE', '');

ALTER TABLE ccp_type ADD COLUMN easy_code CHARACTER VARYING(255);
ALTER TABLE audit.ccp_type ADD COLUMN easy_code CHARACTER VARYING(255);

ALTER TABLE ccp_payment_method ADD COLUMN easy_code CHARACTER VARYING(255);
ALTER TABLE audit.ccp_payment_method ADD COLUMN easy_code CHARACTER VARYING(255);


UPDATE ccp_payment_method SET easy_code = 1 WHERE id = 2;
UPDATE ccp_payment_method SET easy_code = 2 WHERE id = 9;
UPDATE ccp_payment_method SET easy_code = 3 WHERE id = 5;
UPDATE ccp_payment_method SET easy_code = 4 WHERE id = 3;
UPDATE ccp_payment_method SET easy_code = 9 WHERE id = 1;

ALTER TABLE ccp_payment_method DROP COLUMN easy_code;
ALTER TABLE audit.ccp_payment_method DROP COLUMN easy_code;

ALTER TABLE ccp_payment_method ADD COLUMN easy_code INTEGER;
ALTER TABLE audit.ccp_payment_method ADD COLUMN easy_code INTEGER;

select setval('ccp_payment_method_id_seq', (select max(id) from ccp_payment_method));

INSERT INTO ccp_payment_method (name, easy_code) VALUES ('Rimessa diretta', 2);
INSERT INTO ccp_payment_method (name, easy_code) VALUES ('Cassa dote scuola', 3);
UPDATE ccp_payment_method SET easy_code = 1 WHERE id = 9;        ---        RID SEPA scadenza al:
UPDATE ccp_payment_method SET easy_code = 4 WHERE id = 5;        ---        BONIFICO
UPDATE ccp_payment_method SET easy_code = 5 WHERE id = 8;        ---        BOLLETTINO POSTALE
UPDATE ccp_payment_method SET easy_code = 6 WHERE id = 1;        ---        CASSA CONTANTI
UPDATE ccp_payment_method SET easy_code = 7 WHERE id = 2;        ---        CASSA ASSEGNI
UPDATE ccp_payment_method SET easy_code = 8 WHERE id = 3;        ---        POS
ALTER TABLE ccp_type ADD COLUMN da_ratei TIMESTAMP WITH TIME ZONE;
ALTER TABLE audit.ccp_type ADD COLUMN da_ratei TIMESTAMP WITH TIME ZONE;

ALTER TABLE ccp_type ADD COLUMN a_ratei TIMESTAMP WITH TIME ZONE;
ALTER TABLE audit.ccp_type ADD COLUMN a_ratei TIMESTAMP WITH TIME ZONE;

DROP TABLE audit.ccp_credits;

CREATE TABLE audit.ccp_credits (
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),

    id INTEGER,
    description CHARACTER VARYING(255) NOT NULL,
    student_id INTEGER NOT NULL,
    amount  FLOAT,
    dote BOOLEAN
);


CREATE TRIGGER ccp_credits_audit
    AFTER INSERT OR UPDATE OR DELETE ON ccp_credits
    FOR EACH ROW EXECUTE PROCEDURE audit.process_audit();

INSERT INTO parameter (name, value) VALUES ('INVOICE_INCLUDE_BOLLO', 'f');


DROP VIEW ccp_view_payment;
CREATE OR REPLACE VIEW ccp_view_payment AS
 SELECT pay.id, pay.expiration_date, pay.movement_id, pay.creation_date, pay.operation_date, pay.accountable_date, pay.amount, pay.payment_method_id, pay.bollettino, pay.account_reference, pay.ccp_credit, pay.payer_type, pay.payer_id, pay.payer_name, pay.payer_surname, pay.payer_fiscal_code, pay.payer_address, pay.payer_city, pay.payer_province, pay.payer_zip_code, pay.account_id, pay.receipt_id, pay.payer_data, pay.receipt_number, pay.receipt_date, pay.payment_method_text, pay.account_text, pay.movement_number, pay.subject_type, pay.subject_id, pay.subject_data, pay.subject_class, pay.subject_school_address_code, pay.subject_school_address,pay.subject_seat, pay.type_id, pay.section AS type_section, pay.type_text, pay.category_id, pay.category_text, pay.incoming, pay.positive_additionals_euro, pay.negative_additionals_euro, pay.positive_additionals_perc, pay.negative_additionals_perc, pay.count_additionals, pay.note, pay.miscellaneous, pay.school_year, pay.linked_additionals, (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id, m.expiration_date as expiration_date, p.movement_id, p.operation_date, p.accountable_date, p.amount, p.payment_method_id, p.bollettino, p.account_reference, p.ccp_credit, p.payer_type, p.payer_id, p.payer_name, p.payer_surname, p.payer_fiscal_code, p.payer_address, p.payer_city, p.payer_province, p.payer_zip_code, p.account_id, p.receipt_id, p.payer_data, p.receipt_number, p.receipt_date, w.name AS payment_method_text, a.denomination AS account_text, m.school_year, m.number AS movement_number, m.creation_date, m.subject_type, m.subject_id, m.subject_data, m.subject_class, m.subject_school_address_code, m.subject_school_address,m.subject_seat, m.type_id, t.section, m.note, m.miscellaneous, t.name AS type_text, t.category_id, c.name AS category_text, t.incoming, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro, (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc, ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc, ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals, ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id, cp.movement_id, cp.operation_date, cp.accountable_date, cp.amount::numeric(14,2) AS amount, cp.payment_method_id, cp.bollettino, cp.account_reference, cp.ccp_credit, cp.payer_type, cp.payer_id, cp.payer_name, cp.payer_surname, cp.payer_fiscal_code, cp.payer_address, cp.payer_city, cp.payer_province, cp.payer_zip_code, cp.account_id, cp.receipt_id, (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data, cr.number AS receipt_number, cr.date AS receipt_date
                   FROM ccp_payment cp
              LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p, ccp_payment_method w, core_bank_account a, ccp_movement m, ccp_type t, ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;



DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.invoice_number,
    mv.expiration_date,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.positive_additionals_euro,
    mv.negative_additionals_euro,
    mv.positive_additionals_perc,
    mv.negative_additionals_perc,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision + (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.positive_additionals_perc - (mv.amount + mv.positive_additionals_euro::double precision - mv.negative_additionals_euro::double precision) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2) AS total,
    mv.invoice_id,
    mv.invoice_code
   FROM ( SELECT m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.invoice_code,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            (SELECT number from ccp_invoice where id = m.invoice_id LIMIT 1) AS invoice_number,
            t.name AS type_text,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (( SELECT COALESCE(sum(ccp_payment.amount), 0::double precision) AS sum
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id))::numeric(14,2) AS total_payments,
            ( SELECT count(ccp_payment.id) AS count
                   FROM ccp_payment
                  WHERE ccp_payment.movement_id = m.id) AS count_payments,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id AND (ccp_movement_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_movement_additional.movement_id) AS count
                   FROM ccp_movement_additional
                  WHERE ccp_movement_additional.movement_id = m.id) AS count_additionals,
            ( SELECT string_agg(cma.additional_id::text, ','::text) AS string_agg
                   FROM ccp_movement_additional cma
                  WHERE cma.movement_id = m.id) AS linked_additionals,
            ( SELECT string_agg(cp.id::text, ','::text) AS string_agg
                   FROM ccp_payment cp
                  WHERE cp.movement_id = m.id) AS linked_payments
           FROM ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE m.type_id = t.id AND t.category_id = c.id) mv;


INSERT INTO parameter (name, value) VALUES ('PRINT_CLASS_RECEIPT', 'Receipt');


INSERT INTO parameter (name, value) VALUES ('SETTINGS_PRINTS_SIGN_DIRECTOR', '');


ALTER TABLE ccp_type ADD COLUMN include_vat BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE ccp_type ADD COLUMN vat FLOAT;

ALTER TABLE audit.ccp_type ADD COLUMN include_vat BOOLEAN;
ALTER TABLE audit.ccp_type ADD COLUMN vat FLOAT;



-- DROP TABLE ccp_reminder;

CREATE TABLE ccp_reminder (
    id              SERIAL PRIMARY KEY,
    creation        TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    reminder_type   CHARACTER VARYING(255) NOT NULL,
    sent            TIMESTAMP WITH TIME ZONE,
    mail            CHARACTER VARYING(255) NOT NULL,
    message         TEXT NOT NULL,
    tried           BOOLEAN NOT NULL DEFAULT 'f',
    confirmed       TIMESTAMP WITH TIME ZONE
);

INSERT INTO parameter (name, value) VALUES ('REMINDER_MAIL_START',      '00:00');
INSERT INTO parameter (name, value) VALUES ('REMINDER_MAIL_END',        '07:00');
INSERT INTO parameter (name, value) VALUES ('REMINDER_MESSAGE_INFO',    'Questo è un pro memoria <br /> {{ movements }}');
INSERT INTO parameter (name, value) VALUES ('REMINDER_MESSAGE_WARNING', 'Questo è un sollecito <br /> {{ movements }}');

ALTER TABLE archive_mail_account ADD COLUMN reminder BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.archive_mail_account ADD COLUMN reminder BOOLEAN NOT NULL DEFAULT false;


 DROP VIEW ccp_view_payment;
ALTER TABLE ccp_payment ALTER COLUMN payer_city TYPE character varying(255);
 CREATE VIEW ccp_view_payment AS
 SELECT pay.id,
    pay.expiration_date,
    pay.movement_id,
    pay.creation_date,
    pay.operation_date,
    pay.accountable_date,
    pay.amount,
    pay.payment_method_id,
    pay.bollettino,
    pay.account_reference,
    pay.ccp_credit,
    pay.payer_type,
    pay.payer_id,
    pay.payer_name,
    pay.payer_surname,
    pay.payer_fiscal_code,
    pay.payer_address,
    pay.payer_city,
    pay.payer_province,
    pay.payer_zip_code,
    pay.account_id,
    pay.receipt_id,
    pay.payer_data,
    pay.receipt_number,
    pay.receipt_date,
    pay.payment_method_text,
    pay.account_text,
    pay.movement_number,
    pay.subject_type,
    pay.subject_id,
    pay.subject_data,
    pay.subject_class,
    pay.subject_school_address_code,
    pay.subject_school_address,
    pay.subject_seat,
    pay.type_id,
    pay.section AS type_section,
    pay.type_text,
    pay.category_id,
    pay.category_text,
    pay.incoming,
    pay.positive_additionals_euro,
    pay.negative_additionals_euro,
    pay.positive_additionals_perc,
    pay.negative_additionals_perc,
    pay.count_additionals,
    pay.note,
    pay.miscellaneous,
    pay.school_year,
    pay.linked_additionals,
    (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id,
            m.expiration_date,
            p.movement_id,
            p.operation_date,
            p.accountable_date,
            p.amount,
            p.payment_method_id,
            p.bollettino,
            p.account_reference,
            p.ccp_credit,
            p.payer_type,
            p.payer_id,
            p.payer_name,
            p.payer_surname,
            p.payer_fiscal_code,
            p.payer_address,
            p.payer_city,
            p.payer_province,
            p.payer_zip_code,
            p.account_id,
            p.receipt_id,
            p.payer_data,
            p.receipt_number,
            p.receipt_date,
            w.name AS payment_method_text,
            a.denomination AS account_text,
            m.school_year,
            m.number AS movement_number,
            m.creation_date,
            m.subject_type,
            m.subject_id,
            m.subject_data,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.subject_seat,
            m.type_id,
            t.section,
            m.note,
            m.miscellaneous,
            t.name AS type_text,
            t.category_id,
            c.name AS category_text,
            t.incoming,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals,
            ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id,
                    cp.movement_id,
                    cp.operation_date,
                    cp.accountable_date,
                    cp.amount::numeric(14,2) AS amount,
                    cp.payment_method_id,
                    cp.bollettino,
                    cp.account_reference,
                    cp.ccp_credit,
                    cp.payer_type,
                    cp.payer_id,
                    cp.payer_name,
                    cp.payer_surname,
                    cp.payer_fiscal_code,
                    cp.payer_address,
                    cp.payer_city,
                    cp.payer_province,
                    cp.payer_zip_code,
                    cp.account_id,
                    cp.receipt_id,
                    (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data,
                    cr.number AS receipt_number,
                    cr.date AS receipt_date
                   FROM ccp_payment cp
                     LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p,
            ccp_payment_method w,
            core_bank_account a,
            ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;


ALTER TABLE audit.ccp_payment ALTER COLUMN payer_city TYPE character varying(255);

INSERT INTO parameter (name, value) VALUES ('COLLECTION_COST', '0');


ALTER TABLE ccp_invoice ADD COLUMN payment_method INTEGER;
ALTER TABLE audit.ccp_invoice ADD COLUMN payment_method INTEGER;


INSERT INTO parameter (name, value) VALUES ('INVOICE_PAYMENT_DETAILS', 'f');

ALTER TABLE ccp_payment ADD COLUMN ccp_deposit_slip INTEGER;
ALTER TABLE audit.ccp_payment ADD COLUMN ccp_deposit_slip INTEGER;

ALTER TABLE ccp_movement ADD COLUMN easy_select BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE audit.ccp_movement ADD COLUMN easy_select BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE ccp_invoice_deposit_slip ADD COLUMN total FLOAT NOT NULL DEFAULT 0;
ALTER TABLE audit.ccp_invoice_deposit_slip ADD COLUMN total FLOAT NOT NULL DEFAULT 0;


-- CREATE AUDIT.CCP_TYPE_STEP
CREATE TABLE audit.ccp_type_step
(
    op_action character varying(1) NOT NULL,
    op_date timestamp with time zone NOT NULL DEFAULT now(),
    id integer NOT NULL,
    ccp_type integer NOT NULL,
    expiration character varying(255),
    value double precision
);


-- CREATE TRIGGER
CREATE TRIGGER ccp_type_step_audit
    AFTER INSERT OR DELETE OR UPDATE
    ON public.ccp_type_step
    FOR EACH ROW
    EXECUTE PROCEDURE audit.process_audit();



-- CCP_TYPE_STEP DESCRIPTION
ALTER TABLE public.ccp_type_step
    ADD COLUMN description character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT '';

ALTER TABLE audit.ccp_type_step
    ADD COLUMN description character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT '';


-- CCP_TYPE_STEP DA_RATEI
ALTER TABLE public.ccp_type_step
    ADD COLUMN da_ratei timestamp with time zone;

ALTER TABLE audit.ccp_type_step
    ADD COLUMN da_ratei timestamp with time zone;


-- CCP_TYPE_STEP A_RATEI
ALTER TABLE public.ccp_type_step
    ADD COLUMN a_ratei timestamp with time zone;

ALTER TABLE audit.ccp_type_step
    ADD COLUMN a_ratei timestamp with time zone;



-- CCP_MOVEMENT DESCRIPTION
ALTER TABLE public.ccp_movement
    ADD COLUMN description character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT '';

ALTER TABLE audit.ccp_movement
    ADD COLUMN description character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT '';


-- CCP_MOVEMENT DA_RATEI
ALTER TABLE public.ccp_movement
    ADD COLUMN da_ratei timestamp with time zone;

ALTER TABLE audit.ccp_movement
    ADD COLUMN da_ratei timestamp with time zone;


-- CCP_MOVEMENT A_RATEI
ALTER TABLE public.ccp_movement
    ADD COLUMN a_ratei timestamp with time zone;

ALTER TABLE audit.ccp_movement
    ADD COLUMN a_ratei timestamp with time zone;


-- INSERT type_step non esistenti
INSERT INTO ccp_type_step (ccp_type)
SELECT
	b.id
FROM
	ccp_type b
WHERE NOT EXISTS (
	SELECT 1 FROM ccp_type_step WHERE ccp_type = b.id
);

-- UPDATE type_step
UPDATE ccp_type_step
SET description = ccp_type.name,
da_ratei = ccp_type.da_ratei,
a_ratei = ccp_type.a_ratei
FROM ccp_type
WHERE ccp_type.id = ccp_type_step.ccp_type;


-- UPDATE every movement
UPDATE ccp_movement
SET description = ccp_type.name,
da_ratei = ccp_type.da_ratei,
a_ratei = ccp_type.a_ratei
FROM ccp_type
WHERE ccp_type.id = ccp_movement.type_id;



-- ALTER ccp_view_movement
DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT
    mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.invoice_number,
    mv.expiration_date,
    mv.da_ratei,
    mv.a_ratei,
    mv.description,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.positive_additionals_euro,
    mv.negative_additionals_euro,
    mv.positive_additionals_perc,
    mv.negative_additionals_perc,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    (
        mv.amount +
        mv.positive_additionals_euro::double precision -
        mv.negative_additionals_euro::double precision +
        (
            mv.amount +
            mv.positive_additionals_euro::double precision -
            mv.negative_additionals_euro::double precision
        ) / 100::double precision * mv.positive_additionals_perc -
        (
            mv.amount +
            mv.positive_additionals_euro::double precision -
            mv.negative_additionals_euro::double precision
        ) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
        AS total,
    mv.invoice_id,
    mv.invoice_code
   FROM (
       SELECT
            m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.invoice_code,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            m.da_ratei,
            m.a_ratei,
            (
                SELECT number from ccp_invoice where id = m.invoice_id LIMIT 1
            ) AS invoice_number,
            t.name AS type_text,
            /*
            (
                CASE
                    WHEN
                        (
                            m.description <> ''
                        )  IS NOT TRUE
                    THEN
                        t.name
                    ELSE
                        m.description
                END
            ) AS type_text,
            */
            m.description AS description,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (
                (
                    SELECT
                        COALESCE(
                            sum(ccp_payment.amount), 0::double precision
                        ) AS sum
                    FROM
                        ccp_payment
                    WHERE
                        ccp_payment.movement_id = m.id
                )
            )::numeric(14,2) AS total_payments,
            (
                SELECT
                    count(ccp_payment.id) AS count
                FROM
                    ccp_payment
                WHERE
                    ccp_payment.movement_id = m.id
            ) AS count_payments,
            (
                (
                    SELECT
                        COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                    FROM
                        ccp_movement_additional
                    WHERE
                        ccp_movement_additional.movement_id = m.id
                    AND (
                        ccp_movement_additional.additional_id IN (
                            SELECT
                                ccp_additional.id
                            FROM
                                ccp_additional
                            WHERE
                                ccp_additional.percentual = false AND
                                ccp_additional.positive = true
                        )
                    )
                )
            )::numeric(14,2) AS positive_additionals_euro,
            (
                (
                    SELECT
                        COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                    FROM
                        ccp_movement_additional
                    WHERE
                        ccp_movement_additional.movement_id = m.id AND
                        (
                            ccp_movement_additional.additional_id IN (
                                SELECT
                                    ccp_additional.id
                                FROM
                                    ccp_additional
                                WHERE
                                    ccp_additional.percentual = false AND
                                    ccp_additional.positive = false
                            )
                        )
                )
            )::numeric(14,2) AS negative_additionals_euro,
            (
                SELECT
                    COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id AND
                    (
                        ccp_movement_additional.additional_id IN (
                            SELECT
                                ccp_additional.id
                            FROM
                                ccp_additional
                            WHERE
                                ccp_additional.percentual = true AND
                                ccp_additional.positive = true
                        )
                    )
            ) AS positive_additionals_perc,
            (
                SELECT
                    COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id AND
                    (
                        ccp_movement_additional.additional_id IN (
                            SELECT
                                ccp_additional.id
                            FROM
                                ccp_additional
                            WHERE
                                ccp_additional.percentual = true AND
                                ccp_additional.positive = false
                        )
                    )
            ) AS negative_additionals_perc,
            (
                SELECT
                    count(ccp_movement_additional.movement_id) AS count
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id
            ) AS count_additionals,
            (
                SELECT
                    string_agg(cma.additional_id::text, ','::text) AS string_agg
                FROM
                    ccp_movement_additional cma
                WHERE
                    cma.movement_id = m.id
            ) AS linked_additionals,
            (
                SELECT
                    string_agg(cp.id::text, ','::text) AS string_agg
                FROM
                    ccp_payment cp
                WHERE
                    cp.movement_id = m.id
            ) AS linked_payments

       FROM
            ccp_movement m,
            ccp_type t,
            ccp_category c
       WHERE
            m.type_id = t.id AND
            t.category_id = c.id
  ) mv;


-- View: public.ccp_view_payment

DROP VIEW public.ccp_view_payment;

CREATE OR REPLACE VIEW public.ccp_view_payment AS
 SELECT pay.id,
    pay.expiration_date,
    pay.movement_id,
    pay.creation_date,
    pay.operation_date,
    pay.accountable_date,
    pay.amount,
    pay.payment_method_id,
    pay.bollettino,
    pay.account_reference,
    pay.ccp_credit,
    pay.payer_type,
    pay.payer_id,
    pay.payer_name,
    pay.payer_surname,
    pay.payer_fiscal_code,
    pay.payer_address,
    pay.payer_city,
    pay.payer_province,
    pay.payer_zip_code,
    pay.account_id,
    pay.receipt_id,
    pay.payer_data,
    pay.receipt_number,
    pay.receipt_date,
    pay.payment_method_text,
    pay.account_text,
    pay.movement_number,
    pay.subject_type,
    pay.subject_id,
    pay.subject_data,
    pay.subject_class,
    pay.subject_school_address_code,
    pay.subject_school_address,
    pay.subject_seat,
    pay.type_id,
    pay.section AS type_section,
    pay.type_text,
    pay.description,
    pay.category_id,
    pay.category_text,
    pay.incoming,
    pay.positive_additionals_euro,
    pay.negative_additionals_euro,
    pay.positive_additionals_perc,
    pay.negative_additionals_perc,
    pay.count_additionals,
    pay.note,
    pay.miscellaneous,
    pay.school_year,
    pay.linked_additionals,
    (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id,
            m.expiration_date,
            p.movement_id,
            p.operation_date,
            p.accountable_date,
            p.amount,
            p.payment_method_id,
            p.bollettino,
            p.account_reference,
            p.ccp_credit,
            p.payer_type,
            p.payer_id,
            p.payer_name,
            p.payer_surname,
            p.payer_fiscal_code,
            p.payer_address,
            p.payer_city,
            p.payer_province,
            p.payer_zip_code,
            p.account_id,
            p.receipt_id,
            p.payer_data,
            p.receipt_number,
            p.receipt_date,
            w.name AS payment_method_text,
            a.denomination AS account_text,
            m.school_year,
            m.number AS movement_number,
            m.creation_date,
            m.subject_type,
            m.subject_id,
            m.subject_data,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.subject_seat,
            m.description,
            m.type_id,
            t.section,
            m.note,
            m.miscellaneous,
            t.name AS type_text,
            t.category_id,
            c.name AS category_text,
            t.incoming,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals,
            ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id,
                    cp.movement_id,
                    cp.operation_date,
                    cp.accountable_date,
                    cp.amount::numeric(14,2) AS amount,
                    cp.payment_method_id,
                    cp.bollettino,
                    cp.account_reference,
                    cp.ccp_credit,
                    cp.payer_type,
                    cp.payer_id,
                    cp.payer_name,
                    cp.payer_surname,
                    cp.payer_fiscal_code,
                    cp.payer_address,
                    cp.payer_city,
                    cp.payer_province,
                    cp.payer_zip_code,
                    cp.account_id,
                    cp.receipt_id,
                    (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data,
                    cr.number AS receipt_number,
                    cr.date AS receipt_date
                   FROM ccp_payment cp
                     LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p,
            ccp_payment_method w,
            core_bank_account a,
            ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

ALTER TABLE core_bank_account ADD COLUMN pvr_number character varying(20) NOT NULL DEFAULT '';
ALTER TABLE audit.core_bank_account ADD COLUMN pvr_number character varying(20) NOT NULL DEFAULT '';

INSERT INTO parameter (name, value) VALUES ('PVR_CLIENT_NUMBER', '');