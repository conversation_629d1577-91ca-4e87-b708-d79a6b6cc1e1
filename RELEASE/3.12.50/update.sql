INSERT INTO parameter (name,value) VALUES ('MATTHAEUS_AREA', '');
INSERT INTO parameter (name,value) VALUES ('ENABLE_SYNC_MATTHAEUS', 'f');

ALTER TABLE ccp_movement ADD COLUMN matthaeus_id INTEGER;
ALTER TABLE audit.ccp_movement ADD COLUMN matthaeus_id INTEGER;

ALTER TABLE ccp_movement ADD COLUMN matthaeus_year INTEGER;
ALTER TABLE audit.ccp_movement ADD COLUMN matthaeus_year INTEGER;

ALTER TABLE ccp_payment ADD COLUMN matthaeus_id INTEGER;
ALTER TABLE audit.ccp_payment ADD COLUMN matthaeus_id INTEGER;

ALTER TABLE ccp_payment_method ADD COLUMN piano_conti CHARACTER VARYING(255);
ALTER TABLE audit.ccp_payment_method ADD COLUMN piano_conti CHARACTER VARYING(255);
