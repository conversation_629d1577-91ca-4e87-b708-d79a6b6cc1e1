UPDATE archive_mail_account SET active=false where out=true;

INSERT INTO archive_mail_account (name,protocol,host,port,security,username,password,active,out) VALUES ('Mailer', 1, '-',1,1,'-','-',true,true);

ALTER TABLE archive_mail_document ADD COLUMN sent TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE audit.archive_mail_document ADD COLUMN sent TIMESTAMP WITHOUT TIME ZONE;

UPDATE archive_mail_document set sent = (SELECT date from archive_mail WHERE archive_mail.id=archive_mail_document.mail limit 1);

ALTER TABLE protocol_protocol ADD COLUMN mail_sending TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE protocol_protocol ADD COLUMN mail_error TEXT;
ALTER TABLE audit.protocol_protocol ADD COLUMN mail_sending TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE audit.protocol_protocol ADD COLUMN mail_error TEXT;

CREATE OR REPLACE VIEW "public"."protocol_view_protocol" AS  SELECT p.id,
    p.date,
    p.note,
    p.budget_id,
    p.type_id,
    p.rec_id,
    p.rec_type,
    p.protocol_number,
    p.obj_id,
    p.direction,
    p.description,
    p.correspondents_text,
    p.canceled,
    p.external_act_number,
    p.send_method_id,
    p.dossier,
    p.subject_kind_id,
    p.reserved,
    sm.title AS send_method_text,
    sk.title AS subject_kind_text,
    ( SELECT btrim((((((c.c3)::text || ' '::text) || (c.c2)::text) || ' '::text) || (c.c1)::text), ' '::text) AS btrim
           FROM ( SELECT
                        CASE
                            WHEN (p3.code IS NULL) THEN ''::character varying
                            ELSE p3.code
                        END AS c3,
                        CASE
                            WHEN (p2.code IS NULL) THEN ''::character varying
                            ELSE p2.code
                        END AS c2,
                        CASE
                            WHEN (p1.code IS NULL) THEN ''::character varying
                            ELSE p1.code
                        END AS c1
                   FROM ((protocol_type p1
                     LEFT JOIN protocol_type p2 ON ((p1.parent_type_id = p2.id)))
                     LEFT JOIN protocol_type p3 ON ((p2.parent_type_id = p3.id)))
                  WHERE (p1.id = p.type_id)) c) AS type_text,
    ( SELECT count(pd.document_id) AS count
           FROM protocol_protocol_document pd
          WHERE (pd.protocol_id = p.id)) AS count_documents,
    ( SELECT string_agg((pd.document_id)::text, ','::text) AS string_agg
           FROM protocol_protocol_document pd
          WHERE (pd.protocol_id = p.id)) AS linked_documents,
    ( SELECT count(pc.correspondent_id) AS count
           FROM protocol_protocol_correspondent pc
          WHERE (pc.protocol_id = p.id)) AS count_correspondents,
    ( SELECT string_agg((pc.correspondent_id)::text, ','::text) AS string_agg
           FROM protocol_protocol_correspondent pc
          WHERE (pc.protocol_id = p.id)) AS linked_correspondents,
    ( SELECT count(pp.protocol_1_id) AS count
           FROM protocol_protocol_protocol pp
          WHERE ((pp.protocol_1_id = p.id) OR (pp.protocol_2_id = p.id))) AS count_protocols,
    ( SELECT string_agg((protocols.id)::text, ','::text) AS string_agg
           FROM ( SELECT pp.protocol_1_id AS id
                   FROM protocol_protocol_protocol pp
                  WHERE (pp.protocol_2_id = p.id)
                UNION
                 SELECT pp.protocol_2_id AS id
                   FROM protocol_protocol_protocol pp
                  WHERE (pp.protocol_1_id = p.id)) protocols) AS linked_protocols,
   p.header_position,
   p.mail_sending
   FROM (((protocol_protocol p
     LEFT JOIN protocol_type pt ON ((p.type_id = pt.id)))
     LEFT JOIN protocol_send_method sm ON ((p.send_method_id = sm.id)))
     LEFT JOIN protocol_subject_kind sk ON ((p.subject_kind_id = sk.id)));;
