-- Drop useless old tables _del

DROP TABLE IF EXISTS abi_del;
DROP TABLE IF EXISTS asl_del;
DROP TABLE IF EXISTS absence_kind_del;
DROP TABLE IF EXISTS absence_kind_steps_del;
DROP TABLE IF EXISTS absences_del;
DROP TABLE IF EXISTS activity_type_del;
DROP TABLE IF EXISTS app_type_del;
DROP TABLE IF EXISTS appointment_del;
DROP TABLE IF EXISTS ata_project_htypes_projects_del;
DROP TABLE IF EXISTS ateco_code_del;
DROP TABLE IF EXISTS authority_type_del;
DROP TABLE IF EXISTS bank_acc_del;
DROP TABLE IF EXISTS ata_project_hour_types_del;
DROP TABLE IF EXISTS bank_account_del;
DROP TABLE IF EXISTS bdg_act_sum_del;
DROP TABLE IF EXISTS bdg_activities_del;
DROP TABLE IF EXISTS bdg_asses_del;
DROP TABLE IF EXISTS bdg_asses_log_del;
DROP TABLE IF EXISTS bdg_budg_act_del;
DROP TABLE IF EXISTS bdg_budg_voice_del;
DROP TABLE IF EXISTS bdg_budget_del;
DROP TABLE IF EXISTS bdg_charge_emp_del;
DROP TABLE IF EXISTS bdg_engage_header_del;
DROP TABLE IF EXISTS bdg_engage_items_del;
DROP TABLE IF EXISTS bdg_engage_log_del;
DROP TABLE IF EXISTS bdg_initial_del;
DROP TABLE IF EXISTS bdg_modal_del;
DROP TABLE IF EXISTS bdg_orders_del;
DROP TABLE IF EXISTS bdg_registration_del;
DROP TABLE IF EXISTS bdg_res_fund_del;
DROP TABLE IF EXISTS bdg_rever_del;
DROP TABLE IF EXISTS bdg_revpay_del;
DROP TABLE IF EXISTS bdg_rp_rel_del;
DROP TABLE IF EXISTS bdg_var_del;
DROP TABLE IF EXISTS bdg_var_voice_del;
DROP TABLE IF EXISTS bdg_voice_sup_del;
DROP TABLE IF EXISTS bdg_voice_sup_type_del;
DROP TABLE IF EXISTS bdg_voice_var_a_del;
DROP TABLE IF EXISTS bdg_voices_del;
DROP TABLE IF EXISTS cab_del;
DROP TABLE IF EXISTS calender_holidays_del;
DROP TABLE IF EXISTS calender_weekends_del;
DROP TABLE IF EXISTS cedolino_data_del;
DROP TABLE IF EXISTS charges_data_del;
DROP TABLE IF EXISTS charges_kind_del;
DROP TABLE IF EXISTS cities_del;
DROP TABLE IF EXISTS codici_casse_del;
DROP TABLE IF EXISTS codici_cessazione_del;
DROP TABLE IF EXISTS codici_tipo_impiego_del;
DROP TABLE IF EXISTS codici_tipo_servizio_del;
DROP TABLE IF EXISTS contact_del;
DROP TABLE IF EXISTS contracts_del;
DROP TABLE IF EXISTS countries_del;
DROP TABLE IF EXISTS cud_data_del;
DROP TABLE IF EXISTS diaria_code_kind_del;
DROP TABLE IF EXISTS dma_data_del;
DROP TABLE IF EXISTS employee_del;
DROP TABLE IF EXISTS employee_fiscal_details_del;
DROP TABLE IF EXISTS extensions_del;
DROP TABLE IF EXISTS external_missions_del;
DROP TABLE IF EXISTS extraordinary_stored_del;
DROP TABLE IF EXISTS finance_sit_del;
DROP TABLE IF EXISTS fiscal_data_del;
DROP TABLE IF EXISTS fiscal_documents_del;
DROP TABLE IF EXISTS fml_members_del;
DROP TABLE IF EXISTS fml_relation_del;
DROP TABLE IF EXISTS groups_del;
DROP TABLE IF EXISTS grpermissions_del;
DROP TABLE IF EXISTS help_topics_del;
DROP TABLE IF EXISTS institute_del;
DROP TABLE IF EXISTS inv_initial_del;
DROP TABLE IF EXISTS langs_del;
DROP TABLE IF EXISTS liquid_office_del;
DROP TABLE IF EXISTS locks_del;
DROP TABLE IF EXISTS logged_widgets_del;
DROP TABLE IF EXISTS logged_widgets_group_del;
DROP TABLE IF EXISTS logged_widgets_user_del;
DROP TABLE IF EXISTS long_term_del;
DROP TABLE IF EXISTS mastercom_teacher_link_del;
DROP TABLE IF EXISTS mod770_s_del;
DROP TABLE IF EXISTS parameter_del;
DROP TABLE IF EXISTS payment_group_del;
DROP TABLE IF EXISTS permission_vacation_timeline_del;
DROP TABLE IF EXISTS permissions_del;
DROP TABLE IF EXISTS personnel_presences_del;
DROP TABLE IF EXISTS personnel_projects_del;
DROP TABLE IF EXISTS personnel_timetable_del;
DROP TABLE IF EXISTS protocol_del;
DROP TABLE IF EXISTS revocation_del;
DROP TABLE IF EXISTS rtr_charges_del;
DROP TABLE IF EXISTS rtr_income_taxes_del;
DROP TABLE IF EXISTS rtr_sal_pcheck_del;
DROP TABLE IF EXISTS sedi_del;
DROP TABLE IF EXISTS service_state_del;
DROP TABLE IF EXISTS service_termination_del;
DROP TABLE IF EXISTS service_type_del;
DROP TABLE IF EXISTS settings_del;
DROP TABLE IF EXISTS short_term_del;
DROP TABLE IF EXISTS storage_personnel_presences_del;
DROP TABLE IF EXISTS t_stat_del;
DROP TABLE IF EXISTS tax_residuals_del;
DROP TABLE IF EXISTS tfr_coef_del;
DROP TABLE IF EXISTS tfr_data_del;
DROP TABLE IF EXISTS timetable_models_del;
DROP TABLE IF EXISTS tipi_di_tasse_voices_del;
DROP TABLE IF EXISTS users_del;
DROP TABLE IF EXISTS users_settings_del;
DROP TABLE IF EXISTS wh_ammortization_del;
DROP TABLE IF EXISTS wh_item_del;
DROP TABLE IF EXISTS wh_item_kind_del;
DROP TABLE IF EXISTS wh_item_vault_del;
DROP TABLE IF EXISTS wh_order_del;
DROP TABLE IF EXISTS wh_order_header_del;
DROP TABLE IF EXISTS wh_responsible_del;
DROP TABLE IF EXISTS wh_smpitems_history_del;
DROP TABLE IF EXISTS wh_suppliers_del;
DROP TABLE IF EXISTS widget_exceptions_del;
DROP TABLE IF EXISTS widgets_del;
DROP TABLE IF EXISTS zip_codes_del;
DROP TABLE IF EXISTS zone_del;
DROP TABLE IF EXISTS zone_templates_del;



-- DATABASE audit fix

ALTER TABLE audit.archive_document_file ALTER COLUMN id DROP DEFAULT;
ALTER TABLE audit.archive_mail_document ALTER COLUMN id DROP DEFAULT;
ALTER TABLE audit.invoice ALTER COLUMN id DROP DEFAULT;
ALTER TABLE audit.archive_class ALTER COLUMN action TYPE CHARACTER VARYING(1);
ALTER TABLE audit.bdg_activities ALTER COLUMN aggreg_code TYPE CHARACTER VARYING(1);
ALTER TABLE audit.ccp_deposits ALTER COLUMN deposit_type TYPE CHARACTER VARYING(1);
ALTER TABLE audit.ccp_movement ALTER COLUMN school_year TYPE CHARACTER VARYING(255);
ALTER TABLE audit.ccp_movement ALTER COLUMN subject_school_year TYPE CHARACTER VARYING(255);
ALTER TABLE audit.employee ALTER COLUMN adm_code TYPE CHARACTER VARYING(16);
ALTER TABLE audit.employee ALTER COLUMN insur_qual TYPE CHARACTER VARYING(1);
ALTER TABLE audit.employee ALTER COLUMN way_pay TYPE CHARACTER VARYING(2);
ALTER TABLE audit.personnel_presences ALTER COLUMN description TYPE CHARACTER VARYING(255);


-- DATABASE public fix

DELETE FROM contest_type;
ALTER TABLE contest_type ADD PRIMARY KEY(contest_type_id);
ALTER TABLE employee ALTER COLUMN dom_first_curr_year DROP NOT NULL;
ALTER TABLE employee ALTER COLUMN dom_first_curr_year DROP DEFAULT;
DELETE FROM gross;
ALTER TABLE gross ADD PRIMARY KEY (id);
DELETE FROM iis;
ALTER TABLE iis ADD PRIMARY KEY (id);
DELETE FROM magister_sync;
ALTER TABLE magister_sync ADD PRIMARY KEY (id);
ALTER TABLE permissions ALTER COLUMN permission_id DROP DEFAULT;
DELETE FROM prof_prof;
ALTER TABLE prof_prof ADD PRIMARY KEY (id);
DELETE FROM user_log;
ALTER TABLE user_log ADD PRIMARY KEY (id);
ALTER TABLE ata_project_hour_types ALTER COLUMN description TYPE CHARACTER VARYING(255);
DELETE FROM way_of_payment;
ALTER TABLE way_of_payment ADD PRIMARY KEY (id);
DELETE FROM from_payment;
ALTER TABLE from_payment ADD PRIMARY KEY (id);
ALTER TABLE archive_class ALTER COLUMN action TYPE CHARACTER VARYING(1);
ALTER TABLE archive_metadata ALTER COLUMN kind TYPE CHARACTER VARYING(1);
ALTER TABLE asl ALTER COLUMN code TYPE CHARACTER VARYING(1);
ALTER TABLE bank_acc ALTER COLUMN cin TYPE CHARACTER VARYING(1);
ALTER TABLE bdg_base_activities ALTER COLUMN aggreg_code TYPE CHARACTER VARYING(1);
ALTER TABLE ccp_deposits ALTER COLUMN deposit_type TYPE CHARACTER VARYING(1);
ALTER TABLE employee ALTER COLUMN adm_code TYPE CHARACTER VARYING(16);
ALTER TABLE employee ALTER COLUMN insur_qual TYPE CHARACTER VARYING(1);
ALTER TABLE employee ALTER COLUMN way_pay TYPE CHARACTER VARYING(2);
ALTER TABLE fiscal_documents ALTER COLUMN document TYPE CHARACTER VARYING(255);
ALTER TABLE fiscal_documents ALTER COLUMN filter TYPE CHARACTER VARYING(255);
ALTER TABLE groups ALTER COLUMN group_name TYPE CHARACTER VARYING(255);
ALTER TABLE liq_group ALTER COLUMN code TYPE CHARACTER VARYING(4);
ALTER TABLE logged_widgets ALTER COLUMN template_name TYPE CHARACTER VARYING(255);
ALTER TABLE logged_widgets ALTER COLUMN widget_name TYPE CHARACTER VARYING(255);
ALTER TABLE personnel_presences ALTER COLUMN description TYPE CHARACTER VARYING(255);
ALTER TABLE print_templates ALTER COLUMN code TYPE CHARACTER VARYING(255);
ALTER TABLE service_state ALTER COLUMN professional_profile TYPE CHARACTER VARYING(255);
ALTER TABLE social_position ALTER COLUMN description TYPE CHARACTER VARYING(255);
ALTER TABLE way_of_payment ALTER COLUMN id TYPE CHARACTER VARYING(2);
ALTER TABLE zone ALTER COLUMN zone_description TYPE CHARACTER VARYING(255);

DROP view view_activity;
ALTER TABLE bdg_activities ALTER COLUMN aggreg_code TYPE CHARACTER VARYING(1);
CREATE VIEW view_activity AS
    SELECT bdg_activities.activ_id, ((((bdg_activities.aggreg_code)::text || (bdg_activities.aggreg_nr)::text) || ' - '::text) || (bdg_activities.description)::text) AS descr FROM bdg_activities WHERE ((bdg_activities.budget_year)::text = ((SELECT settings.budget_year FROM settings))::text) ORDER BY bdg_activities.aggreg_code, bdg_activities.aggreg_nr;


DROP VIEW ccp_view_movement;
DROP VIEW ccp_view_payment;
ALTER TABLE ccp_movement ALTER COLUMN school_year TYPE CHARACTER VARYING(255);
ALTER TABLE ccp_movement ALTER COLUMN subject_school_year TYPE CHARACTER VARYING(255);
-- 3.14.73
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT
    mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.invoice_number,
    mv.expiration_date,
    mv.da_ratei,
    mv.a_ratei,
    mv.description,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments::numeric(14,2),
    mv.count_payments,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    mv.last_payment,
    mv.total_additionals::numeric(14,2),
    (
        mv.amount +
        mv.total_additionals
    )::numeric(14,2)  AS total,
    mv.invoice_id,
    mv.invoice_code,
    mv.locked,
    mv.date_published
   FROM (
       SELECT
            m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.invoice_code,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            m.da_ratei,
            m.a_ratei,
            m.locked,
            m.date_published,
            (
                SELECT number from ccp_invoice where id = m.invoice_id LIMIT 1
            ) AS invoice_number,
            t.name AS type_text,
            m.description AS description,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (
                SELECT COALESCE(SUM(abs_amount)::numeric(14,2) , 0::double precision) FROM ccp_movement_additional where movement_id=m.id
            ) AS total_additionals,
            (
                (
                    SELECT
                        COALESCE(
                            sum(ccp_payment.amount), 0::double precision
                        ) AS sum
                    FROM
                        ccp_payment
                    WHERE
                        ccp_payment.movement_id = m.id
                )
            )::numeric(14,2) AS total_payments,
            (
                SELECT
                    count(ccp_payment.id) AS count
                FROM
                    ccp_payment
                WHERE
                    ccp_payment.movement_id = m.id
            ) AS count_payments,
            (
                SELECT
                    count(ccp_movement_additional.movement_id) AS count
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id
            ) AS count_additionals,
            (
                SELECT
                    string_agg(cma.additional_id::text, ','::text) AS string_agg
                FROM
                    ccp_movement_additional cma
                WHERE
                    cma.movement_id = m.id
            ) AS linked_additionals,
            (
                SELECT
                    string_agg(cp.id::text, ','::text) AS string_agg
                FROM
                    ccp_payment cp
                WHERE
                    cp.movement_id = m.id
            ) AS linked_payments,
            (
                SELECT
                    accountable_date
                FROM
                    ccp_payment cp
                WHERE
                    cp.movement_id = m.id ORDER BY accountable_date desc limit 1
            ) AS last_payment

       FROM
            ccp_movement m,
            ccp_type t,
            ccp_category c
       WHERE
            m.type_id = t.id AND
            t.category_id = c.id
  ) mv;


-- 3.13.8
CREATE OR REPLACE VIEW public.ccp_view_payment AS
 SELECT pay.id,
    pay.expiration_date,
    pay.movement_id,
    pay.creation_date,
    pay.operation_date,
    pay.accountable_date,
    pay.amount,
    pay.payment_method_id,
    pay.bollettino,
    pay.account_reference,
    pay.ccp_credit,
    pay.covered_movement_id,
    pay.payer_type,
    pay.payer_id,
    pay.payer_name,
    pay.payer_surname,
    pay.payer_fiscal_code,
    pay.payer_address,
    pay.payer_city,
    pay.payer_province,
    pay.payer_zip_code,
    pay.account_id,
    pay.receipt_id,
    pay.payment_group,
    pay.payer_data,
    pay.receipt_number,
    pay.receipt_date,
    pay.payment_method_text,
    pay.account_text,
    pay.movement_number,
    pay.subject_type,
    pay.subject_id,
    pay.subject_data,
    pay.subject_class,
    pay.subject_school_address_code,
    pay.subject_school_address,
    pay.subject_seat,
    pay.type_id,
    pay.section AS type_section,
    pay.type_text,
    pay.description,
    pay.category_id,
    pay.category_text,
    pay.incoming,
    pay.positive_additionals_euro,
    pay.negative_additionals_euro,
    pay.positive_additionals_perc,
    pay.negative_additionals_perc,
    pay.count_additionals,
    pay.note,
    pay.miscellaneous,
    pay.school_year,
    pay.subject_school_year,
    pay.linked_additionals,
    (pay.amount::double precision + pay.positive_additionals_euro::double precision - pay.negative_additionals_euro::double precision + pay.amount::double precision / 100::double precision * pay.positive_additionals_perc - pay.amount::double precision / 100::double precision * pay.negative_additionals_perc)::numeric(14,2) AS total
   FROM ( SELECT p.id,
            m.expiration_date,
            p.movement_id,
            p.operation_date,
            p.accountable_date,
            p.amount,
            p.payment_method_id,
            p.bollettino,
            p.account_reference,
            p.ccp_credit,
            p.covered_movement_id,
            p.payer_type,
            p.payer_id,
            p.payer_name,
            p.payer_surname,
            p.payer_fiscal_code,
            p.payer_address,
            p.payer_city,
            p.payer_province,
            p.payer_zip_code,
            p.account_id,
            p.receipt_id,
            p.payer_data,
            p.receipt_number,
            p.receipt_date,
            p.payment_group,
            w.name AS payment_method_text,
            a.denomination AS account_text,
            m.school_year,
            m.subject_school_year,
            m.number AS movement_number,
            m.creation_date,
            m.subject_type,
            m.subject_id,
            m.subject_data,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.subject_seat,
            m.description,
            m.type_id,
            t.section,
            m.note,
            m.miscellaneous,
            t.name AS type_text,
            t.category_id,
            c.name AS category_text,
            t.incoming,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = true))))::numeric(14,2) AS positive_additionals_euro,
            (( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = false AND ccp_additional.positive = false))))::numeric(14,2) AS negative_additionals_euro,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = true))) AS positive_additionals_perc,
            ( SELECT COALESCE(sum(ccp_payment_additional.amount), 0::double precision) AS sum
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id AND (ccp_payment_additional.additional_id IN ( SELECT ccp_additional.id
                           FROM ccp_additional
                          WHERE ccp_additional.percentual = true AND ccp_additional.positive = false))) AS negative_additionals_perc,
            ( SELECT count(ccp_payment_additional.payment_id) AS count
                   FROM ccp_payment_additional
                  WHERE ccp_payment_additional.payment_id = p.id) AS count_additionals,
            ( SELECT string_agg(cpa.additional_id::text, ','::text) AS string_agg
                   FROM ccp_payment_additional cpa
                  WHERE cpa.payment_id = p.id) AS linked_additionals
           FROM ( SELECT cp.id,
                    cp.movement_id,
                    cp.operation_date,
                    cp.accountable_date,
                    cp.amount::numeric(14,2) AS amount,
                    cp.payment_method_id,
                    cp.bollettino,
                    cp.account_reference,
                    cp.ccp_credit,
                    cp.covered_movement_id,
                    cp.payer_type,
                    cp.payer_id,
                    cp.payer_name,
                    cp.payer_surname,
                    cp.payer_fiscal_code,
                    cp.payer_address,
                    cp.payer_city,
                    cp.payer_province,
                    cp.payer_zip_code,
                    cp.account_id,
                    cp.receipt_id,
                    cp.payment_group,
                    (cp.payer_surname::text || ' '::text) || cp.payer_name::text AS payer_data,
                    cr.number AS receipt_number,
                    cr.date AS receipt_date
                   FROM ccp_payment cp
                     LEFT JOIN ccp_receipt cr ON cp.receipt_id = cr.id) p,
            ccp_payment_method w,
            core_bank_account a,
            ccp_movement m,
            ccp_type t,
            ccp_category c
          WHERE p.movement_id = m.id AND m.type_id = t.id AND t.category_id = c.id AND p.payment_method_id = w.id AND p.account_id = a.id) pay;

alter table auth_permission RENAME TO auth_permissions;