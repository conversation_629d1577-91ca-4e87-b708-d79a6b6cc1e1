-- add subject_type, subject_id and id serial to table ccp_movement_additional
ALTER TABLE ccp_movement_additional
	DROP CONSTRAINT IF EXISTS ccp_movement_additional_pkey,
	ADD column subject_type character varying(1),
	ADD column subject_id integer,
	ADD column id serial PRIMARY KEY
;

-- add fields to audit ccp_movement_additional_table
ALTER TABLE audit.ccp_movement_additional
    ADD column subject_type character varying(1),
	ADD column subject_id integer,
	ADD column id integer
;

DROP VIEW ccp_view_movement;
CREATE OR REPLACE VIEW ccp_view_movement AS
SELECT
    mv.id,
    mv.type_id,
    mv.section AS type_section,
    mv.subject_type,
    mv.subject_id,
    mv.miscellaneous,
    mv.number,
    mv.note,
    mv.school_year,
    mv.subject_school_year,
    mv.subject_data,
    mv.subject_seat,
    mv.subject_class,
    mv.subject_school_address_code,
    mv.subject_school_address,
    mv.amount::numeric(14,2) AS amount,
    mv.creation_date,
    mv.invoice_number,
    mv.expiration_date,
    mv.da_ratei,
    mv.a_ratei,
    mv.description,
    mv.type_text,
    mv.incoming,
    mv.category_id,
    mv.category_text,
    mv.total_payments,
    mv.count_payments,
    mv.positive_additionals_euro,
    mv.negative_additionals_euro,
    mv.positive_additionals_perc,
    mv.negative_additionals_perc,
    mv.count_additionals,
    mv.linked_additionals,
    mv.linked_payments,
    (
        mv.amount +
        mv.positive_additionals_euro::double precision -
        mv.negative_additionals_euro::double precision +
        (
            mv.amount +
            mv.positive_additionals_euro::double precision -
            mv.negative_additionals_euro::double precision
        ) / 100::double precision * mv.positive_additionals_perc -
        (
            mv.amount +
            mv.positive_additionals_euro::double precision -
            mv.negative_additionals_euro::double precision
        ) / 100::double precision * mv.negative_additionals_perc)::numeric(14,2)
        AS total,
    mv.invoice_id,
    mv.invoice_code
   FROM (
       SELECT
            m.id,
            m.type_id,
            t.section,
            m.invoice_id,
            m.invoice_code,
            m.subject_type,
            m.subject_id,
            m.miscellaneous,
            m.number,
            m.note,
            m.school_year,
            m.subject_school_year,
            m.subject_data,
            m.subject_seat,
            m.subject_class,
            m.subject_school_address_code,
            m.subject_school_address,
            m.amount,
            m.creation_date,
            m.expiration_date,
            m.da_ratei,
            m.a_ratei,
            (
                SELECT number from ccp_invoice where id = m.invoice_id LIMIT 1
            ) AS invoice_number,
            t.name AS type_text,
            /*
            (
                CASE
                    WHEN
                        (
                            m.description <> ''
                        )  IS NOT TRUE
                    THEN
                        t.name
                    ELSE
                        m.description
                END
            ) AS type_text,
            */
            m.description AS description,
            t.incoming,
            t.category_id,
            c.name AS category_text,
            (
                (
                    SELECT
                        COALESCE(
                            sum(ccp_payment.amount), 0::double precision
                        ) AS sum
                    FROM
                        ccp_payment
                    WHERE
                        ccp_payment.movement_id = m.id
                )
            )::numeric(14,2) AS total_payments,
            (
                SELECT
                    count(ccp_payment.id) AS count
                FROM
                    ccp_payment
                WHERE
                    ccp_payment.movement_id = m.id
            ) AS count_payments,
            (
                (
                    SELECT
                        COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                    FROM
                        ccp_movement_additional
                    WHERE
                        ccp_movement_additional.movement_id = m.id
                    AND (
                        ccp_movement_additional.additional_id IN (
                            SELECT
                                ccp_additional.id
                            FROM
                                ccp_additional
                            WHERE
                                ccp_additional.percentual = false AND
                                ccp_additional.positive = true
                        )
                    )
                )
            )::numeric(14,2) AS positive_additionals_euro,
            (
                (
                    SELECT
                        COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                    FROM
                        ccp_movement_additional
                    WHERE
                        ccp_movement_additional.movement_id = m.id AND
                        (
                            ccp_movement_additional.additional_id IN (
                                SELECT
                                    ccp_additional.id
                                FROM
                                    ccp_additional
                                WHERE
                                    ccp_additional.percentual = false AND
                                    ccp_additional.positive = false
                            )
                        )
                )
            )::numeric(14,2) AS negative_additionals_euro,
            (
                SELECT
                    COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id AND
                    (
                        ccp_movement_additional.additional_id IN (
                            SELECT
                                ccp_additional.id
                            FROM
                                ccp_additional
                            WHERE
                                ccp_additional.percentual = true AND
                                ccp_additional.positive = true
                        )
                    )
            ) AS positive_additionals_perc,
            (
                SELECT
                    COALESCE(sum(ccp_movement_additional.amount), 0::double precision) AS sum
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id AND
                    (
                        ccp_movement_additional.additional_id IN (
                            SELECT
                                ccp_additional.id
                            FROM
                                ccp_additional
                            WHERE
                                ccp_additional.percentual = true AND
                                ccp_additional.positive = false
                        )
                    )
            ) AS negative_additionals_perc,
            (
                SELECT
                    count(ccp_movement_additional.movement_id) AS count
                FROM
                    ccp_movement_additional
                WHERE
                    ccp_movement_additional.movement_id = m.id
            ) AS count_additionals,
            (
                SELECT
                    string_agg(cma.additional_id::text, ','::text) AS string_agg
                FROM
                    ccp_movement_additional cma
                WHERE
                    cma.movement_id = m.id
            ) AS linked_additionals,
            (
                SELECT
                    string_agg(cp.id::text, ','::text) AS string_agg
                FROM
                    ccp_payment cp
                WHERE
                    cp.movement_id = m.id
            ) AS linked_payments

       FROM
            ccp_movement m,
            ccp_type t,
            ccp_category c
       WHERE
            m.type_id = t.id AND
            t.category_id = c.id
  ) mv;

