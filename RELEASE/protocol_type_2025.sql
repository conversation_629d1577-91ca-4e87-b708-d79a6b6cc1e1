DELETE FROM protocol_type;

SELECT pg_catalog.setval('protocol_type_id_seq', 1, true);


-- I AMMINISTRAZIONE
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1000, 'I', 'AMMINISTRAZIONE', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1001, '1', 'Normativa e disposizioni attuative', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1002, '2', 'Organigramma e funzionigramma', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1003, '3', 'Statistica e sicurezza di dati e informazioni', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1004, '4', 'Archivio, accesso, privacy, trasparenza e relazioni con il pubblico', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1005, '5', 'Registri e repertori di carattere generale', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1006, '6', 'Audit, qualità, carta dei servizi, valutazione e autovalutazione', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1007, '7', 'Elezioni e nomine', 1000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (1008, '8', 'Eventi, cerimoniale, patrocini, concorsi, editoria e stampa', 1000);

-- II ORGANI E ORGANISMI
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2000, 'II', 'ORGANI E ORGANISMI', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2001, '1', 'Consiglio di istituto, Consiglio di circolo e Consiglio di Amministrazione', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2002, '2', 'Consiglio di classe e di interclasse', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2003, '3', 'Collegio dei docenti', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2004, '4', 'Giunta esecutiva', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2005, '5', 'Dirigente scolastico DS', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2006, '6', 'Direttore dei servizi generali e amministrativi DSGA', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2007, '7', 'Comitato di valutazione del servizio dei docenti', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2008, '8', 'Comitato dei genitori, Comitato studentesco e rapporti scuola-famiglia', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2009, '9', 'Reti scolastiche', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2010, '10', 'Rapporti sindacali, contrattazione e Rappresentanza sindacale unitaria (RSU)', 2000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (2011, '11', 'Commissioni e gruppi di lavoro', 2000);

-- III ATTIVITÀ GIURIDICO-LEGALE
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (3000, 'III', 'ATTIVITÀ GIURIDICO-LEGALE', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (3001, '1', 'Contenzioso', 3000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (3002, '2', 'Violazioni amministrative e reati', 3000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (3003, '3', 'Responsabilità civile, penale e amm.va', 3000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (3004, '4', 'Pareri e consulenze', 3000);

-- IV DIDATTICA
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4000, 'IV', 'DIDATTICA', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4001, '1', 'Piano triennale dell’offerta formativa PTOF', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4002, '2', 'Attività extracurricolari', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4003, '3', 'Registro di classe, dei docenti e dei profili', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4004, '4', 'Libri di testo', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4005, '5', 'Progetti e materiali didattici', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4006, '6', 'Viaggi di istruzione, scambi, stage e tirocini', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4007, '7', 'Biblioteca, emeroteca, videoteca e sussidi', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4008, '8', 'Salute e prevenzione', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4009, '9', 'Attività sportivo‐ricreative e rapporti con il Centro Scolastico Sportivo', 4000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (4010, '10', 'Elaborati e prospetti scrutini', 4000);

-- V STUDENTI E DIPLOMATI
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5000, 'V', 'STUDENTI E DIPLOMATI', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5001, '1', 'Orientamento e placement', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5002, '2', 'Ammissioni e iscrizioni', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5003, '3', 'Anagrafe studenti e formazione delle classi', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5004, '4', 'Cursus studiorum', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5005, '5', 'Procedimenti disciplinari', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5006, '6', 'Diritto allo studio e servizi agli studenti (trasporti, mensa, buoni libro, etc.)', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5007, '7', 'Tutela della salute e farmaci', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5008, '8', 'Esoneri', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5009, '9', 'Prescuola e attività parascolastiche', 5000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (5010, '10', 'Disagio e diverse abilità – DSA', 5000);

-- VI FINANZA E PATRIMONIO
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6000, 'VI', 'FINANZA E PATRIMONIO', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6001, '1', 'Entrate e finanziamenti del progetto', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6002, '2', 'Uscite e piani di spesa', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6003, '3', 'Bilancio, tesoreria, cassa, istituti di credito e verifiche contabili', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6004, '4', 'Imposte, tasse, ritenute previdenziali e assistenziali, denunce', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6005, '5', 'Assicurazioni', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6006, '6', 'Utilizzo beni terzi, comodato', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6007, '7', 'Inventario e rendiconto patrimoniale', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6008, '8', 'Infrastrutture e logistica (plessi, succursali)', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6009, '9', 'DVR e sicurezza', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6010, '10', 'Beni mobili e servizi', 6000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (6011, '11', 'Sistemi informatici, telematici e fonia', 6000);

-- VII PERSONALE
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7000, 'VII', 'PERSONALE', NULL);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7001, '1', 'Organici, lavoratori socialmente utili, graduatorie', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7002, '2', 'Carriera', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7003, '3', 'Trattamento giuridico‐economico', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7004, '4', 'Assenze', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7005, '5', 'Formazione, aggiornamento e sviluppo professionale', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7006, '6', 'Obiettivi, incarichi, valutazione e disciplina', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7007, '7', 'Sorveglianza sanitaria', 7000);
INSERT INTO protocol_type (id, code, description, parent_type_id) VALUES (7008, '8', 'Collaboratori esterni', 7000);


SELECT pg_catalog.setval('protocol_type_id_seq', (SELECT max(id) + 1 FROM protocol_type), true);
