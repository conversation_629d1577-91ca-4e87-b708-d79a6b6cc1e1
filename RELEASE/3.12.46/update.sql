UPDATE parameter SET name='CCP_FAMILY_MANAGEMENT',value='f' WHERE name = 'DEPOSIT_SUBJECT_TYPE';

ALTER TABLE ccp_type ADD COLUMN payment_mail character varying(255);
ALTER TABLE audit.ccp_type ADD COLUMN payment_mail character varying(255);

/*
---- FIX MOVIMENTI PER SALDI ---

    Si prendono tutti i movimenti con subject_school_year null oppure 'TUTTI' (che è un  errore)
    e si convertono come segue:
        - se school_year != 'TUTTI' => subject_school_year=school_year
            - SQL: UPDATE ccp_movemente set subject_school_year=school_year WHERE (subject_school_year is null OR subject_school_year='TUTTI') AND school_year != 'TUTTI';
        - se school_year == 'TUTTI' => se expiration_date >= 1/9 allora Y/Y+1 altrimenti Y-1/Y
            - SQL: UPDATE ccp_movement set subject_school_year=(CASE WHEN to_char(to_timestamp(expiration_date),'MM')::numeric >= 9 THEN to_char(to_timestamp(expiration_date),'YYYY')||'/'||to_char(to_timestamp(expiration_date),'YYYY')::numeric+1 ELSE to_char(to_timestamp(expiration_date),'YYYY')::numeric-1||'/'||to_char(to_timestamp(expiration_date),'YYYY')  END) WHERE (subject_school_year is null OR subject_school_year='TUTTI') AND school_year != 'TUTTI';*/
---- FINE FIX MOVIMENTI PER SALDI ---



-- FIX ORDINAMENTO SCONTI --
-- Sconti percentuale
/*
UPDATE ccp_type_additional SET discount_order = id WHERE additional_id IN (
    SELECT id FROM ccp_additional WHERE positive = 'f' AND percentual = 't' AND payment = 'f'
);

-- Sconti valore assoluto
UPDATE ccp_type_additional SET discount_order = (id + 1000) WHERE additional_id IN (
    SELECT id FROM ccp_additional WHERE positive = 'f' AND percentual = 'f' AND payment = 'f' AND code <> 'ABS_DISCOUNT'
);
*/