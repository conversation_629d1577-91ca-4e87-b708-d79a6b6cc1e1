CREATE TABLE ccp_easy_select (
    id SERIAL NOT NULL PRIMARY KEY,
    payment_method INTEGER,
    force_single_invoice BOOLEAN
);

ALTER TABLE ccp_movement ADD COLUMN ccp_easy_select INTEGER;
ALTER TABLE audit.ccp_movement ADD COLUMN ccp_easy_select INTEGER;

ALTER TABLE ccp_movement DROP COLUMN easy_select;
ALTER TABLE audit.ccp_movement DROP COLUMN easy_select;

ALTER TABLE ccp_movement
ADD CONSTRAINT ccp_easy_select_fk
FOREIGN KEY (ccp_easy_select)
REFERENCES ccp_easy_select(id)
ON DELETE SET NULL;
