<?php

$basePath = '/var/files/mc2/mail_attachments/';

$dbconn = pg_connect("host=localhost port=5432 dbname=mastercom2 user=postgres password=postgres");
$result = pg_query($dbconn, "SELECT ama.id, am.date, ama.path from archive_mail_attachment ama, archive_mail am WHERE ama.account=am.account and am.id=ama.mail order by am.date");

while ($row = pg_fetch_assoc($result)) {
    $date = date('Y/m/d', strtotime($row['date']));
    $oldPath = $row['path'];

    // Create folder if not exists
    if(!file_exists($basePath . $date)){
        if (!mkdir($basePath . $date, 0777, true)) {
            die('Failed to create folder');
        }
    }
    $newPath = str_replace($basePath, $basePath . $date . '/', $oldPath);

    if (!copy($oldPath, $newPath)) {
        echo "Failed to copy $file\n";
    }

    pg_query($dbconn, "UPDATE archive_mail_attachment SET path='{$newPath}' WHERE id = " . $row['id']);
}

shell_exec("find {$basePath} -maxdepth 1 -type f -exec rm -f {} \;");