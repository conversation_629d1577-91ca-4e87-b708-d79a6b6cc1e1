<?php

/* RIGHT ROWS EXAMPLE
 *
 *
  1|UFFICIO SCOLASTICO PROVINCIALE|VIA PRADELLO 12|BERGAMO|24100|BG||||UFFICIO SCOLASTICO PROVINCIALE
  2|UFFICIO SCOLASTICO REGIO|PIAZZA DIAZ, 6|MILANO|20123|MI|2.723.091|2.874.211||Enti pubblici
  3|A. S. L.||||||||Enti pubblici
 *
 *
 */

header('Content-type: text/html; charset=utf-8');

// Imports INIT scripts
require_once dirname(__FILE__) . '/../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';
require_once PATH_ROOT . 'configurations/init-api.php';

// Sets error reporting for all errors
error_reporting(E_ALL);

// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");



if (empty($argv[1])) {
    echo "Specificare path del file .csv \n";
    exit(1);
}

$path = $argv[1];

$file = fopen($path, 'r');
if ($file === false) {
    echo "Errore durante l'apertura del file \n";
    exit(1);
}

$db = new \MT\Utils\Db;
$db->setDatabaseParameters([
    'host'     => 'localhost',
    'port'     => '5432',
    'name'     => 'mastercom2',
    'schema'   => 'public',
    'user'     => 'postgres',
    'password' => 'postgres'
]);

while (!feof($file)) {
    $line = fgetcsv($file, null, '|');

    $city = $db->queryParams("SELECT city_id FROM cities WHERE description ILIKE $1 LIMIT 1", ["%{$line[2]}%"]);

    $params = [
        $line[0],       // title
        $line[8],       // note
        -1,             // correspondent_type_id
        'M',            // correspondent_type
        $line[4],           // fiscal_code
        $city['city_id'],          // city_id
        $line[3],       // zipcode
        $line[1],       // address
        $line[5],       // phone
        $line[6],       // fax
        $line[7]        // email
    ];

    $db->queryParams("INSERT INTO protocol_correspondent "
            . "(title, note, correspondent_type_id, correspondent_type, fiscal_code, city_id, zipcode, address, phone, fax, email) "
            . "VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)", $params);


}
