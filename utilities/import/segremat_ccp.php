<?php

$segrematConn = pg_connect("host=localhost port=5432 dbname=segremat user=postgres password=postgres");
$mc2Conn = pg_connect("host=localhost port=5432 dbname=mastercom2 user=postgres password=postgres");
$mcConn = pg_connect("host=localhost port=5432 dbname=mastercom_2018_2019 user=postgres password=postgres");

pg_query($mc2Conn, 'TRUNCATE ccp_type CASCADE');
pg_query($mc2Conn, 'TRUNCATE ccp_movement CASCADE');
pg_query($mc2Conn, 'TRUNCATE ccp_credits CASCADE');
pg_query($mc2Conn, "UPDATE parameter SET value = 1000 WHERE name = 'SEAT_ID'");



/* IMPORT TYPE */
$taxTypeMc2Id = 1; // Tasse
$result = pg_query($segrematConn, 'select * from "Pagamenti"."Causali" order by "Tipo" desc');
$types = pg_fetch_all($result);
foreach ($types as $t) {
    $resultType = pg_query($segrematConn, 'select * from "Pagamenti"."Movimenti" where "Causale"=' . $t['Tipo']);
    $movementsByType = pg_fetch_all($resultType);

    if($movementsByType === false) continue;


    $t['Descrizione'] = pg_escape_string($t['Descrizione']);
    $sqlType  = "INSERT INTO ccp_type (id, category_id, name) VALUES ({$t['Tipo']},{$taxTypeMc2Id}, '{$t['Descrizione']}')";
    pg_query($mc2Conn, $sqlType);
}
/* END IMPORT TYPE */


/* IMPORT MOVEMENT */
$school_year = "2019/2020";
$result = pg_query($segrematConn, 'select * from "Pagamenti"."Movimenti"');
$movements = pg_fetch_all($result);
foreach ($movements as $m) {
    $number = (int)$m['NumeroMovimento'];
    $creation_date = strtotime($m['DataOrigine']);
    $note = pg_escape_string($m['Descrizione'].' - '.$m['Note']. ' - '. $m['Suffisso']);
    $amount = (double) $m['Importo'];
    $expiration_date = strtotime($m['DataScadenza']);
    $type_id = (int) $m['Causale'];


    $cfSql = 'SELECT "CF_Alunno" as fiscal_code, "Libero9" as credito FROM "Anagrafe"."Anagrafica" WHERE "CodiceAlunno" = '. $m['CodiceAlunno'] .' LIMIT 1';
    $result2 = pg_query($segrematConn, $cfSql);
    $studentSegremat = pg_fetch_assoc($result2);
    $fiscal_code = $studentSegremat['fiscal_code'];
    $credito = (double) $studentSegremat['credito'];

    $subjectSql = "
            SELECT
                -- BASE DATA
                id_studente AS db_id,
                nome AS name,
                cognome AS surname,
                id_classe AS class_id,
                codice_indirizzi  AS school_address_code,
                descrizione_indirizzi AS school_address,
                classe AS class,
                sezione AS section,
                matricola AS registration_number,
                codice_fiscale AS fiscal_code,
                sesso AS sex,
                'S' as type,
                to_char(to_timestamp(data_nascita),'DDMMYYYY') AS dob,

                -- RESIDENCE
                cap_residenza AS zip_code,
                indirizzo AS address,
                descrizione_residenza AS city,
                provincia_residenza_da_comune AS province,

                -- BIRTH
                provincia_nascita AS birth_province,
                descrizione_nascita AS birth_city,
                provincia_nascita_da_comune AS birth_province_code,
                descrizione_stato_nascita AS birth_country
            FROM
                public.studenti_completi AS s
            WHERE
                ordinamento = '0'
                AND codice_fiscale = '$fiscal_code'
            LIMIT 1
                ";
    $result3 = pg_query($mcConn, $subjectSql);
    $subject = pg_fetch_assoc($result3);

    $subject_id = (int) $subject['db_id'];
    $subject_class = pg_escape_string($subject['class'].$subject['section']);
    $subject_data = pg_escape_string($subject['surname'].' ' .$subject['name']);
    $subject_school_address_code = pg_escape_string($subject['school_address_code']);
    $subject_school_address = pg_escape_string($subject['school_address']);

    /* END SUBJECT DATA */
    // $sqlCredit  = "INSERT INTO ccp_credits (description, student_id, amount) VALUES ('Dote', '$subject_id', $credito)";
    // pg_query($mc2Conn, $sqlCredit);


    $sqlMovement  = "
        INSERT INTO ccp_movement (
            number,
            creation_date,
            note,
            amount,
            expiration_date,
            type_id,
            school_year,
            subject_type,
            subject_id,
            subject_class,
            subject_data,
            subject_school_address_code,
            subject_school_address,
            subject_seat
        ) VALUES (
            {$number},
            {$creation_date},
            '{$note}',
            {$amount},
            {$expiration_date},
            {$type_id},
            '{$school_year}',
            'S',
            {$subject_id},
            '{$subject_class}',
            '{$subject_data}',
            '{$subject_school_address_code}',
            '{$subject_school_address}',
            '1000'
        )";
    pg_query($mc2Conn, $sqlMovement);
}
/* END IMPORT MOVEMENT */


$studentsSql = "
        SELECT
            id_studente,
            codice_fiscale
        FROM
            public.studenti_completi AS s
        WHERE
            ordinamento = '0'";
$result4 = pg_query($mcConn, $studentsSql);
$students = pg_fetch_all($result4);


foreach ($students as $key => $student) {
    $id = $student['id_studente'];

    // $sqlCheckCredit  = "SELECT count(*) FROM ccp_credits WHERE student_id = {$id}";
    // $result5 = pg_query($mc2Conn, $sqlCheckCredit);
    // $count = pg_fetch_all($result5)[0]['count'];

    $cfSql = 'SELECT "Libero9" as credito FROM "Anagrafe"."Anagrafica" WHERE "CF_Alunno" = \''. $student['codice_fiscale'] .'\' LIMIT 1';
    $result2 = pg_query($segrematConn, $cfSql);
    $studentSegremat = pg_fetch_assoc($result2);
    $credito = (double) $studentSegremat['credito'];

    $sqlCredit  = "INSERT INTO ccp_credits (description, student_id, amount) VALUES ('Dote', '$id', $credito)";
    pg_query($mc2Conn, $sqlCredit);


}
