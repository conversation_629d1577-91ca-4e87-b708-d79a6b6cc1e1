<?php

/* RIGHT ROWS EXAMPLE
 *
 *
  A||||
  A|5||INVENTARIO - PASSAGGIO CO|
  A|7||ZECCA DELLO STATO: RICHIES|
  A|11||SCARTI DI ATTI DI ARCHIVIO|
  A|14||CONCESSIONI FERROVIARIE|
  A|15||DECENTRAMENTO AMMINISTR|
  A|16||RAPPORTI CON ENTI LOCALI E|
  A|16|a|RAPPORTI CON ENTI LOCALI E|Comune
  A|16|b|RAPPORTI CON ENTI LOCALI E|Provincia
  A|16|c|RAPPORTI CON ENTI LOCALI E|Regioni, IRRSAE, CITE, IRRE
  A|16|d|RAPPORTI CON ENTI LOCALI E|Unione Industriali
  A|19||ORGANI COLLEGIALI DELLA SC|
  A|19|a|ORGANI COLLEGIALI DELLA SC|Consiglio di Istituto
  A|19|b|ORGANI COLLEGIALI DELLA SC|Giunta Esecutiva
  A|19|c|ORGANI COLLEGIALI DELLA SC|Collegio dei Docenti
  A|19|d|ORGANI COLLEGIALI DELLA SC|Consigli di Classe
  A|19|f|ORGANI COLLEGIALI DELLA SC|Commissioni varie e loro relazioni
  A|19|g|ORGANI COLLEGIALI DELLA SC|Funzioni strumentali al POF- Funz
 *
 *
 */


header('Content-type: text/html; charset=utf-8');


// Imports INIT scripts
require_once dirname(__FILE__) . '/../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';
require_once PATH_ROOT . 'configurations/init-api.php';

// Sets error reporting for all errors
error_reporting(E_ALL);

// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");



if (empty($argv[1])) {
    echo "Specificare path del file .csv \n";
    exit(1);
}

$path = $argv[1];

$file = fopen($path, 'r');
if ($file === false) {
    echo "Errore durante l'apertura del file \n";
    exit(1);
}

while (!feof($file)) {
    $line = fgetcsv($file, null, '|');

    $line0 = trim($line[0]);
    $line1 = trim($line[1]);
    $line2 = trim($line[2]);

    $type = new Protocol\Type;
    $type->setDescription($line[3]);
    if (!$line1) { // TOP LEVEL
        $type->setCode($line0);


    } else if (!$line2) { // ONE SUB LEVEL
        $type->setCode($line1);

        $parent = \Protocol\TypeQuery::create()->filterByCode($line0)->filterByParentTypeId(NULL)->findOne();
        $type->setParentTypeId($parent->getId());

    } else { // TWO SUB LEVEL
        $type->setCode($line2);

        $parent = \Protocol\TypeQuery::create()->filterByCode($line0)->filterByParentTypeId(NULL)->findOne();
        $subparent = \Protocol\TypeQuery::create()->filterByCode($line1)->filterByParentTypeId($parent->getId())->findOne();

        $type->setParentTypeId($subparent->getId());

    }

    $type->save();
}
