<?php

/* RIGHT ROWS EXAMPLE
 *
 *
  1|UFFICIO SCOLASTICO PROVINCIALE|VIA PRADELLO 12|BERGAMO|24100|BG||||UFFICIO SCOLASTICO PROVINCIALE
  2|UFFICIO SCOLASTICO REGIO|PIAZZA DIAZ, 6|MILANO|20123|MI|2.723.091|2.874.211||Enti pubblici
  3|A. S. L.||||||||Enti pubblici
 *
 *
 */

header('Content-type: text/html; charset=utf-8');

// Imports INIT scripts
require_once dirname(__FILE__) . '/../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';
require_once PATH_ROOT . 'configurations/init-api.php';

// Sets error reporting for all errors
error_reporting(E_ALL);

// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");



if (empty($argv[1])) {
    echo "Specificare path del file .csv \n";
    exit(1);
}

$path = $argv[1];

$file = fopen($path, 'r');
if ($file === false) {
    echo "Errore durante l'apertura del file \n";
    exit(1);
}

$db = new \MT\Utils\Db;

if ( ! getenv('DB_HOST') ){
    putenv( 'DB_HOST=localhost');
}
$db->setDatabaseParameters([
    'host'     => 'localhost',
    'port'     => '5023',
    'name'     => 'mastercom2',
    'schema'   => 'public',
    'user'     => 'postgres',
    'password' => 'postgres'
]);


$i = 0;
while ( ($line = fgetcsv($file, null, '|')) !== false ) {
    $i++;

    $surname = pg_escape_string(trim($line[0]));
    $name = pg_escape_string(trim($line[1]));
    $type = trim($line[2]) == 'DOCENTE' ? '0001' : '0002';
    $birthdate = trim($line[3]) ? strtotime(trim($line[3])) : null;
    $fiscal_code = pg_escape_string(trim($line[6]));
    $email = pg_escape_string(trim($line[14]));
    $address = pg_escape_string(trim($line[7]));
    $phone = pg_escape_string(trim($line[11]));
    $city = pg_escape_string(trim($line[9]));
    $bplace = pg_escape_string(trim($line[4]));
    $cap = pg_escape_string(trim($line[8]));
    $mobile = pg_escape_string(trim($line[12]));
    if ($mobile !== '') {
        $mobile = $mobile.'-'.pg_escape_string(trim($line[13]));
    } else {
        $mobile = pg_escape_string(trim($line[13]));
    }


    $birthplace = null;
    if($bplace){
        $res2 = $db->queryParams("SELECT * from cities where description = $1 LIMIT 1", [$bplace]);
        $birthplace = trim($res2['city_id']) ? trim($res2['city_id']) : null;
    }

    $residenceId = null;
    if($city or $email or $address or $phone or $mobile or $cap){
        $res = $db->queryParams("SELECT * from cities where description = $1 LIMIT 1", [$city]);

        $city_id = $res['city_id'] ? $res['city_id'] : null;

        $res = $db->queryParams(
            "INSERT INTO contact (address, phone_num, mobile, cap, city_id, email) VALUES ($1,$2,$3,$4,$5,$6) RETURNING contact_id",
            [$address, $phone, $mobile, $cap, $city_id, $email]
        );
        $residenceId = $res['contact_id'];
    } else {
        echo 'Contact non presente alla linea '. $i . "\n";
    }

    $params = [
        $surname,       // cognome
        $name,          // nome
        $type,          // type
        $birthdate,     // birthdate (YYYY-MM-DD)
        $birthplace,    // birth place
        $fiscal_code,   // fiscal code
        $residenceId    // Residence id created before
    ];

    // INSERT OR UPDATE ??


    $employee = $db->queryParams("SELECT * FROM employee WHERE surname = $1 AND name = $2 LIMIT 1", [$surname, $name]);
    if (is_array($employee) and ($surname or $name)) {
        if($employee['fiscal_code']!== $fiscal_code) {
            print("UPDATE employee SET fiscal_code='$fiscal_code', residence_id=$residenceId WHERE employee_id = ". $employee['employee_id'].";\n");
        }
        // $db->queryParams("UPDATE employee SET birthdate = $1, birthplace = $2, fiscal_code=$3, residence_id=$4 VALUES ($1, $2, $3, $4, $5, $6, $7)", $params);
    } else {
        print('INSERT ' . $params[0]."\n");
        /*$db->queryParams("INSERT INTO employee "
            . "(surname, name, liquid_group, birthdate, birthplace, fiscal_code, residence_id) VALUES ($1, $2, $3, $4, $5, $6, $7)", $params);*/
    }

}
