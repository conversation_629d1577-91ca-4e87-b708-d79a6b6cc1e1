<?php

/* RIGHT ROWS EXAMPLE
 *
 *
  1|UFFICIO SCOLASTICO PROVINCIALE|VIA PRADELLO 12|BERGAMO|24100|BG||||UFFICIO SCOLASTICO PROVINCIALE
  2|UFFICIO SCOLASTICO REGIO|PIAZZA DIAZ, 6|MILANO|20123|MI|2.723.091|2.874.211||Enti pubblici
  3|A. S. L.||||||||Enti pubblici
 *
 *
 */

header('Content-type: text/html; charset=utf-8');

// Imports INIT scripts
require_once dirname(__FILE__) . '/../../configurations/init-vars.php';
require_once PATH_ROOT . 'configurations/init-autoload.php';
require_once PATH_ROOT . 'configurations/init-db.php';
require_once PATH_ROOT . 'configurations/init-locale.php';
require_once PATH_ROOT . 'configurations/init-api.php';

// Sets error reporting for all errors
error_reporting(E_ALL);

// Imports the main Propel script and initialize Propel with the runtime configuration
require_once PATH_PROPEL . 'runtime/lib/Propel.php';
Propel::init(PATH_ROOT . "configurations/mc2api-conf.php");



if (empty($argv[1])) {
    echo "Specificare path del file .csv \n";
    exit(1);
}

$path = $argv[1];

$file = fopen($path, 'r');
if ($file === false) {
    echo "Errore durante l'apertura del file \n";
    exit(1);
}

$db = new \MT\Utils\Db;
$db->setDatabaseParameters([
    'host'     => 'localhost',
    'port'     => '5432',
    'name'     => 'mastercom2',
    'schema'   => 'public',
    'user'     => 'postgres',
    'password' => 'postgres'
]);

while (!feof($file)) {
    $line = fgetcsv($file, null, '|');

    $params = [
        $line[0],       // title
    ];

    $db->queryParams("INSERT INTO protocol_subject_kind "
            . "(title) VALUES ($1)", $params);


}
