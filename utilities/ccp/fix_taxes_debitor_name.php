<?php

/*
 * <PERSON><PERSON> tabella tasse in cui sia presente l'id studente ma non il debitore e la classe,
 * vengono importati questi ultimi due dati cercando a ritroso fra i db di Mastercom.
 *
 * Questi dati non venivano importati dall'importatore delle tasse da Mastercom a Mc2
 * perchè non venivano trovati nel database attuale e non venivano cercati negli storici.
 *
 * Con questo script si colma questa lacuna che è comunque da fixare nello script migrate_taxes.
 */

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';

function try_update_student($con, $tax) {
    if (!$con) {
        return false;
    }

    $query = "SELECT cognome||' '||nome AS nome, classe||' '||sezione AS classe FROM studenti_completi "
            . "WHERE id_studente = {$tax['id_studente']} "
            . "AND ordinamento='0' "
            . "LIMIT 1";
    $student = $con->query($query);

    if (is_array($student)) {
        $updateSql = "UPDATE tasse SET dati_debitore = '" . pg_escape_string($student['nome']) . "', classe= '" . pg_escape_string($student['classe']) . "' WHERE id_tasse = " . $tax['id_tasse'] . ";\n";
        file_put_contents('/var/lib/postgresql/update_studenti.sql', $updateSql, FILE_APPEND);
        return true;
    }

    return false;
}

// Prepare db instance for mc and mc2
$conMc2 = new \MT\Utils\Db;
$conMc2->name = 'mastercom2';
$conMc2->port = DB_PORT;

// Create instance for all mastercom databases
$mastercomConfig = file_get_contents('/etc/mastercom/mastercom.conf');
preg_match_all('/mastercom_\d{4}_\d{4}/', $mastercomConfig, $find);
$find = $find[0];
$conMc = array_unique($find);
$conMcs = [];

foreach ($conMc as $key => $dbName) {
    $dbMc = new MT\Mastercom\Db;
    $dbMc->name = $dbName;
    $conMcs[$dbName] = $dbMc;
}
// Decrease order
krsort($conMc);

// Take all taxes from mc2 whre id_studente > 0 and trim(dati_debitore) = ''
$query = "SELECT id_tasse, id_studente, anno_scolastico FROM tasse WHERE id_studente > 0 AND trim(dati_debitore) = ''";
$taxes = $conMc2->query($query);

// For every tax search in mastercom data about students. If there aren't data for current school year
// it tries to search in history databases
foreach ($taxes as $tax) {

    $dbTaxStr = 'mastercom_' . str_replace('/', '_', $tax['anno_scolastico']);

    if (!try_update_student($conMcs[$dbTaxStr], $tax)) {
        foreach ($conMcs as $key => $con) {
            if (try_update_student($con, $tax)) {
                break;
            }
        }
    }
}

