<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/funzioni_generiche.php';


// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);

// tasse (movimenti + pagamenti)
$ccpMovementQuery = pg_query($db, "SELECT id, number::numeric, to_timestamp(creation_date) as cd FROM ccp_movement WHERE to_timestamp(creation_date) >= '2015-12-31' AND number IS NULL ORDER BY 3 ASC");
$ccpMovementList = pg_fetch_all($ccpMovementQuery);

$i = 9;
foreach ($ccpMovementList as $ccpMovement) {
    $addNumber = pg_query($db, "UPDATE ccp_movement SET number = '{$i}' WHERE id = {$ccpMovement['id']}");
    $i++;
}
