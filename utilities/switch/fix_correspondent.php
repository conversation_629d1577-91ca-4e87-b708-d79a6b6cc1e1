<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/funzioni_generiche.php';

$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$db = pg_connect($connectionString);

//$ccpProtocolQuery = pg_query($db, "SELECT id, title FROM protocol_correspondent WHERE title LIKE '%BANCA MOLISANA DI%' ORDER BY 2");
$ccpProtocolQuery = pg_query($db, "SELECT id, title FROM protocol_correspondent WHERE title <> '' ORDER BY 2");
$ccpProtocolList = pg_fetch_all($ccpProtocolQuery);

$correspondentTitle = [];
$correspondentDelete = [];

foreach ($ccpProtocolList as $ccpProtocol) {
    if (!in_array($ccpProtocol['title'], $correspondentTitle)) {
        $correspondentTitle[$ccpProtocol['id']] = $ccpProtocol['title'];
    } else {
        $correspondentDelete[$ccpProtocol['id']] = $ccpProtocol['title'];
    }
}

file_put_contents('/tmp/corresp', print_r($correspondentTitle, true));
file_put_contents('/tmp/delete', print_r($correspondentDelete, true));

foreach ($correspondentTitle as $id => $title) {
    $ccpProtocolCorrespondentQuery = pg_query($db, "SELECT * FROM protocol_protocol_correspondent WHERE correspondent_id IN (SELECT id FROM protocol_correspondent WHERE title = '{$title}')");
    $ccpProtocolCorrespondent = pg_fetch_all($ccpProtocolCorrespondentQuery);
    $ccpProtocolCorrespondentNumber = pg_num_rows($ccpProtocolCorrespondentQuery);

    if ($ccpProtocolCorrespondentNumber > 1) {
        foreach ($ccpProtocolCorrespondent as $protocolUpdate) {
            $queryUpdate = "UPDATE protocol_protocol_correspondent
                            SET correspondent_id = {$id}
                            WHERE protocol_id = {$protocolUpdate['protocol_id']}
                                AND correspondent_id <> {$id}
                                AND correspondent_id = {$protocolUpdate['correspondent_id']}";
            $ccpProtocolCorrespondentQuery = pg_query($db, $queryUpdate);
        }
    }
}

foreach ($correspondentDelete as $key => $delete) {
    $ccpProtocolDelete = pg_query($db, "DELETE FROM protocol_correspondent WHERE id = {$key}");
}

//UPDATE protocol_correspondent SET title = replace(title, '&''', '&#039;') WHERE title ILIKE '%&''%';
//UPDATE protocol_correspondent SET title = replace(title, '''', '&#039;') WHERE title ILIKE '%''%';