<?php

// Cleans data on mastercom2 for tasse and tipi_tasse tables
print "\n Cleaning tables \n";

$cleaningQuery = "BEGIN;"
        . "DELETE FROM tasse;"
        . "DELETE FROM tipi_tasse;"
        . "COMMIT;";
$res = pg_query($db, $cleaningQuery);
if ($res === false) {
    print "\n Error while cleaning tables! ROLLBACK DONE! \n";
    exit(1);
}

// Starts taxes migration
print "\n Migrating data \n";

// Takes taxes from mastercom
$rt = pg_query($dbMc, "SELECT * FROM tasse WHERE flag_canc = 0");
$taxes = pg_fetch_all($rt);

// Takes taxes type from mastercom
$rtt = pg_query($dbMc, "SELECT * FROM tipi_tasse WHERE flag_canc = 0");
$taxes_type = pg_fetch_all($rtt);

$query = "BEGIN;";
// Inserts Tax Types
if ($taxes_type !== false) {
    foreach ($taxes_type as $tax_type) {
        $ib = pg_escape_string($tax_type['importo_base']);
        $ib = str_replace(',', '.', $ib);
        $ib = $ib == '' ? '0.00' : $ib;
        $ib = preg_match('/^([0-9]+[.]?[0-9]*|[.][0-9]+)$/', $ib) != 1 ? '0.00' : $ib;
        $ib = floatval($ib);

        $query .= "
		INSERT INTO tipi_tasse (
			id_tipo_tassa,
			descrizione,
			importo_base,
			tipologia,
			tassa_governativa,
			data_scadenza,
			cumulativa,
			anno_scolastico_riferimento)
		VALUES (
			{$tax_type['id_tipo_tassa']},
			'" . pg_escape_string(mb_convert_encoding($tax_type['descrizione'], "UTF-8", "HTML-ENTITIES")) . "',
			{$ib},
			'" . pg_escape_string($tax_type['tipologia']) . "',
			'{$tax_type['tassa_governativa']}',
			{$tax_type['data_scadenza']},
			{$tax_type['data_scadenza']},
			'" . pg_escape_string($tax_type['anno_scolastico_riferimento']) . "');";
    }
}

// Inserts Taxes
if ($taxes !== false) {
    foreach ($taxes as $tax) {
        $modPayment = !$tax['modalita_pagamento'] ? null : pg_escape_string($tax['modalita_pagamento']);
        $class = '';
        if ($tax['id_studente'] > 0) {
            $stt = pg_query($dbMc, "SELECT * FROM studenti_completi WHERE id_studente = '{$tax['id_studente']}'");
            $studentA = pg_fetch_all($stt);
            $tax['dati_debitore'] = ' ';
            $class = ' ';
            if ($studentA !== false) {
                $tax['dati_debitore'] = $studentA[0]['cognome'] . ' ' . $studentA[0]['nome'];
                $class = $studentA[0]['classe'] . $studentA[0]['sezione'];
            }
        }

        if ($tax['employee_id'] > 0) {
            $stt = pg_query($db, "SELECT * FROM employee WHERE employee_id = " . (int) $tax['employee_id']);
            $empA = pg_fetch_all($stt);
            $tax['dati_debitore'] = ' ';
            if ($empA !== false) {
                $tax['dati_debitore'] = $empA[0]['surname'] . ' ' . $empA[0]['name'];
            }
        }

        $iv = pg_escape_string($tax['importo_versato']);
        $iv = str_replace(',', '.', $iv);
        $iv = $iv == '' ? '0.00' : $iv;
        $iv = preg_match('/^([0-9]+[.]?[0-9]*|[.][0-9]+)$/', $iv) != 1 ? '0.00' : $iv;
        $iv = floatval($iv);

        $query .= "
		INSERT INTO tasse (
			id_tipo_tassa,
			id_studente,
			sede_ufficio_postale,
			importo_versato,
			numero_versamento,
			data_versamento,
			note,
			anno_scolastico,
			riferimento_estratto_conto,
			employee_id,
			data_estratto_conto,
			dati_debitore,
			is_incoming,
			numero_bollettino,
			tipologia_uscita,
			destinazione_pagamento,
			modalita_pagamento,
            classe)
		VALUES (
			{$tax['id_tipo_tassa']},
			{$tax['id_studente']},
			'" . pg_escape_string(mb_convert_encoding($tax['sede_ufficio_postale'], "UTF-8", "HTML-ENTITIES")) . "',
			{$iv},
			'" . pg_escape_string($tax['numero_versamento']) . "',
			{$tax['data_versamento']},
			'" . pg_escape_string(mb_convert_encoding($tax['note'], "UTF-8", "HTML-ENTITIES")) . "',
			'" . pg_escape_string($tax['anno_scolastico']) . "',
			'" . pg_escape_string($tax['riferimento_estratto_conto']) . "',
			{$tax['employee_id']},
			{$tax['data_estratto_conto']},
			'" . pg_escape_string(mb_convert_encoding($tax['dati_debitore'], "UTF-8", "HTML-ENTITIES")) . "',
			'" . pg_escape_string($tax['is_incoming']) . "',
			'" . pg_escape_string($tax['numero_bollettino']) . "',
			'" . pg_escape_string($tax['tipologia_uscita']) . "',
			'" . pg_escape_string($tax['destinazione_pagamento']) . "',
			" . ($modPayment === null ? 'NULL' : "'{$modPayment}'") . ",
            '{$class}');";
    }
}

$rtax = pg_query($dbMc, "SELECT * FROM tasse WHERE flag_canc = 0 and is_incoming = 't'");
$taxesIsIncoming = pg_fetch_all($rtax);

// Sets all taxes to incoming if importing data from a DB always used by MC and not MC2
if ($taxesIsIncoming === false && $rtax !== false) {

    $query .= "UPDATE tasse SET is_incoming = 't';";
}
$query .= "COMMIT;";

$res = pg_query($db, $query);
if ($res === false) {
    print "\n Error during data migration! ROLLBACK DONE! \n";
    exit(1);
}
