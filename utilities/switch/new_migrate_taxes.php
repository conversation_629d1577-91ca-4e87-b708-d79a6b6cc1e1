<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/funzioni_generiche.php';

do {
    echo "Inserire il periodo per il quale importare le tasse (dd/mm/yy - dd/mm/yy):\n";
    $periodoScelto = str_replace("\n", '', fgets(STDIN));
    $periodoOk = false;

    if ($periodoScelto != '') {
        $periodoOk = true;
    }
} while ($periodoOk === false);

if ($periodoScelto != '') {
    do {
        echo "Inserire se trattare le tasse come entrate (S/N):\n";
        $isIncoming = strtoupper(str_replace("\n", '', fgets(STDIN)));
    } while (!in_array($isIncoming, ['S', 'N']));

    if ($isIncoming == 'S') {
        $taxincoming = true;
    }

    $periodo = explode('-', $periodoScelto);
    $insertDateStart = $periodo[0];
    $insertDateEnd = $periodo[1];
    $start = mktime(1, 1, 1, explode('/', $periodo[0])[1], explode('/', $periodo[0])[0], explode('/', $periodo[0])[2]);
    $end = mktime(1, 1, 1, explode('/', $periodo[1])[1], explode('/', $periodo[1])[0], explode('/', $periodo[1])[2]);

    echo "Verranno sincornizzate su MC2 le sole tasse che rientrano nel periodo scelto: {$periodoScelto}\n\n";
    $inOut = $isIncoming == 'S' ?  "entrate" : "uscite";
    echo " e verranno trattate tutte come {$inOut}\n\n";
} else {
    echo "Nessun periodo inserito\n\n";
}

// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2018_2019' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);
$dbMc = pg_connect($connectionStringMc);

$categoryMc2 = pg_query($db, "SELECT name FROM ccp_category");
$mc2Categories = pg_fetch_all($categoryMc2);

$mc2CategoryName = $mcCategoryName = $insertCategory = $mc2TypesSorted = [];

foreach ($mc2Categories as $mc2Category) {
    if (!in_array($mc2Category['name'], $mc2CategoryName)) {
        $mc2CategoryName[] = $mc2Category['name'];
    }
}

$categoryMc = pg_query($dbMc, "SELECT DISTINCT initcap(lower(tipologia)) as name FROM tipi_tasse");
$mcCategories = pg_fetch_all($categoryMc);

foreach ($mcCategories as $mcCategory) {
    if (!in_array($mcCategory['name'], $mcCategoryName)) {
        $mcCategoryName[] = $mcCategory['name'];
    }
}

foreach ($mcCategoryName as $mcCategory) {
    if (!in_array($mcCategory, $mc2CategoryName)) {
        $insertCategory[] = $mcCategory;
    }
}

if (!empty($insertCategory)) {
    foreach ($insertCategory as $category) {
        $queryInsertCategory = pg_query($db, "INSERT INTO ccp_category (name) VALUES ('{$category}')");
    }
}

// tipi_tasse
$typeMc = pg_query($dbMc, "
                        SELECT
                            descrizione as type_name,
                            replace(importo_base, ',', '.') as amount,
                            tassa_governativa as governative,
                            data_scadenza as expiration_date,
                            cumulativa as cumulative,
                            anno_scolastico_riferimento as school_year,
                            initcap(lower(tipologia)) as category_id
                        FROM tipi_tasse
                        WHERE id_tipo_tassa IN (
                                                SELECT id_tipo_tassa
                                                FROM tasse
                                                WHERE data_inserimento >= {$start}
                                                    AND data_inserimento <= {$end}
                                                    AND flag_canc = 0
                                               )
                ");

$mcTypes = pg_fetch_all($typeMc);

$typeMc2 = pg_query($db, "
                        SELECT
                            ct.name as type_name,
                            amount,
                            governative,
                            expiration_date,
                            cumulative,
                            school_year,
                            cc.name as category_name
                        FROM ccp_type ct, ccp_category cc
                        WHERE cc.id = ct.category_id
                        ");
$mc2Types = pg_fetch_all($typeMc2);

foreach ($mc2Types as $mc2Type) {
    $mc2TypesSorted[$mc2Type['type_name'].'-'.$mc2Type['school_year']][] = $mc2Type;
}

foreach ($mcTypes as $mcType) {
    if (!array_key_exists(decode($mcType['type_name']).'-'.$mcType['school_year'], $mc2TypesSorted)) {
        $categoryMc2 = pg_query($db, "SELECT id FROM ccp_category WHERE name = '{$mcType['category_id']}'");
        $mc2Categories = pg_fetch_assoc($categoryMc2, 0);
        $mcType['type_name'] = pg_escape_string(html_entity_decode($mcType['type_name'], ENT_QUOTES));
        $mcType['amount'] = (int) $mcType['amount'];
        $queryInsertType = pg_query($db, "INSERT INTO ccp_type (name, amount, school_year, category_id)
                                          VALUES ('{$mcType['type_name']}', {$mcType['amount']}, '{$mcType['school_year']}', {$mc2Categories['id']})
                                   ");

        if((int)$mcType['amount'] > 0) {
            $querySelectMaxType = pg_query($db, "SELECT max(id) from ccp_type");
            $maxCcpType = pg_fetch_assoc($querySelectMaxType, 0);
            pg_query($db, "INSERT INTO ccp_type_step (ccp_type, value)
                                              VALUES ({$maxCcpType['max']}, {$mcType['amount']})
                                       ");

        }

    }
}

// tasse (movimenti + pagamenti)
$taxesMc = pg_query($dbMc, "
                        SELECT
                            t.*, replace(importo_versato, ',', '.') as tasse_importo,
                            tt.anno_scolastico_riferimento as tipo_tassa_as,
                            tt.descrizione as tipo_tassa_desc
                        FROM tasse t, tipi_tasse tt
                        WHERE t.data_inserimento >= {$start}
                            AND t.data_inserimento <= {$end}
                            AND t.id_tipo_tassa = tt.id_tipo_tassa
                            AND t.flag_canc = 0
                            AND t.importo_versato != ''
                            AND tt.id_tipo_tassa IN (
                                                SELECT id_tipo_tassa
                                                FROM tasse
                                                WHERE data_inserimento >= {$start}
                                                    AND data_inserimento <= {$end}
                                                    AND flag_canc = 0
                                               )
                ");

$mcTaxes = pg_fetch_all($taxesMc);

foreach ($mcTaxes as $mcTax) {
    $descTaxType = pg_escape_string(html_entity_decode($mcTax['tipo_tassa_desc'], ENT_QUOTES));

    if ($taxincoming) {
        $typeMc2 =  pg_query($db, "SELECT id FROM ccp_type WHERE name = '{$descTaxType}' AND school_year = '{$mcTax['tipo_tassa_as']}'");
        $idCcpTypeArr = pg_fetch_all($typeMc2);
        $idCcpType = $idCcpTypeArr[0];
    } elseif (!$taxincoming && $mcTax['is_incoming']) {
        $typeMc2 =  pg_query($db, "SELECT id FROM ccp_type WHERE name = '{$mcTax['tipo_tassa_desc']}' AND school_year = '{$mcTax['tipo_tassa_as']}'");
        $idCcpTypeArr = pg_fetch_all($typeMc2);
        $idCcpType = $idCcpTypeArr[0];
    } else {
        $typeMc =  pg_query($dbMc, "SELECT 'Prelievo - '||initcap(lower(tipologia)) as tipologia FROM tipi_tasse WHERE id_tipo_tassa = {$mcTax['id_tipo_tassa']}");
        $idTipoTassaArr = pg_fetch_all($typeMc);
        $idTipoTassa = $idTipoTassaArr[0];

        $typeMc2 =  pg_query($db, "SELECT id FROM ccp_type WHERE name = '{$idTipoTassa['tipologia']}'");
        $idCcpTypeArr = pg_fetch_all($typeMc2);
        $idCcpType = $idCcpTypeArr[0];
    }

    $ccpMovType = $mcTax['id_studente'] <> NULL ? 'S' : 'E';
    $ccpSeat = pg_query($db, "SELECT value as seat FROM parameter WHERE name = 'SEAT_ID'");
    $idCcpSeat = pg_fetch_assoc($ccpSeat, 0);

    if ($ccpMovType  == 'S' && $mcTax['id_studente'] > 0 and $mcTax['anno_scolastico']) {
        $historyDb = 'mastercom_'.str_replace('/', '_', $mcTax['anno_scolastico']);
        $connectionStringMcHystory = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='{$historyDb}' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
        $dbHistory = pg_connect($connectionStringMcHystory);

        $studentQuery = pg_query($dbHistory, "SELECT DISTINCT cognome||' '||nome as student, classe||sezione as class FROM studenti_completi WHERE ordinamento = '0' AND id_studente = {$mcTax['id_studente']}");
        $studentData = pg_fetch_all($studentQuery);

        if (!empty($studentData)) {
            $datiDebitore = encode($studentData[0]['student']);
            $datiClasse = encode($studentData[0]['class']);
        } else {
            $idStudenteNoCorrispondenza = $mcTax['id_studente'] . " - ". $mcTax['anno_scolastico'] . "\n";
            file_put_contents('/tmp/studenti_noid', print_r($idStudenteNoCorrispondenza, true), FILE_APPEND);
        }
    }

    if (empty($idCcpType)) {
        exit($descTaxType);
        file_put_contents('/tmp/noTipoTassa', $descTaxType, FILE_APPEND);
    }

    $sedeUfficioPostale = encode($mcTax['sede_ufficio_postale']);
    $note = encode($mcTax['note']);

    if ($mcTax['id_studente'] == 0) {
        $mcTax['id_studente'] = 0;
    }
    $mcTax['numero_versamento'] = (int) $mcTax['numero_versamento'];
    //01/01/1970 - 21/04/2016
    print("INSERT INTO ccp_movement (
                                 type_id,
                                 miscellaneous,
                                 number,
                                 note,
                                 school_year,
                                 subject_id,
                                 subject_data,
                                 subject_seat,
                                 subject_class,
                                 amount,
                                 creation_date,
                                 subject_type
                                )
                       VALUES (
                                {$idCcpType['id']},
                                '{$sedeUfficioPostale}',
                                '{$mcTax['numero_versamento']}',
                                '{$note}',
                                '{$mcTax['anno_scolastico']}',
                                {$mcTax['id_studente']},
                                '{$datiDebitore}',
                                {$idCcpSeat['seat']},
                                '{$datiClasse}',
                                {$mcTax['tasse_importo']},
                                {$mcTax['data_inserimento']},
                                '{$ccpMovType}'
                               )");
    $mc2InsertTaxes = pg_query($db,"
                       INSERT INTO ccp_movement (
                                                 type_id,
                                                 miscellaneous,
                                                 number,
                                                 note,
                                                 school_year,
                                                 subject_id,
                                                 subject_data,
                                                 subject_seat,
                                                 subject_class,
                                                 amount,
                                                 creation_date,
                                                 expiration_date,
                                                 subject_type
                                                )
                       VALUES (
                                {$idCcpType['id']},
                                '{$sedeUfficioPostale}',
                                '{$mcTax['numero_versamento']}',
                                '{$note}',
                                '{$mcTax['anno_scolastico']}',
                                {$mcTax['id_studente']},
                                '{$datiDebitore}',
                                {$idCcpSeat['seat']},
                                '{$datiClasse}',
                                {$mcTax['tasse_importo']},
                                {$mcTax['data_inserimento']},
                                {$mcTax['data_inserimento']},
                                '{$ccpMovType}'
                               )");

    $ccpMovementQuery = pg_query($db, "SELECT * FROM ccp_movement ORDER BY id DESC LIMIT 1");
    $ccpMovement = pg_fetch_assoc($ccpMovementQuery, 0);

    $accountIdQuery = pg_query($db, "SELECT id FROM core_bank_account WHERE denomination = 'Conto Corrente Postale'::text");
    $accountId = pg_fetch_assoc($accountIdQuery, 0);

    $paymentMethod = $mcTax['numero_bollettino'] != '' ? 8 : 1;

    $ccpPaymentInsert = pg_query($db,
                         "INSERT INTO ccp_payment (
                                                    movement_id,
                                                    operation_date,
                                                    accountable_date,
                                                    amount,
                                                    bollettino,
                                                    account_id,
                                                    account_reference,
                                                    payer_name,
                                                    payer_surname,
                                                    payer_type,
                                                    payer_id,
                                                    payment_method_id
                                                 )
                         VALUES (
                            {$ccpMovement['id']},
                            {$mcTax['data_versamento']},
                            {$mcTax['data_estratto_conto']},
                            {$ccpMovement['amount']},
                            '{$mcTax['numero_bollettino']}',
                            {$accountId['id']},
                            '{$mcTax['riferimento_estratto_conto']}',
                            '',
                            '{$ccpMovement['subject_data']}',
                            '{$ccpMovement['subject_type']}',
                            {$ccpMovement['subject_id']},
                            {$paymentMethod}
                         )");
}
