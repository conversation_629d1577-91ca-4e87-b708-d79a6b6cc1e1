<?php

/*
 * <PERSON><PERSON>t that switch MasterCom2 parameters
 *
 * Usage:
 * php switch.php [personnel ccp school_id seat_id]
 *
 * Personnel:
 * 0 - Personnel OFF
 * 1 - Personnel ON
 * 2 - Ignore Personnel
 *
 * CCP:
 * 0                       - CCP OFF
 * 1 [School_id] [Seat_id] - CCP ON (No Import)
 * 2 [School_id] [Seat_id] - CCP ON (Import from MC)
 * 3 [School_id] [Seat_id] - CCP ON (Import from MC2)
 * 4                       - Ignore CCP
 *
 * School_id:
 * Id of the row in schools.schools in mpcentral in Milan
 *
 * Seat_id:
 * Gathered from "Ceschionale"
 *
 */


require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';

// Checks Mode correctness
if ($argc !== 5 || ($argc === 5 && (($argv[1] !== '0' && $argv[1] !== '1' && $argv[1] !== '2') || ($argv[2] !== '0' && $argv[2] !== '1' && $argv[2] !== '2' && $argv[2] !== '3' && $argv[2] !== '4')))) {
    print "Usage: php switch.php [Personnel] [CCP] [School_id] [Seat_id] \n";
    print "  Personnel:\n";
    print "    0 - Personnel OFF \n";
    print "    1 - Personnel ON \n";
    print "    2 - Ignore Personnel \n";
    print "  CCP:\n";
    print "    0                       - CCP OFF \n";
    print "    1 [School_id] [Seat_id] - CCP ON (No Import) \n";
    print "    2 [School_id] [Seat_id] - CCP ON (Import from MC) \n";
    print "    3 [School_id] [Seat_id] - CCP ON (Import from MC2) \n";
    print "    4                       - Ignore CCP \n";
    exit(1);
}

if ($argc === 5) {
    $argv[1] = intval($argv[1]);
    $argv[2] = intval($argv[2]);
    $argv[3] = intval($argv[3]);
    $argv[4] = intval($argv[4]);
}

// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2015_2016' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);
$dbMc = pg_connect($connectionStringMc); 

print "\n SWITCHER SCRIPT STARTED \n";

print "\n Activities: \n";
switch ($argc) {
    case 5:
        switch ($argv[1]) {
            case 0:
                print "   Personnel parameter OFF \n";
                break;
            case 1:
                print "   Personnel parameter ON \n";
                break;
            case 2:
                print "   Ignore Personnel \n";
                break;
        }
        switch ($argv[2]) {
            case 0:
                print "   CCP parameter OFF \n";
                break;
            case 1:
                print "   CCP parameter ON \n";
                break;
            case 2:
                print "   CCP Migration (MC  -> new MC2), parameter ON \n";
                break;
            case 3:
                print "   CCP Migration (MC2 -> new MC2), parameter ON \n";
                break;
            case 4:
                print "   Ignore CCP \n";
                break;
        }
    default:
        break;
}

// Update logic
if ($argc == 5) {
    // Personnel activation
    if ($argv[1] === 0 || $argv[1] === 1) {
        $val = $argv[1] === 1 ? 'SI' : 'NO';
        print "\n START MC PERSONNEL PARAMETER SET \n";
        $paramQuery = "BEGIN; UPDATE parametri SET valore = '{$val}' WHERE nome = 'GESTIONE_PERSONALE_MC2'; COMMIT;";
        if (!pg_query($dbMc, $paramQuery)) {
            print "\n Error: MC Personnel parameter NOT set! \n";
            exit(1);
        }
        print "\n END MC PERSONNEL PARAMETER SET \n";
    }

    // CCP migration and activation
    if ($argv[2] === 0 || $argv[2] === 1 || $argv[2] === 2 || $argv[2] === 3) {
        // CCP migration
        if ($argv[2] === 2 || $argv[2] === 3) {
            print "\n START TAXES MIGRATION \n";
            include 'migrate_taxes.php';
            print "\n END TAXES MIGRATION \n";
        }

        // CCP MC2 parameters set
        if ($argv[2] === 1 || $argv[2] === 2 || $argv[2] === 3) {
            print "\n START MC2 CCP PARAMETERS SET \n";
            $paramQuery = "BEGIN;"
                    . "UPDATE parameter SET value = '{$argv[3]}' WHERE name = 'SCHOOL_ID';"
                    . "UPDATE parameter SET value = '{$argv[4]}' WHERE name = 'SEAT_ID';"
                    . "UPDATE tasse SET sede_studente = {$argv[4]};"
                    . "UPDATE tasse SET modalita_pagamento = 'bb';"
                    . "UPDATE tasse SET destinazione_pagamento = 'ccp_0';"
                    . "COMMIT;";
            if (!pg_query($db, $paramQuery)) {
                print "\n Error: MC2 CCP parameters NOT set! \n";
                exit(1);
            }
            print "\n END MC2 CCP PARAMETERS SET \n";
        }

        // CCP activation
        $val = ($argv[2] === 1 || $argv[2] === 2 || $argv[2] === 3) ? 'SI' : 'NO';
        print "\n START MC CCP PARAMETER SET \n";
        $paramQuery = "BEGIN; UPDATE parametri SET valore = '{$val}' WHERE nome = 'GESTIONE_TASSE_MC2'; COMMIT;";
        if (!pg_query($dbMc, $paramQuery)) {
            print "\n Error: MC CCP parameter NOT set! \n";
            exit(1);
        }
        print "\n END MC CCP PARAMETER SET \n";
    }
}

print "\n SWITCHER SCRIPT TERMINATED SUCCESSFULLY \n";
