<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/funzioni_generiche.php';

do {
    echo "Inserire il periodo per il quale importare le tasse (dd/mm/yy - dd/mm/yy):\n";
    $periodoScelto = str_replace("\n", '', fgets(STDIN));
    $periodoOk = false;

    if ($periodoScelto != '') {
        $periodoOk = true;
    }
} while ($periodoOk === false);

if ($periodoScelto != '') {
    do {
        echo "Inserire se trattare le tasse come entrate (S/N):\n";
        $isIncoming = strtoupper(str_replace("\n", '', fgets(STDIN)));
    } while (!in_array($isIncoming, ['S', 'N']));

    if ($isIncoming == 'S') {
        $taxincoming = true;
    }

    $periodo = explode('-', $periodoScelto);
    $insertDateStart = $periodo[0];
    $insertDateEnd = $periodo[1];
    $start = mktime(1, 1, 1, explode('/', $periodo[0])[1], explode('/', $periodo[0])[0], explode('/', $periodo[0])[2]);
    $end = mktime(1, 1, 1, explode('/', $periodo[1])[1], explode('/', $periodo[1])[0], explode('/', $periodo[1])[2]);

    echo "Verranno sincornizzate su MC2 le sole tasse che rientrano nel periodo scelto: {$periodoScelto}\n\n";
    $inOut = $isIncoming == 'S' ?  "entrate" : "uscite";
    echo " e verranno trattate tutte come {$inOut}\n\n";
} else {
    echo "Nessun periodo inserito\n\n";
}

// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2019_2020' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);
$dbMc = pg_connect($connectionStringMc);

$categoryMc2 = pg_query($db, "SELECT name FROM ccp_category");
$mc2Categories = pg_fetch_all($categoryMc2);

$mc2CategoryName = $mcCategoryName = $insertCategory = $mc2TypesSorted = [];

foreach ($mc2Categories as $mc2Category) {
    if (!in_array($mc2Category['name'], $mc2CategoryName)) {
        $mc2CategoryName[] = $mc2Category['name'];
    }
}

$categoryMc = pg_query($dbMc, "SELECT DISTINCT initcap(lower(tipologia)) as name FROM tipi_tasse");
$mcCategories = pg_fetch_all($categoryMc);

foreach ($mcCategories as $mcCategory) {
    if (!in_array($mcCategory['name'], $mcCategoryName)) {
        $mcCategoryName[] = $mcCategory['name'];
    }
}

foreach ($mcCategoryName as $mcCategory) {
    if (!in_array($mcCategory, $mc2CategoryName)) {
        $insertCategory[] = $mcCategory;
    }
}

if (!empty($insertCategory)) {
    foreach ($insertCategory as $category) {
        $queryInsertCategory = pg_query($db, "INSERT INTO ccp_category (name) VALUES ('{$category}')");
    }
}

// tasse (movimenti + pagamenti)
$taxesMc = pg_query($dbMc, "
                        SELECT *
                        FROM tasse
                        WHERE flag_canc = 0
                            AND id_tipo_tassa NOT IN (SELECT id_tipo_tassa FROM tipi_tasse)
                ");
$mcTaxes = pg_fetch_all($taxesMc);

foreach ($mcTaxes as $mcTax) {
    $descTaxType = pg_escape_string(decode($mcTax['tipo_tassa_desc']));

    $typeMc =  pg_query($dbMc, "SELECT 'Prelievo - '||initcap(lower(tipologia_uscita)) as tipologia FROM tasse WHERE id_tasse = {$mcTax['id_tasse']}");
    $idTipoTassaArr = pg_fetch_all($typeMc);
    $idTipoTassa = $idTipoTassaArr[0]['tipologia'];

    $typeMc2 =  pg_query($db, "SELECT id FROM ccp_type WHERE name = '{$idTipoTassa}'");
    $idCcpTypeArr = pg_fetch_all($typeMc2);
    $idCcpType = $idCcpTypeArr[0];

    $ccpSeat = pg_query($db, "SELECT value as seat FROM parameter WHERE name = 'SEAT_ID'");
    $idCcpSeat = pg_fetch_assoc($ccpSeat, 0);

    $sedeUfficioPostale = encode($mcTax['sede_ufficio_postale']);
    $note = encode($mcTax['note']);

    if ($mcTax['id_studente'] == 0) {
        $mcTax['id_studente'] = 0;
    }
    //01/01/1970 - 21/04/2016
    $mc2InsertTaxes = pg_query($db,"
                       INSERT INTO ccp_movement (
                                                 type_id,
                                                 miscellaneous,
                                                 number,
                                                 note,
                                                 school_year,
                                                 subject_id,
                                                 subject_data,
                                                 subject_seat,
                                                 subject_class,
                                                 amount,
                                                 creation_date,
                                                 subject_type
                                                )
                       VALUES (
                                {$idCcpType['id']},
                                '{$sedeUfficioPostale}',
                                '{$mcTax['numero_versamento']}',
                                '{$note}',
                                '{$mcTax['anno_scolastico']}',
                                {$mcTax['id_studente']},
                                '{$datiDebitore}',
                                {$idCcpSeat['seat']},
                                '{$datiClasse}',
                                {$mcTax['importo_versato']},
                                {$mcTax['data_inserimento']},
                                'O'
                               )");

    $ccpMovementQuery = pg_query($db, "SELECT * FROM ccp_movement ORDER BY id DESC LIMIT 1");
    $ccpMovement = pg_fetch_assoc($ccpMovementQuery, 0);

    $accountIdQuery = pg_query($db, "SELECT id FROM core_bank_account WHERE denomination = 'Conto Corrente Postale'::text");
    $accountId = pg_fetch_assoc($accountIdQuery, 0);

    $paymentMethod = $mcTax['numero_bollettino'] != '' ? 8 : 1;

    $ccpPaymentInsert = pg_query($db,
                         "INSERT INTO ccp_payment (
                                                    movement_id,
                                                    operation_date,
                                                    accountable_date,
                                                    amount,
                                                    bollettino,
                                                    account_id,
                                                    account_reference,
                                                    payer_name,
                                                    payer_surname,
                                                    payer_type,
                                                    payer_id,
                                                    payment_method_id
                                                 )
                         VALUES (
                            {$ccpMovement['id']},
                            {$mcTax['data_versamento']},
                            {$mcTax['data_estratto_conto']},
                            {$ccpMovement['amount']},
                            '{$mcTax['numero_bollettino']}',
                            {$accountId['id']},
                            '{$mcTax['riferimento_estratto_conto']}',
                            '',
                            '{$ccpMovement['subject_data']}',
                            '{$ccpMovement['subject_type']}',
                            {$ccpMovement['subject_id']},
                            {$paymentMethod}
                         )");
}
