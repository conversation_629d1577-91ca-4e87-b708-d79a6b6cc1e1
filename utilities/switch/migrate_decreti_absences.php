<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';

// Get db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$db = pg_connect($connectionString);
/**
 * The script link all presents decreti to a new absences that has new code.
 * So if ASSMAL had a decreto, the new code for that must have it.
 */
// Preparing array for copy old decreto on new absence kinds
$links = array(
    'ASSMAL'  => array('AA06', 'AN03', 'AN05', 'AN06', 'AA03', 'AN11', 'AA04'),
    'COSCOA'  => array('P015'),
    'PERCOE2' => array('PN06', 'PE01'),
    'PERLUT2' => array('PE02', 'PN08'),
    'ASTFMB1' => array('B019', 'B016'),
    'ASTFPP1' => array('B018'),
    'PERM3A'  => array('PN10', 'PE09'),
    'COSCOA'  => array('PN10', 'PE09'),
    'PERSTU'  => array('PN05', 'PE12'),
    'PERCOE'  => array('PN06', 'PE01'),
    'PERLUT'  => array('PE02', 'PN08'),
    'PERCOA'  => array('PN06'),
    'PERACC'  => array('PN12', 'PE05', 'PN14', 'PE17'),
    'PERMOP2' => array('PN07', 'PE03'),
    'PERMOP'  => array('PN07', 'PE03'),
);

$r = pg_query($db, "SELECT * FROM decreti");
$decreti = pg_fetch_all($r);

$query_decreti = '';
foreach ($decreti as $decreto) {
    $newAbsKinds = isset($links[$decreto['absence_kind']]) ? $links[$decreto['absence_kind']] : null;

    if ($newAbsKinds == null) {
        continue;
    }
    foreach ($newAbsKinds as $newAbsKind) {
        $query_decreti .= "INSERT INTO decreti (absence_kind,employee_kind,employee_role,html,time_back,prev_abs_kind)
				VALUES ('" . $newAbsKind . "',
						'" . $decreto['employee_kind'] . "',
						'" . $decreto['employee_role'] . "',
						'" . pg_escape_string($decreto['html']) . "',
						'" . $decreto['time_back'] . "',
						'" . $decreto['prev_abs_kind'] . "," . implode(',', $newAbsKinds) . "'
		);
		";
    }
}

print(str_replace(array("\n", "\t"), "", $query_decreti));
