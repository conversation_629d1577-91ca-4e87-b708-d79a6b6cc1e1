<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/funzioni_generiche.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/utenti.php';


// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2015_2016' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);

$select = pg_query($db,"SELECT id, subject_id, subject_data, subject_class, school_year FROM ccp_movement ORDER BY id");
$ccpTax = pg_fetch_all($select);

foreach ($ccpTax as $tax) {
    $historyDb = 'mastercom_'.str_replace('/', '_', $tax['school_year']);

    $connectionStringMcHystory = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='{$historyDb}' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
    $dbHistory = pg_connect($connectionStringMcHystory);

    $studentId = $tax['subject_id'];

    $studentQuery = pg_query($dbHistory, "SELECT DISTINCT cognome||' '||nome as student, classe||sezione as class FROM studenti_completi WHERE ordinamento = '0' AND id_studente = {$studentId}");
    $studentData = pg_fetch_all($studentQuery);

    if (!empty($studentData)) {
        $datiDebitore = formatta_nome(encode($studentData[0]['student']));
        $datiClasse = encode($studentData[0]['class']);
    } else {
        $idStudenteNoCorrispondenza = $studentId . " - ". $tax['school_year'] . "\n";
        file_put_contents('/tmp/studenti_noid', print_r($idStudenteNoCorrispondenza, true), FILE_APPEND);
    }

    if ($tax['subject_id'] == 0) {
        $tax['subject_id'] = 0;
    }

    if ($datiDebitore != '') {
        $stringUpdate = "UPDATE ccp_movement SET subject_data = {$datiDebitore}, subject_class = {$datiClasse} AND id = {$tax['id']}";
        file_put_contents('/tmp/stringUpdate', $tax['school_year'] . "-----------". $stringUpdate. "\n", FILE_APPEND);

        $update = pg_query($db,"UPDATE ccp_movement SET subject_data = '{$datiDebitore}', subject_class = '{$datiClasse}' WHERE id = {$tax['id']}");
    }
}


