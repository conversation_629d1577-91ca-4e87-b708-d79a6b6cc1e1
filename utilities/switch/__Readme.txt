--- SETUP Milano per tasse ---

<nome_scuola>                  => es.: <PERSON><PERSON>la
<sigla_provincia>              => es.: RE
<id_scuola_sito_genitori>      => da Ceschionale
<nome_sede>                    => es.: <PERSON><PERSON><PERSON> (se solo una sede), NomeSede (se con più sedi)
<comune_sede>                  => es.: Montecchio
<site_url>                     => vuoto
<hostname>                     => es.: scuola-re
<porta_postgres_sito_genitori> => check 'll /var/lib/postgresql/9.1/'


ssh root@mtm-db1 (*************)
su - postgres
psql -p 5001 mpcentral
INSERT INTO schools.schools (name, province_code) VALUES ('<nome_scuola>', '<sigla_provincia>');
INSERT INTO schools.seats (id, school, name, county, site_url) VALUES (<id_scuola_sito_genitori>, <schools.id>, '<nome_sede>', '<comune_sede>', '<site_url>');
INSERT INTO schools.servers (seat, hostname) VALUES (<id_scuola_sito_genitori>, '<hostname>');
INSERT INTO schools.databases (seat, host, port, username, password, dbname_mastercom) VALUES (<id_scuola_sito_genitori>, '*************', <porta_postgres_sito_genitori>, 'postgres', 'postgres', 'mastercom_2014_2015');