<?php

/*
 * Script generico per fixare eventuali problemi derivanti dall'importazione
 */



require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';


// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2019_2020' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);
$dbMc = pg_connect($connectionStringMc);

// Takes taxes from mastercom
$rt = pg_query($dbMc, "SELECT * FROM tasse WHERE flag_canc = 0");
$taxes = pg_fetch_all($rt);

// Takes taxes type from mastercom
$rtt = pg_query($dbMc, "SELECT * FROM tipi_tasse WHERE flag_canc = 0");
$taxes_type = pg_fetch_all($rtt);

$total_duplocated = 0;
foreach ($taxes as $tax){
    $val = (double)str_replace(',','.',trim($tax['importo_versato']));
    $mr = pg_query($db, ""
            . "SELECT * FROM ccp_movement WHERE number='{$tax['numero_versamento']}' "
            . "AND school_year='{$tax['anno_scolastico']}' AND subject_id={$tax['id_studente']} AND amount={$val} AND type_id<5"
    );
    $movements = pg_fetch_all($mr);

    if (count($movements) > 1){
        var_dump($movements);
        $total_duplocated += count($movements);
        continue;
    }

    if (count($movements) == 0){
        print("Nessuna tassa trovate\n");
        print_r($tax);
    }

    if((int)$tax['id_tipo_tassa'] && (int)$tax['id_studente']){

        $update = pg_query($db, ""
                . "UPDATE ccp_movement SET type_id={$tax['id_tipo_tassa']} WHERE number='{$tax['numero_versamento']}' "
                . "AND school_year='{$tax['anno_scolastico']}' AND subject_id={$tax['id_studente']} AND amount={$val} AND type_id<5"
        );

        if($update === false){
            exit("Update failed on movement {$tax['numero_versamento']}");
        } else {
            print('Aggiornato da '.$movements[0]['type_id'].' a '.$tax['id_tipo_tassa']."\n");
        }
    }

}

print('Total duplicated: '. $total_duplocated);

