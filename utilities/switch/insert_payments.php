<?php

require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';
require_once '/var/www-source/mastercom/class/NEXUS_MASTERCOM_FUNCTIONS/funzioni_generiche.php';

do {
    echo "Inserire il periodo per il quale importare le tasse (dd/mm/yy - dd/mm/yy):\n";
    $periodoScelto = str_replace("\n", '', fgets(STDIN));
    $periodoOk = false;

    if ($periodoScelto != '') {
        $periodoOk = true;
    }
} while ($periodoOk === false);

if ($periodoScelto != '') {
    $periodo = explode('-', $periodoScelto);
    $insertDateStart = $periodo[0];
    $insertDateEnd = $periodo[1];
    $start = mktime(1, 1, 1, explode('/', $periodo[0])[1], explode('/', $periodo[0])[0], explode('/', $periodo[0])[2]);
    $end = mktime(1, 1, 1, explode('/', $periodo[1])[1], explode('/', $periodo[1])[0], explode('/', $periodo[1])[2]);

    echo "Verranno sincornizzate su MC2 le sole tasse che rientrano nel periodo scelto: {$periodoScelto}\n\n";
} else {
    echo "Nessun periodo inserito\n\n";
}

// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2015_2016' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);
$dbMc = pg_connect($connectionStringMc);

// tasse (movimenti + pagamenti)
$ccpMovementQuery = pg_query($db, "SELECT * FROM ccp_movement WHERE creation_date >= '{$start}' AND creation_date <= '{$end}'");
$ccpMovementList = pg_fetch_all($ccpMovementQuery);

$accountIdQuery = pg_query($db, "SELECT id FROM core_bank_account WHERE denomination = 'Conto Corrente Postale'::text");
$accountId = pg_fetch_assoc($accountIdQuery, 0);

//$paymentMethod = $mcTax['numero_bollettino'] != '' ? 8 : 1;
foreach ($ccpMovementList as $ccpMovement) {
    $ccpPaymentQuery = pg_query($db, "SELECT * FROM ccp_payment WHERE movement_id = {$ccpMovement['id']}");
    $ccpPayment = pg_fetch_row($ccpPaymentQuery);

    if ($ccpPayment < 1) {
        if ($ccpMovement['subject_type'] == 'S') {
            $historyDb = 'mastercom_' . str_replace('/', '_', $ccpMovement['school_year']);
            $connectionStringMcHystory = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='{$historyDb}' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
            $dbHistory = pg_connect($connectionStringMcHystory);

            $studentQuery = pg_query($dbHistory, "SELECT DISTINCT cognome, nome FROM studenti_completi WHERE id_studente = {$ccpMovement['subject_id']}");
            $studentData = pg_fetch_all($studentQuery);

            foreach ($studentData as $data) {
                $payerSurname = encode($data['cognome']);
                $payerName = encode($data['nome']);
            }
        } else {
            $payerName = encode($ccpMovement['subject_data']);
        }

        $ccpPaymentInsert = pg_query($db, "INSERT INTO ccp_payment (
                                                    movement_id,
                                                    operation_date,
                                                    accountable_date,
                                                    amount,
                                                    account_id,
                                                    payer_name,
                                                    payer_surname,
                                                    payer_type,
                                                    payer_id,
                                                    payment_method_id
                                                 )
                         VALUES (
                            {$ccpMovement['id']},
                            {$ccpMovement['creation_date']},
                            {$ccpMovement['creation_date']},
                            {$ccpMovement['amount']},
                            {$accountId['id']},
                            '{$payerSurname}',
                            '{$payerName}',
                            '{$ccpMovement['subject_type']}',
                            {$ccpMovement['subject_id']},
                            8
                         )");
    }
}
