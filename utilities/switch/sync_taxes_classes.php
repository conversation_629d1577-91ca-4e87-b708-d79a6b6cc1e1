<?php

/*
 * Script generico per fixare eventuali problemi derivanti dall'importazione
 */



require_once '../../configurations/init-vars.php';
require_once '../../configurations/init-autoload.php';
require_once '../../configurations/init-db.php';


// Gets db instance
$connectionString = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='" . DB_NAME . "' user='" . DB_USER . "' password='" . DB_PASSWD . "'";
$connectionStringMc = "host='" . DB_HOST . "' port=" . DB_PORT . " dbname='mastercom_2015_2016' user='" . DB_USER . "' password='" . DB_PASSWD . "'";

// DB connections
$db = pg_connect($connectionString);
$dbMc = pg_connect($connectionStringMc);

// Takes taxes from mastercom
$rt = pg_query($db, "SELECT * FROM ccp_movement where creation_date >=1420066800 and school_year='2015/2016' and subject_type= 'S'");
$movements = pg_fetch_all($rt);
print(count($movements));

foreach ($movements as $movement) {
    $rtt = pg_query($dbMc, "SELECT classe||''||sezione AS classe from studenti_completi where ordinamento = '0' and  id_studente = {$movement['subject_id']}");
    $students = pg_fetch_all($rtt);

    if($students === false){
        print("Studente non trovato\n");
        continue;
    }
    $class = $students[0]['classe'];

    $success = pg_query($db, "UPDATE ccp_movement SET subject_class='{$class}' where id={$movement['id']}");
    if($success === FALSE){
        print("Errore di update\n");
    } else {
        print("Trovato studente. Classe nuova: {$class}\n");
    }
}
