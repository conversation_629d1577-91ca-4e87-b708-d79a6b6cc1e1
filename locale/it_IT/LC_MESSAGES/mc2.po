# Mastercom 2 API Translation Back-End.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the Mastercom2 package.
# <AUTHOR> <EMAIL>, 2012.
# <AUTHOR> <EMAIL>, 2012.
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Mastercom 2 API\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2012-11-19 10:56+0100\n"
"PO-Revision-Date: 2012-11-19 11:01+0100\n"
"Last-Translator: Mastercom 2 <<EMAIL>>\n"
"Language-Team: ITALIAN <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../applications/core/classes/Db.php:37
#: ../applications/core/classes/Db.php:52
msgid "Database Error Connection"
msgstr "Errore Connessione Database"

#: ../applications/core/classes/Db.php:48
#: ../applications/core/classes/Db.php:76
#: ../applications/core/classes/Db.php:103
msgid "Database Error Query"
msgstr "Errore Query Database"

#: ../applications/core/classes/Session.php:34
#: ../applications/core/utils.php:14
#: ../applications/core/classes/Session.php:187
msgid "Session Expired"
msgstr "Sessione Scaduta"

#: ../applications/employees/extraordinary/write.php:31
msgid "Error on month store"
msgstr "Errore durante il bloccaggio del mese"

#: ../applications/employees/extraordinary/write.php:36
msgid "Error on month deletion"
msgstr "Errore durante la cancellazione del mese"

#: ../applications/core/classes/DbObject.php:52
msgid "Object not found in table"
msgstr "Oggetto non trovato nella tabella"

msgid "Sun"
msgstr "Dom"

msgid "Mon"
msgstr "Lun"

msgid "Tue"
msgstr "Mar"

msgid "Wed"
msgstr "Mer"

msgid "Thu"
msgstr "Gio"

msgid "Fri"
msgstr "Ven"

msgid "Sat"
msgstr "Sab"

msgid "Monday"
msgstr "Lunedì"

msgid "Tuesday"
msgstr "Martedì"

msgid "Wednesday"
msgstr "Mercoledì"

msgid "Thursday"
msgstr "Giovedì"

msgid "Friday"
msgstr "Venerdì"

msgid "Saturday"
msgstr "Sabato"

msgid "Sunday"
msgstr "Domenica"

msgid "Jan"
msgstr "Gen"

msgid "Feb"
msgstr "Feb"

msgid "Mar"
msgstr "Mar"

msgid "Apr"
msgstr "Apr"

msgid "May."
msgstr "Mag"

msgid "Jun"
msgstr "Giu"

msgid "Jul"
msgstr "Lug"

msgid "Aug"
msgstr "Ago"

msgid "Sep"
msgstr "Set"

msgid "Oct"
msgstr "Ott"

msgid "Nov"
msgstr "Nov"

msgid "Dec"
msgstr "Dic"

msgid "January"
msgstr "Gennaio"

msgid "February"
msgstr "Febbraio"

msgid "March"
msgstr "Marzo"

msgid "April"
msgstr "Aprile"

msgid "May"
msgstr "Maggio"

msgid "June"
msgstr "Giugno"

msgid "July"
msgstr "Luglio"

msgid "August"
msgstr "Agosto"

msgid "September"
msgstr "Settembre"

msgid "October"
msgstr "Ottobre"

msgid "November"
msgstr "Novembre"

msgid "December"
msgstr "Dicembre"

msgid "Page"
msgstr "Pagina"

msgid "pages"
msgstr "pagine"

msgid "of"
msgstr "di"

#: ../applications/ccp/taxes/destroy.php:34
msgid "Error during tax deletion"
msgstr "Errore durante la cancellazione della tassa"

#: ../applications/ccp/taxes_type/read.php:56
msgid "All"
msgstr "Tutti"

#: ../classes/Ccp/map/TipiTasseTableMap.php:87
#: ../PROPEL/build/classes/Ccp/map/TipiTasseTableMap.php:87
msgid "Description is mandatory."
msgstr "La descrizione è obbligatoria"

#: ../classes/Ccp/map/TipiTasseTableMap.php:88
#: ../classes/Ccp/map/TasseTableMap.php:71
#: ../PROPEL/build/classes/Ccp/map/TipiTasseTableMap.php:88
#: ../PROPEL/build/classes/Ccp/map/TasseTableMap.php:71
msgid "Amount must be positive"
msgstr "L'importo deve essere positivo"

#: ../classes/Ccp/map/TasseTableMap.php:70
#: ../PROPEL/build/classes/Ccp/map/TasseTableMap.php:70
msgid "Transaction number is mandatory"
msgstr "Il numero di transazione è obbligatorio"

#: ../classes/Ccp/Tasse.php:29
msgid "Post office is mandatory"
msgstr "L'ufficio postale è obbligatorio"

#: ../classes/Ccp/Tasse.php:37
msgid "Subject is mandatory"
msgstr "Il soggetto è obbligatorio"

#: ../classes/Ccp/Tasse.php:59
msgid "Data estratto conto not in school year range"
msgstr "La data estratto conto non è nell'anno scolastico"

#: ../classes/Ccp/Tasse.php:44
msgid "Tax type is mandatory"
msgstr "Il tipo di tassa è obbligatorio"

#: ../classes/Ccp/Tasse.php:50
msgid "School year is mandatory"
msgstr "L'anno scolastico è obbligatorio"
