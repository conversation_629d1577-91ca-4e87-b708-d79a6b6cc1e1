<?php

namespace Budget;

use Budget\om\BaseActivityPeer;


/**
 * Skeleton subclass for performing query and update operations on the 'bdg_activities' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Budget
 */
class ActivityPeer extends BaseActivityPeer
{
}
