<?php

namespace Budget\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'bdg_activities' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Budget.map
 */
class ActivityTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Budget.map.ActivityTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('bdg_activities');
        $this->setPhpName('Activity');
        $this->setClassname('Budget\\Activity');
        $this->setPackage('Budget');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('bdg_activities_activ_id_seq');
        // columns
        $this->addPrimaryKey('activ_id', 'ActivityId', 'INTEGER', true, null, null);
        $this->addColumn('aggreg_code', 'AggregateCode', 'CHAR', true, 1, null);
        $this->addColumn('aggreg_nr', 'AggregateNumber', 'SMALLINT', true, null, null);
        $this->addColumn('description', 'Description', 'VARCHAR', true, 200, null);
        $this->addColumn('ext_desc', 'DescriptionExtended', 'LONGVARCHAR', false, null, null);
        $this->addColumn('start_date', 'DateStart', 'BIGINT', false, null, null);
        $this->addColumn('end_date', 'DateEnd', 'BIGINT', false, null, null);
        $this->addColumn('suspend', 'Suspend', 'BOOLEAN', false, null, false);
        $this->addColumn('avm', 'AVM', 'BOOLEAN', false, null, false);
        $this->addColumn('notsdate', 'NotDateStart', 'BOOLEAN', false, null, false);
        $this->addColumn('notedate', 'NotDateEnd', 'BOOLEAN', false, null, false);
        $this->addColumn('responsibles', 'Responsibles', 'LONGVARCHAR', false, null, null);
        $this->addColumn('objectives', 'Objectives', 'LONGVARCHAR', false, null, null);
        $this->addColumn('human_resources', 'HumanResources', 'LONGVARCHAR', false, null, null);
        $this->addColumn('goods_services', 'GoodsServices', 'LONGVARCHAR', false, null, null);
        $this->addColumn('durata', 'Duration', 'LONGVARCHAR', false, null, null);
        $this->addColumn('budget_year', 'BudgetYear', 'INTEGER', true, null, 2008);
        $this->addColumn('residui', 'Residuals', 'DOUBLE', true, null, 0);
        $this->addColumn('hours_insertions_end_date', 'HoursInsertionsEndDate', 'BIGINT', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('Presence', 'Employee\\Presence', RelationMap::ONE_TO_MANY, array('activ_id' => 'project_edit_id', ), 'SET NULL', null, 'Presences');
    } // buildRelations()

} // ActivityTableMap
