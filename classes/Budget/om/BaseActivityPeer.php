<?php

namespace Budget\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Budget\Activity;
use Budget\ActivityPeer;
use Budget\map\ActivityTableMap;
use Employee\PresencePeer;

/**
 * Base static class for performing query and update operations on the 'bdg_activities' table.
 *
 *
 *
 * @package propel.generator.Budget.om
 */
abstract class BaseActivityPeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'bdg_activities';

    /** the related Propel class for this table */
    const OM_CLASS = 'Budget\\Activity';

    /** the related TableMap class for this table */
    const TM_CLASS = 'ActivityTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 19;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 19;

    /** the column name for the activ_id field */
    const ACTIV_ID = 'bdg_activities.activ_id';

    /** the column name for the aggreg_code field */
    const AGGREG_CODE = 'bdg_activities.aggreg_code';

    /** the column name for the aggreg_nr field */
    const AGGREG_NR = 'bdg_activities.aggreg_nr';

    /** the column name for the description field */
    const DESCRIPTION = 'bdg_activities.description';

    /** the column name for the ext_desc field */
    const EXT_DESC = 'bdg_activities.ext_desc';

    /** the column name for the start_date field */
    const START_DATE = 'bdg_activities.start_date';

    /** the column name for the end_date field */
    const END_DATE = 'bdg_activities.end_date';

    /** the column name for the suspend field */
    const SUSPEND = 'bdg_activities.suspend';

    /** the column name for the avm field */
    const AVM = 'bdg_activities.avm';

    /** the column name for the notsdate field */
    const NOTSDATE = 'bdg_activities.notsdate';

    /** the column name for the notedate field */
    const NOTEDATE = 'bdg_activities.notedate';

    /** the column name for the responsibles field */
    const RESPONSIBLES = 'bdg_activities.responsibles';

    /** the column name for the objectives field */
    const OBJECTIVES = 'bdg_activities.objectives';

    /** the column name for the human_resources field */
    const HUMAN_RESOURCES = 'bdg_activities.human_resources';

    /** the column name for the goods_services field */
    const GOODS_SERVICES = 'bdg_activities.goods_services';

    /** the column name for the durata field */
    const DURATA = 'bdg_activities.durata';

    /** the column name for the budget_year field */
    const BUDGET_YEAR = 'bdg_activities.budget_year';

    /** the column name for the residui field */
    const RESIDUI = 'bdg_activities.residui';

    /** the column name for the hours_insertions_end_date field */
    const HOURS_INSERTIONS_END_DATE = 'bdg_activities.hours_insertions_end_date';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of Activity objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array Activity[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. ActivityPeer::$fieldNames[ActivityPeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('ActivityId', 'AggregateCode', 'AggregateNumber', 'Description', 'DescriptionExtended', 'DateStart', 'DateEnd', 'Suspend', 'AVM', 'NotDateStart', 'NotDateEnd', 'Responsibles', 'Objectives', 'HumanResources', 'GoodsServices', 'Duration', 'BudgetYear', 'Residuals', 'HoursInsertionsEndDate', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('activityId', 'aggregateCode', 'aggregateNumber', 'description', 'descriptionExtended', 'dateStart', 'dateEnd', 'suspend', 'aVM', 'notDateStart', 'notDateEnd', 'responsibles', 'objectives', 'humanResources', 'goodsServices', 'duration', 'budgetYear', 'residuals', 'hoursInsertionsEndDate', ),
        BasePeer::TYPE_COLNAME => array (ActivityPeer::ACTIV_ID, ActivityPeer::AGGREG_CODE, ActivityPeer::AGGREG_NR, ActivityPeer::DESCRIPTION, ActivityPeer::EXT_DESC, ActivityPeer::START_DATE, ActivityPeer::END_DATE, ActivityPeer::SUSPEND, ActivityPeer::AVM, ActivityPeer::NOTSDATE, ActivityPeer::NOTEDATE, ActivityPeer::RESPONSIBLES, ActivityPeer::OBJECTIVES, ActivityPeer::HUMAN_RESOURCES, ActivityPeer::GOODS_SERVICES, ActivityPeer::DURATA, ActivityPeer::BUDGET_YEAR, ActivityPeer::RESIDUI, ActivityPeer::HOURS_INSERTIONS_END_DATE, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ACTIV_ID', 'AGGREG_CODE', 'AGGREG_NR', 'DESCRIPTION', 'EXT_DESC', 'START_DATE', 'END_DATE', 'SUSPEND', 'AVM', 'NOTSDATE', 'NOTEDATE', 'RESPONSIBLES', 'OBJECTIVES', 'HUMAN_RESOURCES', 'GOODS_SERVICES', 'DURATA', 'BUDGET_YEAR', 'RESIDUI', 'HOURS_INSERTIONS_END_DATE', ),
        BasePeer::TYPE_FIELDNAME => array ('activ_id', 'aggreg_code', 'aggreg_nr', 'description', 'ext_desc', 'start_date', 'end_date', 'suspend', 'avm', 'notsdate', 'notedate', 'responsibles', 'objectives', 'human_resources', 'goods_services', 'durata', 'budget_year', 'residui', 'hours_insertions_end_date', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. ActivityPeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('ActivityId' => 0, 'AggregateCode' => 1, 'AggregateNumber' => 2, 'Description' => 3, 'DescriptionExtended' => 4, 'DateStart' => 5, 'DateEnd' => 6, 'Suspend' => 7, 'AVM' => 8, 'NotDateStart' => 9, 'NotDateEnd' => 10, 'Responsibles' => 11, 'Objectives' => 12, 'HumanResources' => 13, 'GoodsServices' => 14, 'Duration' => 15, 'BudgetYear' => 16, 'Residuals' => 17, 'HoursInsertionsEndDate' => 18, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('activityId' => 0, 'aggregateCode' => 1, 'aggregateNumber' => 2, 'description' => 3, 'descriptionExtended' => 4, 'dateStart' => 5, 'dateEnd' => 6, 'suspend' => 7, 'aVM' => 8, 'notDateStart' => 9, 'notDateEnd' => 10, 'responsibles' => 11, 'objectives' => 12, 'humanResources' => 13, 'goodsServices' => 14, 'duration' => 15, 'budgetYear' => 16, 'residuals' => 17, 'hoursInsertionsEndDate' => 18, ),
        BasePeer::TYPE_COLNAME => array (ActivityPeer::ACTIV_ID => 0, ActivityPeer::AGGREG_CODE => 1, ActivityPeer::AGGREG_NR => 2, ActivityPeer::DESCRIPTION => 3, ActivityPeer::EXT_DESC => 4, ActivityPeer::START_DATE => 5, ActivityPeer::END_DATE => 6, ActivityPeer::SUSPEND => 7, ActivityPeer::AVM => 8, ActivityPeer::NOTSDATE => 9, ActivityPeer::NOTEDATE => 10, ActivityPeer::RESPONSIBLES => 11, ActivityPeer::OBJECTIVES => 12, ActivityPeer::HUMAN_RESOURCES => 13, ActivityPeer::GOODS_SERVICES => 14, ActivityPeer::DURATA => 15, ActivityPeer::BUDGET_YEAR => 16, ActivityPeer::RESIDUI => 17, ActivityPeer::HOURS_INSERTIONS_END_DATE => 18, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ACTIV_ID' => 0, 'AGGREG_CODE' => 1, 'AGGREG_NR' => 2, 'DESCRIPTION' => 3, 'EXT_DESC' => 4, 'START_DATE' => 5, 'END_DATE' => 6, 'SUSPEND' => 7, 'AVM' => 8, 'NOTSDATE' => 9, 'NOTEDATE' => 10, 'RESPONSIBLES' => 11, 'OBJECTIVES' => 12, 'HUMAN_RESOURCES' => 13, 'GOODS_SERVICES' => 14, 'DURATA' => 15, 'BUDGET_YEAR' => 16, 'RESIDUI' => 17, 'HOURS_INSERTIONS_END_DATE' => 18, ),
        BasePeer::TYPE_FIELDNAME => array ('activ_id' => 0, 'aggreg_code' => 1, 'aggreg_nr' => 2, 'description' => 3, 'ext_desc' => 4, 'start_date' => 5, 'end_date' => 6, 'suspend' => 7, 'avm' => 8, 'notsdate' => 9, 'notedate' => 10, 'responsibles' => 11, 'objectives' => 12, 'human_resources' => 13, 'goods_services' => 14, 'durata' => 15, 'budget_year' => 16, 'residui' => 17, 'hours_insertions_end_date' => 18, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = ActivityPeer::getFieldNames($toType);
        $key = isset(ActivityPeer::$fieldKeys[$fromType][$name]) ? ActivityPeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(ActivityPeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, ActivityPeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return ActivityPeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. ActivityPeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(ActivityPeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(ActivityPeer::ACTIV_ID);
            $criteria->addSelectColumn(ActivityPeer::AGGREG_CODE);
            $criteria->addSelectColumn(ActivityPeer::AGGREG_NR);
            $criteria->addSelectColumn(ActivityPeer::DESCRIPTION);
            $criteria->addSelectColumn(ActivityPeer::EXT_DESC);
            $criteria->addSelectColumn(ActivityPeer::START_DATE);
            $criteria->addSelectColumn(ActivityPeer::END_DATE);
            $criteria->addSelectColumn(ActivityPeer::SUSPEND);
            $criteria->addSelectColumn(ActivityPeer::AVM);
            $criteria->addSelectColumn(ActivityPeer::NOTSDATE);
            $criteria->addSelectColumn(ActivityPeer::NOTEDATE);
            $criteria->addSelectColumn(ActivityPeer::RESPONSIBLES);
            $criteria->addSelectColumn(ActivityPeer::OBJECTIVES);
            $criteria->addSelectColumn(ActivityPeer::HUMAN_RESOURCES);
            $criteria->addSelectColumn(ActivityPeer::GOODS_SERVICES);
            $criteria->addSelectColumn(ActivityPeer::DURATA);
            $criteria->addSelectColumn(ActivityPeer::BUDGET_YEAR);
            $criteria->addSelectColumn(ActivityPeer::RESIDUI);
            $criteria->addSelectColumn(ActivityPeer::HOURS_INSERTIONS_END_DATE);
        } else {
            $criteria->addSelectColumn($alias . '.activ_id');
            $criteria->addSelectColumn($alias . '.aggreg_code');
            $criteria->addSelectColumn($alias . '.aggreg_nr');
            $criteria->addSelectColumn($alias . '.description');
            $criteria->addSelectColumn($alias . '.ext_desc');
            $criteria->addSelectColumn($alias . '.start_date');
            $criteria->addSelectColumn($alias . '.end_date');
            $criteria->addSelectColumn($alias . '.suspend');
            $criteria->addSelectColumn($alias . '.avm');
            $criteria->addSelectColumn($alias . '.notsdate');
            $criteria->addSelectColumn($alias . '.notedate');
            $criteria->addSelectColumn($alias . '.responsibles');
            $criteria->addSelectColumn($alias . '.objectives');
            $criteria->addSelectColumn($alias . '.human_resources');
            $criteria->addSelectColumn($alias . '.goods_services');
            $criteria->addSelectColumn($alias . '.durata');
            $criteria->addSelectColumn($alias . '.budget_year');
            $criteria->addSelectColumn($alias . '.residui');
            $criteria->addSelectColumn($alias . '.hours_insertions_end_date');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(ActivityPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            ActivityPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(ActivityPeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return Activity
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = ActivityPeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return ActivityPeer::populateObjects(ActivityPeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            ActivityPeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(ActivityPeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param Activity $obj A Activity object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getActivityId();
            } // if key === null
            ActivityPeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A Activity object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof Activity) {
                $key = (string) $value->getActivityId();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or Activity object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(ActivityPeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return Activity Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(ActivityPeer::$instances[$key])) {
                return ActivityPeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (ActivityPeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        ActivityPeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to bdg_activities
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
        // Invalidate objects in PresencePeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        PresencePeer::clearInstancePool();
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = ActivityPeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = ActivityPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = ActivityPeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                ActivityPeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (Activity object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = ActivityPeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = ActivityPeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + ActivityPeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = ActivityPeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            ActivityPeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(ActivityPeer::DATABASE_NAME)->getTable(ActivityPeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseActivityPeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseActivityPeer::TABLE_NAME)) {
        $dbMap->addTableObject(new ActivityTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return ActivityPeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a Activity or Criteria object.
     *
     * @param      mixed $values Criteria or Activity object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from Activity object
        }

        if ($criteria->containsKey(ActivityPeer::ACTIV_ID) && $criteria->keyContainsValue(ActivityPeer::ACTIV_ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.ActivityPeer::ACTIV_ID.')');
        }


        // Set the correct dbName
        $criteria->setDbName(ActivityPeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a Activity or Criteria object.
     *
     * @param      mixed $values Criteria or Activity object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(ActivityPeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(ActivityPeer::ACTIV_ID);
            $value = $criteria->remove(ActivityPeer::ACTIV_ID);
            if ($value) {
                $selectCriteria->add(ActivityPeer::ACTIV_ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(ActivityPeer::TABLE_NAME);
            }

        } else { // $values is Activity object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(ActivityPeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the bdg_activities table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(ActivityPeer::TABLE_NAME, $con, ActivityPeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            ActivityPeer::clearInstancePool();
            ActivityPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a Activity or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or Activity object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            ActivityPeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof Activity) { // it's a model object
            // invalidate the cache for this single object
            ActivityPeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(ActivityPeer::DATABASE_NAME);
            $criteria->add(ActivityPeer::ACTIV_ID, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                ActivityPeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(ActivityPeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            ActivityPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given Activity object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param Activity $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(ActivityPeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(ActivityPeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(ActivityPeer::DATABASE_NAME, ActivityPeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return Activity
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = ActivityPeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(ActivityPeer::DATABASE_NAME);
        $criteria->add(ActivityPeer::ACTIV_ID, $pk);

        $v = ActivityPeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return Activity[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(ActivityPeer::DATABASE_NAME);
            $criteria->add(ActivityPeer::ACTIV_ID, $pks, Criteria::IN);
            $objs = ActivityPeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseActivityPeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseActivityPeer::buildTableMap();

