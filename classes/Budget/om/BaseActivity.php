<?php

namespace Budget\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Budget\Activity;
use Budget\ActivityPeer;
use Budget\ActivityQuery;
use Employee\Presence;
use Employee\PresenceQuery;

/**
 * Base class that represents a row from the 'bdg_activities' table.
 *
 *
 *
 * @package    propel.generator.Budget.om
 */
abstract class BaseActivity extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Budget\\ActivityPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        ActivityPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the activ_id field.
     * @var        int
     */
    protected $activ_id;

    /**
     * The value for the aggreg_code field.
     * @var        string
     */
    protected $aggreg_code;

    /**
     * The value for the aggreg_nr field.
     * @var        int
     */
    protected $aggreg_nr;

    /**
     * The value for the description field.
     * @var        string
     */
    protected $description;

    /**
     * The value for the ext_desc field.
     * @var        string
     */
    protected $ext_desc;

    /**
     * The value for the start_date field.
     * @var        string
     */
    protected $start_date;

    /**
     * The value for the end_date field.
     * @var        string
     */
    protected $end_date;

    /**
     * The value for the suspend field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $suspend;

    /**
     * The value for the avm field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $avm;

    /**
     * The value for the notsdate field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $notsdate;

    /**
     * The value for the notedate field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $notedate;

    /**
     * The value for the responsibles field.
     * @var        string
     */
    protected $responsibles;

    /**
     * The value for the objectives field.
     * @var        string
     */
    protected $objectives;

    /**
     * The value for the human_resources field.
     * @var        string
     */
    protected $human_resources;

    /**
     * The value for the goods_services field.
     * @var        string
     */
    protected $goods_services;

    /**
     * The value for the durata field.
     * @var        string
     */
    protected $durata;

    /**
     * The value for the budget_year field.
     * Note: this column has a database default value of: 2008
     * @var        int
     */
    protected $budget_year;

    /**
     * The value for the residui field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $residui;

    /**
     * The value for the hours_insertions_end_date field.
     * @var        string
     */
    protected $hours_insertions_end_date;

    /**
     * @var        PropelObjectCollection|Presence[] Collection to store aggregation of Presence objects.
     */
    protected $collPresences;
    protected $collPresencesPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $presencesScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->suspend = false;
        $this->avm = false;
        $this->notsdate = false;
        $this->notedate = false;
        $this->budget_year = 2008;
        $this->residui = 0;
    }

    /**
     * Initializes internal state of BaseActivity object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [activ_id] column value.
     *
     * @return int
     */
    public function getActivityId()
    {

        return $this->activ_id;
    }

    /**
     * Get the [aggreg_code] column value.
     *
     * @return string
     */
    public function getAggregateCode()
    {

        return $this->aggreg_code;
    }

    /**
     * Get the [aggreg_nr] column value.
     *
     * @return int
     */
    public function getAggregateNumber()
    {

        return $this->aggreg_nr;
    }

    /**
     * Get the [description] column value.
     *
     * @return string
     */
    public function getDescription()
    {

        return $this->description;
    }

    /**
     * Get the [ext_desc] column value.
     *
     * @return string
     */
    public function getDescriptionExtended()
    {

        return $this->ext_desc;
    }

    /**
     * Get the [start_date] column value.
     *
     * @return string
     */
    public function getDateStart()
    {

        return $this->start_date;
    }

    /**
     * Get the [end_date] column value.
     *
     * @return string
     */
    public function getDateEnd()
    {

        return $this->end_date;
    }

    /**
     * Get the [suspend] column value.
     *
     * @return boolean
     */
    public function getSuspend()
    {

        return $this->suspend;
    }

    /**
     * Get the [avm] column value.
     *
     * @return boolean
     */
    public function getAVM()
    {

        return $this->avm;
    }

    /**
     * Get the [notsdate] column value.
     *
     * @return boolean
     */
    public function getNotDateStart()
    {

        return $this->notsdate;
    }

    /**
     * Get the [notedate] column value.
     *
     * @return boolean
     */
    public function getNotDateEnd()
    {

        return $this->notedate;
    }

    /**
     * Get the [responsibles] column value.
     *
     * @return string
     */
    public function getResponsibles()
    {

        return $this->responsibles;
    }

    /**
     * Get the [objectives] column value.
     *
     * @return string
     */
    public function getObjectives()
    {

        return $this->objectives;
    }

    /**
     * Get the [human_resources] column value.
     *
     * @return string
     */
    public function getHumanResources()
    {

        return $this->human_resources;
    }

    /**
     * Get the [goods_services] column value.
     *
     * @return string
     */
    public function getGoodsServices()
    {

        return $this->goods_services;
    }

    /**
     * Get the [durata] column value.
     *
     * @return string
     */
    public function getDuration()
    {

        return $this->durata;
    }

    /**
     * Get the [budget_year] column value.
     *
     * @return int
     */
    public function getBudgetYear()
    {

        return $this->budget_year;
    }

    /**
     * Get the [residui] column value.
     *
     * @return double
     */
    public function getResiduals()
    {

        return $this->residui;
    }

    /**
     * Get the [hours_insertions_end_date] column value.
     *
     * @return string
     */
    public function getHoursInsertionsEndDate()
    {

        return $this->hours_insertions_end_date;
    }

    /**
     * Set the value of [activ_id] column.
     *
     * @param  int $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setActivityId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->activ_id !== $v) {
            $this->activ_id = $v;
            $this->modifiedColumns[] = ActivityPeer::ACTIV_ID;
        }


        return $this;
    } // setActivityId()

    /**
     * Set the value of [aggreg_code] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setAggregateCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->aggreg_code !== $v) {
            $this->aggreg_code = $v;
            $this->modifiedColumns[] = ActivityPeer::AGGREG_CODE;
        }


        return $this;
    } // setAggregateCode()

    /**
     * Set the value of [aggreg_nr] column.
     *
     * @param  int $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setAggregateNumber($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->aggreg_nr !== $v) {
            $this->aggreg_nr = $v;
            $this->modifiedColumns[] = ActivityPeer::AGGREG_NR;
        }


        return $this;
    } // setAggregateNumber()

    /**
     * Set the value of [description] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setDescription($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->description !== $v) {
            $this->description = $v;
            $this->modifiedColumns[] = ActivityPeer::DESCRIPTION;
        }


        return $this;
    } // setDescription()

    /**
     * Set the value of [ext_desc] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setDescriptionExtended($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ext_desc !== $v) {
            $this->ext_desc = $v;
            $this->modifiedColumns[] = ActivityPeer::EXT_DESC;
        }


        return $this;
    } // setDescriptionExtended()

    /**
     * Set the value of [start_date] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setDateStart($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->start_date !== $v) {
            $this->start_date = $v;
            $this->modifiedColumns[] = ActivityPeer::START_DATE;
        }


        return $this;
    } // setDateStart()

    /**
     * Set the value of [end_date] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setDateEnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->end_date !== $v) {
            $this->end_date = $v;
            $this->modifiedColumns[] = ActivityPeer::END_DATE;
        }


        return $this;
    } // setDateEnd()

    /**
     * Sets the value of the [suspend] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Activity The current object (for fluent API support)
     */
    public function setSuspend($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->suspend !== $v) {
            $this->suspend = $v;
            $this->modifiedColumns[] = ActivityPeer::SUSPEND;
        }


        return $this;
    } // setSuspend()

    /**
     * Sets the value of the [avm] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Activity The current object (for fluent API support)
     */
    public function setAVM($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->avm !== $v) {
            $this->avm = $v;
            $this->modifiedColumns[] = ActivityPeer::AVM;
        }


        return $this;
    } // setAVM()

    /**
     * Sets the value of the [notsdate] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Activity The current object (for fluent API support)
     */
    public function setNotDateStart($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->notsdate !== $v) {
            $this->notsdate = $v;
            $this->modifiedColumns[] = ActivityPeer::NOTSDATE;
        }


        return $this;
    } // setNotDateStart()

    /**
     * Sets the value of the [notedate] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Activity The current object (for fluent API support)
     */
    public function setNotDateEnd($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->notedate !== $v) {
            $this->notedate = $v;
            $this->modifiedColumns[] = ActivityPeer::NOTEDATE;
        }


        return $this;
    } // setNotDateEnd()

    /**
     * Set the value of [responsibles] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setResponsibles($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->responsibles !== $v) {
            $this->responsibles = $v;
            $this->modifiedColumns[] = ActivityPeer::RESPONSIBLES;
        }


        return $this;
    } // setResponsibles()

    /**
     * Set the value of [objectives] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setObjectives($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->objectives !== $v) {
            $this->objectives = $v;
            $this->modifiedColumns[] = ActivityPeer::OBJECTIVES;
        }


        return $this;
    } // setObjectives()

    /**
     * Set the value of [human_resources] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setHumanResources($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->human_resources !== $v) {
            $this->human_resources = $v;
            $this->modifiedColumns[] = ActivityPeer::HUMAN_RESOURCES;
        }


        return $this;
    } // setHumanResources()

    /**
     * Set the value of [goods_services] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setGoodsServices($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->goods_services !== $v) {
            $this->goods_services = $v;
            $this->modifiedColumns[] = ActivityPeer::GOODS_SERVICES;
        }


        return $this;
    } // setGoodsServices()

    /**
     * Set the value of [durata] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setDuration($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->durata !== $v) {
            $this->durata = $v;
            $this->modifiedColumns[] = ActivityPeer::DURATA;
        }


        return $this;
    } // setDuration()

    /**
     * Set the value of [budget_year] column.
     *
     * @param  int $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setBudgetYear($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->budget_year !== $v) {
            $this->budget_year = $v;
            $this->modifiedColumns[] = ActivityPeer::BUDGET_YEAR;
        }


        return $this;
    } // setBudgetYear()

    /**
     * Set the value of [residui] column.
     *
     * @param  double $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setResiduals($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->residui !== $v) {
            $this->residui = $v;
            $this->modifiedColumns[] = ActivityPeer::RESIDUI;
        }


        return $this;
    } // setResiduals()

    /**
     * Set the value of [hours_insertions_end_date] column.
     *
     * @param  string $v new value
     * @return Activity The current object (for fluent API support)
     */
    public function setHoursInsertionsEndDate($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->hours_insertions_end_date !== $v) {
            $this->hours_insertions_end_date = $v;
            $this->modifiedColumns[] = ActivityPeer::HOURS_INSERTIONS_END_DATE;
        }


        return $this;
    } // setHoursInsertionsEndDate()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->suspend !== false) {
                return false;
            }

            if ($this->avm !== false) {
                return false;
            }

            if ($this->notsdate !== false) {
                return false;
            }

            if ($this->notedate !== false) {
                return false;
            }

            if ($this->budget_year !== 2008) {
                return false;
            }

            if ($this->residui !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->activ_id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->aggreg_code = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->aggreg_nr = ($row[$startcol + 2] !== null) ? (int) $row[$startcol + 2] : null;
            $this->description = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->ext_desc = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->start_date = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->end_date = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->suspend = ($row[$startcol + 7] !== null) ? (boolean) $row[$startcol + 7] : null;
            $this->avm = ($row[$startcol + 8] !== null) ? (boolean) $row[$startcol + 8] : null;
            $this->notsdate = ($row[$startcol + 9] !== null) ? (boolean) $row[$startcol + 9] : null;
            $this->notedate = ($row[$startcol + 10] !== null) ? (boolean) $row[$startcol + 10] : null;
            $this->responsibles = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->objectives = ($row[$startcol + 12] !== null) ? (string) $row[$startcol + 12] : null;
            $this->human_resources = ($row[$startcol + 13] !== null) ? (string) $row[$startcol + 13] : null;
            $this->goods_services = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->durata = ($row[$startcol + 15] !== null) ? (string) $row[$startcol + 15] : null;
            $this->budget_year = ($row[$startcol + 16] !== null) ? (int) $row[$startcol + 16] : null;
            $this->residui = ($row[$startcol + 17] !== null) ? (double) $row[$startcol + 17] : null;
            $this->hours_insertions_end_date = ($row[$startcol + 18] !== null) ? (string) $row[$startcol + 18] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 19; // 19 = ActivityPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Activity object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = ActivityPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->collPresences = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = ActivityQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                ActivityPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->presencesScheduledForDeletion !== null) {
                if (!$this->presencesScheduledForDeletion->isEmpty()) {
                    foreach ($this->presencesScheduledForDeletion as $presence) {
                        // need to save related object because we set the relation to null
                        $presence->save($con);
                    }
                    $this->presencesScheduledForDeletion = null;
                }
            }

            if ($this->collPresences !== null) {
                foreach ($this->collPresences as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = ActivityPeer::ACTIV_ID;
        if (null !== $this->activ_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . ActivityPeer::ACTIV_ID . ')');
        }
        if (null === $this->activ_id) {
            try {
                $stmt = $con->query("SELECT nextval('bdg_activities_activ_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->activ_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(ActivityPeer::ACTIV_ID)) {
            $modifiedColumns[':p' . $index++]  = '"activ_id"';
        }
        if ($this->isColumnModified(ActivityPeer::AGGREG_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"aggreg_code"';
        }
        if ($this->isColumnModified(ActivityPeer::AGGREG_NR)) {
            $modifiedColumns[':p' . $index++]  = '"aggreg_nr"';
        }
        if ($this->isColumnModified(ActivityPeer::DESCRIPTION)) {
            $modifiedColumns[':p' . $index++]  = '"description"';
        }
        if ($this->isColumnModified(ActivityPeer::EXT_DESC)) {
            $modifiedColumns[':p' . $index++]  = '"ext_desc"';
        }
        if ($this->isColumnModified(ActivityPeer::START_DATE)) {
            $modifiedColumns[':p' . $index++]  = '"start_date"';
        }
        if ($this->isColumnModified(ActivityPeer::END_DATE)) {
            $modifiedColumns[':p' . $index++]  = '"end_date"';
        }
        if ($this->isColumnModified(ActivityPeer::SUSPEND)) {
            $modifiedColumns[':p' . $index++]  = '"suspend"';
        }
        if ($this->isColumnModified(ActivityPeer::AVM)) {
            $modifiedColumns[':p' . $index++]  = '"avm"';
        }
        if ($this->isColumnModified(ActivityPeer::NOTSDATE)) {
            $modifiedColumns[':p' . $index++]  = '"notsdate"';
        }
        if ($this->isColumnModified(ActivityPeer::NOTEDATE)) {
            $modifiedColumns[':p' . $index++]  = '"notedate"';
        }
        if ($this->isColumnModified(ActivityPeer::RESPONSIBLES)) {
            $modifiedColumns[':p' . $index++]  = '"responsibles"';
        }
        if ($this->isColumnModified(ActivityPeer::OBJECTIVES)) {
            $modifiedColumns[':p' . $index++]  = '"objectives"';
        }
        if ($this->isColumnModified(ActivityPeer::HUMAN_RESOURCES)) {
            $modifiedColumns[':p' . $index++]  = '"human_resources"';
        }
        if ($this->isColumnModified(ActivityPeer::GOODS_SERVICES)) {
            $modifiedColumns[':p' . $index++]  = '"goods_services"';
        }
        if ($this->isColumnModified(ActivityPeer::DURATA)) {
            $modifiedColumns[':p' . $index++]  = '"durata"';
        }
        if ($this->isColumnModified(ActivityPeer::BUDGET_YEAR)) {
            $modifiedColumns[':p' . $index++]  = '"budget_year"';
        }
        if ($this->isColumnModified(ActivityPeer::RESIDUI)) {
            $modifiedColumns[':p' . $index++]  = '"residui"';
        }
        if ($this->isColumnModified(ActivityPeer::HOURS_INSERTIONS_END_DATE)) {
            $modifiedColumns[':p' . $index++]  = '"hours_insertions_end_date"';
        }

        $sql = sprintf(
            'INSERT INTO "bdg_activities" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"activ_id"':
                        $stmt->bindValue($identifier, $this->activ_id, PDO::PARAM_INT);
                        break;
                    case '"aggreg_code"':
                        $stmt->bindValue($identifier, $this->aggreg_code, PDO::PARAM_STR);
                        break;
                    case '"aggreg_nr"':
                        $stmt->bindValue($identifier, $this->aggreg_nr, PDO::PARAM_INT);
                        break;
                    case '"description"':
                        $stmt->bindValue($identifier, $this->description, PDO::PARAM_STR);
                        break;
                    case '"ext_desc"':
                        $stmt->bindValue($identifier, $this->ext_desc, PDO::PARAM_STR);
                        break;
                    case '"start_date"':
                        $stmt->bindValue($identifier, $this->start_date, PDO::PARAM_STR);
                        break;
                    case '"end_date"':
                        $stmt->bindValue($identifier, $this->end_date, PDO::PARAM_STR);
                        break;
                    case '"suspend"':
                        $stmt->bindValue($identifier, $this->suspend, PDO::PARAM_BOOL);
                        break;
                    case '"avm"':
                        $stmt->bindValue($identifier, $this->avm, PDO::PARAM_BOOL);
                        break;
                    case '"notsdate"':
                        $stmt->bindValue($identifier, $this->notsdate, PDO::PARAM_BOOL);
                        break;
                    case '"notedate"':
                        $stmt->bindValue($identifier, $this->notedate, PDO::PARAM_BOOL);
                        break;
                    case '"responsibles"':
                        $stmt->bindValue($identifier, $this->responsibles, PDO::PARAM_STR);
                        break;
                    case '"objectives"':
                        $stmt->bindValue($identifier, $this->objectives, PDO::PARAM_STR);
                        break;
                    case '"human_resources"':
                        $stmt->bindValue($identifier, $this->human_resources, PDO::PARAM_STR);
                        break;
                    case '"goods_services"':
                        $stmt->bindValue($identifier, $this->goods_services, PDO::PARAM_STR);
                        break;
                    case '"durata"':
                        $stmt->bindValue($identifier, $this->durata, PDO::PARAM_STR);
                        break;
                    case '"budget_year"':
                        $stmt->bindValue($identifier, $this->budget_year, PDO::PARAM_INT);
                        break;
                    case '"residui"':
                        $stmt->bindValue($identifier, $this->residui, PDO::PARAM_STR);
                        break;
                    case '"hours_insertions_end_date"':
                        $stmt->bindValue($identifier, $this->hours_insertions_end_date, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = ActivityPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collPresences !== null) {
                    foreach ($this->collPresences as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = ActivityPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getActivityId();
                break;
            case 1:
                return $this->getAggregateCode();
                break;
            case 2:
                return $this->getAggregateNumber();
                break;
            case 3:
                return $this->getDescription();
                break;
            case 4:
                return $this->getDescriptionExtended();
                break;
            case 5:
                return $this->getDateStart();
                break;
            case 6:
                return $this->getDateEnd();
                break;
            case 7:
                return $this->getSuspend();
                break;
            case 8:
                return $this->getAVM();
                break;
            case 9:
                return $this->getNotDateStart();
                break;
            case 10:
                return $this->getNotDateEnd();
                break;
            case 11:
                return $this->getResponsibles();
                break;
            case 12:
                return $this->getObjectives();
                break;
            case 13:
                return $this->getHumanResources();
                break;
            case 14:
                return $this->getGoodsServices();
                break;
            case 15:
                return $this->getDuration();
                break;
            case 16:
                return $this->getBudgetYear();
                break;
            case 17:
                return $this->getResiduals();
                break;
            case 18:
                return $this->getHoursInsertionsEndDate();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Activity'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Activity'][$this->getPrimaryKey()] = true;
        $keys = ActivityPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getActivityId(),
            $keys[1] => $this->getAggregateCode(),
            $keys[2] => $this->getAggregateNumber(),
            $keys[3] => $this->getDescription(),
            $keys[4] => $this->getDescriptionExtended(),
            $keys[5] => $this->getDateStart(),
            $keys[6] => $this->getDateEnd(),
            $keys[7] => $this->getSuspend(),
            $keys[8] => $this->getAVM(),
            $keys[9] => $this->getNotDateStart(),
            $keys[10] => $this->getNotDateEnd(),
            $keys[11] => $this->getResponsibles(),
            $keys[12] => $this->getObjectives(),
            $keys[13] => $this->getHumanResources(),
            $keys[14] => $this->getGoodsServices(),
            $keys[15] => $this->getDuration(),
            $keys[16] => $this->getBudgetYear(),
            $keys[17] => $this->getResiduals(),
            $keys[18] => $this->getHoursInsertionsEndDate(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->collPresences) {
                $result['Presences'] = $this->collPresences->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = ActivityPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setActivityId($value);
                break;
            case 1:
                $this->setAggregateCode($value);
                break;
            case 2:
                $this->setAggregateNumber($value);
                break;
            case 3:
                $this->setDescription($value);
                break;
            case 4:
                $this->setDescriptionExtended($value);
                break;
            case 5:
                $this->setDateStart($value);
                break;
            case 6:
                $this->setDateEnd($value);
                break;
            case 7:
                $this->setSuspend($value);
                break;
            case 8:
                $this->setAVM($value);
                break;
            case 9:
                $this->setNotDateStart($value);
                break;
            case 10:
                $this->setNotDateEnd($value);
                break;
            case 11:
                $this->setResponsibles($value);
                break;
            case 12:
                $this->setObjectives($value);
                break;
            case 13:
                $this->setHumanResources($value);
                break;
            case 14:
                $this->setGoodsServices($value);
                break;
            case 15:
                $this->setDuration($value);
                break;
            case 16:
                $this->setBudgetYear($value);
                break;
            case 17:
                $this->setResiduals($value);
                break;
            case 18:
                $this->setHoursInsertionsEndDate($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = ActivityPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setActivityId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setAggregateCode($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setAggregateNumber($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setDescription($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setDescriptionExtended($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setDateStart($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setDateEnd($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setSuspend($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setAVM($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setNotDateStart($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setNotDateEnd($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setResponsibles($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setObjectives($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setHumanResources($arr[$keys[13]]);
        if (array_key_exists($keys[14], $arr)) $this->setGoodsServices($arr[$keys[14]]);
        if (array_key_exists($keys[15], $arr)) $this->setDuration($arr[$keys[15]]);
        if (array_key_exists($keys[16], $arr)) $this->setBudgetYear($arr[$keys[16]]);
        if (array_key_exists($keys[17], $arr)) $this->setResiduals($arr[$keys[17]]);
        if (array_key_exists($keys[18], $arr)) $this->setHoursInsertionsEndDate($arr[$keys[18]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(ActivityPeer::DATABASE_NAME);

        if ($this->isColumnModified(ActivityPeer::ACTIV_ID)) $criteria->add(ActivityPeer::ACTIV_ID, $this->activ_id);
        if ($this->isColumnModified(ActivityPeer::AGGREG_CODE)) $criteria->add(ActivityPeer::AGGREG_CODE, $this->aggreg_code);
        if ($this->isColumnModified(ActivityPeer::AGGREG_NR)) $criteria->add(ActivityPeer::AGGREG_NR, $this->aggreg_nr);
        if ($this->isColumnModified(ActivityPeer::DESCRIPTION)) $criteria->add(ActivityPeer::DESCRIPTION, $this->description);
        if ($this->isColumnModified(ActivityPeer::EXT_DESC)) $criteria->add(ActivityPeer::EXT_DESC, $this->ext_desc);
        if ($this->isColumnModified(ActivityPeer::START_DATE)) $criteria->add(ActivityPeer::START_DATE, $this->start_date);
        if ($this->isColumnModified(ActivityPeer::END_DATE)) $criteria->add(ActivityPeer::END_DATE, $this->end_date);
        if ($this->isColumnModified(ActivityPeer::SUSPEND)) $criteria->add(ActivityPeer::SUSPEND, $this->suspend);
        if ($this->isColumnModified(ActivityPeer::AVM)) $criteria->add(ActivityPeer::AVM, $this->avm);
        if ($this->isColumnModified(ActivityPeer::NOTSDATE)) $criteria->add(ActivityPeer::NOTSDATE, $this->notsdate);
        if ($this->isColumnModified(ActivityPeer::NOTEDATE)) $criteria->add(ActivityPeer::NOTEDATE, $this->notedate);
        if ($this->isColumnModified(ActivityPeer::RESPONSIBLES)) $criteria->add(ActivityPeer::RESPONSIBLES, $this->responsibles);
        if ($this->isColumnModified(ActivityPeer::OBJECTIVES)) $criteria->add(ActivityPeer::OBJECTIVES, $this->objectives);
        if ($this->isColumnModified(ActivityPeer::HUMAN_RESOURCES)) $criteria->add(ActivityPeer::HUMAN_RESOURCES, $this->human_resources);
        if ($this->isColumnModified(ActivityPeer::GOODS_SERVICES)) $criteria->add(ActivityPeer::GOODS_SERVICES, $this->goods_services);
        if ($this->isColumnModified(ActivityPeer::DURATA)) $criteria->add(ActivityPeer::DURATA, $this->durata);
        if ($this->isColumnModified(ActivityPeer::BUDGET_YEAR)) $criteria->add(ActivityPeer::BUDGET_YEAR, $this->budget_year);
        if ($this->isColumnModified(ActivityPeer::RESIDUI)) $criteria->add(ActivityPeer::RESIDUI, $this->residui);
        if ($this->isColumnModified(ActivityPeer::HOURS_INSERTIONS_END_DATE)) $criteria->add(ActivityPeer::HOURS_INSERTIONS_END_DATE, $this->hours_insertions_end_date);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(ActivityPeer::DATABASE_NAME);
        $criteria->add(ActivityPeer::ACTIV_ID, $this->activ_id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getActivityId();
    }

    /**
     * Generic method to set the primary key (activ_id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setActivityId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getActivityId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Activity (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setAggregateCode($this->getAggregateCode());
        $copyObj->setAggregateNumber($this->getAggregateNumber());
        $copyObj->setDescription($this->getDescription());
        $copyObj->setDescriptionExtended($this->getDescriptionExtended());
        $copyObj->setDateStart($this->getDateStart());
        $copyObj->setDateEnd($this->getDateEnd());
        $copyObj->setSuspend($this->getSuspend());
        $copyObj->setAVM($this->getAVM());
        $copyObj->setNotDateStart($this->getNotDateStart());
        $copyObj->setNotDateEnd($this->getNotDateEnd());
        $copyObj->setResponsibles($this->getResponsibles());
        $copyObj->setObjectives($this->getObjectives());
        $copyObj->setHumanResources($this->getHumanResources());
        $copyObj->setGoodsServices($this->getGoodsServices());
        $copyObj->setDuration($this->getDuration());
        $copyObj->setBudgetYear($this->getBudgetYear());
        $copyObj->setResiduals($this->getResiduals());
        $copyObj->setHoursInsertionsEndDate($this->getHoursInsertionsEndDate());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getPresences() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addPresence($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setActivityId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Activity Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return ActivityPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new ActivityPeer();
        }

        return self::$peer;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Presence' == $relationName) {
            $this->initPresences();
        }
    }

    /**
     * Clears out the collPresences collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Activity The current object (for fluent API support)
     * @see        addPresences()
     */
    public function clearPresences()
    {
        $this->collPresences = null; // important to set this to null since that means it is uninitialized
        $this->collPresencesPartial = null;

        return $this;
    }

    /**
     * reset is the collPresences collection loaded partially
     *
     * @return void
     */
    public function resetPartialPresences($v = true)
    {
        $this->collPresencesPartial = $v;
    }

    /**
     * Initializes the collPresences collection.
     *
     * By default this just sets the collPresences collection to an empty array (like clearcollPresences());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initPresences($overrideExisting = true)
    {
        if (null !== $this->collPresences && !$overrideExisting) {
            return;
        }
        $this->collPresences = new PropelObjectCollection();
        $this->collPresences->setModel('Presence');
    }

    /**
     * Gets an array of Presence objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Activity is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Presence[] List of Presence objects
     * @throws PropelException
     */
    public function getPresences($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collPresencesPartial && !$this->isNew();
        if (null === $this->collPresences || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collPresences) {
                // return empty collection
                $this->initPresences();
            } else {
                $collPresences = PresenceQuery::create(null, $criteria)
                    ->filterByPresenceProject($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collPresencesPartial && count($collPresences)) {
                      $this->initPresences(false);

                      foreach ($collPresences as $obj) {
                        if (false == $this->collPresences->contains($obj)) {
                          $this->collPresences->append($obj);
                        }
                      }

                      $this->collPresencesPartial = true;
                    }

                    $collPresences->getInternalIterator()->rewind();

                    return $collPresences;
                }

                if ($partial && $this->collPresences) {
                    foreach ($this->collPresences as $obj) {
                        if ($obj->isNew()) {
                            $collPresences[] = $obj;
                        }
                    }
                }

                $this->collPresences = $collPresences;
                $this->collPresencesPartial = false;
            }
        }

        return $this->collPresences;
    }

    /**
     * Sets a collection of Presence objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $presences A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Activity The current object (for fluent API support)
     */
    public function setPresences(PropelCollection $presences, PropelPDO $con = null)
    {
        $presencesToDelete = $this->getPresences(new Criteria(), $con)->diff($presences);


        $this->presencesScheduledForDeletion = $presencesToDelete;

        foreach ($presencesToDelete as $presenceRemoved) {
            $presenceRemoved->setPresenceProject(null);
        }

        $this->collPresences = null;
        foreach ($presences as $presence) {
            $this->addPresence($presence);
        }

        $this->collPresences = $presences;
        $this->collPresencesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Presence objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Presence objects.
     * @throws PropelException
     */
    public function countPresences(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collPresencesPartial && !$this->isNew();
        if (null === $this->collPresences || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collPresences) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getPresences());
            }
            $query = PresenceQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByPresenceProject($this)
                ->count($con);
        }

        return count($this->collPresences);
    }

    /**
     * Method called to associate a Presence object to this object
     * through the Presence foreign key attribute.
     *
     * @param    Presence $l Presence
     * @return Activity The current object (for fluent API support)
     */
    public function addPresence(Presence $l)
    {
        if ($this->collPresences === null) {
            $this->initPresences();
            $this->collPresencesPartial = true;
        }

        if (!in_array($l, $this->collPresences->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddPresence($l);

            if ($this->presencesScheduledForDeletion and $this->presencesScheduledForDeletion->contains($l)) {
                $this->presencesScheduledForDeletion->remove($this->presencesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Presence $presence The presence object to add.
     */
    protected function doAddPresence($presence)
    {
        $this->collPresences[]= $presence;
        $presence->setPresenceProject($this);
    }

    /**
     * @param	Presence $presence The presence object to remove.
     * @return Activity The current object (for fluent API support)
     */
    public function removePresence($presence)
    {
        if ($this->getPresences()->contains($presence)) {
            $this->collPresences->remove($this->collPresences->search($presence));
            if (null === $this->presencesScheduledForDeletion) {
                $this->presencesScheduledForDeletion = clone $this->collPresences;
                $this->presencesScheduledForDeletion->clear();
            }
            $this->presencesScheduledForDeletion[]= $presence;
            $presence->setPresenceProject(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Activity is new, it will return
     * an empty collection; or if this Activity has previously
     * been saved, it will retrieve related Presences from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Activity.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Presence[] List of Presence objects
     */
    public function getPresencesJoinPresenceEmployee($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = PresenceQuery::create(null, $criteria);
        $query->joinWith('PresenceEmployee', $join_behavior);

        return $this->getPresences($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->activ_id = null;
        $this->aggreg_code = null;
        $this->aggreg_nr = null;
        $this->description = null;
        $this->ext_desc = null;
        $this->start_date = null;
        $this->end_date = null;
        $this->suspend = null;
        $this->avm = null;
        $this->notsdate = null;
        $this->notedate = null;
        $this->responsibles = null;
        $this->objectives = null;
        $this->human_resources = null;
        $this->goods_services = null;
        $this->durata = null;
        $this->budget_year = null;
        $this->residui = null;
        $this->hours_insertions_end_date = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collPresences) {
                foreach ($this->collPresences as $o) {
                    $o->clearAllReferences($deep);
                }
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collPresences instanceof PropelCollection) {
            $this->collPresences->clearIterator();
        }
        $this->collPresences = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(ActivityPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
