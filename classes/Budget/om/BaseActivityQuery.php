<?php

namespace Budget\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Budget\Activity;
use Budget\ActivityPeer;
use Budget\ActivityQuery;
use Employee\Presence;

/**
 * Base class that represents a query for the 'bdg_activities' table.
 *
 *
 *
 * @method ActivityQuery orderByActivityId($order = Criteria::ASC) Order by the activ_id column
 * @method ActivityQuery orderByAggregateCode($order = Criteria::ASC) Order by the aggreg_code column
 * @method ActivityQuery orderByAggregateNumber($order = Criteria::ASC) Order by the aggreg_nr column
 * @method ActivityQuery orderByDescription($order = Criteria::ASC) Order by the description column
 * @method ActivityQuery orderByDescriptionExtended($order = Criteria::ASC) Order by the ext_desc column
 * @method ActivityQuery orderByDateStart($order = Criteria::ASC) Order by the start_date column
 * @method ActivityQuery orderByDateEnd($order = Criteria::ASC) Order by the end_date column
 * @method ActivityQuery orderBySuspend($order = Criteria::ASC) Order by the suspend column
 * @method ActivityQuery orderByAVM($order = Criteria::ASC) Order by the avm column
 * @method ActivityQuery orderByNotDateStart($order = Criteria::ASC) Order by the notsdate column
 * @method ActivityQuery orderByNotDateEnd($order = Criteria::ASC) Order by the notedate column
 * @method ActivityQuery orderByResponsibles($order = Criteria::ASC) Order by the responsibles column
 * @method ActivityQuery orderByObjectives($order = Criteria::ASC) Order by the objectives column
 * @method ActivityQuery orderByHumanResources($order = Criteria::ASC) Order by the human_resources column
 * @method ActivityQuery orderByGoodsServices($order = Criteria::ASC) Order by the goods_services column
 * @method ActivityQuery orderByDuration($order = Criteria::ASC) Order by the durata column
 * @method ActivityQuery orderByBudgetYear($order = Criteria::ASC) Order by the budget_year column
 * @method ActivityQuery orderByResiduals($order = Criteria::ASC) Order by the residui column
 * @method ActivityQuery orderByHoursInsertionsEndDate($order = Criteria::ASC) Order by the hours_insertions_end_date column
 *
 * @method ActivityQuery groupByActivityId() Group by the activ_id column
 * @method ActivityQuery groupByAggregateCode() Group by the aggreg_code column
 * @method ActivityQuery groupByAggregateNumber() Group by the aggreg_nr column
 * @method ActivityQuery groupByDescription() Group by the description column
 * @method ActivityQuery groupByDescriptionExtended() Group by the ext_desc column
 * @method ActivityQuery groupByDateStart() Group by the start_date column
 * @method ActivityQuery groupByDateEnd() Group by the end_date column
 * @method ActivityQuery groupBySuspend() Group by the suspend column
 * @method ActivityQuery groupByAVM() Group by the avm column
 * @method ActivityQuery groupByNotDateStart() Group by the notsdate column
 * @method ActivityQuery groupByNotDateEnd() Group by the notedate column
 * @method ActivityQuery groupByResponsibles() Group by the responsibles column
 * @method ActivityQuery groupByObjectives() Group by the objectives column
 * @method ActivityQuery groupByHumanResources() Group by the human_resources column
 * @method ActivityQuery groupByGoodsServices() Group by the goods_services column
 * @method ActivityQuery groupByDuration() Group by the durata column
 * @method ActivityQuery groupByBudgetYear() Group by the budget_year column
 * @method ActivityQuery groupByResiduals() Group by the residui column
 * @method ActivityQuery groupByHoursInsertionsEndDate() Group by the hours_insertions_end_date column
 *
 * @method ActivityQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method ActivityQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method ActivityQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method ActivityQuery leftJoinPresence($relationAlias = null) Adds a LEFT JOIN clause to the query using the Presence relation
 * @method ActivityQuery rightJoinPresence($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Presence relation
 * @method ActivityQuery innerJoinPresence($relationAlias = null) Adds a INNER JOIN clause to the query using the Presence relation
 *
 * @method Activity findOne(PropelPDO $con = null) Return the first Activity matching the query
 * @method Activity findOneOrCreate(PropelPDO $con = null) Return the first Activity matching the query, or a new Activity object populated from the query conditions when no match is found
 *
 * @method Activity findOneByAggregateCode(string $aggreg_code) Return the first Activity filtered by the aggreg_code column
 * @method Activity findOneByAggregateNumber(int $aggreg_nr) Return the first Activity filtered by the aggreg_nr column
 * @method Activity findOneByDescription(string $description) Return the first Activity filtered by the description column
 * @method Activity findOneByDescriptionExtended(string $ext_desc) Return the first Activity filtered by the ext_desc column
 * @method Activity findOneByDateStart(string $start_date) Return the first Activity filtered by the start_date column
 * @method Activity findOneByDateEnd(string $end_date) Return the first Activity filtered by the end_date column
 * @method Activity findOneBySuspend(boolean $suspend) Return the first Activity filtered by the suspend column
 * @method Activity findOneByAVM(boolean $avm) Return the first Activity filtered by the avm column
 * @method Activity findOneByNotDateStart(boolean $notsdate) Return the first Activity filtered by the notsdate column
 * @method Activity findOneByNotDateEnd(boolean $notedate) Return the first Activity filtered by the notedate column
 * @method Activity findOneByResponsibles(string $responsibles) Return the first Activity filtered by the responsibles column
 * @method Activity findOneByObjectives(string $objectives) Return the first Activity filtered by the objectives column
 * @method Activity findOneByHumanResources(string $human_resources) Return the first Activity filtered by the human_resources column
 * @method Activity findOneByGoodsServices(string $goods_services) Return the first Activity filtered by the goods_services column
 * @method Activity findOneByDuration(string $durata) Return the first Activity filtered by the durata column
 * @method Activity findOneByBudgetYear(int $budget_year) Return the first Activity filtered by the budget_year column
 * @method Activity findOneByResiduals(double $residui) Return the first Activity filtered by the residui column
 * @method Activity findOneByHoursInsertionsEndDate(string $hours_insertions_end_date) Return the first Activity filtered by the hours_insertions_end_date column
 *
 * @method array findByActivityId(int $activ_id) Return Activity objects filtered by the activ_id column
 * @method array findByAggregateCode(string $aggreg_code) Return Activity objects filtered by the aggreg_code column
 * @method array findByAggregateNumber(int $aggreg_nr) Return Activity objects filtered by the aggreg_nr column
 * @method array findByDescription(string $description) Return Activity objects filtered by the description column
 * @method array findByDescriptionExtended(string $ext_desc) Return Activity objects filtered by the ext_desc column
 * @method array findByDateStart(string $start_date) Return Activity objects filtered by the start_date column
 * @method array findByDateEnd(string $end_date) Return Activity objects filtered by the end_date column
 * @method array findBySuspend(boolean $suspend) Return Activity objects filtered by the suspend column
 * @method array findByAVM(boolean $avm) Return Activity objects filtered by the avm column
 * @method array findByNotDateStart(boolean $notsdate) Return Activity objects filtered by the notsdate column
 * @method array findByNotDateEnd(boolean $notedate) Return Activity objects filtered by the notedate column
 * @method array findByResponsibles(string $responsibles) Return Activity objects filtered by the responsibles column
 * @method array findByObjectives(string $objectives) Return Activity objects filtered by the objectives column
 * @method array findByHumanResources(string $human_resources) Return Activity objects filtered by the human_resources column
 * @method array findByGoodsServices(string $goods_services) Return Activity objects filtered by the goods_services column
 * @method array findByDuration(string $durata) Return Activity objects filtered by the durata column
 * @method array findByBudgetYear(int $budget_year) Return Activity objects filtered by the budget_year column
 * @method array findByResiduals(double $residui) Return Activity objects filtered by the residui column
 * @method array findByHoursInsertionsEndDate(string $hours_insertions_end_date) Return Activity objects filtered by the hours_insertions_end_date column
 *
 * @package    propel.generator.Budget.om
 */
abstract class BaseActivityQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseActivityQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Budget\\Activity';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new ActivityQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   ActivityQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return ActivityQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof ActivityQuery) {
            return $criteria;
        }
        $query = new ActivityQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Activity|Activity[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = ActivityPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(ActivityPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Activity A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByActivityId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Activity A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "activ_id", "aggreg_code", "aggreg_nr", "description", "ext_desc", "start_date", "end_date", "suspend", "avm", "notsdate", "notedate", "responsibles", "objectives", "human_resources", "goods_services", "durata", "budget_year", "residui", "hours_insertions_end_date" FROM "bdg_activities" WHERE "activ_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Activity();
            $obj->hydrate($row);
            ActivityPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Activity|Activity[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Activity[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(ActivityPeer::ACTIV_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(ActivityPeer::ACTIV_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the activ_id column
     *
     * Example usage:
     * <code>
     * $query->filterByActivityId(1234); // WHERE activ_id = 1234
     * $query->filterByActivityId(array(12, 34)); // WHERE activ_id IN (12, 34)
     * $query->filterByActivityId(array('min' => 12)); // WHERE activ_id >= 12
     * $query->filterByActivityId(array('max' => 12)); // WHERE activ_id <= 12
     * </code>
     *
     * @param     mixed $activityId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByActivityId($activityId = null, $comparison = null)
    {
        if (is_array($activityId)) {
            $useMinMax = false;
            if (isset($activityId['min'])) {
                $this->addUsingAlias(ActivityPeer::ACTIV_ID, $activityId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($activityId['max'])) {
                $this->addUsingAlias(ActivityPeer::ACTIV_ID, $activityId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::ACTIV_ID, $activityId, $comparison);
    }

    /**
     * Filter the query on the aggreg_code column
     *
     * Example usage:
     * <code>
     * $query->filterByAggregateCode('fooValue');   // WHERE aggreg_code = 'fooValue'
     * $query->filterByAggregateCode('%fooValue%'); // WHERE aggreg_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $aggregateCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByAggregateCode($aggregateCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($aggregateCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $aggregateCode)) {
                $aggregateCode = str_replace('*', '%', $aggregateCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::AGGREG_CODE, $aggregateCode, $comparison);
    }

    /**
     * Filter the query on the aggreg_nr column
     *
     * Example usage:
     * <code>
     * $query->filterByAggregateNumber(1234); // WHERE aggreg_nr = 1234
     * $query->filterByAggregateNumber(array(12, 34)); // WHERE aggreg_nr IN (12, 34)
     * $query->filterByAggregateNumber(array('min' => 12)); // WHERE aggreg_nr >= 12
     * $query->filterByAggregateNumber(array('max' => 12)); // WHERE aggreg_nr <= 12
     * </code>
     *
     * @param     mixed $aggregateNumber The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByAggregateNumber($aggregateNumber = null, $comparison = null)
    {
        if (is_array($aggregateNumber)) {
            $useMinMax = false;
            if (isset($aggregateNumber['min'])) {
                $this->addUsingAlias(ActivityPeer::AGGREG_NR, $aggregateNumber['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($aggregateNumber['max'])) {
                $this->addUsingAlias(ActivityPeer::AGGREG_NR, $aggregateNumber['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::AGGREG_NR, $aggregateNumber, $comparison);
    }

    /**
     * Filter the query on the description column
     *
     * Example usage:
     * <code>
     * $query->filterByDescription('fooValue');   // WHERE description = 'fooValue'
     * $query->filterByDescription('%fooValue%'); // WHERE description LIKE '%fooValue%'
     * </code>
     *
     * @param     string $description The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByDescription($description = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($description)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $description)) {
                $description = str_replace('*', '%', $description);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::DESCRIPTION, $description, $comparison);
    }

    /**
     * Filter the query on the ext_desc column
     *
     * Example usage:
     * <code>
     * $query->filterByDescriptionExtended('fooValue');   // WHERE ext_desc = 'fooValue'
     * $query->filterByDescriptionExtended('%fooValue%'); // WHERE ext_desc LIKE '%fooValue%'
     * </code>
     *
     * @param     string $descriptionExtended The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByDescriptionExtended($descriptionExtended = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($descriptionExtended)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $descriptionExtended)) {
                $descriptionExtended = str_replace('*', '%', $descriptionExtended);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::EXT_DESC, $descriptionExtended, $comparison);
    }

    /**
     * Filter the query on the start_date column
     *
     * Example usage:
     * <code>
     * $query->filterByDateStart(1234); // WHERE start_date = 1234
     * $query->filterByDateStart(array(12, 34)); // WHERE start_date IN (12, 34)
     * $query->filterByDateStart(array('min' => 12)); // WHERE start_date >= 12
     * $query->filterByDateStart(array('max' => 12)); // WHERE start_date <= 12
     * </code>
     *
     * @param     mixed $dateStart The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByDateStart($dateStart = null, $comparison = null)
    {
        if (is_array($dateStart)) {
            $useMinMax = false;
            if (isset($dateStart['min'])) {
                $this->addUsingAlias(ActivityPeer::START_DATE, $dateStart['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateStart['max'])) {
                $this->addUsingAlias(ActivityPeer::START_DATE, $dateStart['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::START_DATE, $dateStart, $comparison);
    }

    /**
     * Filter the query on the end_date column
     *
     * Example usage:
     * <code>
     * $query->filterByDateEnd(1234); // WHERE end_date = 1234
     * $query->filterByDateEnd(array(12, 34)); // WHERE end_date IN (12, 34)
     * $query->filterByDateEnd(array('min' => 12)); // WHERE end_date >= 12
     * $query->filterByDateEnd(array('max' => 12)); // WHERE end_date <= 12
     * </code>
     *
     * @param     mixed $dateEnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByDateEnd($dateEnd = null, $comparison = null)
    {
        if (is_array($dateEnd)) {
            $useMinMax = false;
            if (isset($dateEnd['min'])) {
                $this->addUsingAlias(ActivityPeer::END_DATE, $dateEnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateEnd['max'])) {
                $this->addUsingAlias(ActivityPeer::END_DATE, $dateEnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::END_DATE, $dateEnd, $comparison);
    }

    /**
     * Filter the query on the suspend column
     *
     * Example usage:
     * <code>
     * $query->filterBySuspend(true); // WHERE suspend = true
     * $query->filterBySuspend('yes'); // WHERE suspend = true
     * </code>
     *
     * @param     boolean|string $suspend The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterBySuspend($suspend = null, $comparison = null)
    {
        if (is_string($suspend)) {
            $suspend = in_array(strtolower($suspend), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(ActivityPeer::SUSPEND, $suspend, $comparison);
    }

    /**
     * Filter the query on the avm column
     *
     * Example usage:
     * <code>
     * $query->filterByAVM(true); // WHERE avm = true
     * $query->filterByAVM('yes'); // WHERE avm = true
     * </code>
     *
     * @param     boolean|string $aVM The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByAVM($aVM = null, $comparison = null)
    {
        if (is_string($aVM)) {
            $aVM = in_array(strtolower($aVM), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(ActivityPeer::AVM, $aVM, $comparison);
    }

    /**
     * Filter the query on the notsdate column
     *
     * Example usage:
     * <code>
     * $query->filterByNotDateStart(true); // WHERE notsdate = true
     * $query->filterByNotDateStart('yes'); // WHERE notsdate = true
     * </code>
     *
     * @param     boolean|string $notDateStart The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByNotDateStart($notDateStart = null, $comparison = null)
    {
        if (is_string($notDateStart)) {
            $notDateStart = in_array(strtolower($notDateStart), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(ActivityPeer::NOTSDATE, $notDateStart, $comparison);
    }

    /**
     * Filter the query on the notedate column
     *
     * Example usage:
     * <code>
     * $query->filterByNotDateEnd(true); // WHERE notedate = true
     * $query->filterByNotDateEnd('yes'); // WHERE notedate = true
     * </code>
     *
     * @param     boolean|string $notDateEnd The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByNotDateEnd($notDateEnd = null, $comparison = null)
    {
        if (is_string($notDateEnd)) {
            $notDateEnd = in_array(strtolower($notDateEnd), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(ActivityPeer::NOTEDATE, $notDateEnd, $comparison);
    }

    /**
     * Filter the query on the responsibles column
     *
     * Example usage:
     * <code>
     * $query->filterByResponsibles('fooValue');   // WHERE responsibles = 'fooValue'
     * $query->filterByResponsibles('%fooValue%'); // WHERE responsibles LIKE '%fooValue%'
     * </code>
     *
     * @param     string $responsibles The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByResponsibles($responsibles = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($responsibles)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $responsibles)) {
                $responsibles = str_replace('*', '%', $responsibles);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::RESPONSIBLES, $responsibles, $comparison);
    }

    /**
     * Filter the query on the objectives column
     *
     * Example usage:
     * <code>
     * $query->filterByObjectives('fooValue');   // WHERE objectives = 'fooValue'
     * $query->filterByObjectives('%fooValue%'); // WHERE objectives LIKE '%fooValue%'
     * </code>
     *
     * @param     string $objectives The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByObjectives($objectives = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($objectives)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $objectives)) {
                $objectives = str_replace('*', '%', $objectives);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::OBJECTIVES, $objectives, $comparison);
    }

    /**
     * Filter the query on the human_resources column
     *
     * Example usage:
     * <code>
     * $query->filterByHumanResources('fooValue');   // WHERE human_resources = 'fooValue'
     * $query->filterByHumanResources('%fooValue%'); // WHERE human_resources LIKE '%fooValue%'
     * </code>
     *
     * @param     string $humanResources The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByHumanResources($humanResources = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($humanResources)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $humanResources)) {
                $humanResources = str_replace('*', '%', $humanResources);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::HUMAN_RESOURCES, $humanResources, $comparison);
    }

    /**
     * Filter the query on the goods_services column
     *
     * Example usage:
     * <code>
     * $query->filterByGoodsServices('fooValue');   // WHERE goods_services = 'fooValue'
     * $query->filterByGoodsServices('%fooValue%'); // WHERE goods_services LIKE '%fooValue%'
     * </code>
     *
     * @param     string $goodsServices The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByGoodsServices($goodsServices = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($goodsServices)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $goodsServices)) {
                $goodsServices = str_replace('*', '%', $goodsServices);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::GOODS_SERVICES, $goodsServices, $comparison);
    }

    /**
     * Filter the query on the durata column
     *
     * Example usage:
     * <code>
     * $query->filterByDuration('fooValue');   // WHERE durata = 'fooValue'
     * $query->filterByDuration('%fooValue%'); // WHERE durata LIKE '%fooValue%'
     * </code>
     *
     * @param     string $duration The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByDuration($duration = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($duration)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $duration)) {
                $duration = str_replace('*', '%', $duration);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ActivityPeer::DURATA, $duration, $comparison);
    }

    /**
     * Filter the query on the budget_year column
     *
     * Example usage:
     * <code>
     * $query->filterByBudgetYear(1234); // WHERE budget_year = 1234
     * $query->filterByBudgetYear(array(12, 34)); // WHERE budget_year IN (12, 34)
     * $query->filterByBudgetYear(array('min' => 12)); // WHERE budget_year >= 12
     * $query->filterByBudgetYear(array('max' => 12)); // WHERE budget_year <= 12
     * </code>
     *
     * @param     mixed $budgetYear The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByBudgetYear($budgetYear = null, $comparison = null)
    {
        if (is_array($budgetYear)) {
            $useMinMax = false;
            if (isset($budgetYear['min'])) {
                $this->addUsingAlias(ActivityPeer::BUDGET_YEAR, $budgetYear['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($budgetYear['max'])) {
                $this->addUsingAlias(ActivityPeer::BUDGET_YEAR, $budgetYear['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::BUDGET_YEAR, $budgetYear, $comparison);
    }

    /**
     * Filter the query on the residui column
     *
     * Example usage:
     * <code>
     * $query->filterByResiduals(1234); // WHERE residui = 1234
     * $query->filterByResiduals(array(12, 34)); // WHERE residui IN (12, 34)
     * $query->filterByResiduals(array('min' => 12)); // WHERE residui >= 12
     * $query->filterByResiduals(array('max' => 12)); // WHERE residui <= 12
     * </code>
     *
     * @param     mixed $residuals The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByResiduals($residuals = null, $comparison = null)
    {
        if (is_array($residuals)) {
            $useMinMax = false;
            if (isset($residuals['min'])) {
                $this->addUsingAlias(ActivityPeer::RESIDUI, $residuals['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($residuals['max'])) {
                $this->addUsingAlias(ActivityPeer::RESIDUI, $residuals['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::RESIDUI, $residuals, $comparison);
    }

    /**
     * Filter the query on the hours_insertions_end_date column
     *
     * Example usage:
     * <code>
     * $query->filterByHoursInsertionsEndDate(1234); // WHERE hours_insertions_end_date = 1234
     * $query->filterByHoursInsertionsEndDate(array(12, 34)); // WHERE hours_insertions_end_date IN (12, 34)
     * $query->filterByHoursInsertionsEndDate(array('min' => 12)); // WHERE hours_insertions_end_date >= 12
     * $query->filterByHoursInsertionsEndDate(array('max' => 12)); // WHERE hours_insertions_end_date <= 12
     * </code>
     *
     * @param     mixed $hoursInsertionsEndDate The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function filterByHoursInsertionsEndDate($hoursInsertionsEndDate = null, $comparison = null)
    {
        if (is_array($hoursInsertionsEndDate)) {
            $useMinMax = false;
            if (isset($hoursInsertionsEndDate['min'])) {
                $this->addUsingAlias(ActivityPeer::HOURS_INSERTIONS_END_DATE, $hoursInsertionsEndDate['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($hoursInsertionsEndDate['max'])) {
                $this->addUsingAlias(ActivityPeer::HOURS_INSERTIONS_END_DATE, $hoursInsertionsEndDate['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ActivityPeer::HOURS_INSERTIONS_END_DATE, $hoursInsertionsEndDate, $comparison);
    }

    /**
     * Filter the query by a related Presence object
     *
     * @param   Presence|PropelObjectCollection $presence  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 ActivityQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPresence($presence, $comparison = null)
    {
        if ($presence instanceof Presence) {
            return $this
                ->addUsingAlias(ActivityPeer::ACTIV_ID, $presence->getProjectIdEdit(), $comparison);
        } elseif ($presence instanceof PropelObjectCollection) {
            return $this
                ->usePresenceQuery()
                ->filterByPrimaryKeys($presence->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByPresence() only accepts arguments of type Presence or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Presence relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function joinPresence($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Presence');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Presence');
        }

        return $this;
    }

    /**
     * Use the Presence relation Presence object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\PresenceQuery A secondary query class using the current class as primary query
     */
    public function usePresenceQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinPresence($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Presence', '\Employee\PresenceQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Activity $activity Object to remove from the list of results
     *
     * @return ActivityQuery The current query, for fluid interface
     */
    public function prune($activity = null)
    {
        if ($activity) {
            $this->addUsingAlias(ActivityPeer::ACTIV_ID, $activity->getActivityId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
