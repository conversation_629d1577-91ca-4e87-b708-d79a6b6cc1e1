<?php

namespace Auth\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthPermissionGroup;
use Auth\AuthPermissionGroupQuery;
use Auth\Groups;
use Auth\GroupsPeer;
use Auth\GroupsQuery;

/**
 * Base class that represents a row from the 'groups' table.
 *
 *
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseGroups extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Auth\\GroupsPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        GroupsPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the gid field.
     * @var        int
     */
    protected $gid;

    /**
     * The value for the group_name field.
     * @var        string
     */
    protected $group_name;

    /**
     * The value for the enabled field.
     * Note: this column has a database default value of: 1
     * @var        int
     */
    protected $enabled;

    /**
     * @var        PropelObjectCollection|AuthPermissionGroup[] Collection to store aggregation of AuthPermissionGroup objects.
     */
    protected $collAuthPermissionGroups;
    protected $collAuthPermissionGroupsPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $authPermissionGroupsScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->enabled = 1;
    }

    /**
     * Initializes internal state of BaseGroups object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [gid] column value.
     *
     * @return int
     */
    public function getGid()
    {

        return $this->gid;
    }

    /**
     * Get the [group_name] column value.
     *
     * @return string
     */
    public function getGroupName()
    {

        return $this->group_name;
    }

    /**
     * Get the [enabled] column value.
     *
     * @return int
     */
    public function getEnabled()
    {

        return $this->enabled;
    }

    /**
     * Set the value of [gid] column.
     *
     * @param  int $v new value
     * @return Groups The current object (for fluent API support)
     */
    public function setGid($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->gid !== $v) {
            $this->gid = $v;
            $this->modifiedColumns[] = GroupsPeer::GID;
        }


        return $this;
    } // setGid()

    /**
     * Set the value of [group_name] column.
     *
     * @param  string $v new value
     * @return Groups The current object (for fluent API support)
     */
    public function setGroupName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->group_name !== $v) {
            $this->group_name = $v;
            $this->modifiedColumns[] = GroupsPeer::GROUP_NAME;
        }


        return $this;
    } // setGroupName()

    /**
     * Set the value of [enabled] column.
     *
     * @param  int $v new value
     * @return Groups The current object (for fluent API support)
     */
    public function setEnabled($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->enabled !== $v) {
            $this->enabled = $v;
            $this->modifiedColumns[] = GroupsPeer::ENABLED;
        }


        return $this;
    } // setEnabled()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->enabled !== 1) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->gid = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->group_name = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->enabled = ($row[$startcol + 2] !== null) ? (int) $row[$startcol + 2] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 3; // 3 = GroupsPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Groups object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(GroupsPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = GroupsPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->collAuthPermissionGroups = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(GroupsPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = GroupsQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(GroupsPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                GroupsPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->authPermissionGroupsScheduledForDeletion !== null) {
                if (!$this->authPermissionGroupsScheduledForDeletion->isEmpty()) {
                    AuthPermissionGroupQuery::create()
                        ->filterByPrimaryKeys($this->authPermissionGroupsScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->authPermissionGroupsScheduledForDeletion = null;
                }
            }

            if ($this->collAuthPermissionGroups !== null) {
                foreach ($this->collAuthPermissionGroups as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = GroupsPeer::GID;
        if (null !== $this->gid) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . GroupsPeer::GID . ')');
        }
        if (null === $this->gid) {
            try {
                $stmt = $con->query("SELECT nextval('groups_gid_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->gid = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(GroupsPeer::GID)) {
            $modifiedColumns[':p' . $index++]  = '"gid"';
        }
        if ($this->isColumnModified(GroupsPeer::GROUP_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"group_name"';
        }
        if ($this->isColumnModified(GroupsPeer::ENABLED)) {
            $modifiedColumns[':p' . $index++]  = '"enabled"';
        }

        $sql = sprintf(
            'INSERT INTO "groups" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"gid"':
                        $stmt->bindValue($identifier, $this->gid, PDO::PARAM_INT);
                        break;
                    case '"group_name"':
                        $stmt->bindValue($identifier, $this->group_name, PDO::PARAM_STR);
                        break;
                    case '"enabled"':
                        $stmt->bindValue($identifier, $this->enabled, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = GroupsPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collAuthPermissionGroups !== null) {
                    foreach ($this->collAuthPermissionGroups as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = GroupsPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getGid();
                break;
            case 1:
                return $this->getGroupName();
                break;
            case 2:
                return $this->getEnabled();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Groups'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Groups'][$this->getPrimaryKey()] = true;
        $keys = GroupsPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getGid(),
            $keys[1] => $this->getGroupName(),
            $keys[2] => $this->getEnabled(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->collAuthPermissionGroups) {
                $result['AuthPermissionGroups'] = $this->collAuthPermissionGroups->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = GroupsPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setGid($value);
                break;
            case 1:
                $this->setGroupName($value);
                break;
            case 2:
                $this->setEnabled($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = GroupsPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setGid($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setGroupName($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setEnabled($arr[$keys[2]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(GroupsPeer::DATABASE_NAME);

        if ($this->isColumnModified(GroupsPeer::GID)) $criteria->add(GroupsPeer::GID, $this->gid);
        if ($this->isColumnModified(GroupsPeer::GROUP_NAME)) $criteria->add(GroupsPeer::GROUP_NAME, $this->group_name);
        if ($this->isColumnModified(GroupsPeer::ENABLED)) $criteria->add(GroupsPeer::ENABLED, $this->enabled);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(GroupsPeer::DATABASE_NAME);
        $criteria->add(GroupsPeer::GID, $this->gid);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getGid();
    }

    /**
     * Generic method to set the primary key (gid column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setGid($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getGid();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Groups (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setGroupName($this->getGroupName());
        $copyObj->setEnabled($this->getEnabled());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getAuthPermissionGroups() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAuthPermissionGroup($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setGid(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Groups Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return GroupsPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new GroupsPeer();
        }

        return self::$peer;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('AuthPermissionGroup' == $relationName) {
            $this->initAuthPermissionGroups();
        }
    }

    /**
     * Clears out the collAuthPermissionGroups collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Groups The current object (for fluent API support)
     * @see        addAuthPermissionGroups()
     */
    public function clearAuthPermissionGroups()
    {
        $this->collAuthPermissionGroups = null; // important to set this to null since that means it is uninitialized
        $this->collAuthPermissionGroupsPartial = null;

        return $this;
    }

    /**
     * reset is the collAuthPermissionGroups collection loaded partially
     *
     * @return void
     */
    public function resetPartialAuthPermissionGroups($v = true)
    {
        $this->collAuthPermissionGroupsPartial = $v;
    }

    /**
     * Initializes the collAuthPermissionGroups collection.
     *
     * By default this just sets the collAuthPermissionGroups collection to an empty array (like clearcollAuthPermissionGroups());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAuthPermissionGroups($overrideExisting = true)
    {
        if (null !== $this->collAuthPermissionGroups && !$overrideExisting) {
            return;
        }
        $this->collAuthPermissionGroups = new PropelObjectCollection();
        $this->collAuthPermissionGroups->setModel('AuthPermissionGroup');
    }

    /**
     * Gets an array of AuthPermissionGroup objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Groups is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|AuthPermissionGroup[] List of AuthPermissionGroup objects
     * @throws PropelException
     */
    public function getAuthPermissionGroups($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAuthPermissionGroupsPartial && !$this->isNew();
        if (null === $this->collAuthPermissionGroups || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAuthPermissionGroups) {
                // return empty collection
                $this->initAuthPermissionGroups();
            } else {
                $collAuthPermissionGroups = AuthPermissionGroupQuery::create(null, $criteria)
                    ->filterByAuthPermissionGroupGroups($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAuthPermissionGroupsPartial && count($collAuthPermissionGroups)) {
                      $this->initAuthPermissionGroups(false);

                      foreach ($collAuthPermissionGroups as $obj) {
                        if (false == $this->collAuthPermissionGroups->contains($obj)) {
                          $this->collAuthPermissionGroups->append($obj);
                        }
                      }

                      $this->collAuthPermissionGroupsPartial = true;
                    }

                    $collAuthPermissionGroups->getInternalIterator()->rewind();

                    return $collAuthPermissionGroups;
                }

                if ($partial && $this->collAuthPermissionGroups) {
                    foreach ($this->collAuthPermissionGroups as $obj) {
                        if ($obj->isNew()) {
                            $collAuthPermissionGroups[] = $obj;
                        }
                    }
                }

                $this->collAuthPermissionGroups = $collAuthPermissionGroups;
                $this->collAuthPermissionGroupsPartial = false;
            }
        }

        return $this->collAuthPermissionGroups;
    }

    /**
     * Sets a collection of AuthPermissionGroup objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $authPermissionGroups A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Groups The current object (for fluent API support)
     */
    public function setAuthPermissionGroups(PropelCollection $authPermissionGroups, PropelPDO $con = null)
    {
        $authPermissionGroupsToDelete = $this->getAuthPermissionGroups(new Criteria(), $con)->diff($authPermissionGroups);


        $this->authPermissionGroupsScheduledForDeletion = $authPermissionGroupsToDelete;

        foreach ($authPermissionGroupsToDelete as $authPermissionGroupRemoved) {
            $authPermissionGroupRemoved->setAuthPermissionGroupGroups(null);
        }

        $this->collAuthPermissionGroups = null;
        foreach ($authPermissionGroups as $authPermissionGroup) {
            $this->addAuthPermissionGroup($authPermissionGroup);
        }

        $this->collAuthPermissionGroups = $authPermissionGroups;
        $this->collAuthPermissionGroupsPartial = false;

        return $this;
    }

    /**
     * Returns the number of related AuthPermissionGroup objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related AuthPermissionGroup objects.
     * @throws PropelException
     */
    public function countAuthPermissionGroups(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAuthPermissionGroupsPartial && !$this->isNew();
        if (null === $this->collAuthPermissionGroups || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAuthPermissionGroups) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAuthPermissionGroups());
            }
            $query = AuthPermissionGroupQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAuthPermissionGroupGroups($this)
                ->count($con);
        }

        return count($this->collAuthPermissionGroups);
    }

    /**
     * Method called to associate a AuthPermissionGroup object to this object
     * through the AuthPermissionGroup foreign key attribute.
     *
     * @param    AuthPermissionGroup $l AuthPermissionGroup
     * @return Groups The current object (for fluent API support)
     */
    public function addAuthPermissionGroup(AuthPermissionGroup $l)
    {
        if ($this->collAuthPermissionGroups === null) {
            $this->initAuthPermissionGroups();
            $this->collAuthPermissionGroupsPartial = true;
        }

        if (!in_array($l, $this->collAuthPermissionGroups->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAuthPermissionGroup($l);

            if ($this->authPermissionGroupsScheduledForDeletion and $this->authPermissionGroupsScheduledForDeletion->contains($l)) {
                $this->authPermissionGroupsScheduledForDeletion->remove($this->authPermissionGroupsScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	AuthPermissionGroup $authPermissionGroup The authPermissionGroup object to add.
     */
    protected function doAddAuthPermissionGroup($authPermissionGroup)
    {
        $this->collAuthPermissionGroups[]= $authPermissionGroup;
        $authPermissionGroup->setAuthPermissionGroupGroups($this);
    }

    /**
     * @param	AuthPermissionGroup $authPermissionGroup The authPermissionGroup object to remove.
     * @return Groups The current object (for fluent API support)
     */
    public function removeAuthPermissionGroup($authPermissionGroup)
    {
        if ($this->getAuthPermissionGroups()->contains($authPermissionGroup)) {
            $this->collAuthPermissionGroups->remove($this->collAuthPermissionGroups->search($authPermissionGroup));
            if (null === $this->authPermissionGroupsScheduledForDeletion) {
                $this->authPermissionGroupsScheduledForDeletion = clone $this->collAuthPermissionGroups;
                $this->authPermissionGroupsScheduledForDeletion->clear();
            }
            $this->authPermissionGroupsScheduledForDeletion[]= $authPermissionGroup;
            $authPermissionGroup->setAuthPermissionGroupGroups(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Groups is new, it will return
     * an empty collection; or if this Groups has previously
     * been saved, it will retrieve related AuthPermissionGroups from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Groups.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|AuthPermissionGroup[] List of AuthPermissionGroup objects
     */
    public function getAuthPermissionGroupsJoinAuthPermissionGroupAuthPermission($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = AuthPermissionGroupQuery::create(null, $criteria);
        $query->joinWith('AuthPermissionGroupAuthPermission', $join_behavior);

        return $this->getAuthPermissionGroups($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->gid = null;
        $this->group_name = null;
        $this->enabled = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collAuthPermissionGroups) {
                foreach ($this->collAuthPermissionGroups as $o) {
                    $o->clearAllReferences($deep);
                }
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collAuthPermissionGroups instanceof PropelCollection) {
            $this->collAuthPermissionGroups->clearIterator();
        }
        $this->collAuthPermissionGroups = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(GroupsPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
