<?php

namespace Auth\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthPermission;
use Auth\AuthPermissionGroup;
use Auth\AuthPermissionGroupPeer;
use Auth\AuthPermissionGroupQuery;
use Auth\Groups;

/**
 * Base class that represents a query for the 'auth_permission_group' table.
 *
 *
 *
 * @method AuthPermissionGroupQuery orderById($order = Criteria::ASC) Order by the id column
 * @method AuthPermissionGroupQuery orderByGroups($order = Criteria::ASC) Order by the groups column
 * @method AuthPermissionGroupQuery orderByAuthPermission($order = Criteria::ASC) Order by the auth_permission column
 *
 * @method AuthPermissionGroupQuery groupById() Group by the id column
 * @method AuthPermissionGroupQuery groupByGroups() Group by the groups column
 * @method AuthPermissionGroupQuery groupByAuthPermission() Group by the auth_permission column
 *
 * @method AuthPermissionGroupQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AuthPermissionGroupQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AuthPermissionGroupQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AuthPermissionGroupQuery leftJoinAuthPermissionGroupAuthPermission($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPermissionGroupAuthPermission relation
 * @method AuthPermissionGroupQuery rightJoinAuthPermissionGroupAuthPermission($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPermissionGroupAuthPermission relation
 * @method AuthPermissionGroupQuery innerJoinAuthPermissionGroupAuthPermission($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPermissionGroupAuthPermission relation
 *
 * @method AuthPermissionGroupQuery leftJoinAuthPermissionGroupGroups($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPermissionGroupGroups relation
 * @method AuthPermissionGroupQuery rightJoinAuthPermissionGroupGroups($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPermissionGroupGroups relation
 * @method AuthPermissionGroupQuery innerJoinAuthPermissionGroupGroups($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPermissionGroupGroups relation
 *
 * @method AuthPermissionGroup findOne(PropelPDO $con = null) Return the first AuthPermissionGroup matching the query
 * @method AuthPermissionGroup findOneOrCreate(PropelPDO $con = null) Return the first AuthPermissionGroup matching the query, or a new AuthPermissionGroup object populated from the query conditions when no match is found
 *
 * @method AuthPermissionGroup findOneByGroups(int $groups) Return the first AuthPermissionGroup filtered by the groups column
 * @method AuthPermissionGroup findOneByAuthPermission(int $auth_permission) Return the first AuthPermissionGroup filtered by the auth_permission column
 *
 * @method array findById(int $id) Return AuthPermissionGroup objects filtered by the id column
 * @method array findByGroups(int $groups) Return AuthPermissionGroup objects filtered by the groups column
 * @method array findByAuthPermission(int $auth_permission) Return AuthPermissionGroup objects filtered by the auth_permission column
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseAuthPermissionGroupQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAuthPermissionGroupQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Auth\\AuthPermissionGroup';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AuthPermissionGroupQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AuthPermissionGroupQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AuthPermissionGroupQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AuthPermissionGroupQuery) {
            return $criteria;
        }
        $query = new AuthPermissionGroupQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   AuthPermissionGroup|AuthPermissionGroup[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AuthPermissionGroupPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AuthPermissionGroupPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthPermissionGroup A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthPermissionGroup A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "groups", "auth_permission" FROM "auth_permission_group" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new AuthPermissionGroup();
            $obj->hydrate($row);
            AuthPermissionGroupPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return AuthPermissionGroup|AuthPermissionGroup[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|AuthPermissionGroup[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AuthPermissionGroupPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AuthPermissionGroupPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(AuthPermissionGroupPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(AuthPermissionGroupPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPermissionGroupPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the groups column
     *
     * Example usage:
     * <code>
     * $query->filterByGroups(1234); // WHERE groups = 1234
     * $query->filterByGroups(array(12, 34)); // WHERE groups IN (12, 34)
     * $query->filterByGroups(array('min' => 12)); // WHERE groups >= 12
     * $query->filterByGroups(array('max' => 12)); // WHERE groups <= 12
     * </code>
     *
     * @see       filterByAuthPermissionGroupGroups()
     *
     * @param     mixed $groups The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function filterByGroups($groups = null, $comparison = null)
    {
        if (is_array($groups)) {
            $useMinMax = false;
            if (isset($groups['min'])) {
                $this->addUsingAlias(AuthPermissionGroupPeer::GROUPS, $groups['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($groups['max'])) {
                $this->addUsingAlias(AuthPermissionGroupPeer::GROUPS, $groups['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPermissionGroupPeer::GROUPS, $groups, $comparison);
    }

    /**
     * Filter the query on the auth_permission column
     *
     * Example usage:
     * <code>
     * $query->filterByAuthPermission(1234); // WHERE auth_permission = 1234
     * $query->filterByAuthPermission(array(12, 34)); // WHERE auth_permission IN (12, 34)
     * $query->filterByAuthPermission(array('min' => 12)); // WHERE auth_permission >= 12
     * $query->filterByAuthPermission(array('max' => 12)); // WHERE auth_permission <= 12
     * </code>
     *
     * @see       filterByAuthPermissionGroupAuthPermission()
     *
     * @param     mixed $authPermission The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function filterByAuthPermission($authPermission = null, $comparison = null)
    {
        if (is_array($authPermission)) {
            $useMinMax = false;
            if (isset($authPermission['min'])) {
                $this->addUsingAlias(AuthPermissionGroupPeer::AUTH_PERMISSION, $authPermission['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($authPermission['max'])) {
                $this->addUsingAlias(AuthPermissionGroupPeer::AUTH_PERMISSION, $authPermission['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPermissionGroupPeer::AUTH_PERMISSION, $authPermission, $comparison);
    }

    /**
     * Filter the query by a related AuthPermission object
     *
     * @param   AuthPermission|PropelObjectCollection $authPermission The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPermissionGroupQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPermissionGroupAuthPermission($authPermission, $comparison = null)
    {
        if ($authPermission instanceof AuthPermission) {
            return $this
                ->addUsingAlias(AuthPermissionGroupPeer::AUTH_PERMISSION, $authPermission->getId(), $comparison);
        } elseif ($authPermission instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AuthPermissionGroupPeer::AUTH_PERMISSION, $authPermission->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByAuthPermissionGroupAuthPermission() only accepts arguments of type AuthPermission or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPermissionGroupAuthPermission relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function joinAuthPermissionGroupAuthPermission($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPermissionGroupAuthPermission');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPermissionGroupAuthPermission');
        }

        return $this;
    }

    /**
     * Use the AuthPermissionGroupAuthPermission relation AuthPermission object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthPermissionQuery A secondary query class using the current class as primary query
     */
    public function useAuthPermissionGroupAuthPermissionQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPermissionGroupAuthPermission($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPermissionGroupAuthPermission', '\Auth\AuthPermissionQuery');
    }

    /**
     * Filter the query by a related Groups object
     *
     * @param   Groups|PropelObjectCollection $groups The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPermissionGroupQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPermissionGroupGroups($groups, $comparison = null)
    {
        if ($groups instanceof Groups) {
            return $this
                ->addUsingAlias(AuthPermissionGroupPeer::GROUPS, $groups->getGid(), $comparison);
        } elseif ($groups instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AuthPermissionGroupPeer::GROUPS, $groups->toKeyValue('PrimaryKey', 'Gid'), $comparison);
        } else {
            throw new PropelException('filterByAuthPermissionGroupGroups() only accepts arguments of type Groups or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPermissionGroupGroups relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function joinAuthPermissionGroupGroups($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPermissionGroupGroups');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPermissionGroupGroups');
        }

        return $this;
    }

    /**
     * Use the AuthPermissionGroupGroups relation Groups object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\GroupsQuery A secondary query class using the current class as primary query
     */
    public function useAuthPermissionGroupGroupsQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPermissionGroupGroups($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPermissionGroupGroups', '\Auth\GroupsQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   AuthPermissionGroup $authPermissionGroup Object to remove from the list of results
     *
     * @return AuthPermissionGroupQuery The current query, for fluid interface
     */
    public function prune($authPermissionGroup = null)
    {
        if ($authPermissionGroup) {
            $this->addUsingAlias(AuthPermissionGroupPeer::ID, $authPermissionGroup->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
