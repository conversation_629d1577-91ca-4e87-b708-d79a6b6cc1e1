<?php

namespace Auth\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Auth\Users;
use Auth\UsersPeer;
use Auth\UsersQuery;

/**
 * Base class that represents a row from the 'users' table.
 *
 *
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseUsers extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Auth\\UsersPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        UsersPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the uid field.
     * @var        int
     */
    protected $uid;

    /**
     * The value for the user_name field.
     * Note: this column has a database default value of: 'USER_NOT_DEFINED'
     * @var        string
     */
    protected $user_name;

    /**
     * The value for the user_password field.
     * Note: this column has a database default value of: 'PASSWORD_NOT_DEFINED'
     * @var        string
     */
    protected $user_password;

    /**
     * The value for the name field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $name;

    /**
     * The value for the surname field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $surname;

    /**
     * The value for the version field.
     * @var        string
     */
    protected $version;

    /**
     * The value for the lastcheck field.
     * @var        string
     */
    protected $lastcheck;

    /**
     * The value for the enabled field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $enabled;

    /**
     * The value for the user_type field.
     * @var        int
     */
    protected $user_type;

    /**
     * The value for the privelege field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $privelege;

    /**
     * The value for the email field.
     * @var        string
     */
    protected $email;

    /**
     * The value for the employee_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $employee_id;

    /**
     * The value for the modify_protocol field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $modify_protocol;

    /**
     * The value for the super_user field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $super_user;

    /**
     * The value for the expiration field.
     * @var        string
     */
    protected $expiration;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->user_name = 'USER_NOT_DEFINED';
        $this->user_password = 'PASSWORD_NOT_DEFINED';
        $this->name = '';
        $this->surname = '';
        $this->enabled = 0;
        $this->privelege = 0;
        $this->employee_id = 0;
        $this->modify_protocol = 0;
        $this->super_user = false;
    }

    /**
     * Initializes internal state of BaseUsers object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [uid] column value.
     *
     * @return int
     */
    public function getUid()
    {

        return $this->uid;
    }

    /**
     * Get the [user_name] column value.
     *
     * @return string
     */
    public function getUserName()
    {

        return $this->user_name;
    }

    /**
     * Get the [user_password] column value.
     *
     * @return string
     */
    public function getUserPassword()
    {

        return $this->user_password;
    }

    /**
     * Get the [name] column value.
     *
     * @return string
     */
    public function getName()
    {

        return $this->name;
    }

    /**
     * Get the [surname] column value.
     *
     * @return string
     */
    public function getSurname()
    {

        return $this->surname;
    }

    /**
     * Get the [version] column value.
     *
     * @return string
     */
    public function getVersion()
    {

        return $this->version;
    }

    /**
     * Get the [lastcheck] column value.
     *
     * @return string
     */
    public function getLastcheck()
    {

        return $this->lastcheck;
    }

    /**
     * Get the [enabled] column value.
     *
     * @return int
     */
    public function getEnabled()
    {

        return $this->enabled;
    }

    /**
     * Get the [user_type] column value.
     *
     * @return int
     */
    public function getUserType()
    {

        return $this->user_type;
    }

    /**
     * Get the [privelege] column value.
     *
     * @return int
     */
    public function getPrivelege()
    {

        return $this->privelege;
    }

    /**
     * Get the [email] column value.
     *
     * @return string
     */
    public function getEmail()
    {

        return $this->email;
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return int
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [modify_protocol] column value.
     *
     * @return int
     */
    public function getModifyProtocol()
    {

        return $this->modify_protocol;
    }

    /**
     * Get the [super_user] column value.
     *
     * @return boolean
     */
    public function getSuperUser()
    {

        return $this->super_user;
    }

    /**
     * Get the [expiration] column value.
     *
     * @return string
     */
    public function getExpiration()
    {

        return $this->expiration;
    }

    /**
     * Set the value of [uid] column.
     *
     * @param  int $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setUid($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->uid !== $v) {
            $this->uid = $v;
            $this->modifiedColumns[] = UsersPeer::UID;
        }


        return $this;
    } // setUid()

    /**
     * Set the value of [user_name] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setUserName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->user_name !== $v) {
            $this->user_name = $v;
            $this->modifiedColumns[] = UsersPeer::USER_NAME;
        }


        return $this;
    } // setUserName()

    /**
     * Set the value of [user_password] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setUserPassword($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->user_password !== $v) {
            $this->user_password = $v;
            $this->modifiedColumns[] = UsersPeer::USER_PASSWORD;
        }


        return $this;
    } // setUserPassword()

    /**
     * Set the value of [name] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->name !== $v) {
            $this->name = $v;
            $this->modifiedColumns[] = UsersPeer::NAME;
        }


        return $this;
    } // setName()

    /**
     * Set the value of [surname] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->surname !== $v) {
            $this->surname = $v;
            $this->modifiedColumns[] = UsersPeer::SURNAME;
        }


        return $this;
    } // setSurname()

    /**
     * Set the value of [version] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setVersion($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->version !== $v) {
            $this->version = $v;
            $this->modifiedColumns[] = UsersPeer::VERSION;
        }


        return $this;
    } // setVersion()

    /**
     * Set the value of [lastcheck] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setLastcheck($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->lastcheck !== $v) {
            $this->lastcheck = $v;
            $this->modifiedColumns[] = UsersPeer::LASTCHECK;
        }


        return $this;
    } // setLastcheck()

    /**
     * Set the value of [enabled] column.
     *
     * @param  int $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setEnabled($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->enabled !== $v) {
            $this->enabled = $v;
            $this->modifiedColumns[] = UsersPeer::ENABLED;
        }


        return $this;
    } // setEnabled()

    /**
     * Set the value of [user_type] column.
     *
     * @param  int $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setUserType($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->user_type !== $v) {
            $this->user_type = $v;
            $this->modifiedColumns[] = UsersPeer::USER_TYPE;
        }


        return $this;
    } // setUserType()

    /**
     * Set the value of [privelege] column.
     *
     * @param  int $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setPrivelege($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->privelege !== $v) {
            $this->privelege = $v;
            $this->modifiedColumns[] = UsersPeer::PRIVELEGE;
        }


        return $this;
    } // setPrivelege()

    /**
     * Set the value of [email] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setEmail($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->email !== $v) {
            $this->email = $v;
            $this->modifiedColumns[] = UsersPeer::EMAIL;
        }


        return $this;
    } // setEmail()

    /**
     * Set the value of [employee_id] column.
     *
     * @param  int $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = UsersPeer::EMPLOYEE_ID;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [modify_protocol] column.
     *
     * @param  int $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setModifyProtocol($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->modify_protocol !== $v) {
            $this->modify_protocol = $v;
            $this->modifiedColumns[] = UsersPeer::MODIFY_PROTOCOL;
        }


        return $this;
    } // setModifyProtocol()

    /**
     * Sets the value of the [super_user] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Users The current object (for fluent API support)
     */
    public function setSuperUser($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->super_user !== $v) {
            $this->super_user = $v;
            $this->modifiedColumns[] = UsersPeer::SUPER_USER;
        }


        return $this;
    } // setSuperUser()

    /**
     * Set the value of [expiration] column.
     *
     * @param  string $v new value
     * @return Users The current object (for fluent API support)
     */
    public function setExpiration($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->expiration !== $v) {
            $this->expiration = $v;
            $this->modifiedColumns[] = UsersPeer::EXPIRATION;
        }


        return $this;
    } // setExpiration()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->user_name !== 'USER_NOT_DEFINED') {
                return false;
            }

            if ($this->user_password !== 'PASSWORD_NOT_DEFINED') {
                return false;
            }

            if ($this->name !== '') {
                return false;
            }

            if ($this->surname !== '') {
                return false;
            }

            if ($this->enabled !== 0) {
                return false;
            }

            if ($this->privelege !== 0) {
                return false;
            }

            if ($this->employee_id !== 0) {
                return false;
            }

            if ($this->modify_protocol !== 0) {
                return false;
            }

            if ($this->super_user !== false) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->uid = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->user_name = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->user_password = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->name = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->surname = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->version = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->lastcheck = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->enabled = ($row[$startcol + 7] !== null) ? (int) $row[$startcol + 7] : null;
            $this->user_type = ($row[$startcol + 8] !== null) ? (int) $row[$startcol + 8] : null;
            $this->privelege = ($row[$startcol + 9] !== null) ? (int) $row[$startcol + 9] : null;
            $this->email = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->employee_id = ($row[$startcol + 11] !== null) ? (int) $row[$startcol + 11] : null;
            $this->modify_protocol = ($row[$startcol + 12] !== null) ? (int) $row[$startcol + 12] : null;
            $this->super_user = ($row[$startcol + 13] !== null) ? (boolean) $row[$startcol + 13] : null;
            $this->expiration = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 15; // 15 = UsersPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Users object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(UsersPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = UsersPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(UsersPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = UsersQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(UsersPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                UsersPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = UsersPeer::UID;
        if (null !== $this->uid) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . UsersPeer::UID . ')');
        }
        if (null === $this->uid) {
            try {
                $stmt = $con->query("SELECT nextval('uid_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->uid = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(UsersPeer::UID)) {
            $modifiedColumns[':p' . $index++]  = '"uid"';
        }
        if ($this->isColumnModified(UsersPeer::USER_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"user_name"';
        }
        if ($this->isColumnModified(UsersPeer::USER_PASSWORD)) {
            $modifiedColumns[':p' . $index++]  = '"user_password"';
        }
        if ($this->isColumnModified(UsersPeer::NAME)) {
            $modifiedColumns[':p' . $index++]  = '"name"';
        }
        if ($this->isColumnModified(UsersPeer::SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"surname"';
        }
        if ($this->isColumnModified(UsersPeer::VERSION)) {
            $modifiedColumns[':p' . $index++]  = '"version"';
        }
        if ($this->isColumnModified(UsersPeer::LASTCHECK)) {
            $modifiedColumns[':p' . $index++]  = '"lastcheck"';
        }
        if ($this->isColumnModified(UsersPeer::ENABLED)) {
            $modifiedColumns[':p' . $index++]  = '"enabled"';
        }
        if ($this->isColumnModified(UsersPeer::USER_TYPE)) {
            $modifiedColumns[':p' . $index++]  = '"user_type"';
        }
        if ($this->isColumnModified(UsersPeer::PRIVELEGE)) {
            $modifiedColumns[':p' . $index++]  = '"privelege"';
        }
        if ($this->isColumnModified(UsersPeer::EMAIL)) {
            $modifiedColumns[':p' . $index++]  = '"email"';
        }
        if ($this->isColumnModified(UsersPeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(UsersPeer::MODIFY_PROTOCOL)) {
            $modifiedColumns[':p' . $index++]  = '"modify_protocol"';
        }
        if ($this->isColumnModified(UsersPeer::SUPER_USER)) {
            $modifiedColumns[':p' . $index++]  = '"super_user"';
        }
        if ($this->isColumnModified(UsersPeer::EXPIRATION)) {
            $modifiedColumns[':p' . $index++]  = '"expiration"';
        }

        $sql = sprintf(
            'INSERT INTO "users" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"uid"':
                        $stmt->bindValue($identifier, $this->uid, PDO::PARAM_INT);
                        break;
                    case '"user_name"':
                        $stmt->bindValue($identifier, $this->user_name, PDO::PARAM_STR);
                        break;
                    case '"user_password"':
                        $stmt->bindValue($identifier, $this->user_password, PDO::PARAM_STR);
                        break;
                    case '"name"':
                        $stmt->bindValue($identifier, $this->name, PDO::PARAM_STR);
                        break;
                    case '"surname"':
                        $stmt->bindValue($identifier, $this->surname, PDO::PARAM_STR);
                        break;
                    case '"version"':
                        $stmt->bindValue($identifier, $this->version, PDO::PARAM_STR);
                        break;
                    case '"lastcheck"':
                        $stmt->bindValue($identifier, $this->lastcheck, PDO::PARAM_STR);
                        break;
                    case '"enabled"':
                        $stmt->bindValue($identifier, $this->enabled, PDO::PARAM_INT);
                        break;
                    case '"user_type"':
                        $stmt->bindValue($identifier, $this->user_type, PDO::PARAM_INT);
                        break;
                    case '"privelege"':
                        $stmt->bindValue($identifier, $this->privelege, PDO::PARAM_INT);
                        break;
                    case '"email"':
                        $stmt->bindValue($identifier, $this->email, PDO::PARAM_STR);
                        break;
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_INT);
                        break;
                    case '"modify_protocol"':
                        $stmt->bindValue($identifier, $this->modify_protocol, PDO::PARAM_INT);
                        break;
                    case '"super_user"':
                        $stmt->bindValue($identifier, $this->super_user, PDO::PARAM_BOOL);
                        break;
                    case '"expiration"':
                        $stmt->bindValue($identifier, $this->expiration, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = UsersPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = UsersPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getUid();
                break;
            case 1:
                return $this->getUserName();
                break;
            case 2:
                return $this->getUserPassword();
                break;
            case 3:
                return $this->getName();
                break;
            case 4:
                return $this->getSurname();
                break;
            case 5:
                return $this->getVersion();
                break;
            case 6:
                return $this->getLastcheck();
                break;
            case 7:
                return $this->getEnabled();
                break;
            case 8:
                return $this->getUserType();
                break;
            case 9:
                return $this->getPrivelege();
                break;
            case 10:
                return $this->getEmail();
                break;
            case 11:
                return $this->getEmployeeId();
                break;
            case 12:
                return $this->getModifyProtocol();
                break;
            case 13:
                return $this->getSuperUser();
                break;
            case 14:
                return $this->getExpiration();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array())
    {
        if (isset($alreadyDumpedObjects['Users'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Users'][$this->getPrimaryKey()] = true;
        $keys = UsersPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getUid(),
            $keys[1] => $this->getUserName(),
            $keys[2] => $this->getUserPassword(),
            $keys[3] => $this->getName(),
            $keys[4] => $this->getSurname(),
            $keys[5] => $this->getVersion(),
            $keys[6] => $this->getLastcheck(),
            $keys[7] => $this->getEnabled(),
            $keys[8] => $this->getUserType(),
            $keys[9] => $this->getPrivelege(),
            $keys[10] => $this->getEmail(),
            $keys[11] => $this->getEmployeeId(),
            $keys[12] => $this->getModifyProtocol(),
            $keys[13] => $this->getSuperUser(),
            $keys[14] => $this->getExpiration(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }


        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = UsersPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setUid($value);
                break;
            case 1:
                $this->setUserName($value);
                break;
            case 2:
                $this->setUserPassword($value);
                break;
            case 3:
                $this->setName($value);
                break;
            case 4:
                $this->setSurname($value);
                break;
            case 5:
                $this->setVersion($value);
                break;
            case 6:
                $this->setLastcheck($value);
                break;
            case 7:
                $this->setEnabled($value);
                break;
            case 8:
                $this->setUserType($value);
                break;
            case 9:
                $this->setPrivelege($value);
                break;
            case 10:
                $this->setEmail($value);
                break;
            case 11:
                $this->setEmployeeId($value);
                break;
            case 12:
                $this->setModifyProtocol($value);
                break;
            case 13:
                $this->setSuperUser($value);
                break;
            case 14:
                $this->setExpiration($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = UsersPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setUid($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setUserName($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setUserPassword($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setName($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setSurname($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setVersion($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setLastcheck($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setEnabled($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setUserType($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setPrivelege($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setEmail($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setEmployeeId($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setModifyProtocol($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setSuperUser($arr[$keys[13]]);
        if (array_key_exists($keys[14], $arr)) $this->setExpiration($arr[$keys[14]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(UsersPeer::DATABASE_NAME);

        if ($this->isColumnModified(UsersPeer::UID)) $criteria->add(UsersPeer::UID, $this->uid);
        if ($this->isColumnModified(UsersPeer::USER_NAME)) $criteria->add(UsersPeer::USER_NAME, $this->user_name);
        if ($this->isColumnModified(UsersPeer::USER_PASSWORD)) $criteria->add(UsersPeer::USER_PASSWORD, $this->user_password);
        if ($this->isColumnModified(UsersPeer::NAME)) $criteria->add(UsersPeer::NAME, $this->name);
        if ($this->isColumnModified(UsersPeer::SURNAME)) $criteria->add(UsersPeer::SURNAME, $this->surname);
        if ($this->isColumnModified(UsersPeer::VERSION)) $criteria->add(UsersPeer::VERSION, $this->version);
        if ($this->isColumnModified(UsersPeer::LASTCHECK)) $criteria->add(UsersPeer::LASTCHECK, $this->lastcheck);
        if ($this->isColumnModified(UsersPeer::ENABLED)) $criteria->add(UsersPeer::ENABLED, $this->enabled);
        if ($this->isColumnModified(UsersPeer::USER_TYPE)) $criteria->add(UsersPeer::USER_TYPE, $this->user_type);
        if ($this->isColumnModified(UsersPeer::PRIVELEGE)) $criteria->add(UsersPeer::PRIVELEGE, $this->privelege);
        if ($this->isColumnModified(UsersPeer::EMAIL)) $criteria->add(UsersPeer::EMAIL, $this->email);
        if ($this->isColumnModified(UsersPeer::EMPLOYEE_ID)) $criteria->add(UsersPeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(UsersPeer::MODIFY_PROTOCOL)) $criteria->add(UsersPeer::MODIFY_PROTOCOL, $this->modify_protocol);
        if ($this->isColumnModified(UsersPeer::SUPER_USER)) $criteria->add(UsersPeer::SUPER_USER, $this->super_user);
        if ($this->isColumnModified(UsersPeer::EXPIRATION)) $criteria->add(UsersPeer::EXPIRATION, $this->expiration);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(UsersPeer::DATABASE_NAME);
        $criteria->add(UsersPeer::UID, $this->uid);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getUid();
    }

    /**
     * Generic method to set the primary key (uid column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setUid($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getUid();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Users (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setUserName($this->getUserName());
        $copyObj->setUserPassword($this->getUserPassword());
        $copyObj->setName($this->getName());
        $copyObj->setSurname($this->getSurname());
        $copyObj->setVersion($this->getVersion());
        $copyObj->setLastcheck($this->getLastcheck());
        $copyObj->setEnabled($this->getEnabled());
        $copyObj->setUserType($this->getUserType());
        $copyObj->setPrivelege($this->getPrivelege());
        $copyObj->setEmail($this->getEmail());
        $copyObj->setEmployeeId($this->getEmployeeId());
        $copyObj->setModifyProtocol($this->getModifyProtocol());
        $copyObj->setSuperUser($this->getSuperUser());
        $copyObj->setExpiration($this->getExpiration());
        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setUid(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Users Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return UsersPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new UsersPeer();
        }

        return self::$peer;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->uid = null;
        $this->user_name = null;
        $this->user_password = null;
        $this->name = null;
        $this->surname = null;
        $this->version = null;
        $this->lastcheck = null;
        $this->enabled = null;
        $this->user_type = null;
        $this->privelege = null;
        $this->email = null;
        $this->employee_id = null;
        $this->modify_protocol = null;
        $this->super_user = null;
        $this->expiration = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(UsersPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
