<?php

namespace Auth\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthPermission;
use Auth\AuthSection;
use Auth\AuthSectionPeer;
use Auth\AuthSectionQuery;

/**
 * Base class that represents a query for the 'auth_section' table.
 *
 *
 *
 * @method AuthSectionQuery orderById($order = Criteria::ASC) Order by the id column
 * @method AuthSectionQuery orderByTitle($order = Criteria::ASC) Order by the title column
 *
 * @method AuthSectionQuery groupById() Group by the id column
 * @method AuthSectionQuery groupByTitle() Group by the title column
 *
 * @method AuthSectionQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AuthSectionQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AuthSectionQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AuthSectionQuery leftJoinAuthPermission($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPermission relation
 * @method AuthSectionQuery rightJoinAuthPermission($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPermission relation
 * @method AuthSectionQuery innerJoinAuthPermission($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPermission relation
 *
 * @method AuthSection findOne(PropelPDO $con = null) Return the first AuthSection matching the query
 * @method AuthSection findOneOrCreate(PropelPDO $con = null) Return the first AuthSection matching the query, or a new AuthSection object populated from the query conditions when no match is found
 *
 * @method AuthSection findOneByTitle(string $title) Return the first AuthSection filtered by the title column
 *
 * @method array findById(int $id) Return AuthSection objects filtered by the id column
 * @method array findByTitle(string $title) Return AuthSection objects filtered by the title column
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseAuthSectionQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAuthSectionQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Auth\\AuthSection';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AuthSectionQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AuthSectionQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AuthSectionQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AuthSectionQuery) {
            return $criteria;
        }
        $query = new AuthSectionQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   AuthSection|AuthSection[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AuthSectionPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AuthSectionPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthSection A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthSection A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "title" FROM "auth_section" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new AuthSection();
            $obj->hydrate($row);
            AuthSectionPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return AuthSection|AuthSection[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|AuthSection[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AuthSectionQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AuthSectionPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AuthSectionQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AuthSectionPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthSectionQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(AuthSectionPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(AuthSectionPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthSectionPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the title column
     *
     * Example usage:
     * <code>
     * $query->filterByTitle('fooValue');   // WHERE title = 'fooValue'
     * $query->filterByTitle('%fooValue%'); // WHERE title LIKE '%fooValue%'
     * </code>
     *
     * @param     string $title The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthSectionQuery The current query, for fluid interface
     */
    public function filterByTitle($title = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($title)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $title)) {
                $title = str_replace('*', '%', $title);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AuthSectionPeer::TITLE, $title, $comparison);
    }

    /**
     * Filter the query by a related AuthPermission object
     *
     * @param   AuthPermission|PropelObjectCollection $authPermission  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthSectionQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPermission($authPermission, $comparison = null)
    {
        if ($authPermission instanceof AuthPermission) {
            return $this
                ->addUsingAlias(AuthSectionPeer::ID, $authPermission->getAuthSection(), $comparison);
        } elseif ($authPermission instanceof PropelObjectCollection) {
            return $this
                ->useAuthPermissionQuery()
                ->filterByPrimaryKeys($authPermission->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAuthPermission() only accepts arguments of type AuthPermission or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPermission relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthSectionQuery The current query, for fluid interface
     */
    public function joinAuthPermission($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPermission');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPermission');
        }

        return $this;
    }

    /**
     * Use the AuthPermission relation AuthPermission object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthPermissionQuery A secondary query class using the current class as primary query
     */
    public function useAuthPermissionQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPermission($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPermission', '\Auth\AuthPermissionQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   AuthSection $authSection Object to remove from the list of results
     *
     * @return AuthSectionQuery The current query, for fluid interface
     */
    public function prune($authSection = null)
    {
        if ($authSection) {
            $this->addUsingAlias(AuthSectionPeer::ID, $authSection->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
