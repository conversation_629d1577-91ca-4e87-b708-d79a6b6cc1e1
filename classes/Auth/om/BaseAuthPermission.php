<?php

namespace Auth\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthElement;
use Auth\AuthElementQuery;
use Auth\AuthPath;
use Auth\AuthPathQuery;
use Auth\AuthPermission;
use Auth\AuthPermissionGroup;
use Auth\AuthPermissionGroupQuery;
use Auth\AuthPermissionPeer;
use Auth\AuthPermissionQuery;
use Auth\AuthSection;
use Auth\AuthSectionQuery;

/**
 * Base class that represents a row from the 'auth_permission' table.
 *
 *
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseAuthPermission extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Auth\\AuthPermissionPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        AuthPermissionPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id field.
     * @var        int
     */
    protected $id;

    /**
     * The value for the title field.
     * @var        string
     */
    protected $title;

    /**
     * The value for the super_user field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $super_user;

    /**
     * The value for the auth_section field.
     * @var        int
     */
    protected $auth_section;

    /**
     * @var        AuthSection
     */
    protected $aAuthPermissionToSection;

    /**
     * @var        PropelObjectCollection|AuthElement[] Collection to store aggregation of AuthElement objects.
     */
    protected $collAuthElements;
    protected $collAuthElementsPartial;

    /**
     * @var        PropelObjectCollection|AuthPath[] Collection to store aggregation of AuthPath objects.
     */
    protected $collAuthPaths;
    protected $collAuthPathsPartial;

    /**
     * @var        PropelObjectCollection|AuthPermissionGroup[] Collection to store aggregation of AuthPermissionGroup objects.
     */
    protected $collAuthPermissionGroups;
    protected $collAuthPermissionGroupsPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $authElementsScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $authPathsScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $authPermissionGroupsScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->super_user = false;
    }

    /**
     * Initializes internal state of BaseAuthPermission object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id] column value.
     *
     * @return int
     */
    public function getId()
    {

        return $this->id;
    }

    /**
     * Get the [title] column value.
     *
     * @return string
     */
    public function getTitle()
    {

        return $this->title;
    }

    /**
     * Get the [super_user] column value.
     *
     * @return boolean
     */
    public function getSuperUser()
    {

        return $this->super_user;
    }

    /**
     * Get the [auth_section] column value.
     *
     * @return int
     */
    public function getAuthSection()
    {

        return $this->auth_section;
    }

    /**
     * Set the value of [id] column.
     *
     * @param  int $v new value
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id !== $v) {
            $this->id = $v;
            $this->modifiedColumns[] = AuthPermissionPeer::ID;
        }


        return $this;
    } // setId()

    /**
     * Set the value of [title] column.
     *
     * @param  string $v new value
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setTitle($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->title !== $v) {
            $this->title = $v;
            $this->modifiedColumns[] = AuthPermissionPeer::TITLE;
        }


        return $this;
    } // setTitle()

    /**
     * Sets the value of the [super_user] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setSuperUser($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->super_user !== $v) {
            $this->super_user = $v;
            $this->modifiedColumns[] = AuthPermissionPeer::SUPER_USER;
        }


        return $this;
    } // setSuperUser()

    /**
     * Set the value of [auth_section] column.
     *
     * @param  int $v new value
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setAuthSection($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->auth_section !== $v) {
            $this->auth_section = $v;
            $this->modifiedColumns[] = AuthPermissionPeer::AUTH_SECTION;
        }

        if ($this->aAuthPermissionToSection !== null && $this->aAuthPermissionToSection->getId() !== $v) {
            $this->aAuthPermissionToSection = null;
        }


        return $this;
    } // setAuthSection()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->super_user !== false) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->title = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->super_user = ($row[$startcol + 2] !== null) ? (boolean) $row[$startcol + 2] : null;
            $this->auth_section = ($row[$startcol + 3] !== null) ? (int) $row[$startcol + 3] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 4; // 4 = AuthPermissionPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating AuthPermission object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aAuthPermissionToSection !== null && $this->auth_section !== $this->aAuthPermissionToSection->getId()) {
            $this->aAuthPermissionToSection = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AuthPermissionPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = AuthPermissionPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aAuthPermissionToSection = null;
            $this->collAuthElements = null;

            $this->collAuthPaths = null;

            $this->collAuthPermissionGroups = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AuthPermissionPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = AuthPermissionQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AuthPermissionPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                AuthPermissionPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aAuthPermissionToSection !== null) {
                if ($this->aAuthPermissionToSection->isModified() || $this->aAuthPermissionToSection->isNew()) {
                    $affectedRows += $this->aAuthPermissionToSection->save($con);
                }
                $this->setAuthPermissionToSection($this->aAuthPermissionToSection);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->authElementsScheduledForDeletion !== null) {
                if (!$this->authElementsScheduledForDeletion->isEmpty()) {
                    AuthElementQuery::create()
                        ->filterByPrimaryKeys($this->authElementsScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->authElementsScheduledForDeletion = null;
                }
            }

            if ($this->collAuthElements !== null) {
                foreach ($this->collAuthElements as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->authPathsScheduledForDeletion !== null) {
                if (!$this->authPathsScheduledForDeletion->isEmpty()) {
                    AuthPathQuery::create()
                        ->filterByPrimaryKeys($this->authPathsScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->authPathsScheduledForDeletion = null;
                }
            }

            if ($this->collAuthPaths !== null) {
                foreach ($this->collAuthPaths as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->authPermissionGroupsScheduledForDeletion !== null) {
                if (!$this->authPermissionGroupsScheduledForDeletion->isEmpty()) {
                    AuthPermissionGroupQuery::create()
                        ->filterByPrimaryKeys($this->authPermissionGroupsScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->authPermissionGroupsScheduledForDeletion = null;
                }
            }

            if ($this->collAuthPermissionGroups !== null) {
                foreach ($this->collAuthPermissionGroups as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = AuthPermissionPeer::ID;
        if (null !== $this->id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . AuthPermissionPeer::ID . ')');
        }
        if (null === $this->id) {
            try {
                $stmt = $con->query("SELECT nextval('auth_permission_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(AuthPermissionPeer::ID)) {
            $modifiedColumns[':p' . $index++]  = '"id"';
        }
        if ($this->isColumnModified(AuthPermissionPeer::TITLE)) {
            $modifiedColumns[':p' . $index++]  = '"title"';
        }
        if ($this->isColumnModified(AuthPermissionPeer::SUPER_USER)) {
            $modifiedColumns[':p' . $index++]  = '"super_user"';
        }
        if ($this->isColumnModified(AuthPermissionPeer::AUTH_SECTION)) {
            $modifiedColumns[':p' . $index++]  = '"auth_section"';
        }

        $sql = sprintf(
            'INSERT INTO "auth_permissions" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id"':
                        $stmt->bindValue($identifier, $this->id, PDO::PARAM_INT);
                        break;
                    case '"title"':
                        $stmt->bindValue($identifier, $this->title, PDO::PARAM_STR);
                        break;
                    case '"super_user"':
                        $stmt->bindValue($identifier, $this->super_user, PDO::PARAM_BOOL);
                        break;
                    case '"auth_section"':
                        $stmt->bindValue($identifier, $this->auth_section, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aAuthPermissionToSection !== null) {
                if (!$this->aAuthPermissionToSection->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aAuthPermissionToSection->getValidationFailures());
                }
            }


            if (($retval = AuthPermissionPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collAuthElements !== null) {
                    foreach ($this->collAuthElements as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collAuthPaths !== null) {
                    foreach ($this->collAuthPaths as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collAuthPermissionGroups !== null) {
                    foreach ($this->collAuthPermissionGroups as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AuthPermissionPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getId();
                break;
            case 1:
                return $this->getTitle();
                break;
            case 2:
                return $this->getSuperUser();
                break;
            case 3:
                return $this->getAuthSection();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['AuthPermission'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['AuthPermission'][$this->getPrimaryKey()] = true;
        $keys = AuthPermissionPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getId(),
            $keys[1] => $this->getTitle(),
            $keys[2] => $this->getSuperUser(),
            $keys[3] => $this->getAuthSection(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aAuthPermissionToSection) {
                $result['AuthPermissionToSection'] = $this->aAuthPermissionToSection->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collAuthElements) {
                $result['AuthElements'] = $this->collAuthElements->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collAuthPaths) {
                $result['AuthPaths'] = $this->collAuthPaths->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collAuthPermissionGroups) {
                $result['AuthPermissionGroups'] = $this->collAuthPermissionGroups->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AuthPermissionPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setId($value);
                break;
            case 1:
                $this->setTitle($value);
                break;
            case 2:
                $this->setSuperUser($value);
                break;
            case 3:
                $this->setAuthSection($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = AuthPermissionPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setTitle($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setSuperUser($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setAuthSection($arr[$keys[3]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(AuthPermissionPeer::DATABASE_NAME);

        if ($this->isColumnModified(AuthPermissionPeer::ID)) $criteria->add(AuthPermissionPeer::ID, $this->id);
        if ($this->isColumnModified(AuthPermissionPeer::TITLE)) $criteria->add(AuthPermissionPeer::TITLE, $this->title);
        if ($this->isColumnModified(AuthPermissionPeer::SUPER_USER)) $criteria->add(AuthPermissionPeer::SUPER_USER, $this->super_user);
        if ($this->isColumnModified(AuthPermissionPeer::AUTH_SECTION)) $criteria->add(AuthPermissionPeer::AUTH_SECTION, $this->auth_section);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(AuthPermissionPeer::DATABASE_NAME);
        $criteria->add(AuthPermissionPeer::ID, $this->id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getId();
    }

    /**
     * Generic method to set the primary key (id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of AuthPermission (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setTitle($this->getTitle());
        $copyObj->setSuperUser($this->getSuperUser());
        $copyObj->setAuthSection($this->getAuthSection());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getAuthElements() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAuthElement($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getAuthPaths() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAuthPath($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getAuthPermissionGroups() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAuthPermissionGroup($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return AuthPermission Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return AuthPermissionPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new AuthPermissionPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a AuthSection object.
     *
     * @param                  AuthSection $v
     * @return AuthPermission The current object (for fluent API support)
     * @throws PropelException
     */
    public function setAuthPermissionToSection(AuthSection $v = null)
    {
        if ($v === null) {
            $this->setAuthSection(NULL);
        } else {
            $this->setAuthSection($v->getId());
        }

        $this->aAuthPermissionToSection = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the AuthSection object, it will not be re-added.
        if ($v !== null) {
            $v->addAuthPermission($this);
        }


        return $this;
    }


    /**
     * Get the associated AuthSection object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return AuthSection The associated AuthSection object.
     * @throws PropelException
     */
    public function getAuthPermissionToSection(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aAuthPermissionToSection === null && ($this->auth_section !== null) && $doQuery) {
            $this->aAuthPermissionToSection = AuthSectionQuery::create()->findPk($this->auth_section, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aAuthPermissionToSection->addAuthPermissions($this);
             */
        }

        return $this->aAuthPermissionToSection;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('AuthElement' == $relationName) {
            $this->initAuthElements();
        }
        if ('AuthPath' == $relationName) {
            $this->initAuthPaths();
        }
        if ('AuthPermissionGroup' == $relationName) {
            $this->initAuthPermissionGroups();
        }
    }

    /**
     * Clears out the collAuthElements collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AuthPermission The current object (for fluent API support)
     * @see        addAuthElements()
     */
    public function clearAuthElements()
    {
        $this->collAuthElements = null; // important to set this to null since that means it is uninitialized
        $this->collAuthElementsPartial = null;

        return $this;
    }

    /**
     * reset is the collAuthElements collection loaded partially
     *
     * @return void
     */
    public function resetPartialAuthElements($v = true)
    {
        $this->collAuthElementsPartial = $v;
    }

    /**
     * Initializes the collAuthElements collection.
     *
     * By default this just sets the collAuthElements collection to an empty array (like clearcollAuthElements());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAuthElements($overrideExisting = true)
    {
        if (null !== $this->collAuthElements && !$overrideExisting) {
            return;
        }
        $this->collAuthElements = new PropelObjectCollection();
        $this->collAuthElements->setModel('AuthElement');
    }

    /**
     * Gets an array of AuthElement objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AuthPermission is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|AuthElement[] List of AuthElement objects
     * @throws PropelException
     */
    public function getAuthElements($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAuthElementsPartial && !$this->isNew();
        if (null === $this->collAuthElements || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAuthElements) {
                // return empty collection
                $this->initAuthElements();
            } else {
                $collAuthElements = AuthElementQuery::create(null, $criteria)
                    ->filterByAuthElementAuthPermission($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAuthElementsPartial && count($collAuthElements)) {
                      $this->initAuthElements(false);

                      foreach ($collAuthElements as $obj) {
                        if (false == $this->collAuthElements->contains($obj)) {
                          $this->collAuthElements->append($obj);
                        }
                      }

                      $this->collAuthElementsPartial = true;
                    }

                    $collAuthElements->getInternalIterator()->rewind();

                    return $collAuthElements;
                }

                if ($partial && $this->collAuthElements) {
                    foreach ($this->collAuthElements as $obj) {
                        if ($obj->isNew()) {
                            $collAuthElements[] = $obj;
                        }
                    }
                }

                $this->collAuthElements = $collAuthElements;
                $this->collAuthElementsPartial = false;
            }
        }

        return $this->collAuthElements;
    }

    /**
     * Sets a collection of AuthElement objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $authElements A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setAuthElements(PropelCollection $authElements, PropelPDO $con = null)
    {
        $authElementsToDelete = $this->getAuthElements(new Criteria(), $con)->diff($authElements);


        $this->authElementsScheduledForDeletion = $authElementsToDelete;

        foreach ($authElementsToDelete as $authElementRemoved) {
            $authElementRemoved->setAuthElementAuthPermission(null);
        }

        $this->collAuthElements = null;
        foreach ($authElements as $authElement) {
            $this->addAuthElement($authElement);
        }

        $this->collAuthElements = $authElements;
        $this->collAuthElementsPartial = false;

        return $this;
    }

    /**
     * Returns the number of related AuthElement objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related AuthElement objects.
     * @throws PropelException
     */
    public function countAuthElements(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAuthElementsPartial && !$this->isNew();
        if (null === $this->collAuthElements || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAuthElements) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAuthElements());
            }
            $query = AuthElementQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAuthElementAuthPermission($this)
                ->count($con);
        }

        return count($this->collAuthElements);
    }

    /**
     * Method called to associate a AuthElement object to this object
     * through the AuthElement foreign key attribute.
     *
     * @param    AuthElement $l AuthElement
     * @return AuthPermission The current object (for fluent API support)
     */
    public function addAuthElement(AuthElement $l)
    {
        if ($this->collAuthElements === null) {
            $this->initAuthElements();
            $this->collAuthElementsPartial = true;
        }

        if (!in_array($l, $this->collAuthElements->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAuthElement($l);

            if ($this->authElementsScheduledForDeletion and $this->authElementsScheduledForDeletion->contains($l)) {
                $this->authElementsScheduledForDeletion->remove($this->authElementsScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	AuthElement $authElement The authElement object to add.
     */
    protected function doAddAuthElement($authElement)
    {
        $this->collAuthElements[]= $authElement;
        $authElement->setAuthElementAuthPermission($this);
    }

    /**
     * @param	AuthElement $authElement The authElement object to remove.
     * @return AuthPermission The current object (for fluent API support)
     */
    public function removeAuthElement($authElement)
    {
        if ($this->getAuthElements()->contains($authElement)) {
            $this->collAuthElements->remove($this->collAuthElements->search($authElement));
            if (null === $this->authElementsScheduledForDeletion) {
                $this->authElementsScheduledForDeletion = clone $this->collAuthElements;
                $this->authElementsScheduledForDeletion->clear();
            }
            $this->authElementsScheduledForDeletion[]= $authElement;
            $authElement->setAuthElementAuthPermission(null);
        }

        return $this;
    }

    /**
     * Clears out the collAuthPaths collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AuthPermission The current object (for fluent API support)
     * @see        addAuthPaths()
     */
    public function clearAuthPaths()
    {
        $this->collAuthPaths = null; // important to set this to null since that means it is uninitialized
        $this->collAuthPathsPartial = null;

        return $this;
    }

    /**
     * reset is the collAuthPaths collection loaded partially
     *
     * @return void
     */
    public function resetPartialAuthPaths($v = true)
    {
        $this->collAuthPathsPartial = $v;
    }

    /**
     * Initializes the collAuthPaths collection.
     *
     * By default this just sets the collAuthPaths collection to an empty array (like clearcollAuthPaths());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAuthPaths($overrideExisting = true)
    {
        if (null !== $this->collAuthPaths && !$overrideExisting) {
            return;
        }
        $this->collAuthPaths = new PropelObjectCollection();
        $this->collAuthPaths->setModel('AuthPath');
    }

    /**
     * Gets an array of AuthPath objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AuthPermission is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|AuthPath[] List of AuthPath objects
     * @throws PropelException
     */
    public function getAuthPaths($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAuthPathsPartial && !$this->isNew();
        if (null === $this->collAuthPaths || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAuthPaths) {
                // return empty collection
                $this->initAuthPaths();
            } else {
                $collAuthPaths = AuthPathQuery::create(null, $criteria)
                    ->filterByAuthPathAuthPermission($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAuthPathsPartial && count($collAuthPaths)) {
                      $this->initAuthPaths(false);

                      foreach ($collAuthPaths as $obj) {
                        if (false == $this->collAuthPaths->contains($obj)) {
                          $this->collAuthPaths->append($obj);
                        }
                      }

                      $this->collAuthPathsPartial = true;
                    }

                    $collAuthPaths->getInternalIterator()->rewind();

                    return $collAuthPaths;
                }

                if ($partial && $this->collAuthPaths) {
                    foreach ($this->collAuthPaths as $obj) {
                        if ($obj->isNew()) {
                            $collAuthPaths[] = $obj;
                        }
                    }
                }

                $this->collAuthPaths = $collAuthPaths;
                $this->collAuthPathsPartial = false;
            }
        }

        return $this->collAuthPaths;
    }

    /**
     * Sets a collection of AuthPath objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $authPaths A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setAuthPaths(PropelCollection $authPaths, PropelPDO $con = null)
    {
        $authPathsToDelete = $this->getAuthPaths(new Criteria(), $con)->diff($authPaths);


        $this->authPathsScheduledForDeletion = $authPathsToDelete;

        foreach ($authPathsToDelete as $authPathRemoved) {
            $authPathRemoved->setAuthPathAuthPermission(null);
        }

        $this->collAuthPaths = null;
        foreach ($authPaths as $authPath) {
            $this->addAuthPath($authPath);
        }

        $this->collAuthPaths = $authPaths;
        $this->collAuthPathsPartial = false;

        return $this;
    }

    /**
     * Returns the number of related AuthPath objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related AuthPath objects.
     * @throws PropelException
     */
    public function countAuthPaths(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAuthPathsPartial && !$this->isNew();
        if (null === $this->collAuthPaths || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAuthPaths) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAuthPaths());
            }
            $query = AuthPathQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAuthPathAuthPermission($this)
                ->count($con);
        }

        return count($this->collAuthPaths);
    }

    /**
     * Method called to associate a AuthPath object to this object
     * through the AuthPath foreign key attribute.
     *
     * @param    AuthPath $l AuthPath
     * @return AuthPermission The current object (for fluent API support)
     */
    public function addAuthPath(AuthPath $l)
    {
        if ($this->collAuthPaths === null) {
            $this->initAuthPaths();
            $this->collAuthPathsPartial = true;
        }

        if (!in_array($l, $this->collAuthPaths->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAuthPath($l);

            if ($this->authPathsScheduledForDeletion and $this->authPathsScheduledForDeletion->contains($l)) {
                $this->authPathsScheduledForDeletion->remove($this->authPathsScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	AuthPath $authPath The authPath object to add.
     */
    protected function doAddAuthPath($authPath)
    {
        $this->collAuthPaths[]= $authPath;
        $authPath->setAuthPathAuthPermission($this);
    }

    /**
     * @param	AuthPath $authPath The authPath object to remove.
     * @return AuthPermission The current object (for fluent API support)
     */
    public function removeAuthPath($authPath)
    {
        if ($this->getAuthPaths()->contains($authPath)) {
            $this->collAuthPaths->remove($this->collAuthPaths->search($authPath));
            if (null === $this->authPathsScheduledForDeletion) {
                $this->authPathsScheduledForDeletion = clone $this->collAuthPaths;
                $this->authPathsScheduledForDeletion->clear();
            }
            $this->authPathsScheduledForDeletion[]= $authPath;
            $authPath->setAuthPathAuthPermission(null);
        }

        return $this;
    }

    /**
     * Clears out the collAuthPermissionGroups collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AuthPermission The current object (for fluent API support)
     * @see        addAuthPermissionGroups()
     */
    public function clearAuthPermissionGroups()
    {
        $this->collAuthPermissionGroups = null; // important to set this to null since that means it is uninitialized
        $this->collAuthPermissionGroupsPartial = null;

        return $this;
    }

    /**
     * reset is the collAuthPermissionGroups collection loaded partially
     *
     * @return void
     */
    public function resetPartialAuthPermissionGroups($v = true)
    {
        $this->collAuthPermissionGroupsPartial = $v;
    }

    /**
     * Initializes the collAuthPermissionGroups collection.
     *
     * By default this just sets the collAuthPermissionGroups collection to an empty array (like clearcollAuthPermissionGroups());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAuthPermissionGroups($overrideExisting = true)
    {
        if (null !== $this->collAuthPermissionGroups && !$overrideExisting) {
            return;
        }
        $this->collAuthPermissionGroups = new PropelObjectCollection();
        $this->collAuthPermissionGroups->setModel('AuthPermissionGroup');
    }

    /**
     * Gets an array of AuthPermissionGroup objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AuthPermission is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|AuthPermissionGroup[] List of AuthPermissionGroup objects
     * @throws PropelException
     */
    public function getAuthPermissionGroups($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAuthPermissionGroupsPartial && !$this->isNew();
        if (null === $this->collAuthPermissionGroups || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAuthPermissionGroups) {
                // return empty collection
                $this->initAuthPermissionGroups();
            } else {
                $collAuthPermissionGroups = AuthPermissionGroupQuery::create(null, $criteria)
                    ->filterByAuthPermissionGroupAuthPermission($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAuthPermissionGroupsPartial && count($collAuthPermissionGroups)) {
                      $this->initAuthPermissionGroups(false);

                      foreach ($collAuthPermissionGroups as $obj) {
                        if (false == $this->collAuthPermissionGroups->contains($obj)) {
                          $this->collAuthPermissionGroups->append($obj);
                        }
                      }

                      $this->collAuthPermissionGroupsPartial = true;
                    }

                    $collAuthPermissionGroups->getInternalIterator()->rewind();

                    return $collAuthPermissionGroups;
                }

                if ($partial && $this->collAuthPermissionGroups) {
                    foreach ($this->collAuthPermissionGroups as $obj) {
                        if ($obj->isNew()) {
                            $collAuthPermissionGroups[] = $obj;
                        }
                    }
                }

                $this->collAuthPermissionGroups = $collAuthPermissionGroups;
                $this->collAuthPermissionGroupsPartial = false;
            }
        }

        return $this->collAuthPermissionGroups;
    }

    /**
     * Sets a collection of AuthPermissionGroup objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $authPermissionGroups A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AuthPermission The current object (for fluent API support)
     */
    public function setAuthPermissionGroups(PropelCollection $authPermissionGroups, PropelPDO $con = null)
    {
        $authPermissionGroupsToDelete = $this->getAuthPermissionGroups(new Criteria(), $con)->diff($authPermissionGroups);


        $this->authPermissionGroupsScheduledForDeletion = $authPermissionGroupsToDelete;

        foreach ($authPermissionGroupsToDelete as $authPermissionGroupRemoved) {
            $authPermissionGroupRemoved->setAuthPermissionGroupAuthPermission(null);
        }

        $this->collAuthPermissionGroups = null;
        foreach ($authPermissionGroups as $authPermissionGroup) {
            $this->addAuthPermissionGroup($authPermissionGroup);
        }

        $this->collAuthPermissionGroups = $authPermissionGroups;
        $this->collAuthPermissionGroupsPartial = false;

        return $this;
    }

    /**
     * Returns the number of related AuthPermissionGroup objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related AuthPermissionGroup objects.
     * @throws PropelException
     */
    public function countAuthPermissionGroups(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAuthPermissionGroupsPartial && !$this->isNew();
        if (null === $this->collAuthPermissionGroups || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAuthPermissionGroups) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAuthPermissionGroups());
            }
            $query = AuthPermissionGroupQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAuthPermissionGroupAuthPermission($this)
                ->count($con);
        }

        return count($this->collAuthPermissionGroups);
    }

    /**
     * Method called to associate a AuthPermissionGroup object to this object
     * through the AuthPermissionGroup foreign key attribute.
     *
     * @param    AuthPermissionGroup $l AuthPermissionGroup
     * @return AuthPermission The current object (for fluent API support)
     */
    public function addAuthPermissionGroup(AuthPermissionGroup $l)
    {
        if ($this->collAuthPermissionGroups === null) {
            $this->initAuthPermissionGroups();
            $this->collAuthPermissionGroupsPartial = true;
        }

        if (!in_array($l, $this->collAuthPermissionGroups->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAuthPermissionGroup($l);

            if ($this->authPermissionGroupsScheduledForDeletion and $this->authPermissionGroupsScheduledForDeletion->contains($l)) {
                $this->authPermissionGroupsScheduledForDeletion->remove($this->authPermissionGroupsScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	AuthPermissionGroup $authPermissionGroup The authPermissionGroup object to add.
     */
    protected function doAddAuthPermissionGroup($authPermissionGroup)
    {
        $this->collAuthPermissionGroups[]= $authPermissionGroup;
        $authPermissionGroup->setAuthPermissionGroupAuthPermission($this);
    }

    /**
     * @param	AuthPermissionGroup $authPermissionGroup The authPermissionGroup object to remove.
     * @return AuthPermission The current object (for fluent API support)
     */
    public function removeAuthPermissionGroup($authPermissionGroup)
    {
        if ($this->getAuthPermissionGroups()->contains($authPermissionGroup)) {
            $this->collAuthPermissionGroups->remove($this->collAuthPermissionGroups->search($authPermissionGroup));
            if (null === $this->authPermissionGroupsScheduledForDeletion) {
                $this->authPermissionGroupsScheduledForDeletion = clone $this->collAuthPermissionGroups;
                $this->authPermissionGroupsScheduledForDeletion->clear();
            }
            $this->authPermissionGroupsScheduledForDeletion[]= $authPermissionGroup;
            $authPermissionGroup->setAuthPermissionGroupAuthPermission(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this AuthPermission is new, it will return
     * an empty collection; or if this AuthPermission has previously
     * been saved, it will retrieve related AuthPermissionGroups from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in AuthPermission.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|AuthPermissionGroup[] List of AuthPermissionGroup objects
     */
    public function getAuthPermissionGroupsJoinAuthPermissionGroupGroups($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = AuthPermissionGroupQuery::create(null, $criteria);
        $query->joinWith('AuthPermissionGroupGroups', $join_behavior);

        return $this->getAuthPermissionGroups($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id = null;
        $this->title = null;
        $this->super_user = null;
        $this->auth_section = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collAuthElements) {
                foreach ($this->collAuthElements as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collAuthPaths) {
                foreach ($this->collAuthPaths as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collAuthPermissionGroups) {
                foreach ($this->collAuthPermissionGroups as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aAuthPermissionToSection instanceof Persistent) {
              $this->aAuthPermissionToSection->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collAuthElements instanceof PropelCollection) {
            $this->collAuthElements->clearIterator();
        }
        $this->collAuthElements = null;
        if ($this->collAuthPaths instanceof PropelCollection) {
            $this->collAuthPaths->clearIterator();
        }
        $this->collAuthPaths = null;
        if ($this->collAuthPermissionGroups instanceof PropelCollection) {
            $this->collAuthPermissionGroups->clearIterator();
        }
        $this->collAuthPermissionGroups = null;
        $this->aAuthPermissionToSection = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(AuthPermissionPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
