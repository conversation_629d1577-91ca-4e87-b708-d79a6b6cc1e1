<?php

namespace Auth\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthPermissionGroup;
use Auth\Groups;
use Auth\GroupsPeer;
use Auth\GroupsQuery;

/**
 * Base class that represents a query for the 'groups' table.
 *
 *
 *
 * @method GroupsQuery orderByGid($order = Criteria::ASC) Order by the gid column
 * @method GroupsQuery orderByGroupName($order = Criteria::ASC) Order by the group_name column
 * @method GroupsQuery orderByEnabled($order = Criteria::ASC) Order by the enabled column
 *
 * @method GroupsQuery groupByGid() Group by the gid column
 * @method GroupsQuery groupByGroupName() Group by the group_name column
 * @method GroupsQuery groupByEnabled() Group by the enabled column
 *
 * @method GroupsQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method GroupsQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method GroupsQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method GroupsQuery leftJoinAuthPermissionGroup($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPermissionGroup relation
 * @method GroupsQuery rightJoinAuthPermissionGroup($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPermissionGroup relation
 * @method GroupsQuery innerJoinAuthPermissionGroup($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPermissionGroup relation
 *
 * @method Groups findOne(PropelPDO $con = null) Return the first Groups matching the query
 * @method Groups findOneOrCreate(PropelPDO $con = null) Return the first Groups matching the query, or a new Groups object populated from the query conditions when no match is found
 *
 * @method Groups findOneByGroupName(string $group_name) Return the first Groups filtered by the group_name column
 * @method Groups findOneByEnabled(int $enabled) Return the first Groups filtered by the enabled column
 *
 * @method array findByGid(int $gid) Return Groups objects filtered by the gid column
 * @method array findByGroupName(string $group_name) Return Groups objects filtered by the group_name column
 * @method array findByEnabled(int $enabled) Return Groups objects filtered by the enabled column
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseGroupsQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseGroupsQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Auth\\Groups';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new GroupsQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   GroupsQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return GroupsQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof GroupsQuery) {
            return $criteria;
        }
        $query = new GroupsQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Groups|Groups[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = GroupsPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(GroupsPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Groups A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByGid($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Groups A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "gid", "group_name", "enabled" FROM "groups" WHERE "gid" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Groups();
            $obj->hydrate($row);
            GroupsPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Groups|Groups[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Groups[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(GroupsPeer::GID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(GroupsPeer::GID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the gid column
     *
     * Example usage:
     * <code>
     * $query->filterByGid(1234); // WHERE gid = 1234
     * $query->filterByGid(array(12, 34)); // WHERE gid IN (12, 34)
     * $query->filterByGid(array('min' => 12)); // WHERE gid >= 12
     * $query->filterByGid(array('max' => 12)); // WHERE gid <= 12
     * </code>
     *
     * @param     mixed $gid The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function filterByGid($gid = null, $comparison = null)
    {
        if (is_array($gid)) {
            $useMinMax = false;
            if (isset($gid['min'])) {
                $this->addUsingAlias(GroupsPeer::GID, $gid['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($gid['max'])) {
                $this->addUsingAlias(GroupsPeer::GID, $gid['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(GroupsPeer::GID, $gid, $comparison);
    }

    /**
     * Filter the query on the group_name column
     *
     * Example usage:
     * <code>
     * $query->filterByGroupName('fooValue');   // WHERE group_name = 'fooValue'
     * $query->filterByGroupName('%fooValue%'); // WHERE group_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $groupName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function filterByGroupName($groupName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($groupName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $groupName)) {
                $groupName = str_replace('*', '%', $groupName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(GroupsPeer::GROUP_NAME, $groupName, $comparison);
    }

    /**
     * Filter the query on the enabled column
     *
     * Example usage:
     * <code>
     * $query->filterByEnabled(1234); // WHERE enabled = 1234
     * $query->filterByEnabled(array(12, 34)); // WHERE enabled IN (12, 34)
     * $query->filterByEnabled(array('min' => 12)); // WHERE enabled >= 12
     * $query->filterByEnabled(array('max' => 12)); // WHERE enabled <= 12
     * </code>
     *
     * @param     mixed $enabled The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function filterByEnabled($enabled = null, $comparison = null)
    {
        if (is_array($enabled)) {
            $useMinMax = false;
            if (isset($enabled['min'])) {
                $this->addUsingAlias(GroupsPeer::ENABLED, $enabled['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($enabled['max'])) {
                $this->addUsingAlias(GroupsPeer::ENABLED, $enabled['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(GroupsPeer::ENABLED, $enabled, $comparison);
    }

    /**
     * Filter the query by a related AuthPermissionGroup object
     *
     * @param   AuthPermissionGroup|PropelObjectCollection $authPermissionGroup  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 GroupsQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPermissionGroup($authPermissionGroup, $comparison = null)
    {
        if ($authPermissionGroup instanceof AuthPermissionGroup) {
            return $this
                ->addUsingAlias(GroupsPeer::GID, $authPermissionGroup->getGroups(), $comparison);
        } elseif ($authPermissionGroup instanceof PropelObjectCollection) {
            return $this
                ->useAuthPermissionGroupQuery()
                ->filterByPrimaryKeys($authPermissionGroup->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAuthPermissionGroup() only accepts arguments of type AuthPermissionGroup or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPermissionGroup relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function joinAuthPermissionGroup($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPermissionGroup');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPermissionGroup');
        }

        return $this;
    }

    /**
     * Use the AuthPermissionGroup relation AuthPermissionGroup object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthPermissionGroupQuery A secondary query class using the current class as primary query
     */
    public function useAuthPermissionGroupQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPermissionGroup($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPermissionGroup', '\Auth\AuthPermissionGroupQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Groups $groups Object to remove from the list of results
     *
     * @return GroupsQuery The current query, for fluid interface
     */
    public function prune($groups = null)
    {
        if ($groups) {
            $this->addUsingAlias(GroupsPeer::GID, $groups->getGid(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
