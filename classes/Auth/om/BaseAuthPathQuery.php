<?php

namespace Auth\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthPath;
use Auth\AuthPathPeer;
use Auth\AuthPathQuery;
use Auth\AuthPermission;

/**
 * Base class that represents a query for the 'auth_path' table.
 *
 *
 *
 * @method AuthPathQuery orderById($order = Criteria::ASC) Order by the id column
 * @method AuthPathQuery orderByPath($order = Criteria::ASC) Order by the path column
 * @method AuthPathQuery orderByAuthPermission($order = Criteria::ASC) Order by the auth_permission column
 *
 * @method AuthPathQuery groupById() Group by the id column
 * @method AuthPathQuery groupByPath() Group by the path column
 * @method AuthPathQuery groupByAuthPermission() Group by the auth_permission column
 *
 * @method AuthPathQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AuthPathQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AuthPathQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AuthPathQuery leftJoinAuthPathAuthPermission($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPathAuthPermission relation
 * @method AuthPathQuery rightJoinAuthPathAuthPermission($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPathAuthPermission relation
 * @method AuthPathQuery innerJoinAuthPathAuthPermission($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPathAuthPermission relation
 *
 * @method AuthPath findOne(PropelPDO $con = null) Return the first AuthPath matching the query
 * @method AuthPath findOneOrCreate(PropelPDO $con = null) Return the first AuthPath matching the query, or a new AuthPath object populated from the query conditions when no match is found
 *
 * @method AuthPath findOneByPath(string $path) Return the first AuthPath filtered by the path column
 * @method AuthPath findOneByAuthPermission(int $auth_permission) Return the first AuthPath filtered by the auth_permission column
 *
 * @method array findById(int $id) Return AuthPath objects filtered by the id column
 * @method array findByPath(string $path) Return AuthPath objects filtered by the path column
 * @method array findByAuthPermission(int $auth_permission) Return AuthPath objects filtered by the auth_permission column
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseAuthPathQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAuthPathQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Auth\\AuthPath';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AuthPathQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AuthPathQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AuthPathQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AuthPathQuery) {
            return $criteria;
        }
        $query = new AuthPathQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   AuthPath|AuthPath[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AuthPathPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AuthPathPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthPath A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthPath A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "path", "auth_permission" FROM "auth_path" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new AuthPath();
            $obj->hydrate($row);
            AuthPathPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return AuthPath|AuthPath[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|AuthPath[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AuthPathPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AuthPathPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(AuthPathPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(AuthPathPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPathPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the path column
     *
     * Example usage:
     * <code>
     * $query->filterByPath('fooValue');   // WHERE path = 'fooValue'
     * $query->filterByPath('%fooValue%'); // WHERE path LIKE '%fooValue%'
     * </code>
     *
     * @param     string $path The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function filterByPath($path = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($path)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $path)) {
                $path = str_replace('*', '%', $path);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AuthPathPeer::PATH, $path, $comparison);
    }

    /**
     * Filter the query on the auth_permission column
     *
     * Example usage:
     * <code>
     * $query->filterByAuthPermission(1234); // WHERE auth_permission = 1234
     * $query->filterByAuthPermission(array(12, 34)); // WHERE auth_permission IN (12, 34)
     * $query->filterByAuthPermission(array('min' => 12)); // WHERE auth_permission >= 12
     * $query->filterByAuthPermission(array('max' => 12)); // WHERE auth_permission <= 12
     * </code>
     *
     * @see       filterByAuthPathAuthPermission()
     *
     * @param     mixed $authPermission The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function filterByAuthPermission($authPermission = null, $comparison = null)
    {
        if (is_array($authPermission)) {
            $useMinMax = false;
            if (isset($authPermission['min'])) {
                $this->addUsingAlias(AuthPathPeer::AUTH_PERMISSION, $authPermission['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($authPermission['max'])) {
                $this->addUsingAlias(AuthPathPeer::AUTH_PERMISSION, $authPermission['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPathPeer::AUTH_PERMISSION, $authPermission, $comparison);
    }

    /**
     * Filter the query by a related AuthPermission object
     *
     * @param   AuthPermission|PropelObjectCollection $authPermission The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPathQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPathAuthPermission($authPermission, $comparison = null)
    {
        if ($authPermission instanceof AuthPermission) {
            return $this
                ->addUsingAlias(AuthPathPeer::AUTH_PERMISSION, $authPermission->getId(), $comparison);
        } elseif ($authPermission instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AuthPathPeer::AUTH_PERMISSION, $authPermission->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByAuthPathAuthPermission() only accepts arguments of type AuthPermission or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPathAuthPermission relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function joinAuthPathAuthPermission($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPathAuthPermission');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPathAuthPermission');
        }

        return $this;
    }

    /**
     * Use the AuthPathAuthPermission relation AuthPermission object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthPermissionQuery A secondary query class using the current class as primary query
     */
    public function useAuthPathAuthPermissionQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPathAuthPermission($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPathAuthPermission', '\Auth\AuthPermissionQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   AuthPath $authPath Object to remove from the list of results
     *
     * @return AuthPathQuery The current query, for fluid interface
     */
    public function prune($authPath = null)
    {
        if ($authPath) {
            $this->addUsingAlias(AuthPathPeer::ID, $authPath->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
