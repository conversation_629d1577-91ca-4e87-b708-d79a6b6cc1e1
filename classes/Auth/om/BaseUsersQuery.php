<?php

namespace Auth\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\Users;
use Auth\UsersPeer;
use Auth\UsersQuery;

/**
 * Base class that represents a query for the 'users' table.
 *
 *
 *
 * @method UsersQuery orderByUid($order = Criteria::ASC) Order by the uid column
 * @method UsersQuery orderByUserName($order = Criteria::ASC) Order by the user_name column
 * @method UsersQuery orderByUserPassword($order = Criteria::ASC) Order by the user_password column
 * @method UsersQuery orderByName($order = Criteria::ASC) Order by the name column
 * @method UsersQuery orderBySurname($order = Criteria::ASC) Order by the surname column
 * @method UsersQuery orderByVersion($order = Criteria::ASC) Order by the version column
 * @method UsersQuery orderByLastcheck($order = Criteria::ASC) Order by the lastcheck column
 * @method UsersQuery orderByEnabled($order = Criteria::ASC) Order by the enabled column
 * @method UsersQuery orderByUserType($order = Criteria::ASC) Order by the user_type column
 * @method UsersQuery orderByPrivelege($order = Criteria::ASC) Order by the privelege column
 * @method UsersQuery orderByEmail($order = Criteria::ASC) Order by the email column
 * @method UsersQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method UsersQuery orderByModifyProtocol($order = Criteria::ASC) Order by the modify_protocol column
 * @method UsersQuery orderBySuperUser($order = Criteria::ASC) Order by the super_user column
 * @method UsersQuery orderByExpiration($order = Criteria::ASC) Order by the expiration column
 *
 * @method UsersQuery groupByUid() Group by the uid column
 * @method UsersQuery groupByUserName() Group by the user_name column
 * @method UsersQuery groupByUserPassword() Group by the user_password column
 * @method UsersQuery groupByName() Group by the name column
 * @method UsersQuery groupBySurname() Group by the surname column
 * @method UsersQuery groupByVersion() Group by the version column
 * @method UsersQuery groupByLastcheck() Group by the lastcheck column
 * @method UsersQuery groupByEnabled() Group by the enabled column
 * @method UsersQuery groupByUserType() Group by the user_type column
 * @method UsersQuery groupByPrivelege() Group by the privelege column
 * @method UsersQuery groupByEmail() Group by the email column
 * @method UsersQuery groupByEmployeeId() Group by the employee_id column
 * @method UsersQuery groupByModifyProtocol() Group by the modify_protocol column
 * @method UsersQuery groupBySuperUser() Group by the super_user column
 * @method UsersQuery groupByExpiration() Group by the expiration column
 *
 * @method UsersQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method UsersQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method UsersQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method Users findOne(PropelPDO $con = null) Return the first Users matching the query
 * @method Users findOneOrCreate(PropelPDO $con = null) Return the first Users matching the query, or a new Users object populated from the query conditions when no match is found
 *
 * @method Users findOneByUserName(string $user_name) Return the first Users filtered by the user_name column
 * @method Users findOneByUserPassword(string $user_password) Return the first Users filtered by the user_password column
 * @method Users findOneByName(string $name) Return the first Users filtered by the name column
 * @method Users findOneBySurname(string $surname) Return the first Users filtered by the surname column
 * @method Users findOneByVersion(string $version) Return the first Users filtered by the version column
 * @method Users findOneByLastcheck(string $lastcheck) Return the first Users filtered by the lastcheck column
 * @method Users findOneByEnabled(int $enabled) Return the first Users filtered by the enabled column
 * @method Users findOneByUserType(int $user_type) Return the first Users filtered by the user_type column
 * @method Users findOneByPrivelege(int $privelege) Return the first Users filtered by the privelege column
 * @method Users findOneByEmail(string $email) Return the first Users filtered by the email column
 * @method Users findOneByEmployeeId(int $employee_id) Return the first Users filtered by the employee_id column
 * @method Users findOneByModifyProtocol(int $modify_protocol) Return the first Users filtered by the modify_protocol column
 * @method Users findOneBySuperUser(boolean $super_user) Return the first Users filtered by the super_user column
 * @method Users findOneByExpiration(string $expiration) Return the first Users filtered by the expiration column
 *
 * @method array findByUid(int $uid) Return Users objects filtered by the uid column
 * @method array findByUserName(string $user_name) Return Users objects filtered by the user_name column
 * @method array findByUserPassword(string $user_password) Return Users objects filtered by the user_password column
 * @method array findByName(string $name) Return Users objects filtered by the name column
 * @method array findBySurname(string $surname) Return Users objects filtered by the surname column
 * @method array findByVersion(string $version) Return Users objects filtered by the version column
 * @method array findByLastcheck(string $lastcheck) Return Users objects filtered by the lastcheck column
 * @method array findByEnabled(int $enabled) Return Users objects filtered by the enabled column
 * @method array findByUserType(int $user_type) Return Users objects filtered by the user_type column
 * @method array findByPrivelege(int $privelege) Return Users objects filtered by the privelege column
 * @method array findByEmail(string $email) Return Users objects filtered by the email column
 * @method array findByEmployeeId(int $employee_id) Return Users objects filtered by the employee_id column
 * @method array findByModifyProtocol(int $modify_protocol) Return Users objects filtered by the modify_protocol column
 * @method array findBySuperUser(boolean $super_user) Return Users objects filtered by the super_user column
 * @method array findByExpiration(string $expiration) Return Users objects filtered by the expiration column
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseUsersQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseUsersQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Auth\\Users';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new UsersQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   UsersQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return UsersQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof UsersQuery) {
            return $criteria;
        }
        $query = new UsersQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Users|Users[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = UsersPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(UsersPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Users A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByUid($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Users A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "uid", "user_name", "user_password", "name", "surname", "version", "lastcheck", "enabled", "user_type", "privelege", "email", "employee_id", "modify_protocol", "super_user", "expiration" FROM "users" WHERE "uid" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Users();
            $obj->hydrate($row);
            UsersPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Users|Users[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Users[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(UsersPeer::UID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(UsersPeer::UID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the uid column
     *
     * Example usage:
     * <code>
     * $query->filterByUid(1234); // WHERE uid = 1234
     * $query->filterByUid(array(12, 34)); // WHERE uid IN (12, 34)
     * $query->filterByUid(array('min' => 12)); // WHERE uid >= 12
     * $query->filterByUid(array('max' => 12)); // WHERE uid <= 12
     * </code>
     *
     * @param     mixed $uid The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByUid($uid = null, $comparison = null)
    {
        if (is_array($uid)) {
            $useMinMax = false;
            if (isset($uid['min'])) {
                $this->addUsingAlias(UsersPeer::UID, $uid['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($uid['max'])) {
                $this->addUsingAlias(UsersPeer::UID, $uid['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::UID, $uid, $comparison);
    }

    /**
     * Filter the query on the user_name column
     *
     * Example usage:
     * <code>
     * $query->filterByUserName('fooValue');   // WHERE user_name = 'fooValue'
     * $query->filterByUserName('%fooValue%'); // WHERE user_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $userName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByUserName($userName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($userName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $userName)) {
                $userName = str_replace('*', '%', $userName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(UsersPeer::USER_NAME, $userName, $comparison);
    }

    /**
     * Filter the query on the user_password column
     *
     * Example usage:
     * <code>
     * $query->filterByUserPassword('fooValue');   // WHERE user_password = 'fooValue'
     * $query->filterByUserPassword('%fooValue%'); // WHERE user_password LIKE '%fooValue%'
     * </code>
     *
     * @param     string $userPassword The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByUserPassword($userPassword = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($userPassword)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $userPassword)) {
                $userPassword = str_replace('*', '%', $userPassword);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(UsersPeer::USER_PASSWORD, $userPassword, $comparison);
    }

    /**
     * Filter the query on the name column
     *
     * Example usage:
     * <code>
     * $query->filterByName('fooValue');   // WHERE name = 'fooValue'
     * $query->filterByName('%fooValue%'); // WHERE name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $name The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByName($name = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($name)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $name)) {
                $name = str_replace('*', '%', $name);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(UsersPeer::NAME, $name, $comparison);
    }

    /**
     * Filter the query on the surname column
     *
     * Example usage:
     * <code>
     * $query->filterBySurname('fooValue');   // WHERE surname = 'fooValue'
     * $query->filterBySurname('%fooValue%'); // WHERE surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $surname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterBySurname($surname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($surname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $surname)) {
                $surname = str_replace('*', '%', $surname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(UsersPeer::SURNAME, $surname, $comparison);
    }

    /**
     * Filter the query on the version column
     *
     * Example usage:
     * <code>
     * $query->filterByVersion('fooValue');   // WHERE version = 'fooValue'
     * $query->filterByVersion('%fooValue%'); // WHERE version LIKE '%fooValue%'
     * </code>
     *
     * @param     string $version The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByVersion($version = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($version)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $version)) {
                $version = str_replace('*', '%', $version);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(UsersPeer::VERSION, $version, $comparison);
    }

    /**
     * Filter the query on the lastcheck column
     *
     * Example usage:
     * <code>
     * $query->filterByLastcheck(1234); // WHERE lastcheck = 1234
     * $query->filterByLastcheck(array(12, 34)); // WHERE lastcheck IN (12, 34)
     * $query->filterByLastcheck(array('min' => 12)); // WHERE lastcheck >= 12
     * $query->filterByLastcheck(array('max' => 12)); // WHERE lastcheck <= 12
     * </code>
     *
     * @param     mixed $lastcheck The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByLastcheck($lastcheck = null, $comparison = null)
    {
        if (is_array($lastcheck)) {
            $useMinMax = false;
            if (isset($lastcheck['min'])) {
                $this->addUsingAlias(UsersPeer::LASTCHECK, $lastcheck['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($lastcheck['max'])) {
                $this->addUsingAlias(UsersPeer::LASTCHECK, $lastcheck['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::LASTCHECK, $lastcheck, $comparison);
    }

    /**
     * Filter the query on the enabled column
     *
     * Example usage:
     * <code>
     * $query->filterByEnabled(1234); // WHERE enabled = 1234
     * $query->filterByEnabled(array(12, 34)); // WHERE enabled IN (12, 34)
     * $query->filterByEnabled(array('min' => 12)); // WHERE enabled >= 12
     * $query->filterByEnabled(array('max' => 12)); // WHERE enabled <= 12
     * </code>
     *
     * @param     mixed $enabled The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByEnabled($enabled = null, $comparison = null)
    {
        if (is_array($enabled)) {
            $useMinMax = false;
            if (isset($enabled['min'])) {
                $this->addUsingAlias(UsersPeer::ENABLED, $enabled['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($enabled['max'])) {
                $this->addUsingAlias(UsersPeer::ENABLED, $enabled['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::ENABLED, $enabled, $comparison);
    }

    /**
     * Filter the query on the user_type column
     *
     * Example usage:
     * <code>
     * $query->filterByUserType(1234); // WHERE user_type = 1234
     * $query->filterByUserType(array(12, 34)); // WHERE user_type IN (12, 34)
     * $query->filterByUserType(array('min' => 12)); // WHERE user_type >= 12
     * $query->filterByUserType(array('max' => 12)); // WHERE user_type <= 12
     * </code>
     *
     * @param     mixed $userType The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByUserType($userType = null, $comparison = null)
    {
        if (is_array($userType)) {
            $useMinMax = false;
            if (isset($userType['min'])) {
                $this->addUsingAlias(UsersPeer::USER_TYPE, $userType['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($userType['max'])) {
                $this->addUsingAlias(UsersPeer::USER_TYPE, $userType['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::USER_TYPE, $userType, $comparison);
    }

    /**
     * Filter the query on the privelege column
     *
     * Example usage:
     * <code>
     * $query->filterByPrivelege(1234); // WHERE privelege = 1234
     * $query->filterByPrivelege(array(12, 34)); // WHERE privelege IN (12, 34)
     * $query->filterByPrivelege(array('min' => 12)); // WHERE privelege >= 12
     * $query->filterByPrivelege(array('max' => 12)); // WHERE privelege <= 12
     * </code>
     *
     * @param     mixed $privelege The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByPrivelege($privelege = null, $comparison = null)
    {
        if (is_array($privelege)) {
            $useMinMax = false;
            if (isset($privelege['min'])) {
                $this->addUsingAlias(UsersPeer::PRIVELEGE, $privelege['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($privelege['max'])) {
                $this->addUsingAlias(UsersPeer::PRIVELEGE, $privelege['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::PRIVELEGE, $privelege, $comparison);
    }

    /**
     * Filter the query on the email column
     *
     * Example usage:
     * <code>
     * $query->filterByEmail('fooValue');   // WHERE email = 'fooValue'
     * $query->filterByEmail('%fooValue%'); // WHERE email LIKE '%fooValue%'
     * </code>
     *
     * @param     string $email The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByEmail($email = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($email)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $email)) {
                $email = str_replace('*', '%', $email);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(UsersPeer::EMAIL, $email, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(UsersPeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(UsersPeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the modify_protocol column
     *
     * Example usage:
     * <code>
     * $query->filterByModifyProtocol(1234); // WHERE modify_protocol = 1234
     * $query->filterByModifyProtocol(array(12, 34)); // WHERE modify_protocol IN (12, 34)
     * $query->filterByModifyProtocol(array('min' => 12)); // WHERE modify_protocol >= 12
     * $query->filterByModifyProtocol(array('max' => 12)); // WHERE modify_protocol <= 12
     * </code>
     *
     * @param     mixed $modifyProtocol The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByModifyProtocol($modifyProtocol = null, $comparison = null)
    {
        if (is_array($modifyProtocol)) {
            $useMinMax = false;
            if (isset($modifyProtocol['min'])) {
                $this->addUsingAlias(UsersPeer::MODIFY_PROTOCOL, $modifyProtocol['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($modifyProtocol['max'])) {
                $this->addUsingAlias(UsersPeer::MODIFY_PROTOCOL, $modifyProtocol['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::MODIFY_PROTOCOL, $modifyProtocol, $comparison);
    }

    /**
     * Filter the query on the super_user column
     *
     * Example usage:
     * <code>
     * $query->filterBySuperUser(true); // WHERE super_user = true
     * $query->filterBySuperUser('yes'); // WHERE super_user = true
     * </code>
     *
     * @param     boolean|string $superUser The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterBySuperUser($superUser = null, $comparison = null)
    {
        if (is_string($superUser)) {
            $superUser = in_array(strtolower($superUser), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(UsersPeer::SUPER_USER, $superUser, $comparison);
    }

    /**
     * Filter the query on the expiration column
     *
     * Example usage:
     * <code>
     * $query->filterByExpiration(1234); // WHERE expiration = 1234
     * $query->filterByExpiration(array(12, 34)); // WHERE expiration IN (12, 34)
     * $query->filterByExpiration(array('min' => 12)); // WHERE expiration >= 12
     * $query->filterByExpiration(array('max' => 12)); // WHERE expiration <= 12
     * </code>
     *
     * @param     mixed $expiration The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function filterByExpiration($expiration = null, $comparison = null)
    {
        if (is_array($expiration)) {
            $useMinMax = false;
            if (isset($expiration['min'])) {
                $this->addUsingAlias(UsersPeer::EXPIRATION, $expiration['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($expiration['max'])) {
                $this->addUsingAlias(UsersPeer::EXPIRATION, $expiration['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(UsersPeer::EXPIRATION, $expiration, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   Users $users Object to remove from the list of results
     *
     * @return UsersQuery The current query, for fluid interface
     */
    public function prune($users = null)
    {
        if ($users) {
            $this->addUsingAlias(UsersPeer::UID, $users->getUid(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
