<?php

namespace Auth\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Auth\AuthElement;
use Auth\AuthPath;
use Auth\AuthPermission;
use Auth\AuthPermissionGroup;
use Auth\AuthPermissionPeer;
use Auth\AuthPermissionQuery;
use Auth\AuthSection;

/**
 * Base class that represents a query for the 'auth_permission' table.
 *
 *
 *
 * @method AuthPermissionQuery orderById($order = Criteria::ASC) Order by the id column
 * @method AuthPermissionQuery orderByTitle($order = Criteria::ASC) Order by the title column
 * @method AuthPermissionQuery orderBySuperUser($order = Criteria::ASC) Order by the super_user column
 * @method AuthPermissionQuery orderByAuthSection($order = Criteria::ASC) Order by the auth_section column
 *
 * @method AuthPermissionQuery groupById() Group by the id column
 * @method AuthPermissionQuery groupByTitle() Group by the title column
 * @method AuthPermissionQuery groupBySuperUser() Group by the super_user column
 * @method AuthPermissionQuery groupByAuthSection() Group by the auth_section column
 *
 * @method AuthPermissionQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AuthPermissionQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AuthPermissionQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AuthPermissionQuery leftJoinAuthPermissionToSection($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPermissionToSection relation
 * @method AuthPermissionQuery rightJoinAuthPermissionToSection($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPermissionToSection relation
 * @method AuthPermissionQuery innerJoinAuthPermissionToSection($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPermissionToSection relation
 *
 * @method AuthPermissionQuery leftJoinAuthElement($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthElement relation
 * @method AuthPermissionQuery rightJoinAuthElement($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthElement relation
 * @method AuthPermissionQuery innerJoinAuthElement($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthElement relation
 *
 * @method AuthPermissionQuery leftJoinAuthPath($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPath relation
 * @method AuthPermissionQuery rightJoinAuthPath($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPath relation
 * @method AuthPermissionQuery innerJoinAuthPath($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPath relation
 *
 * @method AuthPermissionQuery leftJoinAuthPermissionGroup($relationAlias = null) Adds a LEFT JOIN clause to the query using the AuthPermissionGroup relation
 * @method AuthPermissionQuery rightJoinAuthPermissionGroup($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AuthPermissionGroup relation
 * @method AuthPermissionQuery innerJoinAuthPermissionGroup($relationAlias = null) Adds a INNER JOIN clause to the query using the AuthPermissionGroup relation
 *
 * @method AuthPermission findOne(PropelPDO $con = null) Return the first AuthPermission matching the query
 * @method AuthPermission findOneOrCreate(PropelPDO $con = null) Return the first AuthPermission matching the query, or a new AuthPermission object populated from the query conditions when no match is found
 *
 * @method AuthPermission findOneByTitle(string $title) Return the first AuthPermission filtered by the title column
 * @method AuthPermission findOneBySuperUser(boolean $super_user) Return the first AuthPermission filtered by the super_user column
 * @method AuthPermission findOneByAuthSection(int $auth_section) Return the first AuthPermission filtered by the auth_section column
 *
 * @method array findById(int $id) Return AuthPermission objects filtered by the id column
 * @method array findByTitle(string $title) Return AuthPermission objects filtered by the title column
 * @method array findBySuperUser(boolean $super_user) Return AuthPermission objects filtered by the super_user column
 * @method array findByAuthSection(int $auth_section) Return AuthPermission objects filtered by the auth_section column
 *
 * @package    propel.generator.Auth.om
 */
abstract class BaseAuthPermissionQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAuthPermissionQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Auth\\AuthPermission';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AuthPermissionQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AuthPermissionQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AuthPermissionQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AuthPermissionQuery) {
            return $criteria;
        }
        $query = new AuthPermissionQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   AuthPermission|AuthPermission[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AuthPermissionPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AuthPermissionPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthPermission A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AuthPermission A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "title", "super_user", "auth_section" FROM "auth_permissions" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new AuthPermission();
            $obj->hydrate($row);
            AuthPermissionPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return AuthPermission|AuthPermission[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|AuthPermission[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AuthPermissionPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AuthPermissionPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(AuthPermissionPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(AuthPermissionPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPermissionPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the title column
     *
     * Example usage:
     * <code>
     * $query->filterByTitle('fooValue');   // WHERE title = 'fooValue'
     * $query->filterByTitle('%fooValue%'); // WHERE title LIKE '%fooValue%'
     * </code>
     *
     * @param     string $title The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function filterByTitle($title = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($title)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $title)) {
                $title = str_replace('*', '%', $title);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AuthPermissionPeer::TITLE, $title, $comparison);
    }

    /**
     * Filter the query on the super_user column
     *
     * Example usage:
     * <code>
     * $query->filterBySuperUser(true); // WHERE super_user = true
     * $query->filterBySuperUser('yes'); // WHERE super_user = true
     * </code>
     *
     * @param     boolean|string $superUser The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function filterBySuperUser($superUser = null, $comparison = null)
    {
        if (is_string($superUser)) {
            $superUser = in_array(strtolower($superUser), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(AuthPermissionPeer::SUPER_USER, $superUser, $comparison);
    }

    /**
     * Filter the query on the auth_section column
     *
     * Example usage:
     * <code>
     * $query->filterByAuthSection(1234); // WHERE auth_section = 1234
     * $query->filterByAuthSection(array(12, 34)); // WHERE auth_section IN (12, 34)
     * $query->filterByAuthSection(array('min' => 12)); // WHERE auth_section >= 12
     * $query->filterByAuthSection(array('max' => 12)); // WHERE auth_section <= 12
     * </code>
     *
     * @see       filterByAuthPermissionToSection()
     *
     * @param     mixed $authSection The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function filterByAuthSection($authSection = null, $comparison = null)
    {
        if (is_array($authSection)) {
            $useMinMax = false;
            if (isset($authSection['min'])) {
                $this->addUsingAlias(AuthPermissionPeer::AUTH_SECTION, $authSection['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($authSection['max'])) {
                $this->addUsingAlias(AuthPermissionPeer::AUTH_SECTION, $authSection['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AuthPermissionPeer::AUTH_SECTION, $authSection, $comparison);
    }

    /**
     * Filter the query by a related AuthSection object
     *
     * @param   AuthSection|PropelObjectCollection $authSection The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPermissionQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPermissionToSection($authSection, $comparison = null)
    {
        if ($authSection instanceof AuthSection) {
            return $this
                ->addUsingAlias(AuthPermissionPeer::AUTH_SECTION, $authSection->getId(), $comparison);
        } elseif ($authSection instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AuthPermissionPeer::AUTH_SECTION, $authSection->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByAuthPermissionToSection() only accepts arguments of type AuthSection or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPermissionToSection relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function joinAuthPermissionToSection($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPermissionToSection');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPermissionToSection');
        }

        return $this;
    }

    /**
     * Use the AuthPermissionToSection relation AuthSection object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthSectionQuery A secondary query class using the current class as primary query
     */
    public function useAuthPermissionToSectionQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPermissionToSection($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPermissionToSection', '\Auth\AuthSectionQuery');
    }

    /**
     * Filter the query by a related AuthElement object
     *
     * @param   AuthElement|PropelObjectCollection $authElement  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPermissionQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthElement($authElement, $comparison = null)
    {
        if ($authElement instanceof AuthElement) {
            return $this
                ->addUsingAlias(AuthPermissionPeer::ID, $authElement->getAuthPermission(), $comparison);
        } elseif ($authElement instanceof PropelObjectCollection) {
            return $this
                ->useAuthElementQuery()
                ->filterByPrimaryKeys($authElement->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAuthElement() only accepts arguments of type AuthElement or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthElement relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function joinAuthElement($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthElement');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthElement');
        }

        return $this;
    }

    /**
     * Use the AuthElement relation AuthElement object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthElementQuery A secondary query class using the current class as primary query
     */
    public function useAuthElementQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthElement($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthElement', '\Auth\AuthElementQuery');
    }

    /**
     * Filter the query by a related AuthPath object
     *
     * @param   AuthPath|PropelObjectCollection $authPath  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPermissionQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPath($authPath, $comparison = null)
    {
        if ($authPath instanceof AuthPath) {
            return $this
                ->addUsingAlias(AuthPermissionPeer::ID, $authPath->getAuthPermission(), $comparison);
        } elseif ($authPath instanceof PropelObjectCollection) {
            return $this
                ->useAuthPathQuery()
                ->filterByPrimaryKeys($authPath->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAuthPath() only accepts arguments of type AuthPath or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPath relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function joinAuthPath($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPath');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPath');
        }

        return $this;
    }

    /**
     * Use the AuthPath relation AuthPath object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthPathQuery A secondary query class using the current class as primary query
     */
    public function useAuthPathQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPath($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPath', '\Auth\AuthPathQuery');
    }

    /**
     * Filter the query by a related AuthPermissionGroup object
     *
     * @param   AuthPermissionGroup|PropelObjectCollection $authPermissionGroup  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AuthPermissionQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAuthPermissionGroup($authPermissionGroup, $comparison = null)
    {
        if ($authPermissionGroup instanceof AuthPermissionGroup) {
            return $this
                ->addUsingAlias(AuthPermissionPeer::ID, $authPermissionGroup->getAuthPermission(), $comparison);
        } elseif ($authPermissionGroup instanceof PropelObjectCollection) {
            return $this
                ->useAuthPermissionGroupQuery()
                ->filterByPrimaryKeys($authPermissionGroup->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAuthPermissionGroup() only accepts arguments of type AuthPermissionGroup or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AuthPermissionGroup relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function joinAuthPermissionGroup($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AuthPermissionGroup');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AuthPermissionGroup');
        }

        return $this;
    }

    /**
     * Use the AuthPermissionGroup relation AuthPermissionGroup object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Auth\AuthPermissionGroupQuery A secondary query class using the current class as primary query
     */
    public function useAuthPermissionGroupQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAuthPermissionGroup($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AuthPermissionGroup', '\Auth\AuthPermissionGroupQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   AuthPermission $authPermission Object to remove from the list of results
     *
     * @return AuthPermissionQuery The current query, for fluid interface
     */
    public function prune($authPermission = null)
    {
        if ($authPermission) {
            $this->addUsingAlias(AuthPermissionPeer::ID, $authPermission->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
