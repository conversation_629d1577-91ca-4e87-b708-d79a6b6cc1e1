<?php

namespace Auth;

use Auth\om\BaseGroups;

/**
 * Skeleton subclass for representing a row from the 'groups' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Auth
 */
class Groups extends BaseGroups {

	/**
	 * Returns true if the group has a $permission, false otherwise
	 * @param AuthPermission $permission
	 * @return boolean
	 */
	public function hasPermission($permission = null) {
		$permArr = AuthPermissionGroupQuery::create()
				->filterByAuthPermission($permission->getId())
				->filterByGroups($this->getPrimaryKey())
				->count();

		if ($permArr > 0)
			return false;
		else
			return true;
	}

	/**
	 * Switch specific permission for this group. If $to is true, we have to remove prohibition, add otherwise
	 * @param AuthPermission $permission
	 * @param boolean $to
	 * @return boolean if operation has been executed or not
	 */
	public function switchPermission($permission = null, $to = false) {
		if ($permission == null) {
			return false;
		}

		$authPermissionGroup = AuthPermissionGroupQuery::create()->filterByAuthPermission($permission->getPrimaryKey())->filterByGroups($this->getPrimaryKey())->find()->getFirst();
		if ($to === true) { // It has to set true a permission. It means to delete from table the correspondent row
			if ($authPermissionGroup == null) { // But if this row does not exist, we are done
				return true;
			} else {
				$authPermissionGroup->delete();
			}
		} else { // It has to set to false, it means to insert into table the correspondent row
			if ($authPermissionGroup == null) {
				$authPermissionGroup = new AuthPermissionGroup();
				$authPermissionGroup->setGroups($this->getPrimaryKey());
				$authPermissionGroup->setAuthPermission($permission->getPrimaryKey());
				$authPermissionGroup->save();
			} else { // But if this row already exists, we are done
				return true;
			}
		}
	}

}
