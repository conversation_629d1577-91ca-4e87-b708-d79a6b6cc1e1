<?php

namespace Auth;

use Auth\om\BaseAuthElementQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'auth_element' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Auth
 */
class AuthElementQuery extends BaseAuthElementQuery {

    /**
     * Search list of elements the user interface must hide or disable for that user
     * @param Users $user
     * @return array
     */
    public function getUserUnpermittedElements($user) {
        $permissionsArray = array();

        // If it is superuser it has no prohibition to get
        if ($user->getSuperUser()) {
            return array();
        }

        $authPermissions = AuthPermissionQuery::create()->filterBySuperUser(true)->find();
        foreach ($authPermissions as $permission) {
            $permissionsElements = AuthElementQuery::create()->filterByAuthPermission($permission->getPrimaryKey())
                    ->find();
            foreach ($permissionsElements as $element) {
                $permissionsArray[] = $element->toArray(\BasePeer::TYPE_FIELDNAME);
            }
        }

        // If it is admin it has only super user prohibition to get
        if ($user->getPrivelege() > 0) {
            return $permissionsArray;
        }

        // If the code arrives here, the user should appartain to a group. So we get group permission
        $groupPermissions = $this->getGroupUnpermittedElements(GroupsQuery::create()->findPk($user->getUserType()));
        foreach ($groupPermissions as $permission) {
            $permissionsArray[] = $permission;
        }
        return $permissionsArray;
    }

    /**
     * Search list of elements the user interface must hide or disable for that group
     * @param Groups $group
     * @return array
     */
    public function getGroupUnpermittedElements($group) {
        $groupPermissions = array();
        $authPermissions = AuthPermissionGroupQuery::create()->filterByGroups($group->getPrimaryKey())->find();
        if ($authPermissions !== null) {
            foreach ($authPermissions as $permission) {
                $groupElements = AuthElementQuery::create()->filterByAuthPermission($permission->getAuthPermission())
                        ->find();
                foreach ($groupElements as $element) {
                    $groupPermissions[] = $element->toArray(\BasePeer::TYPE_FIELDNAME);
                }
            }
            return $groupPermissions;
        } else {
            return array();
        }
    }

}
