<?php

namespace Auth;

use Auth\om\BaseAuthPermissionQuery;
use Auth\Groups;

/**
 * Skeleton subclass for performing query and update operations on the 'auth_permission' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Auth
 */
class AuthPermissionQuery extends BaseAuthPermissionQuery {

	/**
	 * Return a list of permission, eventually filtered by superuser and/or group
	 * @param boolean $super_user
	 * @param Groups $group
	 */
	public function getPermissionsList($super_user = false, $group = null) {
		$permissionsList = array();
		$q = new AuthPermissionQuery();

		// If not superuser, it can't see some administrator section
		if ($super_user !== true) {
			$q->filterBySuperUser(false);
		}

		$q->orderByTitle();

		$permissions = $q->find();
		foreach ($permissions as $permission) {
			$tmpArr = $permission->toArray(\BasePeer::TYPE_FIELDNAME);

			/**
			 * Se SuperUtente settiamo sempre hasPermission a true;
			 */
			if ( ! $super_user ) {
				$tmpArr['has_permission'] = $group == null ? false : $group->hasPermission($permission);
			} else {
				$tmpArr['has_permission'] = true;
			}
			
			
			// Add section TODO => Find the way to do it all in a query instance by join or other
			$tmpArr['section'] = AuthSectionQuery::create()->filterById($tmpArr['auth_section'])->findOne()->getTitle();
			$permissionsList[$tmpArr['id']] = $tmpArr;
		}

		return $permissionsList;
	}

}
