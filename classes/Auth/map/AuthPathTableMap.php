<?php

namespace Auth\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'auth_path' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Auth.map
 */
class AuthPathTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Auth.map.AuthPathTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('auth_path');
        $this->setPhpName('AuthPath');
        $this->setClassname('Auth\\AuthPath');
        $this->setPackage('Auth');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('auth_path_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('path', 'Path', 'VARCHAR', true, 50, null);
        $this->addForeignKey('auth_permission', 'AuthPermission', 'INTEGER', 'auth_permission', 'id', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AuthPathAuthPermission', 'Auth\\AuthPermission', RelationMap::MANY_TO_ONE, array('auth_permission' => 'id', ), 'CASCADE', null);
    } // buildRelations()

} // AuthPathTableMap
