<?php

namespace Auth\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'auth_element' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Auth.map
 */
class AuthElementTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Auth.map.AuthElementTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('auth_element');
        $this->setPhpName('AuthElement');
        $this->setClassname('Auth\\AuthElement');
        $this->setPackage('Auth');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('auth_element_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('name', 'Name', 'VARCHAR', true, 50, null);
        $this->addColumn('control_interface', 'ControlInterface', 'VARCHAR', true, 50, null);
        $this->addForeignKey('auth_permission', 'AuthPermission', 'INTEGER', 'auth_permission', 'id', false, null, null);
        $this->addColumn('state', 'State', 'VARCHAR', true, 10, 'hide');
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AuthElementAuthPermission', 'Auth\\AuthPermission', RelationMap::MANY_TO_ONE, array('auth_permission' => 'id', ), 'CASCADE', null);
    } // buildRelations()

} // AuthElementTableMap
