<?php

namespace Auth\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'users' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Auth.map
 */
class UsersTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Auth.map.UsersTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('users');
        $this->setPhpName('Users');
        $this->setClassname('Auth\\Users');
        $this->setPackage('Auth');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('uid_seq');
        // columns
        $this->addPrimaryKey('uid', 'Uid', 'INTEGER', true, null, null);
        $this->addColumn('user_name', 'UserName', 'VARCHAR', false, 255, 'USER_NOT_DEFINED');
        $this->addColumn('user_password', 'UserPassword', 'VARCHAR', false, 255, 'PASSWORD_NOT_DEFINED');
        $this->addColumn('name', 'Name', 'VARCHAR', false, 255, '');
        $this->addColumn('surname', 'Surname', 'VARCHAR', false, 255, '');
        $this->addColumn('version', 'Version', 'VARCHAR', false, 255, null);
        $this->addColumn('lastcheck', 'Lastcheck', 'BIGINT', false, null, null);
        $this->addColumn('enabled', 'Enabled', 'INTEGER', false, null, 0);
        $this->addColumn('user_type', 'UserType', 'INTEGER', false, null, null);
        $this->addColumn('privelege', 'Privelege', 'INTEGER', false, null, 0);
        $this->addColumn('email', 'Email', 'VARCHAR', false, 40, null);
        $this->addColumn('employee_id', 'EmployeeId', 'INTEGER', true, null, 0);
        $this->addColumn('modify_protocol', 'ModifyProtocol', 'INTEGER', false, null, 0);
        $this->addColumn('super_user', 'SuperUser', 'BOOLEAN', true, null, false);
        $this->addColumn('expiration', 'Expiration', 'BIGINT', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // UsersTableMap
