<?php

namespace Auth\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'auth_permission' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Auth.map
 */
class AuthPermissionTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Auth.map.AuthPermissionTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('auth_permissions');
        $this->setPhpName('AuthPermission');
        $this->setClassname('Auth\\AuthPermission');
        $this->setPackage('Auth');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('auth_permission_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('title', 'Title', 'VARCHAR', true, 255, null);
        $this->addColumn('super_user', 'SuperUser', 'BOOLEAN', false, null, false);
        $this->addForeignKey('auth_section', 'AuthSection', 'INTEGER', 'auth_section', 'id', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AuthPermissionToSection', 'Auth\\AuthSection', RelationMap::MANY_TO_ONE, array('auth_section' => 'id', ), 'CASCADE', null);
        $this->addRelation('AuthElement', 'Auth\\AuthElement', RelationMap::ONE_TO_MANY, array('id' => 'auth_permission', ), 'CASCADE', null, 'AuthElements');
        $this->addRelation('AuthPath', 'Auth\\AuthPath', RelationMap::ONE_TO_MANY, array('id' => 'auth_permission', ), 'CASCADE', null, 'AuthPaths');
        $this->addRelation('AuthPermissionGroup', 'Auth\\AuthPermissionGroup', RelationMap::ONE_TO_MANY, array('id' => 'auth_permission', ), 'CASCADE', null, 'AuthPermissionGroups');
    } // buildRelations()

} // AuthPermissionTableMap
