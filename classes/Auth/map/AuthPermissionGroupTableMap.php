<?php

namespace Auth\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'auth_permission_group' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Auth.map
 */
class AuthPermissionGroupTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Auth.map.AuthPermissionGroupTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('auth_permission_group');
        $this->setPhpName('AuthPermissionGroup');
        $this->setClassname('Auth\\AuthPermissionGroup');
        $this->setPackage('Auth');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('auth_permission_group_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addForeignKey('groups', 'Groups', 'INTEGER', 'groups', 'gid', false, null, null);
        $this->addForeignKey('auth_permission', 'AuthPermission', 'INTEGER', 'auth_permission', 'id', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AuthPermissionGroupAuthPermission', 'Auth\\AuthPermission', RelationMap::MANY_TO_ONE, array('auth_permission' => 'id', ), 'CASCADE', null);
        $this->addRelation('AuthPermissionGroupGroups', 'Auth\\Groups', RelationMap::MANY_TO_ONE, array('groups' => 'gid', ), 'CASCADE', null);
    } // buildRelations()

} // AuthPermissionGroupTableMap
