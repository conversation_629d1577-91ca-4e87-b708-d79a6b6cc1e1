<?php

namespace Auth\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'groups' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Auth.map
 */
class GroupsTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Auth.map.GroupsTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('groups');
        $this->setPhpName('Groups');
        $this->setClassname('Auth\\Groups');
        $this->setPackage('Auth');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('groups_gid_seq');
        // columns
        $this->addPrimaryKey('gid', 'Gid', 'INTEGER', true, null, null);
        $this->addColumn('group_name', 'GroupName', 'VARCHAR', false, null, null);
        $this->addColumn('enabled', 'Enabled', 'SMALLINT', false, null, 1);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AuthPermissionGroup', 'Auth\\AuthPermissionGroup', RelationMap::ONE_TO_MANY, array('gid' => 'groups', ), 'CASCADE', null, 'AuthPermissionGroups');
    } // buildRelations()

} // GroupsTableMap
