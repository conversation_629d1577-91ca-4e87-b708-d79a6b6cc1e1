<?php

namespace Auth;

use Auth\om\BaseUsers;


/**
 * Skeleton subclass for representing a row from the 'users' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Auth
 */
class Users extends BaseUsers
{
     public function hasPermission($permissionId) {
        $conn = \Propel::getConnection();
        $statement = $conn->prepare(""
                . "SELECT u.uid, g.gid, apg.auth_permission "
                . "FROM users u, groups g, auth_permission_group apg "
                . "WHERE u.user_type = g.gid "
                . " AND apg.groups=g.gid "
                . " AND apg.auth_permission = {$permissionId} "
                . " AND u.uid = " . $this->getUid() . ""
                . " AND u.super_user = false "
                . " AND u.privelege < 1");  
        $statement->execute();
        $results = $statement->fetchAll();
        return count($results) > 0 ? false : true;
    }
}
