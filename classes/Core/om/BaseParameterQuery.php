<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Parameter;
use Core\ParameterPeer;
use Core\ParameterQuery;

/**
 * Base class that represents a query for the 'parameter' table.
 *
 *
 *
 * @method ParameterQuery orderByParameterId($order = Criteria::ASC) Order by the parameter_id column
 * @method ParameterQuery orderByName($order = Criteria::ASC) Order by the name column
 * @method ParameterQuery orderByValue($order = Criteria::ASC) Order by the value column
 *
 * @method ParameterQuery groupByParameterId() Group by the parameter_id column
 * @method ParameterQuery groupByName() Group by the name column
 * @method ParameterQuery groupByValue() Group by the value column
 *
 * @method ParameterQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method ParameterQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method ParameterQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method Parameter findOne(PropelPDO $con = null) Return the first Parameter matching the query
 * @method Parameter findOneOrCreate(PropelPDO $con = null) Return the first Parameter matching the query, or a new Parameter object populated from the query conditions when no match is found
 *
 * @method Parameter findOneByName(string $name) Return the first Parameter filtered by the name column
 * @method Parameter findOneByValue(string $value) Return the first Parameter filtered by the value column
 *
 * @method array findByParameterId(int $parameter_id) Return Parameter objects filtered by the parameter_id column
 * @method array findByName(string $name) Return Parameter objects filtered by the name column
 * @method array findByValue(string $value) Return Parameter objects filtered by the value column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseParameterQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseParameterQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\Parameter';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new ParameterQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   ParameterQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return ParameterQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof ParameterQuery) {
            return $criteria;
        }
        $query = new ParameterQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Parameter|Parameter[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = ParameterPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(ParameterPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Parameter A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByParameterId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Parameter A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "parameter_id", "name", "value" FROM "parameter" WHERE "parameter_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Parameter();
            $obj->hydrate($row);
            ParameterPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Parameter|Parameter[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Parameter[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return ParameterQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(ParameterPeer::PARAMETER_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return ParameterQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(ParameterPeer::PARAMETER_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the parameter_id column
     *
     * Example usage:
     * <code>
     * $query->filterByParameterId(1234); // WHERE parameter_id = 1234
     * $query->filterByParameterId(array(12, 34)); // WHERE parameter_id IN (12, 34)
     * $query->filterByParameterId(array('min' => 12)); // WHERE parameter_id >= 12
     * $query->filterByParameterId(array('max' => 12)); // WHERE parameter_id <= 12
     * </code>
     *
     * @param     mixed $parameterId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ParameterQuery The current query, for fluid interface
     */
    public function filterByParameterId($parameterId = null, $comparison = null)
    {
        if (is_array($parameterId)) {
            $useMinMax = false;
            if (isset($parameterId['min'])) {
                $this->addUsingAlias(ParameterPeer::PARAMETER_ID, $parameterId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($parameterId['max'])) {
                $this->addUsingAlias(ParameterPeer::PARAMETER_ID, $parameterId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ParameterPeer::PARAMETER_ID, $parameterId, $comparison);
    }

    /**
     * Filter the query on the name column
     *
     * Example usage:
     * <code>
     * $query->filterByName('fooValue');   // WHERE name = 'fooValue'
     * $query->filterByName('%fooValue%'); // WHERE name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $name The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ParameterQuery The current query, for fluid interface
     */
    public function filterByName($name = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($name)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $name)) {
                $name = str_replace('*', '%', $name);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ParameterPeer::NAME, $name, $comparison);
    }

    /**
     * Filter the query on the value column
     *
     * Example usage:
     * <code>
     * $query->filterByValue('fooValue');   // WHERE value = 'fooValue'
     * $query->filterByValue('%fooValue%'); // WHERE value LIKE '%fooValue%'
     * </code>
     *
     * @param     string $value The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ParameterQuery The current query, for fluid interface
     */
    public function filterByValue($value = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($value)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $value)) {
                $value = str_replace('*', '%', $value);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ParameterPeer::VALUE, $value, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   Parameter $parameter Object to remove from the list of results
     *
     * @return ParameterQuery The current query, for fluid interface
     */
    public function prune($parameter = null)
    {
        if ($parameter) {
            $this->addUsingAlias(ParameterPeer::PARAMETER_ID, $parameter->getParameterId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
