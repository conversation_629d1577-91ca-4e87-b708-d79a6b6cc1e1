<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Cities;
use Core\Contact;
use Core\ContactPeer;
use Core\ContactQuery;
use Core\Institute;
use Employee\Employee;

/**
 * Base class that represents a query for the 'contact' table.
 *
 *
 *
 * @method ContactQuery orderByContactId($order = Criteria::ASC) Order by the contact_id column
 * @method ContactQuery orderByAddress($order = Criteria::ASC) Order by the address column
 * @method ContactQuery orderByPhoneNum($order = Criteria::ASC) Order by the phone_num column
 * @method ContactQuery orderByFax($order = Criteria::ASC) Order by the fax column
 * @method ContactQuery orderByCityId($order = Criteria::ASC) Order by the city_id column
 * @method ContactQuery orderByEmail($order = Criteria::ASC) Order by the email column
 * @method ContactQuery orderByMobile($order = Criteria::ASC) Order by the mobile column
 * @method ContactQuery orderByWeb($order = Criteria::ASC) Order by the web column
 * @method ContactQuery orderByCap($order = Criteria::ASC) Order by the cap column
 *
 * @method ContactQuery groupByContactId() Group by the contact_id column
 * @method ContactQuery groupByAddress() Group by the address column
 * @method ContactQuery groupByPhoneNum() Group by the phone_num column
 * @method ContactQuery groupByFax() Group by the fax column
 * @method ContactQuery groupByCityId() Group by the city_id column
 * @method ContactQuery groupByEmail() Group by the email column
 * @method ContactQuery groupByMobile() Group by the mobile column
 * @method ContactQuery groupByWeb() Group by the web column
 * @method ContactQuery groupByCap() Group by the cap column
 *
 * @method ContactQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method ContactQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method ContactQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method ContactQuery leftJoinCityKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the CityKey relation
 * @method ContactQuery rightJoinCityKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the CityKey relation
 * @method ContactQuery innerJoinCityKey($relationAlias = null) Adds a INNER JOIN clause to the query using the CityKey relation
 *
 * @method ContactQuery leftJoinInstitute($relationAlias = null) Adds a LEFT JOIN clause to the query using the Institute relation
 * @method ContactQuery rightJoinInstitute($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Institute relation
 * @method ContactQuery innerJoinInstitute($relationAlias = null) Adds a INNER JOIN clause to the query using the Institute relation
 *
 * @method ContactQuery leftJoinEmployeeRelatedByResidenceId($relationAlias = null) Adds a LEFT JOIN clause to the query using the EmployeeRelatedByResidenceId relation
 * @method ContactQuery rightJoinEmployeeRelatedByResidenceId($relationAlias = null) Adds a RIGHT JOIN clause to the query using the EmployeeRelatedByResidenceId relation
 * @method ContactQuery innerJoinEmployeeRelatedByResidenceId($relationAlias = null) Adds a INNER JOIN clause to the query using the EmployeeRelatedByResidenceId relation
 *
 * @method ContactQuery leftJoinEmployeeRelatedByAddressId($relationAlias = null) Adds a LEFT JOIN clause to the query using the EmployeeRelatedByAddressId relation
 * @method ContactQuery rightJoinEmployeeRelatedByAddressId($relationAlias = null) Adds a RIGHT JOIN clause to the query using the EmployeeRelatedByAddressId relation
 * @method ContactQuery innerJoinEmployeeRelatedByAddressId($relationAlias = null) Adds a INNER JOIN clause to the query using the EmployeeRelatedByAddressId relation
 *
 * @method Contact findOne(PropelPDO $con = null) Return the first Contact matching the query
 * @method Contact findOneOrCreate(PropelPDO $con = null) Return the first Contact matching the query, or a new Contact object populated from the query conditions when no match is found
 *
 * @method Contact findOneByAddress(string $address) Return the first Contact filtered by the address column
 * @method Contact findOneByPhoneNum(string $phone_num) Return the first Contact filtered by the phone_num column
 * @method Contact findOneByFax(string $fax) Return the first Contact filtered by the fax column
 * @method Contact findOneByCityId(int $city_id) Return the first Contact filtered by the city_id column
 * @method Contact findOneByEmail(string $email) Return the first Contact filtered by the email column
 * @method Contact findOneByMobile(string $mobile) Return the first Contact filtered by the mobile column
 * @method Contact findOneByWeb(string $web) Return the first Contact filtered by the web column
 * @method Contact findOneByCap(string $cap) Return the first Contact filtered by the cap column
 *
 * @method array findByContactId(int $contact_id) Return Contact objects filtered by the contact_id column
 * @method array findByAddress(string $address) Return Contact objects filtered by the address column
 * @method array findByPhoneNum(string $phone_num) Return Contact objects filtered by the phone_num column
 * @method array findByFax(string $fax) Return Contact objects filtered by the fax column
 * @method array findByCityId(int $city_id) Return Contact objects filtered by the city_id column
 * @method array findByEmail(string $email) Return Contact objects filtered by the email column
 * @method array findByMobile(string $mobile) Return Contact objects filtered by the mobile column
 * @method array findByWeb(string $web) Return Contact objects filtered by the web column
 * @method array findByCap(string $cap) Return Contact objects filtered by the cap column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseContactQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseContactQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\Contact';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new ContactQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   ContactQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return ContactQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof ContactQuery) {
            return $criteria;
        }
        $query = new ContactQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Contact|Contact[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = ContactPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(ContactPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Contact A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByContactId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Contact A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "contact_id", "address", "phone_num", "fax", "city_id", "email", "mobile", "web", "cap" FROM "contact" WHERE "contact_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Contact();
            $obj->hydrate($row);
            ContactPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Contact|Contact[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Contact[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(ContactPeer::CONTACT_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(ContactPeer::CONTACT_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the contact_id column
     *
     * Example usage:
     * <code>
     * $query->filterByContactId(1234); // WHERE contact_id = 1234
     * $query->filterByContactId(array(12, 34)); // WHERE contact_id IN (12, 34)
     * $query->filterByContactId(array('min' => 12)); // WHERE contact_id >= 12
     * $query->filterByContactId(array('max' => 12)); // WHERE contact_id <= 12
     * </code>
     *
     * @param     mixed $contactId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByContactId($contactId = null, $comparison = null)
    {
        if (is_array($contactId)) {
            $useMinMax = false;
            if (isset($contactId['min'])) {
                $this->addUsingAlias(ContactPeer::CONTACT_ID, $contactId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($contactId['max'])) {
                $this->addUsingAlias(ContactPeer::CONTACT_ID, $contactId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ContactPeer::CONTACT_ID, $contactId, $comparison);
    }

    /**
     * Filter the query on the address column
     *
     * Example usage:
     * <code>
     * $query->filterByAddress('fooValue');   // WHERE address = 'fooValue'
     * $query->filterByAddress('%fooValue%'); // WHERE address LIKE '%fooValue%'
     * </code>
     *
     * @param     string $address The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByAddress($address = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($address)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $address)) {
                $address = str_replace('*', '%', $address);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::ADDRESS, $address, $comparison);
    }

    /**
     * Filter the query on the phone_num column
     *
     * Example usage:
     * <code>
     * $query->filterByPhoneNum('fooValue');   // WHERE phone_num = 'fooValue'
     * $query->filterByPhoneNum('%fooValue%'); // WHERE phone_num LIKE '%fooValue%'
     * </code>
     *
     * @param     string $phoneNum The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByPhoneNum($phoneNum = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($phoneNum)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $phoneNum)) {
                $phoneNum = str_replace('*', '%', $phoneNum);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::PHONE_NUM, $phoneNum, $comparison);
    }

    /**
     * Filter the query on the fax column
     *
     * Example usage:
     * <code>
     * $query->filterByFax('fooValue');   // WHERE fax = 'fooValue'
     * $query->filterByFax('%fooValue%'); // WHERE fax LIKE '%fooValue%'
     * </code>
     *
     * @param     string $fax The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByFax($fax = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($fax)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $fax)) {
                $fax = str_replace('*', '%', $fax);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::FAX, $fax, $comparison);
    }

    /**
     * Filter the query on the city_id column
     *
     * Example usage:
     * <code>
     * $query->filterByCityId(1234); // WHERE city_id = 1234
     * $query->filterByCityId(array(12, 34)); // WHERE city_id IN (12, 34)
     * $query->filterByCityId(array('min' => 12)); // WHERE city_id >= 12
     * $query->filterByCityId(array('max' => 12)); // WHERE city_id <= 12
     * </code>
     *
     * @see       filterByCityKey()
     *
     * @param     mixed $cityId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByCityId($cityId = null, $comparison = null)
    {
        if (is_array($cityId)) {
            $useMinMax = false;
            if (isset($cityId['min'])) {
                $this->addUsingAlias(ContactPeer::CITY_ID, $cityId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($cityId['max'])) {
                $this->addUsingAlias(ContactPeer::CITY_ID, $cityId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ContactPeer::CITY_ID, $cityId, $comparison);
    }

    /**
     * Filter the query on the email column
     *
     * Example usage:
     * <code>
     * $query->filterByEmail('fooValue');   // WHERE email = 'fooValue'
     * $query->filterByEmail('%fooValue%'); // WHERE email LIKE '%fooValue%'
     * </code>
     *
     * @param     string $email The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByEmail($email = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($email)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $email)) {
                $email = str_replace('*', '%', $email);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::EMAIL, $email, $comparison);
    }

    /**
     * Filter the query on the mobile column
     *
     * Example usage:
     * <code>
     * $query->filterByMobile('fooValue');   // WHERE mobile = 'fooValue'
     * $query->filterByMobile('%fooValue%'); // WHERE mobile LIKE '%fooValue%'
     * </code>
     *
     * @param     string $mobile The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByMobile($mobile = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($mobile)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $mobile)) {
                $mobile = str_replace('*', '%', $mobile);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::MOBILE, $mobile, $comparison);
    }

    /**
     * Filter the query on the web column
     *
     * Example usage:
     * <code>
     * $query->filterByWeb('fooValue');   // WHERE web = 'fooValue'
     * $query->filterByWeb('%fooValue%'); // WHERE web LIKE '%fooValue%'
     * </code>
     *
     * @param     string $web The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByWeb($web = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($web)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $web)) {
                $web = str_replace('*', '%', $web);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::WEB, $web, $comparison);
    }

    /**
     * Filter the query on the cap column
     *
     * Example usage:
     * <code>
     * $query->filterByCap('fooValue');   // WHERE cap = 'fooValue'
     * $query->filterByCap('%fooValue%'); // WHERE cap LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cap The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function filterByCap($cap = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cap)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cap)) {
                $cap = str_replace('*', '%', $cap);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ContactPeer::CAP, $cap, $comparison);
    }

    /**
     * Filter the query by a related Cities object
     *
     * @param   Cities|PropelObjectCollection $cities The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 ContactQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByCityKey($cities, $comparison = null)
    {
        if ($cities instanceof Cities) {
            return $this
                ->addUsingAlias(ContactPeer::CITY_ID, $cities->getCityId(), $comparison);
        } elseif ($cities instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(ContactPeer::CITY_ID, $cities->toKeyValue('PrimaryKey', 'CityId'), $comparison);
        } else {
            throw new PropelException('filterByCityKey() only accepts arguments of type Cities or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the CityKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function joinCityKey($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('CityKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'CityKey');
        }

        return $this;
    }

    /**
     * Use the CityKey relation Cities object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\CitiesQuery A secondary query class using the current class as primary query
     */
    public function useCityKeyQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinCityKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'CityKey', '\Core\CitiesQuery');
    }

    /**
     * Filter the query by a related Institute object
     *
     * @param   Institute|PropelObjectCollection $institute  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 ContactQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstitute($institute, $comparison = null)
    {
        if ($institute instanceof Institute) {
            return $this
                ->addUsingAlias(ContactPeer::CONTACT_ID, $institute->getContactId(), $comparison);
        } elseif ($institute instanceof PropelObjectCollection) {
            return $this
                ->useInstituteQuery()
                ->filterByPrimaryKeys($institute->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByInstitute() only accepts arguments of type Institute or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Institute relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function joinInstitute($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Institute');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Institute');
        }

        return $this;
    }

    /**
     * Use the Institute relation Institute object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\InstituteQuery A secondary query class using the current class as primary query
     */
    public function useInstituteQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstitute($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Institute', '\Core\InstituteQuery');
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 ContactQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByEmployeeRelatedByResidenceId($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(ContactPeer::CONTACT_ID, $employee->getResidenceId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            return $this
                ->useEmployeeRelatedByResidenceIdQuery()
                ->filterByPrimaryKeys($employee->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByEmployeeRelatedByResidenceId() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the EmployeeRelatedByResidenceId relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function joinEmployeeRelatedByResidenceId($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('EmployeeRelatedByResidenceId');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'EmployeeRelatedByResidenceId');
        }

        return $this;
    }

    /**
     * Use the EmployeeRelatedByResidenceId relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useEmployeeRelatedByResidenceIdQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinEmployeeRelatedByResidenceId($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'EmployeeRelatedByResidenceId', '\Employee\EmployeeQuery');
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 ContactQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByEmployeeRelatedByAddressId($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(ContactPeer::CONTACT_ID, $employee->getAddressId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            return $this
                ->useEmployeeRelatedByAddressIdQuery()
                ->filterByPrimaryKeys($employee->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByEmployeeRelatedByAddressId() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the EmployeeRelatedByAddressId relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function joinEmployeeRelatedByAddressId($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('EmployeeRelatedByAddressId');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'EmployeeRelatedByAddressId');
        }

        return $this;
    }

    /**
     * Use the EmployeeRelatedByAddressId relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useEmployeeRelatedByAddressIdQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinEmployeeRelatedByAddressId($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'EmployeeRelatedByAddressId', '\Employee\EmployeeQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Contact $contact Object to remove from the list of results
     *
     * @return ContactQuery The current query, for fluid interface
     */
    public function prune($contact = null)
    {
        if ($contact) {
            $this->addUsingAlias(ContactPeer::CONTACT_ID, $contact->getContactId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
