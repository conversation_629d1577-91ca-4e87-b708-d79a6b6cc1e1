<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\BankProfile;
use Core\Institute;
use Core\InstituteBankProfile;
use Core\InstituteBankProfilePeer;
use Core\InstituteBankProfileQuery;

/**
 * Base class that represents a query for the 'institute_bank_profile' table.
 *
 *
 *
 * @method InstituteBankProfileQuery orderById($order = Criteria::ASC) Order by the id column
 * @method InstituteBankProfileQuery orderByInstitute($order = Criteria::ASC) Order by the institute column
 * @method InstituteBankProfileQuery orderByBankProfile($order = Criteria::ASC) Order by the bank_profile column
 *
 * @method InstituteBankProfileQuery groupById() Group by the id column
 * @method InstituteBankProfileQuery groupByInstitute() Group by the institute column
 * @method InstituteBankProfileQuery groupByBankProfile() Group by the bank_profile column
 *
 * @method InstituteBankProfileQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method InstituteBankProfileQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method InstituteBankProfileQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method InstituteBankProfileQuery leftJoinInstituteBankProfileBankProfileKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the InstituteBankProfileBankProfileKey relation
 * @method InstituteBankProfileQuery rightJoinInstituteBankProfileBankProfileKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the InstituteBankProfileBankProfileKey relation
 * @method InstituteBankProfileQuery innerJoinInstituteBankProfileBankProfileKey($relationAlias = null) Adds a INNER JOIN clause to the query using the InstituteBankProfileBankProfileKey relation
 *
 * @method InstituteBankProfileQuery leftJoinInstituteBankProfileKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the InstituteBankProfileKey relation
 * @method InstituteBankProfileQuery rightJoinInstituteBankProfileKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the InstituteBankProfileKey relation
 * @method InstituteBankProfileQuery innerJoinInstituteBankProfileKey($relationAlias = null) Adds a INNER JOIN clause to the query using the InstituteBankProfileKey relation
 *
 * @method InstituteBankProfile findOne(PropelPDO $con = null) Return the first InstituteBankProfile matching the query
 * @method InstituteBankProfile findOneOrCreate(PropelPDO $con = null) Return the first InstituteBankProfile matching the query, or a new InstituteBankProfile object populated from the query conditions when no match is found
 *
 * @method InstituteBankProfile findOneByInstitute(int $institute) Return the first InstituteBankProfile filtered by the institute column
 * @method InstituteBankProfile findOneByBankProfile(int $bank_profile) Return the first InstituteBankProfile filtered by the bank_profile column
 *
 * @method array findById(int $id) Return InstituteBankProfile objects filtered by the id column
 * @method array findByInstitute(int $institute) Return InstituteBankProfile objects filtered by the institute column
 * @method array findByBankProfile(int $bank_profile) Return InstituteBankProfile objects filtered by the bank_profile column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseInstituteBankProfileQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseInstituteBankProfileQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\InstituteBankProfile';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new InstituteBankProfileQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   InstituteBankProfileQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return InstituteBankProfileQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof InstituteBankProfileQuery) {
            return $criteria;
        }
        $query = new InstituteBankProfileQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   InstituteBankProfile|InstituteBankProfile[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = InstituteBankProfilePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(InstituteBankProfilePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 InstituteBankProfile A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 InstituteBankProfile A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "institute", "bank_profile" FROM "institute_bank_profile" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new InstituteBankProfile();
            $obj->hydrate($row);
            InstituteBankProfilePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return InstituteBankProfile|InstituteBankProfile[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|InstituteBankProfile[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(InstituteBankProfilePeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(InstituteBankProfilePeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(InstituteBankProfilePeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(InstituteBankProfilePeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstituteBankProfilePeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the institute column
     *
     * Example usage:
     * <code>
     * $query->filterByInstitute(1234); // WHERE institute = 1234
     * $query->filterByInstitute(array(12, 34)); // WHERE institute IN (12, 34)
     * $query->filterByInstitute(array('min' => 12)); // WHERE institute >= 12
     * $query->filterByInstitute(array('max' => 12)); // WHERE institute <= 12
     * </code>
     *
     * @see       filterByInstituteBankProfileKey()
     *
     * @param     mixed $institute The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function filterByInstitute($institute = null, $comparison = null)
    {
        if (is_array($institute)) {
            $useMinMax = false;
            if (isset($institute['min'])) {
                $this->addUsingAlias(InstituteBankProfilePeer::INSTITUTE, $institute['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($institute['max'])) {
                $this->addUsingAlias(InstituteBankProfilePeer::INSTITUTE, $institute['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstituteBankProfilePeer::INSTITUTE, $institute, $comparison);
    }

    /**
     * Filter the query on the bank_profile column
     *
     * Example usage:
     * <code>
     * $query->filterByBankProfile(1234); // WHERE bank_profile = 1234
     * $query->filterByBankProfile(array(12, 34)); // WHERE bank_profile IN (12, 34)
     * $query->filterByBankProfile(array('min' => 12)); // WHERE bank_profile >= 12
     * $query->filterByBankProfile(array('max' => 12)); // WHERE bank_profile <= 12
     * </code>
     *
     * @see       filterByInstituteBankProfileBankProfileKey()
     *
     * @param     mixed $bankProfile The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function filterByBankProfile($bankProfile = null, $comparison = null)
    {
        if (is_array($bankProfile)) {
            $useMinMax = false;
            if (isset($bankProfile['min'])) {
                $this->addUsingAlias(InstituteBankProfilePeer::BANK_PROFILE, $bankProfile['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($bankProfile['max'])) {
                $this->addUsingAlias(InstituteBankProfilePeer::BANK_PROFILE, $bankProfile['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstituteBankProfilePeer::BANK_PROFILE, $bankProfile, $comparison);
    }

    /**
     * Filter the query by a related BankProfile object
     *
     * @param   BankProfile|PropelObjectCollection $bankProfile The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteBankProfileQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstituteBankProfileBankProfileKey($bankProfile, $comparison = null)
    {
        if ($bankProfile instanceof BankProfile) {
            return $this
                ->addUsingAlias(InstituteBankProfilePeer::BANK_PROFILE, $bankProfile->getId(), $comparison);
        } elseif ($bankProfile instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(InstituteBankProfilePeer::BANK_PROFILE, $bankProfile->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByInstituteBankProfileBankProfileKey() only accepts arguments of type BankProfile or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the InstituteBankProfileBankProfileKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function joinInstituteBankProfileBankProfileKey($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('InstituteBankProfileBankProfileKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'InstituteBankProfileBankProfileKey');
        }

        return $this;
    }

    /**
     * Use the InstituteBankProfileBankProfileKey relation BankProfile object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\BankProfileQuery A secondary query class using the current class as primary query
     */
    public function useInstituteBankProfileBankProfileKeyQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstituteBankProfileBankProfileKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'InstituteBankProfileBankProfileKey', '\Core\BankProfileQuery');
    }

    /**
     * Filter the query by a related Institute object
     *
     * @param   Institute|PropelObjectCollection $institute The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteBankProfileQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstituteBankProfileKey($institute, $comparison = null)
    {
        if ($institute instanceof Institute) {
            return $this
                ->addUsingAlias(InstituteBankProfilePeer::INSTITUTE, $institute->getInstituteId(), $comparison);
        } elseif ($institute instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(InstituteBankProfilePeer::INSTITUTE, $institute->toKeyValue('InstituteId', 'InstituteId'), $comparison);
        } else {
            throw new PropelException('filterByInstituteBankProfileKey() only accepts arguments of type Institute or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the InstituteBankProfileKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function joinInstituteBankProfileKey($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('InstituteBankProfileKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'InstituteBankProfileKey');
        }

        return $this;
    }

    /**
     * Use the InstituteBankProfileKey relation Institute object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\InstituteQuery A secondary query class using the current class as primary query
     */
    public function useInstituteBankProfileKeyQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstituteBankProfileKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'InstituteBankProfileKey', '\Core\InstituteQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   InstituteBankProfile $instituteBankProfile Object to remove from the list of results
     *
     * @return InstituteBankProfileQuery The current query, for fluid interface
     */
    public function prune($instituteBankProfile = null)
    {
        if ($instituteBankProfile) {
            $this->addUsingAlias(InstituteBankProfilePeer::ID, $instituteBankProfile->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
