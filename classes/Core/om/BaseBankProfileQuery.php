<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\BankProfile;
use Core\BankProfilePeer;
use Core\BankProfileQuery;
use Core\InstituteBankProfile;

/**
 * Base class that represents a query for the 'bank_profile' table.
 *
 *
 *
 * @method BankProfileQuery orderById($order = Criteria::ASC) Order by the id column
 * @method BankProfileQuery orderByCountryCode($order = Criteria::ASC) Order by the country_code column
 * @method BankProfileQuery orderByCheckCode($order = Criteria::ASC) Order by the check_code column
 * @method BankProfileQuery orderByBban($order = Criteria::ASC) Order by the bban column
 * @method BankProfileQuery orderByDenomination($order = Criteria::ASC) Order by the denomination column
 *
 * @method BankProfileQuery groupById() Group by the id column
 * @method BankProfileQuery groupByCountryCode() Group by the country_code column
 * @method BankProfileQuery groupByCheckCode() Group by the check_code column
 * @method BankProfileQuery groupByBban() Group by the bban column
 * @method BankProfileQuery groupByDenomination() Group by the denomination column
 *
 * @method BankProfileQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method BankProfileQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method BankProfileQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method BankProfileQuery leftJoinInstituteBankProfile($relationAlias = null) Adds a LEFT JOIN clause to the query using the InstituteBankProfile relation
 * @method BankProfileQuery rightJoinInstituteBankProfile($relationAlias = null) Adds a RIGHT JOIN clause to the query using the InstituteBankProfile relation
 * @method BankProfileQuery innerJoinInstituteBankProfile($relationAlias = null) Adds a INNER JOIN clause to the query using the InstituteBankProfile relation
 *
 * @method BankProfile findOne(PropelPDO $con = null) Return the first BankProfile matching the query
 * @method BankProfile findOneOrCreate(PropelPDO $con = null) Return the first BankProfile matching the query, or a new BankProfile object populated from the query conditions when no match is found
 *
 * @method BankProfile findOneByCountryCode(string $country_code) Return the first BankProfile filtered by the country_code column
 * @method BankProfile findOneByCheckCode(string $check_code) Return the first BankProfile filtered by the check_code column
 * @method BankProfile findOneByBban(string $bban) Return the first BankProfile filtered by the bban column
 * @method BankProfile findOneByDenomination(string $denomination) Return the first BankProfile filtered by the denomination column
 *
 * @method array findById(int $id) Return BankProfile objects filtered by the id column
 * @method array findByCountryCode(string $country_code) Return BankProfile objects filtered by the country_code column
 * @method array findByCheckCode(string $check_code) Return BankProfile objects filtered by the check_code column
 * @method array findByBban(string $bban) Return BankProfile objects filtered by the bban column
 * @method array findByDenomination(string $denomination) Return BankProfile objects filtered by the denomination column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseBankProfileQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseBankProfileQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\BankProfile';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new BankProfileQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   BankProfileQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return BankProfileQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof BankProfileQuery) {
            return $criteria;
        }
        $query = new BankProfileQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   BankProfile|BankProfile[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = BankProfilePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(BankProfilePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 BankProfile A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 BankProfile A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "country_code", "check_code", "bban", "denomination" FROM "bank_profile" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new BankProfile();
            $obj->hydrate($row);
            BankProfilePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return BankProfile|BankProfile[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|BankProfile[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(BankProfilePeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(BankProfilePeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(BankProfilePeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(BankProfilePeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(BankProfilePeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the country_code column
     *
     * Example usage:
     * <code>
     * $query->filterByCountryCode('fooValue');   // WHERE country_code = 'fooValue'
     * $query->filterByCountryCode('%fooValue%'); // WHERE country_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $countryCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterByCountryCode($countryCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($countryCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $countryCode)) {
                $countryCode = str_replace('*', '%', $countryCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(BankProfilePeer::COUNTRY_CODE, $countryCode, $comparison);
    }

    /**
     * Filter the query on the check_code column
     *
     * Example usage:
     * <code>
     * $query->filterByCheckCode('fooValue');   // WHERE check_code = 'fooValue'
     * $query->filterByCheckCode('%fooValue%'); // WHERE check_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $checkCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterByCheckCode($checkCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($checkCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $checkCode)) {
                $checkCode = str_replace('*', '%', $checkCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(BankProfilePeer::CHECK_CODE, $checkCode, $comparison);
    }

    /**
     * Filter the query on the bban column
     *
     * Example usage:
     * <code>
     * $query->filterByBban('fooValue');   // WHERE bban = 'fooValue'
     * $query->filterByBban('%fooValue%'); // WHERE bban LIKE '%fooValue%'
     * </code>
     *
     * @param     string $bban The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterByBban($bban = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($bban)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $bban)) {
                $bban = str_replace('*', '%', $bban);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(BankProfilePeer::BBAN, $bban, $comparison);
    }

    /**
     * Filter the query on the denomination column
     *
     * Example usage:
     * <code>
     * $query->filterByDenomination('fooValue');   // WHERE denomination = 'fooValue'
     * $query->filterByDenomination('%fooValue%'); // WHERE denomination LIKE '%fooValue%'
     * </code>
     *
     * @param     string $denomination The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function filterByDenomination($denomination = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($denomination)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $denomination)) {
                $denomination = str_replace('*', '%', $denomination);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(BankProfilePeer::DENOMINATION, $denomination, $comparison);
    }

    /**
     * Filter the query by a related InstituteBankProfile object
     *
     * @param   InstituteBankProfile|PropelObjectCollection $instituteBankProfile  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 BankProfileQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstituteBankProfile($instituteBankProfile, $comparison = null)
    {
        if ($instituteBankProfile instanceof InstituteBankProfile) {
            return $this
                ->addUsingAlias(BankProfilePeer::ID, $instituteBankProfile->getBankProfile(), $comparison);
        } elseif ($instituteBankProfile instanceof PropelObjectCollection) {
            return $this
                ->useInstituteBankProfileQuery()
                ->filterByPrimaryKeys($instituteBankProfile->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByInstituteBankProfile() only accepts arguments of type InstituteBankProfile or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the InstituteBankProfile relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function joinInstituteBankProfile($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('InstituteBankProfile');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'InstituteBankProfile');
        }

        return $this;
    }

    /**
     * Use the InstituteBankProfile relation InstituteBankProfile object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\InstituteBankProfileQuery A secondary query class using the current class as primary query
     */
    public function useInstituteBankProfileQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstituteBankProfile($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'InstituteBankProfile', '\Core\InstituteBankProfileQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   BankProfile $bankProfile Object to remove from the list of results
     *
     * @return BankProfileQuery The current query, for fluid interface
     */
    public function prune($bankProfile = null)
    {
        if ($bankProfile) {
            $this->addUsingAlias(BankProfilePeer::ID, $bankProfile->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
