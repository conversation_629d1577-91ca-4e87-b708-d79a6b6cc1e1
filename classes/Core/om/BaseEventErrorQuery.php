<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\EventError;
use Core\EventErrorPeer;
use Core\EventErrorQuery;

/**
 * Base class that represents a query for the 'event_error' table.
 *
 *
 *
 * @method EventErrorQuery orderById($order = Criteria::ASC) Order by the id column
 * @method EventErrorQuery orderByLevelNr($order = Criteria::ASC) Order by the level_nr column
 * @method EventErrorQuery orderByLevelDescr($order = Criteria::ASC) Order by the level_descr column
 * @method EventErrorQuery orderByDescription($order = Criteria::ASC) Order by the description column
 * @method EventErrorQuery orderByPath($order = Criteria::ASC) Order by the path column
 * @method EventErrorQuery orderByLine($order = Criteria::ASC) Order by the line column
 * @method EventErrorQuery orderByErrorTime($order = Criteria::ASC) Order by the error_time column
 *
 * @method EventErrorQuery groupById() Group by the id column
 * @method EventErrorQuery groupByLevelNr() Group by the level_nr column
 * @method EventErrorQuery groupByLevelDescr() Group by the level_descr column
 * @method EventErrorQuery groupByDescription() Group by the description column
 * @method EventErrorQuery groupByPath() Group by the path column
 * @method EventErrorQuery groupByLine() Group by the line column
 * @method EventErrorQuery groupByErrorTime() Group by the error_time column
 *
 * @method EventErrorQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method EventErrorQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method EventErrorQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method EventError findOne(PropelPDO $con = null) Return the first EventError matching the query
 * @method EventError findOneOrCreate(PropelPDO $con = null) Return the first EventError matching the query, or a new EventError object populated from the query conditions when no match is found
 *
 * @method EventError findOneByLevelNr(int $level_nr) Return the first EventError filtered by the level_nr column
 * @method EventError findOneByLevelDescr(string $level_descr) Return the first EventError filtered by the level_descr column
 * @method EventError findOneByDescription(string $description) Return the first EventError filtered by the description column
 * @method EventError findOneByPath(string $path) Return the first EventError filtered by the path column
 * @method EventError findOneByLine(string $line) Return the first EventError filtered by the line column
 * @method EventError findOneByErrorTime(string $error_time) Return the first EventError filtered by the error_time column
 *
 * @method array findById(int $id) Return EventError objects filtered by the id column
 * @method array findByLevelNr(int $level_nr) Return EventError objects filtered by the level_nr column
 * @method array findByLevelDescr(string $level_descr) Return EventError objects filtered by the level_descr column
 * @method array findByDescription(string $description) Return EventError objects filtered by the description column
 * @method array findByPath(string $path) Return EventError objects filtered by the path column
 * @method array findByLine(string $line) Return EventError objects filtered by the line column
 * @method array findByErrorTime(string $error_time) Return EventError objects filtered by the error_time column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseEventErrorQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseEventErrorQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\EventError';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new EventErrorQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   EventErrorQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return EventErrorQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof EventErrorQuery) {
            return $criteria;
        }
        $query = new EventErrorQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   EventError|EventError[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = EventErrorPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(EventErrorPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 EventError A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 EventError A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "level_nr", "level_descr", "description", "path", "line", "error_time" FROM "event_error" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new EventError();
            $obj->hydrate($row);
            EventErrorPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return EventError|EventError[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|EventError[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(EventErrorPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(EventErrorPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(EventErrorPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(EventErrorPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the level_nr column
     *
     * Example usage:
     * <code>
     * $query->filterByLevelNr(1234); // WHERE level_nr = 1234
     * $query->filterByLevelNr(array(12, 34)); // WHERE level_nr IN (12, 34)
     * $query->filterByLevelNr(array('min' => 12)); // WHERE level_nr >= 12
     * $query->filterByLevelNr(array('max' => 12)); // WHERE level_nr <= 12
     * </code>
     *
     * @param     mixed $levelNr The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByLevelNr($levelNr = null, $comparison = null)
    {
        if (is_array($levelNr)) {
            $useMinMax = false;
            if (isset($levelNr['min'])) {
                $this->addUsingAlias(EventErrorPeer::LEVEL_NR, $levelNr['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($levelNr['max'])) {
                $this->addUsingAlias(EventErrorPeer::LEVEL_NR, $levelNr['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::LEVEL_NR, $levelNr, $comparison);
    }

    /**
     * Filter the query on the level_descr column
     *
     * Example usage:
     * <code>
     * $query->filterByLevelDescr('fooValue');   // WHERE level_descr = 'fooValue'
     * $query->filterByLevelDescr('%fooValue%'); // WHERE level_descr LIKE '%fooValue%'
     * </code>
     *
     * @param     string $levelDescr The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByLevelDescr($levelDescr = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($levelDescr)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $levelDescr)) {
                $levelDescr = str_replace('*', '%', $levelDescr);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::LEVEL_DESCR, $levelDescr, $comparison);
    }

    /**
     * Filter the query on the description column
     *
     * Example usage:
     * <code>
     * $query->filterByDescription('fooValue');   // WHERE description = 'fooValue'
     * $query->filterByDescription('%fooValue%'); // WHERE description LIKE '%fooValue%'
     * </code>
     *
     * @param     string $description The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByDescription($description = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($description)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $description)) {
                $description = str_replace('*', '%', $description);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::DESCRIPTION, $description, $comparison);
    }

    /**
     * Filter the query on the path column
     *
     * Example usage:
     * <code>
     * $query->filterByPath('fooValue');   // WHERE path = 'fooValue'
     * $query->filterByPath('%fooValue%'); // WHERE path LIKE '%fooValue%'
     * </code>
     *
     * @param     string $path The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByPath($path = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($path)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $path)) {
                $path = str_replace('*', '%', $path);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::PATH, $path, $comparison);
    }

    /**
     * Filter the query on the line column
     *
     * Example usage:
     * <code>
     * $query->filterByLine('fooValue');   // WHERE line = 'fooValue'
     * $query->filterByLine('%fooValue%'); // WHERE line LIKE '%fooValue%'
     * </code>
     *
     * @param     string $line The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByLine($line = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($line)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $line)) {
                $line = str_replace('*', '%', $line);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::LINE, $line, $comparison);
    }

    /**
     * Filter the query on the error_time column
     *
     * Example usage:
     * <code>
     * $query->filterByErrorTime('2011-03-14'); // WHERE error_time = '2011-03-14'
     * $query->filterByErrorTime('now'); // WHERE error_time = '2011-03-14'
     * $query->filterByErrorTime(array('max' => 'yesterday')); // WHERE error_time < '2011-03-13'
     * </code>
     *
     * @param     mixed $errorTime The value to use as filter.
     *              Values can be integers (unix timestamps), DateTime objects, or strings.
     *              Empty strings are treated as NULL.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function filterByErrorTime($errorTime = null, $comparison = null)
    {
        if (is_array($errorTime)) {
            $useMinMax = false;
            if (isset($errorTime['min'])) {
                $this->addUsingAlias(EventErrorPeer::ERROR_TIME, $errorTime['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($errorTime['max'])) {
                $this->addUsingAlias(EventErrorPeer::ERROR_TIME, $errorTime['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EventErrorPeer::ERROR_TIME, $errorTime, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   EventError $eventError Object to remove from the list of results
     *
     * @return EventErrorQuery The current query, for fluid interface
     */
    public function prune($eventError = null)
    {
        if ($eventError) {
            $this->addUsingAlias(EventErrorPeer::ID, $eventError->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
