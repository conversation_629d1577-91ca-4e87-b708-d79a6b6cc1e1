<?php

namespace Core\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Core\ContactPeer;
use Core\Institute;
use Core\InstituteBankProfilePeer;
use Core\InstitutePeer;
use Core\map\InstituteTableMap;
use Employee\EmployeePeer;

/**
 * Base static class for performing query and update operations on the 'institute' table.
 *
 *
 *
 * @package propel.generator.Core.om
 */
abstract class BaseInstitutePeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'institute';

    /** the related Propel class for this table */
    const OM_CLASS = 'Core\\Institute';

    /** the related TableMap class for this table */
    const TM_CLASS = 'InstituteTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 44;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 44;

    /** the column name for the institute_id field */
    const INSTITUTE_ID = 'institute.institute_id';

    /** the column name for the name field */
    const NAME = 'institute.name';

    /** the column name for the mechan_code field */
    const MECHAN_CODE = 'institute.mechan_code';

    /** the column name for the contact_id field */
    const CONTACT_ID = 'institute.contact_id';

    /** the column name for the fiscal_code field */
    const FISCAL_CODE = 'institute.fiscal_code';

    /** the column name for the school_type field */
    const SCHOOL_TYPE = 'institute.school_type';

    /** the column name for the parent field */
    const PARENT = 'institute.parent';

    /** the column name for the def field */
    const DEF = 'institute.def';

    /** the column name for the dir_name field */
    const DIR_NAME = 'institute.dir_name';

    /** the column name for the dir_surname field */
    const DIR_SURNAME = 'institute.dir_surname';

    /** the column name for the adir_name field */
    const ADIR_NAME = 'institute.adir_name';

    /** the column name for the adir_surname field */
    const ADIR_SURNAME = 'institute.adir_surname';

    /** the column name for the pres_ge_name field */
    const PRES_GE_NAME = 'institute.pres_ge_name';

    /** the column name for the pres_ge_surname field */
    const PRES_GE_SURNAME = 'institute.pres_ge_surname';

    /** the column name for the seg_cons_name field */
    const SEG_CONS_NAME = 'institute.seg_cons_name';

    /** the column name for the seg_cons_surname field */
    const SEG_CONS_SURNAME = 'institute.seg_cons_surname';

    /** the column name for the pres_con_name field */
    const PRES_CON_NAME = 'institute.pres_con_name';

    /** the column name for the pres_con_surname field */
    const PRES_CON_SURNAME = 'institute.pres_con_surname';

    /** the column name for the dir_fiscal_code field */
    const DIR_FISCAL_CODE = 'institute.dir_fiscal_code';

    /** the column name for the school_fiscal_code field */
    const SCHOOL_FISCAL_CODE = 'institute.school_fiscal_code';

    /** the column name for the inpdap_code field */
    const INPDAP_CODE = 'institute.inpdap_code';

    /** the column name for the assicurazioni_sanitarie field */
    const ASSICURAZIONI_SANITARIE = 'institute.assicurazioni_sanitarie';

    /** the column name for the dir_sesso field */
    const DIR_SESSO = 'institute.dir_sesso';

    /** the column name for the dir_birth field */
    const DIR_BIRTH = 'institute.dir_birth';

    /** the column name for the dir_city field */
    const DIR_CITY = 'institute.dir_city';

    /** the column name for the postal_account field */
    const POSTAL_ACCOUNT = 'institute.postal_account';

    /** the column name for the ateco_code field */
    const ATECO_CODE = 'institute.ateco_code';

    /** the column name for the activity_code field */
    const ACTIVITY_CODE = 'institute.activity_code';

    /** the column name for the dir_curr_addr field */
    const DIR_CURR_ADDR = 'institute.dir_curr_addr';

    /** the column name for the dir_curr_city field */
    const DIR_CURR_CITY = 'institute.dir_curr_city';

    /** the column name for the dir_curr_phone field */
    const DIR_CURR_PHONE = 'institute.dir_curr_phone';

    /** the column name for the dir_emp_id field */
    const DIR_EMP_ID = 'institute.dir_emp_id';

    /** the column name for the adir_emp_id field */
    const ADIR_EMP_ID = 'institute.adir_emp_id';

    /** the column name for the presge_emp_id field */
    const PRESGE_EMP_ID = 'institute.presge_emp_id';

    /** the column name for the segcons_emp_id field */
    const SEGCONS_EMP_ID = 'institute.segcons_emp_id';

    /** the column name for the prescon_emp_id field */
    const PRESCON_EMP_ID = 'institute.prescon_emp_id';

    /** the column name for the respacq_emp_id field */
    const RESPACQ_EMP_ID = 'institute.respacq_emp_id';

    /** the column name for the job_director_id field */
    const JOB_DIRECTOR_ID = 'institute.job_director_id';

    /** the column name for the job_vice_director_id field */
    const JOB_VICE_DIRECTOR_ID = 'institute.job_vice_director_id';

    /** the column name for the job_dsga_id field */
    const JOB_DSGA_ID = 'institute.job_dsga_id';

    /** the column name for the job_personnel_id field */
    const JOB_PERSONNEL_ID = 'institute.job_personnel_id';

    /** the column name for the job_accounting_id field */
    const JOB_ACCOUNTING_ID = 'institute.job_accounting_id';

    /** the column name for the job_warehouse_id field */
    const JOB_WAREHOUSE_ID = 'institute.job_warehouse_id';

    /** the column name for the job_registry_id field */
    const JOB_REGISTRY_ID = 'institute.job_registry_id';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of Institute objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array Institute[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. InstitutePeer::$fieldNames[InstitutePeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('InstituteId', 'Name', 'MechanCode', 'ContactId', 'FiscalCode', 'SchoolType', 'Parent', 'Def', 'DirName', 'DirSurname', 'AdirName', 'AdirSurname', 'PresGeName', 'PresGeSurname', 'SegConsName', 'SegConsSurname', 'PresConName', 'PresConSurname', 'DirFiscalCode', 'SchoolFiscalCode', 'InpdapCode', 'AssicurazioniSanitarie', 'DirSesso', 'DirBirth', 'DirCity', 'PostalAccount', 'AtecoCode', 'ActivityCode', 'DirCurrAddr', 'DirCurrCity', 'DirCurrPhone', 'DirEmpId', 'AdirEmpId', 'PresgeEmpId', 'SegconsEmpId', 'PresconEmpId', 'RespacqEmpId', 'JobDirectorId', 'JobViceDirectorId', 'JobDSGAId', 'JobPersonnelId', 'JobAccountingId', 'JobWarehouseId', 'JobRegistryId', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('instituteId', 'name', 'mechanCode', 'contactId', 'fiscalCode', 'schoolType', 'parent', 'def', 'dirName', 'dirSurname', 'adirName', 'adirSurname', 'presGeName', 'presGeSurname', 'segConsName', 'segConsSurname', 'presConName', 'presConSurname', 'dirFiscalCode', 'schoolFiscalCode', 'inpdapCode', 'assicurazioniSanitarie', 'dirSesso', 'dirBirth', 'dirCity', 'postalAccount', 'atecoCode', 'activityCode', 'dirCurrAddr', 'dirCurrCity', 'dirCurrPhone', 'dirEmpId', 'adirEmpId', 'presgeEmpId', 'segconsEmpId', 'presconEmpId', 'respacqEmpId', 'jobDirectorId', 'jobViceDirectorId', 'jobDSGAId', 'jobPersonnelId', 'jobAccountingId', 'jobWarehouseId', 'jobRegistryId', ),
        BasePeer::TYPE_COLNAME => array (InstitutePeer::INSTITUTE_ID, InstitutePeer::NAME, InstitutePeer::MECHAN_CODE, InstitutePeer::CONTACT_ID, InstitutePeer::FISCAL_CODE, InstitutePeer::SCHOOL_TYPE, InstitutePeer::PARENT, InstitutePeer::DEF, InstitutePeer::DIR_NAME, InstitutePeer::DIR_SURNAME, InstitutePeer::ADIR_NAME, InstitutePeer::ADIR_SURNAME, InstitutePeer::PRES_GE_NAME, InstitutePeer::PRES_GE_SURNAME, InstitutePeer::SEG_CONS_NAME, InstitutePeer::SEG_CONS_SURNAME, InstitutePeer::PRES_CON_NAME, InstitutePeer::PRES_CON_SURNAME, InstitutePeer::DIR_FISCAL_CODE, InstitutePeer::SCHOOL_FISCAL_CODE, InstitutePeer::INPDAP_CODE, InstitutePeer::ASSICURAZIONI_SANITARIE, InstitutePeer::DIR_SESSO, InstitutePeer::DIR_BIRTH, InstitutePeer::DIR_CITY, InstitutePeer::POSTAL_ACCOUNT, InstitutePeer::ATECO_CODE, InstitutePeer::ACTIVITY_CODE, InstitutePeer::DIR_CURR_ADDR, InstitutePeer::DIR_CURR_CITY, InstitutePeer::DIR_CURR_PHONE, InstitutePeer::DIR_EMP_ID, InstitutePeer::ADIR_EMP_ID, InstitutePeer::PRESGE_EMP_ID, InstitutePeer::SEGCONS_EMP_ID, InstitutePeer::PRESCON_EMP_ID, InstitutePeer::RESPACQ_EMP_ID, InstitutePeer::JOB_DIRECTOR_ID, InstitutePeer::JOB_VICE_DIRECTOR_ID, InstitutePeer::JOB_DSGA_ID, InstitutePeer::JOB_PERSONNEL_ID, InstitutePeer::JOB_ACCOUNTING_ID, InstitutePeer::JOB_WAREHOUSE_ID, InstitutePeer::JOB_REGISTRY_ID, ),
        BasePeer::TYPE_RAW_COLNAME => array ('INSTITUTE_ID', 'NAME', 'MECHAN_CODE', 'CONTACT_ID', 'FISCAL_CODE', 'SCHOOL_TYPE', 'PARENT', 'DEF', 'DIR_NAME', 'DIR_SURNAME', 'ADIR_NAME', 'ADIR_SURNAME', 'PRES_GE_NAME', 'PRES_GE_SURNAME', 'SEG_CONS_NAME', 'SEG_CONS_SURNAME', 'PRES_CON_NAME', 'PRES_CON_SURNAME', 'DIR_FISCAL_CODE', 'SCHOOL_FISCAL_CODE', 'INPDAP_CODE', 'ASSICURAZIONI_SANITARIE', 'DIR_SESSO', 'DIR_BIRTH', 'DIR_CITY', 'POSTAL_ACCOUNT', 'ATECO_CODE', 'ACTIVITY_CODE', 'DIR_CURR_ADDR', 'DIR_CURR_CITY', 'DIR_CURR_PHONE', 'DIR_EMP_ID', 'ADIR_EMP_ID', 'PRESGE_EMP_ID', 'SEGCONS_EMP_ID', 'PRESCON_EMP_ID', 'RESPACQ_EMP_ID', 'JOB_DIRECTOR_ID', 'JOB_VICE_DIRECTOR_ID', 'JOB_DSGA_ID', 'JOB_PERSONNEL_ID', 'JOB_ACCOUNTING_ID', 'JOB_WAREHOUSE_ID', 'JOB_REGISTRY_ID', ),
        BasePeer::TYPE_FIELDNAME => array ('institute_id', 'name', 'mechan_code', 'contact_id', 'fiscal_code', 'school_type', 'parent', 'def', 'dir_name', 'dir_surname', 'adir_name', 'adir_surname', 'pres_ge_name', 'pres_ge_surname', 'seg_cons_name', 'seg_cons_surname', 'pres_con_name', 'pres_con_surname', 'dir_fiscal_code', 'school_fiscal_code', 'inpdap_code', 'assicurazioni_sanitarie', 'dir_sesso', 'dir_birth', 'dir_city', 'postal_account', 'ateco_code', 'activity_code', 'dir_curr_addr', 'dir_curr_city', 'dir_curr_phone', 'dir_emp_id', 'adir_emp_id', 'presge_emp_id', 'segcons_emp_id', 'prescon_emp_id', 'respacq_emp_id', 'job_director_id', 'job_vice_director_id', 'job_dsga_id', 'job_personnel_id', 'job_accounting_id', 'job_warehouse_id', 'job_registry_id', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. InstitutePeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('InstituteId' => 0, 'Name' => 1, 'MechanCode' => 2, 'ContactId' => 3, 'FiscalCode' => 4, 'SchoolType' => 5, 'Parent' => 6, 'Def' => 7, 'DirName' => 8, 'DirSurname' => 9, 'AdirName' => 10, 'AdirSurname' => 11, 'PresGeName' => 12, 'PresGeSurname' => 13, 'SegConsName' => 14, 'SegConsSurname' => 15, 'PresConName' => 16, 'PresConSurname' => 17, 'DirFiscalCode' => 18, 'SchoolFiscalCode' => 19, 'InpdapCode' => 20, 'AssicurazioniSanitarie' => 21, 'DirSesso' => 22, 'DirBirth' => 23, 'DirCity' => 24, 'PostalAccount' => 25, 'AtecoCode' => 26, 'ActivityCode' => 27, 'DirCurrAddr' => 28, 'DirCurrCity' => 29, 'DirCurrPhone' => 30, 'DirEmpId' => 31, 'AdirEmpId' => 32, 'PresgeEmpId' => 33, 'SegconsEmpId' => 34, 'PresconEmpId' => 35, 'RespacqEmpId' => 36, 'JobDirectorId' => 37, 'JobViceDirectorId' => 38, 'JobDSGAId' => 39, 'JobPersonnelId' => 40, 'JobAccountingId' => 41, 'JobWarehouseId' => 42, 'JobRegistryId' => 43, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('instituteId' => 0, 'name' => 1, 'mechanCode' => 2, 'contactId' => 3, 'fiscalCode' => 4, 'schoolType' => 5, 'parent' => 6, 'def' => 7, 'dirName' => 8, 'dirSurname' => 9, 'adirName' => 10, 'adirSurname' => 11, 'presGeName' => 12, 'presGeSurname' => 13, 'segConsName' => 14, 'segConsSurname' => 15, 'presConName' => 16, 'presConSurname' => 17, 'dirFiscalCode' => 18, 'schoolFiscalCode' => 19, 'inpdapCode' => 20, 'assicurazioniSanitarie' => 21, 'dirSesso' => 22, 'dirBirth' => 23, 'dirCity' => 24, 'postalAccount' => 25, 'atecoCode' => 26, 'activityCode' => 27, 'dirCurrAddr' => 28, 'dirCurrCity' => 29, 'dirCurrPhone' => 30, 'dirEmpId' => 31, 'adirEmpId' => 32, 'presgeEmpId' => 33, 'segconsEmpId' => 34, 'presconEmpId' => 35, 'respacqEmpId' => 36, 'jobDirectorId' => 37, 'jobViceDirectorId' => 38, 'jobDSGAId' => 39, 'jobPersonnelId' => 40, 'jobAccountingId' => 41, 'jobWarehouseId' => 42, 'jobRegistryId' => 43, ),
        BasePeer::TYPE_COLNAME => array (InstitutePeer::INSTITUTE_ID => 0, InstitutePeer::NAME => 1, InstitutePeer::MECHAN_CODE => 2, InstitutePeer::CONTACT_ID => 3, InstitutePeer::FISCAL_CODE => 4, InstitutePeer::SCHOOL_TYPE => 5, InstitutePeer::PARENT => 6, InstitutePeer::DEF => 7, InstitutePeer::DIR_NAME => 8, InstitutePeer::DIR_SURNAME => 9, InstitutePeer::ADIR_NAME => 10, InstitutePeer::ADIR_SURNAME => 11, InstitutePeer::PRES_GE_NAME => 12, InstitutePeer::PRES_GE_SURNAME => 13, InstitutePeer::SEG_CONS_NAME => 14, InstitutePeer::SEG_CONS_SURNAME => 15, InstitutePeer::PRES_CON_NAME => 16, InstitutePeer::PRES_CON_SURNAME => 17, InstitutePeer::DIR_FISCAL_CODE => 18, InstitutePeer::SCHOOL_FISCAL_CODE => 19, InstitutePeer::INPDAP_CODE => 20, InstitutePeer::ASSICURAZIONI_SANITARIE => 21, InstitutePeer::DIR_SESSO => 22, InstitutePeer::DIR_BIRTH => 23, InstitutePeer::DIR_CITY => 24, InstitutePeer::POSTAL_ACCOUNT => 25, InstitutePeer::ATECO_CODE => 26, InstitutePeer::ACTIVITY_CODE => 27, InstitutePeer::DIR_CURR_ADDR => 28, InstitutePeer::DIR_CURR_CITY => 29, InstitutePeer::DIR_CURR_PHONE => 30, InstitutePeer::DIR_EMP_ID => 31, InstitutePeer::ADIR_EMP_ID => 32, InstitutePeer::PRESGE_EMP_ID => 33, InstitutePeer::SEGCONS_EMP_ID => 34, InstitutePeer::PRESCON_EMP_ID => 35, InstitutePeer::RESPACQ_EMP_ID => 36, InstitutePeer::JOB_DIRECTOR_ID => 37, InstitutePeer::JOB_VICE_DIRECTOR_ID => 38, InstitutePeer::JOB_DSGA_ID => 39, InstitutePeer::JOB_PERSONNEL_ID => 40, InstitutePeer::JOB_ACCOUNTING_ID => 41, InstitutePeer::JOB_WAREHOUSE_ID => 42, InstitutePeer::JOB_REGISTRY_ID => 43, ),
        BasePeer::TYPE_RAW_COLNAME => array ('INSTITUTE_ID' => 0, 'NAME' => 1, 'MECHAN_CODE' => 2, 'CONTACT_ID' => 3, 'FISCAL_CODE' => 4, 'SCHOOL_TYPE' => 5, 'PARENT' => 6, 'DEF' => 7, 'DIR_NAME' => 8, 'DIR_SURNAME' => 9, 'ADIR_NAME' => 10, 'ADIR_SURNAME' => 11, 'PRES_GE_NAME' => 12, 'PRES_GE_SURNAME' => 13, 'SEG_CONS_NAME' => 14, 'SEG_CONS_SURNAME' => 15, 'PRES_CON_NAME' => 16, 'PRES_CON_SURNAME' => 17, 'DIR_FISCAL_CODE' => 18, 'SCHOOL_FISCAL_CODE' => 19, 'INPDAP_CODE' => 20, 'ASSICURAZIONI_SANITARIE' => 21, 'DIR_SESSO' => 22, 'DIR_BIRTH' => 23, 'DIR_CITY' => 24, 'POSTAL_ACCOUNT' => 25, 'ATECO_CODE' => 26, 'ACTIVITY_CODE' => 27, 'DIR_CURR_ADDR' => 28, 'DIR_CURR_CITY' => 29, 'DIR_CURR_PHONE' => 30, 'DIR_EMP_ID' => 31, 'ADIR_EMP_ID' => 32, 'PRESGE_EMP_ID' => 33, 'SEGCONS_EMP_ID' => 34, 'PRESCON_EMP_ID' => 35, 'RESPACQ_EMP_ID' => 36, 'JOB_DIRECTOR_ID' => 37, 'JOB_VICE_DIRECTOR_ID' => 38, 'JOB_DSGA_ID' => 39, 'JOB_PERSONNEL_ID' => 40, 'JOB_ACCOUNTING_ID' => 41, 'JOB_WAREHOUSE_ID' => 42, 'JOB_REGISTRY_ID' => 43, ),
        BasePeer::TYPE_FIELDNAME => array ('institute_id' => 0, 'name' => 1, 'mechan_code' => 2, 'contact_id' => 3, 'fiscal_code' => 4, 'school_type' => 5, 'parent' => 6, 'def' => 7, 'dir_name' => 8, 'dir_surname' => 9, 'adir_name' => 10, 'adir_surname' => 11, 'pres_ge_name' => 12, 'pres_ge_surname' => 13, 'seg_cons_name' => 14, 'seg_cons_surname' => 15, 'pres_con_name' => 16, 'pres_con_surname' => 17, 'dir_fiscal_code' => 18, 'school_fiscal_code' => 19, 'inpdap_code' => 20, 'assicurazioni_sanitarie' => 21, 'dir_sesso' => 22, 'dir_birth' => 23, 'dir_city' => 24, 'postal_account' => 25, 'ateco_code' => 26, 'activity_code' => 27, 'dir_curr_addr' => 28, 'dir_curr_city' => 29, 'dir_curr_phone' => 30, 'dir_emp_id' => 31, 'adir_emp_id' => 32, 'presge_emp_id' => 33, 'segcons_emp_id' => 34, 'prescon_emp_id' => 35, 'respacq_emp_id' => 36, 'job_director_id' => 37, 'job_vice_director_id' => 38, 'job_dsga_id' => 39, 'job_personnel_id' => 40, 'job_accounting_id' => 41, 'job_warehouse_id' => 42, 'job_registry_id' => 43, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = InstitutePeer::getFieldNames($toType);
        $key = isset(InstitutePeer::$fieldKeys[$fromType][$name]) ? InstitutePeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(InstitutePeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, InstitutePeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return InstitutePeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. InstitutePeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(InstitutePeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(InstitutePeer::INSTITUTE_ID);
            $criteria->addSelectColumn(InstitutePeer::NAME);
            $criteria->addSelectColumn(InstitutePeer::MECHAN_CODE);
            $criteria->addSelectColumn(InstitutePeer::CONTACT_ID);
            $criteria->addSelectColumn(InstitutePeer::FISCAL_CODE);
            $criteria->addSelectColumn(InstitutePeer::SCHOOL_TYPE);
            $criteria->addSelectColumn(InstitutePeer::PARENT);
            $criteria->addSelectColumn(InstitutePeer::DEF);
            $criteria->addSelectColumn(InstitutePeer::DIR_NAME);
            $criteria->addSelectColumn(InstitutePeer::DIR_SURNAME);
            $criteria->addSelectColumn(InstitutePeer::ADIR_NAME);
            $criteria->addSelectColumn(InstitutePeer::ADIR_SURNAME);
            $criteria->addSelectColumn(InstitutePeer::PRES_GE_NAME);
            $criteria->addSelectColumn(InstitutePeer::PRES_GE_SURNAME);
            $criteria->addSelectColumn(InstitutePeer::SEG_CONS_NAME);
            $criteria->addSelectColumn(InstitutePeer::SEG_CONS_SURNAME);
            $criteria->addSelectColumn(InstitutePeer::PRES_CON_NAME);
            $criteria->addSelectColumn(InstitutePeer::PRES_CON_SURNAME);
            $criteria->addSelectColumn(InstitutePeer::DIR_FISCAL_CODE);
            $criteria->addSelectColumn(InstitutePeer::SCHOOL_FISCAL_CODE);
            $criteria->addSelectColumn(InstitutePeer::INPDAP_CODE);
            $criteria->addSelectColumn(InstitutePeer::ASSICURAZIONI_SANITARIE);
            $criteria->addSelectColumn(InstitutePeer::DIR_SESSO);
            $criteria->addSelectColumn(InstitutePeer::DIR_BIRTH);
            $criteria->addSelectColumn(InstitutePeer::DIR_CITY);
            $criteria->addSelectColumn(InstitutePeer::POSTAL_ACCOUNT);
            $criteria->addSelectColumn(InstitutePeer::ATECO_CODE);
            $criteria->addSelectColumn(InstitutePeer::ACTIVITY_CODE);
            $criteria->addSelectColumn(InstitutePeer::DIR_CURR_ADDR);
            $criteria->addSelectColumn(InstitutePeer::DIR_CURR_CITY);
            $criteria->addSelectColumn(InstitutePeer::DIR_CURR_PHONE);
            $criteria->addSelectColumn(InstitutePeer::DIR_EMP_ID);
            $criteria->addSelectColumn(InstitutePeer::ADIR_EMP_ID);
            $criteria->addSelectColumn(InstitutePeer::PRESGE_EMP_ID);
            $criteria->addSelectColumn(InstitutePeer::SEGCONS_EMP_ID);
            $criteria->addSelectColumn(InstitutePeer::PRESCON_EMP_ID);
            $criteria->addSelectColumn(InstitutePeer::RESPACQ_EMP_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_DIRECTOR_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_VICE_DIRECTOR_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_DSGA_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_PERSONNEL_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_ACCOUNTING_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_WAREHOUSE_ID);
            $criteria->addSelectColumn(InstitutePeer::JOB_REGISTRY_ID);
        } else {
            $criteria->addSelectColumn($alias . '.institute_id');
            $criteria->addSelectColumn($alias . '.name');
            $criteria->addSelectColumn($alias . '.mechan_code');
            $criteria->addSelectColumn($alias . '.contact_id');
            $criteria->addSelectColumn($alias . '.fiscal_code');
            $criteria->addSelectColumn($alias . '.school_type');
            $criteria->addSelectColumn($alias . '.parent');
            $criteria->addSelectColumn($alias . '.def');
            $criteria->addSelectColumn($alias . '.dir_name');
            $criteria->addSelectColumn($alias . '.dir_surname');
            $criteria->addSelectColumn($alias . '.adir_name');
            $criteria->addSelectColumn($alias . '.adir_surname');
            $criteria->addSelectColumn($alias . '.pres_ge_name');
            $criteria->addSelectColumn($alias . '.pres_ge_surname');
            $criteria->addSelectColumn($alias . '.seg_cons_name');
            $criteria->addSelectColumn($alias . '.seg_cons_surname');
            $criteria->addSelectColumn($alias . '.pres_con_name');
            $criteria->addSelectColumn($alias . '.pres_con_surname');
            $criteria->addSelectColumn($alias . '.dir_fiscal_code');
            $criteria->addSelectColumn($alias . '.school_fiscal_code');
            $criteria->addSelectColumn($alias . '.inpdap_code');
            $criteria->addSelectColumn($alias . '.assicurazioni_sanitarie');
            $criteria->addSelectColumn($alias . '.dir_sesso');
            $criteria->addSelectColumn($alias . '.dir_birth');
            $criteria->addSelectColumn($alias . '.dir_city');
            $criteria->addSelectColumn($alias . '.postal_account');
            $criteria->addSelectColumn($alias . '.ateco_code');
            $criteria->addSelectColumn($alias . '.activity_code');
            $criteria->addSelectColumn($alias . '.dir_curr_addr');
            $criteria->addSelectColumn($alias . '.dir_curr_city');
            $criteria->addSelectColumn($alias . '.dir_curr_phone');
            $criteria->addSelectColumn($alias . '.dir_emp_id');
            $criteria->addSelectColumn($alias . '.adir_emp_id');
            $criteria->addSelectColumn($alias . '.presge_emp_id');
            $criteria->addSelectColumn($alias . '.segcons_emp_id');
            $criteria->addSelectColumn($alias . '.prescon_emp_id');
            $criteria->addSelectColumn($alias . '.respacq_emp_id');
            $criteria->addSelectColumn($alias . '.job_director_id');
            $criteria->addSelectColumn($alias . '.job_vice_director_id');
            $criteria->addSelectColumn($alias . '.job_dsga_id');
            $criteria->addSelectColumn($alias . '.job_personnel_id');
            $criteria->addSelectColumn($alias . '.job_accounting_id');
            $criteria->addSelectColumn($alias . '.job_warehouse_id');
            $criteria->addSelectColumn($alias . '.job_registry_id');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            InstitutePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(InstitutePeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return Institute
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = InstitutePeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return InstitutePeer::populateObjects(InstitutePeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            InstitutePeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param Institute $obj A Institute object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = serialize(array((string) $obj->getInstituteId(), (string) $obj->getContactId()));
            } // if key === null
            InstitutePeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A Institute object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof Institute) {
                $key = serialize(array((string) $value->getInstituteId(), (string) $value->getContactId()));
            } elseif (is_array($value) && count($value) === 2) {
                // assume we've been passed a primary key
                $key = serialize(array((string) $value[0], (string) $value[1]));
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or Institute object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(InstitutePeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return Institute Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(InstitutePeer::$instances[$key])) {
                return InstitutePeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (InstitutePeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        InstitutePeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to institute
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
        // Invalidate objects in InstituteBankProfilePeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        InstituteBankProfilePeer::clearInstancePool();
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null && $row[$startcol + 3] === null) {
            return null;
        }

        return serialize(array((string) $row[$startcol], (string) $row[$startcol + 3]));
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return array((int) $row[$startcol], (int) $row[$startcol + 3]);
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = InstitutePeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = InstitutePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = InstitutePeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                InstitutePeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (Institute object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = InstitutePeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = InstitutePeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + InstitutePeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = InstitutePeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            InstitutePeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }


    /**
     * Returns the number of rows matching criteria, joining the related Contact table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinContact(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            InstitutePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(InstitutePeer::CONTACT_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related Employee table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinEmployee(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            InstitutePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(InstitutePeer::DIR_EMP_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Institute objects pre-filled with their Contact objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Institute objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinContact(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(InstitutePeer::DATABASE_NAME);
        }

        InstitutePeer::addSelectColumns($criteria);
        $startcol = InstitutePeer::NUM_HYDRATE_COLUMNS;
        ContactPeer::addSelectColumns($criteria);

        $criteria->addJoin(InstitutePeer::CONTACT_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = InstitutePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = InstitutePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = InstitutePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                InstitutePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = ContactPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = ContactPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    ContactPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Institute) to $obj2 (Contact)
                $obj2->addInstitute($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Institute objects pre-filled with their Employee objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Institute objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinEmployee(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(InstitutePeer::DATABASE_NAME);
        }

        InstitutePeer::addSelectColumns($criteria);
        $startcol = InstitutePeer::NUM_HYDRATE_COLUMNS;
        EmployeePeer::addSelectColumns($criteria);

        $criteria->addJoin(InstitutePeer::DIR_EMP_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = InstitutePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = InstitutePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = InstitutePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                InstitutePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = EmployeePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Institute) to $obj2 (Employee)
                $obj2->addInstitute($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining all related tables
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAll(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            InstitutePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(InstitutePeer::CONTACT_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $criteria->addJoin(InstitutePeer::DIR_EMP_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }

    /**
     * Selects a collection of Institute objects pre-filled with all related objects.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Institute objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAll(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(InstitutePeer::DATABASE_NAME);
        }

        InstitutePeer::addSelectColumns($criteria);
        $startcol2 = InstitutePeer::NUM_HYDRATE_COLUMNS;

        ContactPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + ContactPeer::NUM_HYDRATE_COLUMNS;

        EmployeePeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + EmployeePeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(InstitutePeer::CONTACT_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $criteria->addJoin(InstitutePeer::DIR_EMP_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = InstitutePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = InstitutePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = InstitutePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                InstitutePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            // Add objects for joined Contact rows

            $key2 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol2);
            if ($key2 !== null) {
                $obj2 = ContactPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = ContactPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    ContactPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 loaded

                // Add the $obj1 (Institute) to the collection in $obj2 (Contact)
                $obj2->addInstitute($obj1);
            } // if joined row not null

            // Add objects for joined Employee rows

            $key3 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol3);
            if ($key3 !== null) {
                $obj3 = EmployeePeer::getInstanceFromPool($key3);
                if (!$obj3) {

                    $cls = EmployeePeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    EmployeePeer::addInstanceToPool($obj3, $key3);
                } // if obj3 loaded

                // Add the $obj1 (Institute) to the collection in $obj3 (Employee)
                $obj3->addInstitute($obj1);
            } // if joined row not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining the related Contact table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptContact(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            InstitutePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(InstitutePeer::DIR_EMP_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related Employee table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptEmployee(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            InstitutePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(InstitutePeer::CONTACT_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Institute objects pre-filled with all related objects except Contact.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Institute objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptContact(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(InstitutePeer::DATABASE_NAME);
        }

        InstitutePeer::addSelectColumns($criteria);
        $startcol2 = InstitutePeer::NUM_HYDRATE_COLUMNS;

        EmployeePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + EmployeePeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(InstitutePeer::DIR_EMP_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = InstitutePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = InstitutePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = InstitutePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                InstitutePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined Employee rows

                $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = EmployeePeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Institute) to the collection in $obj2 (Employee)
                $obj2->addInstitute($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Institute objects pre-filled with all related objects except Employee.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Institute objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptEmployee(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(InstitutePeer::DATABASE_NAME);
        }

        InstitutePeer::addSelectColumns($criteria);
        $startcol2 = InstitutePeer::NUM_HYDRATE_COLUMNS;

        ContactPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + ContactPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(InstitutePeer::CONTACT_ID, ContactPeer::CONTACT_ID, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = InstitutePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = InstitutePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = InstitutePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                InstitutePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined Contact rows

                $key2 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = ContactPeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = ContactPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    ContactPeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Institute) to the collection in $obj2 (Contact)
                $obj2->addInstitute($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(InstitutePeer::DATABASE_NAME)->getTable(InstitutePeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseInstitutePeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseInstitutePeer::TABLE_NAME)) {
        $dbMap->addTableObject(new InstituteTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return InstitutePeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a Institute or Criteria object.
     *
     * @param      mixed $values Criteria or Institute object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from Institute object
        }

        if ($criteria->containsKey(InstitutePeer::INSTITUTE_ID) && $criteria->keyContainsValue(InstitutePeer::INSTITUTE_ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.InstitutePeer::INSTITUTE_ID.')');
        }


        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a Institute or Criteria object.
     *
     * @param      mixed $values Criteria or Institute object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(InstitutePeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(InstitutePeer::INSTITUTE_ID);
            $value = $criteria->remove(InstitutePeer::INSTITUTE_ID);
            if ($value) {
                $selectCriteria->add(InstitutePeer::INSTITUTE_ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);
            }

            $comparison = $criteria->getComparison(InstitutePeer::CONTACT_ID);
            $value = $criteria->remove(InstitutePeer::CONTACT_ID);
            if ($value) {
                $selectCriteria->add(InstitutePeer::CONTACT_ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(InstitutePeer::TABLE_NAME);
            }

        } else { // $values is Institute object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the institute table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(InstitutePeer::TABLE_NAME, $con, InstitutePeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            InstitutePeer::clearInstancePool();
            InstitutePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a Institute or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or Institute object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            InstitutePeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof Institute) { // it's a model object
            // invalidate the cache for this single object
            InstitutePeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(InstitutePeer::DATABASE_NAME);
            // primary key is composite; we therefore, expect
            // the primary key passed to be an array of pkey values
            if (count($values) == count($values, COUNT_RECURSIVE)) {
                // array is not multi-dimensional
                $values = array($values);
            }
            foreach ($values as $value) {
                $criterion = $criteria->getNewCriterion(InstitutePeer::INSTITUTE_ID, $value[0]);
                $criterion->addAnd($criteria->getNewCriterion(InstitutePeer::CONTACT_ID, $value[1]));
                $criteria->addOr($criterion);
                // we can invalidate the cache for this single PK
                InstitutePeer::removeInstanceFromPool($value);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(InstitutePeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            InstitutePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given Institute object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param Institute $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(InstitutePeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(InstitutePeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(InstitutePeer::DATABASE_NAME, InstitutePeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve object using using composite pkey values.
     * @param   int $institute_id
     * @param   int $contact_id
     * @param      PropelPDO $con
     * @return Institute
     */
    public static function retrieveByPK($institute_id, $contact_id, PropelPDO $con = null) {
        $_instancePoolKey = serialize(array((string) $institute_id, (string) $contact_id));
         if (null !== ($obj = InstitutePeer::getInstanceFromPool($_instancePoolKey))) {
             return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $criteria = new Criteria(InstitutePeer::DATABASE_NAME);
        $criteria->add(InstitutePeer::INSTITUTE_ID, $institute_id);
        $criteria->add(InstitutePeer::CONTACT_ID, $contact_id);
        $v = InstitutePeer::doSelect($criteria, $con);

        return !empty($v) ? $v[0] : null;
    }
} // BaseInstitutePeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseInstitutePeer::buildTableMap();

