<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\BankAccount;
use Core\Institute;
use Core\InstituteBankAccount;
use Core\InstituteBankAccountPeer;
use Core\InstituteBankAccountQuery;

/**
 * Base class that represents a query for the 'institute_bank_account' table.
 *
 *
 *
 * @method InstituteBankAccountQuery orderById($order = Criteria::ASC) Order by the id column
 * @method InstituteBankAccountQuery orderByInstitute($order = Criteria::ASC) Order by the institute column
 * @method InstituteBankAccountQuery orderByBankAccount($order = Criteria::ASC) Order by the bank_account column
 *
 * @method InstituteBankAccountQuery groupById() Group by the id column
 * @method InstituteBankAccountQuery groupByInstitute() Group by the institute column
 * @method InstituteBankAccountQuery groupByBankAccount() Group by the bank_account column
 *
 * @method InstituteBankAccountQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method InstituteBankAccountQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method InstituteBankAccountQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method InstituteBankAccountQuery leftJoinInstituteBankAccountBankAccountKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the InstituteBankAccountBankAccountKey relation
 * @method InstituteBankAccountQuery rightJoinInstituteBankAccountBankAccountKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the InstituteBankAccountBankAccountKey relation
 * @method InstituteBankAccountQuery innerJoinInstituteBankAccountBankAccountKey($relationAlias = null) Adds a INNER JOIN clause to the query using the InstituteBankAccountBankAccountKey relation
 *
 * @method InstituteBankAccountQuery leftJoinInstituteBankAccountKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the InstituteBankAccountKey relation
 * @method InstituteBankAccountQuery rightJoinInstituteBankAccountKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the InstituteBankAccountKey relation
 * @method InstituteBankAccountQuery innerJoinInstituteBankAccountKey($relationAlias = null) Adds a INNER JOIN clause to the query using the InstituteBankAccountKey relation
 *
 * @method InstituteBankAccount findOne(PropelPDO $con = null) Return the first InstituteBankAccount matching the query
 * @method InstituteBankAccount findOneOrCreate(PropelPDO $con = null) Return the first InstituteBankAccount matching the query, or a new InstituteBankAccount object populated from the query conditions when no match is found
 *
 * @method InstituteBankAccount findOneByInstitute(int $institute) Return the first InstituteBankAccount filtered by the institute column
 * @method InstituteBankAccount findOneByBankAccount(int $bank_account) Return the first InstituteBankAccount filtered by the bank_account column
 *
 * @method array findById(int $id) Return InstituteBankAccount objects filtered by the id column
 * @method array findByInstitute(int $institute) Return InstituteBankAccount objects filtered by the institute column
 * @method array findByBankAccount(int $bank_account) Return InstituteBankAccount objects filtered by the bank_account column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseInstituteBankAccountQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseInstituteBankAccountQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\InstituteBankAccount';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new InstituteBankAccountQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   InstituteBankAccountQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return InstituteBankAccountQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof InstituteBankAccountQuery) {
            return $criteria;
        }
        $query = new InstituteBankAccountQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   InstituteBankAccount|InstituteBankAccount[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = InstituteBankAccountPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(InstituteBankAccountPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 InstituteBankAccount A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 InstituteBankAccount A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "institute", "bank_account" FROM "institute_bank_account" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new InstituteBankAccount();
            $obj->hydrate($row);
            InstituteBankAccountPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return InstituteBankAccount|InstituteBankAccount[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|InstituteBankAccount[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(InstituteBankAccountPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(InstituteBankAccountPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(InstituteBankAccountPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(InstituteBankAccountPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstituteBankAccountPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the institute column
     *
     * Example usage:
     * <code>
     * $query->filterByInstitute(1234); // WHERE institute = 1234
     * $query->filterByInstitute(array(12, 34)); // WHERE institute IN (12, 34)
     * $query->filterByInstitute(array('min' => 12)); // WHERE institute >= 12
     * $query->filterByInstitute(array('max' => 12)); // WHERE institute <= 12
     * </code>
     *
     * @see       filterByInstituteBankAccountKey()
     *
     * @param     mixed $institute The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function filterByInstitute($institute = null, $comparison = null)
    {
        if (is_array($institute)) {
            $useMinMax = false;
            if (isset($institute['min'])) {
                $this->addUsingAlias(InstituteBankAccountPeer::INSTITUTE, $institute['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($institute['max'])) {
                $this->addUsingAlias(InstituteBankAccountPeer::INSTITUTE, $institute['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstituteBankAccountPeer::INSTITUTE, $institute, $comparison);
    }

    /**
     * Filter the query on the bank_account column
     *
     * Example usage:
     * <code>
     * $query->filterByBankAccount(1234); // WHERE bank_account = 1234
     * $query->filterByBankAccount(array(12, 34)); // WHERE bank_account IN (12, 34)
     * $query->filterByBankAccount(array('min' => 12)); // WHERE bank_account >= 12
     * $query->filterByBankAccount(array('max' => 12)); // WHERE bank_account <= 12
     * </code>
     *
     * @see       filterByInstituteBankAccountBankAccountKey()
     *
     * @param     mixed $bankAccount The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function filterByBankAccount($bankAccount = null, $comparison = null)
    {
        if (is_array($bankAccount)) {
            $useMinMax = false;
            if (isset($bankAccount['min'])) {
                $this->addUsingAlias(InstituteBankAccountPeer::BANK_ACCOUNT, $bankAccount['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($bankAccount['max'])) {
                $this->addUsingAlias(InstituteBankAccountPeer::BANK_ACCOUNT, $bankAccount['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstituteBankAccountPeer::BANK_ACCOUNT, $bankAccount, $comparison);
    }

    /**
     * Filter the query by a related BankAccount object
     *
     * @param   BankAccount|PropelObjectCollection $bankAccount The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteBankAccountQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstituteBankAccountBankAccountKey($bankAccount, $comparison = null)
    {
        if ($bankAccount instanceof BankAccount) {
            return $this
                ->addUsingAlias(InstituteBankAccountPeer::BANK_ACCOUNT, $bankAccount->getId(), $comparison);
        } elseif ($bankAccount instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(InstituteBankAccountPeer::BANK_ACCOUNT, $bankAccount->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByInstituteBankAccountBankAccountKey() only accepts arguments of type BankAccount or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the InstituteBankAccountBankAccountKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function joinInstituteBankAccountBankAccountKey($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('InstituteBankAccountBankAccountKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'InstituteBankAccountBankAccountKey');
        }

        return $this;
    }

    /**
     * Use the InstituteBankAccountBankAccountKey relation BankAccount object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\BankAccountQuery A secondary query class using the current class as primary query
     */
    public function useInstituteBankAccountBankAccountKeyQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstituteBankAccountBankAccountKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'InstituteBankAccountBankAccountKey', '\Core\BankAccountQuery');
    }

    /**
     * Filter the query by a related Institute object
     *
     * @param   Institute|PropelObjectCollection $institute The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteBankAccountQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstituteBankAccountKey($institute, $comparison = null)
    {
        if ($institute instanceof Institute) {
            return $this
                ->addUsingAlias(InstituteBankAccountPeer::INSTITUTE, $institute->getInstituteId(), $comparison);
        } elseif ($institute instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(InstituteBankAccountPeer::INSTITUTE, $institute->toKeyValue('InstituteId', 'InstituteId'), $comparison);
        } else {
            throw new PropelException('filterByInstituteBankAccountKey() only accepts arguments of type Institute or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the InstituteBankAccountKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function joinInstituteBankAccountKey($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('InstituteBankAccountKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'InstituteBankAccountKey');
        }

        return $this;
    }

    /**
     * Use the InstituteBankAccountKey relation Institute object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\InstituteQuery A secondary query class using the current class as primary query
     */
    public function useInstituteBankAccountKeyQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstituteBankAccountKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'InstituteBankAccountKey', '\Core\InstituteQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   InstituteBankAccount $instituteBankAccount Object to remove from the list of results
     *
     * @return InstituteBankAccountQuery The current query, for fluid interface
     */
    public function prune($instituteBankAccount = null)
    {
        if ($instituteBankAccount) {
            $this->addUsingAlias(InstituteBankAccountPeer::ID, $instituteBankAccount->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
