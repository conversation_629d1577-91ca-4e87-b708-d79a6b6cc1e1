<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Cities;
use Core\CitiesPeer;
use Core\CitiesQuery;
use Core\Contact;
use Core\Regions;

/**
 * Base class that represents a query for the 'cities' table.
 *
 *
 *
 * @method CitiesQuery orderByCityId($order = Criteria::ASC) Order by the city_id column
 * @method CitiesQuery orderByDescription($order = Criteria::ASC) Order by the description column
 * @method CitiesQuery orderByCityCode($order = Criteria::ASC) Order by the city_code column
 * @method CitiesQuery orderByProvince($order = Criteria::ASC) Order by the province column
 * @method CitiesQuery orderByRegion($order = Criteria::ASC) Order by the region column
 * @method CitiesQuery orderByIsCity($order = Criteria::ASC) Order by the is_city column
 *
 * @method CitiesQuery groupByCityId() Group by the city_id column
 * @method CitiesQuery groupByDescription() Group by the description column
 * @method CitiesQuery groupByCityCode() Group by the city_code column
 * @method CitiesQuery groupByProvince() Group by the province column
 * @method CitiesQuery groupByRegion() Group by the region column
 * @method CitiesQuery groupByIsCity() Group by the is_city column
 *
 * @method CitiesQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method CitiesQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method CitiesQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method CitiesQuery leftJoinRegionKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the RegionKey relation
 * @method CitiesQuery rightJoinRegionKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the RegionKey relation
 * @method CitiesQuery innerJoinRegionKey($relationAlias = null) Adds a INNER JOIN clause to the query using the RegionKey relation
 *
 * @method CitiesQuery leftJoinContact($relationAlias = null) Adds a LEFT JOIN clause to the query using the Contact relation
 * @method CitiesQuery rightJoinContact($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Contact relation
 * @method CitiesQuery innerJoinContact($relationAlias = null) Adds a INNER JOIN clause to the query using the Contact relation
 *
 * @method Cities findOne(PropelPDO $con = null) Return the first Cities matching the query
 * @method Cities findOneOrCreate(PropelPDO $con = null) Return the first Cities matching the query, or a new Cities object populated from the query conditions when no match is found
 *
 * @method Cities findOneByDescription(string $description) Return the first Cities filtered by the description column
 * @method Cities findOneByCityCode(string $city_code) Return the first Cities filtered by the city_code column
 * @method Cities findOneByProvince(string $province) Return the first Cities filtered by the province column
 * @method Cities findOneByRegion(string $region) Return the first Cities filtered by the region column
 * @method Cities findOneByIsCity(int $is_city) Return the first Cities filtered by the is_city column
 *
 * @method array findByCityId(int $city_id) Return Cities objects filtered by the city_id column
 * @method array findByDescription(string $description) Return Cities objects filtered by the description column
 * @method array findByCityCode(string $city_code) Return Cities objects filtered by the city_code column
 * @method array findByProvince(string $province) Return Cities objects filtered by the province column
 * @method array findByRegion(string $region) Return Cities objects filtered by the region column
 * @method array findByIsCity(int $is_city) Return Cities objects filtered by the is_city column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseCitiesQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseCitiesQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\Cities';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new CitiesQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   CitiesQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return CitiesQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof CitiesQuery) {
            return $criteria;
        }
        $query = new CitiesQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Cities|Cities[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = CitiesPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(CitiesPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Cities A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByCityId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Cities A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "city_id", "description", "city_code", "province", "region", "is_city" FROM "cities" WHERE "city_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Cities();
            $obj->hydrate($row);
            CitiesPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Cities|Cities[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Cities[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(CitiesPeer::CITY_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(CitiesPeer::CITY_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the city_id column
     *
     * Example usage:
     * <code>
     * $query->filterByCityId(1234); // WHERE city_id = 1234
     * $query->filterByCityId(array(12, 34)); // WHERE city_id IN (12, 34)
     * $query->filterByCityId(array('min' => 12)); // WHERE city_id >= 12
     * $query->filterByCityId(array('max' => 12)); // WHERE city_id <= 12
     * </code>
     *
     * @param     mixed $cityId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByCityId($cityId = null, $comparison = null)
    {
        if (is_array($cityId)) {
            $useMinMax = false;
            if (isset($cityId['min'])) {
                $this->addUsingAlias(CitiesPeer::CITY_ID, $cityId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($cityId['max'])) {
                $this->addUsingAlias(CitiesPeer::CITY_ID, $cityId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(CitiesPeer::CITY_ID, $cityId, $comparison);
    }

    /**
     * Filter the query on the description column
     *
     * Example usage:
     * <code>
     * $query->filterByDescription('fooValue');   // WHERE description = 'fooValue'
     * $query->filterByDescription('%fooValue%'); // WHERE description LIKE '%fooValue%'
     * </code>
     *
     * @param     string $description The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByDescription($description = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($description)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $description)) {
                $description = str_replace('*', '%', $description);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(CitiesPeer::DESCRIPTION, $description, $comparison);
    }

    /**
     * Filter the query on the city_code column
     *
     * Example usage:
     * <code>
     * $query->filterByCityCode('fooValue');   // WHERE city_code = 'fooValue'
     * $query->filterByCityCode('%fooValue%'); // WHERE city_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cityCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByCityCode($cityCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cityCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cityCode)) {
                $cityCode = str_replace('*', '%', $cityCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(CitiesPeer::CITY_CODE, $cityCode, $comparison);
    }

    /**
     * Filter the query on the province column
     *
     * Example usage:
     * <code>
     * $query->filterByProvince('fooValue');   // WHERE province = 'fooValue'
     * $query->filterByProvince('%fooValue%'); // WHERE province LIKE '%fooValue%'
     * </code>
     *
     * @param     string $province The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByProvince($province = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($province)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $province)) {
                $province = str_replace('*', '%', $province);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(CitiesPeer::PROVINCE, $province, $comparison);
    }

    /**
     * Filter the query on the region column
     *
     * Example usage:
     * <code>
     * $query->filterByRegion('fooValue');   // WHERE region = 'fooValue'
     * $query->filterByRegion('%fooValue%'); // WHERE region LIKE '%fooValue%'
     * </code>
     *
     * @param     string $region The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByRegion($region = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($region)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $region)) {
                $region = str_replace('*', '%', $region);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(CitiesPeer::REGION, $region, $comparison);
    }

    /**
     * Filter the query on the is_city column
     *
     * Example usage:
     * <code>
     * $query->filterByIsCity(1234); // WHERE is_city = 1234
     * $query->filterByIsCity(array(12, 34)); // WHERE is_city IN (12, 34)
     * $query->filterByIsCity(array('min' => 12)); // WHERE is_city >= 12
     * $query->filterByIsCity(array('max' => 12)); // WHERE is_city <= 12
     * </code>
     *
     * @param     mixed $isCity The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function filterByIsCity($isCity = null, $comparison = null)
    {
        if (is_array($isCity)) {
            $useMinMax = false;
            if (isset($isCity['min'])) {
                $this->addUsingAlias(CitiesPeer::IS_CITY, $isCity['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($isCity['max'])) {
                $this->addUsingAlias(CitiesPeer::IS_CITY, $isCity['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(CitiesPeer::IS_CITY, $isCity, $comparison);
    }

    /**
     * Filter the query by a related Regions object
     *
     * @param   Regions|PropelObjectCollection $regions The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 CitiesQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByRegionKey($regions, $comparison = null)
    {
        if ($regions instanceof Regions) {
            return $this
                ->addUsingAlias(CitiesPeer::REGION, $regions->getCode(), $comparison);
        } elseif ($regions instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(CitiesPeer::REGION, $regions->toKeyValue('PrimaryKey', 'Code'), $comparison);
        } else {
            throw new PropelException('filterByRegionKey() only accepts arguments of type Regions or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the RegionKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function joinRegionKey($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('RegionKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'RegionKey');
        }

        return $this;
    }

    /**
     * Use the RegionKey relation Regions object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\RegionsQuery A secondary query class using the current class as primary query
     */
    public function useRegionKeyQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinRegionKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'RegionKey', '\Core\RegionsQuery');
    }

    /**
     * Filter the query by a related Contact object
     *
     * @param   Contact|PropelObjectCollection $contact  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 CitiesQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByContact($contact, $comparison = null)
    {
        if ($contact instanceof Contact) {
            return $this
                ->addUsingAlias(CitiesPeer::CITY_ID, $contact->getCityId(), $comparison);
        } elseif ($contact instanceof PropelObjectCollection) {
            return $this
                ->useContactQuery()
                ->filterByPrimaryKeys($contact->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByContact() only accepts arguments of type Contact or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Contact relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function joinContact($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Contact');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Contact');
        }

        return $this;
    }

    /**
     * Use the Contact relation Contact object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\ContactQuery A secondary query class using the current class as primary query
     */
    public function useContactQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinContact($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Contact', '\Core\ContactQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Cities $cities Object to remove from the list of results
     *
     * @return CitiesQuery The current query, for fluid interface
     */
    public function prune($cities = null)
    {
        if ($cities) {
            $this->addUsingAlias(CitiesPeer::CITY_ID, $cities->getCityId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
