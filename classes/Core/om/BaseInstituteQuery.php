<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Contact;
use Core\Institute;
use Core\InstituteBankProfile;
use Core\InstitutePeer;
use Core\InstituteQuery;
use Employee\Employee;

/**
 * Base class that represents a query for the 'institute' table.
 *
 *
 *
 * @method InstituteQuery orderByInstituteId($order = Criteria::ASC) Order by the institute_id column
 * @method InstituteQuery orderByName($order = Criteria::ASC) Order by the name column
 * @method InstituteQuery orderByMechanCode($order = Criteria::ASC) Order by the mechan_code column
 * @method InstituteQuery orderByContactId($order = Criteria::ASC) Order by the contact_id column
 * @method InstituteQuery orderByFiscalCode($order = Criteria::ASC) Order by the fiscal_code column
 * @method InstituteQuery orderBySchoolType($order = Criteria::ASC) Order by the school_type column
 * @method InstituteQuery orderByParent($order = Criteria::ASC) Order by the parent column
 * @method InstituteQuery orderByDef($order = Criteria::ASC) Order by the def column
 * @method InstituteQuery orderByDirName($order = Criteria::ASC) Order by the dir_name column
 * @method InstituteQuery orderByDirSurname($order = Criteria::ASC) Order by the dir_surname column
 * @method InstituteQuery orderByAdirName($order = Criteria::ASC) Order by the adir_name column
 * @method InstituteQuery orderByAdirSurname($order = Criteria::ASC) Order by the adir_surname column
 * @method InstituteQuery orderByPresGeName($order = Criteria::ASC) Order by the pres_ge_name column
 * @method InstituteQuery orderByPresGeSurname($order = Criteria::ASC) Order by the pres_ge_surname column
 * @method InstituteQuery orderBySegConsName($order = Criteria::ASC) Order by the seg_cons_name column
 * @method InstituteQuery orderBySegConsSurname($order = Criteria::ASC) Order by the seg_cons_surname column
 * @method InstituteQuery orderByPresConName($order = Criteria::ASC) Order by the pres_con_name column
 * @method InstituteQuery orderByPresConSurname($order = Criteria::ASC) Order by the pres_con_surname column
 * @method InstituteQuery orderByDirFiscalCode($order = Criteria::ASC) Order by the dir_fiscal_code column
 * @method InstituteQuery orderBySchoolFiscalCode($order = Criteria::ASC) Order by the school_fiscal_code column
 * @method InstituteQuery orderByInpdapCode($order = Criteria::ASC) Order by the inpdap_code column
 * @method InstituteQuery orderByAssicurazioniSanitarie($order = Criteria::ASC) Order by the assicurazioni_sanitarie column
 * @method InstituteQuery orderByDirSesso($order = Criteria::ASC) Order by the dir_sesso column
 * @method InstituteQuery orderByDirBirth($order = Criteria::ASC) Order by the dir_birth column
 * @method InstituteQuery orderByDirCity($order = Criteria::ASC) Order by the dir_city column
 * @method InstituteQuery orderByPostalAccount($order = Criteria::ASC) Order by the postal_account column
 * @method InstituteQuery orderByAtecoCode($order = Criteria::ASC) Order by the ateco_code column
 * @method InstituteQuery orderByActivityCode($order = Criteria::ASC) Order by the activity_code column
 * @method InstituteQuery orderByDirCurrAddr($order = Criteria::ASC) Order by the dir_curr_addr column
 * @method InstituteQuery orderByDirCurrCity($order = Criteria::ASC) Order by the dir_curr_city column
 * @method InstituteQuery orderByDirCurrPhone($order = Criteria::ASC) Order by the dir_curr_phone column
 * @method InstituteQuery orderByDirEmpId($order = Criteria::ASC) Order by the dir_emp_id column
 * @method InstituteQuery orderByAdirEmpId($order = Criteria::ASC) Order by the adir_emp_id column
 * @method InstituteQuery orderByPresgeEmpId($order = Criteria::ASC) Order by the presge_emp_id column
 * @method InstituteQuery orderBySegconsEmpId($order = Criteria::ASC) Order by the segcons_emp_id column
 * @method InstituteQuery orderByPresconEmpId($order = Criteria::ASC) Order by the prescon_emp_id column
 * @method InstituteQuery orderByRespacqEmpId($order = Criteria::ASC) Order by the respacq_emp_id column
 * @method InstituteQuery orderByJobDirectorId($order = Criteria::ASC) Order by the job_director_id column
 * @method InstituteQuery orderByJobViceDirectorId($order = Criteria::ASC) Order by the job_vice_director_id column
 * @method InstituteQuery orderByJobDSGAId($order = Criteria::ASC) Order by the job_dsga_id column
 * @method InstituteQuery orderByJobPersonnelId($order = Criteria::ASC) Order by the job_personnel_id column
 * @method InstituteQuery orderByJobAccountingId($order = Criteria::ASC) Order by the job_accounting_id column
 * @method InstituteQuery orderByJobWarehouseId($order = Criteria::ASC) Order by the job_warehouse_id column
 * @method InstituteQuery orderByJobRegistryId($order = Criteria::ASC) Order by the job_registry_id column
 *
 * @method InstituteQuery groupByInstituteId() Group by the institute_id column
 * @method InstituteQuery groupByName() Group by the name column
 * @method InstituteQuery groupByMechanCode() Group by the mechan_code column
 * @method InstituteQuery groupByContactId() Group by the contact_id column
 * @method InstituteQuery groupByFiscalCode() Group by the fiscal_code column
 * @method InstituteQuery groupBySchoolType() Group by the school_type column
 * @method InstituteQuery groupByParent() Group by the parent column
 * @method InstituteQuery groupByDef() Group by the def column
 * @method InstituteQuery groupByDirName() Group by the dir_name column
 * @method InstituteQuery groupByDirSurname() Group by the dir_surname column
 * @method InstituteQuery groupByAdirName() Group by the adir_name column
 * @method InstituteQuery groupByAdirSurname() Group by the adir_surname column
 * @method InstituteQuery groupByPresGeName() Group by the pres_ge_name column
 * @method InstituteQuery groupByPresGeSurname() Group by the pres_ge_surname column
 * @method InstituteQuery groupBySegConsName() Group by the seg_cons_name column
 * @method InstituteQuery groupBySegConsSurname() Group by the seg_cons_surname column
 * @method InstituteQuery groupByPresConName() Group by the pres_con_name column
 * @method InstituteQuery groupByPresConSurname() Group by the pres_con_surname column
 * @method InstituteQuery groupByDirFiscalCode() Group by the dir_fiscal_code column
 * @method InstituteQuery groupBySchoolFiscalCode() Group by the school_fiscal_code column
 * @method InstituteQuery groupByInpdapCode() Group by the inpdap_code column
 * @method InstituteQuery groupByAssicurazioniSanitarie() Group by the assicurazioni_sanitarie column
 * @method InstituteQuery groupByDirSesso() Group by the dir_sesso column
 * @method InstituteQuery groupByDirBirth() Group by the dir_birth column
 * @method InstituteQuery groupByDirCity() Group by the dir_city column
 * @method InstituteQuery groupByPostalAccount() Group by the postal_account column
 * @method InstituteQuery groupByAtecoCode() Group by the ateco_code column
 * @method InstituteQuery groupByActivityCode() Group by the activity_code column
 * @method InstituteQuery groupByDirCurrAddr() Group by the dir_curr_addr column
 * @method InstituteQuery groupByDirCurrCity() Group by the dir_curr_city column
 * @method InstituteQuery groupByDirCurrPhone() Group by the dir_curr_phone column
 * @method InstituteQuery groupByDirEmpId() Group by the dir_emp_id column
 * @method InstituteQuery groupByAdirEmpId() Group by the adir_emp_id column
 * @method InstituteQuery groupByPresgeEmpId() Group by the presge_emp_id column
 * @method InstituteQuery groupBySegconsEmpId() Group by the segcons_emp_id column
 * @method InstituteQuery groupByPresconEmpId() Group by the prescon_emp_id column
 * @method InstituteQuery groupByRespacqEmpId() Group by the respacq_emp_id column
 * @method InstituteQuery groupByJobDirectorId() Group by the job_director_id column
 * @method InstituteQuery groupByJobViceDirectorId() Group by the job_vice_director_id column
 * @method InstituteQuery groupByJobDSGAId() Group by the job_dsga_id column
 * @method InstituteQuery groupByJobPersonnelId() Group by the job_personnel_id column
 * @method InstituteQuery groupByJobAccountingId() Group by the job_accounting_id column
 * @method InstituteQuery groupByJobWarehouseId() Group by the job_warehouse_id column
 * @method InstituteQuery groupByJobRegistryId() Group by the job_registry_id column
 *
 * @method InstituteQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method InstituteQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method InstituteQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method InstituteQuery leftJoinContact($relationAlias = null) Adds a LEFT JOIN clause to the query using the Contact relation
 * @method InstituteQuery rightJoinContact($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Contact relation
 * @method InstituteQuery innerJoinContact($relationAlias = null) Adds a INNER JOIN clause to the query using the Contact relation
 *
 * @method InstituteQuery leftJoinEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the Employee relation
 * @method InstituteQuery rightJoinEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Employee relation
 * @method InstituteQuery innerJoinEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the Employee relation
 *
 * @method InstituteQuery leftJoinInstituteBankProfile($relationAlias = null) Adds a LEFT JOIN clause to the query using the InstituteBankProfile relation
 * @method InstituteQuery rightJoinInstituteBankProfile($relationAlias = null) Adds a RIGHT JOIN clause to the query using the InstituteBankProfile relation
 * @method InstituteQuery innerJoinInstituteBankProfile($relationAlias = null) Adds a INNER JOIN clause to the query using the InstituteBankProfile relation
 *
 * @method Institute findOne(PropelPDO $con = null) Return the first Institute matching the query
 * @method Institute findOneOrCreate(PropelPDO $con = null) Return the first Institute matching the query, or a new Institute object populated from the query conditions when no match is found
 *
 * @method Institute findOneByInstituteId(int $institute_id) Return the first Institute filtered by the institute_id column
 * @method Institute findOneByName(string $name) Return the first Institute filtered by the name column
 * @method Institute findOneByMechanCode(string $mechan_code) Return the first Institute filtered by the mechan_code column
 * @method Institute findOneByContactId(int $contact_id) Return the first Institute filtered by the contact_id column
 * @method Institute findOneByFiscalCode(string $fiscal_code) Return the first Institute filtered by the fiscal_code column
 * @method Institute findOneBySchoolType(string $school_type) Return the first Institute filtered by the school_type column
 * @method Institute findOneByParent(int $parent) Return the first Institute filtered by the parent column
 * @method Institute findOneByDef(boolean $def) Return the first Institute filtered by the def column
 * @method Institute findOneByDirName(string $dir_name) Return the first Institute filtered by the dir_name column
 * @method Institute findOneByDirSurname(string $dir_surname) Return the first Institute filtered by the dir_surname column
 * @method Institute findOneByAdirName(string $adir_name) Return the first Institute filtered by the adir_name column
 * @method Institute findOneByAdirSurname(string $adir_surname) Return the first Institute filtered by the adir_surname column
 * @method Institute findOneByPresGeName(string $pres_ge_name) Return the first Institute filtered by the pres_ge_name column
 * @method Institute findOneByPresGeSurname(string $pres_ge_surname) Return the first Institute filtered by the pres_ge_surname column
 * @method Institute findOneBySegConsName(string $seg_cons_name) Return the first Institute filtered by the seg_cons_name column
 * @method Institute findOneBySegConsSurname(string $seg_cons_surname) Return the first Institute filtered by the seg_cons_surname column
 * @method Institute findOneByPresConName(string $pres_con_name) Return the first Institute filtered by the pres_con_name column
 * @method Institute findOneByPresConSurname(string $pres_con_surname) Return the first Institute filtered by the pres_con_surname column
 * @method Institute findOneByDirFiscalCode(string $dir_fiscal_code) Return the first Institute filtered by the dir_fiscal_code column
 * @method Institute findOneBySchoolFiscalCode(string $school_fiscal_code) Return the first Institute filtered by the school_fiscal_code column
 * @method Institute findOneByInpdapCode(string $inpdap_code) Return the first Institute filtered by the inpdap_code column
 * @method Institute findOneByAssicurazioniSanitarie(string $assicurazioni_sanitarie) Return the first Institute filtered by the assicurazioni_sanitarie column
 * @method Institute findOneByDirSesso(string $dir_sesso) Return the first Institute filtered by the dir_sesso column
 * @method Institute findOneByDirBirth(string $dir_birth) Return the first Institute filtered by the dir_birth column
 * @method Institute findOneByDirCity(string $dir_city) Return the first Institute filtered by the dir_city column
 * @method Institute findOneByPostalAccount(string $postal_account) Return the first Institute filtered by the postal_account column
 * @method Institute findOneByAtecoCode(string $ateco_code) Return the first Institute filtered by the ateco_code column
 * @method Institute findOneByActivityCode(string $activity_code) Return the first Institute filtered by the activity_code column
 * @method Institute findOneByDirCurrAddr(string $dir_curr_addr) Return the first Institute filtered by the dir_curr_addr column
 * @method Institute findOneByDirCurrCity(string $dir_curr_city) Return the first Institute filtered by the dir_curr_city column
 * @method Institute findOneByDirCurrPhone(string $dir_curr_phone) Return the first Institute filtered by the dir_curr_phone column
 * @method Institute findOneByDirEmpId(int $dir_emp_id) Return the first Institute filtered by the dir_emp_id column
 * @method Institute findOneByAdirEmpId(int $adir_emp_id) Return the first Institute filtered by the adir_emp_id column
 * @method Institute findOneByPresgeEmpId(int $presge_emp_id) Return the first Institute filtered by the presge_emp_id column
 * @method Institute findOneBySegconsEmpId(int $segcons_emp_id) Return the first Institute filtered by the segcons_emp_id column
 * @method Institute findOneByPresconEmpId(int $prescon_emp_id) Return the first Institute filtered by the prescon_emp_id column
 * @method Institute findOneByRespacqEmpId(int $respacq_emp_id) Return the first Institute filtered by the respacq_emp_id column
 * @method Institute findOneByJobDirectorId(int $job_director_id) Return the first Institute filtered by the job_director_id column
 * @method Institute findOneByJobViceDirectorId(int $job_vice_director_id) Return the first Institute filtered by the job_vice_director_id column
 * @method Institute findOneByJobDSGAId(int $job_dsga_id) Return the first Institute filtered by the job_dsga_id column
 * @method Institute findOneByJobPersonnelId(int $job_personnel_id) Return the first Institute filtered by the job_personnel_id column
 * @method Institute findOneByJobAccountingId(int $job_accounting_id) Return the first Institute filtered by the job_accounting_id column
 * @method Institute findOneByJobWarehouseId(int $job_warehouse_id) Return the first Institute filtered by the job_warehouse_id column
 * @method Institute findOneByJobRegistryId(int $job_registry_id) Return the first Institute filtered by the job_registry_id column
 *
 * @method array findByInstituteId(int $institute_id) Return Institute objects filtered by the institute_id column
 * @method array findByName(string $name) Return Institute objects filtered by the name column
 * @method array findByMechanCode(string $mechan_code) Return Institute objects filtered by the mechan_code column
 * @method array findByContactId(int $contact_id) Return Institute objects filtered by the contact_id column
 * @method array findByFiscalCode(string $fiscal_code) Return Institute objects filtered by the fiscal_code column
 * @method array findBySchoolType(string $school_type) Return Institute objects filtered by the school_type column
 * @method array findByParent(int $parent) Return Institute objects filtered by the parent column
 * @method array findByDef(boolean $def) Return Institute objects filtered by the def column
 * @method array findByDirName(string $dir_name) Return Institute objects filtered by the dir_name column
 * @method array findByDirSurname(string $dir_surname) Return Institute objects filtered by the dir_surname column
 * @method array findByAdirName(string $adir_name) Return Institute objects filtered by the adir_name column
 * @method array findByAdirSurname(string $adir_surname) Return Institute objects filtered by the adir_surname column
 * @method array findByPresGeName(string $pres_ge_name) Return Institute objects filtered by the pres_ge_name column
 * @method array findByPresGeSurname(string $pres_ge_surname) Return Institute objects filtered by the pres_ge_surname column
 * @method array findBySegConsName(string $seg_cons_name) Return Institute objects filtered by the seg_cons_name column
 * @method array findBySegConsSurname(string $seg_cons_surname) Return Institute objects filtered by the seg_cons_surname column
 * @method array findByPresConName(string $pres_con_name) Return Institute objects filtered by the pres_con_name column
 * @method array findByPresConSurname(string $pres_con_surname) Return Institute objects filtered by the pres_con_surname column
 * @method array findByDirFiscalCode(string $dir_fiscal_code) Return Institute objects filtered by the dir_fiscal_code column
 * @method array findBySchoolFiscalCode(string $school_fiscal_code) Return Institute objects filtered by the school_fiscal_code column
 * @method array findByInpdapCode(string $inpdap_code) Return Institute objects filtered by the inpdap_code column
 * @method array findByAssicurazioniSanitarie(string $assicurazioni_sanitarie) Return Institute objects filtered by the assicurazioni_sanitarie column
 * @method array findByDirSesso(string $dir_sesso) Return Institute objects filtered by the dir_sesso column
 * @method array findByDirBirth(string $dir_birth) Return Institute objects filtered by the dir_birth column
 * @method array findByDirCity(string $dir_city) Return Institute objects filtered by the dir_city column
 * @method array findByPostalAccount(string $postal_account) Return Institute objects filtered by the postal_account column
 * @method array findByAtecoCode(string $ateco_code) Return Institute objects filtered by the ateco_code column
 * @method array findByActivityCode(string $activity_code) Return Institute objects filtered by the activity_code column
 * @method array findByDirCurrAddr(string $dir_curr_addr) Return Institute objects filtered by the dir_curr_addr column
 * @method array findByDirCurrCity(string $dir_curr_city) Return Institute objects filtered by the dir_curr_city column
 * @method array findByDirCurrPhone(string $dir_curr_phone) Return Institute objects filtered by the dir_curr_phone column
 * @method array findByDirEmpId(int $dir_emp_id) Return Institute objects filtered by the dir_emp_id column
 * @method array findByAdirEmpId(int $adir_emp_id) Return Institute objects filtered by the adir_emp_id column
 * @method array findByPresgeEmpId(int $presge_emp_id) Return Institute objects filtered by the presge_emp_id column
 * @method array findBySegconsEmpId(int $segcons_emp_id) Return Institute objects filtered by the segcons_emp_id column
 * @method array findByPresconEmpId(int $prescon_emp_id) Return Institute objects filtered by the prescon_emp_id column
 * @method array findByRespacqEmpId(int $respacq_emp_id) Return Institute objects filtered by the respacq_emp_id column
 * @method array findByJobDirectorId(int $job_director_id) Return Institute objects filtered by the job_director_id column
 * @method array findByJobViceDirectorId(int $job_vice_director_id) Return Institute objects filtered by the job_vice_director_id column
 * @method array findByJobDSGAId(int $job_dsga_id) Return Institute objects filtered by the job_dsga_id column
 * @method array findByJobPersonnelId(int $job_personnel_id) Return Institute objects filtered by the job_personnel_id column
 * @method array findByJobAccountingId(int $job_accounting_id) Return Institute objects filtered by the job_accounting_id column
 * @method array findByJobWarehouseId(int $job_warehouse_id) Return Institute objects filtered by the job_warehouse_id column
 * @method array findByJobRegistryId(int $job_registry_id) Return Institute objects filtered by the job_registry_id column
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseInstituteQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseInstituteQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\Institute';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new InstituteQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   InstituteQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return InstituteQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof InstituteQuery) {
            return $criteria;
        }
        $query = new InstituteQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj = $c->findPk(array(12, 34), $con);
     * </code>
     *
     * @param array $key Primary key to use for the query
                         A Primary key composition: [$institute_id, $contact_id]
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Institute|Institute[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = InstitutePeer::getInstanceFromPool(serialize(array((string) $key[0], (string) $key[1]))))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Institute A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "institute_id", "name", "mechan_code", "contact_id", "fiscal_code", "school_type", "parent", "def", "dir_name", "dir_surname", "adir_name", "adir_surname", "pres_ge_name", "pres_ge_surname", "seg_cons_name", "seg_cons_surname", "pres_con_name", "pres_con_surname", "dir_fiscal_code", "school_fiscal_code", "inpdap_code", "assicurazioni_sanitarie", "dir_sesso", "dir_birth", "dir_city", "postal_account", "ateco_code", "activity_code", "dir_curr_addr", "dir_curr_city", "dir_curr_phone", "dir_emp_id", "adir_emp_id", "presge_emp_id", "segcons_emp_id", "prescon_emp_id", "respacq_emp_id", "job_director_id", "job_vice_director_id", "job_dsga_id", "job_personnel_id", "job_accounting_id", "job_warehouse_id", "job_registry_id" FROM "institute" WHERE "institute_id" = :p0 AND "contact_id" = :p1';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key[0], PDO::PARAM_INT);
            $stmt->bindValue(':p1', $key[1], PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Institute();
            $obj->hydrate($row);
            InstitutePeer::addInstanceToPool($obj, serialize(array((string) $key[0], (string) $key[1])));
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Institute|Institute[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(array(12, 56), array(832, 123), array(123, 456)), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Institute[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {
        $this->addUsingAlias(InstitutePeer::INSTITUTE_ID, $key[0], Criteria::EQUAL);
        $this->addUsingAlias(InstitutePeer::CONTACT_ID, $key[1], Criteria::EQUAL);

        return $this;
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {
        if (empty($keys)) {
            return $this->add(null, '1<>1', Criteria::CUSTOM);
        }
        foreach ($keys as $key) {
            $cton0 = $this->getNewCriterion(InstitutePeer::INSTITUTE_ID, $key[0], Criteria::EQUAL);
            $cton1 = $this->getNewCriterion(InstitutePeer::CONTACT_ID, $key[1], Criteria::EQUAL);
            $cton0->addAnd($cton1);
            $this->addOr($cton0);
        }

        return $this;
    }

    /**
     * Filter the query on the institute_id column
     *
     * Example usage:
     * <code>
     * $query->filterByInstituteId(1234); // WHERE institute_id = 1234
     * $query->filterByInstituteId(array(12, 34)); // WHERE institute_id IN (12, 34)
     * $query->filterByInstituteId(array('min' => 12)); // WHERE institute_id >= 12
     * $query->filterByInstituteId(array('max' => 12)); // WHERE institute_id <= 12
     * </code>
     *
     * @param     mixed $instituteId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByInstituteId($instituteId = null, $comparison = null)
    {
        if (is_array($instituteId)) {
            $useMinMax = false;
            if (isset($instituteId['min'])) {
                $this->addUsingAlias(InstitutePeer::INSTITUTE_ID, $instituteId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($instituteId['max'])) {
                $this->addUsingAlias(InstitutePeer::INSTITUTE_ID, $instituteId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::INSTITUTE_ID, $instituteId, $comparison);
    }

    /**
     * Filter the query on the name column
     *
     * Example usage:
     * <code>
     * $query->filterByName('fooValue');   // WHERE name = 'fooValue'
     * $query->filterByName('%fooValue%'); // WHERE name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $name The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByName($name = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($name)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $name)) {
                $name = str_replace('*', '%', $name);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::NAME, $name, $comparison);
    }

    /**
     * Filter the query on the mechan_code column
     *
     * Example usage:
     * <code>
     * $query->filterByMechanCode('fooValue');   // WHERE mechan_code = 'fooValue'
     * $query->filterByMechanCode('%fooValue%'); // WHERE mechan_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $mechanCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByMechanCode($mechanCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($mechanCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $mechanCode)) {
                $mechanCode = str_replace('*', '%', $mechanCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::MECHAN_CODE, $mechanCode, $comparison);
    }

    /**
     * Filter the query on the contact_id column
     *
     * Example usage:
     * <code>
     * $query->filterByContactId(1234); // WHERE contact_id = 1234
     * $query->filterByContactId(array(12, 34)); // WHERE contact_id IN (12, 34)
     * $query->filterByContactId(array('min' => 12)); // WHERE contact_id >= 12
     * $query->filterByContactId(array('max' => 12)); // WHERE contact_id <= 12
     * </code>
     *
     * @see       filterByContact()
     *
     * @param     mixed $contactId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByContactId($contactId = null, $comparison = null)
    {
        if (is_array($contactId)) {
            $useMinMax = false;
            if (isset($contactId['min'])) {
                $this->addUsingAlias(InstitutePeer::CONTACT_ID, $contactId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($contactId['max'])) {
                $this->addUsingAlias(InstitutePeer::CONTACT_ID, $contactId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::CONTACT_ID, $contactId, $comparison);
    }

    /**
     * Filter the query on the fiscal_code column
     *
     * Example usage:
     * <code>
     * $query->filterByFiscalCode('fooValue');   // WHERE fiscal_code = 'fooValue'
     * $query->filterByFiscalCode('%fooValue%'); // WHERE fiscal_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $fiscalCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByFiscalCode($fiscalCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($fiscalCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $fiscalCode)) {
                $fiscalCode = str_replace('*', '%', $fiscalCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::FISCAL_CODE, $fiscalCode, $comparison);
    }

    /**
     * Filter the query on the school_type column
     *
     * Example usage:
     * <code>
     * $query->filterBySchoolType('fooValue');   // WHERE school_type = 'fooValue'
     * $query->filterBySchoolType('%fooValue%'); // WHERE school_type LIKE '%fooValue%'
     * </code>
     *
     * @param     string $schoolType The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterBySchoolType($schoolType = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($schoolType)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $schoolType)) {
                $schoolType = str_replace('*', '%', $schoolType);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::SCHOOL_TYPE, $schoolType, $comparison);
    }

    /**
     * Filter the query on the parent column
     *
     * Example usage:
     * <code>
     * $query->filterByParent(1234); // WHERE parent = 1234
     * $query->filterByParent(array(12, 34)); // WHERE parent IN (12, 34)
     * $query->filterByParent(array('min' => 12)); // WHERE parent >= 12
     * $query->filterByParent(array('max' => 12)); // WHERE parent <= 12
     * </code>
     *
     * @param     mixed $parent The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByParent($parent = null, $comparison = null)
    {
        if (is_array($parent)) {
            $useMinMax = false;
            if (isset($parent['min'])) {
                $this->addUsingAlias(InstitutePeer::PARENT, $parent['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($parent['max'])) {
                $this->addUsingAlias(InstitutePeer::PARENT, $parent['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PARENT, $parent, $comparison);
    }

    /**
     * Filter the query on the def column
     *
     * Example usage:
     * <code>
     * $query->filterByDef(true); // WHERE def = true
     * $query->filterByDef('yes'); // WHERE def = true
     * </code>
     *
     * @param     boolean|string $def The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDef($def = null, $comparison = null)
    {
        if (is_string($def)) {
            $def = in_array(strtolower($def), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(InstitutePeer::DEF, $def, $comparison);
    }

    /**
     * Filter the query on the dir_name column
     *
     * Example usage:
     * <code>
     * $query->filterByDirName('fooValue');   // WHERE dir_name = 'fooValue'
     * $query->filterByDirName('%fooValue%'); // WHERE dir_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirName($dirName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirName)) {
                $dirName = str_replace('*', '%', $dirName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_NAME, $dirName, $comparison);
    }

    /**
     * Filter the query on the dir_surname column
     *
     * Example usage:
     * <code>
     * $query->filterByDirSurname('fooValue');   // WHERE dir_surname = 'fooValue'
     * $query->filterByDirSurname('%fooValue%'); // WHERE dir_surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirSurname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirSurname($dirSurname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirSurname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirSurname)) {
                $dirSurname = str_replace('*', '%', $dirSurname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_SURNAME, $dirSurname, $comparison);
    }

    /**
     * Filter the query on the adir_name column
     *
     * Example usage:
     * <code>
     * $query->filterByAdirName('fooValue');   // WHERE adir_name = 'fooValue'
     * $query->filterByAdirName('%fooValue%'); // WHERE adir_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $adirName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByAdirName($adirName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($adirName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $adirName)) {
                $adirName = str_replace('*', '%', $adirName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::ADIR_NAME, $adirName, $comparison);
    }

    /**
     * Filter the query on the adir_surname column
     *
     * Example usage:
     * <code>
     * $query->filterByAdirSurname('fooValue');   // WHERE adir_surname = 'fooValue'
     * $query->filterByAdirSurname('%fooValue%'); // WHERE adir_surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $adirSurname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByAdirSurname($adirSurname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($adirSurname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $adirSurname)) {
                $adirSurname = str_replace('*', '%', $adirSurname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::ADIR_SURNAME, $adirSurname, $comparison);
    }

    /**
     * Filter the query on the pres_ge_name column
     *
     * Example usage:
     * <code>
     * $query->filterByPresGeName('fooValue');   // WHERE pres_ge_name = 'fooValue'
     * $query->filterByPresGeName('%fooValue%'); // WHERE pres_ge_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $presGeName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPresGeName($presGeName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($presGeName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $presGeName)) {
                $presGeName = str_replace('*', '%', $presGeName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PRES_GE_NAME, $presGeName, $comparison);
    }

    /**
     * Filter the query on the pres_ge_surname column
     *
     * Example usage:
     * <code>
     * $query->filterByPresGeSurname('fooValue');   // WHERE pres_ge_surname = 'fooValue'
     * $query->filterByPresGeSurname('%fooValue%'); // WHERE pres_ge_surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $presGeSurname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPresGeSurname($presGeSurname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($presGeSurname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $presGeSurname)) {
                $presGeSurname = str_replace('*', '%', $presGeSurname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PRES_GE_SURNAME, $presGeSurname, $comparison);
    }

    /**
     * Filter the query on the seg_cons_name column
     *
     * Example usage:
     * <code>
     * $query->filterBySegConsName('fooValue');   // WHERE seg_cons_name = 'fooValue'
     * $query->filterBySegConsName('%fooValue%'); // WHERE seg_cons_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $segConsName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterBySegConsName($segConsName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($segConsName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $segConsName)) {
                $segConsName = str_replace('*', '%', $segConsName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::SEG_CONS_NAME, $segConsName, $comparison);
    }

    /**
     * Filter the query on the seg_cons_surname column
     *
     * Example usage:
     * <code>
     * $query->filterBySegConsSurname('fooValue');   // WHERE seg_cons_surname = 'fooValue'
     * $query->filterBySegConsSurname('%fooValue%'); // WHERE seg_cons_surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $segConsSurname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterBySegConsSurname($segConsSurname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($segConsSurname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $segConsSurname)) {
                $segConsSurname = str_replace('*', '%', $segConsSurname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::SEG_CONS_SURNAME, $segConsSurname, $comparison);
    }

    /**
     * Filter the query on the pres_con_name column
     *
     * Example usage:
     * <code>
     * $query->filterByPresConName('fooValue');   // WHERE pres_con_name = 'fooValue'
     * $query->filterByPresConName('%fooValue%'); // WHERE pres_con_name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $presConName The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPresConName($presConName = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($presConName)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $presConName)) {
                $presConName = str_replace('*', '%', $presConName);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PRES_CON_NAME, $presConName, $comparison);
    }

    /**
     * Filter the query on the pres_con_surname column
     *
     * Example usage:
     * <code>
     * $query->filterByPresConSurname('fooValue');   // WHERE pres_con_surname = 'fooValue'
     * $query->filterByPresConSurname('%fooValue%'); // WHERE pres_con_surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $presConSurname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPresConSurname($presConSurname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($presConSurname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $presConSurname)) {
                $presConSurname = str_replace('*', '%', $presConSurname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PRES_CON_SURNAME, $presConSurname, $comparison);
    }

    /**
     * Filter the query on the dir_fiscal_code column
     *
     * Example usage:
     * <code>
     * $query->filterByDirFiscalCode('fooValue');   // WHERE dir_fiscal_code = 'fooValue'
     * $query->filterByDirFiscalCode('%fooValue%'); // WHERE dir_fiscal_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirFiscalCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirFiscalCode($dirFiscalCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirFiscalCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirFiscalCode)) {
                $dirFiscalCode = str_replace('*', '%', $dirFiscalCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_FISCAL_CODE, $dirFiscalCode, $comparison);
    }

    /**
     * Filter the query on the school_fiscal_code column
     *
     * Example usage:
     * <code>
     * $query->filterBySchoolFiscalCode('fooValue');   // WHERE school_fiscal_code = 'fooValue'
     * $query->filterBySchoolFiscalCode('%fooValue%'); // WHERE school_fiscal_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $schoolFiscalCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterBySchoolFiscalCode($schoolFiscalCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($schoolFiscalCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $schoolFiscalCode)) {
                $schoolFiscalCode = str_replace('*', '%', $schoolFiscalCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::SCHOOL_FISCAL_CODE, $schoolFiscalCode, $comparison);
    }

    /**
     * Filter the query on the inpdap_code column
     *
     * Example usage:
     * <code>
     * $query->filterByInpdapCode('fooValue');   // WHERE inpdap_code = 'fooValue'
     * $query->filterByInpdapCode('%fooValue%'); // WHERE inpdap_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $inpdapCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByInpdapCode($inpdapCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($inpdapCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $inpdapCode)) {
                $inpdapCode = str_replace('*', '%', $inpdapCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::INPDAP_CODE, $inpdapCode, $comparison);
    }

    /**
     * Filter the query on the assicurazioni_sanitarie column
     *
     * Example usage:
     * <code>
     * $query->filterByAssicurazioniSanitarie('fooValue');   // WHERE assicurazioni_sanitarie = 'fooValue'
     * $query->filterByAssicurazioniSanitarie('%fooValue%'); // WHERE assicurazioni_sanitarie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $assicurazioniSanitarie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByAssicurazioniSanitarie($assicurazioniSanitarie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($assicurazioniSanitarie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $assicurazioniSanitarie)) {
                $assicurazioniSanitarie = str_replace('*', '%', $assicurazioniSanitarie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::ASSICURAZIONI_SANITARIE, $assicurazioniSanitarie, $comparison);
    }

    /**
     * Filter the query on the dir_sesso column
     *
     * Example usage:
     * <code>
     * $query->filterByDirSesso('fooValue');   // WHERE dir_sesso = 'fooValue'
     * $query->filterByDirSesso('%fooValue%'); // WHERE dir_sesso LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirSesso The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirSesso($dirSesso = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirSesso)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirSesso)) {
                $dirSesso = str_replace('*', '%', $dirSesso);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_SESSO, $dirSesso, $comparison);
    }

    /**
     * Filter the query on the dir_birth column
     *
     * Example usage:
     * <code>
     * $query->filterByDirBirth(1234); // WHERE dir_birth = 1234
     * $query->filterByDirBirth(array(12, 34)); // WHERE dir_birth IN (12, 34)
     * $query->filterByDirBirth(array('min' => 12)); // WHERE dir_birth >= 12
     * $query->filterByDirBirth(array('max' => 12)); // WHERE dir_birth <= 12
     * </code>
     *
     * @param     mixed $dirBirth The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirBirth($dirBirth = null, $comparison = null)
    {
        if (is_array($dirBirth)) {
            $useMinMax = false;
            if (isset($dirBirth['min'])) {
                $this->addUsingAlias(InstitutePeer::DIR_BIRTH, $dirBirth['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dirBirth['max'])) {
                $this->addUsingAlias(InstitutePeer::DIR_BIRTH, $dirBirth['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_BIRTH, $dirBirth, $comparison);
    }

    /**
     * Filter the query on the dir_city column
     *
     * Example usage:
     * <code>
     * $query->filterByDirCity('fooValue');   // WHERE dir_city = 'fooValue'
     * $query->filterByDirCity('%fooValue%'); // WHERE dir_city LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirCity The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirCity($dirCity = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirCity)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirCity)) {
                $dirCity = str_replace('*', '%', $dirCity);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_CITY, $dirCity, $comparison);
    }

    /**
     * Filter the query on the postal_account column
     *
     * Example usage:
     * <code>
     * $query->filterByPostalAccount(1234); // WHERE postal_account = 1234
     * $query->filterByPostalAccount(array(12, 34)); // WHERE postal_account IN (12, 34)
     * $query->filterByPostalAccount(array('min' => 12)); // WHERE postal_account >= 12
     * $query->filterByPostalAccount(array('max' => 12)); // WHERE postal_account <= 12
     * </code>
     *
     * @param     mixed $postalAccount The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPostalAccount($postalAccount = null, $comparison = null)
    {
        if (is_array($postalAccount)) {
            $useMinMax = false;
            if (isset($postalAccount['min'])) {
                $this->addUsingAlias(InstitutePeer::POSTAL_ACCOUNT, $postalAccount['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($postalAccount['max'])) {
                $this->addUsingAlias(InstitutePeer::POSTAL_ACCOUNT, $postalAccount['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::POSTAL_ACCOUNT, $postalAccount, $comparison);
    }

    /**
     * Filter the query on the ateco_code column
     *
     * Example usage:
     * <code>
     * $query->filterByAtecoCode('fooValue');   // WHERE ateco_code = 'fooValue'
     * $query->filterByAtecoCode('%fooValue%'); // WHERE ateco_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $atecoCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByAtecoCode($atecoCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($atecoCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $atecoCode)) {
                $atecoCode = str_replace('*', '%', $atecoCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::ATECO_CODE, $atecoCode, $comparison);
    }

    /**
     * Filter the query on the activity_code column
     *
     * Example usage:
     * <code>
     * $query->filterByActivityCode('fooValue');   // WHERE activity_code = 'fooValue'
     * $query->filterByActivityCode('%fooValue%'); // WHERE activity_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $activityCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByActivityCode($activityCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($activityCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $activityCode)) {
                $activityCode = str_replace('*', '%', $activityCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::ACTIVITY_CODE, $activityCode, $comparison);
    }

    /**
     * Filter the query on the dir_curr_addr column
     *
     * Example usage:
     * <code>
     * $query->filterByDirCurrAddr('fooValue');   // WHERE dir_curr_addr = 'fooValue'
     * $query->filterByDirCurrAddr('%fooValue%'); // WHERE dir_curr_addr LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirCurrAddr The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirCurrAddr($dirCurrAddr = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirCurrAddr)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirCurrAddr)) {
                $dirCurrAddr = str_replace('*', '%', $dirCurrAddr);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_CURR_ADDR, $dirCurrAddr, $comparison);
    }

    /**
     * Filter the query on the dir_curr_city column
     *
     * Example usage:
     * <code>
     * $query->filterByDirCurrCity('fooValue');   // WHERE dir_curr_city = 'fooValue'
     * $query->filterByDirCurrCity('%fooValue%'); // WHERE dir_curr_city LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirCurrCity The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirCurrCity($dirCurrCity = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirCurrCity)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirCurrCity)) {
                $dirCurrCity = str_replace('*', '%', $dirCurrCity);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_CURR_CITY, $dirCurrCity, $comparison);
    }

    /**
     * Filter the query on the dir_curr_phone column
     *
     * Example usage:
     * <code>
     * $query->filterByDirCurrPhone('fooValue');   // WHERE dir_curr_phone = 'fooValue'
     * $query->filterByDirCurrPhone('%fooValue%'); // WHERE dir_curr_phone LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dirCurrPhone The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirCurrPhone($dirCurrPhone = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dirCurrPhone)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dirCurrPhone)) {
                $dirCurrPhone = str_replace('*', '%', $dirCurrPhone);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_CURR_PHONE, $dirCurrPhone, $comparison);
    }

    /**
     * Filter the query on the dir_emp_id column
     *
     * Example usage:
     * <code>
     * $query->filterByDirEmpId(1234); // WHERE dir_emp_id = 1234
     * $query->filterByDirEmpId(array(12, 34)); // WHERE dir_emp_id IN (12, 34)
     * $query->filterByDirEmpId(array('min' => 12)); // WHERE dir_emp_id >= 12
     * $query->filterByDirEmpId(array('max' => 12)); // WHERE dir_emp_id <= 12
     * </code>
     *
     * @see       filterByEmployee()
     *
     * @param     mixed $dirEmpId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByDirEmpId($dirEmpId = null, $comparison = null)
    {
        if (is_array($dirEmpId)) {
            $useMinMax = false;
            if (isset($dirEmpId['min'])) {
                $this->addUsingAlias(InstitutePeer::DIR_EMP_ID, $dirEmpId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dirEmpId['max'])) {
                $this->addUsingAlias(InstitutePeer::DIR_EMP_ID, $dirEmpId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::DIR_EMP_ID, $dirEmpId, $comparison);
    }

    /**
     * Filter the query on the adir_emp_id column
     *
     * Example usage:
     * <code>
     * $query->filterByAdirEmpId(1234); // WHERE adir_emp_id = 1234
     * $query->filterByAdirEmpId(array(12, 34)); // WHERE adir_emp_id IN (12, 34)
     * $query->filterByAdirEmpId(array('min' => 12)); // WHERE adir_emp_id >= 12
     * $query->filterByAdirEmpId(array('max' => 12)); // WHERE adir_emp_id <= 12
     * </code>
     *
     * @param     mixed $adirEmpId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByAdirEmpId($adirEmpId = null, $comparison = null)
    {
        if (is_array($adirEmpId)) {
            $useMinMax = false;
            if (isset($adirEmpId['min'])) {
                $this->addUsingAlias(InstitutePeer::ADIR_EMP_ID, $adirEmpId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($adirEmpId['max'])) {
                $this->addUsingAlias(InstitutePeer::ADIR_EMP_ID, $adirEmpId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::ADIR_EMP_ID, $adirEmpId, $comparison);
    }

    /**
     * Filter the query on the presge_emp_id column
     *
     * Example usage:
     * <code>
     * $query->filterByPresgeEmpId(1234); // WHERE presge_emp_id = 1234
     * $query->filterByPresgeEmpId(array(12, 34)); // WHERE presge_emp_id IN (12, 34)
     * $query->filterByPresgeEmpId(array('min' => 12)); // WHERE presge_emp_id >= 12
     * $query->filterByPresgeEmpId(array('max' => 12)); // WHERE presge_emp_id <= 12
     * </code>
     *
     * @param     mixed $presgeEmpId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPresgeEmpId($presgeEmpId = null, $comparison = null)
    {
        if (is_array($presgeEmpId)) {
            $useMinMax = false;
            if (isset($presgeEmpId['min'])) {
                $this->addUsingAlias(InstitutePeer::PRESGE_EMP_ID, $presgeEmpId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($presgeEmpId['max'])) {
                $this->addUsingAlias(InstitutePeer::PRESGE_EMP_ID, $presgeEmpId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PRESGE_EMP_ID, $presgeEmpId, $comparison);
    }

    /**
     * Filter the query on the segcons_emp_id column
     *
     * Example usage:
     * <code>
     * $query->filterBySegconsEmpId(1234); // WHERE segcons_emp_id = 1234
     * $query->filterBySegconsEmpId(array(12, 34)); // WHERE segcons_emp_id IN (12, 34)
     * $query->filterBySegconsEmpId(array('min' => 12)); // WHERE segcons_emp_id >= 12
     * $query->filterBySegconsEmpId(array('max' => 12)); // WHERE segcons_emp_id <= 12
     * </code>
     *
     * @param     mixed $segconsEmpId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterBySegconsEmpId($segconsEmpId = null, $comparison = null)
    {
        if (is_array($segconsEmpId)) {
            $useMinMax = false;
            if (isset($segconsEmpId['min'])) {
                $this->addUsingAlias(InstitutePeer::SEGCONS_EMP_ID, $segconsEmpId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($segconsEmpId['max'])) {
                $this->addUsingAlias(InstitutePeer::SEGCONS_EMP_ID, $segconsEmpId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::SEGCONS_EMP_ID, $segconsEmpId, $comparison);
    }

    /**
     * Filter the query on the prescon_emp_id column
     *
     * Example usage:
     * <code>
     * $query->filterByPresconEmpId(1234); // WHERE prescon_emp_id = 1234
     * $query->filterByPresconEmpId(array(12, 34)); // WHERE prescon_emp_id IN (12, 34)
     * $query->filterByPresconEmpId(array('min' => 12)); // WHERE prescon_emp_id >= 12
     * $query->filterByPresconEmpId(array('max' => 12)); // WHERE prescon_emp_id <= 12
     * </code>
     *
     * @param     mixed $presconEmpId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByPresconEmpId($presconEmpId = null, $comparison = null)
    {
        if (is_array($presconEmpId)) {
            $useMinMax = false;
            if (isset($presconEmpId['min'])) {
                $this->addUsingAlias(InstitutePeer::PRESCON_EMP_ID, $presconEmpId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($presconEmpId['max'])) {
                $this->addUsingAlias(InstitutePeer::PRESCON_EMP_ID, $presconEmpId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::PRESCON_EMP_ID, $presconEmpId, $comparison);
    }

    /**
     * Filter the query on the respacq_emp_id column
     *
     * Example usage:
     * <code>
     * $query->filterByRespacqEmpId(1234); // WHERE respacq_emp_id = 1234
     * $query->filterByRespacqEmpId(array(12, 34)); // WHERE respacq_emp_id IN (12, 34)
     * $query->filterByRespacqEmpId(array('min' => 12)); // WHERE respacq_emp_id >= 12
     * $query->filterByRespacqEmpId(array('max' => 12)); // WHERE respacq_emp_id <= 12
     * </code>
     *
     * @param     mixed $respacqEmpId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByRespacqEmpId($respacqEmpId = null, $comparison = null)
    {
        if (is_array($respacqEmpId)) {
            $useMinMax = false;
            if (isset($respacqEmpId['min'])) {
                $this->addUsingAlias(InstitutePeer::RESPACQ_EMP_ID, $respacqEmpId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($respacqEmpId['max'])) {
                $this->addUsingAlias(InstitutePeer::RESPACQ_EMP_ID, $respacqEmpId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::RESPACQ_EMP_ID, $respacqEmpId, $comparison);
    }

    /**
     * Filter the query on the job_director_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobDirectorId(1234); // WHERE job_director_id = 1234
     * $query->filterByJobDirectorId(array(12, 34)); // WHERE job_director_id IN (12, 34)
     * $query->filterByJobDirectorId(array('min' => 12)); // WHERE job_director_id >= 12
     * $query->filterByJobDirectorId(array('max' => 12)); // WHERE job_director_id <= 12
     * </code>
     *
     * @param     mixed $jobDirectorId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobDirectorId($jobDirectorId = null, $comparison = null)
    {
        if (is_array($jobDirectorId)) {
            $useMinMax = false;
            if (isset($jobDirectorId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_DIRECTOR_ID, $jobDirectorId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobDirectorId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_DIRECTOR_ID, $jobDirectorId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_DIRECTOR_ID, $jobDirectorId, $comparison);
    }

    /**
     * Filter the query on the job_vice_director_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobViceDirectorId(1234); // WHERE job_vice_director_id = 1234
     * $query->filterByJobViceDirectorId(array(12, 34)); // WHERE job_vice_director_id IN (12, 34)
     * $query->filterByJobViceDirectorId(array('min' => 12)); // WHERE job_vice_director_id >= 12
     * $query->filterByJobViceDirectorId(array('max' => 12)); // WHERE job_vice_director_id <= 12
     * </code>
     *
     * @param     mixed $jobViceDirectorId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobViceDirectorId($jobViceDirectorId = null, $comparison = null)
    {
        if (is_array($jobViceDirectorId)) {
            $useMinMax = false;
            if (isset($jobViceDirectorId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_VICE_DIRECTOR_ID, $jobViceDirectorId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobViceDirectorId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_VICE_DIRECTOR_ID, $jobViceDirectorId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_VICE_DIRECTOR_ID, $jobViceDirectorId, $comparison);
    }

    /**
     * Filter the query on the job_dsga_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobDSGAId(1234); // WHERE job_dsga_id = 1234
     * $query->filterByJobDSGAId(array(12, 34)); // WHERE job_dsga_id IN (12, 34)
     * $query->filterByJobDSGAId(array('min' => 12)); // WHERE job_dsga_id >= 12
     * $query->filterByJobDSGAId(array('max' => 12)); // WHERE job_dsga_id <= 12
     * </code>
     *
     * @param     mixed $jobDSGAId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobDSGAId($jobDSGAId = null, $comparison = null)
    {
        if (is_array($jobDSGAId)) {
            $useMinMax = false;
            if (isset($jobDSGAId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_DSGA_ID, $jobDSGAId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobDSGAId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_DSGA_ID, $jobDSGAId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_DSGA_ID, $jobDSGAId, $comparison);
    }

    /**
     * Filter the query on the job_personnel_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobPersonnelId(1234); // WHERE job_personnel_id = 1234
     * $query->filterByJobPersonnelId(array(12, 34)); // WHERE job_personnel_id IN (12, 34)
     * $query->filterByJobPersonnelId(array('min' => 12)); // WHERE job_personnel_id >= 12
     * $query->filterByJobPersonnelId(array('max' => 12)); // WHERE job_personnel_id <= 12
     * </code>
     *
     * @param     mixed $jobPersonnelId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobPersonnelId($jobPersonnelId = null, $comparison = null)
    {
        if (is_array($jobPersonnelId)) {
            $useMinMax = false;
            if (isset($jobPersonnelId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_PERSONNEL_ID, $jobPersonnelId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobPersonnelId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_PERSONNEL_ID, $jobPersonnelId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_PERSONNEL_ID, $jobPersonnelId, $comparison);
    }

    /**
     * Filter the query on the job_accounting_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobAccountingId(1234); // WHERE job_accounting_id = 1234
     * $query->filterByJobAccountingId(array(12, 34)); // WHERE job_accounting_id IN (12, 34)
     * $query->filterByJobAccountingId(array('min' => 12)); // WHERE job_accounting_id >= 12
     * $query->filterByJobAccountingId(array('max' => 12)); // WHERE job_accounting_id <= 12
     * </code>
     *
     * @param     mixed $jobAccountingId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobAccountingId($jobAccountingId = null, $comparison = null)
    {
        if (is_array($jobAccountingId)) {
            $useMinMax = false;
            if (isset($jobAccountingId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_ACCOUNTING_ID, $jobAccountingId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobAccountingId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_ACCOUNTING_ID, $jobAccountingId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_ACCOUNTING_ID, $jobAccountingId, $comparison);
    }

    /**
     * Filter the query on the job_warehouse_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobWarehouseId(1234); // WHERE job_warehouse_id = 1234
     * $query->filterByJobWarehouseId(array(12, 34)); // WHERE job_warehouse_id IN (12, 34)
     * $query->filterByJobWarehouseId(array('min' => 12)); // WHERE job_warehouse_id >= 12
     * $query->filterByJobWarehouseId(array('max' => 12)); // WHERE job_warehouse_id <= 12
     * </code>
     *
     * @param     mixed $jobWarehouseId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobWarehouseId($jobWarehouseId = null, $comparison = null)
    {
        if (is_array($jobWarehouseId)) {
            $useMinMax = false;
            if (isset($jobWarehouseId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_WAREHOUSE_ID, $jobWarehouseId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobWarehouseId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_WAREHOUSE_ID, $jobWarehouseId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_WAREHOUSE_ID, $jobWarehouseId, $comparison);
    }

    /**
     * Filter the query on the job_registry_id column
     *
     * Example usage:
     * <code>
     * $query->filterByJobRegistryId(1234); // WHERE job_registry_id = 1234
     * $query->filterByJobRegistryId(array(12, 34)); // WHERE job_registry_id IN (12, 34)
     * $query->filterByJobRegistryId(array('min' => 12)); // WHERE job_registry_id >= 12
     * $query->filterByJobRegistryId(array('max' => 12)); // WHERE job_registry_id <= 12
     * </code>
     *
     * @param     mixed $jobRegistryId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function filterByJobRegistryId($jobRegistryId = null, $comparison = null)
    {
        if (is_array($jobRegistryId)) {
            $useMinMax = false;
            if (isset($jobRegistryId['min'])) {
                $this->addUsingAlias(InstitutePeer::JOB_REGISTRY_ID, $jobRegistryId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($jobRegistryId['max'])) {
                $this->addUsingAlias(InstitutePeer::JOB_REGISTRY_ID, $jobRegistryId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(InstitutePeer::JOB_REGISTRY_ID, $jobRegistryId, $comparison);
    }

    /**
     * Filter the query by a related Contact object
     *
     * @param   Contact|PropelObjectCollection $contact The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByContact($contact, $comparison = null)
    {
        if ($contact instanceof Contact) {
            return $this
                ->addUsingAlias(InstitutePeer::CONTACT_ID, $contact->getContactId(), $comparison);
        } elseif ($contact instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(InstitutePeer::CONTACT_ID, $contact->toKeyValue('PrimaryKey', 'ContactId'), $comparison);
        } else {
            throw new PropelException('filterByContact() only accepts arguments of type Contact or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Contact relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function joinContact($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Contact');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Contact');
        }

        return $this;
    }

    /**
     * Use the Contact relation Contact object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\ContactQuery A secondary query class using the current class as primary query
     */
    public function useContactQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinContact($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Contact', '\Core\ContactQuery');
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(InstitutePeer::DIR_EMP_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(InstitutePeer::DIR_EMP_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Employee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function joinEmployee($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Employee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Employee');
        }

        return $this;
    }

    /**
     * Use the Employee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useEmployeeQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Employee', '\Employee\EmployeeQuery');
    }

    /**
     * Filter the query by a related InstituteBankProfile object
     *
     * @param   InstituteBankProfile|PropelObjectCollection $instituteBankProfile  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 InstituteQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstituteBankProfile($instituteBankProfile, $comparison = null)
    {
        if ($instituteBankProfile instanceof InstituteBankProfile) {
            return $this
                ->addUsingAlias(InstitutePeer::INSTITUTE_ID, $instituteBankProfile->getInstitute(), $comparison);
        } elseif ($instituteBankProfile instanceof PropelObjectCollection) {
            return $this
                ->useInstituteBankProfileQuery()
                ->filterByPrimaryKeys($instituteBankProfile->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByInstituteBankProfile() only accepts arguments of type InstituteBankProfile or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the InstituteBankProfile relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function joinInstituteBankProfile($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('InstituteBankProfile');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'InstituteBankProfile');
        }

        return $this;
    }

    /**
     * Use the InstituteBankProfile relation InstituteBankProfile object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\InstituteBankProfileQuery A secondary query class using the current class as primary query
     */
    public function useInstituteBankProfileQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstituteBankProfile($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'InstituteBankProfile', '\Core\InstituteBankProfileQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Institute $institute Object to remove from the list of results
     *
     * @return InstituteQuery The current query, for fluid interface
     */
    public function prune($institute = null)
    {
        if ($institute) {
            $this->addCond('pruneCond0', $this->getAliasedColName(InstitutePeer::INSTITUTE_ID), $institute->getInstituteId(), Criteria::NOT_EQUAL);
            $this->addCond('pruneCond1', $this->getAliasedColName(InstitutePeer::CONTACT_ID), $institute->getContactId(), Criteria::NOT_EQUAL);
            $this->combine(array('pruneCond0', 'pruneCond1'), Criteria::LOGICAL_OR);
        }

        return $this;
    }

}
