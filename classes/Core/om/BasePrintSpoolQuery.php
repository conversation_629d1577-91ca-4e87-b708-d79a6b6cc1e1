<?php

namespace Core\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\PrintSpool;
use Core\PrintSpoolPeer;
use Core\PrintSpoolQuery;

/**
 * Base class that represents a query for the 'core_print_spool' table.
 *
 *
 *
 * @method PrintSpoolQuery orderById($order = Criteria::ASC) Order by the id column
 * @method PrintSpoolQuery orderByUserId($order = Criteria::ASC) Order by the user_id column
 * @method PrintSpoolQuery orderByName($order = Criteria::ASC) Order by the name column
 * @method PrintSpoolQuery orderByPath($order = Criteria::ASC) Order by the path column
 * @method PrintSpoolQuery orderByCompleted($order = Criteria::ASC) Order by the completed column
 * @method PrintSpoolQuery orderByParameters($order = Criteria::ASC) Order by the params column
 * @method PrintSpoolQuery orderByNotified($order = Criteria::ASC) Order by the notified column
 * @method PrintSpoolQuery orderByMime($order = Criteria::ASC) Order by the mime column
 *
 * @method PrintSpoolQuery groupById() Group by the id column
 * @method PrintSpoolQuery groupByUserId() Group by the user_id column
 * @method PrintSpoolQuery groupByName() Group by the name column
 * @method PrintSpoolQuery groupByPath() Group by the path column
 * @method PrintSpoolQuery groupByCompleted() Group by the completed column
 * @method PrintSpoolQuery groupByParameters() Group by the params column
 * @method PrintSpoolQuery groupByNotified() Group by the notified column
 * @method PrintSpoolQuery groupByMime() Group by the mime column
 *
 * @method PrintSpoolQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method PrintSpoolQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method PrintSpoolQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method PrintSpool findOne(PropelPDO $con = null) Return the first PrintSpool matching the query
 * @method PrintSpool findOneOrCreate(PropelPDO $con = null) Return the first PrintSpool matching the query, or a new PrintSpool object populated from the query conditions when no match is found
 *
 * @method PrintSpool findOneByUserId(int $user_id) Return the first PrintSpool filtered by the user_id column
 * @method PrintSpool findOneByName(string $name) Return the first PrintSpool filtered by the name column
 * @method PrintSpool findOneByPath(string $path) Return the first PrintSpool filtered by the path column
 * @method PrintSpool findOneByCompleted(boolean $completed) Return the first PrintSpool filtered by the completed column
 * @method PrintSpool findOneByParameters(string $params) Return the first PrintSpool filtered by the params column
 * @method PrintSpool findOneByNotified(boolean $notified) Return the first PrintSpool filtered by the notified column
 * @method PrintSpool findOneByMime(string $mime) Return the first PrintSpool filtered by the mime column
 *
 * @method array findById(int $id) Return PrintSpool objects filtered by the id column
 * @method array findByUserId(int $user_id) Return PrintSpool objects filtered by the user_id column
 * @method array findByName(string $name) Return PrintSpool objects filtered by the name column
 * @method array findByPath(string $path) Return PrintSpool objects filtered by the path column
 * @method array findByCompleted(boolean $completed) Return PrintSpool objects filtered by the completed column
 * @method array findByParameters(string $params) Return PrintSpool objects filtered by the params column
 * @method array findByNotified(boolean $notified) Return PrintSpool objects filtered by the notified column
 * @method array findByMime(string $mime) Return PrintSpool objects filtered by the mime column
 *
 * @package    propel.generator.Core.om
 */
abstract class BasePrintSpoolQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BasePrintSpoolQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Core\\PrintSpool';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new PrintSpoolQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   PrintSpoolQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return PrintSpoolQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof PrintSpoolQuery) {
            return $criteria;
        }
        $query = new PrintSpoolQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   PrintSpool|PrintSpool[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = PrintSpoolPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(PrintSpoolPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 PrintSpool A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 PrintSpool A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "user_id", "name", "path", "completed", "params", "notified", "mime" FROM "core_print_spool" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new PrintSpool();
            $obj->hydrate($row);
            PrintSpoolPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return PrintSpool|PrintSpool[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|PrintSpool[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(PrintSpoolPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(PrintSpoolPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(PrintSpoolPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(PrintSpoolPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PrintSpoolPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the user_id column
     *
     * Example usage:
     * <code>
     * $query->filterByUserId(1234); // WHERE user_id = 1234
     * $query->filterByUserId(array(12, 34)); // WHERE user_id IN (12, 34)
     * $query->filterByUserId(array('min' => 12)); // WHERE user_id >= 12
     * $query->filterByUserId(array('max' => 12)); // WHERE user_id <= 12
     * </code>
     *
     * @param     mixed $userId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByUserId($userId = null, $comparison = null)
    {
        if (is_array($userId)) {
            $useMinMax = false;
            if (isset($userId['min'])) {
                $this->addUsingAlias(PrintSpoolPeer::USER_ID, $userId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($userId['max'])) {
                $this->addUsingAlias(PrintSpoolPeer::USER_ID, $userId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PrintSpoolPeer::USER_ID, $userId, $comparison);
    }

    /**
     * Filter the query on the name column
     *
     * Example usage:
     * <code>
     * $query->filterByName('fooValue');   // WHERE name = 'fooValue'
     * $query->filterByName('%fooValue%'); // WHERE name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $name The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByName($name = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($name)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $name)) {
                $name = str_replace('*', '%', $name);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(PrintSpoolPeer::NAME, $name, $comparison);
    }

    /**
     * Filter the query on the path column
     *
     * Example usage:
     * <code>
     * $query->filterByPath('fooValue');   // WHERE path = 'fooValue'
     * $query->filterByPath('%fooValue%'); // WHERE path LIKE '%fooValue%'
     * </code>
     *
     * @param     string $path The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByPath($path = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($path)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $path)) {
                $path = str_replace('*', '%', $path);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(PrintSpoolPeer::PATH, $path, $comparison);
    }

    /**
     * Filter the query on the completed column
     *
     * Example usage:
     * <code>
     * $query->filterByCompleted(true); // WHERE completed = true
     * $query->filterByCompleted('yes'); // WHERE completed = true
     * </code>
     *
     * @param     boolean|string $completed The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByCompleted($completed = null, $comparison = null)
    {
        if (is_string($completed)) {
            $completed = in_array(strtolower($completed), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(PrintSpoolPeer::COMPLETED, $completed, $comparison);
    }

    /**
     * Filter the query on the params column
     *
     * Example usage:
     * <code>
     * $query->filterByParameters('fooValue');   // WHERE params = 'fooValue'
     * $query->filterByParameters('%fooValue%'); // WHERE params LIKE '%fooValue%'
     * </code>
     *
     * @param     string $parameters The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByParameters($parameters = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($parameters)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $parameters)) {
                $parameters = str_replace('*', '%', $parameters);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(PrintSpoolPeer::PARAMS, $parameters, $comparison);
    }

    /**
     * Filter the query on the notified column
     *
     * Example usage:
     * <code>
     * $query->filterByNotified(true); // WHERE notified = true
     * $query->filterByNotified('yes'); // WHERE notified = true
     * </code>
     *
     * @param     boolean|string $notified The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByNotified($notified = null, $comparison = null)
    {
        if (is_string($notified)) {
            $notified = in_array(strtolower($notified), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(PrintSpoolPeer::NOTIFIED, $notified, $comparison);
    }

    /**
     * Filter the query on the mime column
     *
     * Example usage:
     * <code>
     * $query->filterByMime('fooValue');   // WHERE mime = 'fooValue'
     * $query->filterByMime('%fooValue%'); // WHERE mime LIKE '%fooValue%'
     * </code>
     *
     * @param     string $mime The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function filterByMime($mime = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($mime)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $mime)) {
                $mime = str_replace('*', '%', $mime);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(PrintSpoolPeer::MIME, $mime, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   PrintSpool $printSpool Object to remove from the list of results
     *
     * @return PrintSpoolQuery The current query, for fluid interface
     */
    public function prune($printSpool = null)
    {
        if ($printSpool) {
            $this->addUsingAlias(PrintSpoolPeer::ID, $printSpool->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
