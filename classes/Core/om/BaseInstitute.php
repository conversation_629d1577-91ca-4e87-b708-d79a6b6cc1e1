<?php

namespace Core\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Contact;
use Core\ContactQuery;
use Core\Institute;
use Core\InstituteBankProfile;
use Core\InstituteBankProfileQuery;
use Core\InstitutePeer;
use Core\InstituteQuery;
use Employee\Employee;
use Employee\EmployeeQuery;

/**
 * Base class that represents a row from the 'institute' table.
 *
 *
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseInstitute extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Core\\InstitutePeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        InstitutePeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the institute_id field.
     * @var        int
     */
    protected $institute_id;

    /**
     * The value for the name field.
     * @var        string
     */
    protected $name;

    /**
     * The value for the mechan_code field.
     * @var        string
     */
    protected $mechan_code;

    /**
     * The value for the contact_id field.
     * @var        int
     */
    protected $contact_id;

    /**
     * The value for the fiscal_code field.
     * @var        string
     */
    protected $fiscal_code;

    /**
     * The value for the school_type field.
     * @var        string
     */
    protected $school_type;

    /**
     * The value for the parent field.
     * @var        int
     */
    protected $parent;

    /**
     * The value for the def field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $def;

    /**
     * The value for the dir_name field.
     * @var        string
     */
    protected $dir_name;

    /**
     * The value for the dir_surname field.
     * @var        string
     */
    protected $dir_surname;

    /**
     * The value for the adir_name field.
     * @var        string
     */
    protected $adir_name;

    /**
     * The value for the adir_surname field.
     * @var        string
     */
    protected $adir_surname;

    /**
     * The value for the pres_ge_name field.
     * @var        string
     */
    protected $pres_ge_name;

    /**
     * The value for the pres_ge_surname field.
     * @var        string
     */
    protected $pres_ge_surname;

    /**
     * The value for the seg_cons_name field.
     * @var        string
     */
    protected $seg_cons_name;

    /**
     * The value for the seg_cons_surname field.
     * @var        string
     */
    protected $seg_cons_surname;

    /**
     * The value for the pres_con_name field.
     * @var        string
     */
    protected $pres_con_name;

    /**
     * The value for the pres_con_surname field.
     * @var        string
     */
    protected $pres_con_surname;

    /**
     * The value for the dir_fiscal_code field.
     * @var        string
     */
    protected $dir_fiscal_code;

    /**
     * The value for the school_fiscal_code field.
     * @var        string
     */
    protected $school_fiscal_code;

    /**
     * The value for the inpdap_code field.
     * @var        string
     */
    protected $inpdap_code;

    /**
     * The value for the assicurazioni_sanitarie field.
     * @var        string
     */
    protected $assicurazioni_sanitarie;

    /**
     * The value for the dir_sesso field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $dir_sesso;

    /**
     * The value for the dir_birth field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $dir_birth;

    /**
     * The value for the dir_city field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $dir_city;

    /**
     * The value for the postal_account field.
     * @var        string
     */
    protected $postal_account;

    /**
     * The value for the ateco_code field.
     * @var        string
     */
    protected $ateco_code;

    /**
     * The value for the activity_code field.
     * @var        string
     */
    protected $activity_code;

    /**
     * The value for the dir_curr_addr field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $dir_curr_addr;

    /**
     * The value for the dir_curr_city field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $dir_curr_city;

    /**
     * The value for the dir_curr_phone field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $dir_curr_phone;

    /**
     * The value for the dir_emp_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $dir_emp_id;

    /**
     * The value for the adir_emp_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $adir_emp_id;

    /**
     * The value for the presge_emp_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $presge_emp_id;

    /**
     * The value for the segcons_emp_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $segcons_emp_id;

    /**
     * The value for the prescon_emp_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $prescon_emp_id;

    /**
     * The value for the respacq_emp_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $respacq_emp_id;

    /**
     * The value for the job_director_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_director_id;

    /**
     * The value for the job_vice_director_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_vice_director_id;

    /**
     * The value for the job_dsga_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_dsga_id;

    /**
     * The value for the job_personnel_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_personnel_id;

    /**
     * The value for the job_accounting_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_accounting_id;

    /**
     * The value for the job_warehouse_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_warehouse_id;

    /**
     * The value for the job_registry_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $job_registry_id;

    /**
     * @var        Contact
     */
    protected $aContact;

    /**
     * @var        Employee
     */
    protected $aEmployee;

    /**
     * @var        PropelObjectCollection|InstituteBankProfile[] Collection to store aggregation of InstituteBankProfile objects.
     */
    protected $collInstituteBankProfiles;
    protected $collInstituteBankProfilesPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $instituteBankProfilesScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->def = false;
        $this->dir_sesso = '';
        $this->dir_birth = '0';
        $this->dir_city = '';
        $this->dir_curr_addr = '';
        $this->dir_curr_city = '';
        $this->dir_curr_phone = '';
        $this->dir_emp_id = 0;
        $this->adir_emp_id = 0;
        $this->presge_emp_id = 0;
        $this->segcons_emp_id = 0;
        $this->prescon_emp_id = 0;
        $this->respacq_emp_id = 0;
        $this->job_director_id = 0;
        $this->job_vice_director_id = 0;
        $this->job_dsga_id = 0;
        $this->job_personnel_id = 0;
        $this->job_accounting_id = 0;
        $this->job_warehouse_id = 0;
        $this->job_registry_id = 0;
    }

    /**
     * Initializes internal state of BaseInstitute object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [institute_id] column value.
     *
     * @return int
     */
    public function getInstituteId()
    {

        return $this->institute_id;
    }

    /**
     * Get the [name] column value.
     *
     * @return string
     */
    public function getName()
    {

        return $this->name;
    }

    /**
     * Get the [mechan_code] column value.
     *
     * @return string
     */
    public function getMechanCode()
    {

        return $this->mechan_code;
    }

    /**
     * Get the [contact_id] column value.
     *
     * @return int
     */
    public function getContactId()
    {

        return $this->contact_id;
    }

    /**
     * Get the [fiscal_code] column value.
     *
     * @return string
     */
    public function getFiscalCode()
    {

        return $this->fiscal_code;
    }

    /**
     * Get the [school_type] column value.
     *
     * @return string
     */
    public function getSchoolType()
    {

        return $this->school_type;
    }

    /**
     * Get the [parent] column value.
     *
     * @return int
     */
    public function getParent()
    {

        return $this->parent;
    }

    /**
     * Get the [def] column value.
     *
     * @return boolean
     */
    public function getDef()
    {

        return $this->def;
    }

    /**
     * Get the [dir_name] column value.
     *
     * @return string
     */
    public function getDirName()
    {

        return $this->dir_name;
    }

    /**
     * Get the [dir_surname] column value.
     *
     * @return string
     */
    public function getDirSurname()
    {

        return $this->dir_surname;
    }

    /**
     * Get the [adir_name] column value.
     *
     * @return string
     */
    public function getAdirName()
    {

        return $this->adir_name;
    }

    /**
     * Get the [adir_surname] column value.
     *
     * @return string
     */
    public function getAdirSurname()
    {

        return $this->adir_surname;
    }

    /**
     * Get the [pres_ge_name] column value.
     *
     * @return string
     */
    public function getPresGeName()
    {

        return $this->pres_ge_name;
    }

    /**
     * Get the [pres_ge_surname] column value.
     *
     * @return string
     */
    public function getPresGeSurname()
    {

        return $this->pres_ge_surname;
    }

    /**
     * Get the [seg_cons_name] column value.
     *
     * @return string
     */
    public function getSegConsName()
    {

        return $this->seg_cons_name;
    }

    /**
     * Get the [seg_cons_surname] column value.
     *
     * @return string
     */
    public function getSegConsSurname()
    {

        return $this->seg_cons_surname;
    }

    /**
     * Get the [pres_con_name] column value.
     *
     * @return string
     */
    public function getPresConName()
    {

        return $this->pres_con_name;
    }

    /**
     * Get the [pres_con_surname] column value.
     *
     * @return string
     */
    public function getPresConSurname()
    {

        return $this->pres_con_surname;
    }

    /**
     * Get the [dir_fiscal_code] column value.
     *
     * @return string
     */
    public function getDirFiscalCode()
    {

        return $this->dir_fiscal_code;
    }

    /**
     * Get the [school_fiscal_code] column value.
     *
     * @return string
     */
    public function getSchoolFiscalCode()
    {

        return $this->school_fiscal_code;
    }

    /**
     * Get the [inpdap_code] column value.
     *
     * @return string
     */
    public function getInpdapCode()
    {

        return $this->inpdap_code;
    }

    /**
     * Get the [assicurazioni_sanitarie] column value.
     *
     * @return string
     */
    public function getAssicurazioniSanitarie()
    {

        return $this->assicurazioni_sanitarie;
    }

    /**
     * Get the [dir_sesso] column value.
     *
     * @return string
     */
    public function getDirSesso()
    {

        return $this->dir_sesso;
    }

    /**
     * Get the [dir_birth] column value.
     *
     * @return string
     */
    public function getDirBirth()
    {

        return $this->dir_birth;
    }

    /**
     * Get the [dir_city] column value.
     *
     * @return string
     */
    public function getDirCity()
    {

        return $this->dir_city;
    }

    /**
     * Get the [postal_account] column value.
     *
     * @return string
     */
    public function getPostalAccount()
    {

        return $this->postal_account;
    }

    /**
     * Get the [ateco_code] column value.
     *
     * @return string
     */
    public function getAtecoCode()
    {

        return $this->ateco_code;
    }

    /**
     * Get the [activity_code] column value.
     *
     * @return string
     */
    public function getActivityCode()
    {

        return $this->activity_code;
    }

    /**
     * Get the [dir_curr_addr] column value.
     *
     * @return string
     */
    public function getDirCurrAddr()
    {

        return $this->dir_curr_addr;
    }

    /**
     * Get the [dir_curr_city] column value.
     *
     * @return string
     */
    public function getDirCurrCity()
    {

        return $this->dir_curr_city;
    }

    /**
     * Get the [dir_curr_phone] column value.
     *
     * @return string
     */
    public function getDirCurrPhone()
    {

        return $this->dir_curr_phone;
    }

    /**
     * Get the [dir_emp_id] column value.
     *
     * @return int
     */
    public function getDirEmpId()
    {

        return $this->dir_emp_id;
    }

    /**
     * Get the [adir_emp_id] column value.
     *
     * @return int
     */
    public function getAdirEmpId()
    {

        return $this->adir_emp_id;
    }

    /**
     * Get the [presge_emp_id] column value.
     *
     * @return int
     */
    public function getPresgeEmpId()
    {

        return $this->presge_emp_id;
    }

    /**
     * Get the [segcons_emp_id] column value.
     *
     * @return int
     */
    public function getSegconsEmpId()
    {

        return $this->segcons_emp_id;
    }

    /**
     * Get the [prescon_emp_id] column value.
     *
     * @return int
     */
    public function getPresconEmpId()
    {

        return $this->prescon_emp_id;
    }

    /**
     * Get the [respacq_emp_id] column value.
     *
     * @return int
     */
    public function getRespacqEmpId()
    {

        return $this->respacq_emp_id;
    }

    /**
     * Get the [job_director_id] column value.
     *
     * @return int
     */
    public function getJobDirectorId()
    {

        return $this->job_director_id;
    }

    /**
     * Get the [job_vice_director_id] column value.
     *
     * @return int
     */
    public function getJobViceDirectorId()
    {

        return $this->job_vice_director_id;
    }

    /**
     * Get the [job_dsga_id] column value.
     *
     * @return int
     */
    public function getJobDSGAId()
    {

        return $this->job_dsga_id;
    }

    /**
     * Get the [job_personnel_id] column value.
     *
     * @return int
     */
    public function getJobPersonnelId()
    {

        return $this->job_personnel_id;
    }

    /**
     * Get the [job_accounting_id] column value.
     *
     * @return int
     */
    public function getJobAccountingId()
    {

        return $this->job_accounting_id;
    }

    /**
     * Get the [job_warehouse_id] column value.
     *
     * @return int
     */
    public function getJobWarehouseId()
    {

        return $this->job_warehouse_id;
    }

    /**
     * Get the [job_registry_id] column value.
     *
     * @return int
     */
    public function getJobRegistryId()
    {

        return $this->job_registry_id;
    }

    /**
     * Set the value of [institute_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setInstituteId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->institute_id !== $v) {
            $this->institute_id = $v;
            $this->modifiedColumns[] = InstitutePeer::INSTITUTE_ID;
        }


        return $this;
    } // setInstituteId()

    /**
     * Set the value of [name] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->name !== $v) {
            $this->name = $v;
            $this->modifiedColumns[] = InstitutePeer::NAME;
        }


        return $this;
    } // setName()

    /**
     * Set the value of [mechan_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setMechanCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->mechan_code !== $v) {
            $this->mechan_code = $v;
            $this->modifiedColumns[] = InstitutePeer::MECHAN_CODE;
        }


        return $this;
    } // setMechanCode()

    /**
     * Set the value of [contact_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setContactId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->contact_id !== $v) {
            $this->contact_id = $v;
            $this->modifiedColumns[] = InstitutePeer::CONTACT_ID;
        }

        if ($this->aContact !== null && $this->aContact->getContactId() !== $v) {
            $this->aContact = null;
        }


        return $this;
    } // setContactId()

    /**
     * Set the value of [fiscal_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setFiscalCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->fiscal_code !== $v) {
            $this->fiscal_code = $v;
            $this->modifiedColumns[] = InstitutePeer::FISCAL_CODE;
        }


        return $this;
    } // setFiscalCode()

    /**
     * Set the value of [school_type] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setSchoolType($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->school_type !== $v) {
            $this->school_type = $v;
            $this->modifiedColumns[] = InstitutePeer::SCHOOL_TYPE;
        }


        return $this;
    } // setSchoolType()

    /**
     * Set the value of [parent] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setParent($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->parent !== $v) {
            $this->parent = $v;
            $this->modifiedColumns[] = InstitutePeer::PARENT;
        }


        return $this;
    } // setParent()

    /**
     * Sets the value of the [def] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDef($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->def !== $v) {
            $this->def = $v;
            $this->modifiedColumns[] = InstitutePeer::DEF;
        }


        return $this;
    } // setDef()

    /**
     * Set the value of [dir_name] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_name !== $v) {
            $this->dir_name = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_NAME;
        }


        return $this;
    } // setDirName()

    /**
     * Set the value of [dir_surname] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_surname !== $v) {
            $this->dir_surname = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_SURNAME;
        }


        return $this;
    } // setDirSurname()

    /**
     * Set the value of [adir_name] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setAdirName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->adir_name !== $v) {
            $this->adir_name = $v;
            $this->modifiedColumns[] = InstitutePeer::ADIR_NAME;
        }


        return $this;
    } // setAdirName()

    /**
     * Set the value of [adir_surname] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setAdirSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->adir_surname !== $v) {
            $this->adir_surname = $v;
            $this->modifiedColumns[] = InstitutePeer::ADIR_SURNAME;
        }


        return $this;
    } // setAdirSurname()

    /**
     * Set the value of [pres_ge_name] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPresGeName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pres_ge_name !== $v) {
            $this->pres_ge_name = $v;
            $this->modifiedColumns[] = InstitutePeer::PRES_GE_NAME;
        }


        return $this;
    } // setPresGeName()

    /**
     * Set the value of [pres_ge_surname] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPresGeSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pres_ge_surname !== $v) {
            $this->pres_ge_surname = $v;
            $this->modifiedColumns[] = InstitutePeer::PRES_GE_SURNAME;
        }


        return $this;
    } // setPresGeSurname()

    /**
     * Set the value of [seg_cons_name] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setSegConsName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->seg_cons_name !== $v) {
            $this->seg_cons_name = $v;
            $this->modifiedColumns[] = InstitutePeer::SEG_CONS_NAME;
        }


        return $this;
    } // setSegConsName()

    /**
     * Set the value of [seg_cons_surname] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setSegConsSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->seg_cons_surname !== $v) {
            $this->seg_cons_surname = $v;
            $this->modifiedColumns[] = InstitutePeer::SEG_CONS_SURNAME;
        }


        return $this;
    } // setSegConsSurname()

    /**
     * Set the value of [pres_con_name] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPresConName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pres_con_name !== $v) {
            $this->pres_con_name = $v;
            $this->modifiedColumns[] = InstitutePeer::PRES_CON_NAME;
        }


        return $this;
    } // setPresConName()

    /**
     * Set the value of [pres_con_surname] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPresConSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pres_con_surname !== $v) {
            $this->pres_con_surname = $v;
            $this->modifiedColumns[] = InstitutePeer::PRES_CON_SURNAME;
        }


        return $this;
    } // setPresConSurname()

    /**
     * Set the value of [dir_fiscal_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirFiscalCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_fiscal_code !== $v) {
            $this->dir_fiscal_code = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_FISCAL_CODE;
        }


        return $this;
    } // setDirFiscalCode()

    /**
     * Set the value of [school_fiscal_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setSchoolFiscalCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->school_fiscal_code !== $v) {
            $this->school_fiscal_code = $v;
            $this->modifiedColumns[] = InstitutePeer::SCHOOL_FISCAL_CODE;
        }


        return $this;
    } // setSchoolFiscalCode()

    /**
     * Set the value of [inpdap_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setInpdapCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->inpdap_code !== $v) {
            $this->inpdap_code = $v;
            $this->modifiedColumns[] = InstitutePeer::INPDAP_CODE;
        }


        return $this;
    } // setInpdapCode()

    /**
     * Set the value of [assicurazioni_sanitarie] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setAssicurazioniSanitarie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->assicurazioni_sanitarie !== $v) {
            $this->assicurazioni_sanitarie = $v;
            $this->modifiedColumns[] = InstitutePeer::ASSICURAZIONI_SANITARIE;
        }


        return $this;
    } // setAssicurazioniSanitarie()

    /**
     * Set the value of [dir_sesso] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirSesso($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_sesso !== $v) {
            $this->dir_sesso = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_SESSO;
        }


        return $this;
    } // setDirSesso()

    /**
     * Set the value of [dir_birth] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirBirth($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_birth !== $v) {
            $this->dir_birth = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_BIRTH;
        }


        return $this;
    } // setDirBirth()

    /**
     * Set the value of [dir_city] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirCity($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_city !== $v) {
            $this->dir_city = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_CITY;
        }


        return $this;
    } // setDirCity()

    /**
     * Set the value of [postal_account] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPostalAccount($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->postal_account !== $v) {
            $this->postal_account = $v;
            $this->modifiedColumns[] = InstitutePeer::POSTAL_ACCOUNT;
        }


        return $this;
    } // setPostalAccount()

    /**
     * Set the value of [ateco_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setAtecoCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ateco_code !== $v) {
            $this->ateco_code = $v;
            $this->modifiedColumns[] = InstitutePeer::ATECO_CODE;
        }


        return $this;
    } // setAtecoCode()

    /**
     * Set the value of [activity_code] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setActivityCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->activity_code !== $v) {
            $this->activity_code = $v;
            $this->modifiedColumns[] = InstitutePeer::ACTIVITY_CODE;
        }


        return $this;
    } // setActivityCode()

    /**
     * Set the value of [dir_curr_addr] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirCurrAddr($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_curr_addr !== $v) {
            $this->dir_curr_addr = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_CURR_ADDR;
        }


        return $this;
    } // setDirCurrAddr()

    /**
     * Set the value of [dir_curr_city] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirCurrCity($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_curr_city !== $v) {
            $this->dir_curr_city = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_CURR_CITY;
        }


        return $this;
    } // setDirCurrCity()

    /**
     * Set the value of [dir_curr_phone] column.
     *
     * @param  string $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirCurrPhone($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dir_curr_phone !== $v) {
            $this->dir_curr_phone = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_CURR_PHONE;
        }


        return $this;
    } // setDirCurrPhone()

    /**
     * Set the value of [dir_emp_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setDirEmpId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->dir_emp_id !== $v) {
            $this->dir_emp_id = $v;
            $this->modifiedColumns[] = InstitutePeer::DIR_EMP_ID;
        }

        if ($this->aEmployee !== null && $this->aEmployee->getEmployeeId() !== $v) {
            $this->aEmployee = null;
        }


        return $this;
    } // setDirEmpId()

    /**
     * Set the value of [adir_emp_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setAdirEmpId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->adir_emp_id !== $v) {
            $this->adir_emp_id = $v;
            $this->modifiedColumns[] = InstitutePeer::ADIR_EMP_ID;
        }


        return $this;
    } // setAdirEmpId()

    /**
     * Set the value of [presge_emp_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPresgeEmpId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->presge_emp_id !== $v) {
            $this->presge_emp_id = $v;
            $this->modifiedColumns[] = InstitutePeer::PRESGE_EMP_ID;
        }


        return $this;
    } // setPresgeEmpId()

    /**
     * Set the value of [segcons_emp_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setSegconsEmpId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->segcons_emp_id !== $v) {
            $this->segcons_emp_id = $v;
            $this->modifiedColumns[] = InstitutePeer::SEGCONS_EMP_ID;
        }


        return $this;
    } // setSegconsEmpId()

    /**
     * Set the value of [prescon_emp_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setPresconEmpId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->prescon_emp_id !== $v) {
            $this->prescon_emp_id = $v;
            $this->modifiedColumns[] = InstitutePeer::PRESCON_EMP_ID;
        }


        return $this;
    } // setPresconEmpId()

    /**
     * Set the value of [respacq_emp_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setRespacqEmpId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->respacq_emp_id !== $v) {
            $this->respacq_emp_id = $v;
            $this->modifiedColumns[] = InstitutePeer::RESPACQ_EMP_ID;
        }


        return $this;
    } // setRespacqEmpId()

    /**
     * Set the value of [job_director_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobDirectorId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_director_id !== $v) {
            $this->job_director_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_DIRECTOR_ID;
        }


        return $this;
    } // setJobDirectorId()

    /**
     * Set the value of [job_vice_director_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobViceDirectorId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_vice_director_id !== $v) {
            $this->job_vice_director_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_VICE_DIRECTOR_ID;
        }


        return $this;
    } // setJobViceDirectorId()

    /**
     * Set the value of [job_dsga_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobDSGAId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_dsga_id !== $v) {
            $this->job_dsga_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_DSGA_ID;
        }


        return $this;
    } // setJobDSGAId()

    /**
     * Set the value of [job_personnel_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobPersonnelId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_personnel_id !== $v) {
            $this->job_personnel_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_PERSONNEL_ID;
        }


        return $this;
    } // setJobPersonnelId()

    /**
     * Set the value of [job_accounting_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobAccountingId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_accounting_id !== $v) {
            $this->job_accounting_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_ACCOUNTING_ID;
        }


        return $this;
    } // setJobAccountingId()

    /**
     * Set the value of [job_warehouse_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobWarehouseId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_warehouse_id !== $v) {
            $this->job_warehouse_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_WAREHOUSE_ID;
        }


        return $this;
    } // setJobWarehouseId()

    /**
     * Set the value of [job_registry_id] column.
     *
     * @param  int $v new value
     * @return Institute The current object (for fluent API support)
     */
    public function setJobRegistryId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->job_registry_id !== $v) {
            $this->job_registry_id = $v;
            $this->modifiedColumns[] = InstitutePeer::JOB_REGISTRY_ID;
        }


        return $this;
    } // setJobRegistryId()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->def !== false) {
                return false;
            }

            if ($this->dir_sesso !== '') {
                return false;
            }

            if ($this->dir_birth !== '0') {
                return false;
            }

            if ($this->dir_city !== '') {
                return false;
            }

            if ($this->dir_curr_addr !== '') {
                return false;
            }

            if ($this->dir_curr_city !== '') {
                return false;
            }

            if ($this->dir_curr_phone !== '') {
                return false;
            }

            if ($this->dir_emp_id !== 0) {
                return false;
            }

            if ($this->adir_emp_id !== 0) {
                return false;
            }

            if ($this->presge_emp_id !== 0) {
                return false;
            }

            if ($this->segcons_emp_id !== 0) {
                return false;
            }

            if ($this->prescon_emp_id !== 0) {
                return false;
            }

            if ($this->respacq_emp_id !== 0) {
                return false;
            }

            if ($this->job_director_id !== 0) {
                return false;
            }

            if ($this->job_vice_director_id !== 0) {
                return false;
            }

            if ($this->job_dsga_id !== 0) {
                return false;
            }

            if ($this->job_personnel_id !== 0) {
                return false;
            }

            if ($this->job_accounting_id !== 0) {
                return false;
            }

            if ($this->job_warehouse_id !== 0) {
                return false;
            }

            if ($this->job_registry_id !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->institute_id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->name = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->mechan_code = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->contact_id = ($row[$startcol + 3] !== null) ? (int) $row[$startcol + 3] : null;
            $this->fiscal_code = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->school_type = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->parent = ($row[$startcol + 6] !== null) ? (int) $row[$startcol + 6] : null;
            $this->def = ($row[$startcol + 7] !== null) ? (boolean) $row[$startcol + 7] : null;
            $this->dir_name = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->dir_surname = ($row[$startcol + 9] !== null) ? (string) $row[$startcol + 9] : null;
            $this->adir_name = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->adir_surname = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->pres_ge_name = ($row[$startcol + 12] !== null) ? (string) $row[$startcol + 12] : null;
            $this->pres_ge_surname = ($row[$startcol + 13] !== null) ? (string) $row[$startcol + 13] : null;
            $this->seg_cons_name = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->seg_cons_surname = ($row[$startcol + 15] !== null) ? (string) $row[$startcol + 15] : null;
            $this->pres_con_name = ($row[$startcol + 16] !== null) ? (string) $row[$startcol + 16] : null;
            $this->pres_con_surname = ($row[$startcol + 17] !== null) ? (string) $row[$startcol + 17] : null;
            $this->dir_fiscal_code = ($row[$startcol + 18] !== null) ? (string) $row[$startcol + 18] : null;
            $this->school_fiscal_code = ($row[$startcol + 19] !== null) ? (string) $row[$startcol + 19] : null;
            $this->inpdap_code = ($row[$startcol + 20] !== null) ? (string) $row[$startcol + 20] : null;
            $this->assicurazioni_sanitarie = ($row[$startcol + 21] !== null) ? (string) $row[$startcol + 21] : null;
            $this->dir_sesso = ($row[$startcol + 22] !== null) ? (string) $row[$startcol + 22] : null;
            $this->dir_birth = ($row[$startcol + 23] !== null) ? (string) $row[$startcol + 23] : null;
            $this->dir_city = ($row[$startcol + 24] !== null) ? (string) $row[$startcol + 24] : null;
            $this->postal_account = ($row[$startcol + 25] !== null) ? (string) $row[$startcol + 25] : null;
            $this->ateco_code = ($row[$startcol + 26] !== null) ? (string) $row[$startcol + 26] : null;
            $this->activity_code = ($row[$startcol + 27] !== null) ? (string) $row[$startcol + 27] : null;
            $this->dir_curr_addr = ($row[$startcol + 28] !== null) ? (string) $row[$startcol + 28] : null;
            $this->dir_curr_city = ($row[$startcol + 29] !== null) ? (string) $row[$startcol + 29] : null;
            $this->dir_curr_phone = ($row[$startcol + 30] !== null) ? (string) $row[$startcol + 30] : null;
            $this->dir_emp_id = ($row[$startcol + 31] !== null) ? (int) $row[$startcol + 31] : null;
            $this->adir_emp_id = ($row[$startcol + 32] !== null) ? (int) $row[$startcol + 32] : null;
            $this->presge_emp_id = ($row[$startcol + 33] !== null) ? (int) $row[$startcol + 33] : null;
            $this->segcons_emp_id = ($row[$startcol + 34] !== null) ? (int) $row[$startcol + 34] : null;
            $this->prescon_emp_id = ($row[$startcol + 35] !== null) ? (int) $row[$startcol + 35] : null;
            $this->respacq_emp_id = ($row[$startcol + 36] !== null) ? (int) $row[$startcol + 36] : null;
            $this->job_director_id = ($row[$startcol + 37] !== null) ? (int) $row[$startcol + 37] : null;
            $this->job_vice_director_id = ($row[$startcol + 38] !== null) ? (int) $row[$startcol + 38] : null;
            $this->job_dsga_id = ($row[$startcol + 39] !== null) ? (int) $row[$startcol + 39] : null;
            $this->job_personnel_id = ($row[$startcol + 40] !== null) ? (int) $row[$startcol + 40] : null;
            $this->job_accounting_id = ($row[$startcol + 41] !== null) ? (int) $row[$startcol + 41] : null;
            $this->job_warehouse_id = ($row[$startcol + 42] !== null) ? (int) $row[$startcol + 42] : null;
            $this->job_registry_id = ($row[$startcol + 43] !== null) ? (int) $row[$startcol + 43] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 44; // 44 = InstitutePeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Institute object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aContact !== null && $this->contact_id !== $this->aContact->getContactId()) {
            $this->aContact = null;
        }
        if ($this->aEmployee !== null && $this->dir_emp_id !== $this->aEmployee->getEmployeeId()) {
            $this->aEmployee = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = InstitutePeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aContact = null;
            $this->aEmployee = null;
            $this->collInstituteBankProfiles = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = InstituteQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(InstitutePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                InstitutePeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aContact !== null) {
                if ($this->aContact->isModified() || $this->aContact->isNew()) {
                    $affectedRows += $this->aContact->save($con);
                }
                $this->setContact($this->aContact);
            }

            if ($this->aEmployee !== null) {
                if ($this->aEmployee->isModified() || $this->aEmployee->isNew()) {
                    $affectedRows += $this->aEmployee->save($con);
                }
                $this->setEmployee($this->aEmployee);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->instituteBankProfilesScheduledForDeletion !== null) {
                if (!$this->instituteBankProfilesScheduledForDeletion->isEmpty()) {
                    InstituteBankProfileQuery::create()
                        ->filterByPrimaryKeys($this->instituteBankProfilesScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->instituteBankProfilesScheduledForDeletion = null;
                }
            }

            if ($this->collInstituteBankProfiles !== null) {
                foreach ($this->collInstituteBankProfiles as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = InstitutePeer::INSTITUTE_ID;
        if (null !== $this->institute_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . InstitutePeer::INSTITUTE_ID . ')');
        }
        if (null === $this->institute_id) {
            try {
                $stmt = $con->query("SELECT nextval('institute_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->institute_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(InstitutePeer::INSTITUTE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"institute_id"';
        }
        if ($this->isColumnModified(InstitutePeer::NAME)) {
            $modifiedColumns[':p' . $index++]  = '"name"';
        }
        if ($this->isColumnModified(InstitutePeer::MECHAN_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"mechan_code"';
        }
        if ($this->isColumnModified(InstitutePeer::CONTACT_ID)) {
            $modifiedColumns[':p' . $index++]  = '"contact_id"';
        }
        if ($this->isColumnModified(InstitutePeer::FISCAL_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"fiscal_code"';
        }
        if ($this->isColumnModified(InstitutePeer::SCHOOL_TYPE)) {
            $modifiedColumns[':p' . $index++]  = '"school_type"';
        }
        if ($this->isColumnModified(InstitutePeer::PARENT)) {
            $modifiedColumns[':p' . $index++]  = '"parent"';
        }
        if ($this->isColumnModified(InstitutePeer::DEF)) {
            $modifiedColumns[':p' . $index++]  = '"def"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"dir_name"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"dir_surname"';
        }
        if ($this->isColumnModified(InstitutePeer::ADIR_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"adir_name"';
        }
        if ($this->isColumnModified(InstitutePeer::ADIR_SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"adir_surname"';
        }
        if ($this->isColumnModified(InstitutePeer::PRES_GE_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"pres_ge_name"';
        }
        if ($this->isColumnModified(InstitutePeer::PRES_GE_SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"pres_ge_surname"';
        }
        if ($this->isColumnModified(InstitutePeer::SEG_CONS_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"seg_cons_name"';
        }
        if ($this->isColumnModified(InstitutePeer::SEG_CONS_SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"seg_cons_surname"';
        }
        if ($this->isColumnModified(InstitutePeer::PRES_CON_NAME)) {
            $modifiedColumns[':p' . $index++]  = '"pres_con_name"';
        }
        if ($this->isColumnModified(InstitutePeer::PRES_CON_SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"pres_con_surname"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_FISCAL_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"dir_fiscal_code"';
        }
        if ($this->isColumnModified(InstitutePeer::SCHOOL_FISCAL_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"school_fiscal_code"';
        }
        if ($this->isColumnModified(InstitutePeer::INPDAP_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"inpdap_code"';
        }
        if ($this->isColumnModified(InstitutePeer::ASSICURAZIONI_SANITARIE)) {
            $modifiedColumns[':p' . $index++]  = '"assicurazioni_sanitarie"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_SESSO)) {
            $modifiedColumns[':p' . $index++]  = '"dir_sesso"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_BIRTH)) {
            $modifiedColumns[':p' . $index++]  = '"dir_birth"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_CITY)) {
            $modifiedColumns[':p' . $index++]  = '"dir_city"';
        }
        if ($this->isColumnModified(InstitutePeer::POSTAL_ACCOUNT)) {
            $modifiedColumns[':p' . $index++]  = '"postal_account"';
        }
        if ($this->isColumnModified(InstitutePeer::ATECO_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"ateco_code"';
        }
        if ($this->isColumnModified(InstitutePeer::ACTIVITY_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"activity_code"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_CURR_ADDR)) {
            $modifiedColumns[':p' . $index++]  = '"dir_curr_addr"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_CURR_CITY)) {
            $modifiedColumns[':p' . $index++]  = '"dir_curr_city"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_CURR_PHONE)) {
            $modifiedColumns[':p' . $index++]  = '"dir_curr_phone"';
        }
        if ($this->isColumnModified(InstitutePeer::DIR_EMP_ID)) {
            $modifiedColumns[':p' . $index++]  = '"dir_emp_id"';
        }
        if ($this->isColumnModified(InstitutePeer::ADIR_EMP_ID)) {
            $modifiedColumns[':p' . $index++]  = '"adir_emp_id"';
        }
        if ($this->isColumnModified(InstitutePeer::PRESGE_EMP_ID)) {
            $modifiedColumns[':p' . $index++]  = '"presge_emp_id"';
        }
        if ($this->isColumnModified(InstitutePeer::SEGCONS_EMP_ID)) {
            $modifiedColumns[':p' . $index++]  = '"segcons_emp_id"';
        }
        if ($this->isColumnModified(InstitutePeer::PRESCON_EMP_ID)) {
            $modifiedColumns[':p' . $index++]  = '"prescon_emp_id"';
        }
        if ($this->isColumnModified(InstitutePeer::RESPACQ_EMP_ID)) {
            $modifiedColumns[':p' . $index++]  = '"respacq_emp_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_DIRECTOR_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_director_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_VICE_DIRECTOR_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_vice_director_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_DSGA_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_dsga_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_PERSONNEL_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_personnel_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_ACCOUNTING_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_accounting_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_WAREHOUSE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_warehouse_id"';
        }
        if ($this->isColumnModified(InstitutePeer::JOB_REGISTRY_ID)) {
            $modifiedColumns[':p' . $index++]  = '"job_registry_id"';
        }

        $sql = sprintf(
            'INSERT INTO "institute" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"institute_id"':
                        $stmt->bindValue($identifier, $this->institute_id, PDO::PARAM_INT);
                        break;
                    case '"name"':
                        $stmt->bindValue($identifier, $this->name, PDO::PARAM_STR);
                        break;
                    case '"mechan_code"':
                        $stmt->bindValue($identifier, $this->mechan_code, PDO::PARAM_STR);
                        break;
                    case '"contact_id"':
                        $stmt->bindValue($identifier, $this->contact_id, PDO::PARAM_INT);
                        break;
                    case '"fiscal_code"':
                        $stmt->bindValue($identifier, $this->fiscal_code, PDO::PARAM_STR);
                        break;
                    case '"school_type"':
                        $stmt->bindValue($identifier, $this->school_type, PDO::PARAM_STR);
                        break;
                    case '"parent"':
                        $stmt->bindValue($identifier, $this->parent, PDO::PARAM_INT);
                        break;
                    case '"def"':
                        $stmt->bindValue($identifier, $this->def, PDO::PARAM_BOOL);
                        break;
                    case '"dir_name"':
                        $stmt->bindValue($identifier, $this->dir_name, PDO::PARAM_STR);
                        break;
                    case '"dir_surname"':
                        $stmt->bindValue($identifier, $this->dir_surname, PDO::PARAM_STR);
                        break;
                    case '"adir_name"':
                        $stmt->bindValue($identifier, $this->adir_name, PDO::PARAM_STR);
                        break;
                    case '"adir_surname"':
                        $stmt->bindValue($identifier, $this->adir_surname, PDO::PARAM_STR);
                        break;
                    case '"pres_ge_name"':
                        $stmt->bindValue($identifier, $this->pres_ge_name, PDO::PARAM_STR);
                        break;
                    case '"pres_ge_surname"':
                        $stmt->bindValue($identifier, $this->pres_ge_surname, PDO::PARAM_STR);
                        break;
                    case '"seg_cons_name"':
                        $stmt->bindValue($identifier, $this->seg_cons_name, PDO::PARAM_STR);
                        break;
                    case '"seg_cons_surname"':
                        $stmt->bindValue($identifier, $this->seg_cons_surname, PDO::PARAM_STR);
                        break;
                    case '"pres_con_name"':
                        $stmt->bindValue($identifier, $this->pres_con_name, PDO::PARAM_STR);
                        break;
                    case '"pres_con_surname"':
                        $stmt->bindValue($identifier, $this->pres_con_surname, PDO::PARAM_STR);
                        break;
                    case '"dir_fiscal_code"':
                        $stmt->bindValue($identifier, $this->dir_fiscal_code, PDO::PARAM_STR);
                        break;
                    case '"school_fiscal_code"':
                        $stmt->bindValue($identifier, $this->school_fiscal_code, PDO::PARAM_STR);
                        break;
                    case '"inpdap_code"':
                        $stmt->bindValue($identifier, $this->inpdap_code, PDO::PARAM_STR);
                        break;
                    case '"assicurazioni_sanitarie"':
                        $stmt->bindValue($identifier, $this->assicurazioni_sanitarie, PDO::PARAM_STR);
                        break;
                    case '"dir_sesso"':
                        $stmt->bindValue($identifier, $this->dir_sesso, PDO::PARAM_STR);
                        break;
                    case '"dir_birth"':
                        $stmt->bindValue($identifier, $this->dir_birth, PDO::PARAM_STR);
                        break;
                    case '"dir_city"':
                        $stmt->bindValue($identifier, $this->dir_city, PDO::PARAM_STR);
                        break;
                    case '"postal_account"':
                        $stmt->bindValue($identifier, $this->postal_account, PDO::PARAM_STR);
                        break;
                    case '"ateco_code"':
                        $stmt->bindValue($identifier, $this->ateco_code, PDO::PARAM_STR);
                        break;
                    case '"activity_code"':
                        $stmt->bindValue($identifier, $this->activity_code, PDO::PARAM_STR);
                        break;
                    case '"dir_curr_addr"':
                        $stmt->bindValue($identifier, $this->dir_curr_addr, PDO::PARAM_STR);
                        break;
                    case '"dir_curr_city"':
                        $stmt->bindValue($identifier, $this->dir_curr_city, PDO::PARAM_STR);
                        break;
                    case '"dir_curr_phone"':
                        $stmt->bindValue($identifier, $this->dir_curr_phone, PDO::PARAM_STR);
                        break;
                    case '"dir_emp_id"':
                        $stmt->bindValue($identifier, $this->dir_emp_id, PDO::PARAM_INT);
                        break;
                    case '"adir_emp_id"':
                        $stmt->bindValue($identifier, $this->adir_emp_id, PDO::PARAM_INT);
                        break;
                    case '"presge_emp_id"':
                        $stmt->bindValue($identifier, $this->presge_emp_id, PDO::PARAM_INT);
                        break;
                    case '"segcons_emp_id"':
                        $stmt->bindValue($identifier, $this->segcons_emp_id, PDO::PARAM_INT);
                        break;
                    case '"prescon_emp_id"':
                        $stmt->bindValue($identifier, $this->prescon_emp_id, PDO::PARAM_INT);
                        break;
                    case '"respacq_emp_id"':
                        $stmt->bindValue($identifier, $this->respacq_emp_id, PDO::PARAM_INT);
                        break;
                    case '"job_director_id"':
                        $stmt->bindValue($identifier, $this->job_director_id, PDO::PARAM_INT);
                        break;
                    case '"job_vice_director_id"':
                        $stmt->bindValue($identifier, $this->job_vice_director_id, PDO::PARAM_INT);
                        break;
                    case '"job_dsga_id"':
                        $stmt->bindValue($identifier, $this->job_dsga_id, PDO::PARAM_INT);
                        break;
                    case '"job_personnel_id"':
                        $stmt->bindValue($identifier, $this->job_personnel_id, PDO::PARAM_INT);
                        break;
                    case '"job_accounting_id"':
                        $stmt->bindValue($identifier, $this->job_accounting_id, PDO::PARAM_INT);
                        break;
                    case '"job_warehouse_id"':
                        $stmt->bindValue($identifier, $this->job_warehouse_id, PDO::PARAM_INT);
                        break;
                    case '"job_registry_id"':
                        $stmt->bindValue($identifier, $this->job_registry_id, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aContact !== null) {
                if (!$this->aContact->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aContact->getValidationFailures());
                }
            }

            if ($this->aEmployee !== null) {
                if (!$this->aEmployee->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aEmployee->getValidationFailures());
                }
            }


            if (($retval = InstitutePeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collInstituteBankProfiles !== null) {
                    foreach ($this->collInstituteBankProfiles as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = InstitutePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getInstituteId();
                break;
            case 1:
                return $this->getName();
                break;
            case 2:
                return $this->getMechanCode();
                break;
            case 3:
                return $this->getContactId();
                break;
            case 4:
                return $this->getFiscalCode();
                break;
            case 5:
                return $this->getSchoolType();
                break;
            case 6:
                return $this->getParent();
                break;
            case 7:
                return $this->getDef();
                break;
            case 8:
                return $this->getDirName();
                break;
            case 9:
                return $this->getDirSurname();
                break;
            case 10:
                return $this->getAdirName();
                break;
            case 11:
                return $this->getAdirSurname();
                break;
            case 12:
                return $this->getPresGeName();
                break;
            case 13:
                return $this->getPresGeSurname();
                break;
            case 14:
                return $this->getSegConsName();
                break;
            case 15:
                return $this->getSegConsSurname();
                break;
            case 16:
                return $this->getPresConName();
                break;
            case 17:
                return $this->getPresConSurname();
                break;
            case 18:
                return $this->getDirFiscalCode();
                break;
            case 19:
                return $this->getSchoolFiscalCode();
                break;
            case 20:
                return $this->getInpdapCode();
                break;
            case 21:
                return $this->getAssicurazioniSanitarie();
                break;
            case 22:
                return $this->getDirSesso();
                break;
            case 23:
                return $this->getDirBirth();
                break;
            case 24:
                return $this->getDirCity();
                break;
            case 25:
                return $this->getPostalAccount();
                break;
            case 26:
                return $this->getAtecoCode();
                break;
            case 27:
                return $this->getActivityCode();
                break;
            case 28:
                return $this->getDirCurrAddr();
                break;
            case 29:
                return $this->getDirCurrCity();
                break;
            case 30:
                return $this->getDirCurrPhone();
                break;
            case 31:
                return $this->getDirEmpId();
                break;
            case 32:
                return $this->getAdirEmpId();
                break;
            case 33:
                return $this->getPresgeEmpId();
                break;
            case 34:
                return $this->getSegconsEmpId();
                break;
            case 35:
                return $this->getPresconEmpId();
                break;
            case 36:
                return $this->getRespacqEmpId();
                break;
            case 37:
                return $this->getJobDirectorId();
                break;
            case 38:
                return $this->getJobViceDirectorId();
                break;
            case 39:
                return $this->getJobDSGAId();
                break;
            case 40:
                return $this->getJobPersonnelId();
                break;
            case 41:
                return $this->getJobAccountingId();
                break;
            case 42:
                return $this->getJobWarehouseId();
                break;
            case 43:
                return $this->getJobRegistryId();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Institute'][serialize($this->getPrimaryKey())])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Institute'][serialize($this->getPrimaryKey())] = true;
        $keys = InstitutePeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getInstituteId(),
            $keys[1] => $this->getName(),
            $keys[2] => $this->getMechanCode(),
            $keys[3] => $this->getContactId(),
            $keys[4] => $this->getFiscalCode(),
            $keys[5] => $this->getSchoolType(),
            $keys[6] => $this->getParent(),
            $keys[7] => $this->getDef(),
            $keys[8] => $this->getDirName(),
            $keys[9] => $this->getDirSurname(),
            $keys[10] => $this->getAdirName(),
            $keys[11] => $this->getAdirSurname(),
            $keys[12] => $this->getPresGeName(),
            $keys[13] => $this->getPresGeSurname(),
            $keys[14] => $this->getSegConsName(),
            $keys[15] => $this->getSegConsSurname(),
            $keys[16] => $this->getPresConName(),
            $keys[17] => $this->getPresConSurname(),
            $keys[18] => $this->getDirFiscalCode(),
            $keys[19] => $this->getSchoolFiscalCode(),
            $keys[20] => $this->getInpdapCode(),
            $keys[21] => $this->getAssicurazioniSanitarie(),
            $keys[22] => $this->getDirSesso(),
            $keys[23] => $this->getDirBirth(),
            $keys[24] => $this->getDirCity(),
            $keys[25] => $this->getPostalAccount(),
            $keys[26] => $this->getAtecoCode(),
            $keys[27] => $this->getActivityCode(),
            $keys[28] => $this->getDirCurrAddr(),
            $keys[29] => $this->getDirCurrCity(),
            $keys[30] => $this->getDirCurrPhone(),
            $keys[31] => $this->getDirEmpId(),
            $keys[32] => $this->getAdirEmpId(),
            $keys[33] => $this->getPresgeEmpId(),
            $keys[34] => $this->getSegconsEmpId(),
            $keys[35] => $this->getPresconEmpId(),
            $keys[36] => $this->getRespacqEmpId(),
            $keys[37] => $this->getJobDirectorId(),
            $keys[38] => $this->getJobViceDirectorId(),
            $keys[39] => $this->getJobDSGAId(),
            $keys[40] => $this->getJobPersonnelId(),
            $keys[41] => $this->getJobAccountingId(),
            $keys[42] => $this->getJobWarehouseId(),
            $keys[43] => $this->getJobRegistryId(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aContact) {
                $result['Contact'] = $this->aContact->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aEmployee) {
                $result['Employee'] = $this->aEmployee->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collInstituteBankProfiles) {
                $result['InstituteBankProfiles'] = $this->collInstituteBankProfiles->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = InstitutePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setInstituteId($value);
                break;
            case 1:
                $this->setName($value);
                break;
            case 2:
                $this->setMechanCode($value);
                break;
            case 3:
                $this->setContactId($value);
                break;
            case 4:
                $this->setFiscalCode($value);
                break;
            case 5:
                $this->setSchoolType($value);
                break;
            case 6:
                $this->setParent($value);
                break;
            case 7:
                $this->setDef($value);
                break;
            case 8:
                $this->setDirName($value);
                break;
            case 9:
                $this->setDirSurname($value);
                break;
            case 10:
                $this->setAdirName($value);
                break;
            case 11:
                $this->setAdirSurname($value);
                break;
            case 12:
                $this->setPresGeName($value);
                break;
            case 13:
                $this->setPresGeSurname($value);
                break;
            case 14:
                $this->setSegConsName($value);
                break;
            case 15:
                $this->setSegConsSurname($value);
                break;
            case 16:
                $this->setPresConName($value);
                break;
            case 17:
                $this->setPresConSurname($value);
                break;
            case 18:
                $this->setDirFiscalCode($value);
                break;
            case 19:
                $this->setSchoolFiscalCode($value);
                break;
            case 20:
                $this->setInpdapCode($value);
                break;
            case 21:
                $this->setAssicurazioniSanitarie($value);
                break;
            case 22:
                $this->setDirSesso($value);
                break;
            case 23:
                $this->setDirBirth($value);
                break;
            case 24:
                $this->setDirCity($value);
                break;
            case 25:
                $this->setPostalAccount($value);
                break;
            case 26:
                $this->setAtecoCode($value);
                break;
            case 27:
                $this->setActivityCode($value);
                break;
            case 28:
                $this->setDirCurrAddr($value);
                break;
            case 29:
                $this->setDirCurrCity($value);
                break;
            case 30:
                $this->setDirCurrPhone($value);
                break;
            case 31:
                $this->setDirEmpId($value);
                break;
            case 32:
                $this->setAdirEmpId($value);
                break;
            case 33:
                $this->setPresgeEmpId($value);
                break;
            case 34:
                $this->setSegconsEmpId($value);
                break;
            case 35:
                $this->setPresconEmpId($value);
                break;
            case 36:
                $this->setRespacqEmpId($value);
                break;
            case 37:
                $this->setJobDirectorId($value);
                break;
            case 38:
                $this->setJobViceDirectorId($value);
                break;
            case 39:
                $this->setJobDSGAId($value);
                break;
            case 40:
                $this->setJobPersonnelId($value);
                break;
            case 41:
                $this->setJobAccountingId($value);
                break;
            case 42:
                $this->setJobWarehouseId($value);
                break;
            case 43:
                $this->setJobRegistryId($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = InstitutePeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setInstituteId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setName($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setMechanCode($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setContactId($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setFiscalCode($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setSchoolType($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setParent($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setDef($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setDirName($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setDirSurname($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setAdirName($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setAdirSurname($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setPresGeName($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setPresGeSurname($arr[$keys[13]]);
        if (array_key_exists($keys[14], $arr)) $this->setSegConsName($arr[$keys[14]]);
        if (array_key_exists($keys[15], $arr)) $this->setSegConsSurname($arr[$keys[15]]);
        if (array_key_exists($keys[16], $arr)) $this->setPresConName($arr[$keys[16]]);
        if (array_key_exists($keys[17], $arr)) $this->setPresConSurname($arr[$keys[17]]);
        if (array_key_exists($keys[18], $arr)) $this->setDirFiscalCode($arr[$keys[18]]);
        if (array_key_exists($keys[19], $arr)) $this->setSchoolFiscalCode($arr[$keys[19]]);
        if (array_key_exists($keys[20], $arr)) $this->setInpdapCode($arr[$keys[20]]);
        if (array_key_exists($keys[21], $arr)) $this->setAssicurazioniSanitarie($arr[$keys[21]]);
        if (array_key_exists($keys[22], $arr)) $this->setDirSesso($arr[$keys[22]]);
        if (array_key_exists($keys[23], $arr)) $this->setDirBirth($arr[$keys[23]]);
        if (array_key_exists($keys[24], $arr)) $this->setDirCity($arr[$keys[24]]);
        if (array_key_exists($keys[25], $arr)) $this->setPostalAccount($arr[$keys[25]]);
        if (array_key_exists($keys[26], $arr)) $this->setAtecoCode($arr[$keys[26]]);
        if (array_key_exists($keys[27], $arr)) $this->setActivityCode($arr[$keys[27]]);
        if (array_key_exists($keys[28], $arr)) $this->setDirCurrAddr($arr[$keys[28]]);
        if (array_key_exists($keys[29], $arr)) $this->setDirCurrCity($arr[$keys[29]]);
        if (array_key_exists($keys[30], $arr)) $this->setDirCurrPhone($arr[$keys[30]]);
        if (array_key_exists($keys[31], $arr)) $this->setDirEmpId($arr[$keys[31]]);
        if (array_key_exists($keys[32], $arr)) $this->setAdirEmpId($arr[$keys[32]]);
        if (array_key_exists($keys[33], $arr)) $this->setPresgeEmpId($arr[$keys[33]]);
        if (array_key_exists($keys[34], $arr)) $this->setSegconsEmpId($arr[$keys[34]]);
        if (array_key_exists($keys[35], $arr)) $this->setPresconEmpId($arr[$keys[35]]);
        if (array_key_exists($keys[36], $arr)) $this->setRespacqEmpId($arr[$keys[36]]);
        if (array_key_exists($keys[37], $arr)) $this->setJobDirectorId($arr[$keys[37]]);
        if (array_key_exists($keys[38], $arr)) $this->setJobViceDirectorId($arr[$keys[38]]);
        if (array_key_exists($keys[39], $arr)) $this->setJobDSGAId($arr[$keys[39]]);
        if (array_key_exists($keys[40], $arr)) $this->setJobPersonnelId($arr[$keys[40]]);
        if (array_key_exists($keys[41], $arr)) $this->setJobAccountingId($arr[$keys[41]]);
        if (array_key_exists($keys[42], $arr)) $this->setJobWarehouseId($arr[$keys[42]]);
        if (array_key_exists($keys[43], $arr)) $this->setJobRegistryId($arr[$keys[43]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(InstitutePeer::DATABASE_NAME);

        if ($this->isColumnModified(InstitutePeer::INSTITUTE_ID)) $criteria->add(InstitutePeer::INSTITUTE_ID, $this->institute_id);
        if ($this->isColumnModified(InstitutePeer::NAME)) $criteria->add(InstitutePeer::NAME, $this->name);
        if ($this->isColumnModified(InstitutePeer::MECHAN_CODE)) $criteria->add(InstitutePeer::MECHAN_CODE, $this->mechan_code);
        if ($this->isColumnModified(InstitutePeer::CONTACT_ID)) $criteria->add(InstitutePeer::CONTACT_ID, $this->contact_id);
        if ($this->isColumnModified(InstitutePeer::FISCAL_CODE)) $criteria->add(InstitutePeer::FISCAL_CODE, $this->fiscal_code);
        if ($this->isColumnModified(InstitutePeer::SCHOOL_TYPE)) $criteria->add(InstitutePeer::SCHOOL_TYPE, $this->school_type);
        if ($this->isColumnModified(InstitutePeer::PARENT)) $criteria->add(InstitutePeer::PARENT, $this->parent);
        if ($this->isColumnModified(InstitutePeer::DEF)) $criteria->add(InstitutePeer::DEF, $this->def);
        if ($this->isColumnModified(InstitutePeer::DIR_NAME)) $criteria->add(InstitutePeer::DIR_NAME, $this->dir_name);
        if ($this->isColumnModified(InstitutePeer::DIR_SURNAME)) $criteria->add(InstitutePeer::DIR_SURNAME, $this->dir_surname);
        if ($this->isColumnModified(InstitutePeer::ADIR_NAME)) $criteria->add(InstitutePeer::ADIR_NAME, $this->adir_name);
        if ($this->isColumnModified(InstitutePeer::ADIR_SURNAME)) $criteria->add(InstitutePeer::ADIR_SURNAME, $this->adir_surname);
        if ($this->isColumnModified(InstitutePeer::PRES_GE_NAME)) $criteria->add(InstitutePeer::PRES_GE_NAME, $this->pres_ge_name);
        if ($this->isColumnModified(InstitutePeer::PRES_GE_SURNAME)) $criteria->add(InstitutePeer::PRES_GE_SURNAME, $this->pres_ge_surname);
        if ($this->isColumnModified(InstitutePeer::SEG_CONS_NAME)) $criteria->add(InstitutePeer::SEG_CONS_NAME, $this->seg_cons_name);
        if ($this->isColumnModified(InstitutePeer::SEG_CONS_SURNAME)) $criteria->add(InstitutePeer::SEG_CONS_SURNAME, $this->seg_cons_surname);
        if ($this->isColumnModified(InstitutePeer::PRES_CON_NAME)) $criteria->add(InstitutePeer::PRES_CON_NAME, $this->pres_con_name);
        if ($this->isColumnModified(InstitutePeer::PRES_CON_SURNAME)) $criteria->add(InstitutePeer::PRES_CON_SURNAME, $this->pres_con_surname);
        if ($this->isColumnModified(InstitutePeer::DIR_FISCAL_CODE)) $criteria->add(InstitutePeer::DIR_FISCAL_CODE, $this->dir_fiscal_code);
        if ($this->isColumnModified(InstitutePeer::SCHOOL_FISCAL_CODE)) $criteria->add(InstitutePeer::SCHOOL_FISCAL_CODE, $this->school_fiscal_code);
        if ($this->isColumnModified(InstitutePeer::INPDAP_CODE)) $criteria->add(InstitutePeer::INPDAP_CODE, $this->inpdap_code);
        if ($this->isColumnModified(InstitutePeer::ASSICURAZIONI_SANITARIE)) $criteria->add(InstitutePeer::ASSICURAZIONI_SANITARIE, $this->assicurazioni_sanitarie);
        if ($this->isColumnModified(InstitutePeer::DIR_SESSO)) $criteria->add(InstitutePeer::DIR_SESSO, $this->dir_sesso);
        if ($this->isColumnModified(InstitutePeer::DIR_BIRTH)) $criteria->add(InstitutePeer::DIR_BIRTH, $this->dir_birth);
        if ($this->isColumnModified(InstitutePeer::DIR_CITY)) $criteria->add(InstitutePeer::DIR_CITY, $this->dir_city);
        if ($this->isColumnModified(InstitutePeer::POSTAL_ACCOUNT)) $criteria->add(InstitutePeer::POSTAL_ACCOUNT, $this->postal_account);
        if ($this->isColumnModified(InstitutePeer::ATECO_CODE)) $criteria->add(InstitutePeer::ATECO_CODE, $this->ateco_code);
        if ($this->isColumnModified(InstitutePeer::ACTIVITY_CODE)) $criteria->add(InstitutePeer::ACTIVITY_CODE, $this->activity_code);
        if ($this->isColumnModified(InstitutePeer::DIR_CURR_ADDR)) $criteria->add(InstitutePeer::DIR_CURR_ADDR, $this->dir_curr_addr);
        if ($this->isColumnModified(InstitutePeer::DIR_CURR_CITY)) $criteria->add(InstitutePeer::DIR_CURR_CITY, $this->dir_curr_city);
        if ($this->isColumnModified(InstitutePeer::DIR_CURR_PHONE)) $criteria->add(InstitutePeer::DIR_CURR_PHONE, $this->dir_curr_phone);
        if ($this->isColumnModified(InstitutePeer::DIR_EMP_ID)) $criteria->add(InstitutePeer::DIR_EMP_ID, $this->dir_emp_id);
        if ($this->isColumnModified(InstitutePeer::ADIR_EMP_ID)) $criteria->add(InstitutePeer::ADIR_EMP_ID, $this->adir_emp_id);
        if ($this->isColumnModified(InstitutePeer::PRESGE_EMP_ID)) $criteria->add(InstitutePeer::PRESGE_EMP_ID, $this->presge_emp_id);
        if ($this->isColumnModified(InstitutePeer::SEGCONS_EMP_ID)) $criteria->add(InstitutePeer::SEGCONS_EMP_ID, $this->segcons_emp_id);
        if ($this->isColumnModified(InstitutePeer::PRESCON_EMP_ID)) $criteria->add(InstitutePeer::PRESCON_EMP_ID, $this->prescon_emp_id);
        if ($this->isColumnModified(InstitutePeer::RESPACQ_EMP_ID)) $criteria->add(InstitutePeer::RESPACQ_EMP_ID, $this->respacq_emp_id);
        if ($this->isColumnModified(InstitutePeer::JOB_DIRECTOR_ID)) $criteria->add(InstitutePeer::JOB_DIRECTOR_ID, $this->job_director_id);
        if ($this->isColumnModified(InstitutePeer::JOB_VICE_DIRECTOR_ID)) $criteria->add(InstitutePeer::JOB_VICE_DIRECTOR_ID, $this->job_vice_director_id);
        if ($this->isColumnModified(InstitutePeer::JOB_DSGA_ID)) $criteria->add(InstitutePeer::JOB_DSGA_ID, $this->job_dsga_id);
        if ($this->isColumnModified(InstitutePeer::JOB_PERSONNEL_ID)) $criteria->add(InstitutePeer::JOB_PERSONNEL_ID, $this->job_personnel_id);
        if ($this->isColumnModified(InstitutePeer::JOB_ACCOUNTING_ID)) $criteria->add(InstitutePeer::JOB_ACCOUNTING_ID, $this->job_accounting_id);
        if ($this->isColumnModified(InstitutePeer::JOB_WAREHOUSE_ID)) $criteria->add(InstitutePeer::JOB_WAREHOUSE_ID, $this->job_warehouse_id);
        if ($this->isColumnModified(InstitutePeer::JOB_REGISTRY_ID)) $criteria->add(InstitutePeer::JOB_REGISTRY_ID, $this->job_registry_id);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(InstitutePeer::DATABASE_NAME);
        $criteria->add(InstitutePeer::INSTITUTE_ID, $this->institute_id);
        $criteria->add(InstitutePeer::CONTACT_ID, $this->contact_id);

        return $criteria;
    }

    /**
     * Returns the composite primary key for this object.
     * The array elements will be in same order as specified in XML.
     * @return array
     */
    public function getPrimaryKey()
    {
        $pks = array();
        $pks[0] = $this->getInstituteId();
        $pks[1] = $this->getContactId();

        return $pks;
    }

    /**
     * Set the [composite] primary key.
     *
     * @param array $keys The elements of the composite key (order must match the order in XML file).
     * @return void
     */
    public function setPrimaryKey($keys)
    {
        $this->setInstituteId($keys[0]);
        $this->setContactId($keys[1]);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return (null === $this->getInstituteId()) && (null === $this->getContactId());
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Institute (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setName($this->getName());
        $copyObj->setMechanCode($this->getMechanCode());
        $copyObj->setContactId($this->getContactId());
        $copyObj->setFiscalCode($this->getFiscalCode());
        $copyObj->setSchoolType($this->getSchoolType());
        $copyObj->setParent($this->getParent());
        $copyObj->setDef($this->getDef());
        $copyObj->setDirName($this->getDirName());
        $copyObj->setDirSurname($this->getDirSurname());
        $copyObj->setAdirName($this->getAdirName());
        $copyObj->setAdirSurname($this->getAdirSurname());
        $copyObj->setPresGeName($this->getPresGeName());
        $copyObj->setPresGeSurname($this->getPresGeSurname());
        $copyObj->setSegConsName($this->getSegConsName());
        $copyObj->setSegConsSurname($this->getSegConsSurname());
        $copyObj->setPresConName($this->getPresConName());
        $copyObj->setPresConSurname($this->getPresConSurname());
        $copyObj->setDirFiscalCode($this->getDirFiscalCode());
        $copyObj->setSchoolFiscalCode($this->getSchoolFiscalCode());
        $copyObj->setInpdapCode($this->getInpdapCode());
        $copyObj->setAssicurazioniSanitarie($this->getAssicurazioniSanitarie());
        $copyObj->setDirSesso($this->getDirSesso());
        $copyObj->setDirBirth($this->getDirBirth());
        $copyObj->setDirCity($this->getDirCity());
        $copyObj->setPostalAccount($this->getPostalAccount());
        $copyObj->setAtecoCode($this->getAtecoCode());
        $copyObj->setActivityCode($this->getActivityCode());
        $copyObj->setDirCurrAddr($this->getDirCurrAddr());
        $copyObj->setDirCurrCity($this->getDirCurrCity());
        $copyObj->setDirCurrPhone($this->getDirCurrPhone());
        $copyObj->setDirEmpId($this->getDirEmpId());
        $copyObj->setAdirEmpId($this->getAdirEmpId());
        $copyObj->setPresgeEmpId($this->getPresgeEmpId());
        $copyObj->setSegconsEmpId($this->getSegconsEmpId());
        $copyObj->setPresconEmpId($this->getPresconEmpId());
        $copyObj->setRespacqEmpId($this->getRespacqEmpId());
        $copyObj->setJobDirectorId($this->getJobDirectorId());
        $copyObj->setJobViceDirectorId($this->getJobViceDirectorId());
        $copyObj->setJobDSGAId($this->getJobDSGAId());
        $copyObj->setJobPersonnelId($this->getJobPersonnelId());
        $copyObj->setJobAccountingId($this->getJobAccountingId());
        $copyObj->setJobWarehouseId($this->getJobWarehouseId());
        $copyObj->setJobRegistryId($this->getJobRegistryId());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getInstituteBankProfiles() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addInstituteBankProfile($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setInstituteId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Institute Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return InstitutePeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new InstitutePeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Contact object.
     *
     * @param                  Contact $v
     * @return Institute The current object (for fluent API support)
     * @throws PropelException
     */
    public function setContact(Contact $v = null)
    {
        if ($v === null) {
            $this->setContactId(NULL);
        } else {
            $this->setContactId($v->getContactId());
        }

        $this->aContact = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Contact object, it will not be re-added.
        if ($v !== null) {
            $v->addInstitute($this);
        }


        return $this;
    }


    /**
     * Get the associated Contact object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Contact The associated Contact object.
     * @throws PropelException
     */
    public function getContact(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aContact === null && ($this->contact_id !== null) && $doQuery) {
            $this->aContact = ContactQuery::create()->findPk($this->contact_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aContact->addInstitutes($this);
             */
        }

        return $this->aContact;
    }

    /**
     * Declares an association between this object and a Employee object.
     *
     * @param                  Employee $v
     * @return Institute The current object (for fluent API support)
     * @throws PropelException
     */
    public function setEmployee(Employee $v = null)
    {
        if ($v === null) {
            $this->setDirEmpId(0);
        } else {
            $this->setDirEmpId($v->getEmployeeId());
        }

        $this->aEmployee = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Employee object, it will not be re-added.
        if ($v !== null) {
            $v->addInstitute($this);
        }


        return $this;
    }


    /**
     * Get the associated Employee object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Employee The associated Employee object.
     * @throws PropelException
     */
    public function getEmployee(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aEmployee === null && ($this->dir_emp_id !== null) && $doQuery) {
            $this->aEmployee = EmployeeQuery::create()->findPk($this->dir_emp_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aEmployee->addInstitutes($this);
             */
        }

        return $this->aEmployee;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('InstituteBankProfile' == $relationName) {
            $this->initInstituteBankProfiles();
        }
    }

    /**
     * Clears out the collInstituteBankProfiles collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Institute The current object (for fluent API support)
     * @see        addInstituteBankProfiles()
     */
    public function clearInstituteBankProfiles()
    {
        $this->collInstituteBankProfiles = null; // important to set this to null since that means it is uninitialized
        $this->collInstituteBankProfilesPartial = null;

        return $this;
    }

    /**
     * reset is the collInstituteBankProfiles collection loaded partially
     *
     * @return void
     */
    public function resetPartialInstituteBankProfiles($v = true)
    {
        $this->collInstituteBankProfilesPartial = $v;
    }

    /**
     * Initializes the collInstituteBankProfiles collection.
     *
     * By default this just sets the collInstituteBankProfiles collection to an empty array (like clearcollInstituteBankProfiles());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initInstituteBankProfiles($overrideExisting = true)
    {
        if (null !== $this->collInstituteBankProfiles && !$overrideExisting) {
            return;
        }
        $this->collInstituteBankProfiles = new PropelObjectCollection();
        $this->collInstituteBankProfiles->setModel('InstituteBankProfile');
    }

    /**
     * Gets an array of InstituteBankProfile objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Institute is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|InstituteBankProfile[] List of InstituteBankProfile objects
     * @throws PropelException
     */
    public function getInstituteBankProfiles($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collInstituteBankProfilesPartial && !$this->isNew();
        if (null === $this->collInstituteBankProfiles || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collInstituteBankProfiles) {
                // return empty collection
                $this->initInstituteBankProfiles();
            } else {
                $collInstituteBankProfiles = InstituteBankProfileQuery::create(null, $criteria)
                    ->filterByInstituteBankProfileKey($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collInstituteBankProfilesPartial && count($collInstituteBankProfiles)) {
                      $this->initInstituteBankProfiles(false);

                      foreach ($collInstituteBankProfiles as $obj) {
                        if (false == $this->collInstituteBankProfiles->contains($obj)) {
                          $this->collInstituteBankProfiles->append($obj);
                        }
                      }

                      $this->collInstituteBankProfilesPartial = true;
                    }

                    $collInstituteBankProfiles->getInternalIterator()->rewind();

                    return $collInstituteBankProfiles;
                }

                if ($partial && $this->collInstituteBankProfiles) {
                    foreach ($this->collInstituteBankProfiles as $obj) {
                        if ($obj->isNew()) {
                            $collInstituteBankProfiles[] = $obj;
                        }
                    }
                }

                $this->collInstituteBankProfiles = $collInstituteBankProfiles;
                $this->collInstituteBankProfilesPartial = false;
            }
        }

        return $this->collInstituteBankProfiles;
    }

    /**
     * Sets a collection of InstituteBankProfile objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $instituteBankProfiles A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Institute The current object (for fluent API support)
     */
    public function setInstituteBankProfiles(PropelCollection $instituteBankProfiles, PropelPDO $con = null)
    {
        $instituteBankProfilesToDelete = $this->getInstituteBankProfiles(new Criteria(), $con)->diff($instituteBankProfiles);


        $this->instituteBankProfilesScheduledForDeletion = $instituteBankProfilesToDelete;

        foreach ($instituteBankProfilesToDelete as $instituteBankProfileRemoved) {
            $instituteBankProfileRemoved->setInstituteBankProfileKey(null);
        }

        $this->collInstituteBankProfiles = null;
        foreach ($instituteBankProfiles as $instituteBankProfile) {
            $this->addInstituteBankProfile($instituteBankProfile);
        }

        $this->collInstituteBankProfiles = $instituteBankProfiles;
        $this->collInstituteBankProfilesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related InstituteBankProfile objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related InstituteBankProfile objects.
     * @throws PropelException
     */
    public function countInstituteBankProfiles(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collInstituteBankProfilesPartial && !$this->isNew();
        if (null === $this->collInstituteBankProfiles || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collInstituteBankProfiles) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getInstituteBankProfiles());
            }
            $query = InstituteBankProfileQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByInstituteBankProfileKey($this)
                ->count($con);
        }

        return count($this->collInstituteBankProfiles);
    }

    /**
     * Method called to associate a InstituteBankProfile object to this object
     * through the InstituteBankProfile foreign key attribute.
     *
     * @param    InstituteBankProfile $l InstituteBankProfile
     * @return Institute The current object (for fluent API support)
     */
    public function addInstituteBankProfile(InstituteBankProfile $l)
    {
        if ($this->collInstituteBankProfiles === null) {
            $this->initInstituteBankProfiles();
            $this->collInstituteBankProfilesPartial = true;
        }

        if (!in_array($l, $this->collInstituteBankProfiles->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddInstituteBankProfile($l);

            if ($this->instituteBankProfilesScheduledForDeletion and $this->instituteBankProfilesScheduledForDeletion->contains($l)) {
                $this->instituteBankProfilesScheduledForDeletion->remove($this->instituteBankProfilesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	InstituteBankProfile $instituteBankProfile The instituteBankProfile object to add.
     */
    protected function doAddInstituteBankProfile($instituteBankProfile)
    {
        $this->collInstituteBankProfiles[]= $instituteBankProfile;
        $instituteBankProfile->setInstituteBankProfileKey($this);
    }

    /**
     * @param	InstituteBankProfile $instituteBankProfile The instituteBankProfile object to remove.
     * @return Institute The current object (for fluent API support)
     */
    public function removeInstituteBankProfile($instituteBankProfile)
    {
        if ($this->getInstituteBankProfiles()->contains($instituteBankProfile)) {
            $this->collInstituteBankProfiles->remove($this->collInstituteBankProfiles->search($instituteBankProfile));
            if (null === $this->instituteBankProfilesScheduledForDeletion) {
                $this->instituteBankProfilesScheduledForDeletion = clone $this->collInstituteBankProfiles;
                $this->instituteBankProfilesScheduledForDeletion->clear();
            }
            $this->instituteBankProfilesScheduledForDeletion[]= clone $instituteBankProfile;
            $instituteBankProfile->setInstituteBankProfileKey(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Institute is new, it will return
     * an empty collection; or if this Institute has previously
     * been saved, it will retrieve related InstituteBankProfiles from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Institute.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|InstituteBankProfile[] List of InstituteBankProfile objects
     */
    public function getInstituteBankProfilesJoinInstituteBankProfileBankProfileKey($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = InstituteBankProfileQuery::create(null, $criteria);
        $query->joinWith('InstituteBankProfileBankProfileKey', $join_behavior);

        return $this->getInstituteBankProfiles($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->institute_id = null;
        $this->name = null;
        $this->mechan_code = null;
        $this->contact_id = null;
        $this->fiscal_code = null;
        $this->school_type = null;
        $this->parent = null;
        $this->def = null;
        $this->dir_name = null;
        $this->dir_surname = null;
        $this->adir_name = null;
        $this->adir_surname = null;
        $this->pres_ge_name = null;
        $this->pres_ge_surname = null;
        $this->seg_cons_name = null;
        $this->seg_cons_surname = null;
        $this->pres_con_name = null;
        $this->pres_con_surname = null;
        $this->dir_fiscal_code = null;
        $this->school_fiscal_code = null;
        $this->inpdap_code = null;
        $this->assicurazioni_sanitarie = null;
        $this->dir_sesso = null;
        $this->dir_birth = null;
        $this->dir_city = null;
        $this->postal_account = null;
        $this->ateco_code = null;
        $this->activity_code = null;
        $this->dir_curr_addr = null;
        $this->dir_curr_city = null;
        $this->dir_curr_phone = null;
        $this->dir_emp_id = null;
        $this->adir_emp_id = null;
        $this->presge_emp_id = null;
        $this->segcons_emp_id = null;
        $this->prescon_emp_id = null;
        $this->respacq_emp_id = null;
        $this->job_director_id = null;
        $this->job_vice_director_id = null;
        $this->job_dsga_id = null;
        $this->job_personnel_id = null;
        $this->job_accounting_id = null;
        $this->job_warehouse_id = null;
        $this->job_registry_id = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collInstituteBankProfiles) {
                foreach ($this->collInstituteBankProfiles as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aContact instanceof Persistent) {
              $this->aContact->clearAllReferences($deep);
            }
            if ($this->aEmployee instanceof Persistent) {
              $this->aEmployee->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collInstituteBankProfiles instanceof PropelCollection) {
            $this->collInstituteBankProfiles->clearIterator();
        }
        $this->collInstituteBankProfiles = null;
        $this->aContact = null;
        $this->aEmployee = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(InstitutePeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
