<?php

namespace Core\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Cities;
use Core\CitiesQuery;
use Core\Contact;
use Core\ContactPeer;
use Core\ContactQuery;
use Core\Institute;
use Core\InstituteQuery;
use Employee\Employee;
use Employee\EmployeeQuery;

/**
 * Base class that represents a row from the 'contact' table.
 *
 *
 *
 * @package    propel.generator.Core.om
 */
abstract class BaseContact extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Core\\ContactPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        ContactPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the contact_id field.
     * @var        int
     */
    protected $contact_id;

    /**
     * The value for the address field.
     * @var        string
     */
    protected $address;

    /**
     * The value for the phone_num field.
     * @var        string
     */
    protected $phone_num;

    /**
     * The value for the fax field.
     * @var        string
     */
    protected $fax;

    /**
     * The value for the city_id field.
     * @var        int
     */
    protected $city_id;

    /**
     * The value for the email field.
     * @var        string
     */
    protected $email;

    /**
     * The value for the mobile field.
     * @var        string
     */
    protected $mobile;

    /**
     * The value for the web field.
     * @var        string
     */
    protected $web;

    /**
     * The value for the cap field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cap;

    /**
     * @var        Cities
     */
    protected $aCityKey;

    /**
     * @var        PropelObjectCollection|Institute[] Collection to store aggregation of Institute objects.
     */
    protected $collInstitutes;
    protected $collInstitutesPartial;

    /**
     * @var        PropelObjectCollection|Employee[] Collection to store aggregation of Employee objects.
     */
    protected $collEmployeesRelatedByResidenceId;
    protected $collEmployeesRelatedByResidenceIdPartial;

    /**
     * @var        PropelObjectCollection|Employee[] Collection to store aggregation of Employee objects.
     */
    protected $collEmployeesRelatedByAddressId;
    protected $collEmployeesRelatedByAddressIdPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $institutesScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $employeesRelatedByResidenceIdScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $employeesRelatedByAddressIdScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->cap = '';
    }

    /**
     * Initializes internal state of BaseContact object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [contact_id] column value.
     *
     * @return int
     */
    public function getContactId()
    {

        return $this->contact_id;
    }

    /**
     * Get the [address] column value.
     *
     * @return string
     */
    public function getAddress()
    {

        return $this->address;
    }

    /**
     * Get the [phone_num] column value.
     *
     * @return string
     */
    public function getPhoneNum()
    {

        return $this->phone_num;
    }

    /**
     * Get the [fax] column value.
     *
     * @return string
     */
    public function getFax()
    {

        return $this->fax;
    }

    /**
     * Get the [city_id] column value.
     *
     * @return int
     */
    public function getCityId()
    {

        return $this->city_id;
    }

    /**
     * Get the [email] column value.
     *
     * @return string
     */
    public function getEmail()
    {

        return $this->email;
    }

    /**
     * Get the [mobile] column value.
     *
     * @return string
     */
    public function getMobile()
    {

        return $this->mobile;
    }

    /**
     * Get the [web] column value.
     *
     * @return string
     */
    public function getWeb()
    {

        return $this->web;
    }

    /**
     * Get the [cap] column value.
     *
     * @return string
     */
    public function getCap()
    {

        return $this->cap;
    }

    /**
     * Set the value of [contact_id] column.
     *
     * @param  int $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setContactId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->contact_id !== $v) {
            $this->contact_id = $v;
            $this->modifiedColumns[] = ContactPeer::CONTACT_ID;
        }


        return $this;
    } // setContactId()

    /**
     * Set the value of [address] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setAddress($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->address !== $v) {
            $this->address = $v;
            $this->modifiedColumns[] = ContactPeer::ADDRESS;
        }


        return $this;
    } // setAddress()

    /**
     * Set the value of [phone_num] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setPhoneNum($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->phone_num !== $v) {
            $this->phone_num = $v;
            $this->modifiedColumns[] = ContactPeer::PHONE_NUM;
        }


        return $this;
    } // setPhoneNum()

    /**
     * Set the value of [fax] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setFax($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->fax !== $v) {
            $this->fax = $v;
            $this->modifiedColumns[] = ContactPeer::FAX;
        }


        return $this;
    } // setFax()

    /**
     * Set the value of [city_id] column.
     *
     * @param  int $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setCityId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->city_id !== $v) {
            $this->city_id = $v;
            $this->modifiedColumns[] = ContactPeer::CITY_ID;
        }

        if ($this->aCityKey !== null && $this->aCityKey->getCityId() !== $v) {
            $this->aCityKey = null;
        }


        return $this;
    } // setCityId()

    /**
     * Set the value of [email] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setEmail($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->email !== $v) {
            $this->email = $v;
            $this->modifiedColumns[] = ContactPeer::EMAIL;
        }


        return $this;
    } // setEmail()

    /**
     * Set the value of [mobile] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setMobile($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->mobile !== $v) {
            $this->mobile = $v;
            $this->modifiedColumns[] = ContactPeer::MOBILE;
        }


        return $this;
    } // setMobile()

    /**
     * Set the value of [web] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setWeb($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->web !== $v) {
            $this->web = $v;
            $this->modifiedColumns[] = ContactPeer::WEB;
        }


        return $this;
    } // setWeb()

    /**
     * Set the value of [cap] column.
     *
     * @param  string $v new value
     * @return Contact The current object (for fluent API support)
     */
    public function setCap($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cap !== $v) {
            $this->cap = $v;
            $this->modifiedColumns[] = ContactPeer::CAP;
        }


        return $this;
    } // setCap()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->cap !== '') {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->contact_id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->address = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->phone_num = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->fax = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->city_id = ($row[$startcol + 4] !== null) ? (int) $row[$startcol + 4] : null;
            $this->email = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->mobile = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->web = ($row[$startcol + 7] !== null) ? (string) $row[$startcol + 7] : null;
            $this->cap = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 9; // 9 = ContactPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Contact object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aCityKey !== null && $this->city_id !== $this->aCityKey->getCityId()) {
            $this->aCityKey = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ContactPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = ContactPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aCityKey = null;
            $this->collInstitutes = null;

            $this->collEmployeesRelatedByResidenceId = null;

            $this->collEmployeesRelatedByAddressId = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ContactPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = ContactQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ContactPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                ContactPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aCityKey !== null) {
                if ($this->aCityKey->isModified() || $this->aCityKey->isNew()) {
                    $affectedRows += $this->aCityKey->save($con);
                }
                $this->setCityKey($this->aCityKey);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->institutesScheduledForDeletion !== null) {
                if (!$this->institutesScheduledForDeletion->isEmpty()) {
                    InstituteQuery::create()
                        ->filterByPrimaryKeys($this->institutesScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->institutesScheduledForDeletion = null;
                }
            }

            if ($this->collInstitutes !== null) {
                foreach ($this->collInstitutes as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->employeesRelatedByResidenceIdScheduledForDeletion !== null) {
                if (!$this->employeesRelatedByResidenceIdScheduledForDeletion->isEmpty()) {
                    EmployeeQuery::create()
                        ->filterByPrimaryKeys($this->employeesRelatedByResidenceIdScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->employeesRelatedByResidenceIdScheduledForDeletion = null;
                }
            }

            if ($this->collEmployeesRelatedByResidenceId !== null) {
                foreach ($this->collEmployeesRelatedByResidenceId as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->employeesRelatedByAddressIdScheduledForDeletion !== null) {
                if (!$this->employeesRelatedByAddressIdScheduledForDeletion->isEmpty()) {
                    EmployeeQuery::create()
                        ->filterByPrimaryKeys($this->employeesRelatedByAddressIdScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->employeesRelatedByAddressIdScheduledForDeletion = null;
                }
            }

            if ($this->collEmployeesRelatedByAddressId !== null) {
                foreach ($this->collEmployeesRelatedByAddressId as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = ContactPeer::CONTACT_ID;
        if (null !== $this->contact_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . ContactPeer::CONTACT_ID . ')');
        }
        if (null === $this->contact_id) {
            try {
                $stmt = $con->query("SELECT nextval('contact_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->contact_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(ContactPeer::CONTACT_ID)) {
            $modifiedColumns[':p' . $index++]  = '"contact_id"';
        }
        if ($this->isColumnModified(ContactPeer::ADDRESS)) {
            $modifiedColumns[':p' . $index++]  = '"address"';
        }
        if ($this->isColumnModified(ContactPeer::PHONE_NUM)) {
            $modifiedColumns[':p' . $index++]  = '"phone_num"';
        }
        if ($this->isColumnModified(ContactPeer::FAX)) {
            $modifiedColumns[':p' . $index++]  = '"fax"';
        }
        if ($this->isColumnModified(ContactPeer::CITY_ID)) {
            $modifiedColumns[':p' . $index++]  = '"city_id"';
        }
        if ($this->isColumnModified(ContactPeer::EMAIL)) {
            $modifiedColumns[':p' . $index++]  = '"email"';
        }
        if ($this->isColumnModified(ContactPeer::MOBILE)) {
            $modifiedColumns[':p' . $index++]  = '"mobile"';
        }
        if ($this->isColumnModified(ContactPeer::WEB)) {
            $modifiedColumns[':p' . $index++]  = '"web"';
        }
        if ($this->isColumnModified(ContactPeer::CAP)) {
            $modifiedColumns[':p' . $index++]  = '"cap"';
        }

        $sql = sprintf(
            'INSERT INTO "contact" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"contact_id"':
                        $stmt->bindValue($identifier, $this->contact_id, PDO::PARAM_INT);
                        break;
                    case '"address"':
                        $stmt->bindValue($identifier, $this->address, PDO::PARAM_STR);
                        break;
                    case '"phone_num"':
                        $stmt->bindValue($identifier, $this->phone_num, PDO::PARAM_STR);
                        break;
                    case '"fax"':
                        $stmt->bindValue($identifier, $this->fax, PDO::PARAM_STR);
                        break;
                    case '"city_id"':
                        $stmt->bindValue($identifier, $this->city_id, PDO::PARAM_INT);
                        break;
                    case '"email"':
                        $stmt->bindValue($identifier, $this->email, PDO::PARAM_STR);
                        break;
                    case '"mobile"':
                        $stmt->bindValue($identifier, $this->mobile, PDO::PARAM_STR);
                        break;
                    case '"web"':
                        $stmt->bindValue($identifier, $this->web, PDO::PARAM_STR);
                        break;
                    case '"cap"':
                        $stmt->bindValue($identifier, $this->cap, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aCityKey !== null) {
                if (!$this->aCityKey->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aCityKey->getValidationFailures());
                }
            }


            if (($retval = ContactPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collInstitutes !== null) {
                    foreach ($this->collInstitutes as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collEmployeesRelatedByResidenceId !== null) {
                    foreach ($this->collEmployeesRelatedByResidenceId as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collEmployeesRelatedByAddressId !== null) {
                    foreach ($this->collEmployeesRelatedByAddressId as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = ContactPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getContactId();
                break;
            case 1:
                return $this->getAddress();
                break;
            case 2:
                return $this->getPhoneNum();
                break;
            case 3:
                return $this->getFax();
                break;
            case 4:
                return $this->getCityId();
                break;
            case 5:
                return $this->getEmail();
                break;
            case 6:
                return $this->getMobile();
                break;
            case 7:
                return $this->getWeb();
                break;
            case 8:
                return $this->getCap();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Contact'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Contact'][$this->getPrimaryKey()] = true;
        $keys = ContactPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getContactId(),
            $keys[1] => $this->getAddress(),
            $keys[2] => $this->getPhoneNum(),
            $keys[3] => $this->getFax(),
            $keys[4] => $this->getCityId(),
            $keys[5] => $this->getEmail(),
            $keys[6] => $this->getMobile(),
            $keys[7] => $this->getWeb(),
            $keys[8] => $this->getCap(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aCityKey) {
                $result['CityKey'] = $this->aCityKey->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collInstitutes) {
                $result['Institutes'] = $this->collInstitutes->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collEmployeesRelatedByResidenceId) {
                $result['EmployeesRelatedByResidenceId'] = $this->collEmployeesRelatedByResidenceId->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collEmployeesRelatedByAddressId) {
                $result['EmployeesRelatedByAddressId'] = $this->collEmployeesRelatedByAddressId->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = ContactPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setContactId($value);
                break;
            case 1:
                $this->setAddress($value);
                break;
            case 2:
                $this->setPhoneNum($value);
                break;
            case 3:
                $this->setFax($value);
                break;
            case 4:
                $this->setCityId($value);
                break;
            case 5:
                $this->setEmail($value);
                break;
            case 6:
                $this->setMobile($value);
                break;
            case 7:
                $this->setWeb($value);
                break;
            case 8:
                $this->setCap($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = ContactPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setContactId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setAddress($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setPhoneNum($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setFax($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setCityId($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setEmail($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setMobile($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setWeb($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setCap($arr[$keys[8]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(ContactPeer::DATABASE_NAME);

        if ($this->isColumnModified(ContactPeer::CONTACT_ID)) $criteria->add(ContactPeer::CONTACT_ID, $this->contact_id);
        if ($this->isColumnModified(ContactPeer::ADDRESS)) $criteria->add(ContactPeer::ADDRESS, $this->address);
        if ($this->isColumnModified(ContactPeer::PHONE_NUM)) $criteria->add(ContactPeer::PHONE_NUM, $this->phone_num);
        if ($this->isColumnModified(ContactPeer::FAX)) $criteria->add(ContactPeer::FAX, $this->fax);
        if ($this->isColumnModified(ContactPeer::CITY_ID)) $criteria->add(ContactPeer::CITY_ID, $this->city_id);
        if ($this->isColumnModified(ContactPeer::EMAIL)) $criteria->add(ContactPeer::EMAIL, $this->email);
        if ($this->isColumnModified(ContactPeer::MOBILE)) $criteria->add(ContactPeer::MOBILE, $this->mobile);
        if ($this->isColumnModified(ContactPeer::WEB)) $criteria->add(ContactPeer::WEB, $this->web);
        if ($this->isColumnModified(ContactPeer::CAP)) $criteria->add(ContactPeer::CAP, $this->cap);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(ContactPeer::DATABASE_NAME);
        $criteria->add(ContactPeer::CONTACT_ID, $this->contact_id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getContactId();
    }

    /**
     * Generic method to set the primary key (contact_id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setContactId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getContactId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Contact (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setAddress($this->getAddress());
        $copyObj->setPhoneNum($this->getPhoneNum());
        $copyObj->setFax($this->getFax());
        $copyObj->setCityId($this->getCityId());
        $copyObj->setEmail($this->getEmail());
        $copyObj->setMobile($this->getMobile());
        $copyObj->setWeb($this->getWeb());
        $copyObj->setCap($this->getCap());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getInstitutes() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addInstitute($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getEmployeesRelatedByResidenceId() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addEmployeeRelatedByResidenceId($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getEmployeesRelatedByAddressId() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addEmployeeRelatedByAddressId($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setContactId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Contact Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return ContactPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new ContactPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Cities object.
     *
     * @param                  Cities $v
     * @return Contact The current object (for fluent API support)
     * @throws PropelException
     */
    public function setCityKey(Cities $v = null)
    {
        if ($v === null) {
            $this->setCityId(NULL);
        } else {
            $this->setCityId($v->getCityId());
        }

        $this->aCityKey = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Cities object, it will not be re-added.
        if ($v !== null) {
            $v->addContact($this);
        }


        return $this;
    }


    /**
     * Get the associated Cities object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Cities The associated Cities object.
     * @throws PropelException
     */
    public function getCityKey(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aCityKey === null && ($this->city_id !== null) && $doQuery) {
            $this->aCityKey = CitiesQuery::create()->findPk($this->city_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aCityKey->addContacts($this);
             */
        }

        return $this->aCityKey;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Institute' == $relationName) {
            $this->initInstitutes();
        }
        if ('EmployeeRelatedByResidenceId' == $relationName) {
            $this->initEmployeesRelatedByResidenceId();
        }
        if ('EmployeeRelatedByAddressId' == $relationName) {
            $this->initEmployeesRelatedByAddressId();
        }
    }

    /**
     * Clears out the collInstitutes collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Contact The current object (for fluent API support)
     * @see        addInstitutes()
     */
    public function clearInstitutes()
    {
        $this->collInstitutes = null; // important to set this to null since that means it is uninitialized
        $this->collInstitutesPartial = null;

        return $this;
    }

    /**
     * reset is the collInstitutes collection loaded partially
     *
     * @return void
     */
    public function resetPartialInstitutes($v = true)
    {
        $this->collInstitutesPartial = $v;
    }

    /**
     * Initializes the collInstitutes collection.
     *
     * By default this just sets the collInstitutes collection to an empty array (like clearcollInstitutes());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initInstitutes($overrideExisting = true)
    {
        if (null !== $this->collInstitutes && !$overrideExisting) {
            return;
        }
        $this->collInstitutes = new PropelObjectCollection();
        $this->collInstitutes->setModel('Institute');
    }

    /**
     * Gets an array of Institute objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Contact is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Institute[] List of Institute objects
     * @throws PropelException
     */
    public function getInstitutes($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collInstitutesPartial && !$this->isNew();
        if (null === $this->collInstitutes || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collInstitutes) {
                // return empty collection
                $this->initInstitutes();
            } else {
                $collInstitutes = InstituteQuery::create(null, $criteria)
                    ->filterByContact($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collInstitutesPartial && count($collInstitutes)) {
                      $this->initInstitutes(false);

                      foreach ($collInstitutes as $obj) {
                        if (false == $this->collInstitutes->contains($obj)) {
                          $this->collInstitutes->append($obj);
                        }
                      }

                      $this->collInstitutesPartial = true;
                    }

                    $collInstitutes->getInternalIterator()->rewind();

                    return $collInstitutes;
                }

                if ($partial && $this->collInstitutes) {
                    foreach ($this->collInstitutes as $obj) {
                        if ($obj->isNew()) {
                            $collInstitutes[] = $obj;
                        }
                    }
                }

                $this->collInstitutes = $collInstitutes;
                $this->collInstitutesPartial = false;
            }
        }

        return $this->collInstitutes;
    }

    /**
     * Sets a collection of Institute objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $institutes A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Contact The current object (for fluent API support)
     */
    public function setInstitutes(PropelCollection $institutes, PropelPDO $con = null)
    {
        $institutesToDelete = $this->getInstitutes(new Criteria(), $con)->diff($institutes);


        //since at least one column in the foreign key is at the same time a PK
        //we can not just set a PK to NULL in the lines below. We have to store
        //a backup of all values, so we are able to manipulate these items based on the onDelete value later.
        $this->institutesScheduledForDeletion = clone $institutesToDelete;

        foreach ($institutesToDelete as $instituteRemoved) {
            $instituteRemoved->setContact(null);
        }

        $this->collInstitutes = null;
        foreach ($institutes as $institute) {
            $this->addInstitute($institute);
        }

        $this->collInstitutes = $institutes;
        $this->collInstitutesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Institute objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Institute objects.
     * @throws PropelException
     */
    public function countInstitutes(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collInstitutesPartial && !$this->isNew();
        if (null === $this->collInstitutes || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collInstitutes) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getInstitutes());
            }
            $query = InstituteQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByContact($this)
                ->count($con);
        }

        return count($this->collInstitutes);
    }

    /**
     * Method called to associate a Institute object to this object
     * through the Institute foreign key attribute.
     *
     * @param    Institute $l Institute
     * @return Contact The current object (for fluent API support)
     */
    public function addInstitute(Institute $l)
    {
        if ($this->collInstitutes === null) {
            $this->initInstitutes();
            $this->collInstitutesPartial = true;
        }

        if (!in_array($l, $this->collInstitutes->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddInstitute($l);

            if ($this->institutesScheduledForDeletion and $this->institutesScheduledForDeletion->contains($l)) {
                $this->institutesScheduledForDeletion->remove($this->institutesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Institute $institute The institute object to add.
     */
    protected function doAddInstitute($institute)
    {
        $this->collInstitutes[]= $institute;
        $institute->setContact($this);
    }

    /**
     * @param	Institute $institute The institute object to remove.
     * @return Contact The current object (for fluent API support)
     */
    public function removeInstitute($institute)
    {
        if ($this->getInstitutes()->contains($institute)) {
            $this->collInstitutes->remove($this->collInstitutes->search($institute));
            if (null === $this->institutesScheduledForDeletion) {
                $this->institutesScheduledForDeletion = clone $this->collInstitutes;
                $this->institutesScheduledForDeletion->clear();
            }
            $this->institutesScheduledForDeletion[]= clone $institute;
            $institute->setContact(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Contact is new, it will return
     * an empty collection; or if this Contact has previously
     * been saved, it will retrieve related Institutes from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Contact.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Institute[] List of Institute objects
     */
    public function getInstitutesJoinEmployee($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = InstituteQuery::create(null, $criteria);
        $query->joinWith('Employee', $join_behavior);

        return $this->getInstitutes($query, $con);
    }

    /**
     * Clears out the collEmployeesRelatedByResidenceId collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Contact The current object (for fluent API support)
     * @see        addEmployeesRelatedByResidenceId()
     */
    public function clearEmployeesRelatedByResidenceId()
    {
        $this->collEmployeesRelatedByResidenceId = null; // important to set this to null since that means it is uninitialized
        $this->collEmployeesRelatedByResidenceIdPartial = null;

        return $this;
    }

    /**
     * reset is the collEmployeesRelatedByResidenceId collection loaded partially
     *
     * @return void
     */
    public function resetPartialEmployeesRelatedByResidenceId($v = true)
    {
        $this->collEmployeesRelatedByResidenceIdPartial = $v;
    }

    /**
     * Initializes the collEmployeesRelatedByResidenceId collection.
     *
     * By default this just sets the collEmployeesRelatedByResidenceId collection to an empty array (like clearcollEmployeesRelatedByResidenceId());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initEmployeesRelatedByResidenceId($overrideExisting = true)
    {
        if (null !== $this->collEmployeesRelatedByResidenceId && !$overrideExisting) {
            return;
        }
        $this->collEmployeesRelatedByResidenceId = new PropelObjectCollection();
        $this->collEmployeesRelatedByResidenceId->setModel('Employee');
    }

    /**
     * Gets an array of Employee objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Contact is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Employee[] List of Employee objects
     * @throws PropelException
     */
    public function getEmployeesRelatedByResidenceId($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collEmployeesRelatedByResidenceIdPartial && !$this->isNew();
        if (null === $this->collEmployeesRelatedByResidenceId || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collEmployeesRelatedByResidenceId) {
                // return empty collection
                $this->initEmployeesRelatedByResidenceId();
            } else {
                $collEmployeesRelatedByResidenceId = EmployeeQuery::create(null, $criteria)
                    ->filterByResidenceKey($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collEmployeesRelatedByResidenceIdPartial && count($collEmployeesRelatedByResidenceId)) {
                      $this->initEmployeesRelatedByResidenceId(false);

                      foreach ($collEmployeesRelatedByResidenceId as $obj) {
                        if (false == $this->collEmployeesRelatedByResidenceId->contains($obj)) {
                          $this->collEmployeesRelatedByResidenceId->append($obj);
                        }
                      }

                      $this->collEmployeesRelatedByResidenceIdPartial = true;
                    }

                    $collEmployeesRelatedByResidenceId->getInternalIterator()->rewind();

                    return $collEmployeesRelatedByResidenceId;
                }

                if ($partial && $this->collEmployeesRelatedByResidenceId) {
                    foreach ($this->collEmployeesRelatedByResidenceId as $obj) {
                        if ($obj->isNew()) {
                            $collEmployeesRelatedByResidenceId[] = $obj;
                        }
                    }
                }

                $this->collEmployeesRelatedByResidenceId = $collEmployeesRelatedByResidenceId;
                $this->collEmployeesRelatedByResidenceIdPartial = false;
            }
        }

        return $this->collEmployeesRelatedByResidenceId;
    }

    /**
     * Sets a collection of EmployeeRelatedByResidenceId objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $employeesRelatedByResidenceId A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Contact The current object (for fluent API support)
     */
    public function setEmployeesRelatedByResidenceId(PropelCollection $employeesRelatedByResidenceId, PropelPDO $con = null)
    {
        $employeesRelatedByResidenceIdToDelete = $this->getEmployeesRelatedByResidenceId(new Criteria(), $con)->diff($employeesRelatedByResidenceId);


        $this->employeesRelatedByResidenceIdScheduledForDeletion = $employeesRelatedByResidenceIdToDelete;

        foreach ($employeesRelatedByResidenceIdToDelete as $employeeRelatedByResidenceIdRemoved) {
            $employeeRelatedByResidenceIdRemoved->setResidenceKey(null);
        }

        $this->collEmployeesRelatedByResidenceId = null;
        foreach ($employeesRelatedByResidenceId as $employeeRelatedByResidenceId) {
            $this->addEmployeeRelatedByResidenceId($employeeRelatedByResidenceId);
        }

        $this->collEmployeesRelatedByResidenceId = $employeesRelatedByResidenceId;
        $this->collEmployeesRelatedByResidenceIdPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Employee objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Employee objects.
     * @throws PropelException
     */
    public function countEmployeesRelatedByResidenceId(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collEmployeesRelatedByResidenceIdPartial && !$this->isNew();
        if (null === $this->collEmployeesRelatedByResidenceId || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collEmployeesRelatedByResidenceId) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getEmployeesRelatedByResidenceId());
            }
            $query = EmployeeQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByResidenceKey($this)
                ->count($con);
        }

        return count($this->collEmployeesRelatedByResidenceId);
    }

    /**
     * Method called to associate a Employee object to this object
     * through the Employee foreign key attribute.
     *
     * @param    Employee $l Employee
     * @return Contact The current object (for fluent API support)
     */
    public function addEmployeeRelatedByResidenceId(Employee $l)
    {
        if ($this->collEmployeesRelatedByResidenceId === null) {
            $this->initEmployeesRelatedByResidenceId();
            $this->collEmployeesRelatedByResidenceIdPartial = true;
        }

        if (!in_array($l, $this->collEmployeesRelatedByResidenceId->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddEmployeeRelatedByResidenceId($l);

            if ($this->employeesRelatedByResidenceIdScheduledForDeletion and $this->employeesRelatedByResidenceIdScheduledForDeletion->contains($l)) {
                $this->employeesRelatedByResidenceIdScheduledForDeletion->remove($this->employeesRelatedByResidenceIdScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	EmployeeRelatedByResidenceId $employeeRelatedByResidenceId The employeeRelatedByResidenceId object to add.
     */
    protected function doAddEmployeeRelatedByResidenceId($employeeRelatedByResidenceId)
    {
        $this->collEmployeesRelatedByResidenceId[]= $employeeRelatedByResidenceId;
        $employeeRelatedByResidenceId->setResidenceKey($this);
    }

    /**
     * @param	EmployeeRelatedByResidenceId $employeeRelatedByResidenceId The employeeRelatedByResidenceId object to remove.
     * @return Contact The current object (for fluent API support)
     */
    public function removeEmployeeRelatedByResidenceId($employeeRelatedByResidenceId)
    {
        if ($this->getEmployeesRelatedByResidenceId()->contains($employeeRelatedByResidenceId)) {
            $this->collEmployeesRelatedByResidenceId->remove($this->collEmployeesRelatedByResidenceId->search($employeeRelatedByResidenceId));
            if (null === $this->employeesRelatedByResidenceIdScheduledForDeletion) {
                $this->employeesRelatedByResidenceIdScheduledForDeletion = clone $this->collEmployeesRelatedByResidenceId;
                $this->employeesRelatedByResidenceIdScheduledForDeletion->clear();
            }
            $this->employeesRelatedByResidenceIdScheduledForDeletion[]= $employeeRelatedByResidenceId;
            $employeeRelatedByResidenceId->setResidenceKey(null);
        }

        return $this;
    }

    /**
     * Clears out the collEmployeesRelatedByAddressId collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Contact The current object (for fluent API support)
     * @see        addEmployeesRelatedByAddressId()
     */
    public function clearEmployeesRelatedByAddressId()
    {
        $this->collEmployeesRelatedByAddressId = null; // important to set this to null since that means it is uninitialized
        $this->collEmployeesRelatedByAddressIdPartial = null;

        return $this;
    }

    /**
     * reset is the collEmployeesRelatedByAddressId collection loaded partially
     *
     * @return void
     */
    public function resetPartialEmployeesRelatedByAddressId($v = true)
    {
        $this->collEmployeesRelatedByAddressIdPartial = $v;
    }

    /**
     * Initializes the collEmployeesRelatedByAddressId collection.
     *
     * By default this just sets the collEmployeesRelatedByAddressId collection to an empty array (like clearcollEmployeesRelatedByAddressId());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initEmployeesRelatedByAddressId($overrideExisting = true)
    {
        if (null !== $this->collEmployeesRelatedByAddressId && !$overrideExisting) {
            return;
        }
        $this->collEmployeesRelatedByAddressId = new PropelObjectCollection();
        $this->collEmployeesRelatedByAddressId->setModel('Employee');
    }

    /**
     * Gets an array of Employee objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Contact is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Employee[] List of Employee objects
     * @throws PropelException
     */
    public function getEmployeesRelatedByAddressId($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collEmployeesRelatedByAddressIdPartial && !$this->isNew();
        if (null === $this->collEmployeesRelatedByAddressId || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collEmployeesRelatedByAddressId) {
                // return empty collection
                $this->initEmployeesRelatedByAddressId();
            } else {
                $collEmployeesRelatedByAddressId = EmployeeQuery::create(null, $criteria)
                    ->filterByAddressKey($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collEmployeesRelatedByAddressIdPartial && count($collEmployeesRelatedByAddressId)) {
                      $this->initEmployeesRelatedByAddressId(false);

                      foreach ($collEmployeesRelatedByAddressId as $obj) {
                        if (false == $this->collEmployeesRelatedByAddressId->contains($obj)) {
                          $this->collEmployeesRelatedByAddressId->append($obj);
                        }
                      }

                      $this->collEmployeesRelatedByAddressIdPartial = true;
                    }

                    $collEmployeesRelatedByAddressId->getInternalIterator()->rewind();

                    return $collEmployeesRelatedByAddressId;
                }

                if ($partial && $this->collEmployeesRelatedByAddressId) {
                    foreach ($this->collEmployeesRelatedByAddressId as $obj) {
                        if ($obj->isNew()) {
                            $collEmployeesRelatedByAddressId[] = $obj;
                        }
                    }
                }

                $this->collEmployeesRelatedByAddressId = $collEmployeesRelatedByAddressId;
                $this->collEmployeesRelatedByAddressIdPartial = false;
            }
        }

        return $this->collEmployeesRelatedByAddressId;
    }

    /**
     * Sets a collection of EmployeeRelatedByAddressId objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $employeesRelatedByAddressId A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Contact The current object (for fluent API support)
     */
    public function setEmployeesRelatedByAddressId(PropelCollection $employeesRelatedByAddressId, PropelPDO $con = null)
    {
        $employeesRelatedByAddressIdToDelete = $this->getEmployeesRelatedByAddressId(new Criteria(), $con)->diff($employeesRelatedByAddressId);


        $this->employeesRelatedByAddressIdScheduledForDeletion = $employeesRelatedByAddressIdToDelete;

        foreach ($employeesRelatedByAddressIdToDelete as $employeeRelatedByAddressIdRemoved) {
            $employeeRelatedByAddressIdRemoved->setAddressKey(null);
        }

        $this->collEmployeesRelatedByAddressId = null;
        foreach ($employeesRelatedByAddressId as $employeeRelatedByAddressId) {
            $this->addEmployeeRelatedByAddressId($employeeRelatedByAddressId);
        }

        $this->collEmployeesRelatedByAddressId = $employeesRelatedByAddressId;
        $this->collEmployeesRelatedByAddressIdPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Employee objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Employee objects.
     * @throws PropelException
     */
    public function countEmployeesRelatedByAddressId(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collEmployeesRelatedByAddressIdPartial && !$this->isNew();
        if (null === $this->collEmployeesRelatedByAddressId || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collEmployeesRelatedByAddressId) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getEmployeesRelatedByAddressId());
            }
            $query = EmployeeQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAddressKey($this)
                ->count($con);
        }

        return count($this->collEmployeesRelatedByAddressId);
    }

    /**
     * Method called to associate a Employee object to this object
     * through the Employee foreign key attribute.
     *
     * @param    Employee $l Employee
     * @return Contact The current object (for fluent API support)
     */
    public function addEmployeeRelatedByAddressId(Employee $l)
    {
        if ($this->collEmployeesRelatedByAddressId === null) {
            $this->initEmployeesRelatedByAddressId();
            $this->collEmployeesRelatedByAddressIdPartial = true;
        }

        if (!in_array($l, $this->collEmployeesRelatedByAddressId->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddEmployeeRelatedByAddressId($l);

            if ($this->employeesRelatedByAddressIdScheduledForDeletion and $this->employeesRelatedByAddressIdScheduledForDeletion->contains($l)) {
                $this->employeesRelatedByAddressIdScheduledForDeletion->remove($this->employeesRelatedByAddressIdScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	EmployeeRelatedByAddressId $employeeRelatedByAddressId The employeeRelatedByAddressId object to add.
     */
    protected function doAddEmployeeRelatedByAddressId($employeeRelatedByAddressId)
    {
        $this->collEmployeesRelatedByAddressId[]= $employeeRelatedByAddressId;
        $employeeRelatedByAddressId->setAddressKey($this);
    }

    /**
     * @param	EmployeeRelatedByAddressId $employeeRelatedByAddressId The employeeRelatedByAddressId object to remove.
     * @return Contact The current object (for fluent API support)
     */
    public function removeEmployeeRelatedByAddressId($employeeRelatedByAddressId)
    {
        if ($this->getEmployeesRelatedByAddressId()->contains($employeeRelatedByAddressId)) {
            $this->collEmployeesRelatedByAddressId->remove($this->collEmployeesRelatedByAddressId->search($employeeRelatedByAddressId));
            if (null === $this->employeesRelatedByAddressIdScheduledForDeletion) {
                $this->employeesRelatedByAddressIdScheduledForDeletion = clone $this->collEmployeesRelatedByAddressId;
                $this->employeesRelatedByAddressIdScheduledForDeletion->clear();
            }
            $this->employeesRelatedByAddressIdScheduledForDeletion[]= $employeeRelatedByAddressId;
            $employeeRelatedByAddressId->setAddressKey(null);
        }

        return $this;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->contact_id = null;
        $this->address = null;
        $this->phone_num = null;
        $this->fax = null;
        $this->city_id = null;
        $this->email = null;
        $this->mobile = null;
        $this->web = null;
        $this->cap = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collInstitutes) {
                foreach ($this->collInstitutes as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collEmployeesRelatedByResidenceId) {
                foreach ($this->collEmployeesRelatedByResidenceId as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collEmployeesRelatedByAddressId) {
                foreach ($this->collEmployeesRelatedByAddressId as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aCityKey instanceof Persistent) {
              $this->aCityKey->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collInstitutes instanceof PropelCollection) {
            $this->collInstitutes->clearIterator();
        }
        $this->collInstitutes = null;
        if ($this->collEmployeesRelatedByResidenceId instanceof PropelCollection) {
            $this->collEmployeesRelatedByResidenceId->clearIterator();
        }
        $this->collEmployeesRelatedByResidenceId = null;
        if ($this->collEmployeesRelatedByAddressId instanceof PropelCollection) {
            $this->collEmployeesRelatedByAddressId->clearIterator();
        }
        $this->collEmployeesRelatedByAddressId = null;
        $this->aCityKey = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(ContactPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
