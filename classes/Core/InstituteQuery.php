<?php

namespace Core;

use Core\om\BaseInstituteQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'institute' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Core
 */
class InstituteQuery extends BaseInstituteQuery {

	public function read($data) {
		$where = key_exists('name', $data) ? " WHERE name ILIKE '%" . pg_escape_string($data['name']) . "%'" : "";
		$limit = isset($data['limit']) ? ' LIMIT ' . $data['limit'] : '';
		$offset = isset($data['start']) ? ' OFFSET ' . $data['start'] : '';

		$sql = "
			SELECT
				institute_id, institute.name, mechan_code, fiscal_code,	def, postal_account,
				contact.address, cities.description AS city, cities.city_id
			FROM
				institute LEFT JOIN contact ON (institute.contact_id = contact.contact_id)
				LEFT JOIN cities ON (contact.city_id = cities.city_id)
			{$where}
			ORDER BY
				def DESC, name ASC
			{$limit}
			{$offset}
			";

		$db = \Propel::getConnection('mc2api');
		$res = $db->query($sql);
		return $res->fetchAll(\PDO::FETCH_ASSOC);
	}

}
