<?php

namespace Core;


require_once '/var/www-source/mc2-api/module/CCP/src/CCP/Model/Mastercom.php';
require_once '/var/www-source/mt/1.2/classes/MT/Mastercom/Db.php';

/**
 * Class that manage login.
 *
 * <AUTHOR>
 */
class Login
{

    private $_force = false;
    public $username;
    public $password;
    public $nexusUser;

    /**
     *
     * @param type $username
     * @param type $password
     */
    public function __construct($username, $password)
    {
        $this->username = pg_escape_string($username);
        $this->password = pg_escape_string($password);

        $rootUser = explode(':', $this->username);
        if (count($rootUser) == 2) {
            $db = \Propel::getConnection('mc2api');

            $res = $db->query("SELECT * FROM users
                    WHERE user_name = '{$rootUser[0]}'
                        AND super_user=true LIMIT 1");
            $this->nexusUser = $res->fetch();
            if (!empty($this->nexusUser)) {
                $this->nexusUser = $this->nexusUser['user_name'];
                $this->_force = true;
                $this->username = $rootUser[1];
            }
        }
    }

    public function authentication()
    {
        $db = \Propel::getConnection('mc2api');

        // Caso utente forzato tramite $this->nexusUser
        // TODO Da togliere una volta passati a couch
        if ($this->_force) {
            $res = $db->query("SELECT * FROM users
                    WHERE user_name = '{$this->nexusUser}'
                        AND enabled=1
                        AND (privelege > 0 OR user_type > 0)
                    LIMIT 1");
            $masterUser = $res->fetch();

            if (\MT\Utils\Pbkdf2::isValid($this->password, $masterUser['user_password'])) {
                $res = $db->query("SELECT * FROM users
                    WHERE user_name = '" . $this->username . "'
                        AND enabled=1
                        AND (privelege > 0 OR user_type > 0)
                    LIMIT 1");
                $origUser = $res->fetch();
                return new \User((int) $origUser['uid']);
            }
        }


        $resNew = $db->query("SELECT * FROM users
                    WHERE user_name = '{$this->username}'
                        AND enabled=1
                        AND (privelege > 0 OR user_type > 0)
                    LIMIT 1");
        $resOld = $db->query("SELECT * FROM users
                    WHERE user_name = '{$this->username}'
                        AND user_password = md5('{$this->password}')
                        AND enabled=1
                        AND (privelege > 0 OR user_type > 0)
                    LIMIT 1");

        $userArrNew = $resNew->fetch();
        $userArr = $resOld->fetch();

        if ($userArr !== false) {
            $user = new \User((int) $userArr['uid']);
            $user->setPassword($this->password);
        } else if (\MT\Utils\Pbkdf2::isValid($this->password, $userArrNew['user_password'])) {
            $userArr = $userArrNew;
        }


        if ($userArr === false) {
            return false;
        } else {
            return new \User((int) $userArr['uid']);
        }
        // TODO Da togliere una volta passati a couch
    }

    public function couchAuthenticationEnabled()
    {
        $db = \Propel::getConnection('mc2api');
        $res = $db->query("SELECT value from parameter where name = 'COUCH_AUTHENTICATION' ");
        $params = $res->fetch();
        return $params['value'] === 't';
    }

    public function isCouchUser() {
        $db = \Propel::getConnection('mc2api');
        $res = $db->query("SELECT couch_id from users where user_name = '{$this->username}' ");
        $params = $res->fetch();
        return !empty($params['couch_id']);
    }

    public function getMastercomId() {
        $dbMc = new \MT\Mastercom\Db;
        return $dbMc->query("SELECT valore from parametri where nome='MASTERCOM_ID'")['valore'];
    }

    /**
     * Resposnse string $id or false
     */
    public function generateCouchUser($user, $postfix='mc2') {
        $mcApi = new \CCP\Model\Mastercom();
        $db = \Propel::getConnection('mc2api');

        $this->username .=  $postfix;
        $mastercomId = $this->getMastercomId();
        $data = array(
            'username' => $this->username,
            'password' => $this->password,
            'tipo' => 'user',
            'ruoli' => [
                $mastercomId => [
                    'mc2' => [
                        'TUTTI' => $user->getId()
                    ]
                ]
            ],
            'main_role' => [
                'type' => 'mc2',
                'id' => $user->getId(),
                'school' => $mastercomId,
            ]
        );


        if($user->surname) {
            $data['surname'] =  $user->surname;
        }
        if($user->name) {
            $data['first_name'] =  $user->name;
        }
        if($user->email) {
            $data['email'] =  $user->email;
        }

        $res = $mcApi->put('/user', $data);
        if(!$res || $res['ok']!= true || !$res['id']) {
            return false;
        }

        $db->query("UPDATE users SET couch_id='{$res['id']}', user_name='{$this->username}' where user_name = '{$user->user_name}'");
        return true;
    }

    /**
     * Resposnse string $id or false
     */
    public function updateCouchUser($user) {
        $mcApi = new \CCP\Model\Mastercom();
        $db = \Propel::getConnection('mc2api');

        $res = $db->query("SELECT couch_id FROM users
                    WHERE uid = '{$user->getId()}'");
        $couchId = $res->fetch()['couch_id'];

        $data = array(
            'username' => $this->username,
        );


        if($user->surname) {
            $data['surname'] =  $user->surname;
        }
        if($user->name) {
            $data['first_name'] =  $user->name;
        }
        if($user->email) {
            $data['email'] =  $user->email;
        }

        $res = $mcApi->put('/user/'.$couchId , $data);
        if(!$res || $res['ok']!= true || !$res['id']) {
            return false;
        }

        return true;
    }


    public function couchAuthentication()
    {
        if(!$this->isCouchUser()) {
            return false;
        }

        $db = \Propel::getConnection('mc2api');
        $mcApi = new \CCP\Model\Mastercom();

        $res = $db->query("SELECT value from parameter where name = 'NEXT_API_HOST' ");
        $params = $res->fetch();

        if (empty($params['value'])) {
            throw new \Exception('Next api host parameter not found or empty');
        }

        $tk = '';
        if($this->_force) {
            //file_put_contents('/tmp/mc2.log', "Login for nexus user: {$this->nexusUser} and user: {$this->username}\n", FILE_APPEND);
            $nexusTk = $mcApi->post("/login", [
                'username' => $this->nexusUser,
                'password' => $this->password
            ], false);
            if (!$nexusTk) {
                return false;
            }

            $res = $db->query("SELECT * FROM users
                    WHERE user_name = '{$this->username}'
                        AND enabled=1
                        AND (privelege > 0 OR user_type > 0)
                    LIMIT 1");
            $userMc2 = $res->fetch();

            if(!$userMc2) {
                return false;
            }
            $data = [
                'uid' => $userMc2['uid'],
                'type' => 'mc2'
            ];



            $tk = $mcApi->post("/login", $data, false);

            //file_put_contents('/tmp/mc2.log', "Response from login: " . print_r($res, true) . "\n", FILE_APPEND);

            if(!$tk) {
                return false;
            }

        } else {

            $tk = $mcApi->post("/login", [
                'username' => $this->username,
                'password' => $this->password
            ], false);


            if (!$tk) {
                return false;
            }
        }


        $_SESSION['couch_tk'] = $tk;


        $res = $db->query("SELECT * FROM users
                WHERE user_name = '{$this->username}'
                    AND enabled=1
                    AND (privelege > 0 OR user_type > 0)
                LIMIT 1");
        $couchUser = $res->fetch();

        if ($couchUser === false) {
            return false;
        } else {
            return new \User((int) $couchUser['uid']);
        }

    }
}
