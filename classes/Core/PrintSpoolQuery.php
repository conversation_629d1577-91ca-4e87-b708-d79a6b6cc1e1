<?php

namespace Core;

use Core\om\BasePrintSpoolQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'core_print_spool' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Core
 */
class PrintSpoolQuery extends BasePrintSpoolQuery {

    public function read($filter) {
        if (in_array('id', array_keys($filter))) {
            return PrintSpoolQuery::create()->filterById((int) $filter['id'])->orderById()->find()->toArray(null, false, \BasePeer::TYPE_FIELDNAME);
        } else {
            return [];
        }
    }

    public function clean() {
        $prints = PrintSpoolQuery::create()->find();
        foreach ($prints as $print) {
            $print->delete();
        }
        return true;
    }

    public function setStatus($id, $completed = true) {
        $print = PrintSpoolQuery::create()->findPk((int) $id);
        if (!$print) {
            return false;
        } else {
            $print->setCompleted((bool) $completed);
            $print->save();
            return true;
        }
    }

}
