<?php

namespace Core;

/**
 * The class create pdf files from svg, replacing key with value
 * from passed array
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintSvg {

    public $baseTemplatePath;
    public $data;

    public function __construct($baseTemplatePath, $data) {
        $this->baseTemplatePath = $baseTemplatePath;
        $this->data = $data;
    }

    public function create($md5Name = null) {
        $pdfFiles = array();
        for ($k = 1; $k <= (int) $this->data['common']['pages']; $k++) {
            $thisPageData = $this->data[$k];

            $tmpFile = basename(tempnam(PATH_TMP_ROOT, 'tmp'));
            rename(PATH_TMP_ROOT . $tmpFile, PATH_TMP_ROOT . $tmpFile . '.pdf');
            $pdfFiles[] = PATH_TMP_ROOT . $tmpFile . '.pdf';
            $pathSvgTemplate = PATH_ROOT . $this->baseTemplatePath . '_' . $k . '.svg';
            $svgFileContent = @file_get_contents($pathSvgTemplate);

            foreach ($this->data['common'] as $key => $value) {
                $svgFileContent = str_replace('{' . $key . '}', $value, $svgFileContent);
            }

            foreach ($thisPageData as $key => $value) {
                if (is_array($value)) {
                    foreach ($value as $key2 => $value2) {
                        $svgFileContent = str_replace('{' . $key2 . '}', $value2, $svgFileContent);
                    }
                    unset($this->data[$k][$key]);
                    if (count($this->data[$k]) > 0) {
                        $k--;
                    }
                    break;
                } else {
                    $svgFileContent = str_replace('{' . $key . '}', $value, $svgFileContent);
                }
            }

            @file_put_contents(PATH_TMP_ROOT . $tmpFile . '.svg', $svgFileContent);
            $command = 'chmod 777 ' . PATH_TMP_ROOT . $tmpFile . '.svg && ';
            $command .= 'chmod 777 ' . PATH_TMP_ROOT . $tmpFile . '.pdf && ';
            //$command .= 'inkscape -f ' . PATH_TMP_ROOT . $tmpFile . '.svg -A ' . PATH_TMP_ROOT . $tmpFile . '.pdf ';
            $command .= 'inkscape -z --vacuum-defs ' . PATH_TMP_ROOT . $tmpFile . '.svg --export-pdf=' . PATH_TMP_ROOT . $tmpFile . '.pdf --export-text-to-path ';
            shell_exec($command);
        }

        $file = basename(tempnam(PATH_TMP_ROOT, 'tmp'));
        if ($md5Name !== null) {
            rename(PATH_TMP_ROOT . $file, PATH_TMP_ROOT . $md5Name);
            $file = $md5Name;
        }
        rename(PATH_TMP_ROOT . $file, PATH_TMP_ROOT . $file . '.pdf');
        exec('chmod 777 ' . PATH_TMP_ROOT . $file . '.pdf');
        $file.='.pdf';
        exec('pdftk ' . implode(' ', $pdfFiles) . ' cat output ' . PATH_TMP_ROOT . $file);

        // Remove tmp file
        $rmCommandPdf = 'rm -f ' . implode(' ', $pdfFiles);
        $rmCommandSvg = str_replace('.pdf', '.svg', $rmCommandPdf);
        exec($rmCommandPdf);
        exec($rmCommandSvg);

        if (file_exists(PATH_TMP_ROOT . $file)) {
            return true;
        } else {
            return false;
        }
    }

}
