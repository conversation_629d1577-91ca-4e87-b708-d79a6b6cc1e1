<?php

namespace Core;

/*
 * Class to handle APi research on external API (Milan for ex)
 */

/**
 *
 * <AUTHOR>
 */
class ExternalApi {

    private $_token;
    private $_baseUrl;
    private $_root;
    private $_port;
    private $_url;

    public function __construct() {
        $api_url = ParameterQuery::create()->filterByName('API_URL')->findOne();
        $api_root = ParameterQuery::create()->filterByName('API_ROOT')->findOne();
        $api_port = ParameterQuery::create()->filterByName('API_PORT')->findOne();
        $this->_token = md5('mc2' . date("dmY"));
        $this->_baseUrl = $api_url->getValue();
        $this->_root = $api_root->getValue();
        $this->_port = $api_port->getValue();
        $this->_url = $this->_baseUrl . ($this->_port ? ':' . $this->_port : '') . $this->_root;
    }

    public function get($api, $data = array()) {
        $this->_url .= $api;

        $getArr = array();
        $data['token'] = $this->_token;
        foreach ($data as $key => $value) {
            $getArr[] = $key . '=' . $value;
        }

        $params = join('&', $getArr);
        $data = json_decode(file_get_contents($this->_url . '?' . $params));

        if ($data == null) {
            return false;
        } else {
            return (array) $data;
        }
    }

}
