<?php

namespace Core;

use Core\om\BaseInstituteBankProfileQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'institute_bank_profile' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Core
 */
class InstituteBankProfileQuery extends BaseInstituteBankProfileQuery {

    public $errors = array();
    public $message = '';

    public function read($filters) {
        $db = \Propel::getConnection('mc2api');

        $and = "";
        if (isset($filters['institute'])) {
            $and = " AND ibp.institute = " . $filters['institute'];
        }

        $sql = "SELECT ba.*, ibp.institute
                FROM bank_profile AS ba, institute_bank_profile AS ibp
                WHERE ba.id = ibp.bank_profile {$and}";
        $r = $db->query($sql);
        return $r->fetchAll();
    }

    public function validate($data) {
        if (strlen($data['country_code']) !== 2) {
            $this->errors['country_code'] = _('This field must have two charatcters only');
        }
        if (strlen($data['check_code']) !== 2) {
            $this->errors['check_code'] = _('This field must have two charatcters only');
        }
    }

    public function write($data) {
        $this->validate($data);

        if (!$data['institute']) {
            $this->message = _('Institute is required');
        }

        if (isset($data['id']) && $data['id'] > 0) {
            $this->message = _('Bank account already present');
        } else {
            unset($data['id']);
        }

        if (count($this->errors) > 0 || $this->message) {
            return false;
        }

        $bObj = new BankProfile;
        $bObj->fromArray($data, \BasePeer::TYPE_FIELDNAME);
        $bObj->save();

        $iba = new InstituteBankProfile;
        $iba->setInstitute($data['institute']);
        $iba->setBankProfile($bObj->getPrimaryKey());
        $iba->save();

        return $bObj->getDenomination();
    }

    public function update($data) {
        if (!$data['id'] || !isset($data['id'])) {
            $this->message = _('Bank account does not exist');
        }

        if ($this->message) {
            return false;
        }
        $bankAccount = \Core\BankProfileQuery::create()->findPk($data['id']);
        $bankAccount->fromArray($data, \BasePeer::TYPE_FIELDNAME);
        $this->validate($bankAccount->toArray(\BasePeer::TYPE_FIELDNAME));
        if (count($this->errors) > 0) {
            return false;
        }
        $bankAccount->save();
        return $bankAccount->getDenomination();
    }

    public function destroy($filter) {
        if (isset($filter['id']) && $filter['id'] > 0) {
            $bp = BankProfileQuery::create()->findPK($filter['id']);
            $bp->delete();
            return $bp->getDenomination();
        } else {
            $this->message = _('Bank does not exist');
            return false;
        }
    }

    // Gets all institute banks and Ccp profile
    public function readCcp() {
        $ccpBanks = array();
        $institute = InstituteQuery::create()->filterByDef(true)->findOne();
        if ($institute !== null) {
            $banks = $this->read(array(
                'institute' => $institute->getInstituteId()
                    )
            );

            foreach ($banks as $bank) {
                $ccpBanks[] = array(
                    'id'   => 'ba_' . $bank['id'],
                    'text' => $bank['denomination']
                );
            }

            $ccpBanks[] = array(
                'id'   => 'ccp_0',
                'text' => _('CCP')
            );
        }

        return $ccpBanks;
    }

    // Gets all institute banks and Ccp profile
    public function readAll() {
        $ccpBanks = array();
        $institute = InstituteQuery::create()->filterByDef(true)->findOne();

        if ($institute !== null) {
            if ($institute->getPostalAccount()) {
                $ccpBanks[] = [
                    'id'           => '0',
                    'denomination' => 'C/C Postale',
                    'coordinates'  => $institute->getPostalAccount()
                ];
            }

            $banks = $this->read(array('institute' => $institute->getInstituteId()));

            foreach ($banks as $bank) {
                $ccpBanks[] = array(
                    'id'           => $bank['id'],
                    'denomination' => $bank['denomination'],
                    'coordinates'  => $bank['country_code'] . $bank['check_code'] . $bank['bban']
                );
            }
        }

        return $ccpBanks;
    }

}
