<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'institute_bank_profile' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class InstituteBankProfileTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.InstituteBankProfileTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('institute_bank_profile');
        $this->setPhpName('InstituteBankProfile');
        $this->setClassname('Core\\InstituteBankProfile');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('institute_bank_profile_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addForeignKey('institute', 'Institute', 'INTEGER', 'institute', 'institute_id', true, null, null);
        $this->addForeignKey('bank_profile', 'BankProfile', 'INTEGER', 'bank_profile', 'id', true, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('InstituteBankProfileBankProfileKey', 'Core\\BankProfile', RelationMap::MANY_TO_ONE, array('bank_profile' => 'id', ), 'CASCADE', null);
        $this->addRelation('InstituteBankProfileKey', 'Core\\Institute', RelationMap::MANY_TO_ONE, array('institute' => 'institute_id', ), 'CASCADE', null);
    } // buildRelations()

} // InstituteBankProfileTableMap
