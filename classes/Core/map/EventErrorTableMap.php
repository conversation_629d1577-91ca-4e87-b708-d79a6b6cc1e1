<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'event_error' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class EventErrorTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.EventErrorTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('event_error');
        $this->setPhpName('EventError');
        $this->setClassname('Core\\EventError');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('event_error_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('level_nr', 'LevelNr', 'INTEGER', true, null, null);
        $this->addColumn('level_descr', 'LevelDescr', 'LONGVARCHAR', true, null, null);
        $this->addColumn('description', 'Description', 'LONGVARCHAR', true, null, null);
        $this->addColumn('path', 'Path', 'LONGVARCHAR', true, null, null);
        $this->addColumn('line', 'Line', 'LONGVARCHAR', true, null, null);
        $this->addColumn('error_time', 'ErrorTime', 'TIMESTAMP', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // EventErrorTableMap
