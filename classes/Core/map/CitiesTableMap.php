<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'cities' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class CitiesTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.CitiesTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('cities');
        $this->setPhpName('Cities');
        $this->setClassname('Core\\Cities');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('cities_city_id_seq');
        // columns
        $this->addPrimaryKey('city_id', 'CityId', 'INTEGER', true, null, null);
        $this->addColumn('description', 'Description', 'VARCHAR', true, 50, null);
        $this->addColumn('city_code', 'CityCode', 'VARCHAR', false, 10, null);
        $this->addColumn('province', 'Province', 'VARCHAR', false, 5, null);
        $this->addForeignKey('region', 'Region', 'VARCHAR', 'regions', 'code', false, 5, null);
        $this->addColumn('is_city', 'IsCity', 'SMALLINT', false, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('RegionKey', 'Core\\Regions', RelationMap::MANY_TO_ONE, array('region' => 'code', ), null, null);
        $this->addRelation('Contact', 'Core\\Contact', RelationMap::ONE_TO_MANY, array('city_id' => 'city_id', ), 'SET NULL', null, 'Contacts');
    } // buildRelations()

} // CitiesTableMap
