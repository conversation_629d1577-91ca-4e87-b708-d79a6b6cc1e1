<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'bank_profile' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class BankProfileTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.BankProfileTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('bank_profile');
        $this->setPhpName('BankProfile');
        $this->setClassname('Core\\BankProfile');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('bank_profile_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('country_code', 'CountryCode', 'VARCHAR', false, 2, null);
        $this->addColumn('check_code', 'CheckCode', 'VARCHAR', false, 2, null);
        $this->addColumn('bban', 'Bban', 'VARCHAR', false, 100, null);
        $this->addColumn('denomination', 'Denomination', 'VARCHAR', false, 100, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('InstituteBankProfile', 'Core\\InstituteBankProfile', RelationMap::ONE_TO_MANY, array('id' => 'bank_profile', ), 'CASCADE', null, 'InstituteBankProfiles');
    } // buildRelations()

} // BankProfileTableMap
