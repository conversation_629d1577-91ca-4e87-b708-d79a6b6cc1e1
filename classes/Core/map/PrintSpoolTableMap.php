<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'core_print_spool' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class PrintSpoolTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.PrintSpoolTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('core_print_spool');
        $this->setPhpName('PrintSpool');
        $this->setClassname('Core\\PrintSpool');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('core_print_spool_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('user_id', 'UserId', 'INTEGER', true, null, null);
        $this->addColumn('name', 'Name', 'VARCHAR', true, 100, null);
        $this->addColumn('path', 'Path', 'VARCHAR', true, 100, null);
        $this->addColumn('completed', 'Completed', 'BOOLEAN', true, null, false);
        $this->addColumn('params', 'Parameters', 'LONGVARCHAR', true, null, '');
        $this->addColumn('notified', 'Notified', 'BOOLEAN', true, null, false);
        $this->addColumn('mime', 'Mime', 'VARCHAR', true, 50, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // PrintSpoolTableMap
