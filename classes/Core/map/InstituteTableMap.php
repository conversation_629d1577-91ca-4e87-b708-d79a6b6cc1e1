<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'institute' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class InstituteTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.InstituteTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('institute');
        $this->setPhpName('Institute');
        $this->setClassname('Core\\Institute');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('institute_id_seq');
        // columns
        $this->addPrimaryKey('institute_id', 'InstituteId', 'INTEGER', true, null, null);
        $this->addColumn('name', 'Name', 'VARCHAR', true, 200, null);
        $this->addColumn('mechan_code', 'MechanCode', 'VARCHAR', true, 16, null);
        $this->addForeignPrimaryKey('contact_id', 'ContactId', 'INTEGER' , 'contact', 'contact_id', true, null, null);
        $this->addColumn('fiscal_code', 'FiscalCode', 'VARCHAR', false, 16, null);
        $this->addColumn('school_type', 'SchoolType', 'VARCHAR', false, 3, null);
        $this->addColumn('parent', 'Parent', 'INTEGER', false, null, null);
        $this->addColumn('def', 'Def', 'BOOLEAN', false, null, false);
        $this->addColumn('dir_name', 'DirName', 'VARCHAR', false, 100, null);
        $this->addColumn('dir_surname', 'DirSurname', 'VARCHAR', false, 100, null);
        $this->addColumn('adir_name', 'AdirName', 'VARCHAR', false, 100, null);
        $this->addColumn('adir_surname', 'AdirSurname', 'VARCHAR', false, 100, null);
        $this->addColumn('pres_ge_name', 'PresGeName', 'VARCHAR', false, 100, null);
        $this->addColumn('pres_ge_surname', 'PresGeSurname', 'VARCHAR', false, 100, null);
        $this->addColumn('seg_cons_name', 'SegConsName', 'VARCHAR', false, 100, null);
        $this->addColumn('seg_cons_surname', 'SegConsSurname', 'VARCHAR', false, 100, null);
        $this->addColumn('pres_con_name', 'PresConName', 'VARCHAR', false, 100, null);
        $this->addColumn('pres_con_surname', 'PresConSurname', 'VARCHAR', false, 100, null);
        $this->addColumn('dir_fiscal_code', 'DirFiscalCode', 'LONGVARCHAR', false, null, null);
        $this->addColumn('school_fiscal_code', 'SchoolFiscalCode', 'LONGVARCHAR', false, null, null);
        $this->addColumn('inpdap_code', 'InpdapCode', 'LONGVARCHAR', false, null, null);
        $this->addColumn('assicurazioni_sanitarie', 'AssicurazioniSanitarie', 'LONGVARCHAR', false, null, null);
        $this->addColumn('dir_sesso', 'DirSesso', 'VARCHAR', true, 1, '');
        $this->addColumn('dir_birth', 'DirBirth', 'BIGINT', true, null, 0);
        $this->addColumn('dir_city', 'DirCity', 'LONGVARCHAR', true, null, '');
        $this->addColumn('postal_account', 'PostalAccount', 'BIGINT', false, null, null);
        $this->addColumn('ateco_code', 'AtecoCode', 'LONGVARCHAR', false, null, null);
        $this->addColumn('activity_code', 'ActivityCode', 'LONGVARCHAR', false, null, null);
        $this->addColumn('dir_curr_addr', 'DirCurrAddr', 'LONGVARCHAR', true, null, '');
        $this->addColumn('dir_curr_city', 'DirCurrCity', 'LONGVARCHAR', true, null, '');
        $this->addColumn('dir_curr_phone', 'DirCurrPhone', 'LONGVARCHAR', true, null, '');
        $this->addForeignKey('dir_emp_id', 'DirEmpId', 'INTEGER', 'employee', 'employee_id', true, null, 0);
        $this->addColumn('adir_emp_id', 'AdirEmpId', 'INTEGER', true, null, 0);
        $this->addColumn('presge_emp_id', 'PresgeEmpId', 'INTEGER', true, null, 0);
        $this->addColumn('segcons_emp_id', 'SegconsEmpId', 'INTEGER', true, null, 0);
        $this->addColumn('prescon_emp_id', 'PresconEmpId', 'INTEGER', true, null, 0);
        $this->addColumn('respacq_emp_id', 'RespacqEmpId', 'INTEGER', true, null, 0);
        $this->addColumn('job_director_id', 'JobDirectorId', 'INTEGER', true, null, 0);
        $this->addColumn('job_vice_director_id', 'JobViceDirectorId', 'INTEGER', true, null, 0);
        $this->addColumn('job_dsga_id', 'JobDSGAId', 'INTEGER', true, null, 0);
        $this->addColumn('job_personnel_id', 'JobPersonnelId', 'INTEGER', true, null, 0);
        $this->addColumn('job_accounting_id', 'JobAccountingId', 'INTEGER', true, null, 0);
        $this->addColumn('job_warehouse_id', 'JobWarehouseId', 'INTEGER', true, null, 0);
        $this->addColumn('job_registry_id', 'JobRegistryId', 'INTEGER', true, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('Contact', 'Core\\Contact', RelationMap::MANY_TO_ONE, array('contact_id' => 'contact_id', ), 'SET NULL', null);
        $this->addRelation('Employee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('dir_emp_id' => 'employee_id', ), 'SET NULL', null);
        $this->addRelation('InstituteBankProfile', 'Core\\InstituteBankProfile', RelationMap::ONE_TO_MANY, array('institute_id' => 'institute', ), 'CASCADE', null, 'InstituteBankProfiles');
    } // buildRelations()

} // InstituteTableMap
