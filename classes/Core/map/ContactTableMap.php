<?php

namespace Core\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'contact' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Core.map
 */
class ContactTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Core.map.ContactTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('contact');
        $this->setPhpName('Contact');
        $this->setClassname('Core\\Contact');
        $this->setPackage('Core');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('contact_id_seq');
        // columns
        $this->addPrimaryKey('contact_id', 'ContactId', 'INTEGER', true, null, null);
        $this->addColumn('address', 'Address', 'LONGVARCHAR', false, null, null);
        $this->addColumn('phone_num', 'PhoneNum', 'LONGVARCHAR', false, null, null);
        $this->addColumn('fax', 'Fax', 'LONGVARCHAR', false, null, null);
        $this->addForeignKey('city_id', 'CityId', 'INTEGER', 'cities', 'city_id', false, null, null);
        $this->addColumn('email', 'Email', 'LONGVARCHAR', false, null, null);
        $this->addColumn('mobile', 'Mobile', 'LONGVARCHAR', false, null, null);
        $this->addColumn('web', 'Web', 'LONGVARCHAR', false, null, null);
        $this->addColumn('cap', 'Cap', 'VARCHAR', false, 5, '');
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('CityKey', 'Core\\Cities', RelationMap::MANY_TO_ONE, array('city_id' => 'city_id', ), 'SET NULL', null);
        $this->addRelation('Institute', 'Core\\Institute', RelationMap::ONE_TO_MANY, array('contact_id' => 'contact_id', ), 'SET NULL', null, 'Institutes');
        $this->addRelation('EmployeeRelatedByResidenceId', 'Employee\\Employee', RelationMap::ONE_TO_MANY, array('contact_id' => 'residence_id', ), 'CASCADE', null, 'EmployeesRelatedByResidenceId');
        $this->addRelation('EmployeeRelatedByAddressId', 'Employee\\Employee', RelationMap::ONE_TO_MANY, array('contact_id' => 'address_id', ), 'CASCADE', null, 'EmployeesRelatedByAddressId');
    } // buildRelations()

} // ContactTableMap
