<?php

namespace Core;

/**
 * The class manage filtering by filed__op standar.
 * "field" is a field in database and
 * "op" the operation to apply
 * It does no check about correct name of field or if they are present
 * into original table to search on.
 *
 * @package    mc2api.Core.FilterCompiler

 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * @license    http://www.mastertraining.it/license MTSRL License
 * @version    Release: @package_version@
 * @link       http://www.mastertraining.it/products/PackageName
 * <AUTHOR>
 */
class FilterCompiler {

	private $_arrayFilter = array(); // Array contain mix of date need to filter with relative comparison
	public $input = array(); // Array passing. Ex. array('name_eq','Marco')

	public function __construct($input) {
		$this->input = $input ? $input : array();
	}

	/**
	 * Convert string opration passing tipically by client, to a string readable
	 * for Propel
	 * @param string $op
	 * @return string
	 */
	public function convertOperation($op) {
		switch ($op) {
			case 'exact':
				$opConverted = \Criteria::EQUAL;
				break;
			case 'lt':
				$opConverted = \Criteria::LESS_THAN;
				break;
			case 'lte':
				$opConverted = \Criteria::LESS_EQUAL;
				break;
			case 'gt':
				$opConverted = \Criteria::GREATER_THAN;
				break;
			case 'gte':
				$opConverted = \Criteria::GREATER_EQUAL;
				break;
			case 'like':
				$opConverted = \Criteria::LIKE;
				break;
			case 'ilike':
				$opConverted = \Criteria::ILIKE;
				break;
			default:
				$opConverted = \Criteria::EQUAL;
				break;
		}
		return $opConverted;
	}

	/**
	 * Build filter array
	 * @return objects
	 */
	public function compile() {
		foreach ($this->input as $cond => $value) {
			if (!$value) {
				continue;
			}
			$key = explode('__', $cond);
			$field = $key[0];
			$op = $this->convertOperation(isset($key[1]) ? $key[1] : null);
			$this->_arrayFilter[] = array(
				'field'	 => $field,
				'op'	 => $op,
				'value'	 => $value
			);
		}
		return $this;
	}

	/**
	 * Get the compiler filter array
	 * @return Array
	 */
	public function getFilter() {
		return $this->_arrayFilter;
	}

}

