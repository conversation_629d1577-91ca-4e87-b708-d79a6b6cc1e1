<?php

namespace Ccp\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\Studenti;
use Ccp\StudentiPeer;
use Ccp\StudentiQuery;
use Ccp\Tasse;

/**
 * Base class that represents a query for the 'studenti' table.
 *
 *
 *
 * @method StudentiQuery orderByIdStudente($order = Criteria::ASC) Order by the id_studente column
 * @method StudentiQuery orderByNome($order = Criteria::ASC) Order by the nome column
 * @method StudentiQuery orderByCognome($order = Criteria::ASC) Order by the cognome column
 * @method StudentiQuery orderByIndirizzo($order = Criteria::ASC) Order by the indirizzo column
 * @method StudentiQuery orderByCitta($order = Criteria::ASC) Order by the citta column
 * @method StudentiQuery orderByCap($order = Criteria::ASC) Order by the cap column
 * @method StudentiQuery orderByProvincia($order = Criteria::ASC) Order by the provincia column
 * @method StudentiQuery orderBySesso($order = Criteria::ASC) Order by the sesso column
 * @method StudentiQuery orderByTelefono($order = Criteria::ASC) Order by the telefono column
 * @method StudentiQuery orderByCellulare1($order = Criteria::ASC) Order by the cellulare1 column
 * @method StudentiQuery orderByCellulare2($order = Criteria::ASC) Order by the cellulare2 column
 * @method StudentiQuery orderByEmail1($order = Criteria::ASC) Order by the email1 column
 * @method StudentiQuery orderByEmail2($order = Criteria::ASC) Order by the email2 column
 * @method StudentiQuery orderByInvioEmail($order = Criteria::ASC) Order by the invio_email column
 * @method StudentiQuery orderByInvioEmailCumulativo($order = Criteria::ASC) Order by the invio_email_cumulativo column
 * @method StudentiQuery orderByInvioEmailParametrico($order = Criteria::ASC) Order by the invio_email_parametrico column
 * @method StudentiQuery orderByInvioEmailTemporale($order = Criteria::ASC) Order by the invio_email_temporale column
 * @method StudentiQuery orderByTipoSms($order = Criteria::ASC) Order by the tipo_sms column
 * @method StudentiQuery orderByTipoSmsCumulativo($order = Criteria::ASC) Order by the tipo_sms_cumulativo column
 * @method StudentiQuery orderByTipoSmsParametrico($order = Criteria::ASC) Order by the tipo_sms_parametrico column
 * @method StudentiQuery orderByTipoSmsTemporale($order = Criteria::ASC) Order by the tipo_sms_temporale column
 * @method StudentiQuery orderByAutEntrataRitardo($order = Criteria::ASC) Order by the aut_entrata_ritardo column
 * @method StudentiQuery orderByAutUscitaAnticipo($order = Criteria::ASC) Order by the aut_uscita_anticipo column
 * @method StudentiQuery orderByAutPomeriggio($order = Criteria::ASC) Order by the aut_pomeriggio column
 * @method StudentiQuery orderByAcconsente($order = Criteria::ASC) Order by the acconsente column
 * @method StudentiQuery orderByRitirato($order = Criteria::ASC) Order by the ritirato column
 * @method StudentiQuery orderByDataNascita($order = Criteria::ASC) Order by the data_nascita column
 * @method StudentiQuery orderByCodiceStudente($order = Criteria::ASC) Order by the codice_studente column
 * @method StudentiQuery orderByPasswordStudente($order = Criteria::ASC) Order by the password_studente column
 * @method StudentiQuery orderByCodiceGiustificazioniStudente($order = Criteria::ASC) Order by the codice_giustificazioni_studente column
 * @method StudentiQuery orderByEsoneroReligione($order = Criteria::ASC) Order by the esonero_religione column
 * @method StudentiQuery orderByMateriaSostitutivaReligione($order = Criteria::ASC) Order by the materia_sostitutiva_religione column
 * @method StudentiQuery orderByEsoneroEdFisica($order = Criteria::ASC) Order by the esonero_ed_fisica column
 * @method StudentiQuery orderByMateriaSostitutivaEdfisica($order = Criteria::ASC) Order by the materia_sostitutiva_edfisica column
 * @method StudentiQuery orderByCreditiTerza($order = Criteria::ASC) Order by the crediti_terza column
 * @method StudentiQuery orderByMediaVotiTerza($order = Criteria::ASC) Order by the media_voti_terza column
 * @method StudentiQuery orderByDebitiTerza($order = Criteria::ASC) Order by the debiti_terza column
 * @method StudentiQuery orderByCreditiSospesiTerza($order = Criteria::ASC) Order by the crediti_sospesi_terza column
 * @method StudentiQuery orderByCreditiReintegratiTerza($order = Criteria::ASC) Order by the crediti_reintegrati_terza column
 * @method StudentiQuery orderByCreditiQuarta($order = Criteria::ASC) Order by the crediti_quarta column
 * @method StudentiQuery orderByMediaVotiQuarta($order = Criteria::ASC) Order by the media_voti_quarta column
 * @method StudentiQuery orderByDebitiQuarta($order = Criteria::ASC) Order by the debiti_quarta column
 * @method StudentiQuery orderByCreditiSospesiQuarta($order = Criteria::ASC) Order by the crediti_sospesi_quarta column
 * @method StudentiQuery orderByCreditiReintegratiQuarta($order = Criteria::ASC) Order by the crediti_reintegrati_quarta column
 * @method StudentiQuery orderByCreditiQuinta($order = Criteria::ASC) Order by the crediti_quinta column
 * @method StudentiQuery orderByMediaVotiQuinta($order = Criteria::ASC) Order by the media_voti_quinta column
 * @method StudentiQuery orderByCreditiFinaliAgg($order = Criteria::ASC) Order by the crediti_finali_agg column
 * @method StudentiQuery orderByMatricola($order = Criteria::ASC) Order by the matricola column
 * @method StudentiQuery orderByLuogoNascita($order = Criteria::ASC) Order by the luogo_nascita column
 * @method StudentiQuery orderByProvinciaNascita($order = Criteria::ASC) Order by the provincia_nascita column
 * @method StudentiQuery orderByMotiviCreditiTerza($order = Criteria::ASC) Order by the motivi_crediti_terza column
 * @method StudentiQuery orderByMotiviCreditiQuarta($order = Criteria::ASC) Order by the motivi_crediti_quarta column
 * @method StudentiQuery orderByMotiviCreditiQuinta($order = Criteria::ASC) Order by the motivi_crediti_quinta column
 * @method StudentiQuery orderByMotiviCreditiAgg($order = Criteria::ASC) Order by the motivi_crediti_agg column
 * @method StudentiQuery orderByCodiceComuneNascita($order = Criteria::ASC) Order by the codice_comune_nascita column
 * @method StudentiQuery orderByStatoNascita($order = Criteria::ASC) Order by the stato_nascita column
 * @method StudentiQuery orderByCittadinanza($order = Criteria::ASC) Order by the cittadinanza column
 * @method StudentiQuery orderBySecondaCittadinanza($order = Criteria::ASC) Order by the seconda_cittadinanza column
 * @method StudentiQuery orderByCodiceComuneResidenza($order = Criteria::ASC) Order by the codice_comune_residenza column
 * @method StudentiQuery orderByDistretto($order = Criteria::ASC) Order by the distretto column
 * @method StudentiQuery orderByCodiceFiscale($order = Criteria::ASC) Order by the codice_fiscale column
 * @method StudentiQuery orderByMedico($order = Criteria::ASC) Order by the medico column
 * @method StudentiQuery orderByTelefonoMedico($order = Criteria::ASC) Order by the telefono_medico column
 * @method StudentiQuery orderByIntolleranzeAlim($order = Criteria::ASC) Order by the intolleranze_alim column
 * @method StudentiQuery orderByGruppoSanguigno($order = Criteria::ASC) Order by the gruppo_sanguigno column
 * @method StudentiQuery orderByGruppoRh($order = Criteria::ASC) Order by the gruppo_rh column
 * @method StudentiQuery orderByCodiceAsl($order = Criteria::ASC) Order by the codice_asl column
 * @method StudentiQuery orderByAnnotazioni($order = Criteria::ASC) Order by the annotazioni column
 * @method StudentiQuery orderByStatoCivile($order = Criteria::ASC) Order by the stato_civile column
 * @method StudentiQuery orderByVotoPrimoScritto($order = Criteria::ASC) Order by the voto_primo_scritto column
 * @method StudentiQuery orderByVotoSecondoScritto($order = Criteria::ASC) Order by the voto_secondo_scritto column
 * @method StudentiQuery orderByVotoTerzoScritto($order = Criteria::ASC) Order by the voto_terzo_scritto column
 * @method StudentiQuery orderByVotoOrale($order = Criteria::ASC) Order by the voto_orale column
 * @method StudentiQuery orderByVotoBonus($order = Criteria::ASC) Order by the voto_bonus column
 * @method StudentiQuery orderByMateriaSecondoScr($order = Criteria::ASC) Order by the materia_secondo_scr column
 * @method StudentiQuery orderByUlterioriSpecifDiploma($order = Criteria::ASC) Order by the ulteriori_specif_diploma column
 * @method StudentiQuery orderByNumeroDiploma($order = Criteria::ASC) Order by the numero_diploma column
 * @method StudentiQuery orderByChiInserisce($order = Criteria::ASC) Order by the chi_inserisce column
 * @method StudentiQuery orderByDataInserimento($order = Criteria::ASC) Order by the data_inserimento column
 * @method StudentiQuery orderByTipoInserimento($order = Criteria::ASC) Order by the tipo_inserimento column
 * @method StudentiQuery orderByChiModifica($order = Criteria::ASC) Order by the chi_modifica column
 * @method StudentiQuery orderByDataModifica($order = Criteria::ASC) Order by the data_modifica column
 * @method StudentiQuery orderByTipoModifica($order = Criteria::ASC) Order by the tipo_modifica column
 * @method StudentiQuery orderByFlagCanc($order = Criteria::ASC) Order by the flag_canc column
 * @method StudentiQuery orderByStatoAvanzamento($order = Criteria::ASC) Order by the stato_avanzamento column
 * @method StudentiQuery orderByDataStatoAvanzamento($order = Criteria::ASC) Order by the data_stato_avanzamento column
 * @method StudentiQuery orderByCapProvinciaNascita($order = Criteria::ASC) Order by the cap_provincia_nascita column
 * @method StudentiQuery orderByBadge($order = Criteria::ASC) Order by the badge column
 * @method StudentiQuery orderByCapResidenza($order = Criteria::ASC) Order by the cap_residenza column
 * @method StudentiQuery orderByCodiceComuneDomicilio($order = Criteria::ASC) Order by the codice_comune_domicilio column
 * @method StudentiQuery orderByCapDomicilio($order = Criteria::ASC) Order by the cap_domicilio column
 * @method StudentiQuery orderByCapNascita($order = Criteria::ASC) Order by the cap_nascita column
 * @method StudentiQuery orderByIndirizzoDomicilio($order = Criteria::ASC) Order by the indirizzo_domicilio column
 * @method StudentiQuery orderByCittaNascitaStraniera($order = Criteria::ASC) Order by the citta_nascita_straniera column
 * @method StudentiQuery orderByCellulareAllievo($order = Criteria::ASC) Order by the cellulare_allievo column
 * @method StudentiQuery orderByHandicap($order = Criteria::ASC) Order by the handicap column
 * @method StudentiQuery orderByStatoConvittore($order = Criteria::ASC) Order by the stato_convittore column
 * @method StudentiQuery orderByDataRitiro($order = Criteria::ASC) Order by the data_ritiro column
 * @method StudentiQuery orderByVotoAmmissione($order = Criteria::ASC) Order by the voto_ammissione column
 * @method StudentiQuery orderByDifferenzaPunteggio($order = Criteria::ASC) Order by the differenza_punteggio column
 * @method StudentiQuery orderByVotoQualifica($order = Criteria::ASC) Order by the voto_qualifica column
 * @method StudentiQuery orderByVotoEsameSc1Qual($order = Criteria::ASC) Order by the voto_esame_sc1_qual column
 * @method StudentiQuery orderByVotoEsameSc2Qual($order = Criteria::ASC) Order by the voto_esame_sc2_qual column
 * @method StudentiQuery orderByVotoEsameOrQual($order = Criteria::ASC) Order by the voto_esame_or_qual column
 * @method StudentiQuery orderByStatoPrivatista($order = Criteria::ASC) Order by the stato_privatista column
 * @method StudentiQuery orderByFoto($order = Criteria::ASC) Order by the foto column
 * @method StudentiQuery orderByRappresentante($order = Criteria::ASC) Order by the rappresentante column
 * @method StudentiQuery orderByObbligoFormativo($order = Criteria::ASC) Order by the obbligo_formativo column
 * @method StudentiQuery orderByIdLingua1($order = Criteria::ASC) Order by the id_lingua_1 column
 * @method StudentiQuery orderByIdLingua2($order = Criteria::ASC) Order by the id_lingua_2 column
 * @method StudentiQuery orderByIdLingua3($order = Criteria::ASC) Order by the id_lingua_3 column
 * @method StudentiQuery orderByIdLingua4($order = Criteria::ASC) Order by the id_lingua_4 column
 * @method StudentiQuery orderByIdLingua5($order = Criteria::ASC) Order by the id_lingua_5 column
 * @method StudentiQuery orderByIdProvenienzaScolastica($order = Criteria::ASC) Order by the id_provenienza_scolastica column
 * @method StudentiQuery orderByIdScuolaMedia($order = Criteria::ASC) Order by the id_scuola_media column
 * @method StudentiQuery orderByLinguaScuolaMedia($order = Criteria::ASC) Order by the lingua_scuola_media column
 * @method StudentiQuery orderByLinguaScuolaMedia2($order = Criteria::ASC) Order by the lingua_scuola_media_2 column
 * @method StudentiQuery orderByGiudizioScuolaMedia($order = Criteria::ASC) Order by the giudizio_scuola_media column
 * @method StudentiQuery orderByTrasporto($order = Criteria::ASC) Order by the trasporto column
 * @method StudentiQuery orderByDataIscrizione($order = Criteria::ASC) Order by the data_iscrizione column
 * @method StudentiQuery orderByPei($order = Criteria::ASC) Order by the pei column
 * @method StudentiQuery orderByAmmessoEsameQualifica($order = Criteria::ASC) Order by the ammesso_esame_qualifica column
 * @method StudentiQuery orderByAmmessoEsameQuinta($order = Criteria::ASC) Order by the ammesso_esame_quinta column
 * @method StudentiQuery orderByGiudizioAmmissioneQuinta($order = Criteria::ASC) Order by the giudizio_ammissione_quinta column
 * @method StudentiQuery orderByGradoHandicap($order = Criteria::ASC) Order by the grado_handicap column
 * @method StudentiQuery orderByTipoHandicap($order = Criteria::ASC) Order by the tipo_handicap column
 * @method StudentiQuery orderByStatoLicenzaMaestro($order = Criteria::ASC) Order by the stato_licenza_maestro column
 * @method StudentiQuery orderByIdStudenteSissi($order = Criteria::ASC) Order by the id_studente_sissi column
 * @method StudentiQuery orderByBadgeRfid($order = Criteria::ASC) Order by the badge_rfid column
 * @method StudentiQuery orderByLode($order = Criteria::ASC) Order by the lode column
 * @method StudentiQuery orderByDistrettoScolastico($order = Criteria::ASC) Order by the distretto_scolastico column
 * @method StudentiQuery orderByGiudizioAmmissioneTerza($order = Criteria::ASC) Order by the giudizio_ammissione_terza column
 * @method StudentiQuery orderByEsitoPrimaMedia($order = Criteria::ASC) Order by the esito_prima_media column
 * @method StudentiQuery orderByEsitoSecondaMedia($order = Criteria::ASC) Order by the esito_seconda_media column
 * @method StudentiQuery orderByEsitoTerzaMedia($order = Criteria::ASC) Order by the esito_terza_media column
 * @method StudentiQuery orderByGiudizioEsameSc1Qual($order = Criteria::ASC) Order by the giudizio_esame_sc1_qual column
 * @method StudentiQuery orderByGiudizioEsameSc2Qual($order = Criteria::ASC) Order by the giudizio_esame_sc2_qual column
 * @method StudentiQuery orderByGiudizioEsameOrQual($order = Criteria::ASC) Order by the giudizio_esame_or_qual column
 * @method StudentiQuery orderByGiudizioComplessivoEsameQual($order = Criteria::ASC) Order by the giudizio_complessivo_esame_qual column
 * @method StudentiQuery orderByAcconsenteAziende($order = Criteria::ASC) Order by the acconsente_aziende column
 * @method StudentiQuery orderByCurriculumPrima($order = Criteria::ASC) Order by the curriculum_prima column
 * @method StudentiQuery orderByCurriculumSeconda($order = Criteria::ASC) Order by the curriculum_seconda column
 * @method StudentiQuery orderByStageProfessionali($order = Criteria::ASC) Order by the stage_professionali column
 * @method StudentiQuery orderByDataOrale($order = Criteria::ASC) Order by the data_orale column
 * @method StudentiQuery orderByOrdineEsameOrale($order = Criteria::ASC) Order by the ordine_esame_orale column
 * @method StudentiQuery orderByTipoPrimoScritto($order = Criteria::ASC) Order by the tipo_primo_scritto column
 * @method StudentiQuery orderByTipoSecondoScritto($order = Criteria::ASC) Order by the tipo_secondo_scritto column
 * @method StudentiQuery orderByTipoTerzoScritto($order = Criteria::ASC) Order by the tipo_terzo_scritto column
 * @method StudentiQuery orderByUnanimitaPrimoScritto($order = Criteria::ASC) Order by the unanimita_primo_scritto column
 * @method StudentiQuery orderByUnanimitaSecondoScritto($order = Criteria::ASC) Order by the unanimita_secondo_scritto column
 * @method StudentiQuery orderByUnanimitaTerzoScritto($order = Criteria::ASC) Order by the unanimita_terzo_scritto column
 * @method StudentiQuery orderByArgomentoSceltoOrale($order = Criteria::ASC) Order by the argomento_scelto_orale column
 * @method StudentiQuery orderByAreaDisc1Orale($order = Criteria::ASC) Order by the area_disc_1_orale column
 * @method StudentiQuery orderByAreaDisc2Orale($order = Criteria::ASC) Order by the area_disc_2_orale column
 * @method StudentiQuery orderByDiscElaboratiOrale($order = Criteria::ASC) Order by the disc_elaborati_orale column
 * @method StudentiQuery orderByUnanimitaVotoFinale($order = Criteria::ASC) Order by the unanimita_voto_finale column
 * @method StudentiQuery orderByPresenteEsameQuinta($order = Criteria::ASC) Order by the presente_esame_quinta column
 * @method StudentiQuery orderByStampaBadge($order = Criteria::ASC) Order by the stampa_badge column
 * @method StudentiQuery orderByIdClasseDestinazione($order = Criteria::ASC) Order by the id_classe_destinazione column
 * @method StudentiQuery orderByScontoRette($order = Criteria::ASC) Order by the sconto_rette column
 * @method StudentiQuery orderByCartaStudenteNumero($order = Criteria::ASC) Order by the carta_studente_numero column
 * @method StudentiQuery orderByCartaStudenteScadenza($order = Criteria::ASC) Order by the carta_studente_scadenza column
 * @method StudentiQuery orderByEsitoCorrenteCalcolato($order = Criteria::ASC) Order by the esito_corrente_calcolato column
 * @method StudentiQuery orderByIdFlusso($order = Criteria::ASC) Order by the id_flusso column
 * @method StudentiQuery orderByDataAggiornamentoSogei($order = Criteria::ASC) Order by the data_aggiornamento_sogei column
 * @method StudentiQuery orderByCodiceAlunnoMinisteriale($order = Criteria::ASC) Order by the codice_alunno_ministeriale column
 * @method StudentiQuery orderByFlagCfFittizio($order = Criteria::ASC) Order by the flag_cf_fittizio column
 * @method StudentiQuery orderByFlagS2f($order = Criteria::ASC) Order by the flag_s2f column
 * @method StudentiQuery orderByCodiceStatoSogei($order = Criteria::ASC) Order by the codice_stato_sogei column
 * @method StudentiQuery orderByCodiceGruppoNomade($order = Criteria::ASC) Order by the codice_gruppo_nomade column
 * @method StudentiQuery orderByFlagMinoreStraniero($order = Criteria::ASC) Order by the flag_minore_straniero column
 * @method StudentiQuery orderByChiave($order = Criteria::ASC) Order by the chiave column
 * @method StudentiQuery orderByVotoEsameMedieItaliano($order = Criteria::ASC) Order by the voto_esame_medie_italiano column
 * @method StudentiQuery orderByVotoEsameMedieInglese($order = Criteria::ASC) Order by the voto_esame_medie_inglese column
 * @method StudentiQuery orderByVotoEsameMedieMatematica($order = Criteria::ASC) Order by the voto_esame_medie_matematica column
 * @method StudentiQuery orderByVotoEsameMedieSecondaLingua($order = Criteria::ASC) Order by the voto_esame_medie_seconda_lingua column
 * @method StudentiQuery orderByVotoEsameMedieInvalsiIta($order = Criteria::ASC) Order by the voto_esame_medie_invalsi_ita column
 * @method StudentiQuery orderByVotoEsameMedieInvalsiMat($order = Criteria::ASC) Order by the voto_esame_medie_invalsi_mat column
 * @method StudentiQuery orderByVotoEsameMedieOrale($order = Criteria::ASC) Order by the voto_esame_medie_orale column
 * @method StudentiQuery orderByVotoAmmissioneMedie($order = Criteria::ASC) Order by the voto_ammissione_medie column
 * @method StudentiQuery orderByEsitoPrimaElementare($order = Criteria::ASC) Order by the esito_prima_elementare column
 * @method StudentiQuery orderByEsitoSecondaElementare($order = Criteria::ASC) Order by the esito_seconda_elementare column
 * @method StudentiQuery orderByEsitoTerzaElementare($order = Criteria::ASC) Order by the esito_terza_elementare column
 * @method StudentiQuery orderByEsitoQuartaElementare($order = Criteria::ASC) Order by the esito_quarta_elementare column
 * @method StudentiQuery orderByEsitoQuintaElementare($order = Criteria::ASC) Order by the esito_quinta_elementare column
 * @method StudentiQuery orderByTipoVotoEsameMedieItaliano($order = Criteria::ASC) Order by the tipo_voto_esame_medie_italiano column
 * @method StudentiQuery orderByTipoVotoEsameMedieInglese($order = Criteria::ASC) Order by the tipo_voto_esame_medie_inglese column
 * @method StudentiQuery orderByGiudizio1Medie($order = Criteria::ASC) Order by the giudizio_1_medie column
 * @method StudentiQuery orderByGiudizio2Medie($order = Criteria::ASC) Order by the giudizio_2_medie column
 * @method StudentiQuery orderByGiudizio3Medie($order = Criteria::ASC) Order by the giudizio_3_medie column
 * @method StudentiQuery orderByArgomentiOraliMedie($order = Criteria::ASC) Order by the argomenti_orali_medie column
 * @method StudentiQuery orderByGiudizioFinale1Medie($order = Criteria::ASC) Order by the giudizio_finale_1_medie column
 * @method StudentiQuery orderByGiudizioFinale2Medie($order = Criteria::ASC) Order by the giudizio_finale_2_medie column
 * @method StudentiQuery orderByGiudizioFinale3Medie($order = Criteria::ASC) Order by the giudizio_finale_3_medie column
 * @method StudentiQuery orderByConsiglioTerzaMedia($order = Criteria::ASC) Order by the consiglio_terza_media column
 * @method StudentiQuery orderByGiudizioSinteticoEsameTerzaMedia($order = Criteria::ASC) Order by the giudizio_sintetico_esame_terza_media column
 * @method StudentiQuery orderByDataArrivoInItalia($order = Criteria::ASC) Order by the data_arrivo_in_italia column
 * @method StudentiQuery orderByFrequenzaAsiloNido($order = Criteria::ASC) Order by the frequenza_asilo_nido column
 * @method StudentiQuery orderByFrequenzaScuolaMaterna($order = Criteria::ASC) Order by the frequenza_scuola_materna column
 * @method StudentiQuery orderByDataAggiornamentoSidi($order = Criteria::ASC) Order by the data_aggiornamento_sidi column
 * @method StudentiQuery orderByCmpSupValIta($order = Criteria::ASC) Order by the cmp_sup_val_ita column
 * @method StudentiQuery orderByCmpSupTxtIta($order = Criteria::ASC) Order by the cmp_sup_txt_ita column
 * @method StudentiQuery orderByCmpSupValIng($order = Criteria::ASC) Order by the cmp_sup_val_ing column
 * @method StudentiQuery orderByCmpSupTxtIng($order = Criteria::ASC) Order by the cmp_sup_txt_ing column
 * @method StudentiQuery orderByCmpSupValAltri($order = Criteria::ASC) Order by the cmp_sup_val_altri column
 * @method StudentiQuery orderByCmpSupTxtAltri($order = Criteria::ASC) Order by the cmp_sup_txt_altri column
 * @method StudentiQuery orderByCmpSupValMat($order = Criteria::ASC) Order by the cmp_sup_val_mat column
 * @method StudentiQuery orderByCmpSupTxtMat($order = Criteria::ASC) Order by the cmp_sup_txt_mat column
 * @method StudentiQuery orderByCmpSupValSciTec($order = Criteria::ASC) Order by the cmp_sup_val_sci_tec column
 * @method StudentiQuery orderByCmpSupTxtSciTec($order = Criteria::ASC) Order by the cmp_sup_txt_sci_tec column
 * @method StudentiQuery orderByCmpSupValStoSoc($order = Criteria::ASC) Order by the cmp_sup_val_sto_soc column
 * @method StudentiQuery orderByCmpSupTxtStoSoc($order = Criteria::ASC) Order by the cmp_sup_txt_sto_soc column
 * @method StudentiQuery orderByCmpMedValIta($order = Criteria::ASC) Order by the cmp_med_val_ita column
 * @method StudentiQuery orderByCmpMedTxtIta($order = Criteria::ASC) Order by the cmp_med_txt_ita column
 * @method StudentiQuery orderByCmpMedValIng($order = Criteria::ASC) Order by the cmp_med_val_ing column
 * @method StudentiQuery orderByCmpMedTxtIng($order = Criteria::ASC) Order by the cmp_med_txt_ing column
 * @method StudentiQuery orderByCmpMedValAltri($order = Criteria::ASC) Order by the cmp_med_val_altri column
 * @method StudentiQuery orderByCmpMedTxtAltri($order = Criteria::ASC) Order by the cmp_med_txt_altri column
 * @method StudentiQuery orderByCmpMedValMat($order = Criteria::ASC) Order by the cmp_med_val_mat column
 * @method StudentiQuery orderByCmpMedTxtMat($order = Criteria::ASC) Order by the cmp_med_txt_mat column
 * @method StudentiQuery orderByCmpMedValSciTec($order = Criteria::ASC) Order by the cmp_med_val_sci_tec column
 * @method StudentiQuery orderByCmpMedTxtSciTec($order = Criteria::ASC) Order by the cmp_med_txt_sci_tec column
 * @method StudentiQuery orderByCmpMedValStoSoc($order = Criteria::ASC) Order by the cmp_med_val_sto_soc column
 * @method StudentiQuery orderByCmpMedTxtStoSoc($order = Criteria::ASC) Order by the cmp_med_txt_sto_soc column
 * @method StudentiQuery orderByCmpMedValL2($order = Criteria::ASC) Order by the cmp_med_val_l2 column
 * @method StudentiQuery orderByCmpMedTxtL2($order = Criteria::ASC) Order by the cmp_med_txt_l2 column
 * @method StudentiQuery orderByCmpMedValL3($order = Criteria::ASC) Order by the cmp_med_val_l3 column
 * @method StudentiQuery orderByCmpMedTxtL3($order = Criteria::ASC) Order by the cmp_med_txt_l3 column
 * @method StudentiQuery orderByCmpMedValArte($order = Criteria::ASC) Order by the cmp_med_val_arte column
 * @method StudentiQuery orderByCmpMedTxtArte($order = Criteria::ASC) Order by the cmp_med_txt_arte column
 * @method StudentiQuery orderByCmpMedValMus($order = Criteria::ASC) Order by the cmp_med_val_mus column
 * @method StudentiQuery orderByCmpMedTxtMus($order = Criteria::ASC) Order by the cmp_med_txt_mus column
 * @method StudentiQuery orderByCmpMedValMot($order = Criteria::ASC) Order by the cmp_med_val_mot column
 * @method StudentiQuery orderByCmpMedTxtMot($order = Criteria::ASC) Order by the cmp_med_txt_mot column
 *
 * @method StudentiQuery groupByIdStudente() Group by the id_studente column
 * @method StudentiQuery groupByNome() Group by the nome column
 * @method StudentiQuery groupByCognome() Group by the cognome column
 * @method StudentiQuery groupByIndirizzo() Group by the indirizzo column
 * @method StudentiQuery groupByCitta() Group by the citta column
 * @method StudentiQuery groupByCap() Group by the cap column
 * @method StudentiQuery groupByProvincia() Group by the provincia column
 * @method StudentiQuery groupBySesso() Group by the sesso column
 * @method StudentiQuery groupByTelefono() Group by the telefono column
 * @method StudentiQuery groupByCellulare1() Group by the cellulare1 column
 * @method StudentiQuery groupByCellulare2() Group by the cellulare2 column
 * @method StudentiQuery groupByEmail1() Group by the email1 column
 * @method StudentiQuery groupByEmail2() Group by the email2 column
 * @method StudentiQuery groupByInvioEmail() Group by the invio_email column
 * @method StudentiQuery groupByInvioEmailCumulativo() Group by the invio_email_cumulativo column
 * @method StudentiQuery groupByInvioEmailParametrico() Group by the invio_email_parametrico column
 * @method StudentiQuery groupByInvioEmailTemporale() Group by the invio_email_temporale column
 * @method StudentiQuery groupByTipoSms() Group by the tipo_sms column
 * @method StudentiQuery groupByTipoSmsCumulativo() Group by the tipo_sms_cumulativo column
 * @method StudentiQuery groupByTipoSmsParametrico() Group by the tipo_sms_parametrico column
 * @method StudentiQuery groupByTipoSmsTemporale() Group by the tipo_sms_temporale column
 * @method StudentiQuery groupByAutEntrataRitardo() Group by the aut_entrata_ritardo column
 * @method StudentiQuery groupByAutUscitaAnticipo() Group by the aut_uscita_anticipo column
 * @method StudentiQuery groupByAutPomeriggio() Group by the aut_pomeriggio column
 * @method StudentiQuery groupByAcconsente() Group by the acconsente column
 * @method StudentiQuery groupByRitirato() Group by the ritirato column
 * @method StudentiQuery groupByDataNascita() Group by the data_nascita column
 * @method StudentiQuery groupByCodiceStudente() Group by the codice_studente column
 * @method StudentiQuery groupByPasswordStudente() Group by the password_studente column
 * @method StudentiQuery groupByCodiceGiustificazioniStudente() Group by the codice_giustificazioni_studente column
 * @method StudentiQuery groupByEsoneroReligione() Group by the esonero_religione column
 * @method StudentiQuery groupByMateriaSostitutivaReligione() Group by the materia_sostitutiva_religione column
 * @method StudentiQuery groupByEsoneroEdFisica() Group by the esonero_ed_fisica column
 * @method StudentiQuery groupByMateriaSostitutivaEdfisica() Group by the materia_sostitutiva_edfisica column
 * @method StudentiQuery groupByCreditiTerza() Group by the crediti_terza column
 * @method StudentiQuery groupByMediaVotiTerza() Group by the media_voti_terza column
 * @method StudentiQuery groupByDebitiTerza() Group by the debiti_terza column
 * @method StudentiQuery groupByCreditiSospesiTerza() Group by the crediti_sospesi_terza column
 * @method StudentiQuery groupByCreditiReintegratiTerza() Group by the crediti_reintegrati_terza column
 * @method StudentiQuery groupByCreditiQuarta() Group by the crediti_quarta column
 * @method StudentiQuery groupByMediaVotiQuarta() Group by the media_voti_quarta column
 * @method StudentiQuery groupByDebitiQuarta() Group by the debiti_quarta column
 * @method StudentiQuery groupByCreditiSospesiQuarta() Group by the crediti_sospesi_quarta column
 * @method StudentiQuery groupByCreditiReintegratiQuarta() Group by the crediti_reintegrati_quarta column
 * @method StudentiQuery groupByCreditiQuinta() Group by the crediti_quinta column
 * @method StudentiQuery groupByMediaVotiQuinta() Group by the media_voti_quinta column
 * @method StudentiQuery groupByCreditiFinaliAgg() Group by the crediti_finali_agg column
 * @method StudentiQuery groupByMatricola() Group by the matricola column
 * @method StudentiQuery groupByLuogoNascita() Group by the luogo_nascita column
 * @method StudentiQuery groupByProvinciaNascita() Group by the provincia_nascita column
 * @method StudentiQuery groupByMotiviCreditiTerza() Group by the motivi_crediti_terza column
 * @method StudentiQuery groupByMotiviCreditiQuarta() Group by the motivi_crediti_quarta column
 * @method StudentiQuery groupByMotiviCreditiQuinta() Group by the motivi_crediti_quinta column
 * @method StudentiQuery groupByMotiviCreditiAgg() Group by the motivi_crediti_agg column
 * @method StudentiQuery groupByCodiceComuneNascita() Group by the codice_comune_nascita column
 * @method StudentiQuery groupByStatoNascita() Group by the stato_nascita column
 * @method StudentiQuery groupByCittadinanza() Group by the cittadinanza column
 * @method StudentiQuery groupBySecondaCittadinanza() Group by the seconda_cittadinanza column
 * @method StudentiQuery groupByCodiceComuneResidenza() Group by the codice_comune_residenza column
 * @method StudentiQuery groupByDistretto() Group by the distretto column
 * @method StudentiQuery groupByCodiceFiscale() Group by the codice_fiscale column
 * @method StudentiQuery groupByMedico() Group by the medico column
 * @method StudentiQuery groupByTelefonoMedico() Group by the telefono_medico column
 * @method StudentiQuery groupByIntolleranzeAlim() Group by the intolleranze_alim column
 * @method StudentiQuery groupByGruppoSanguigno() Group by the gruppo_sanguigno column
 * @method StudentiQuery groupByGruppoRh() Group by the gruppo_rh column
 * @method StudentiQuery groupByCodiceAsl() Group by the codice_asl column
 * @method StudentiQuery groupByAnnotazioni() Group by the annotazioni column
 * @method StudentiQuery groupByStatoCivile() Group by the stato_civile column
 * @method StudentiQuery groupByVotoPrimoScritto() Group by the voto_primo_scritto column
 * @method StudentiQuery groupByVotoSecondoScritto() Group by the voto_secondo_scritto column
 * @method StudentiQuery groupByVotoTerzoScritto() Group by the voto_terzo_scritto column
 * @method StudentiQuery groupByVotoOrale() Group by the voto_orale column
 * @method StudentiQuery groupByVotoBonus() Group by the voto_bonus column
 * @method StudentiQuery groupByMateriaSecondoScr() Group by the materia_secondo_scr column
 * @method StudentiQuery groupByUlterioriSpecifDiploma() Group by the ulteriori_specif_diploma column
 * @method StudentiQuery groupByNumeroDiploma() Group by the numero_diploma column
 * @method StudentiQuery groupByChiInserisce() Group by the chi_inserisce column
 * @method StudentiQuery groupByDataInserimento() Group by the data_inserimento column
 * @method StudentiQuery groupByTipoInserimento() Group by the tipo_inserimento column
 * @method StudentiQuery groupByChiModifica() Group by the chi_modifica column
 * @method StudentiQuery groupByDataModifica() Group by the data_modifica column
 * @method StudentiQuery groupByTipoModifica() Group by the tipo_modifica column
 * @method StudentiQuery groupByFlagCanc() Group by the flag_canc column
 * @method StudentiQuery groupByStatoAvanzamento() Group by the stato_avanzamento column
 * @method StudentiQuery groupByDataStatoAvanzamento() Group by the data_stato_avanzamento column
 * @method StudentiQuery groupByCapProvinciaNascita() Group by the cap_provincia_nascita column
 * @method StudentiQuery groupByBadge() Group by the badge column
 * @method StudentiQuery groupByCapResidenza() Group by the cap_residenza column
 * @method StudentiQuery groupByCodiceComuneDomicilio() Group by the codice_comune_domicilio column
 * @method StudentiQuery groupByCapDomicilio() Group by the cap_domicilio column
 * @method StudentiQuery groupByCapNascita() Group by the cap_nascita column
 * @method StudentiQuery groupByIndirizzoDomicilio() Group by the indirizzo_domicilio column
 * @method StudentiQuery groupByCittaNascitaStraniera() Group by the citta_nascita_straniera column
 * @method StudentiQuery groupByCellulareAllievo() Group by the cellulare_allievo column
 * @method StudentiQuery groupByHandicap() Group by the handicap column
 * @method StudentiQuery groupByStatoConvittore() Group by the stato_convittore column
 * @method StudentiQuery groupByDataRitiro() Group by the data_ritiro column
 * @method StudentiQuery groupByVotoAmmissione() Group by the voto_ammissione column
 * @method StudentiQuery groupByDifferenzaPunteggio() Group by the differenza_punteggio column
 * @method StudentiQuery groupByVotoQualifica() Group by the voto_qualifica column
 * @method StudentiQuery groupByVotoEsameSc1Qual() Group by the voto_esame_sc1_qual column
 * @method StudentiQuery groupByVotoEsameSc2Qual() Group by the voto_esame_sc2_qual column
 * @method StudentiQuery groupByVotoEsameOrQual() Group by the voto_esame_or_qual column
 * @method StudentiQuery groupByStatoPrivatista() Group by the stato_privatista column
 * @method StudentiQuery groupByFoto() Group by the foto column
 * @method StudentiQuery groupByRappresentante() Group by the rappresentante column
 * @method StudentiQuery groupByObbligoFormativo() Group by the obbligo_formativo column
 * @method StudentiQuery groupByIdLingua1() Group by the id_lingua_1 column
 * @method StudentiQuery groupByIdLingua2() Group by the id_lingua_2 column
 * @method StudentiQuery groupByIdLingua3() Group by the id_lingua_3 column
 * @method StudentiQuery groupByIdLingua4() Group by the id_lingua_4 column
 * @method StudentiQuery groupByIdLingua5() Group by the id_lingua_5 column
 * @method StudentiQuery groupByIdProvenienzaScolastica() Group by the id_provenienza_scolastica column
 * @method StudentiQuery groupByIdScuolaMedia() Group by the id_scuola_media column
 * @method StudentiQuery groupByLinguaScuolaMedia() Group by the lingua_scuola_media column
 * @method StudentiQuery groupByLinguaScuolaMedia2() Group by the lingua_scuola_media_2 column
 * @method StudentiQuery groupByGiudizioScuolaMedia() Group by the giudizio_scuola_media column
 * @method StudentiQuery groupByTrasporto() Group by the trasporto column
 * @method StudentiQuery groupByDataIscrizione() Group by the data_iscrizione column
 * @method StudentiQuery groupByPei() Group by the pei column
 * @method StudentiQuery groupByAmmessoEsameQualifica() Group by the ammesso_esame_qualifica column
 * @method StudentiQuery groupByAmmessoEsameQuinta() Group by the ammesso_esame_quinta column
 * @method StudentiQuery groupByGiudizioAmmissioneQuinta() Group by the giudizio_ammissione_quinta column
 * @method StudentiQuery groupByGradoHandicap() Group by the grado_handicap column
 * @method StudentiQuery groupByTipoHandicap() Group by the tipo_handicap column
 * @method StudentiQuery groupByStatoLicenzaMaestro() Group by the stato_licenza_maestro column
 * @method StudentiQuery groupByIdStudenteSissi() Group by the id_studente_sissi column
 * @method StudentiQuery groupByBadgeRfid() Group by the badge_rfid column
 * @method StudentiQuery groupByLode() Group by the lode column
 * @method StudentiQuery groupByDistrettoScolastico() Group by the distretto_scolastico column
 * @method StudentiQuery groupByGiudizioAmmissioneTerza() Group by the giudizio_ammissione_terza column
 * @method StudentiQuery groupByEsitoPrimaMedia() Group by the esito_prima_media column
 * @method StudentiQuery groupByEsitoSecondaMedia() Group by the esito_seconda_media column
 * @method StudentiQuery groupByEsitoTerzaMedia() Group by the esito_terza_media column
 * @method StudentiQuery groupByGiudizioEsameSc1Qual() Group by the giudizio_esame_sc1_qual column
 * @method StudentiQuery groupByGiudizioEsameSc2Qual() Group by the giudizio_esame_sc2_qual column
 * @method StudentiQuery groupByGiudizioEsameOrQual() Group by the giudizio_esame_or_qual column
 * @method StudentiQuery groupByGiudizioComplessivoEsameQual() Group by the giudizio_complessivo_esame_qual column
 * @method StudentiQuery groupByAcconsenteAziende() Group by the acconsente_aziende column
 * @method StudentiQuery groupByCurriculumPrima() Group by the curriculum_prima column
 * @method StudentiQuery groupByCurriculumSeconda() Group by the curriculum_seconda column
 * @method StudentiQuery groupByStageProfessionali() Group by the stage_professionali column
 * @method StudentiQuery groupByDataOrale() Group by the data_orale column
 * @method StudentiQuery groupByOrdineEsameOrale() Group by the ordine_esame_orale column
 * @method StudentiQuery groupByTipoPrimoScritto() Group by the tipo_primo_scritto column
 * @method StudentiQuery groupByTipoSecondoScritto() Group by the tipo_secondo_scritto column
 * @method StudentiQuery groupByTipoTerzoScritto() Group by the tipo_terzo_scritto column
 * @method StudentiQuery groupByUnanimitaPrimoScritto() Group by the unanimita_primo_scritto column
 * @method StudentiQuery groupByUnanimitaSecondoScritto() Group by the unanimita_secondo_scritto column
 * @method StudentiQuery groupByUnanimitaTerzoScritto() Group by the unanimita_terzo_scritto column
 * @method StudentiQuery groupByArgomentoSceltoOrale() Group by the argomento_scelto_orale column
 * @method StudentiQuery groupByAreaDisc1Orale() Group by the area_disc_1_orale column
 * @method StudentiQuery groupByAreaDisc2Orale() Group by the area_disc_2_orale column
 * @method StudentiQuery groupByDiscElaboratiOrale() Group by the disc_elaborati_orale column
 * @method StudentiQuery groupByUnanimitaVotoFinale() Group by the unanimita_voto_finale column
 * @method StudentiQuery groupByPresenteEsameQuinta() Group by the presente_esame_quinta column
 * @method StudentiQuery groupByStampaBadge() Group by the stampa_badge column
 * @method StudentiQuery groupByIdClasseDestinazione() Group by the id_classe_destinazione column
 * @method StudentiQuery groupByScontoRette() Group by the sconto_rette column
 * @method StudentiQuery groupByCartaStudenteNumero() Group by the carta_studente_numero column
 * @method StudentiQuery groupByCartaStudenteScadenza() Group by the carta_studente_scadenza column
 * @method StudentiQuery groupByEsitoCorrenteCalcolato() Group by the esito_corrente_calcolato column
 * @method StudentiQuery groupByIdFlusso() Group by the id_flusso column
 * @method StudentiQuery groupByDataAggiornamentoSogei() Group by the data_aggiornamento_sogei column
 * @method StudentiQuery groupByCodiceAlunnoMinisteriale() Group by the codice_alunno_ministeriale column
 * @method StudentiQuery groupByFlagCfFittizio() Group by the flag_cf_fittizio column
 * @method StudentiQuery groupByFlagS2f() Group by the flag_s2f column
 * @method StudentiQuery groupByCodiceStatoSogei() Group by the codice_stato_sogei column
 * @method StudentiQuery groupByCodiceGruppoNomade() Group by the codice_gruppo_nomade column
 * @method StudentiQuery groupByFlagMinoreStraniero() Group by the flag_minore_straniero column
 * @method StudentiQuery groupByChiave() Group by the chiave column
 * @method StudentiQuery groupByVotoEsameMedieItaliano() Group by the voto_esame_medie_italiano column
 * @method StudentiQuery groupByVotoEsameMedieInglese() Group by the voto_esame_medie_inglese column
 * @method StudentiQuery groupByVotoEsameMedieMatematica() Group by the voto_esame_medie_matematica column
 * @method StudentiQuery groupByVotoEsameMedieSecondaLingua() Group by the voto_esame_medie_seconda_lingua column
 * @method StudentiQuery groupByVotoEsameMedieInvalsiIta() Group by the voto_esame_medie_invalsi_ita column
 * @method StudentiQuery groupByVotoEsameMedieInvalsiMat() Group by the voto_esame_medie_invalsi_mat column
 * @method StudentiQuery groupByVotoEsameMedieOrale() Group by the voto_esame_medie_orale column
 * @method StudentiQuery groupByVotoAmmissioneMedie() Group by the voto_ammissione_medie column
 * @method StudentiQuery groupByEsitoPrimaElementare() Group by the esito_prima_elementare column
 * @method StudentiQuery groupByEsitoSecondaElementare() Group by the esito_seconda_elementare column
 * @method StudentiQuery groupByEsitoTerzaElementare() Group by the esito_terza_elementare column
 * @method StudentiQuery groupByEsitoQuartaElementare() Group by the esito_quarta_elementare column
 * @method StudentiQuery groupByEsitoQuintaElementare() Group by the esito_quinta_elementare column
 * @method StudentiQuery groupByTipoVotoEsameMedieItaliano() Group by the tipo_voto_esame_medie_italiano column
 * @method StudentiQuery groupByTipoVotoEsameMedieInglese() Group by the tipo_voto_esame_medie_inglese column
 * @method StudentiQuery groupByGiudizio1Medie() Group by the giudizio_1_medie column
 * @method StudentiQuery groupByGiudizio2Medie() Group by the giudizio_2_medie column
 * @method StudentiQuery groupByGiudizio3Medie() Group by the giudizio_3_medie column
 * @method StudentiQuery groupByArgomentiOraliMedie() Group by the argomenti_orali_medie column
 * @method StudentiQuery groupByGiudizioFinale1Medie() Group by the giudizio_finale_1_medie column
 * @method StudentiQuery groupByGiudizioFinale2Medie() Group by the giudizio_finale_2_medie column
 * @method StudentiQuery groupByGiudizioFinale3Medie() Group by the giudizio_finale_3_medie column
 * @method StudentiQuery groupByConsiglioTerzaMedia() Group by the consiglio_terza_media column
 * @method StudentiQuery groupByGiudizioSinteticoEsameTerzaMedia() Group by the giudizio_sintetico_esame_terza_media column
 * @method StudentiQuery groupByDataArrivoInItalia() Group by the data_arrivo_in_italia column
 * @method StudentiQuery groupByFrequenzaAsiloNido() Group by the frequenza_asilo_nido column
 * @method StudentiQuery groupByFrequenzaScuolaMaterna() Group by the frequenza_scuola_materna column
 * @method StudentiQuery groupByDataAggiornamentoSidi() Group by the data_aggiornamento_sidi column
 * @method StudentiQuery groupByCmpSupValIta() Group by the cmp_sup_val_ita column
 * @method StudentiQuery groupByCmpSupTxtIta() Group by the cmp_sup_txt_ita column
 * @method StudentiQuery groupByCmpSupValIng() Group by the cmp_sup_val_ing column
 * @method StudentiQuery groupByCmpSupTxtIng() Group by the cmp_sup_txt_ing column
 * @method StudentiQuery groupByCmpSupValAltri() Group by the cmp_sup_val_altri column
 * @method StudentiQuery groupByCmpSupTxtAltri() Group by the cmp_sup_txt_altri column
 * @method StudentiQuery groupByCmpSupValMat() Group by the cmp_sup_val_mat column
 * @method StudentiQuery groupByCmpSupTxtMat() Group by the cmp_sup_txt_mat column
 * @method StudentiQuery groupByCmpSupValSciTec() Group by the cmp_sup_val_sci_tec column
 * @method StudentiQuery groupByCmpSupTxtSciTec() Group by the cmp_sup_txt_sci_tec column
 * @method StudentiQuery groupByCmpSupValStoSoc() Group by the cmp_sup_val_sto_soc column
 * @method StudentiQuery groupByCmpSupTxtStoSoc() Group by the cmp_sup_txt_sto_soc column
 * @method StudentiQuery groupByCmpMedValIta() Group by the cmp_med_val_ita column
 * @method StudentiQuery groupByCmpMedTxtIta() Group by the cmp_med_txt_ita column
 * @method StudentiQuery groupByCmpMedValIng() Group by the cmp_med_val_ing column
 * @method StudentiQuery groupByCmpMedTxtIng() Group by the cmp_med_txt_ing column
 * @method StudentiQuery groupByCmpMedValAltri() Group by the cmp_med_val_altri column
 * @method StudentiQuery groupByCmpMedTxtAltri() Group by the cmp_med_txt_altri column
 * @method StudentiQuery groupByCmpMedValMat() Group by the cmp_med_val_mat column
 * @method StudentiQuery groupByCmpMedTxtMat() Group by the cmp_med_txt_mat column
 * @method StudentiQuery groupByCmpMedValSciTec() Group by the cmp_med_val_sci_tec column
 * @method StudentiQuery groupByCmpMedTxtSciTec() Group by the cmp_med_txt_sci_tec column
 * @method StudentiQuery groupByCmpMedValStoSoc() Group by the cmp_med_val_sto_soc column
 * @method StudentiQuery groupByCmpMedTxtStoSoc() Group by the cmp_med_txt_sto_soc column
 * @method StudentiQuery groupByCmpMedValL2() Group by the cmp_med_val_l2 column
 * @method StudentiQuery groupByCmpMedTxtL2() Group by the cmp_med_txt_l2 column
 * @method StudentiQuery groupByCmpMedValL3() Group by the cmp_med_val_l3 column
 * @method StudentiQuery groupByCmpMedTxtL3() Group by the cmp_med_txt_l3 column
 * @method StudentiQuery groupByCmpMedValArte() Group by the cmp_med_val_arte column
 * @method StudentiQuery groupByCmpMedTxtArte() Group by the cmp_med_txt_arte column
 * @method StudentiQuery groupByCmpMedValMus() Group by the cmp_med_val_mus column
 * @method StudentiQuery groupByCmpMedTxtMus() Group by the cmp_med_txt_mus column
 * @method StudentiQuery groupByCmpMedValMot() Group by the cmp_med_val_mot column
 * @method StudentiQuery groupByCmpMedTxtMot() Group by the cmp_med_txt_mot column
 *
 * @method StudentiQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method StudentiQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method StudentiQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method StudentiQuery leftJoinTasse($relationAlias = null) Adds a LEFT JOIN clause to the query using the Tasse relation
 * @method StudentiQuery rightJoinTasse($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Tasse relation
 * @method StudentiQuery innerJoinTasse($relationAlias = null) Adds a INNER JOIN clause to the query using the Tasse relation
 *
 * @method Studenti findOne(PropelPDO $con = null) Return the first Studenti matching the query
 * @method Studenti findOneOrCreate(PropelPDO $con = null) Return the first Studenti matching the query, or a new Studenti object populated from the query conditions when no match is found
 *
 * @method Studenti findOneByNome(string $nome) Return the first Studenti filtered by the nome column
 * @method Studenti findOneByCognome(string $cognome) Return the first Studenti filtered by the cognome column
 * @method Studenti findOneByIndirizzo(string $indirizzo) Return the first Studenti filtered by the indirizzo column
 * @method Studenti findOneByCitta(string $citta) Return the first Studenti filtered by the citta column
 * @method Studenti findOneByCap(string $cap) Return the first Studenti filtered by the cap column
 * @method Studenti findOneByProvincia(string $provincia) Return the first Studenti filtered by the provincia column
 * @method Studenti findOneBySesso(string $sesso) Return the first Studenti filtered by the sesso column
 * @method Studenti findOneByTelefono(string $telefono) Return the first Studenti filtered by the telefono column
 * @method Studenti findOneByCellulare1(string $cellulare1) Return the first Studenti filtered by the cellulare1 column
 * @method Studenti findOneByCellulare2(string $cellulare2) Return the first Studenti filtered by the cellulare2 column
 * @method Studenti findOneByEmail1(string $email1) Return the first Studenti filtered by the email1 column
 * @method Studenti findOneByEmail2(string $email2) Return the first Studenti filtered by the email2 column
 * @method Studenti findOneByInvioEmail(string $invio_email) Return the first Studenti filtered by the invio_email column
 * @method Studenti findOneByInvioEmailCumulativo(string $invio_email_cumulativo) Return the first Studenti filtered by the invio_email_cumulativo column
 * @method Studenti findOneByInvioEmailParametrico(string $invio_email_parametrico) Return the first Studenti filtered by the invio_email_parametrico column
 * @method Studenti findOneByInvioEmailTemporale(string $invio_email_temporale) Return the first Studenti filtered by the invio_email_temporale column
 * @method Studenti findOneByTipoSms(string $tipo_sms) Return the first Studenti filtered by the tipo_sms column
 * @method Studenti findOneByTipoSmsCumulativo(string $tipo_sms_cumulativo) Return the first Studenti filtered by the tipo_sms_cumulativo column
 * @method Studenti findOneByTipoSmsParametrico(string $tipo_sms_parametrico) Return the first Studenti filtered by the tipo_sms_parametrico column
 * @method Studenti findOneByTipoSmsTemporale(string $tipo_sms_temporale) Return the first Studenti filtered by the tipo_sms_temporale column
 * @method Studenti findOneByAutEntrataRitardo(string $aut_entrata_ritardo) Return the first Studenti filtered by the aut_entrata_ritardo column
 * @method Studenti findOneByAutUscitaAnticipo(string $aut_uscita_anticipo) Return the first Studenti filtered by the aut_uscita_anticipo column
 * @method Studenti findOneByAutPomeriggio(string $aut_pomeriggio) Return the first Studenti filtered by the aut_pomeriggio column
 * @method Studenti findOneByAcconsente(string $acconsente) Return the first Studenti filtered by the acconsente column
 * @method Studenti findOneByRitirato(string $ritirato) Return the first Studenti filtered by the ritirato column
 * @method Studenti findOneByDataNascita(int $data_nascita) Return the first Studenti filtered by the data_nascita column
 * @method Studenti findOneByCodiceStudente(string $codice_studente) Return the first Studenti filtered by the codice_studente column
 * @method Studenti findOneByPasswordStudente(string $password_studente) Return the first Studenti filtered by the password_studente column
 * @method Studenti findOneByCodiceGiustificazioniStudente(string $codice_giustificazioni_studente) Return the first Studenti filtered by the codice_giustificazioni_studente column
 * @method Studenti findOneByEsoneroReligione(string $esonero_religione) Return the first Studenti filtered by the esonero_religione column
 * @method Studenti findOneByMateriaSostitutivaReligione(int $materia_sostitutiva_religione) Return the first Studenti filtered by the materia_sostitutiva_religione column
 * @method Studenti findOneByEsoneroEdFisica(string $esonero_ed_fisica) Return the first Studenti filtered by the esonero_ed_fisica column
 * @method Studenti findOneByMateriaSostitutivaEdfisica(int $materia_sostitutiva_edfisica) Return the first Studenti filtered by the materia_sostitutiva_edfisica column
 * @method Studenti findOneByCreditiTerza(int $crediti_terza) Return the first Studenti filtered by the crediti_terza column
 * @method Studenti findOneByMediaVotiTerza(string $media_voti_terza) Return the first Studenti filtered by the media_voti_terza column
 * @method Studenti findOneByDebitiTerza(string $debiti_terza) Return the first Studenti filtered by the debiti_terza column
 * @method Studenti findOneByCreditiSospesiTerza(int $crediti_sospesi_terza) Return the first Studenti filtered by the crediti_sospesi_terza column
 * @method Studenti findOneByCreditiReintegratiTerza(int $crediti_reintegrati_terza) Return the first Studenti filtered by the crediti_reintegrati_terza column
 * @method Studenti findOneByCreditiQuarta(int $crediti_quarta) Return the first Studenti filtered by the crediti_quarta column
 * @method Studenti findOneByMediaVotiQuarta(string $media_voti_quarta) Return the first Studenti filtered by the media_voti_quarta column
 * @method Studenti findOneByDebitiQuarta(string $debiti_quarta) Return the first Studenti filtered by the debiti_quarta column
 * @method Studenti findOneByCreditiSospesiQuarta(int $crediti_sospesi_quarta) Return the first Studenti filtered by the crediti_sospesi_quarta column
 * @method Studenti findOneByCreditiReintegratiQuarta(int $crediti_reintegrati_quarta) Return the first Studenti filtered by the crediti_reintegrati_quarta column
 * @method Studenti findOneByCreditiQuinta(int $crediti_quinta) Return the first Studenti filtered by the crediti_quinta column
 * @method Studenti findOneByMediaVotiQuinta(string $media_voti_quinta) Return the first Studenti filtered by the media_voti_quinta column
 * @method Studenti findOneByCreditiFinaliAgg(int $crediti_finali_agg) Return the first Studenti filtered by the crediti_finali_agg column
 * @method Studenti findOneByMatricola(string $matricola) Return the first Studenti filtered by the matricola column
 * @method Studenti findOneByLuogoNascita(string $luogo_nascita) Return the first Studenti filtered by the luogo_nascita column
 * @method Studenti findOneByProvinciaNascita(string $provincia_nascita) Return the first Studenti filtered by the provincia_nascita column
 * @method Studenti findOneByMotiviCreditiTerza(string $motivi_crediti_terza) Return the first Studenti filtered by the motivi_crediti_terza column
 * @method Studenti findOneByMotiviCreditiQuarta(string $motivi_crediti_quarta) Return the first Studenti filtered by the motivi_crediti_quarta column
 * @method Studenti findOneByMotiviCreditiQuinta(string $motivi_crediti_quinta) Return the first Studenti filtered by the motivi_crediti_quinta column
 * @method Studenti findOneByMotiviCreditiAgg(string $motivi_crediti_agg) Return the first Studenti filtered by the motivi_crediti_agg column
 * @method Studenti findOneByCodiceComuneNascita(string $codice_comune_nascita) Return the first Studenti filtered by the codice_comune_nascita column
 * @method Studenti findOneByStatoNascita(string $stato_nascita) Return the first Studenti filtered by the stato_nascita column
 * @method Studenti findOneByCittadinanza(string $cittadinanza) Return the first Studenti filtered by the cittadinanza column
 * @method Studenti findOneBySecondaCittadinanza(string $seconda_cittadinanza) Return the first Studenti filtered by the seconda_cittadinanza column
 * @method Studenti findOneByCodiceComuneResidenza(string $codice_comune_residenza) Return the first Studenti filtered by the codice_comune_residenza column
 * @method Studenti findOneByDistretto(string $distretto) Return the first Studenti filtered by the distretto column
 * @method Studenti findOneByCodiceFiscale(string $codice_fiscale) Return the first Studenti filtered by the codice_fiscale column
 * @method Studenti findOneByMedico(string $medico) Return the first Studenti filtered by the medico column
 * @method Studenti findOneByTelefonoMedico(string $telefono_medico) Return the first Studenti filtered by the telefono_medico column
 * @method Studenti findOneByIntolleranzeAlim(string $intolleranze_alim) Return the first Studenti filtered by the intolleranze_alim column
 * @method Studenti findOneByGruppoSanguigno(string $gruppo_sanguigno) Return the first Studenti filtered by the gruppo_sanguigno column
 * @method Studenti findOneByGruppoRh(string $gruppo_rh) Return the first Studenti filtered by the gruppo_rh column
 * @method Studenti findOneByCodiceAsl(string $codice_asl) Return the first Studenti filtered by the codice_asl column
 * @method Studenti findOneByAnnotazioni(string $annotazioni) Return the first Studenti filtered by the annotazioni column
 * @method Studenti findOneByStatoCivile(int $stato_civile) Return the first Studenti filtered by the stato_civile column
 * @method Studenti findOneByVotoPrimoScritto(int $voto_primo_scritto) Return the first Studenti filtered by the voto_primo_scritto column
 * @method Studenti findOneByVotoSecondoScritto(int $voto_secondo_scritto) Return the first Studenti filtered by the voto_secondo_scritto column
 * @method Studenti findOneByVotoTerzoScritto(int $voto_terzo_scritto) Return the first Studenti filtered by the voto_terzo_scritto column
 * @method Studenti findOneByVotoOrale(int $voto_orale) Return the first Studenti filtered by the voto_orale column
 * @method Studenti findOneByVotoBonus(int $voto_bonus) Return the first Studenti filtered by the voto_bonus column
 * @method Studenti findOneByMateriaSecondoScr(string $materia_secondo_scr) Return the first Studenti filtered by the materia_secondo_scr column
 * @method Studenti findOneByUlterioriSpecifDiploma(string $ulteriori_specif_diploma) Return the first Studenti filtered by the ulteriori_specif_diploma column
 * @method Studenti findOneByNumeroDiploma(int $numero_diploma) Return the first Studenti filtered by the numero_diploma column
 * @method Studenti findOneByChiInserisce(string $chi_inserisce) Return the first Studenti filtered by the chi_inserisce column
 * @method Studenti findOneByDataInserimento(string $data_inserimento) Return the first Studenti filtered by the data_inserimento column
 * @method Studenti findOneByTipoInserimento(string $tipo_inserimento) Return the first Studenti filtered by the tipo_inserimento column
 * @method Studenti findOneByChiModifica(string $chi_modifica) Return the first Studenti filtered by the chi_modifica column
 * @method Studenti findOneByDataModifica(string $data_modifica) Return the first Studenti filtered by the data_modifica column
 * @method Studenti findOneByTipoModifica(string $tipo_modifica) Return the first Studenti filtered by the tipo_modifica column
 * @method Studenti findOneByFlagCanc(string $flag_canc) Return the first Studenti filtered by the flag_canc column
 * @method Studenti findOneByStatoAvanzamento(string $stato_avanzamento) Return the first Studenti filtered by the stato_avanzamento column
 * @method Studenti findOneByDataStatoAvanzamento(string $data_stato_avanzamento) Return the first Studenti filtered by the data_stato_avanzamento column
 * @method Studenti findOneByCapProvinciaNascita(string $cap_provincia_nascita) Return the first Studenti filtered by the cap_provincia_nascita column
 * @method Studenti findOneByBadge(string $badge) Return the first Studenti filtered by the badge column
 * @method Studenti findOneByCapResidenza(string $cap_residenza) Return the first Studenti filtered by the cap_residenza column
 * @method Studenti findOneByCodiceComuneDomicilio(string $codice_comune_domicilio) Return the first Studenti filtered by the codice_comune_domicilio column
 * @method Studenti findOneByCapDomicilio(string $cap_domicilio) Return the first Studenti filtered by the cap_domicilio column
 * @method Studenti findOneByCapNascita(string $cap_nascita) Return the first Studenti filtered by the cap_nascita column
 * @method Studenti findOneByIndirizzoDomicilio(string $indirizzo_domicilio) Return the first Studenti filtered by the indirizzo_domicilio column
 * @method Studenti findOneByCittaNascitaStraniera(string $citta_nascita_straniera) Return the first Studenti filtered by the citta_nascita_straniera column
 * @method Studenti findOneByCellulareAllievo(string $cellulare_allievo) Return the first Studenti filtered by the cellulare_allievo column
 * @method Studenti findOneByHandicap(string $handicap) Return the first Studenti filtered by the handicap column
 * @method Studenti findOneByStatoConvittore(string $stato_convittore) Return the first Studenti filtered by the stato_convittore column
 * @method Studenti findOneByDataRitiro(string $data_ritiro) Return the first Studenti filtered by the data_ritiro column
 * @method Studenti findOneByVotoAmmissione(string $voto_ammissione) Return the first Studenti filtered by the voto_ammissione column
 * @method Studenti findOneByDifferenzaPunteggio(string $differenza_punteggio) Return the first Studenti filtered by the differenza_punteggio column
 * @method Studenti findOneByVotoQualifica(string $voto_qualifica) Return the first Studenti filtered by the voto_qualifica column
 * @method Studenti findOneByVotoEsameSc1Qual(string $voto_esame_sc1_qual) Return the first Studenti filtered by the voto_esame_sc1_qual column
 * @method Studenti findOneByVotoEsameSc2Qual(string $voto_esame_sc2_qual) Return the first Studenti filtered by the voto_esame_sc2_qual column
 * @method Studenti findOneByVotoEsameOrQual(string $voto_esame_or_qual) Return the first Studenti filtered by the voto_esame_or_qual column
 * @method Studenti findOneByStatoPrivatista(string $stato_privatista) Return the first Studenti filtered by the stato_privatista column
 * @method Studenti findOneByFoto(string $foto) Return the first Studenti filtered by the foto column
 * @method Studenti findOneByRappresentante(string $rappresentante) Return the first Studenti filtered by the rappresentante column
 * @method Studenti findOneByObbligoFormativo(string $obbligo_formativo) Return the first Studenti filtered by the obbligo_formativo column
 * @method Studenti findOneByIdLingua1(string $id_lingua_1) Return the first Studenti filtered by the id_lingua_1 column
 * @method Studenti findOneByIdLingua2(string $id_lingua_2) Return the first Studenti filtered by the id_lingua_2 column
 * @method Studenti findOneByIdLingua3(string $id_lingua_3) Return the first Studenti filtered by the id_lingua_3 column
 * @method Studenti findOneByIdLingua4(string $id_lingua_4) Return the first Studenti filtered by the id_lingua_4 column
 * @method Studenti findOneByIdLingua5(string $id_lingua_5) Return the first Studenti filtered by the id_lingua_5 column
 * @method Studenti findOneByIdProvenienzaScolastica(string $id_provenienza_scolastica) Return the first Studenti filtered by the id_provenienza_scolastica column
 * @method Studenti findOneByIdScuolaMedia(string $id_scuola_media) Return the first Studenti filtered by the id_scuola_media column
 * @method Studenti findOneByLinguaScuolaMedia(string $lingua_scuola_media) Return the first Studenti filtered by the lingua_scuola_media column
 * @method Studenti findOneByLinguaScuolaMedia2(string $lingua_scuola_media_2) Return the first Studenti filtered by the lingua_scuola_media_2 column
 * @method Studenti findOneByGiudizioScuolaMedia(string $giudizio_scuola_media) Return the first Studenti filtered by the giudizio_scuola_media column
 * @method Studenti findOneByTrasporto(string $trasporto) Return the first Studenti filtered by the trasporto column
 * @method Studenti findOneByDataIscrizione(string $data_iscrizione) Return the first Studenti filtered by the data_iscrizione column
 * @method Studenti findOneByPei(string $pei) Return the first Studenti filtered by the pei column
 * @method Studenti findOneByAmmessoEsameQualifica(string $ammesso_esame_qualifica) Return the first Studenti filtered by the ammesso_esame_qualifica column
 * @method Studenti findOneByAmmessoEsameQuinta(string $ammesso_esame_quinta) Return the first Studenti filtered by the ammesso_esame_quinta column
 * @method Studenti findOneByGiudizioAmmissioneQuinta(string $giudizio_ammissione_quinta) Return the first Studenti filtered by the giudizio_ammissione_quinta column
 * @method Studenti findOneByGradoHandicap(string $grado_handicap) Return the first Studenti filtered by the grado_handicap column
 * @method Studenti findOneByTipoHandicap(string $tipo_handicap) Return the first Studenti filtered by the tipo_handicap column
 * @method Studenti findOneByStatoLicenzaMaestro(string $stato_licenza_maestro) Return the first Studenti filtered by the stato_licenza_maestro column
 * @method Studenti findOneByIdStudenteSissi(string $id_studente_sissi) Return the first Studenti filtered by the id_studente_sissi column
 * @method Studenti findOneByBadgeRfid(string $badge_rfid) Return the first Studenti filtered by the badge_rfid column
 * @method Studenti findOneByLode(string $lode) Return the first Studenti filtered by the lode column
 * @method Studenti findOneByDistrettoScolastico(string $distretto_scolastico) Return the first Studenti filtered by the distretto_scolastico column
 * @method Studenti findOneByGiudizioAmmissioneTerza(string $giudizio_ammissione_terza) Return the first Studenti filtered by the giudizio_ammissione_terza column
 * @method Studenti findOneByEsitoPrimaMedia(string $esito_prima_media) Return the first Studenti filtered by the esito_prima_media column
 * @method Studenti findOneByEsitoSecondaMedia(string $esito_seconda_media) Return the first Studenti filtered by the esito_seconda_media column
 * @method Studenti findOneByEsitoTerzaMedia(string $esito_terza_media) Return the first Studenti filtered by the esito_terza_media column
 * @method Studenti findOneByGiudizioEsameSc1Qual(string $giudizio_esame_sc1_qual) Return the first Studenti filtered by the giudizio_esame_sc1_qual column
 * @method Studenti findOneByGiudizioEsameSc2Qual(string $giudizio_esame_sc2_qual) Return the first Studenti filtered by the giudizio_esame_sc2_qual column
 * @method Studenti findOneByGiudizioEsameOrQual(string $giudizio_esame_or_qual) Return the first Studenti filtered by the giudizio_esame_or_qual column
 * @method Studenti findOneByGiudizioComplessivoEsameQual(string $giudizio_complessivo_esame_qual) Return the first Studenti filtered by the giudizio_complessivo_esame_qual column
 * @method Studenti findOneByAcconsenteAziende(int $acconsente_aziende) Return the first Studenti filtered by the acconsente_aziende column
 * @method Studenti findOneByCurriculumPrima(string $curriculum_prima) Return the first Studenti filtered by the curriculum_prima column
 * @method Studenti findOneByCurriculumSeconda(string $curriculum_seconda) Return the first Studenti filtered by the curriculum_seconda column
 * @method Studenti findOneByStageProfessionali(string $stage_professionali) Return the first Studenti filtered by the stage_professionali column
 * @method Studenti findOneByDataOrale(string $data_orale) Return the first Studenti filtered by the data_orale column
 * @method Studenti findOneByOrdineEsameOrale(string $ordine_esame_orale) Return the first Studenti filtered by the ordine_esame_orale column
 * @method Studenti findOneByTipoPrimoScritto(string $tipo_primo_scritto) Return the first Studenti filtered by the tipo_primo_scritto column
 * @method Studenti findOneByTipoSecondoScritto(string $tipo_secondo_scritto) Return the first Studenti filtered by the tipo_secondo_scritto column
 * @method Studenti findOneByTipoTerzoScritto(string $tipo_terzo_scritto) Return the first Studenti filtered by the tipo_terzo_scritto column
 * @method Studenti findOneByUnanimitaPrimoScritto(string $unanimita_primo_scritto) Return the first Studenti filtered by the unanimita_primo_scritto column
 * @method Studenti findOneByUnanimitaSecondoScritto(string $unanimita_secondo_scritto) Return the first Studenti filtered by the unanimita_secondo_scritto column
 * @method Studenti findOneByUnanimitaTerzoScritto(string $unanimita_terzo_scritto) Return the first Studenti filtered by the unanimita_terzo_scritto column
 * @method Studenti findOneByArgomentoSceltoOrale(string $argomento_scelto_orale) Return the first Studenti filtered by the argomento_scelto_orale column
 * @method Studenti findOneByAreaDisc1Orale(string $area_disc_1_orale) Return the first Studenti filtered by the area_disc_1_orale column
 * @method Studenti findOneByAreaDisc2Orale(string $area_disc_2_orale) Return the first Studenti filtered by the area_disc_2_orale column
 * @method Studenti findOneByDiscElaboratiOrale(string $disc_elaborati_orale) Return the first Studenti filtered by the disc_elaborati_orale column
 * @method Studenti findOneByUnanimitaVotoFinale(string $unanimita_voto_finale) Return the first Studenti filtered by the unanimita_voto_finale column
 * @method Studenti findOneByPresenteEsameQuinta(string $presente_esame_quinta) Return the first Studenti filtered by the presente_esame_quinta column
 * @method Studenti findOneByStampaBadge(string $stampa_badge) Return the first Studenti filtered by the stampa_badge column
 * @method Studenti findOneByIdClasseDestinazione(int $id_classe_destinazione) Return the first Studenti filtered by the id_classe_destinazione column
 * @method Studenti findOneByScontoRette(int $sconto_rette) Return the first Studenti filtered by the sconto_rette column
 * @method Studenti findOneByCartaStudenteNumero(string $carta_studente_numero) Return the first Studenti filtered by the carta_studente_numero column
 * @method Studenti findOneByCartaStudenteScadenza(int $carta_studente_scadenza) Return the first Studenti filtered by the carta_studente_scadenza column
 * @method Studenti findOneByEsitoCorrenteCalcolato(string $esito_corrente_calcolato) Return the first Studenti filtered by the esito_corrente_calcolato column
 * @method Studenti findOneByIdFlusso(string $id_flusso) Return the first Studenti filtered by the id_flusso column
 * @method Studenti findOneByDataAggiornamentoSogei(string $data_aggiornamento_sogei) Return the first Studenti filtered by the data_aggiornamento_sogei column
 * @method Studenti findOneByCodiceAlunnoMinisteriale(string $codice_alunno_ministeriale) Return the first Studenti filtered by the codice_alunno_ministeriale column
 * @method Studenti findOneByFlagCfFittizio(int $flag_cf_fittizio) Return the first Studenti filtered by the flag_cf_fittizio column
 * @method Studenti findOneByFlagS2f(string $flag_s2f) Return the first Studenti filtered by the flag_s2f column
 * @method Studenti findOneByCodiceStatoSogei(string $codice_stato_sogei) Return the first Studenti filtered by the codice_stato_sogei column
 * @method Studenti findOneByCodiceGruppoNomade(string $codice_gruppo_nomade) Return the first Studenti filtered by the codice_gruppo_nomade column
 * @method Studenti findOneByFlagMinoreStraniero(int $flag_minore_straniero) Return the first Studenti filtered by the flag_minore_straniero column
 * @method Studenti findOneByChiave(string $chiave) Return the first Studenti filtered by the chiave column
 * @method Studenti findOneByVotoEsameMedieItaliano(string $voto_esame_medie_italiano) Return the first Studenti filtered by the voto_esame_medie_italiano column
 * @method Studenti findOneByVotoEsameMedieInglese(string $voto_esame_medie_inglese) Return the first Studenti filtered by the voto_esame_medie_inglese column
 * @method Studenti findOneByVotoEsameMedieMatematica(string $voto_esame_medie_matematica) Return the first Studenti filtered by the voto_esame_medie_matematica column
 * @method Studenti findOneByVotoEsameMedieSecondaLingua(string $voto_esame_medie_seconda_lingua) Return the first Studenti filtered by the voto_esame_medie_seconda_lingua column
 * @method Studenti findOneByVotoEsameMedieInvalsiIta(string $voto_esame_medie_invalsi_ita) Return the first Studenti filtered by the voto_esame_medie_invalsi_ita column
 * @method Studenti findOneByVotoEsameMedieInvalsiMat(string $voto_esame_medie_invalsi_mat) Return the first Studenti filtered by the voto_esame_medie_invalsi_mat column
 * @method Studenti findOneByVotoEsameMedieOrale(string $voto_esame_medie_orale) Return the first Studenti filtered by the voto_esame_medie_orale column
 * @method Studenti findOneByVotoAmmissioneMedie(string $voto_ammissione_medie) Return the first Studenti filtered by the voto_ammissione_medie column
 * @method Studenti findOneByEsitoPrimaElementare(string $esito_prima_elementare) Return the first Studenti filtered by the esito_prima_elementare column
 * @method Studenti findOneByEsitoSecondaElementare(string $esito_seconda_elementare) Return the first Studenti filtered by the esito_seconda_elementare column
 * @method Studenti findOneByEsitoTerzaElementare(string $esito_terza_elementare) Return the first Studenti filtered by the esito_terza_elementare column
 * @method Studenti findOneByEsitoQuartaElementare(string $esito_quarta_elementare) Return the first Studenti filtered by the esito_quarta_elementare column
 * @method Studenti findOneByEsitoQuintaElementare(string $esito_quinta_elementare) Return the first Studenti filtered by the esito_quinta_elementare column
 * @method Studenti findOneByTipoVotoEsameMedieItaliano(string $tipo_voto_esame_medie_italiano) Return the first Studenti filtered by the tipo_voto_esame_medie_italiano column
 * @method Studenti findOneByTipoVotoEsameMedieInglese(string $tipo_voto_esame_medie_inglese) Return the first Studenti filtered by the tipo_voto_esame_medie_inglese column
 * @method Studenti findOneByGiudizio1Medie(string $giudizio_1_medie) Return the first Studenti filtered by the giudizio_1_medie column
 * @method Studenti findOneByGiudizio2Medie(string $giudizio_2_medie) Return the first Studenti filtered by the giudizio_2_medie column
 * @method Studenti findOneByGiudizio3Medie(string $giudizio_3_medie) Return the first Studenti filtered by the giudizio_3_medie column
 * @method Studenti findOneByArgomentiOraliMedie(string $argomenti_orali_medie) Return the first Studenti filtered by the argomenti_orali_medie column
 * @method Studenti findOneByGiudizioFinale1Medie(string $giudizio_finale_1_medie) Return the first Studenti filtered by the giudizio_finale_1_medie column
 * @method Studenti findOneByGiudizioFinale2Medie(string $giudizio_finale_2_medie) Return the first Studenti filtered by the giudizio_finale_2_medie column
 * @method Studenti findOneByGiudizioFinale3Medie(string $giudizio_finale_3_medie) Return the first Studenti filtered by the giudizio_finale_3_medie column
 * @method Studenti findOneByConsiglioTerzaMedia(string $consiglio_terza_media) Return the first Studenti filtered by the consiglio_terza_media column
 * @method Studenti findOneByGiudizioSinteticoEsameTerzaMedia(string $giudizio_sintetico_esame_terza_media) Return the first Studenti filtered by the giudizio_sintetico_esame_terza_media column
 * @method Studenti findOneByDataArrivoInItalia(int $data_arrivo_in_italia) Return the first Studenti filtered by the data_arrivo_in_italia column
 * @method Studenti findOneByFrequenzaAsiloNido(int $frequenza_asilo_nido) Return the first Studenti filtered by the frequenza_asilo_nido column
 * @method Studenti findOneByFrequenzaScuolaMaterna(int $frequenza_scuola_materna) Return the first Studenti filtered by the frequenza_scuola_materna column
 * @method Studenti findOneByDataAggiornamentoSidi(int $data_aggiornamento_sidi) Return the first Studenti filtered by the data_aggiornamento_sidi column
 * @method Studenti findOneByCmpSupValIta(string $cmp_sup_val_ita) Return the first Studenti filtered by the cmp_sup_val_ita column
 * @method Studenti findOneByCmpSupTxtIta(string $cmp_sup_txt_ita) Return the first Studenti filtered by the cmp_sup_txt_ita column
 * @method Studenti findOneByCmpSupValIng(string $cmp_sup_val_ing) Return the first Studenti filtered by the cmp_sup_val_ing column
 * @method Studenti findOneByCmpSupTxtIng(string $cmp_sup_txt_ing) Return the first Studenti filtered by the cmp_sup_txt_ing column
 * @method Studenti findOneByCmpSupValAltri(string $cmp_sup_val_altri) Return the first Studenti filtered by the cmp_sup_val_altri column
 * @method Studenti findOneByCmpSupTxtAltri(string $cmp_sup_txt_altri) Return the first Studenti filtered by the cmp_sup_txt_altri column
 * @method Studenti findOneByCmpSupValMat(string $cmp_sup_val_mat) Return the first Studenti filtered by the cmp_sup_val_mat column
 * @method Studenti findOneByCmpSupTxtMat(string $cmp_sup_txt_mat) Return the first Studenti filtered by the cmp_sup_txt_mat column
 * @method Studenti findOneByCmpSupValSciTec(string $cmp_sup_val_sci_tec) Return the first Studenti filtered by the cmp_sup_val_sci_tec column
 * @method Studenti findOneByCmpSupTxtSciTec(string $cmp_sup_txt_sci_tec) Return the first Studenti filtered by the cmp_sup_txt_sci_tec column
 * @method Studenti findOneByCmpSupValStoSoc(string $cmp_sup_val_sto_soc) Return the first Studenti filtered by the cmp_sup_val_sto_soc column
 * @method Studenti findOneByCmpSupTxtStoSoc(string $cmp_sup_txt_sto_soc) Return the first Studenti filtered by the cmp_sup_txt_sto_soc column
 * @method Studenti findOneByCmpMedValIta(string $cmp_med_val_ita) Return the first Studenti filtered by the cmp_med_val_ita column
 * @method Studenti findOneByCmpMedTxtIta(string $cmp_med_txt_ita) Return the first Studenti filtered by the cmp_med_txt_ita column
 * @method Studenti findOneByCmpMedValIng(string $cmp_med_val_ing) Return the first Studenti filtered by the cmp_med_val_ing column
 * @method Studenti findOneByCmpMedTxtIng(string $cmp_med_txt_ing) Return the first Studenti filtered by the cmp_med_txt_ing column
 * @method Studenti findOneByCmpMedValAltri(string $cmp_med_val_altri) Return the first Studenti filtered by the cmp_med_val_altri column
 * @method Studenti findOneByCmpMedTxtAltri(string $cmp_med_txt_altri) Return the first Studenti filtered by the cmp_med_txt_altri column
 * @method Studenti findOneByCmpMedValMat(string $cmp_med_val_mat) Return the first Studenti filtered by the cmp_med_val_mat column
 * @method Studenti findOneByCmpMedTxtMat(string $cmp_med_txt_mat) Return the first Studenti filtered by the cmp_med_txt_mat column
 * @method Studenti findOneByCmpMedValSciTec(string $cmp_med_val_sci_tec) Return the first Studenti filtered by the cmp_med_val_sci_tec column
 * @method Studenti findOneByCmpMedTxtSciTec(string $cmp_med_txt_sci_tec) Return the first Studenti filtered by the cmp_med_txt_sci_tec column
 * @method Studenti findOneByCmpMedValStoSoc(string $cmp_med_val_sto_soc) Return the first Studenti filtered by the cmp_med_val_sto_soc column
 * @method Studenti findOneByCmpMedTxtStoSoc(string $cmp_med_txt_sto_soc) Return the first Studenti filtered by the cmp_med_txt_sto_soc column
 * @method Studenti findOneByCmpMedValL2(string $cmp_med_val_l2) Return the first Studenti filtered by the cmp_med_val_l2 column
 * @method Studenti findOneByCmpMedTxtL2(string $cmp_med_txt_l2) Return the first Studenti filtered by the cmp_med_txt_l2 column
 * @method Studenti findOneByCmpMedValL3(string $cmp_med_val_l3) Return the first Studenti filtered by the cmp_med_val_l3 column
 * @method Studenti findOneByCmpMedTxtL3(string $cmp_med_txt_l3) Return the first Studenti filtered by the cmp_med_txt_l3 column
 * @method Studenti findOneByCmpMedValArte(string $cmp_med_val_arte) Return the first Studenti filtered by the cmp_med_val_arte column
 * @method Studenti findOneByCmpMedTxtArte(string $cmp_med_txt_arte) Return the first Studenti filtered by the cmp_med_txt_arte column
 * @method Studenti findOneByCmpMedValMus(string $cmp_med_val_mus) Return the first Studenti filtered by the cmp_med_val_mus column
 * @method Studenti findOneByCmpMedTxtMus(string $cmp_med_txt_mus) Return the first Studenti filtered by the cmp_med_txt_mus column
 * @method Studenti findOneByCmpMedValMot(string $cmp_med_val_mot) Return the first Studenti filtered by the cmp_med_val_mot column
 * @method Studenti findOneByCmpMedTxtMot(string $cmp_med_txt_mot) Return the first Studenti filtered by the cmp_med_txt_mot column
 *
 * @method array findByIdStudente(int $id_studente) Return Studenti objects filtered by the id_studente column
 * @method array findByNome(string $nome) Return Studenti objects filtered by the nome column
 * @method array findByCognome(string $cognome) Return Studenti objects filtered by the cognome column
 * @method array findByIndirizzo(string $indirizzo) Return Studenti objects filtered by the indirizzo column
 * @method array findByCitta(string $citta) Return Studenti objects filtered by the citta column
 * @method array findByCap(string $cap) Return Studenti objects filtered by the cap column
 * @method array findByProvincia(string $provincia) Return Studenti objects filtered by the provincia column
 * @method array findBySesso(string $sesso) Return Studenti objects filtered by the sesso column
 * @method array findByTelefono(string $telefono) Return Studenti objects filtered by the telefono column
 * @method array findByCellulare1(string $cellulare1) Return Studenti objects filtered by the cellulare1 column
 * @method array findByCellulare2(string $cellulare2) Return Studenti objects filtered by the cellulare2 column
 * @method array findByEmail1(string $email1) Return Studenti objects filtered by the email1 column
 * @method array findByEmail2(string $email2) Return Studenti objects filtered by the email2 column
 * @method array findByInvioEmail(string $invio_email) Return Studenti objects filtered by the invio_email column
 * @method array findByInvioEmailCumulativo(string $invio_email_cumulativo) Return Studenti objects filtered by the invio_email_cumulativo column
 * @method array findByInvioEmailParametrico(string $invio_email_parametrico) Return Studenti objects filtered by the invio_email_parametrico column
 * @method array findByInvioEmailTemporale(string $invio_email_temporale) Return Studenti objects filtered by the invio_email_temporale column
 * @method array findByTipoSms(string $tipo_sms) Return Studenti objects filtered by the tipo_sms column
 * @method array findByTipoSmsCumulativo(string $tipo_sms_cumulativo) Return Studenti objects filtered by the tipo_sms_cumulativo column
 * @method array findByTipoSmsParametrico(string $tipo_sms_parametrico) Return Studenti objects filtered by the tipo_sms_parametrico column
 * @method array findByTipoSmsTemporale(string $tipo_sms_temporale) Return Studenti objects filtered by the tipo_sms_temporale column
 * @method array findByAutEntrataRitardo(string $aut_entrata_ritardo) Return Studenti objects filtered by the aut_entrata_ritardo column
 * @method array findByAutUscitaAnticipo(string $aut_uscita_anticipo) Return Studenti objects filtered by the aut_uscita_anticipo column
 * @method array findByAutPomeriggio(string $aut_pomeriggio) Return Studenti objects filtered by the aut_pomeriggio column
 * @method array findByAcconsente(string $acconsente) Return Studenti objects filtered by the acconsente column
 * @method array findByRitirato(string $ritirato) Return Studenti objects filtered by the ritirato column
 * @method array findByDataNascita(int $data_nascita) Return Studenti objects filtered by the data_nascita column
 * @method array findByCodiceStudente(string $codice_studente) Return Studenti objects filtered by the codice_studente column
 * @method array findByPasswordStudente(string $password_studente) Return Studenti objects filtered by the password_studente column
 * @method array findByCodiceGiustificazioniStudente(string $codice_giustificazioni_studente) Return Studenti objects filtered by the codice_giustificazioni_studente column
 * @method array findByEsoneroReligione(string $esonero_religione) Return Studenti objects filtered by the esonero_religione column
 * @method array findByMateriaSostitutivaReligione(int $materia_sostitutiva_religione) Return Studenti objects filtered by the materia_sostitutiva_religione column
 * @method array findByEsoneroEdFisica(string $esonero_ed_fisica) Return Studenti objects filtered by the esonero_ed_fisica column
 * @method array findByMateriaSostitutivaEdfisica(int $materia_sostitutiva_edfisica) Return Studenti objects filtered by the materia_sostitutiva_edfisica column
 * @method array findByCreditiTerza(int $crediti_terza) Return Studenti objects filtered by the crediti_terza column
 * @method array findByMediaVotiTerza(string $media_voti_terza) Return Studenti objects filtered by the media_voti_terza column
 * @method array findByDebitiTerza(string $debiti_terza) Return Studenti objects filtered by the debiti_terza column
 * @method array findByCreditiSospesiTerza(int $crediti_sospesi_terza) Return Studenti objects filtered by the crediti_sospesi_terza column
 * @method array findByCreditiReintegratiTerza(int $crediti_reintegrati_terza) Return Studenti objects filtered by the crediti_reintegrati_terza column
 * @method array findByCreditiQuarta(int $crediti_quarta) Return Studenti objects filtered by the crediti_quarta column
 * @method array findByMediaVotiQuarta(string $media_voti_quarta) Return Studenti objects filtered by the media_voti_quarta column
 * @method array findByDebitiQuarta(string $debiti_quarta) Return Studenti objects filtered by the debiti_quarta column
 * @method array findByCreditiSospesiQuarta(int $crediti_sospesi_quarta) Return Studenti objects filtered by the crediti_sospesi_quarta column
 * @method array findByCreditiReintegratiQuarta(int $crediti_reintegrati_quarta) Return Studenti objects filtered by the crediti_reintegrati_quarta column
 * @method array findByCreditiQuinta(int $crediti_quinta) Return Studenti objects filtered by the crediti_quinta column
 * @method array findByMediaVotiQuinta(string $media_voti_quinta) Return Studenti objects filtered by the media_voti_quinta column
 * @method array findByCreditiFinaliAgg(int $crediti_finali_agg) Return Studenti objects filtered by the crediti_finali_agg column
 * @method array findByMatricola(string $matricola) Return Studenti objects filtered by the matricola column
 * @method array findByLuogoNascita(string $luogo_nascita) Return Studenti objects filtered by the luogo_nascita column
 * @method array findByProvinciaNascita(string $provincia_nascita) Return Studenti objects filtered by the provincia_nascita column
 * @method array findByMotiviCreditiTerza(string $motivi_crediti_terza) Return Studenti objects filtered by the motivi_crediti_terza column
 * @method array findByMotiviCreditiQuarta(string $motivi_crediti_quarta) Return Studenti objects filtered by the motivi_crediti_quarta column
 * @method array findByMotiviCreditiQuinta(string $motivi_crediti_quinta) Return Studenti objects filtered by the motivi_crediti_quinta column
 * @method array findByMotiviCreditiAgg(string $motivi_crediti_agg) Return Studenti objects filtered by the motivi_crediti_agg column
 * @method array findByCodiceComuneNascita(string $codice_comune_nascita) Return Studenti objects filtered by the codice_comune_nascita column
 * @method array findByStatoNascita(string $stato_nascita) Return Studenti objects filtered by the stato_nascita column
 * @method array findByCittadinanza(string $cittadinanza) Return Studenti objects filtered by the cittadinanza column
 * @method array findBySecondaCittadinanza(string $seconda_cittadinanza) Return Studenti objects filtered by the seconda_cittadinanza column
 * @method array findByCodiceComuneResidenza(string $codice_comune_residenza) Return Studenti objects filtered by the codice_comune_residenza column
 * @method array findByDistretto(string $distretto) Return Studenti objects filtered by the distretto column
 * @method array findByCodiceFiscale(string $codice_fiscale) Return Studenti objects filtered by the codice_fiscale column
 * @method array findByMedico(string $medico) Return Studenti objects filtered by the medico column
 * @method array findByTelefonoMedico(string $telefono_medico) Return Studenti objects filtered by the telefono_medico column
 * @method array findByIntolleranzeAlim(string $intolleranze_alim) Return Studenti objects filtered by the intolleranze_alim column
 * @method array findByGruppoSanguigno(string $gruppo_sanguigno) Return Studenti objects filtered by the gruppo_sanguigno column
 * @method array findByGruppoRh(string $gruppo_rh) Return Studenti objects filtered by the gruppo_rh column
 * @method array findByCodiceAsl(string $codice_asl) Return Studenti objects filtered by the codice_asl column
 * @method array findByAnnotazioni(string $annotazioni) Return Studenti objects filtered by the annotazioni column
 * @method array findByStatoCivile(int $stato_civile) Return Studenti objects filtered by the stato_civile column
 * @method array findByVotoPrimoScritto(int $voto_primo_scritto) Return Studenti objects filtered by the voto_primo_scritto column
 * @method array findByVotoSecondoScritto(int $voto_secondo_scritto) Return Studenti objects filtered by the voto_secondo_scritto column
 * @method array findByVotoTerzoScritto(int $voto_terzo_scritto) Return Studenti objects filtered by the voto_terzo_scritto column
 * @method array findByVotoOrale(int $voto_orale) Return Studenti objects filtered by the voto_orale column
 * @method array findByVotoBonus(int $voto_bonus) Return Studenti objects filtered by the voto_bonus column
 * @method array findByMateriaSecondoScr(string $materia_secondo_scr) Return Studenti objects filtered by the materia_secondo_scr column
 * @method array findByUlterioriSpecifDiploma(string $ulteriori_specif_diploma) Return Studenti objects filtered by the ulteriori_specif_diploma column
 * @method array findByNumeroDiploma(int $numero_diploma) Return Studenti objects filtered by the numero_diploma column
 * @method array findByChiInserisce(string $chi_inserisce) Return Studenti objects filtered by the chi_inserisce column
 * @method array findByDataInserimento(string $data_inserimento) Return Studenti objects filtered by the data_inserimento column
 * @method array findByTipoInserimento(string $tipo_inserimento) Return Studenti objects filtered by the tipo_inserimento column
 * @method array findByChiModifica(string $chi_modifica) Return Studenti objects filtered by the chi_modifica column
 * @method array findByDataModifica(string $data_modifica) Return Studenti objects filtered by the data_modifica column
 * @method array findByTipoModifica(string $tipo_modifica) Return Studenti objects filtered by the tipo_modifica column
 * @method array findByFlagCanc(string $flag_canc) Return Studenti objects filtered by the flag_canc column
 * @method array findByStatoAvanzamento(string $stato_avanzamento) Return Studenti objects filtered by the stato_avanzamento column
 * @method array findByDataStatoAvanzamento(string $data_stato_avanzamento) Return Studenti objects filtered by the data_stato_avanzamento column
 * @method array findByCapProvinciaNascita(string $cap_provincia_nascita) Return Studenti objects filtered by the cap_provincia_nascita column
 * @method array findByBadge(string $badge) Return Studenti objects filtered by the badge column
 * @method array findByCapResidenza(string $cap_residenza) Return Studenti objects filtered by the cap_residenza column
 * @method array findByCodiceComuneDomicilio(string $codice_comune_domicilio) Return Studenti objects filtered by the codice_comune_domicilio column
 * @method array findByCapDomicilio(string $cap_domicilio) Return Studenti objects filtered by the cap_domicilio column
 * @method array findByCapNascita(string $cap_nascita) Return Studenti objects filtered by the cap_nascita column
 * @method array findByIndirizzoDomicilio(string $indirizzo_domicilio) Return Studenti objects filtered by the indirizzo_domicilio column
 * @method array findByCittaNascitaStraniera(string $citta_nascita_straniera) Return Studenti objects filtered by the citta_nascita_straniera column
 * @method array findByCellulareAllievo(string $cellulare_allievo) Return Studenti objects filtered by the cellulare_allievo column
 * @method array findByHandicap(string $handicap) Return Studenti objects filtered by the handicap column
 * @method array findByStatoConvittore(string $stato_convittore) Return Studenti objects filtered by the stato_convittore column
 * @method array findByDataRitiro(string $data_ritiro) Return Studenti objects filtered by the data_ritiro column
 * @method array findByVotoAmmissione(string $voto_ammissione) Return Studenti objects filtered by the voto_ammissione column
 * @method array findByDifferenzaPunteggio(string $differenza_punteggio) Return Studenti objects filtered by the differenza_punteggio column
 * @method array findByVotoQualifica(string $voto_qualifica) Return Studenti objects filtered by the voto_qualifica column
 * @method array findByVotoEsameSc1Qual(string $voto_esame_sc1_qual) Return Studenti objects filtered by the voto_esame_sc1_qual column
 * @method array findByVotoEsameSc2Qual(string $voto_esame_sc2_qual) Return Studenti objects filtered by the voto_esame_sc2_qual column
 * @method array findByVotoEsameOrQual(string $voto_esame_or_qual) Return Studenti objects filtered by the voto_esame_or_qual column
 * @method array findByStatoPrivatista(string $stato_privatista) Return Studenti objects filtered by the stato_privatista column
 * @method array findByFoto(string $foto) Return Studenti objects filtered by the foto column
 * @method array findByRappresentante(string $rappresentante) Return Studenti objects filtered by the rappresentante column
 * @method array findByObbligoFormativo(string $obbligo_formativo) Return Studenti objects filtered by the obbligo_formativo column
 * @method array findByIdLingua1(string $id_lingua_1) Return Studenti objects filtered by the id_lingua_1 column
 * @method array findByIdLingua2(string $id_lingua_2) Return Studenti objects filtered by the id_lingua_2 column
 * @method array findByIdLingua3(string $id_lingua_3) Return Studenti objects filtered by the id_lingua_3 column
 * @method array findByIdLingua4(string $id_lingua_4) Return Studenti objects filtered by the id_lingua_4 column
 * @method array findByIdLingua5(string $id_lingua_5) Return Studenti objects filtered by the id_lingua_5 column
 * @method array findByIdProvenienzaScolastica(string $id_provenienza_scolastica) Return Studenti objects filtered by the id_provenienza_scolastica column
 * @method array findByIdScuolaMedia(string $id_scuola_media) Return Studenti objects filtered by the id_scuola_media column
 * @method array findByLinguaScuolaMedia(string $lingua_scuola_media) Return Studenti objects filtered by the lingua_scuola_media column
 * @method array findByLinguaScuolaMedia2(string $lingua_scuola_media_2) Return Studenti objects filtered by the lingua_scuola_media_2 column
 * @method array findByGiudizioScuolaMedia(string $giudizio_scuola_media) Return Studenti objects filtered by the giudizio_scuola_media column
 * @method array findByTrasporto(string $trasporto) Return Studenti objects filtered by the trasporto column
 * @method array findByDataIscrizione(string $data_iscrizione) Return Studenti objects filtered by the data_iscrizione column
 * @method array findByPei(string $pei) Return Studenti objects filtered by the pei column
 * @method array findByAmmessoEsameQualifica(string $ammesso_esame_qualifica) Return Studenti objects filtered by the ammesso_esame_qualifica column
 * @method array findByAmmessoEsameQuinta(string $ammesso_esame_quinta) Return Studenti objects filtered by the ammesso_esame_quinta column
 * @method array findByGiudizioAmmissioneQuinta(string $giudizio_ammissione_quinta) Return Studenti objects filtered by the giudizio_ammissione_quinta column
 * @method array findByGradoHandicap(string $grado_handicap) Return Studenti objects filtered by the grado_handicap column
 * @method array findByTipoHandicap(string $tipo_handicap) Return Studenti objects filtered by the tipo_handicap column
 * @method array findByStatoLicenzaMaestro(string $stato_licenza_maestro) Return Studenti objects filtered by the stato_licenza_maestro column
 * @method array findByIdStudenteSissi(string $id_studente_sissi) Return Studenti objects filtered by the id_studente_sissi column
 * @method array findByBadgeRfid(string $badge_rfid) Return Studenti objects filtered by the badge_rfid column
 * @method array findByLode(string $lode) Return Studenti objects filtered by the lode column
 * @method array findByDistrettoScolastico(string $distretto_scolastico) Return Studenti objects filtered by the distretto_scolastico column
 * @method array findByGiudizioAmmissioneTerza(string $giudizio_ammissione_terza) Return Studenti objects filtered by the giudizio_ammissione_terza column
 * @method array findByEsitoPrimaMedia(string $esito_prima_media) Return Studenti objects filtered by the esito_prima_media column
 * @method array findByEsitoSecondaMedia(string $esito_seconda_media) Return Studenti objects filtered by the esito_seconda_media column
 * @method array findByEsitoTerzaMedia(string $esito_terza_media) Return Studenti objects filtered by the esito_terza_media column
 * @method array findByGiudizioEsameSc1Qual(string $giudizio_esame_sc1_qual) Return Studenti objects filtered by the giudizio_esame_sc1_qual column
 * @method array findByGiudizioEsameSc2Qual(string $giudizio_esame_sc2_qual) Return Studenti objects filtered by the giudizio_esame_sc2_qual column
 * @method array findByGiudizioEsameOrQual(string $giudizio_esame_or_qual) Return Studenti objects filtered by the giudizio_esame_or_qual column
 * @method array findByGiudizioComplessivoEsameQual(string $giudizio_complessivo_esame_qual) Return Studenti objects filtered by the giudizio_complessivo_esame_qual column
 * @method array findByAcconsenteAziende(int $acconsente_aziende) Return Studenti objects filtered by the acconsente_aziende column
 * @method array findByCurriculumPrima(string $curriculum_prima) Return Studenti objects filtered by the curriculum_prima column
 * @method array findByCurriculumSeconda(string $curriculum_seconda) Return Studenti objects filtered by the curriculum_seconda column
 * @method array findByStageProfessionali(string $stage_professionali) Return Studenti objects filtered by the stage_professionali column
 * @method array findByDataOrale(string $data_orale) Return Studenti objects filtered by the data_orale column
 * @method array findByOrdineEsameOrale(string $ordine_esame_orale) Return Studenti objects filtered by the ordine_esame_orale column
 * @method array findByTipoPrimoScritto(string $tipo_primo_scritto) Return Studenti objects filtered by the tipo_primo_scritto column
 * @method array findByTipoSecondoScritto(string $tipo_secondo_scritto) Return Studenti objects filtered by the tipo_secondo_scritto column
 * @method array findByTipoTerzoScritto(string $tipo_terzo_scritto) Return Studenti objects filtered by the tipo_terzo_scritto column
 * @method array findByUnanimitaPrimoScritto(string $unanimita_primo_scritto) Return Studenti objects filtered by the unanimita_primo_scritto column
 * @method array findByUnanimitaSecondoScritto(string $unanimita_secondo_scritto) Return Studenti objects filtered by the unanimita_secondo_scritto column
 * @method array findByUnanimitaTerzoScritto(string $unanimita_terzo_scritto) Return Studenti objects filtered by the unanimita_terzo_scritto column
 * @method array findByArgomentoSceltoOrale(string $argomento_scelto_orale) Return Studenti objects filtered by the argomento_scelto_orale column
 * @method array findByAreaDisc1Orale(string $area_disc_1_orale) Return Studenti objects filtered by the area_disc_1_orale column
 * @method array findByAreaDisc2Orale(string $area_disc_2_orale) Return Studenti objects filtered by the area_disc_2_orale column
 * @method array findByDiscElaboratiOrale(string $disc_elaborati_orale) Return Studenti objects filtered by the disc_elaborati_orale column
 * @method array findByUnanimitaVotoFinale(string $unanimita_voto_finale) Return Studenti objects filtered by the unanimita_voto_finale column
 * @method array findByPresenteEsameQuinta(string $presente_esame_quinta) Return Studenti objects filtered by the presente_esame_quinta column
 * @method array findByStampaBadge(string $stampa_badge) Return Studenti objects filtered by the stampa_badge column
 * @method array findByIdClasseDestinazione(int $id_classe_destinazione) Return Studenti objects filtered by the id_classe_destinazione column
 * @method array findByScontoRette(int $sconto_rette) Return Studenti objects filtered by the sconto_rette column
 * @method array findByCartaStudenteNumero(string $carta_studente_numero) Return Studenti objects filtered by the carta_studente_numero column
 * @method array findByCartaStudenteScadenza(int $carta_studente_scadenza) Return Studenti objects filtered by the carta_studente_scadenza column
 * @method array findByEsitoCorrenteCalcolato(string $esito_corrente_calcolato) Return Studenti objects filtered by the esito_corrente_calcolato column
 * @method array findByIdFlusso(string $id_flusso) Return Studenti objects filtered by the id_flusso column
 * @method array findByDataAggiornamentoSogei(string $data_aggiornamento_sogei) Return Studenti objects filtered by the data_aggiornamento_sogei column
 * @method array findByCodiceAlunnoMinisteriale(string $codice_alunno_ministeriale) Return Studenti objects filtered by the codice_alunno_ministeriale column
 * @method array findByFlagCfFittizio(int $flag_cf_fittizio) Return Studenti objects filtered by the flag_cf_fittizio column
 * @method array findByFlagS2f(string $flag_s2f) Return Studenti objects filtered by the flag_s2f column
 * @method array findByCodiceStatoSogei(string $codice_stato_sogei) Return Studenti objects filtered by the codice_stato_sogei column
 * @method array findByCodiceGruppoNomade(string $codice_gruppo_nomade) Return Studenti objects filtered by the codice_gruppo_nomade column
 * @method array findByFlagMinoreStraniero(int $flag_minore_straniero) Return Studenti objects filtered by the flag_minore_straniero column
 * @method array findByChiave(string $chiave) Return Studenti objects filtered by the chiave column
 * @method array findByVotoEsameMedieItaliano(string $voto_esame_medie_italiano) Return Studenti objects filtered by the voto_esame_medie_italiano column
 * @method array findByVotoEsameMedieInglese(string $voto_esame_medie_inglese) Return Studenti objects filtered by the voto_esame_medie_inglese column
 * @method array findByVotoEsameMedieMatematica(string $voto_esame_medie_matematica) Return Studenti objects filtered by the voto_esame_medie_matematica column
 * @method array findByVotoEsameMedieSecondaLingua(string $voto_esame_medie_seconda_lingua) Return Studenti objects filtered by the voto_esame_medie_seconda_lingua column
 * @method array findByVotoEsameMedieInvalsiIta(string $voto_esame_medie_invalsi_ita) Return Studenti objects filtered by the voto_esame_medie_invalsi_ita column
 * @method array findByVotoEsameMedieInvalsiMat(string $voto_esame_medie_invalsi_mat) Return Studenti objects filtered by the voto_esame_medie_invalsi_mat column
 * @method array findByVotoEsameMedieOrale(string $voto_esame_medie_orale) Return Studenti objects filtered by the voto_esame_medie_orale column
 * @method array findByVotoAmmissioneMedie(string $voto_ammissione_medie) Return Studenti objects filtered by the voto_ammissione_medie column
 * @method array findByEsitoPrimaElementare(string $esito_prima_elementare) Return Studenti objects filtered by the esito_prima_elementare column
 * @method array findByEsitoSecondaElementare(string $esito_seconda_elementare) Return Studenti objects filtered by the esito_seconda_elementare column
 * @method array findByEsitoTerzaElementare(string $esito_terza_elementare) Return Studenti objects filtered by the esito_terza_elementare column
 * @method array findByEsitoQuartaElementare(string $esito_quarta_elementare) Return Studenti objects filtered by the esito_quarta_elementare column
 * @method array findByEsitoQuintaElementare(string $esito_quinta_elementare) Return Studenti objects filtered by the esito_quinta_elementare column
 * @method array findByTipoVotoEsameMedieItaliano(string $tipo_voto_esame_medie_italiano) Return Studenti objects filtered by the tipo_voto_esame_medie_italiano column
 * @method array findByTipoVotoEsameMedieInglese(string $tipo_voto_esame_medie_inglese) Return Studenti objects filtered by the tipo_voto_esame_medie_inglese column
 * @method array findByGiudizio1Medie(string $giudizio_1_medie) Return Studenti objects filtered by the giudizio_1_medie column
 * @method array findByGiudizio2Medie(string $giudizio_2_medie) Return Studenti objects filtered by the giudizio_2_medie column
 * @method array findByGiudizio3Medie(string $giudizio_3_medie) Return Studenti objects filtered by the giudizio_3_medie column
 * @method array findByArgomentiOraliMedie(string $argomenti_orali_medie) Return Studenti objects filtered by the argomenti_orali_medie column
 * @method array findByGiudizioFinale1Medie(string $giudizio_finale_1_medie) Return Studenti objects filtered by the giudizio_finale_1_medie column
 * @method array findByGiudizioFinale2Medie(string $giudizio_finale_2_medie) Return Studenti objects filtered by the giudizio_finale_2_medie column
 * @method array findByGiudizioFinale3Medie(string $giudizio_finale_3_medie) Return Studenti objects filtered by the giudizio_finale_3_medie column
 * @method array findByConsiglioTerzaMedia(string $consiglio_terza_media) Return Studenti objects filtered by the consiglio_terza_media column
 * @method array findByGiudizioSinteticoEsameTerzaMedia(string $giudizio_sintetico_esame_terza_media) Return Studenti objects filtered by the giudizio_sintetico_esame_terza_media column
 * @method array findByDataArrivoInItalia(int $data_arrivo_in_italia) Return Studenti objects filtered by the data_arrivo_in_italia column
 * @method array findByFrequenzaAsiloNido(int $frequenza_asilo_nido) Return Studenti objects filtered by the frequenza_asilo_nido column
 * @method array findByFrequenzaScuolaMaterna(int $frequenza_scuola_materna) Return Studenti objects filtered by the frequenza_scuola_materna column
 * @method array findByDataAggiornamentoSidi(int $data_aggiornamento_sidi) Return Studenti objects filtered by the data_aggiornamento_sidi column
 * @method array findByCmpSupValIta(string $cmp_sup_val_ita) Return Studenti objects filtered by the cmp_sup_val_ita column
 * @method array findByCmpSupTxtIta(string $cmp_sup_txt_ita) Return Studenti objects filtered by the cmp_sup_txt_ita column
 * @method array findByCmpSupValIng(string $cmp_sup_val_ing) Return Studenti objects filtered by the cmp_sup_val_ing column
 * @method array findByCmpSupTxtIng(string $cmp_sup_txt_ing) Return Studenti objects filtered by the cmp_sup_txt_ing column
 * @method array findByCmpSupValAltri(string $cmp_sup_val_altri) Return Studenti objects filtered by the cmp_sup_val_altri column
 * @method array findByCmpSupTxtAltri(string $cmp_sup_txt_altri) Return Studenti objects filtered by the cmp_sup_txt_altri column
 * @method array findByCmpSupValMat(string $cmp_sup_val_mat) Return Studenti objects filtered by the cmp_sup_val_mat column
 * @method array findByCmpSupTxtMat(string $cmp_sup_txt_mat) Return Studenti objects filtered by the cmp_sup_txt_mat column
 * @method array findByCmpSupValSciTec(string $cmp_sup_val_sci_tec) Return Studenti objects filtered by the cmp_sup_val_sci_tec column
 * @method array findByCmpSupTxtSciTec(string $cmp_sup_txt_sci_tec) Return Studenti objects filtered by the cmp_sup_txt_sci_tec column
 * @method array findByCmpSupValStoSoc(string $cmp_sup_val_sto_soc) Return Studenti objects filtered by the cmp_sup_val_sto_soc column
 * @method array findByCmpSupTxtStoSoc(string $cmp_sup_txt_sto_soc) Return Studenti objects filtered by the cmp_sup_txt_sto_soc column
 * @method array findByCmpMedValIta(string $cmp_med_val_ita) Return Studenti objects filtered by the cmp_med_val_ita column
 * @method array findByCmpMedTxtIta(string $cmp_med_txt_ita) Return Studenti objects filtered by the cmp_med_txt_ita column
 * @method array findByCmpMedValIng(string $cmp_med_val_ing) Return Studenti objects filtered by the cmp_med_val_ing column
 * @method array findByCmpMedTxtIng(string $cmp_med_txt_ing) Return Studenti objects filtered by the cmp_med_txt_ing column
 * @method array findByCmpMedValAltri(string $cmp_med_val_altri) Return Studenti objects filtered by the cmp_med_val_altri column
 * @method array findByCmpMedTxtAltri(string $cmp_med_txt_altri) Return Studenti objects filtered by the cmp_med_txt_altri column
 * @method array findByCmpMedValMat(string $cmp_med_val_mat) Return Studenti objects filtered by the cmp_med_val_mat column
 * @method array findByCmpMedTxtMat(string $cmp_med_txt_mat) Return Studenti objects filtered by the cmp_med_txt_mat column
 * @method array findByCmpMedValSciTec(string $cmp_med_val_sci_tec) Return Studenti objects filtered by the cmp_med_val_sci_tec column
 * @method array findByCmpMedTxtSciTec(string $cmp_med_txt_sci_tec) Return Studenti objects filtered by the cmp_med_txt_sci_tec column
 * @method array findByCmpMedValStoSoc(string $cmp_med_val_sto_soc) Return Studenti objects filtered by the cmp_med_val_sto_soc column
 * @method array findByCmpMedTxtStoSoc(string $cmp_med_txt_sto_soc) Return Studenti objects filtered by the cmp_med_txt_sto_soc column
 * @method array findByCmpMedValL2(string $cmp_med_val_l2) Return Studenti objects filtered by the cmp_med_val_l2 column
 * @method array findByCmpMedTxtL2(string $cmp_med_txt_l2) Return Studenti objects filtered by the cmp_med_txt_l2 column
 * @method array findByCmpMedValL3(string $cmp_med_val_l3) Return Studenti objects filtered by the cmp_med_val_l3 column
 * @method array findByCmpMedTxtL3(string $cmp_med_txt_l3) Return Studenti objects filtered by the cmp_med_txt_l3 column
 * @method array findByCmpMedValArte(string $cmp_med_val_arte) Return Studenti objects filtered by the cmp_med_val_arte column
 * @method array findByCmpMedTxtArte(string $cmp_med_txt_arte) Return Studenti objects filtered by the cmp_med_txt_arte column
 * @method array findByCmpMedValMus(string $cmp_med_val_mus) Return Studenti objects filtered by the cmp_med_val_mus column
 * @method array findByCmpMedTxtMus(string $cmp_med_txt_mus) Return Studenti objects filtered by the cmp_med_txt_mus column
 * @method array findByCmpMedValMot(string $cmp_med_val_mot) Return Studenti objects filtered by the cmp_med_val_mot column
 * @method array findByCmpMedTxtMot(string $cmp_med_txt_mot) Return Studenti objects filtered by the cmp_med_txt_mot column
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseStudentiQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseStudentiQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Ccp\\Studenti';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new StudentiQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   StudentiQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return StudentiQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof StudentiQuery) {
            return $criteria;
        }
        $query = new StudentiQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Studenti|Studenti[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = StudentiPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(StudentiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Studenti A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByIdStudente($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Studenti A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id_studente", "nome", "cognome", "indirizzo", "citta", "cap", "provincia", "sesso", "telefono", "cellulare1", "cellulare2", "email1", "email2", "invio_email", "invio_email_cumulativo", "invio_email_parametrico", "invio_email_temporale", "tipo_sms", "tipo_sms_cumulativo", "tipo_sms_parametrico", "tipo_sms_temporale", "aut_entrata_ritardo", "aut_uscita_anticipo", "aut_pomeriggio", "acconsente", "ritirato", "data_nascita", "codice_studente", "password_studente", "codice_giustificazioni_studente", "esonero_religione", "materia_sostitutiva_religione", "esonero_ed_fisica", "materia_sostitutiva_edfisica", "crediti_terza", "media_voti_terza", "debiti_terza", "crediti_sospesi_terza", "crediti_reintegrati_terza", "crediti_quarta", "media_voti_quarta", "debiti_quarta", "crediti_sospesi_quarta", "crediti_reintegrati_quarta", "crediti_quinta", "media_voti_quinta", "crediti_finali_agg", "matricola", "luogo_nascita", "provincia_nascita", "motivi_crediti_terza", "motivi_crediti_quarta", "motivi_crediti_quinta", "motivi_crediti_agg", "codice_comune_nascita", "stato_nascita", "cittadinanza", "seconda_cittadinanza", "codice_comune_residenza", "distretto", "codice_fiscale", "medico", "telefono_medico", "intolleranze_alim", "gruppo_sanguigno", "gruppo_rh", "codice_asl", "annotazioni", "stato_civile", "voto_primo_scritto", "voto_secondo_scritto", "voto_terzo_scritto", "voto_orale", "voto_bonus", "materia_secondo_scr", "ulteriori_specif_diploma", "numero_diploma", "chi_inserisce", "data_inserimento", "tipo_inserimento", "chi_modifica", "data_modifica", "tipo_modifica", "flag_canc", "stato_avanzamento", "data_stato_avanzamento", "cap_provincia_nascita", "badge", "cap_residenza", "codice_comune_domicilio", "cap_domicilio", "cap_nascita", "indirizzo_domicilio", "citta_nascita_straniera", "cellulare_allievo", "handicap", "stato_convittore", "data_ritiro", "voto_ammissione", "differenza_punteggio", "voto_qualifica", "voto_esame_sc1_qual", "voto_esame_sc2_qual", "voto_esame_or_qual", "stato_privatista", "foto", "rappresentante", "obbligo_formativo", "id_lingua_1", "id_lingua_2", "id_lingua_3", "id_lingua_4", "id_lingua_5", "id_provenienza_scolastica", "id_scuola_media", "lingua_scuola_media", "lingua_scuola_media_2", "giudizio_scuola_media", "trasporto", "data_iscrizione", "pei", "ammesso_esame_qualifica", "ammesso_esame_quinta", "giudizio_ammissione_quinta", "grado_handicap", "tipo_handicap", "stato_licenza_maestro", "id_studente_sissi", "badge_rfid", "lode", "distretto_scolastico", "giudizio_ammissione_terza", "esito_prima_media", "esito_seconda_media", "esito_terza_media", "giudizio_esame_sc1_qual", "giudizio_esame_sc2_qual", "giudizio_esame_or_qual", "giudizio_complessivo_esame_qual", "acconsente_aziende", "curriculum_prima", "curriculum_seconda", "stage_professionali", "data_orale", "ordine_esame_orale", "tipo_primo_scritto", "tipo_secondo_scritto", "tipo_terzo_scritto", "unanimita_primo_scritto", "unanimita_secondo_scritto", "unanimita_terzo_scritto", "argomento_scelto_orale", "area_disc_1_orale", "area_disc_2_orale", "disc_elaborati_orale", "unanimita_voto_finale", "presente_esame_quinta", "stampa_badge", "id_classe_destinazione", "sconto_rette", "carta_studente_numero", "carta_studente_scadenza", "esito_corrente_calcolato", "id_flusso", "data_aggiornamento_sogei", "codice_alunno_ministeriale", "flag_cf_fittizio", "flag_s2f", "codice_stato_sogei", "codice_gruppo_nomade", "flag_minore_straniero", "chiave", "voto_esame_medie_italiano", "voto_esame_medie_inglese", "voto_esame_medie_matematica", "voto_esame_medie_seconda_lingua", "voto_esame_medie_invalsi_ita", "voto_esame_medie_invalsi_mat", "voto_esame_medie_orale", "voto_ammissione_medie", "esito_prima_elementare", "esito_seconda_elementare", "esito_terza_elementare", "esito_quarta_elementare", "esito_quinta_elementare", "tipo_voto_esame_medie_italiano", "tipo_voto_esame_medie_inglese", "giudizio_1_medie", "giudizio_2_medie", "giudizio_3_medie", "argomenti_orali_medie", "giudizio_finale_1_medie", "giudizio_finale_2_medie", "giudizio_finale_3_medie", "consiglio_terza_media", "giudizio_sintetico_esame_terza_media", "data_arrivo_in_italia", "frequenza_asilo_nido", "frequenza_scuola_materna", "data_aggiornamento_sidi", "cmp_sup_val_ita", "cmp_sup_txt_ita", "cmp_sup_val_ing", "cmp_sup_txt_ing", "cmp_sup_val_altri", "cmp_sup_txt_altri", "cmp_sup_val_mat", "cmp_sup_txt_mat", "cmp_sup_val_sci_tec", "cmp_sup_txt_sci_tec", "cmp_sup_val_sto_soc", "cmp_sup_txt_sto_soc", "cmp_med_val_ita", "cmp_med_txt_ita", "cmp_med_val_ing", "cmp_med_txt_ing", "cmp_med_val_altri", "cmp_med_txt_altri", "cmp_med_val_mat", "cmp_med_txt_mat", "cmp_med_val_sci_tec", "cmp_med_txt_sci_tec", "cmp_med_val_sto_soc", "cmp_med_txt_sto_soc", "cmp_med_val_l2", "cmp_med_txt_l2", "cmp_med_val_l3", "cmp_med_txt_l3", "cmp_med_val_arte", "cmp_med_txt_arte", "cmp_med_val_mus", "cmp_med_txt_mus", "cmp_med_val_mot", "cmp_med_txt_mot" FROM "studenti" WHERE "id_studente" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Studenti();
            $obj->hydrate($row);
            StudentiPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Studenti|Studenti[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Studenti[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(StudentiPeer::ID_STUDENTE, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(StudentiPeer::ID_STUDENTE, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id_studente column
     *
     * Example usage:
     * <code>
     * $query->filterByIdStudente(1234); // WHERE id_studente = 1234
     * $query->filterByIdStudente(array(12, 34)); // WHERE id_studente IN (12, 34)
     * $query->filterByIdStudente(array('min' => 12)); // WHERE id_studente >= 12
     * $query->filterByIdStudente(array('max' => 12)); // WHERE id_studente <= 12
     * </code>
     *
     * @param     mixed $idStudente The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdStudente($idStudente = null, $comparison = null)
    {
        if (is_array($idStudente)) {
            $useMinMax = false;
            if (isset($idStudente['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_STUDENTE, $idStudente['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idStudente['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_STUDENTE, $idStudente['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_STUDENTE, $idStudente, $comparison);
    }

    /**
     * Filter the query on the nome column
     *
     * Example usage:
     * <code>
     * $query->filterByNome('fooValue');   // WHERE nome = 'fooValue'
     * $query->filterByNome('%fooValue%'); // WHERE nome LIKE '%fooValue%'
     * </code>
     *
     * @param     string $nome The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByNome($nome = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($nome)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $nome)) {
                $nome = str_replace('*', '%', $nome);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::NOME, $nome, $comparison);
    }

    /**
     * Filter the query on the cognome column
     *
     * Example usage:
     * <code>
     * $query->filterByCognome('fooValue');   // WHERE cognome = 'fooValue'
     * $query->filterByCognome('%fooValue%'); // WHERE cognome LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cognome The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCognome($cognome = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cognome)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cognome)) {
                $cognome = str_replace('*', '%', $cognome);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::COGNOME, $cognome, $comparison);
    }

    /**
     * Filter the query on the indirizzo column
     *
     * Example usage:
     * <code>
     * $query->filterByIndirizzo('fooValue');   // WHERE indirizzo = 'fooValue'
     * $query->filterByIndirizzo('%fooValue%'); // WHERE indirizzo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $indirizzo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIndirizzo($indirizzo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($indirizzo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $indirizzo)) {
                $indirizzo = str_replace('*', '%', $indirizzo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INDIRIZZO, $indirizzo, $comparison);
    }

    /**
     * Filter the query on the citta column
     *
     * Example usage:
     * <code>
     * $query->filterByCitta('fooValue');   // WHERE citta = 'fooValue'
     * $query->filterByCitta('%fooValue%'); // WHERE citta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $citta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCitta($citta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($citta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $citta)) {
                $citta = str_replace('*', '%', $citta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CITTA, $citta, $comparison);
    }

    /**
     * Filter the query on the cap column
     *
     * Example usage:
     * <code>
     * $query->filterByCap('fooValue');   // WHERE cap = 'fooValue'
     * $query->filterByCap('%fooValue%'); // WHERE cap LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cap The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCap($cap = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cap)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cap)) {
                $cap = str_replace('*', '%', $cap);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CAP, $cap, $comparison);
    }

    /**
     * Filter the query on the provincia column
     *
     * Example usage:
     * <code>
     * $query->filterByProvincia('fooValue');   // WHERE provincia = 'fooValue'
     * $query->filterByProvincia('%fooValue%'); // WHERE provincia LIKE '%fooValue%'
     * </code>
     *
     * @param     string $provincia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByProvincia($provincia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($provincia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $provincia)) {
                $provincia = str_replace('*', '%', $provincia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::PROVINCIA, $provincia, $comparison);
    }

    /**
     * Filter the query on the sesso column
     *
     * Example usage:
     * <code>
     * $query->filterBySesso('fooValue');   // WHERE sesso = 'fooValue'
     * $query->filterBySesso('%fooValue%'); // WHERE sesso LIKE '%fooValue%'
     * </code>
     *
     * @param     string $sesso The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterBySesso($sesso = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($sesso)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $sesso)) {
                $sesso = str_replace('*', '%', $sesso);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::SESSO, $sesso, $comparison);
    }

    /**
     * Filter the query on the telefono column
     *
     * Example usage:
     * <code>
     * $query->filterByTelefono('fooValue');   // WHERE telefono = 'fooValue'
     * $query->filterByTelefono('%fooValue%'); // WHERE telefono LIKE '%fooValue%'
     * </code>
     *
     * @param     string $telefono The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTelefono($telefono = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($telefono)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $telefono)) {
                $telefono = str_replace('*', '%', $telefono);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TELEFONO, $telefono, $comparison);
    }

    /**
     * Filter the query on the cellulare1 column
     *
     * Example usage:
     * <code>
     * $query->filterByCellulare1('fooValue');   // WHERE cellulare1 = 'fooValue'
     * $query->filterByCellulare1('%fooValue%'); // WHERE cellulare1 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cellulare1 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCellulare1($cellulare1 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cellulare1)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cellulare1)) {
                $cellulare1 = str_replace('*', '%', $cellulare1);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CELLULARE1, $cellulare1, $comparison);
    }

    /**
     * Filter the query on the cellulare2 column
     *
     * Example usage:
     * <code>
     * $query->filterByCellulare2('fooValue');   // WHERE cellulare2 = 'fooValue'
     * $query->filterByCellulare2('%fooValue%'); // WHERE cellulare2 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cellulare2 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCellulare2($cellulare2 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cellulare2)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cellulare2)) {
                $cellulare2 = str_replace('*', '%', $cellulare2);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CELLULARE2, $cellulare2, $comparison);
    }

    /**
     * Filter the query on the email1 column
     *
     * Example usage:
     * <code>
     * $query->filterByEmail1('fooValue');   // WHERE email1 = 'fooValue'
     * $query->filterByEmail1('%fooValue%'); // WHERE email1 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $email1 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEmail1($email1 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($email1)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $email1)) {
                $email1 = str_replace('*', '%', $email1);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::EMAIL1, $email1, $comparison);
    }

    /**
     * Filter the query on the email2 column
     *
     * Example usage:
     * <code>
     * $query->filterByEmail2('fooValue');   // WHERE email2 = 'fooValue'
     * $query->filterByEmail2('%fooValue%'); // WHERE email2 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $email2 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEmail2($email2 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($email2)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $email2)) {
                $email2 = str_replace('*', '%', $email2);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::EMAIL2, $email2, $comparison);
    }

    /**
     * Filter the query on the invio_email column
     *
     * Example usage:
     * <code>
     * $query->filterByInvioEmail('fooValue');   // WHERE invio_email = 'fooValue'
     * $query->filterByInvioEmail('%fooValue%'); // WHERE invio_email LIKE '%fooValue%'
     * </code>
     *
     * @param     string $invioEmail The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByInvioEmail($invioEmail = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($invioEmail)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $invioEmail)) {
                $invioEmail = str_replace('*', '%', $invioEmail);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INVIO_EMAIL, $invioEmail, $comparison);
    }

    /**
     * Filter the query on the invio_email_cumulativo column
     *
     * Example usage:
     * <code>
     * $query->filterByInvioEmailCumulativo('fooValue');   // WHERE invio_email_cumulativo = 'fooValue'
     * $query->filterByInvioEmailCumulativo('%fooValue%'); // WHERE invio_email_cumulativo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $invioEmailCumulativo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByInvioEmailCumulativo($invioEmailCumulativo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($invioEmailCumulativo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $invioEmailCumulativo)) {
                $invioEmailCumulativo = str_replace('*', '%', $invioEmailCumulativo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INVIO_EMAIL_CUMULATIVO, $invioEmailCumulativo, $comparison);
    }

    /**
     * Filter the query on the invio_email_parametrico column
     *
     * Example usage:
     * <code>
     * $query->filterByInvioEmailParametrico('fooValue');   // WHERE invio_email_parametrico = 'fooValue'
     * $query->filterByInvioEmailParametrico('%fooValue%'); // WHERE invio_email_parametrico LIKE '%fooValue%'
     * </code>
     *
     * @param     string $invioEmailParametrico The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByInvioEmailParametrico($invioEmailParametrico = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($invioEmailParametrico)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $invioEmailParametrico)) {
                $invioEmailParametrico = str_replace('*', '%', $invioEmailParametrico);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INVIO_EMAIL_PARAMETRICO, $invioEmailParametrico, $comparison);
    }

    /**
     * Filter the query on the invio_email_temporale column
     *
     * Example usage:
     * <code>
     * $query->filterByInvioEmailTemporale('fooValue');   // WHERE invio_email_temporale = 'fooValue'
     * $query->filterByInvioEmailTemporale('%fooValue%'); // WHERE invio_email_temporale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $invioEmailTemporale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByInvioEmailTemporale($invioEmailTemporale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($invioEmailTemporale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $invioEmailTemporale)) {
                $invioEmailTemporale = str_replace('*', '%', $invioEmailTemporale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INVIO_EMAIL_TEMPORALE, $invioEmailTemporale, $comparison);
    }

    /**
     * Filter the query on the tipo_sms column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoSms('fooValue');   // WHERE tipo_sms = 'fooValue'
     * $query->filterByTipoSms('%fooValue%'); // WHERE tipo_sms LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoSms The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoSms($tipoSms = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoSms)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoSms)) {
                $tipoSms = str_replace('*', '%', $tipoSms);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_SMS, $tipoSms, $comparison);
    }

    /**
     * Filter the query on the tipo_sms_cumulativo column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoSmsCumulativo('fooValue');   // WHERE tipo_sms_cumulativo = 'fooValue'
     * $query->filterByTipoSmsCumulativo('%fooValue%'); // WHERE tipo_sms_cumulativo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoSmsCumulativo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoSmsCumulativo($tipoSmsCumulativo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoSmsCumulativo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoSmsCumulativo)) {
                $tipoSmsCumulativo = str_replace('*', '%', $tipoSmsCumulativo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_SMS_CUMULATIVO, $tipoSmsCumulativo, $comparison);
    }

    /**
     * Filter the query on the tipo_sms_parametrico column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoSmsParametrico('fooValue');   // WHERE tipo_sms_parametrico = 'fooValue'
     * $query->filterByTipoSmsParametrico('%fooValue%'); // WHERE tipo_sms_parametrico LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoSmsParametrico The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoSmsParametrico($tipoSmsParametrico = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoSmsParametrico)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoSmsParametrico)) {
                $tipoSmsParametrico = str_replace('*', '%', $tipoSmsParametrico);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_SMS_PARAMETRICO, $tipoSmsParametrico, $comparison);
    }

    /**
     * Filter the query on the tipo_sms_temporale column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoSmsTemporale('fooValue');   // WHERE tipo_sms_temporale = 'fooValue'
     * $query->filterByTipoSmsTemporale('%fooValue%'); // WHERE tipo_sms_temporale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoSmsTemporale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoSmsTemporale($tipoSmsTemporale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoSmsTemporale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoSmsTemporale)) {
                $tipoSmsTemporale = str_replace('*', '%', $tipoSmsTemporale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_SMS_TEMPORALE, $tipoSmsTemporale, $comparison);
    }

    /**
     * Filter the query on the aut_entrata_ritardo column
     *
     * Example usage:
     * <code>
     * $query->filterByAutEntrataRitardo('fooValue');   // WHERE aut_entrata_ritardo = 'fooValue'
     * $query->filterByAutEntrataRitardo('%fooValue%'); // WHERE aut_entrata_ritardo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $autEntrataRitardo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAutEntrataRitardo($autEntrataRitardo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($autEntrataRitardo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $autEntrataRitardo)) {
                $autEntrataRitardo = str_replace('*', '%', $autEntrataRitardo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AUT_ENTRATA_RITARDO, $autEntrataRitardo, $comparison);
    }

    /**
     * Filter the query on the aut_uscita_anticipo column
     *
     * Example usage:
     * <code>
     * $query->filterByAutUscitaAnticipo('fooValue');   // WHERE aut_uscita_anticipo = 'fooValue'
     * $query->filterByAutUscitaAnticipo('%fooValue%'); // WHERE aut_uscita_anticipo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $autUscitaAnticipo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAutUscitaAnticipo($autUscitaAnticipo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($autUscitaAnticipo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $autUscitaAnticipo)) {
                $autUscitaAnticipo = str_replace('*', '%', $autUscitaAnticipo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AUT_USCITA_ANTICIPO, $autUscitaAnticipo, $comparison);
    }

    /**
     * Filter the query on the aut_pomeriggio column
     *
     * Example usage:
     * <code>
     * $query->filterByAutPomeriggio('fooValue');   // WHERE aut_pomeriggio = 'fooValue'
     * $query->filterByAutPomeriggio('%fooValue%'); // WHERE aut_pomeriggio LIKE '%fooValue%'
     * </code>
     *
     * @param     string $autPomeriggio The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAutPomeriggio($autPomeriggio = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($autPomeriggio)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $autPomeriggio)) {
                $autPomeriggio = str_replace('*', '%', $autPomeriggio);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AUT_POMERIGGIO, $autPomeriggio, $comparison);
    }

    /**
     * Filter the query on the acconsente column
     *
     * Example usage:
     * <code>
     * $query->filterByAcconsente('fooValue');   // WHERE acconsente = 'fooValue'
     * $query->filterByAcconsente('%fooValue%'); // WHERE acconsente LIKE '%fooValue%'
     * </code>
     *
     * @param     string $acconsente The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAcconsente($acconsente = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($acconsente)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $acconsente)) {
                $acconsente = str_replace('*', '%', $acconsente);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ACCONSENTE, $acconsente, $comparison);
    }

    /**
     * Filter the query on the ritirato column
     *
     * Example usage:
     * <code>
     * $query->filterByRitirato('fooValue');   // WHERE ritirato = 'fooValue'
     * $query->filterByRitirato('%fooValue%'); // WHERE ritirato LIKE '%fooValue%'
     * </code>
     *
     * @param     string $ritirato The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByRitirato($ritirato = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($ritirato)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $ritirato)) {
                $ritirato = str_replace('*', '%', $ritirato);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::RITIRATO, $ritirato, $comparison);
    }

    /**
     * Filter the query on the data_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByDataNascita(1234); // WHERE data_nascita = 1234
     * $query->filterByDataNascita(array(12, 34)); // WHERE data_nascita IN (12, 34)
     * $query->filterByDataNascita(array('min' => 12)); // WHERE data_nascita >= 12
     * $query->filterByDataNascita(array('max' => 12)); // WHERE data_nascita <= 12
     * </code>
     *
     * @param     mixed $dataNascita The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataNascita($dataNascita = null, $comparison = null)
    {
        if (is_array($dataNascita)) {
            $useMinMax = false;
            if (isset($dataNascita['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_NASCITA, $dataNascita['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataNascita['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_NASCITA, $dataNascita['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_NASCITA, $dataNascita, $comparison);
    }

    /**
     * Filter the query on the codice_studente column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceStudente('fooValue');   // WHERE codice_studente = 'fooValue'
     * $query->filterByCodiceStudente('%fooValue%'); // WHERE codice_studente LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceStudente The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceStudente($codiceStudente = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceStudente)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceStudente)) {
                $codiceStudente = str_replace('*', '%', $codiceStudente);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_STUDENTE, $codiceStudente, $comparison);
    }

    /**
     * Filter the query on the password_studente column
     *
     * Example usage:
     * <code>
     * $query->filterByPasswordStudente('fooValue');   // WHERE password_studente = 'fooValue'
     * $query->filterByPasswordStudente('%fooValue%'); // WHERE password_studente LIKE '%fooValue%'
     * </code>
     *
     * @param     string $passwordStudente The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByPasswordStudente($passwordStudente = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($passwordStudente)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $passwordStudente)) {
                $passwordStudente = str_replace('*', '%', $passwordStudente);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::PASSWORD_STUDENTE, $passwordStudente, $comparison);
    }

    /**
     * Filter the query on the codice_giustificazioni_studente column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceGiustificazioniStudente(1234); // WHERE codice_giustificazioni_studente = 1234
     * $query->filterByCodiceGiustificazioniStudente(array(12, 34)); // WHERE codice_giustificazioni_studente IN (12, 34)
     * $query->filterByCodiceGiustificazioniStudente(array('min' => 12)); // WHERE codice_giustificazioni_studente >= 12
     * $query->filterByCodiceGiustificazioniStudente(array('max' => 12)); // WHERE codice_giustificazioni_studente <= 12
     * </code>
     *
     * @param     mixed $codiceGiustificazioniStudente The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceGiustificazioniStudente($codiceGiustificazioniStudente = null, $comparison = null)
    {
        if (is_array($codiceGiustificazioniStudente)) {
            $useMinMax = false;
            if (isset($codiceGiustificazioniStudente['min'])) {
                $this->addUsingAlias(StudentiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE, $codiceGiustificazioniStudente['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($codiceGiustificazioniStudente['max'])) {
                $this->addUsingAlias(StudentiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE, $codiceGiustificazioniStudente['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE, $codiceGiustificazioniStudente, $comparison);
    }

    /**
     * Filter the query on the esonero_religione column
     *
     * Example usage:
     * <code>
     * $query->filterByEsoneroReligione('fooValue');   // WHERE esonero_religione = 'fooValue'
     * $query->filterByEsoneroReligione('%fooValue%'); // WHERE esonero_religione LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esoneroReligione The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsoneroReligione($esoneroReligione = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esoneroReligione)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esoneroReligione)) {
                $esoneroReligione = str_replace('*', '%', $esoneroReligione);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESONERO_RELIGIONE, $esoneroReligione, $comparison);
    }

    /**
     * Filter the query on the materia_sostitutiva_religione column
     *
     * Example usage:
     * <code>
     * $query->filterByMateriaSostitutivaReligione(1234); // WHERE materia_sostitutiva_religione = 1234
     * $query->filterByMateriaSostitutivaReligione(array(12, 34)); // WHERE materia_sostitutiva_religione IN (12, 34)
     * $query->filterByMateriaSostitutivaReligione(array('min' => 12)); // WHERE materia_sostitutiva_religione >= 12
     * $query->filterByMateriaSostitutivaReligione(array('max' => 12)); // WHERE materia_sostitutiva_religione <= 12
     * </code>
     *
     * @param     mixed $materiaSostitutivaReligione The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMateriaSostitutivaReligione($materiaSostitutivaReligione = null, $comparison = null)
    {
        if (is_array($materiaSostitutivaReligione)) {
            $useMinMax = false;
            if (isset($materiaSostitutivaReligione['min'])) {
                $this->addUsingAlias(StudentiPeer::MATERIA_SOSTITUTIVA_RELIGIONE, $materiaSostitutivaReligione['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($materiaSostitutivaReligione['max'])) {
                $this->addUsingAlias(StudentiPeer::MATERIA_SOSTITUTIVA_RELIGIONE, $materiaSostitutivaReligione['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MATERIA_SOSTITUTIVA_RELIGIONE, $materiaSostitutivaReligione, $comparison);
    }

    /**
     * Filter the query on the esonero_ed_fisica column
     *
     * Example usage:
     * <code>
     * $query->filterByEsoneroEdFisica('fooValue');   // WHERE esonero_ed_fisica = 'fooValue'
     * $query->filterByEsoneroEdFisica('%fooValue%'); // WHERE esonero_ed_fisica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esoneroEdFisica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsoneroEdFisica($esoneroEdFisica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esoneroEdFisica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esoneroEdFisica)) {
                $esoneroEdFisica = str_replace('*', '%', $esoneroEdFisica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESONERO_ED_FISICA, $esoneroEdFisica, $comparison);
    }

    /**
     * Filter the query on the materia_sostitutiva_edfisica column
     *
     * Example usage:
     * <code>
     * $query->filterByMateriaSostitutivaEdfisica(1234); // WHERE materia_sostitutiva_edfisica = 1234
     * $query->filterByMateriaSostitutivaEdfisica(array(12, 34)); // WHERE materia_sostitutiva_edfisica IN (12, 34)
     * $query->filterByMateriaSostitutivaEdfisica(array('min' => 12)); // WHERE materia_sostitutiva_edfisica >= 12
     * $query->filterByMateriaSostitutivaEdfisica(array('max' => 12)); // WHERE materia_sostitutiva_edfisica <= 12
     * </code>
     *
     * @param     mixed $materiaSostitutivaEdfisica The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMateriaSostitutivaEdfisica($materiaSostitutivaEdfisica = null, $comparison = null)
    {
        if (is_array($materiaSostitutivaEdfisica)) {
            $useMinMax = false;
            if (isset($materiaSostitutivaEdfisica['min'])) {
                $this->addUsingAlias(StudentiPeer::MATERIA_SOSTITUTIVA_EDFISICA, $materiaSostitutivaEdfisica['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($materiaSostitutivaEdfisica['max'])) {
                $this->addUsingAlias(StudentiPeer::MATERIA_SOSTITUTIVA_EDFISICA, $materiaSostitutivaEdfisica['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MATERIA_SOSTITUTIVA_EDFISICA, $materiaSostitutivaEdfisica, $comparison);
    }

    /**
     * Filter the query on the crediti_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiTerza(1234); // WHERE crediti_terza = 1234
     * $query->filterByCreditiTerza(array(12, 34)); // WHERE crediti_terza IN (12, 34)
     * $query->filterByCreditiTerza(array('min' => 12)); // WHERE crediti_terza >= 12
     * $query->filterByCreditiTerza(array('max' => 12)); // WHERE crediti_terza <= 12
     * </code>
     *
     * @param     mixed $creditiTerza The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiTerza($creditiTerza = null, $comparison = null)
    {
        if (is_array($creditiTerza)) {
            $useMinMax = false;
            if (isset($creditiTerza['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_TERZA, $creditiTerza['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiTerza['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_TERZA, $creditiTerza['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_TERZA, $creditiTerza, $comparison);
    }

    /**
     * Filter the query on the media_voti_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByMediaVotiTerza('fooValue');   // WHERE media_voti_terza = 'fooValue'
     * $query->filterByMediaVotiTerza('%fooValue%'); // WHERE media_voti_terza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $mediaVotiTerza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMediaVotiTerza($mediaVotiTerza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($mediaVotiTerza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $mediaVotiTerza)) {
                $mediaVotiTerza = str_replace('*', '%', $mediaVotiTerza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MEDIA_VOTI_TERZA, $mediaVotiTerza, $comparison);
    }

    /**
     * Filter the query on the debiti_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByDebitiTerza('fooValue');   // WHERE debiti_terza = 'fooValue'
     * $query->filterByDebitiTerza('%fooValue%'); // WHERE debiti_terza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $debitiTerza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDebitiTerza($debitiTerza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($debitiTerza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $debitiTerza)) {
                $debitiTerza = str_replace('*', '%', $debitiTerza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DEBITI_TERZA, $debitiTerza, $comparison);
    }

    /**
     * Filter the query on the crediti_sospesi_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiSospesiTerza(1234); // WHERE crediti_sospesi_terza = 1234
     * $query->filterByCreditiSospesiTerza(array(12, 34)); // WHERE crediti_sospesi_terza IN (12, 34)
     * $query->filterByCreditiSospesiTerza(array('min' => 12)); // WHERE crediti_sospesi_terza >= 12
     * $query->filterByCreditiSospesiTerza(array('max' => 12)); // WHERE crediti_sospesi_terza <= 12
     * </code>
     *
     * @param     mixed $creditiSospesiTerza The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiSospesiTerza($creditiSospesiTerza = null, $comparison = null)
    {
        if (is_array($creditiSospesiTerza)) {
            $useMinMax = false;
            if (isset($creditiSospesiTerza['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_SOSPESI_TERZA, $creditiSospesiTerza['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiSospesiTerza['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_SOSPESI_TERZA, $creditiSospesiTerza['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_SOSPESI_TERZA, $creditiSospesiTerza, $comparison);
    }

    /**
     * Filter the query on the crediti_reintegrati_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiReintegratiTerza(1234); // WHERE crediti_reintegrati_terza = 1234
     * $query->filterByCreditiReintegratiTerza(array(12, 34)); // WHERE crediti_reintegrati_terza IN (12, 34)
     * $query->filterByCreditiReintegratiTerza(array('min' => 12)); // WHERE crediti_reintegrati_terza >= 12
     * $query->filterByCreditiReintegratiTerza(array('max' => 12)); // WHERE crediti_reintegrati_terza <= 12
     * </code>
     *
     * @param     mixed $creditiReintegratiTerza The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiReintegratiTerza($creditiReintegratiTerza = null, $comparison = null)
    {
        if (is_array($creditiReintegratiTerza)) {
            $useMinMax = false;
            if (isset($creditiReintegratiTerza['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_REINTEGRATI_TERZA, $creditiReintegratiTerza['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiReintegratiTerza['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_REINTEGRATI_TERZA, $creditiReintegratiTerza['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_REINTEGRATI_TERZA, $creditiReintegratiTerza, $comparison);
    }

    /**
     * Filter the query on the crediti_quarta column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiQuarta(1234); // WHERE crediti_quarta = 1234
     * $query->filterByCreditiQuarta(array(12, 34)); // WHERE crediti_quarta IN (12, 34)
     * $query->filterByCreditiQuarta(array('min' => 12)); // WHERE crediti_quarta >= 12
     * $query->filterByCreditiQuarta(array('max' => 12)); // WHERE crediti_quarta <= 12
     * </code>
     *
     * @param     mixed $creditiQuarta The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiQuarta($creditiQuarta = null, $comparison = null)
    {
        if (is_array($creditiQuarta)) {
            $useMinMax = false;
            if (isset($creditiQuarta['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_QUARTA, $creditiQuarta['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiQuarta['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_QUARTA, $creditiQuarta['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_QUARTA, $creditiQuarta, $comparison);
    }

    /**
     * Filter the query on the media_voti_quarta column
     *
     * Example usage:
     * <code>
     * $query->filterByMediaVotiQuarta('fooValue');   // WHERE media_voti_quarta = 'fooValue'
     * $query->filterByMediaVotiQuarta('%fooValue%'); // WHERE media_voti_quarta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $mediaVotiQuarta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMediaVotiQuarta($mediaVotiQuarta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($mediaVotiQuarta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $mediaVotiQuarta)) {
                $mediaVotiQuarta = str_replace('*', '%', $mediaVotiQuarta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MEDIA_VOTI_QUARTA, $mediaVotiQuarta, $comparison);
    }

    /**
     * Filter the query on the debiti_quarta column
     *
     * Example usage:
     * <code>
     * $query->filterByDebitiQuarta('fooValue');   // WHERE debiti_quarta = 'fooValue'
     * $query->filterByDebitiQuarta('%fooValue%'); // WHERE debiti_quarta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $debitiQuarta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDebitiQuarta($debitiQuarta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($debitiQuarta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $debitiQuarta)) {
                $debitiQuarta = str_replace('*', '%', $debitiQuarta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DEBITI_QUARTA, $debitiQuarta, $comparison);
    }

    /**
     * Filter the query on the crediti_sospesi_quarta column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiSospesiQuarta(1234); // WHERE crediti_sospesi_quarta = 1234
     * $query->filterByCreditiSospesiQuarta(array(12, 34)); // WHERE crediti_sospesi_quarta IN (12, 34)
     * $query->filterByCreditiSospesiQuarta(array('min' => 12)); // WHERE crediti_sospesi_quarta >= 12
     * $query->filterByCreditiSospesiQuarta(array('max' => 12)); // WHERE crediti_sospesi_quarta <= 12
     * </code>
     *
     * @param     mixed $creditiSospesiQuarta The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiSospesiQuarta($creditiSospesiQuarta = null, $comparison = null)
    {
        if (is_array($creditiSospesiQuarta)) {
            $useMinMax = false;
            if (isset($creditiSospesiQuarta['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_SOSPESI_QUARTA, $creditiSospesiQuarta['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiSospesiQuarta['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_SOSPESI_QUARTA, $creditiSospesiQuarta['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_SOSPESI_QUARTA, $creditiSospesiQuarta, $comparison);
    }

    /**
     * Filter the query on the crediti_reintegrati_quarta column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiReintegratiQuarta(1234); // WHERE crediti_reintegrati_quarta = 1234
     * $query->filterByCreditiReintegratiQuarta(array(12, 34)); // WHERE crediti_reintegrati_quarta IN (12, 34)
     * $query->filterByCreditiReintegratiQuarta(array('min' => 12)); // WHERE crediti_reintegrati_quarta >= 12
     * $query->filterByCreditiReintegratiQuarta(array('max' => 12)); // WHERE crediti_reintegrati_quarta <= 12
     * </code>
     *
     * @param     mixed $creditiReintegratiQuarta The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiReintegratiQuarta($creditiReintegratiQuarta = null, $comparison = null)
    {
        if (is_array($creditiReintegratiQuarta)) {
            $useMinMax = false;
            if (isset($creditiReintegratiQuarta['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_REINTEGRATI_QUARTA, $creditiReintegratiQuarta['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiReintegratiQuarta['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_REINTEGRATI_QUARTA, $creditiReintegratiQuarta['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_REINTEGRATI_QUARTA, $creditiReintegratiQuarta, $comparison);
    }

    /**
     * Filter the query on the crediti_quinta column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiQuinta(1234); // WHERE crediti_quinta = 1234
     * $query->filterByCreditiQuinta(array(12, 34)); // WHERE crediti_quinta IN (12, 34)
     * $query->filterByCreditiQuinta(array('min' => 12)); // WHERE crediti_quinta >= 12
     * $query->filterByCreditiQuinta(array('max' => 12)); // WHERE crediti_quinta <= 12
     * </code>
     *
     * @param     mixed $creditiQuinta The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiQuinta($creditiQuinta = null, $comparison = null)
    {
        if (is_array($creditiQuinta)) {
            $useMinMax = false;
            if (isset($creditiQuinta['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_QUINTA, $creditiQuinta['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiQuinta['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_QUINTA, $creditiQuinta['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_QUINTA, $creditiQuinta, $comparison);
    }

    /**
     * Filter the query on the media_voti_quinta column
     *
     * Example usage:
     * <code>
     * $query->filterByMediaVotiQuinta('fooValue');   // WHERE media_voti_quinta = 'fooValue'
     * $query->filterByMediaVotiQuinta('%fooValue%'); // WHERE media_voti_quinta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $mediaVotiQuinta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMediaVotiQuinta($mediaVotiQuinta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($mediaVotiQuinta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $mediaVotiQuinta)) {
                $mediaVotiQuinta = str_replace('*', '%', $mediaVotiQuinta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MEDIA_VOTI_QUINTA, $mediaVotiQuinta, $comparison);
    }

    /**
     * Filter the query on the crediti_finali_agg column
     *
     * Example usage:
     * <code>
     * $query->filterByCreditiFinaliAgg(1234); // WHERE crediti_finali_agg = 1234
     * $query->filterByCreditiFinaliAgg(array(12, 34)); // WHERE crediti_finali_agg IN (12, 34)
     * $query->filterByCreditiFinaliAgg(array('min' => 12)); // WHERE crediti_finali_agg >= 12
     * $query->filterByCreditiFinaliAgg(array('max' => 12)); // WHERE crediti_finali_agg <= 12
     * </code>
     *
     * @param     mixed $creditiFinaliAgg The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCreditiFinaliAgg($creditiFinaliAgg = null, $comparison = null)
    {
        if (is_array($creditiFinaliAgg)) {
            $useMinMax = false;
            if (isset($creditiFinaliAgg['min'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_FINALI_AGG, $creditiFinaliAgg['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($creditiFinaliAgg['max'])) {
                $this->addUsingAlias(StudentiPeer::CREDITI_FINALI_AGG, $creditiFinaliAgg['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CREDITI_FINALI_AGG, $creditiFinaliAgg, $comparison);
    }

    /**
     * Filter the query on the matricola column
     *
     * Example usage:
     * <code>
     * $query->filterByMatricola('fooValue');   // WHERE matricola = 'fooValue'
     * $query->filterByMatricola('%fooValue%'); // WHERE matricola LIKE '%fooValue%'
     * </code>
     *
     * @param     string $matricola The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMatricola($matricola = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($matricola)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $matricola)) {
                $matricola = str_replace('*', '%', $matricola);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MATRICOLA, $matricola, $comparison);
    }

    /**
     * Filter the query on the luogo_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByLuogoNascita('fooValue');   // WHERE luogo_nascita = 'fooValue'
     * $query->filterByLuogoNascita('%fooValue%'); // WHERE luogo_nascita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $luogoNascita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByLuogoNascita($luogoNascita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($luogoNascita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $luogoNascita)) {
                $luogoNascita = str_replace('*', '%', $luogoNascita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::LUOGO_NASCITA, $luogoNascita, $comparison);
    }

    /**
     * Filter the query on the provincia_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByProvinciaNascita('fooValue');   // WHERE provincia_nascita = 'fooValue'
     * $query->filterByProvinciaNascita('%fooValue%'); // WHERE provincia_nascita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $provinciaNascita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByProvinciaNascita($provinciaNascita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($provinciaNascita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $provinciaNascita)) {
                $provinciaNascita = str_replace('*', '%', $provinciaNascita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::PROVINCIA_NASCITA, $provinciaNascita, $comparison);
    }

    /**
     * Filter the query on the motivi_crediti_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByMotiviCreditiTerza('fooValue');   // WHERE motivi_crediti_terza = 'fooValue'
     * $query->filterByMotiviCreditiTerza('%fooValue%'); // WHERE motivi_crediti_terza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $motiviCreditiTerza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMotiviCreditiTerza($motiviCreditiTerza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($motiviCreditiTerza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $motiviCreditiTerza)) {
                $motiviCreditiTerza = str_replace('*', '%', $motiviCreditiTerza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MOTIVI_CREDITI_TERZA, $motiviCreditiTerza, $comparison);
    }

    /**
     * Filter the query on the motivi_crediti_quarta column
     *
     * Example usage:
     * <code>
     * $query->filterByMotiviCreditiQuarta('fooValue');   // WHERE motivi_crediti_quarta = 'fooValue'
     * $query->filterByMotiviCreditiQuarta('%fooValue%'); // WHERE motivi_crediti_quarta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $motiviCreditiQuarta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMotiviCreditiQuarta($motiviCreditiQuarta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($motiviCreditiQuarta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $motiviCreditiQuarta)) {
                $motiviCreditiQuarta = str_replace('*', '%', $motiviCreditiQuarta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MOTIVI_CREDITI_QUARTA, $motiviCreditiQuarta, $comparison);
    }

    /**
     * Filter the query on the motivi_crediti_quinta column
     *
     * Example usage:
     * <code>
     * $query->filterByMotiviCreditiQuinta('fooValue');   // WHERE motivi_crediti_quinta = 'fooValue'
     * $query->filterByMotiviCreditiQuinta('%fooValue%'); // WHERE motivi_crediti_quinta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $motiviCreditiQuinta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMotiviCreditiQuinta($motiviCreditiQuinta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($motiviCreditiQuinta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $motiviCreditiQuinta)) {
                $motiviCreditiQuinta = str_replace('*', '%', $motiviCreditiQuinta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MOTIVI_CREDITI_QUINTA, $motiviCreditiQuinta, $comparison);
    }

    /**
     * Filter the query on the motivi_crediti_agg column
     *
     * Example usage:
     * <code>
     * $query->filterByMotiviCreditiAgg('fooValue');   // WHERE motivi_crediti_agg = 'fooValue'
     * $query->filterByMotiviCreditiAgg('%fooValue%'); // WHERE motivi_crediti_agg LIKE '%fooValue%'
     * </code>
     *
     * @param     string $motiviCreditiAgg The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMotiviCreditiAgg($motiviCreditiAgg = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($motiviCreditiAgg)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $motiviCreditiAgg)) {
                $motiviCreditiAgg = str_replace('*', '%', $motiviCreditiAgg);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MOTIVI_CREDITI_AGG, $motiviCreditiAgg, $comparison);
    }

    /**
     * Filter the query on the codice_comune_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceComuneNascita('fooValue');   // WHERE codice_comune_nascita = 'fooValue'
     * $query->filterByCodiceComuneNascita('%fooValue%'); // WHERE codice_comune_nascita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceComuneNascita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceComuneNascita($codiceComuneNascita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceComuneNascita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceComuneNascita)) {
                $codiceComuneNascita = str_replace('*', '%', $codiceComuneNascita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_COMUNE_NASCITA, $codiceComuneNascita, $comparison);
    }

    /**
     * Filter the query on the stato_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByStatoNascita('fooValue');   // WHERE stato_nascita = 'fooValue'
     * $query->filterByStatoNascita('%fooValue%'); // WHERE stato_nascita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $statoNascita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStatoNascita($statoNascita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($statoNascita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $statoNascita)) {
                $statoNascita = str_replace('*', '%', $statoNascita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STATO_NASCITA, $statoNascita, $comparison);
    }

    /**
     * Filter the query on the cittadinanza column
     *
     * Example usage:
     * <code>
     * $query->filterByCittadinanza('fooValue');   // WHERE cittadinanza = 'fooValue'
     * $query->filterByCittadinanza('%fooValue%'); // WHERE cittadinanza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cittadinanza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCittadinanza($cittadinanza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cittadinanza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cittadinanza)) {
                $cittadinanza = str_replace('*', '%', $cittadinanza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CITTADINANZA, $cittadinanza, $comparison);
    }

    /**
     * Filter the query on the seconda_cittadinanza column
     *
     * Example usage:
     * <code>
     * $query->filterBySecondaCittadinanza('fooValue');   // WHERE seconda_cittadinanza = 'fooValue'
     * $query->filterBySecondaCittadinanza('%fooValue%'); // WHERE seconda_cittadinanza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $secondaCittadinanza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterBySecondaCittadinanza($secondaCittadinanza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($secondaCittadinanza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $secondaCittadinanza)) {
                $secondaCittadinanza = str_replace('*', '%', $secondaCittadinanza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::SECONDA_CITTADINANZA, $secondaCittadinanza, $comparison);
    }

    /**
     * Filter the query on the codice_comune_residenza column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceComuneResidenza('fooValue');   // WHERE codice_comune_residenza = 'fooValue'
     * $query->filterByCodiceComuneResidenza('%fooValue%'); // WHERE codice_comune_residenza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceComuneResidenza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceComuneResidenza($codiceComuneResidenza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceComuneResidenza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceComuneResidenza)) {
                $codiceComuneResidenza = str_replace('*', '%', $codiceComuneResidenza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_COMUNE_RESIDENZA, $codiceComuneResidenza, $comparison);
    }

    /**
     * Filter the query on the distretto column
     *
     * Example usage:
     * <code>
     * $query->filterByDistretto('fooValue');   // WHERE distretto = 'fooValue'
     * $query->filterByDistretto('%fooValue%'); // WHERE distretto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $distretto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDistretto($distretto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($distretto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $distretto)) {
                $distretto = str_replace('*', '%', $distretto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DISTRETTO, $distretto, $comparison);
    }

    /**
     * Filter the query on the codice_fiscale column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceFiscale('fooValue');   // WHERE codice_fiscale = 'fooValue'
     * $query->filterByCodiceFiscale('%fooValue%'); // WHERE codice_fiscale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceFiscale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceFiscale($codiceFiscale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceFiscale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceFiscale)) {
                $codiceFiscale = str_replace('*', '%', $codiceFiscale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_FISCALE, $codiceFiscale, $comparison);
    }

    /**
     * Filter the query on the medico column
     *
     * Example usage:
     * <code>
     * $query->filterByMedico('fooValue');   // WHERE medico = 'fooValue'
     * $query->filterByMedico('%fooValue%'); // WHERE medico LIKE '%fooValue%'
     * </code>
     *
     * @param     string $medico The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMedico($medico = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($medico)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $medico)) {
                $medico = str_replace('*', '%', $medico);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MEDICO, $medico, $comparison);
    }

    /**
     * Filter the query on the telefono_medico column
     *
     * Example usage:
     * <code>
     * $query->filterByTelefonoMedico('fooValue');   // WHERE telefono_medico = 'fooValue'
     * $query->filterByTelefonoMedico('%fooValue%'); // WHERE telefono_medico LIKE '%fooValue%'
     * </code>
     *
     * @param     string $telefonoMedico The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTelefonoMedico($telefonoMedico = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($telefonoMedico)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $telefonoMedico)) {
                $telefonoMedico = str_replace('*', '%', $telefonoMedico);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TELEFONO_MEDICO, $telefonoMedico, $comparison);
    }

    /**
     * Filter the query on the intolleranze_alim column
     *
     * Example usage:
     * <code>
     * $query->filterByIntolleranzeAlim('fooValue');   // WHERE intolleranze_alim = 'fooValue'
     * $query->filterByIntolleranzeAlim('%fooValue%'); // WHERE intolleranze_alim LIKE '%fooValue%'
     * </code>
     *
     * @param     string $intolleranzeAlim The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIntolleranzeAlim($intolleranzeAlim = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($intolleranzeAlim)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $intolleranzeAlim)) {
                $intolleranzeAlim = str_replace('*', '%', $intolleranzeAlim);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INTOLLERANZE_ALIM, $intolleranzeAlim, $comparison);
    }

    /**
     * Filter the query on the gruppo_sanguigno column
     *
     * Example usage:
     * <code>
     * $query->filterByGruppoSanguigno('fooValue');   // WHERE gruppo_sanguigno = 'fooValue'
     * $query->filterByGruppoSanguigno('%fooValue%'); // WHERE gruppo_sanguigno LIKE '%fooValue%'
     * </code>
     *
     * @param     string $gruppoSanguigno The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGruppoSanguigno($gruppoSanguigno = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($gruppoSanguigno)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $gruppoSanguigno)) {
                $gruppoSanguigno = str_replace('*', '%', $gruppoSanguigno);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GRUPPO_SANGUIGNO, $gruppoSanguigno, $comparison);
    }

    /**
     * Filter the query on the gruppo_rh column
     *
     * Example usage:
     * <code>
     * $query->filterByGruppoRh('fooValue');   // WHERE gruppo_rh = 'fooValue'
     * $query->filterByGruppoRh('%fooValue%'); // WHERE gruppo_rh LIKE '%fooValue%'
     * </code>
     *
     * @param     string $gruppoRh The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGruppoRh($gruppoRh = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($gruppoRh)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $gruppoRh)) {
                $gruppoRh = str_replace('*', '%', $gruppoRh);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GRUPPO_RH, $gruppoRh, $comparison);
    }

    /**
     * Filter the query on the codice_asl column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceAsl('fooValue');   // WHERE codice_asl = 'fooValue'
     * $query->filterByCodiceAsl('%fooValue%'); // WHERE codice_asl LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceAsl The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceAsl($codiceAsl = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceAsl)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceAsl)) {
                $codiceAsl = str_replace('*', '%', $codiceAsl);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_ASL, $codiceAsl, $comparison);
    }

    /**
     * Filter the query on the annotazioni column
     *
     * Example usage:
     * <code>
     * $query->filterByAnnotazioni('fooValue');   // WHERE annotazioni = 'fooValue'
     * $query->filterByAnnotazioni('%fooValue%'); // WHERE annotazioni LIKE '%fooValue%'
     * </code>
     *
     * @param     string $annotazioni The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAnnotazioni($annotazioni = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($annotazioni)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $annotazioni)) {
                $annotazioni = str_replace('*', '%', $annotazioni);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ANNOTAZIONI, $annotazioni, $comparison);
    }

    /**
     * Filter the query on the stato_civile column
     *
     * Example usage:
     * <code>
     * $query->filterByStatoCivile(1234); // WHERE stato_civile = 1234
     * $query->filterByStatoCivile(array(12, 34)); // WHERE stato_civile IN (12, 34)
     * $query->filterByStatoCivile(array('min' => 12)); // WHERE stato_civile >= 12
     * $query->filterByStatoCivile(array('max' => 12)); // WHERE stato_civile <= 12
     * </code>
     *
     * @param     mixed $statoCivile The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStatoCivile($statoCivile = null, $comparison = null)
    {
        if (is_array($statoCivile)) {
            $useMinMax = false;
            if (isset($statoCivile['min'])) {
                $this->addUsingAlias(StudentiPeer::STATO_CIVILE, $statoCivile['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($statoCivile['max'])) {
                $this->addUsingAlias(StudentiPeer::STATO_CIVILE, $statoCivile['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STATO_CIVILE, $statoCivile, $comparison);
    }

    /**
     * Filter the query on the voto_primo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoPrimoScritto(1234); // WHERE voto_primo_scritto = 1234
     * $query->filterByVotoPrimoScritto(array(12, 34)); // WHERE voto_primo_scritto IN (12, 34)
     * $query->filterByVotoPrimoScritto(array('min' => 12)); // WHERE voto_primo_scritto >= 12
     * $query->filterByVotoPrimoScritto(array('max' => 12)); // WHERE voto_primo_scritto <= 12
     * </code>
     *
     * @param     mixed $votoPrimoScritto The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoPrimoScritto($votoPrimoScritto = null, $comparison = null)
    {
        if (is_array($votoPrimoScritto)) {
            $useMinMax = false;
            if (isset($votoPrimoScritto['min'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_PRIMO_SCRITTO, $votoPrimoScritto['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($votoPrimoScritto['max'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_PRIMO_SCRITTO, $votoPrimoScritto['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_PRIMO_SCRITTO, $votoPrimoScritto, $comparison);
    }

    /**
     * Filter the query on the voto_secondo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoSecondoScritto(1234); // WHERE voto_secondo_scritto = 1234
     * $query->filterByVotoSecondoScritto(array(12, 34)); // WHERE voto_secondo_scritto IN (12, 34)
     * $query->filterByVotoSecondoScritto(array('min' => 12)); // WHERE voto_secondo_scritto >= 12
     * $query->filterByVotoSecondoScritto(array('max' => 12)); // WHERE voto_secondo_scritto <= 12
     * </code>
     *
     * @param     mixed $votoSecondoScritto The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoSecondoScritto($votoSecondoScritto = null, $comparison = null)
    {
        if (is_array($votoSecondoScritto)) {
            $useMinMax = false;
            if (isset($votoSecondoScritto['min'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_SECONDO_SCRITTO, $votoSecondoScritto['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($votoSecondoScritto['max'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_SECONDO_SCRITTO, $votoSecondoScritto['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_SECONDO_SCRITTO, $votoSecondoScritto, $comparison);
    }

    /**
     * Filter the query on the voto_terzo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoTerzoScritto(1234); // WHERE voto_terzo_scritto = 1234
     * $query->filterByVotoTerzoScritto(array(12, 34)); // WHERE voto_terzo_scritto IN (12, 34)
     * $query->filterByVotoTerzoScritto(array('min' => 12)); // WHERE voto_terzo_scritto >= 12
     * $query->filterByVotoTerzoScritto(array('max' => 12)); // WHERE voto_terzo_scritto <= 12
     * </code>
     *
     * @param     mixed $votoTerzoScritto The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoTerzoScritto($votoTerzoScritto = null, $comparison = null)
    {
        if (is_array($votoTerzoScritto)) {
            $useMinMax = false;
            if (isset($votoTerzoScritto['min'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_TERZO_SCRITTO, $votoTerzoScritto['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($votoTerzoScritto['max'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_TERZO_SCRITTO, $votoTerzoScritto['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_TERZO_SCRITTO, $votoTerzoScritto, $comparison);
    }

    /**
     * Filter the query on the voto_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoOrale(1234); // WHERE voto_orale = 1234
     * $query->filterByVotoOrale(array(12, 34)); // WHERE voto_orale IN (12, 34)
     * $query->filterByVotoOrale(array('min' => 12)); // WHERE voto_orale >= 12
     * $query->filterByVotoOrale(array('max' => 12)); // WHERE voto_orale <= 12
     * </code>
     *
     * @param     mixed $votoOrale The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoOrale($votoOrale = null, $comparison = null)
    {
        if (is_array($votoOrale)) {
            $useMinMax = false;
            if (isset($votoOrale['min'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_ORALE, $votoOrale['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($votoOrale['max'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_ORALE, $votoOrale['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ORALE, $votoOrale, $comparison);
    }

    /**
     * Filter the query on the voto_bonus column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoBonus(1234); // WHERE voto_bonus = 1234
     * $query->filterByVotoBonus(array(12, 34)); // WHERE voto_bonus IN (12, 34)
     * $query->filterByVotoBonus(array('min' => 12)); // WHERE voto_bonus >= 12
     * $query->filterByVotoBonus(array('max' => 12)); // WHERE voto_bonus <= 12
     * </code>
     *
     * @param     mixed $votoBonus The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoBonus($votoBonus = null, $comparison = null)
    {
        if (is_array($votoBonus)) {
            $useMinMax = false;
            if (isset($votoBonus['min'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_BONUS, $votoBonus['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($votoBonus['max'])) {
                $this->addUsingAlias(StudentiPeer::VOTO_BONUS, $votoBonus['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_BONUS, $votoBonus, $comparison);
    }

    /**
     * Filter the query on the materia_secondo_scr column
     *
     * Example usage:
     * <code>
     * $query->filterByMateriaSecondoScr('fooValue');   // WHERE materia_secondo_scr = 'fooValue'
     * $query->filterByMateriaSecondoScr('%fooValue%'); // WHERE materia_secondo_scr LIKE '%fooValue%'
     * </code>
     *
     * @param     string $materiaSecondoScr The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByMateriaSecondoScr($materiaSecondoScr = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($materiaSecondoScr)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $materiaSecondoScr)) {
                $materiaSecondoScr = str_replace('*', '%', $materiaSecondoScr);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::MATERIA_SECONDO_SCR, $materiaSecondoScr, $comparison);
    }

    /**
     * Filter the query on the ulteriori_specif_diploma column
     *
     * Example usage:
     * <code>
     * $query->filterByUlterioriSpecifDiploma('fooValue');   // WHERE ulteriori_specif_diploma = 'fooValue'
     * $query->filterByUlterioriSpecifDiploma('%fooValue%'); // WHERE ulteriori_specif_diploma LIKE '%fooValue%'
     * </code>
     *
     * @param     string $ulterioriSpecifDiploma The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByUlterioriSpecifDiploma($ulterioriSpecifDiploma = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($ulterioriSpecifDiploma)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $ulterioriSpecifDiploma)) {
                $ulterioriSpecifDiploma = str_replace('*', '%', $ulterioriSpecifDiploma);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ULTERIORI_SPECIF_DIPLOMA, $ulterioriSpecifDiploma, $comparison);
    }

    /**
     * Filter the query on the numero_diploma column
     *
     * Example usage:
     * <code>
     * $query->filterByNumeroDiploma(1234); // WHERE numero_diploma = 1234
     * $query->filterByNumeroDiploma(array(12, 34)); // WHERE numero_diploma IN (12, 34)
     * $query->filterByNumeroDiploma(array('min' => 12)); // WHERE numero_diploma >= 12
     * $query->filterByNumeroDiploma(array('max' => 12)); // WHERE numero_diploma <= 12
     * </code>
     *
     * @param     mixed $numeroDiploma The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByNumeroDiploma($numeroDiploma = null, $comparison = null)
    {
        if (is_array($numeroDiploma)) {
            $useMinMax = false;
            if (isset($numeroDiploma['min'])) {
                $this->addUsingAlias(StudentiPeer::NUMERO_DIPLOMA, $numeroDiploma['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($numeroDiploma['max'])) {
                $this->addUsingAlias(StudentiPeer::NUMERO_DIPLOMA, $numeroDiploma['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::NUMERO_DIPLOMA, $numeroDiploma, $comparison);
    }

    /**
     * Filter the query on the chi_inserisce column
     *
     * Example usage:
     * <code>
     * $query->filterByChiInserisce(1234); // WHERE chi_inserisce = 1234
     * $query->filterByChiInserisce(array(12, 34)); // WHERE chi_inserisce IN (12, 34)
     * $query->filterByChiInserisce(array('min' => 12)); // WHERE chi_inserisce >= 12
     * $query->filterByChiInserisce(array('max' => 12)); // WHERE chi_inserisce <= 12
     * </code>
     *
     * @param     mixed $chiInserisce The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByChiInserisce($chiInserisce = null, $comparison = null)
    {
        if (is_array($chiInserisce)) {
            $useMinMax = false;
            if (isset($chiInserisce['min'])) {
                $this->addUsingAlias(StudentiPeer::CHI_INSERISCE, $chiInserisce['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($chiInserisce['max'])) {
                $this->addUsingAlias(StudentiPeer::CHI_INSERISCE, $chiInserisce['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CHI_INSERISCE, $chiInserisce, $comparison);
    }

    /**
     * Filter the query on the data_inserimento column
     *
     * Example usage:
     * <code>
     * $query->filterByDataInserimento(1234); // WHERE data_inserimento = 1234
     * $query->filterByDataInserimento(array(12, 34)); // WHERE data_inserimento IN (12, 34)
     * $query->filterByDataInserimento(array('min' => 12)); // WHERE data_inserimento >= 12
     * $query->filterByDataInserimento(array('max' => 12)); // WHERE data_inserimento <= 12
     * </code>
     *
     * @param     mixed $dataInserimento The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataInserimento($dataInserimento = null, $comparison = null)
    {
        if (is_array($dataInserimento)) {
            $useMinMax = false;
            if (isset($dataInserimento['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_INSERIMENTO, $dataInserimento['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataInserimento['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_INSERIMENTO, $dataInserimento['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_INSERIMENTO, $dataInserimento, $comparison);
    }

    /**
     * Filter the query on the tipo_inserimento column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoInserimento('fooValue');   // WHERE tipo_inserimento = 'fooValue'
     * $query->filterByTipoInserimento('%fooValue%'); // WHERE tipo_inserimento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoInserimento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoInserimento($tipoInserimento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoInserimento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoInserimento)) {
                $tipoInserimento = str_replace('*', '%', $tipoInserimento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_INSERIMENTO, $tipoInserimento, $comparison);
    }

    /**
     * Filter the query on the chi_modifica column
     *
     * Example usage:
     * <code>
     * $query->filterByChiModifica(1234); // WHERE chi_modifica = 1234
     * $query->filterByChiModifica(array(12, 34)); // WHERE chi_modifica IN (12, 34)
     * $query->filterByChiModifica(array('min' => 12)); // WHERE chi_modifica >= 12
     * $query->filterByChiModifica(array('max' => 12)); // WHERE chi_modifica <= 12
     * </code>
     *
     * @param     mixed $chiModifica The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByChiModifica($chiModifica = null, $comparison = null)
    {
        if (is_array($chiModifica)) {
            $useMinMax = false;
            if (isset($chiModifica['min'])) {
                $this->addUsingAlias(StudentiPeer::CHI_MODIFICA, $chiModifica['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($chiModifica['max'])) {
                $this->addUsingAlias(StudentiPeer::CHI_MODIFICA, $chiModifica['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CHI_MODIFICA, $chiModifica, $comparison);
    }

    /**
     * Filter the query on the data_modifica column
     *
     * Example usage:
     * <code>
     * $query->filterByDataModifica(1234); // WHERE data_modifica = 1234
     * $query->filterByDataModifica(array(12, 34)); // WHERE data_modifica IN (12, 34)
     * $query->filterByDataModifica(array('min' => 12)); // WHERE data_modifica >= 12
     * $query->filterByDataModifica(array('max' => 12)); // WHERE data_modifica <= 12
     * </code>
     *
     * @param     mixed $dataModifica The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataModifica($dataModifica = null, $comparison = null)
    {
        if (is_array($dataModifica)) {
            $useMinMax = false;
            if (isset($dataModifica['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_MODIFICA, $dataModifica['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataModifica['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_MODIFICA, $dataModifica['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_MODIFICA, $dataModifica, $comparison);
    }

    /**
     * Filter the query on the tipo_modifica column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoModifica('fooValue');   // WHERE tipo_modifica = 'fooValue'
     * $query->filterByTipoModifica('%fooValue%'); // WHERE tipo_modifica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoModifica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoModifica($tipoModifica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoModifica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoModifica)) {
                $tipoModifica = str_replace('*', '%', $tipoModifica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_MODIFICA, $tipoModifica, $comparison);
    }

    /**
     * Filter the query on the flag_canc column
     *
     * Example usage:
     * <code>
     * $query->filterByFlagCanc(1234); // WHERE flag_canc = 1234
     * $query->filterByFlagCanc(array(12, 34)); // WHERE flag_canc IN (12, 34)
     * $query->filterByFlagCanc(array('min' => 12)); // WHERE flag_canc >= 12
     * $query->filterByFlagCanc(array('max' => 12)); // WHERE flag_canc <= 12
     * </code>
     *
     * @param     mixed $flagCanc The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFlagCanc($flagCanc = null, $comparison = null)
    {
        if (is_array($flagCanc)) {
            $useMinMax = false;
            if (isset($flagCanc['min'])) {
                $this->addUsingAlias(StudentiPeer::FLAG_CANC, $flagCanc['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($flagCanc['max'])) {
                $this->addUsingAlias(StudentiPeer::FLAG_CANC, $flagCanc['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FLAG_CANC, $flagCanc, $comparison);
    }

    /**
     * Filter the query on the stato_avanzamento column
     *
     * Example usage:
     * <code>
     * $query->filterByStatoAvanzamento('fooValue');   // WHERE stato_avanzamento = 'fooValue'
     * $query->filterByStatoAvanzamento('%fooValue%'); // WHERE stato_avanzamento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $statoAvanzamento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStatoAvanzamento($statoAvanzamento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($statoAvanzamento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $statoAvanzamento)) {
                $statoAvanzamento = str_replace('*', '%', $statoAvanzamento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STATO_AVANZAMENTO, $statoAvanzamento, $comparison);
    }

    /**
     * Filter the query on the data_stato_avanzamento column
     *
     * Example usage:
     * <code>
     * $query->filterByDataStatoAvanzamento(1234); // WHERE data_stato_avanzamento = 1234
     * $query->filterByDataStatoAvanzamento(array(12, 34)); // WHERE data_stato_avanzamento IN (12, 34)
     * $query->filterByDataStatoAvanzamento(array('min' => 12)); // WHERE data_stato_avanzamento >= 12
     * $query->filterByDataStatoAvanzamento(array('max' => 12)); // WHERE data_stato_avanzamento <= 12
     * </code>
     *
     * @param     mixed $dataStatoAvanzamento The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataStatoAvanzamento($dataStatoAvanzamento = null, $comparison = null)
    {
        if (is_array($dataStatoAvanzamento)) {
            $useMinMax = false;
            if (isset($dataStatoAvanzamento['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_STATO_AVANZAMENTO, $dataStatoAvanzamento['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataStatoAvanzamento['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_STATO_AVANZAMENTO, $dataStatoAvanzamento['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_STATO_AVANZAMENTO, $dataStatoAvanzamento, $comparison);
    }

    /**
     * Filter the query on the cap_provincia_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByCapProvinciaNascita('fooValue');   // WHERE cap_provincia_nascita = 'fooValue'
     * $query->filterByCapProvinciaNascita('%fooValue%'); // WHERE cap_provincia_nascita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $capProvinciaNascita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCapProvinciaNascita($capProvinciaNascita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($capProvinciaNascita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $capProvinciaNascita)) {
                $capProvinciaNascita = str_replace('*', '%', $capProvinciaNascita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CAP_PROVINCIA_NASCITA, $capProvinciaNascita, $comparison);
    }

    /**
     * Filter the query on the badge column
     *
     * Example usage:
     * <code>
     * $query->filterByBadge(1234); // WHERE badge = 1234
     * $query->filterByBadge(array(12, 34)); // WHERE badge IN (12, 34)
     * $query->filterByBadge(array('min' => 12)); // WHERE badge >= 12
     * $query->filterByBadge(array('max' => 12)); // WHERE badge <= 12
     * </code>
     *
     * @param     mixed $badge The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByBadge($badge = null, $comparison = null)
    {
        if (is_array($badge)) {
            $useMinMax = false;
            if (isset($badge['min'])) {
                $this->addUsingAlias(StudentiPeer::BADGE, $badge['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($badge['max'])) {
                $this->addUsingAlias(StudentiPeer::BADGE, $badge['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::BADGE, $badge, $comparison);
    }

    /**
     * Filter the query on the cap_residenza column
     *
     * Example usage:
     * <code>
     * $query->filterByCapResidenza('fooValue');   // WHERE cap_residenza = 'fooValue'
     * $query->filterByCapResidenza('%fooValue%'); // WHERE cap_residenza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $capResidenza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCapResidenza($capResidenza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($capResidenza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $capResidenza)) {
                $capResidenza = str_replace('*', '%', $capResidenza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CAP_RESIDENZA, $capResidenza, $comparison);
    }

    /**
     * Filter the query on the codice_comune_domicilio column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceComuneDomicilio('fooValue');   // WHERE codice_comune_domicilio = 'fooValue'
     * $query->filterByCodiceComuneDomicilio('%fooValue%'); // WHERE codice_comune_domicilio LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceComuneDomicilio The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceComuneDomicilio($codiceComuneDomicilio = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceComuneDomicilio)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceComuneDomicilio)) {
                $codiceComuneDomicilio = str_replace('*', '%', $codiceComuneDomicilio);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_COMUNE_DOMICILIO, $codiceComuneDomicilio, $comparison);
    }

    /**
     * Filter the query on the cap_domicilio column
     *
     * Example usage:
     * <code>
     * $query->filterByCapDomicilio('fooValue');   // WHERE cap_domicilio = 'fooValue'
     * $query->filterByCapDomicilio('%fooValue%'); // WHERE cap_domicilio LIKE '%fooValue%'
     * </code>
     *
     * @param     string $capDomicilio The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCapDomicilio($capDomicilio = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($capDomicilio)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $capDomicilio)) {
                $capDomicilio = str_replace('*', '%', $capDomicilio);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CAP_DOMICILIO, $capDomicilio, $comparison);
    }

    /**
     * Filter the query on the cap_nascita column
     *
     * Example usage:
     * <code>
     * $query->filterByCapNascita('fooValue');   // WHERE cap_nascita = 'fooValue'
     * $query->filterByCapNascita('%fooValue%'); // WHERE cap_nascita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $capNascita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCapNascita($capNascita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($capNascita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $capNascita)) {
                $capNascita = str_replace('*', '%', $capNascita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CAP_NASCITA, $capNascita, $comparison);
    }

    /**
     * Filter the query on the indirizzo_domicilio column
     *
     * Example usage:
     * <code>
     * $query->filterByIndirizzoDomicilio('fooValue');   // WHERE indirizzo_domicilio = 'fooValue'
     * $query->filterByIndirizzoDomicilio('%fooValue%'); // WHERE indirizzo_domicilio LIKE '%fooValue%'
     * </code>
     *
     * @param     string $indirizzoDomicilio The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIndirizzoDomicilio($indirizzoDomicilio = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($indirizzoDomicilio)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $indirizzoDomicilio)) {
                $indirizzoDomicilio = str_replace('*', '%', $indirizzoDomicilio);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::INDIRIZZO_DOMICILIO, $indirizzoDomicilio, $comparison);
    }

    /**
     * Filter the query on the citta_nascita_straniera column
     *
     * Example usage:
     * <code>
     * $query->filterByCittaNascitaStraniera('fooValue');   // WHERE citta_nascita_straniera = 'fooValue'
     * $query->filterByCittaNascitaStraniera('%fooValue%'); // WHERE citta_nascita_straniera LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cittaNascitaStraniera The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCittaNascitaStraniera($cittaNascitaStraniera = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cittaNascitaStraniera)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cittaNascitaStraniera)) {
                $cittaNascitaStraniera = str_replace('*', '%', $cittaNascitaStraniera);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CITTA_NASCITA_STRANIERA, $cittaNascitaStraniera, $comparison);
    }

    /**
     * Filter the query on the cellulare_allievo column
     *
     * Example usage:
     * <code>
     * $query->filterByCellulareAllievo('fooValue');   // WHERE cellulare_allievo = 'fooValue'
     * $query->filterByCellulareAllievo('%fooValue%'); // WHERE cellulare_allievo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cellulareAllievo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCellulareAllievo($cellulareAllievo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cellulareAllievo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cellulareAllievo)) {
                $cellulareAllievo = str_replace('*', '%', $cellulareAllievo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CELLULARE_ALLIEVO, $cellulareAllievo, $comparison);
    }

    /**
     * Filter the query on the handicap column
     *
     * Example usage:
     * <code>
     * $query->filterByHandicap('fooValue');   // WHERE handicap = 'fooValue'
     * $query->filterByHandicap('%fooValue%'); // WHERE handicap LIKE '%fooValue%'
     * </code>
     *
     * @param     string $handicap The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByHandicap($handicap = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($handicap)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $handicap)) {
                $handicap = str_replace('*', '%', $handicap);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::HANDICAP, $handicap, $comparison);
    }

    /**
     * Filter the query on the stato_convittore column
     *
     * Example usage:
     * <code>
     * $query->filterByStatoConvittore('fooValue');   // WHERE stato_convittore = 'fooValue'
     * $query->filterByStatoConvittore('%fooValue%'); // WHERE stato_convittore LIKE '%fooValue%'
     * </code>
     *
     * @param     string $statoConvittore The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStatoConvittore($statoConvittore = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($statoConvittore)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $statoConvittore)) {
                $statoConvittore = str_replace('*', '%', $statoConvittore);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STATO_CONVITTORE, $statoConvittore, $comparison);
    }

    /**
     * Filter the query on the data_ritiro column
     *
     * Example usage:
     * <code>
     * $query->filterByDataRitiro(1234); // WHERE data_ritiro = 1234
     * $query->filterByDataRitiro(array(12, 34)); // WHERE data_ritiro IN (12, 34)
     * $query->filterByDataRitiro(array('min' => 12)); // WHERE data_ritiro >= 12
     * $query->filterByDataRitiro(array('max' => 12)); // WHERE data_ritiro <= 12
     * </code>
     *
     * @param     mixed $dataRitiro The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataRitiro($dataRitiro = null, $comparison = null)
    {
        if (is_array($dataRitiro)) {
            $useMinMax = false;
            if (isset($dataRitiro['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_RITIRO, $dataRitiro['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataRitiro['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_RITIRO, $dataRitiro['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_RITIRO, $dataRitiro, $comparison);
    }

    /**
     * Filter the query on the voto_ammissione column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoAmmissione('fooValue');   // WHERE voto_ammissione = 'fooValue'
     * $query->filterByVotoAmmissione('%fooValue%'); // WHERE voto_ammissione LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoAmmissione The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoAmmissione($votoAmmissione = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoAmmissione)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoAmmissione)) {
                $votoAmmissione = str_replace('*', '%', $votoAmmissione);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_AMMISSIONE, $votoAmmissione, $comparison);
    }

    /**
     * Filter the query on the differenza_punteggio column
     *
     * Example usage:
     * <code>
     * $query->filterByDifferenzaPunteggio('fooValue');   // WHERE differenza_punteggio = 'fooValue'
     * $query->filterByDifferenzaPunteggio('%fooValue%'); // WHERE differenza_punteggio LIKE '%fooValue%'
     * </code>
     *
     * @param     string $differenzaPunteggio The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDifferenzaPunteggio($differenzaPunteggio = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($differenzaPunteggio)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $differenzaPunteggio)) {
                $differenzaPunteggio = str_replace('*', '%', $differenzaPunteggio);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DIFFERENZA_PUNTEGGIO, $differenzaPunteggio, $comparison);
    }

    /**
     * Filter the query on the voto_qualifica column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoQualifica('fooValue');   // WHERE voto_qualifica = 'fooValue'
     * $query->filterByVotoQualifica('%fooValue%'); // WHERE voto_qualifica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoQualifica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoQualifica($votoQualifica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoQualifica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoQualifica)) {
                $votoQualifica = str_replace('*', '%', $votoQualifica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_QUALIFICA, $votoQualifica, $comparison);
    }

    /**
     * Filter the query on the voto_esame_sc1_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameSc1Qual('fooValue');   // WHERE voto_esame_sc1_qual = 'fooValue'
     * $query->filterByVotoEsameSc1Qual('%fooValue%'); // WHERE voto_esame_sc1_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameSc1Qual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameSc1Qual($votoEsameSc1Qual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameSc1Qual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameSc1Qual)) {
                $votoEsameSc1Qual = str_replace('*', '%', $votoEsameSc1Qual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_SC1_QUAL, $votoEsameSc1Qual, $comparison);
    }

    /**
     * Filter the query on the voto_esame_sc2_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameSc2Qual('fooValue');   // WHERE voto_esame_sc2_qual = 'fooValue'
     * $query->filterByVotoEsameSc2Qual('%fooValue%'); // WHERE voto_esame_sc2_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameSc2Qual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameSc2Qual($votoEsameSc2Qual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameSc2Qual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameSc2Qual)) {
                $votoEsameSc2Qual = str_replace('*', '%', $votoEsameSc2Qual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_SC2_QUAL, $votoEsameSc2Qual, $comparison);
    }

    /**
     * Filter the query on the voto_esame_or_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameOrQual('fooValue');   // WHERE voto_esame_or_qual = 'fooValue'
     * $query->filterByVotoEsameOrQual('%fooValue%'); // WHERE voto_esame_or_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameOrQual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameOrQual($votoEsameOrQual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameOrQual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameOrQual)) {
                $votoEsameOrQual = str_replace('*', '%', $votoEsameOrQual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_OR_QUAL, $votoEsameOrQual, $comparison);
    }

    /**
     * Filter the query on the stato_privatista column
     *
     * Example usage:
     * <code>
     * $query->filterByStatoPrivatista('fooValue');   // WHERE stato_privatista = 'fooValue'
     * $query->filterByStatoPrivatista('%fooValue%'); // WHERE stato_privatista LIKE '%fooValue%'
     * </code>
     *
     * @param     string $statoPrivatista The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStatoPrivatista($statoPrivatista = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($statoPrivatista)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $statoPrivatista)) {
                $statoPrivatista = str_replace('*', '%', $statoPrivatista);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STATO_PRIVATISTA, $statoPrivatista, $comparison);
    }

    /**
     * Filter the query on the foto column
     *
     * Example usage:
     * <code>
     * $query->filterByFoto('fooValue');   // WHERE foto = 'fooValue'
     * $query->filterByFoto('%fooValue%'); // WHERE foto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $foto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFoto($foto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($foto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $foto)) {
                $foto = str_replace('*', '%', $foto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FOTO, $foto, $comparison);
    }

    /**
     * Filter the query on the rappresentante column
     *
     * Example usage:
     * <code>
     * $query->filterByRappresentante('fooValue');   // WHERE rappresentante = 'fooValue'
     * $query->filterByRappresentante('%fooValue%'); // WHERE rappresentante LIKE '%fooValue%'
     * </code>
     *
     * @param     string $rappresentante The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByRappresentante($rappresentante = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($rappresentante)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $rappresentante)) {
                $rappresentante = str_replace('*', '%', $rappresentante);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::RAPPRESENTANTE, $rappresentante, $comparison);
    }

    /**
     * Filter the query on the obbligo_formativo column
     *
     * Example usage:
     * <code>
     * $query->filterByObbligoFormativo('fooValue');   // WHERE obbligo_formativo = 'fooValue'
     * $query->filterByObbligoFormativo('%fooValue%'); // WHERE obbligo_formativo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $obbligoFormativo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByObbligoFormativo($obbligoFormativo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($obbligoFormativo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $obbligoFormativo)) {
                $obbligoFormativo = str_replace('*', '%', $obbligoFormativo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::OBBLIGO_FORMATIVO, $obbligoFormativo, $comparison);
    }

    /**
     * Filter the query on the id_lingua_1 column
     *
     * Example usage:
     * <code>
     * $query->filterByIdLingua1(1234); // WHERE id_lingua_1 = 1234
     * $query->filterByIdLingua1(array(12, 34)); // WHERE id_lingua_1 IN (12, 34)
     * $query->filterByIdLingua1(array('min' => 12)); // WHERE id_lingua_1 >= 12
     * $query->filterByIdLingua1(array('max' => 12)); // WHERE id_lingua_1 <= 12
     * </code>
     *
     * @param     mixed $idLingua1 The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdLingua1($idLingua1 = null, $comparison = null)
    {
        if (is_array($idLingua1)) {
            $useMinMax = false;
            if (isset($idLingua1['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_1, $idLingua1['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idLingua1['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_1, $idLingua1['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_LINGUA_1, $idLingua1, $comparison);
    }

    /**
     * Filter the query on the id_lingua_2 column
     *
     * Example usage:
     * <code>
     * $query->filterByIdLingua2(1234); // WHERE id_lingua_2 = 1234
     * $query->filterByIdLingua2(array(12, 34)); // WHERE id_lingua_2 IN (12, 34)
     * $query->filterByIdLingua2(array('min' => 12)); // WHERE id_lingua_2 >= 12
     * $query->filterByIdLingua2(array('max' => 12)); // WHERE id_lingua_2 <= 12
     * </code>
     *
     * @param     mixed $idLingua2 The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdLingua2($idLingua2 = null, $comparison = null)
    {
        if (is_array($idLingua2)) {
            $useMinMax = false;
            if (isset($idLingua2['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_2, $idLingua2['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idLingua2['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_2, $idLingua2['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_LINGUA_2, $idLingua2, $comparison);
    }

    /**
     * Filter the query on the id_lingua_3 column
     *
     * Example usage:
     * <code>
     * $query->filterByIdLingua3(1234); // WHERE id_lingua_3 = 1234
     * $query->filterByIdLingua3(array(12, 34)); // WHERE id_lingua_3 IN (12, 34)
     * $query->filterByIdLingua3(array('min' => 12)); // WHERE id_lingua_3 >= 12
     * $query->filterByIdLingua3(array('max' => 12)); // WHERE id_lingua_3 <= 12
     * </code>
     *
     * @param     mixed $idLingua3 The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdLingua3($idLingua3 = null, $comparison = null)
    {
        if (is_array($idLingua3)) {
            $useMinMax = false;
            if (isset($idLingua3['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_3, $idLingua3['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idLingua3['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_3, $idLingua3['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_LINGUA_3, $idLingua3, $comparison);
    }

    /**
     * Filter the query on the id_lingua_4 column
     *
     * Example usage:
     * <code>
     * $query->filterByIdLingua4(1234); // WHERE id_lingua_4 = 1234
     * $query->filterByIdLingua4(array(12, 34)); // WHERE id_lingua_4 IN (12, 34)
     * $query->filterByIdLingua4(array('min' => 12)); // WHERE id_lingua_4 >= 12
     * $query->filterByIdLingua4(array('max' => 12)); // WHERE id_lingua_4 <= 12
     * </code>
     *
     * @param     mixed $idLingua4 The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdLingua4($idLingua4 = null, $comparison = null)
    {
        if (is_array($idLingua4)) {
            $useMinMax = false;
            if (isset($idLingua4['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_4, $idLingua4['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idLingua4['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_4, $idLingua4['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_LINGUA_4, $idLingua4, $comparison);
    }

    /**
     * Filter the query on the id_lingua_5 column
     *
     * Example usage:
     * <code>
     * $query->filterByIdLingua5(1234); // WHERE id_lingua_5 = 1234
     * $query->filterByIdLingua5(array(12, 34)); // WHERE id_lingua_5 IN (12, 34)
     * $query->filterByIdLingua5(array('min' => 12)); // WHERE id_lingua_5 >= 12
     * $query->filterByIdLingua5(array('max' => 12)); // WHERE id_lingua_5 <= 12
     * </code>
     *
     * @param     mixed $idLingua5 The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdLingua5($idLingua5 = null, $comparison = null)
    {
        if (is_array($idLingua5)) {
            $useMinMax = false;
            if (isset($idLingua5['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_5, $idLingua5['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idLingua5['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_LINGUA_5, $idLingua5['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_LINGUA_5, $idLingua5, $comparison);
    }

    /**
     * Filter the query on the id_provenienza_scolastica column
     *
     * Example usage:
     * <code>
     * $query->filterByIdProvenienzaScolastica('fooValue');   // WHERE id_provenienza_scolastica = 'fooValue'
     * $query->filterByIdProvenienzaScolastica('%fooValue%'); // WHERE id_provenienza_scolastica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $idProvenienzaScolastica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdProvenienzaScolastica($idProvenienzaScolastica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($idProvenienzaScolastica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $idProvenienzaScolastica)) {
                $idProvenienzaScolastica = str_replace('*', '%', $idProvenienzaScolastica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_PROVENIENZA_SCOLASTICA, $idProvenienzaScolastica, $comparison);
    }

    /**
     * Filter the query on the id_scuola_media column
     *
     * Example usage:
     * <code>
     * $query->filterByIdScuolaMedia('fooValue');   // WHERE id_scuola_media = 'fooValue'
     * $query->filterByIdScuolaMedia('%fooValue%'); // WHERE id_scuola_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $idScuolaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdScuolaMedia($idScuolaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($idScuolaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $idScuolaMedia)) {
                $idScuolaMedia = str_replace('*', '%', $idScuolaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_SCUOLA_MEDIA, $idScuolaMedia, $comparison);
    }

    /**
     * Filter the query on the lingua_scuola_media column
     *
     * Example usage:
     * <code>
     * $query->filterByLinguaScuolaMedia('fooValue');   // WHERE lingua_scuola_media = 'fooValue'
     * $query->filterByLinguaScuolaMedia('%fooValue%'); // WHERE lingua_scuola_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $linguaScuolaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByLinguaScuolaMedia($linguaScuolaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($linguaScuolaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $linguaScuolaMedia)) {
                $linguaScuolaMedia = str_replace('*', '%', $linguaScuolaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::LINGUA_SCUOLA_MEDIA, $linguaScuolaMedia, $comparison);
    }

    /**
     * Filter the query on the lingua_scuola_media_2 column
     *
     * Example usage:
     * <code>
     * $query->filterByLinguaScuolaMedia2('fooValue');   // WHERE lingua_scuola_media_2 = 'fooValue'
     * $query->filterByLinguaScuolaMedia2('%fooValue%'); // WHERE lingua_scuola_media_2 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $linguaScuolaMedia2 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByLinguaScuolaMedia2($linguaScuolaMedia2 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($linguaScuolaMedia2)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $linguaScuolaMedia2)) {
                $linguaScuolaMedia2 = str_replace('*', '%', $linguaScuolaMedia2);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::LINGUA_SCUOLA_MEDIA_2, $linguaScuolaMedia2, $comparison);
    }

    /**
     * Filter the query on the giudizio_scuola_media column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioScuolaMedia('fooValue');   // WHERE giudizio_scuola_media = 'fooValue'
     * $query->filterByGiudizioScuolaMedia('%fooValue%'); // WHERE giudizio_scuola_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioScuolaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioScuolaMedia($giudizioScuolaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioScuolaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioScuolaMedia)) {
                $giudizioScuolaMedia = str_replace('*', '%', $giudizioScuolaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_SCUOLA_MEDIA, $giudizioScuolaMedia, $comparison);
    }

    /**
     * Filter the query on the trasporto column
     *
     * Example usage:
     * <code>
     * $query->filterByTrasporto('fooValue');   // WHERE trasporto = 'fooValue'
     * $query->filterByTrasporto('%fooValue%'); // WHERE trasporto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $trasporto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTrasporto($trasporto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($trasporto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $trasporto)) {
                $trasporto = str_replace('*', '%', $trasporto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TRASPORTO, $trasporto, $comparison);
    }

    /**
     * Filter the query on the data_iscrizione column
     *
     * Example usage:
     * <code>
     * $query->filterByDataIscrizione(1234); // WHERE data_iscrizione = 1234
     * $query->filterByDataIscrizione(array(12, 34)); // WHERE data_iscrizione IN (12, 34)
     * $query->filterByDataIscrizione(array('min' => 12)); // WHERE data_iscrizione >= 12
     * $query->filterByDataIscrizione(array('max' => 12)); // WHERE data_iscrizione <= 12
     * </code>
     *
     * @param     mixed $dataIscrizione The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataIscrizione($dataIscrizione = null, $comparison = null)
    {
        if (is_array($dataIscrizione)) {
            $useMinMax = false;
            if (isset($dataIscrizione['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_ISCRIZIONE, $dataIscrizione['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataIscrizione['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_ISCRIZIONE, $dataIscrizione['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_ISCRIZIONE, $dataIscrizione, $comparison);
    }

    /**
     * Filter the query on the pei column
     *
     * Example usage:
     * <code>
     * $query->filterByPei('fooValue');   // WHERE pei = 'fooValue'
     * $query->filterByPei('%fooValue%'); // WHERE pei LIKE '%fooValue%'
     * </code>
     *
     * @param     string $pei The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByPei($pei = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($pei)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $pei)) {
                $pei = str_replace('*', '%', $pei);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::PEI, $pei, $comparison);
    }

    /**
     * Filter the query on the ammesso_esame_qualifica column
     *
     * Example usage:
     * <code>
     * $query->filterByAmmessoEsameQualifica('fooValue');   // WHERE ammesso_esame_qualifica = 'fooValue'
     * $query->filterByAmmessoEsameQualifica('%fooValue%'); // WHERE ammesso_esame_qualifica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $ammessoEsameQualifica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAmmessoEsameQualifica($ammessoEsameQualifica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($ammessoEsameQualifica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $ammessoEsameQualifica)) {
                $ammessoEsameQualifica = str_replace('*', '%', $ammessoEsameQualifica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AMMESSO_ESAME_QUALIFICA, $ammessoEsameQualifica, $comparison);
    }

    /**
     * Filter the query on the ammesso_esame_quinta column
     *
     * Example usage:
     * <code>
     * $query->filterByAmmessoEsameQuinta('fooValue');   // WHERE ammesso_esame_quinta = 'fooValue'
     * $query->filterByAmmessoEsameQuinta('%fooValue%'); // WHERE ammesso_esame_quinta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $ammessoEsameQuinta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAmmessoEsameQuinta($ammessoEsameQuinta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($ammessoEsameQuinta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $ammessoEsameQuinta)) {
                $ammessoEsameQuinta = str_replace('*', '%', $ammessoEsameQuinta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AMMESSO_ESAME_QUINTA, $ammessoEsameQuinta, $comparison);
    }

    /**
     * Filter the query on the giudizio_ammissione_quinta column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioAmmissioneQuinta('fooValue');   // WHERE giudizio_ammissione_quinta = 'fooValue'
     * $query->filterByGiudizioAmmissioneQuinta('%fooValue%'); // WHERE giudizio_ammissione_quinta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioAmmissioneQuinta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioAmmissioneQuinta($giudizioAmmissioneQuinta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioAmmissioneQuinta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioAmmissioneQuinta)) {
                $giudizioAmmissioneQuinta = str_replace('*', '%', $giudizioAmmissioneQuinta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_AMMISSIONE_QUINTA, $giudizioAmmissioneQuinta, $comparison);
    }

    /**
     * Filter the query on the grado_handicap column
     *
     * Example usage:
     * <code>
     * $query->filterByGradoHandicap('fooValue');   // WHERE grado_handicap = 'fooValue'
     * $query->filterByGradoHandicap('%fooValue%'); // WHERE grado_handicap LIKE '%fooValue%'
     * </code>
     *
     * @param     string $gradoHandicap The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGradoHandicap($gradoHandicap = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($gradoHandicap)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $gradoHandicap)) {
                $gradoHandicap = str_replace('*', '%', $gradoHandicap);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GRADO_HANDICAP, $gradoHandicap, $comparison);
    }

    /**
     * Filter the query on the tipo_handicap column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoHandicap('fooValue');   // WHERE tipo_handicap = 'fooValue'
     * $query->filterByTipoHandicap('%fooValue%'); // WHERE tipo_handicap LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoHandicap The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoHandicap($tipoHandicap = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoHandicap)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoHandicap)) {
                $tipoHandicap = str_replace('*', '%', $tipoHandicap);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_HANDICAP, $tipoHandicap, $comparison);
    }

    /**
     * Filter the query on the stato_licenza_maestro column
     *
     * Example usage:
     * <code>
     * $query->filterByStatoLicenzaMaestro('fooValue');   // WHERE stato_licenza_maestro = 'fooValue'
     * $query->filterByStatoLicenzaMaestro('%fooValue%'); // WHERE stato_licenza_maestro LIKE '%fooValue%'
     * </code>
     *
     * @param     string $statoLicenzaMaestro The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStatoLicenzaMaestro($statoLicenzaMaestro = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($statoLicenzaMaestro)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $statoLicenzaMaestro)) {
                $statoLicenzaMaestro = str_replace('*', '%', $statoLicenzaMaestro);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STATO_LICENZA_MAESTRO, $statoLicenzaMaestro, $comparison);
    }

    /**
     * Filter the query on the id_studente_sissi column
     *
     * Example usage:
     * <code>
     * $query->filterByIdStudenteSissi('fooValue');   // WHERE id_studente_sissi = 'fooValue'
     * $query->filterByIdStudenteSissi('%fooValue%'); // WHERE id_studente_sissi LIKE '%fooValue%'
     * </code>
     *
     * @param     string $idStudenteSissi The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdStudenteSissi($idStudenteSissi = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($idStudenteSissi)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $idStudenteSissi)) {
                $idStudenteSissi = str_replace('*', '%', $idStudenteSissi);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_STUDENTE_SISSI, $idStudenteSissi, $comparison);
    }

    /**
     * Filter the query on the badge_rfid column
     *
     * Example usage:
     * <code>
     * $query->filterByBadgeRfid('fooValue');   // WHERE badge_rfid = 'fooValue'
     * $query->filterByBadgeRfid('%fooValue%'); // WHERE badge_rfid LIKE '%fooValue%'
     * </code>
     *
     * @param     string $badgeRfid The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByBadgeRfid($badgeRfid = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($badgeRfid)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $badgeRfid)) {
                $badgeRfid = str_replace('*', '%', $badgeRfid);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::BADGE_RFID, $badgeRfid, $comparison);
    }

    /**
     * Filter the query on the lode column
     *
     * Example usage:
     * <code>
     * $query->filterByLode('fooValue');   // WHERE lode = 'fooValue'
     * $query->filterByLode('%fooValue%'); // WHERE lode LIKE '%fooValue%'
     * </code>
     *
     * @param     string $lode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByLode($lode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($lode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $lode)) {
                $lode = str_replace('*', '%', $lode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::LODE, $lode, $comparison);
    }

    /**
     * Filter the query on the distretto_scolastico column
     *
     * Example usage:
     * <code>
     * $query->filterByDistrettoScolastico('fooValue');   // WHERE distretto_scolastico = 'fooValue'
     * $query->filterByDistrettoScolastico('%fooValue%'); // WHERE distretto_scolastico LIKE '%fooValue%'
     * </code>
     *
     * @param     string $distrettoScolastico The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDistrettoScolastico($distrettoScolastico = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($distrettoScolastico)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $distrettoScolastico)) {
                $distrettoScolastico = str_replace('*', '%', $distrettoScolastico);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DISTRETTO_SCOLASTICO, $distrettoScolastico, $comparison);
    }

    /**
     * Filter the query on the giudizio_ammissione_terza column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioAmmissioneTerza('fooValue');   // WHERE giudizio_ammissione_terza = 'fooValue'
     * $query->filterByGiudizioAmmissioneTerza('%fooValue%'); // WHERE giudizio_ammissione_terza LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioAmmissioneTerza The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioAmmissioneTerza($giudizioAmmissioneTerza = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioAmmissioneTerza)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioAmmissioneTerza)) {
                $giudizioAmmissioneTerza = str_replace('*', '%', $giudizioAmmissioneTerza);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_AMMISSIONE_TERZA, $giudizioAmmissioneTerza, $comparison);
    }

    /**
     * Filter the query on the esito_prima_media column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoPrimaMedia('fooValue');   // WHERE esito_prima_media = 'fooValue'
     * $query->filterByEsitoPrimaMedia('%fooValue%'); // WHERE esito_prima_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoPrimaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoPrimaMedia($esitoPrimaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoPrimaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoPrimaMedia)) {
                $esitoPrimaMedia = str_replace('*', '%', $esitoPrimaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_PRIMA_MEDIA, $esitoPrimaMedia, $comparison);
    }

    /**
     * Filter the query on the esito_seconda_media column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoSecondaMedia('fooValue');   // WHERE esito_seconda_media = 'fooValue'
     * $query->filterByEsitoSecondaMedia('%fooValue%'); // WHERE esito_seconda_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoSecondaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoSecondaMedia($esitoSecondaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoSecondaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoSecondaMedia)) {
                $esitoSecondaMedia = str_replace('*', '%', $esitoSecondaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_SECONDA_MEDIA, $esitoSecondaMedia, $comparison);
    }

    /**
     * Filter the query on the esito_terza_media column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoTerzaMedia('fooValue');   // WHERE esito_terza_media = 'fooValue'
     * $query->filterByEsitoTerzaMedia('%fooValue%'); // WHERE esito_terza_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoTerzaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoTerzaMedia($esitoTerzaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoTerzaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoTerzaMedia)) {
                $esitoTerzaMedia = str_replace('*', '%', $esitoTerzaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_TERZA_MEDIA, $esitoTerzaMedia, $comparison);
    }

    /**
     * Filter the query on the giudizio_esame_sc1_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioEsameSc1Qual('fooValue');   // WHERE giudizio_esame_sc1_qual = 'fooValue'
     * $query->filterByGiudizioEsameSc1Qual('%fooValue%'); // WHERE giudizio_esame_sc1_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioEsameSc1Qual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioEsameSc1Qual($giudizioEsameSc1Qual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioEsameSc1Qual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioEsameSc1Qual)) {
                $giudizioEsameSc1Qual = str_replace('*', '%', $giudizioEsameSc1Qual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_ESAME_SC1_QUAL, $giudizioEsameSc1Qual, $comparison);
    }

    /**
     * Filter the query on the giudizio_esame_sc2_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioEsameSc2Qual('fooValue');   // WHERE giudizio_esame_sc2_qual = 'fooValue'
     * $query->filterByGiudizioEsameSc2Qual('%fooValue%'); // WHERE giudizio_esame_sc2_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioEsameSc2Qual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioEsameSc2Qual($giudizioEsameSc2Qual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioEsameSc2Qual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioEsameSc2Qual)) {
                $giudizioEsameSc2Qual = str_replace('*', '%', $giudizioEsameSc2Qual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_ESAME_SC2_QUAL, $giudizioEsameSc2Qual, $comparison);
    }

    /**
     * Filter the query on the giudizio_esame_or_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioEsameOrQual('fooValue');   // WHERE giudizio_esame_or_qual = 'fooValue'
     * $query->filterByGiudizioEsameOrQual('%fooValue%'); // WHERE giudizio_esame_or_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioEsameOrQual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioEsameOrQual($giudizioEsameOrQual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioEsameOrQual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioEsameOrQual)) {
                $giudizioEsameOrQual = str_replace('*', '%', $giudizioEsameOrQual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_ESAME_OR_QUAL, $giudizioEsameOrQual, $comparison);
    }

    /**
     * Filter the query on the giudizio_complessivo_esame_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioComplessivoEsameQual('fooValue');   // WHERE giudizio_complessivo_esame_qual = 'fooValue'
     * $query->filterByGiudizioComplessivoEsameQual('%fooValue%'); // WHERE giudizio_complessivo_esame_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioComplessivoEsameQual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioComplessivoEsameQual($giudizioComplessivoEsameQual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioComplessivoEsameQual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioComplessivoEsameQual)) {
                $giudizioComplessivoEsameQual = str_replace('*', '%', $giudizioComplessivoEsameQual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL, $giudizioComplessivoEsameQual, $comparison);
    }

    /**
     * Filter the query on the acconsente_aziende column
     *
     * Example usage:
     * <code>
     * $query->filterByAcconsenteAziende(1234); // WHERE acconsente_aziende = 1234
     * $query->filterByAcconsenteAziende(array(12, 34)); // WHERE acconsente_aziende IN (12, 34)
     * $query->filterByAcconsenteAziende(array('min' => 12)); // WHERE acconsente_aziende >= 12
     * $query->filterByAcconsenteAziende(array('max' => 12)); // WHERE acconsente_aziende <= 12
     * </code>
     *
     * @param     mixed $acconsenteAziende The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAcconsenteAziende($acconsenteAziende = null, $comparison = null)
    {
        if (is_array($acconsenteAziende)) {
            $useMinMax = false;
            if (isset($acconsenteAziende['min'])) {
                $this->addUsingAlias(StudentiPeer::ACCONSENTE_AZIENDE, $acconsenteAziende['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($acconsenteAziende['max'])) {
                $this->addUsingAlias(StudentiPeer::ACCONSENTE_AZIENDE, $acconsenteAziende['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ACCONSENTE_AZIENDE, $acconsenteAziende, $comparison);
    }

    /**
     * Filter the query on the curriculum_prima column
     *
     * Example usage:
     * <code>
     * $query->filterByCurriculumPrima('fooValue');   // WHERE curriculum_prima = 'fooValue'
     * $query->filterByCurriculumPrima('%fooValue%'); // WHERE curriculum_prima LIKE '%fooValue%'
     * </code>
     *
     * @param     string $curriculumPrima The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCurriculumPrima($curriculumPrima = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($curriculumPrima)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $curriculumPrima)) {
                $curriculumPrima = str_replace('*', '%', $curriculumPrima);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CURRICULUM_PRIMA, $curriculumPrima, $comparison);
    }

    /**
     * Filter the query on the curriculum_seconda column
     *
     * Example usage:
     * <code>
     * $query->filterByCurriculumSeconda('fooValue');   // WHERE curriculum_seconda = 'fooValue'
     * $query->filterByCurriculumSeconda('%fooValue%'); // WHERE curriculum_seconda LIKE '%fooValue%'
     * </code>
     *
     * @param     string $curriculumSeconda The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCurriculumSeconda($curriculumSeconda = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($curriculumSeconda)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $curriculumSeconda)) {
                $curriculumSeconda = str_replace('*', '%', $curriculumSeconda);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CURRICULUM_SECONDA, $curriculumSeconda, $comparison);
    }

    /**
     * Filter the query on the stage_professionali column
     *
     * Example usage:
     * <code>
     * $query->filterByStageProfessionali('fooValue');   // WHERE stage_professionali = 'fooValue'
     * $query->filterByStageProfessionali('%fooValue%'); // WHERE stage_professionali LIKE '%fooValue%'
     * </code>
     *
     * @param     string $stageProfessionali The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStageProfessionali($stageProfessionali = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($stageProfessionali)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $stageProfessionali)) {
                $stageProfessionali = str_replace('*', '%', $stageProfessionali);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STAGE_PROFESSIONALI, $stageProfessionali, $comparison);
    }

    /**
     * Filter the query on the data_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByDataOrale(1234); // WHERE data_orale = 1234
     * $query->filterByDataOrale(array(12, 34)); // WHERE data_orale IN (12, 34)
     * $query->filterByDataOrale(array('min' => 12)); // WHERE data_orale >= 12
     * $query->filterByDataOrale(array('max' => 12)); // WHERE data_orale <= 12
     * </code>
     *
     * @param     mixed $dataOrale The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataOrale($dataOrale = null, $comparison = null)
    {
        if (is_array($dataOrale)) {
            $useMinMax = false;
            if (isset($dataOrale['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_ORALE, $dataOrale['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataOrale['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_ORALE, $dataOrale['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_ORALE, $dataOrale, $comparison);
    }

    /**
     * Filter the query on the ordine_esame_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByOrdineEsameOrale(1234); // WHERE ordine_esame_orale = 1234
     * $query->filterByOrdineEsameOrale(array(12, 34)); // WHERE ordine_esame_orale IN (12, 34)
     * $query->filterByOrdineEsameOrale(array('min' => 12)); // WHERE ordine_esame_orale >= 12
     * $query->filterByOrdineEsameOrale(array('max' => 12)); // WHERE ordine_esame_orale <= 12
     * </code>
     *
     * @param     mixed $ordineEsameOrale The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByOrdineEsameOrale($ordineEsameOrale = null, $comparison = null)
    {
        if (is_array($ordineEsameOrale)) {
            $useMinMax = false;
            if (isset($ordineEsameOrale['min'])) {
                $this->addUsingAlias(StudentiPeer::ORDINE_ESAME_ORALE, $ordineEsameOrale['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($ordineEsameOrale['max'])) {
                $this->addUsingAlias(StudentiPeer::ORDINE_ESAME_ORALE, $ordineEsameOrale['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ORDINE_ESAME_ORALE, $ordineEsameOrale, $comparison);
    }

    /**
     * Filter the query on the tipo_primo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoPrimoScritto('fooValue');   // WHERE tipo_primo_scritto = 'fooValue'
     * $query->filterByTipoPrimoScritto('%fooValue%'); // WHERE tipo_primo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoPrimoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoPrimoScritto($tipoPrimoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoPrimoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoPrimoScritto)) {
                $tipoPrimoScritto = str_replace('*', '%', $tipoPrimoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_PRIMO_SCRITTO, $tipoPrimoScritto, $comparison);
    }

    /**
     * Filter the query on the tipo_secondo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoSecondoScritto('fooValue');   // WHERE tipo_secondo_scritto = 'fooValue'
     * $query->filterByTipoSecondoScritto('%fooValue%'); // WHERE tipo_secondo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoSecondoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoSecondoScritto($tipoSecondoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoSecondoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoSecondoScritto)) {
                $tipoSecondoScritto = str_replace('*', '%', $tipoSecondoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_SECONDO_SCRITTO, $tipoSecondoScritto, $comparison);
    }

    /**
     * Filter the query on the tipo_terzo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoTerzoScritto('fooValue');   // WHERE tipo_terzo_scritto = 'fooValue'
     * $query->filterByTipoTerzoScritto('%fooValue%'); // WHERE tipo_terzo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoTerzoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoTerzoScritto($tipoTerzoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoTerzoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoTerzoScritto)) {
                $tipoTerzoScritto = str_replace('*', '%', $tipoTerzoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_TERZO_SCRITTO, $tipoTerzoScritto, $comparison);
    }

    /**
     * Filter the query on the unanimita_primo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByUnanimitaPrimoScritto('fooValue');   // WHERE unanimita_primo_scritto = 'fooValue'
     * $query->filterByUnanimitaPrimoScritto('%fooValue%'); // WHERE unanimita_primo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unanimitaPrimoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByUnanimitaPrimoScritto($unanimitaPrimoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unanimitaPrimoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unanimitaPrimoScritto)) {
                $unanimitaPrimoScritto = str_replace('*', '%', $unanimitaPrimoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::UNANIMITA_PRIMO_SCRITTO, $unanimitaPrimoScritto, $comparison);
    }

    /**
     * Filter the query on the unanimita_secondo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByUnanimitaSecondoScritto('fooValue');   // WHERE unanimita_secondo_scritto = 'fooValue'
     * $query->filterByUnanimitaSecondoScritto('%fooValue%'); // WHERE unanimita_secondo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unanimitaSecondoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByUnanimitaSecondoScritto($unanimitaSecondoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unanimitaSecondoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unanimitaSecondoScritto)) {
                $unanimitaSecondoScritto = str_replace('*', '%', $unanimitaSecondoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::UNANIMITA_SECONDO_SCRITTO, $unanimitaSecondoScritto, $comparison);
    }

    /**
     * Filter the query on the unanimita_terzo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByUnanimitaTerzoScritto('fooValue');   // WHERE unanimita_terzo_scritto = 'fooValue'
     * $query->filterByUnanimitaTerzoScritto('%fooValue%'); // WHERE unanimita_terzo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unanimitaTerzoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByUnanimitaTerzoScritto($unanimitaTerzoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unanimitaTerzoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unanimitaTerzoScritto)) {
                $unanimitaTerzoScritto = str_replace('*', '%', $unanimitaTerzoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::UNANIMITA_TERZO_SCRITTO, $unanimitaTerzoScritto, $comparison);
    }

    /**
     * Filter the query on the argomento_scelto_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByArgomentoSceltoOrale('fooValue');   // WHERE argomento_scelto_orale = 'fooValue'
     * $query->filterByArgomentoSceltoOrale('%fooValue%'); // WHERE argomento_scelto_orale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $argomentoSceltoOrale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByArgomentoSceltoOrale($argomentoSceltoOrale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($argomentoSceltoOrale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $argomentoSceltoOrale)) {
                $argomentoSceltoOrale = str_replace('*', '%', $argomentoSceltoOrale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ARGOMENTO_SCELTO_ORALE, $argomentoSceltoOrale, $comparison);
    }

    /**
     * Filter the query on the area_disc_1_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByAreaDisc1Orale('fooValue');   // WHERE area_disc_1_orale = 'fooValue'
     * $query->filterByAreaDisc1Orale('%fooValue%'); // WHERE area_disc_1_orale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $areaDisc1Orale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAreaDisc1Orale($areaDisc1Orale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($areaDisc1Orale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $areaDisc1Orale)) {
                $areaDisc1Orale = str_replace('*', '%', $areaDisc1Orale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AREA_DISC_1_ORALE, $areaDisc1Orale, $comparison);
    }

    /**
     * Filter the query on the area_disc_2_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByAreaDisc2Orale('fooValue');   // WHERE area_disc_2_orale = 'fooValue'
     * $query->filterByAreaDisc2Orale('%fooValue%'); // WHERE area_disc_2_orale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $areaDisc2Orale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByAreaDisc2Orale($areaDisc2Orale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($areaDisc2Orale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $areaDisc2Orale)) {
                $areaDisc2Orale = str_replace('*', '%', $areaDisc2Orale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::AREA_DISC_2_ORALE, $areaDisc2Orale, $comparison);
    }

    /**
     * Filter the query on the disc_elaborati_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByDiscElaboratiOrale('fooValue');   // WHERE disc_elaborati_orale = 'fooValue'
     * $query->filterByDiscElaboratiOrale('%fooValue%'); // WHERE disc_elaborati_orale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $discElaboratiOrale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDiscElaboratiOrale($discElaboratiOrale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($discElaboratiOrale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $discElaboratiOrale)) {
                $discElaboratiOrale = str_replace('*', '%', $discElaboratiOrale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DISC_ELABORATI_ORALE, $discElaboratiOrale, $comparison);
    }

    /**
     * Filter the query on the unanimita_voto_finale column
     *
     * Example usage:
     * <code>
     * $query->filterByUnanimitaVotoFinale('fooValue');   // WHERE unanimita_voto_finale = 'fooValue'
     * $query->filterByUnanimitaVotoFinale('%fooValue%'); // WHERE unanimita_voto_finale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unanimitaVotoFinale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByUnanimitaVotoFinale($unanimitaVotoFinale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unanimitaVotoFinale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unanimitaVotoFinale)) {
                $unanimitaVotoFinale = str_replace('*', '%', $unanimitaVotoFinale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::UNANIMITA_VOTO_FINALE, $unanimitaVotoFinale, $comparison);
    }

    /**
     * Filter the query on the presente_esame_quinta column
     *
     * Example usage:
     * <code>
     * $query->filterByPresenteEsameQuinta('fooValue');   // WHERE presente_esame_quinta = 'fooValue'
     * $query->filterByPresenteEsameQuinta('%fooValue%'); // WHERE presente_esame_quinta LIKE '%fooValue%'
     * </code>
     *
     * @param     string $presenteEsameQuinta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByPresenteEsameQuinta($presenteEsameQuinta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($presenteEsameQuinta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $presenteEsameQuinta)) {
                $presenteEsameQuinta = str_replace('*', '%', $presenteEsameQuinta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::PRESENTE_ESAME_QUINTA, $presenteEsameQuinta, $comparison);
    }

    /**
     * Filter the query on the stampa_badge column
     *
     * Example usage:
     * <code>
     * $query->filterByStampaBadge('fooValue');   // WHERE stampa_badge = 'fooValue'
     * $query->filterByStampaBadge('%fooValue%'); // WHERE stampa_badge LIKE '%fooValue%'
     * </code>
     *
     * @param     string $stampaBadge The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByStampaBadge($stampaBadge = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($stampaBadge)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $stampaBadge)) {
                $stampaBadge = str_replace('*', '%', $stampaBadge);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::STAMPA_BADGE, $stampaBadge, $comparison);
    }

    /**
     * Filter the query on the id_classe_destinazione column
     *
     * Example usage:
     * <code>
     * $query->filterByIdClasseDestinazione(1234); // WHERE id_classe_destinazione = 1234
     * $query->filterByIdClasseDestinazione(array(12, 34)); // WHERE id_classe_destinazione IN (12, 34)
     * $query->filterByIdClasseDestinazione(array('min' => 12)); // WHERE id_classe_destinazione >= 12
     * $query->filterByIdClasseDestinazione(array('max' => 12)); // WHERE id_classe_destinazione <= 12
     * </code>
     *
     * @param     mixed $idClasseDestinazione The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdClasseDestinazione($idClasseDestinazione = null, $comparison = null)
    {
        if (is_array($idClasseDestinazione)) {
            $useMinMax = false;
            if (isset($idClasseDestinazione['min'])) {
                $this->addUsingAlias(StudentiPeer::ID_CLASSE_DESTINAZIONE, $idClasseDestinazione['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idClasseDestinazione['max'])) {
                $this->addUsingAlias(StudentiPeer::ID_CLASSE_DESTINAZIONE, $idClasseDestinazione['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_CLASSE_DESTINAZIONE, $idClasseDestinazione, $comparison);
    }

    /**
     * Filter the query on the sconto_rette column
     *
     * Example usage:
     * <code>
     * $query->filterByScontoRette(1234); // WHERE sconto_rette = 1234
     * $query->filterByScontoRette(array(12, 34)); // WHERE sconto_rette IN (12, 34)
     * $query->filterByScontoRette(array('min' => 12)); // WHERE sconto_rette >= 12
     * $query->filterByScontoRette(array('max' => 12)); // WHERE sconto_rette <= 12
     * </code>
     *
     * @param     mixed $scontoRette The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByScontoRette($scontoRette = null, $comparison = null)
    {
        if (is_array($scontoRette)) {
            $useMinMax = false;
            if (isset($scontoRette['min'])) {
                $this->addUsingAlias(StudentiPeer::SCONTO_RETTE, $scontoRette['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($scontoRette['max'])) {
                $this->addUsingAlias(StudentiPeer::SCONTO_RETTE, $scontoRette['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::SCONTO_RETTE, $scontoRette, $comparison);
    }

    /**
     * Filter the query on the carta_studente_numero column
     *
     * Example usage:
     * <code>
     * $query->filterByCartaStudenteNumero(1234); // WHERE carta_studente_numero = 1234
     * $query->filterByCartaStudenteNumero(array(12, 34)); // WHERE carta_studente_numero IN (12, 34)
     * $query->filterByCartaStudenteNumero(array('min' => 12)); // WHERE carta_studente_numero >= 12
     * $query->filterByCartaStudenteNumero(array('max' => 12)); // WHERE carta_studente_numero <= 12
     * </code>
     *
     * @param     mixed $cartaStudenteNumero The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCartaStudenteNumero($cartaStudenteNumero = null, $comparison = null)
    {
        if (is_array($cartaStudenteNumero)) {
            $useMinMax = false;
            if (isset($cartaStudenteNumero['min'])) {
                $this->addUsingAlias(StudentiPeer::CARTA_STUDENTE_NUMERO, $cartaStudenteNumero['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($cartaStudenteNumero['max'])) {
                $this->addUsingAlias(StudentiPeer::CARTA_STUDENTE_NUMERO, $cartaStudenteNumero['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CARTA_STUDENTE_NUMERO, $cartaStudenteNumero, $comparison);
    }

    /**
     * Filter the query on the carta_studente_scadenza column
     *
     * Example usage:
     * <code>
     * $query->filterByCartaStudenteScadenza(1234); // WHERE carta_studente_scadenza = 1234
     * $query->filterByCartaStudenteScadenza(array(12, 34)); // WHERE carta_studente_scadenza IN (12, 34)
     * $query->filterByCartaStudenteScadenza(array('min' => 12)); // WHERE carta_studente_scadenza >= 12
     * $query->filterByCartaStudenteScadenza(array('max' => 12)); // WHERE carta_studente_scadenza <= 12
     * </code>
     *
     * @param     mixed $cartaStudenteScadenza The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCartaStudenteScadenza($cartaStudenteScadenza = null, $comparison = null)
    {
        if (is_array($cartaStudenteScadenza)) {
            $useMinMax = false;
            if (isset($cartaStudenteScadenza['min'])) {
                $this->addUsingAlias(StudentiPeer::CARTA_STUDENTE_SCADENZA, $cartaStudenteScadenza['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($cartaStudenteScadenza['max'])) {
                $this->addUsingAlias(StudentiPeer::CARTA_STUDENTE_SCADENZA, $cartaStudenteScadenza['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CARTA_STUDENTE_SCADENZA, $cartaStudenteScadenza, $comparison);
    }

    /**
     * Filter the query on the esito_corrente_calcolato column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoCorrenteCalcolato('fooValue');   // WHERE esito_corrente_calcolato = 'fooValue'
     * $query->filterByEsitoCorrenteCalcolato('%fooValue%'); // WHERE esito_corrente_calcolato LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoCorrenteCalcolato The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoCorrenteCalcolato($esitoCorrenteCalcolato = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoCorrenteCalcolato)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoCorrenteCalcolato)) {
                $esitoCorrenteCalcolato = str_replace('*', '%', $esitoCorrenteCalcolato);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_CORRENTE_CALCOLATO, $esitoCorrenteCalcolato, $comparison);
    }

    /**
     * Filter the query on the id_flusso column
     *
     * Example usage:
     * <code>
     * $query->filterByIdFlusso('fooValue');   // WHERE id_flusso = 'fooValue'
     * $query->filterByIdFlusso('%fooValue%'); // WHERE id_flusso LIKE '%fooValue%'
     * </code>
     *
     * @param     string $idFlusso The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByIdFlusso($idFlusso = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($idFlusso)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $idFlusso)) {
                $idFlusso = str_replace('*', '%', $idFlusso);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ID_FLUSSO, $idFlusso, $comparison);
    }

    /**
     * Filter the query on the data_aggiornamento_sogei column
     *
     * Example usage:
     * <code>
     * $query->filterByDataAggiornamentoSogei('fooValue');   // WHERE data_aggiornamento_sogei = 'fooValue'
     * $query->filterByDataAggiornamentoSogei('%fooValue%'); // WHERE data_aggiornamento_sogei LIKE '%fooValue%'
     * </code>
     *
     * @param     string $dataAggiornamentoSogei The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataAggiornamentoSogei($dataAggiornamentoSogei = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($dataAggiornamentoSogei)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $dataAggiornamentoSogei)) {
                $dataAggiornamentoSogei = str_replace('*', '%', $dataAggiornamentoSogei);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_AGGIORNAMENTO_SOGEI, $dataAggiornamentoSogei, $comparison);
    }

    /**
     * Filter the query on the codice_alunno_ministeriale column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceAlunnoMinisteriale('fooValue');   // WHERE codice_alunno_ministeriale = 'fooValue'
     * $query->filterByCodiceAlunnoMinisteriale('%fooValue%'); // WHERE codice_alunno_ministeriale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceAlunnoMinisteriale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceAlunnoMinisteriale($codiceAlunnoMinisteriale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceAlunnoMinisteriale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceAlunnoMinisteriale)) {
                $codiceAlunnoMinisteriale = str_replace('*', '%', $codiceAlunnoMinisteriale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_ALUNNO_MINISTERIALE, $codiceAlunnoMinisteriale, $comparison);
    }

    /**
     * Filter the query on the flag_cf_fittizio column
     *
     * Example usage:
     * <code>
     * $query->filterByFlagCfFittizio(1234); // WHERE flag_cf_fittizio = 1234
     * $query->filterByFlagCfFittizio(array(12, 34)); // WHERE flag_cf_fittizio IN (12, 34)
     * $query->filterByFlagCfFittizio(array('min' => 12)); // WHERE flag_cf_fittizio >= 12
     * $query->filterByFlagCfFittizio(array('max' => 12)); // WHERE flag_cf_fittizio <= 12
     * </code>
     *
     * @param     mixed $flagCfFittizio The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFlagCfFittizio($flagCfFittizio = null, $comparison = null)
    {
        if (is_array($flagCfFittizio)) {
            $useMinMax = false;
            if (isset($flagCfFittizio['min'])) {
                $this->addUsingAlias(StudentiPeer::FLAG_CF_FITTIZIO, $flagCfFittizio['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($flagCfFittizio['max'])) {
                $this->addUsingAlias(StudentiPeer::FLAG_CF_FITTIZIO, $flagCfFittizio['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FLAG_CF_FITTIZIO, $flagCfFittizio, $comparison);
    }

    /**
     * Filter the query on the flag_s2f column
     *
     * Example usage:
     * <code>
     * $query->filterByFlagS2f('fooValue');   // WHERE flag_s2f = 'fooValue'
     * $query->filterByFlagS2f('%fooValue%'); // WHERE flag_s2f LIKE '%fooValue%'
     * </code>
     *
     * @param     string $flagS2f The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFlagS2f($flagS2f = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($flagS2f)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $flagS2f)) {
                $flagS2f = str_replace('*', '%', $flagS2f);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FLAG_S2F, $flagS2f, $comparison);
    }

    /**
     * Filter the query on the codice_stato_sogei column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceStatoSogei('fooValue');   // WHERE codice_stato_sogei = 'fooValue'
     * $query->filterByCodiceStatoSogei('%fooValue%'); // WHERE codice_stato_sogei LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceStatoSogei The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceStatoSogei($codiceStatoSogei = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceStatoSogei)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceStatoSogei)) {
                $codiceStatoSogei = str_replace('*', '%', $codiceStatoSogei);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_STATO_SOGEI, $codiceStatoSogei, $comparison);
    }

    /**
     * Filter the query on the codice_gruppo_nomade column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceGruppoNomade('fooValue');   // WHERE codice_gruppo_nomade = 'fooValue'
     * $query->filterByCodiceGruppoNomade('%fooValue%'); // WHERE codice_gruppo_nomade LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceGruppoNomade The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCodiceGruppoNomade($codiceGruppoNomade = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceGruppoNomade)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceGruppoNomade)) {
                $codiceGruppoNomade = str_replace('*', '%', $codiceGruppoNomade);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CODICE_GRUPPO_NOMADE, $codiceGruppoNomade, $comparison);
    }

    /**
     * Filter the query on the flag_minore_straniero column
     *
     * Example usage:
     * <code>
     * $query->filterByFlagMinoreStraniero(1234); // WHERE flag_minore_straniero = 1234
     * $query->filterByFlagMinoreStraniero(array(12, 34)); // WHERE flag_minore_straniero IN (12, 34)
     * $query->filterByFlagMinoreStraniero(array('min' => 12)); // WHERE flag_minore_straniero >= 12
     * $query->filterByFlagMinoreStraniero(array('max' => 12)); // WHERE flag_minore_straniero <= 12
     * </code>
     *
     * @param     mixed $flagMinoreStraniero The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFlagMinoreStraniero($flagMinoreStraniero = null, $comparison = null)
    {
        if (is_array($flagMinoreStraniero)) {
            $useMinMax = false;
            if (isset($flagMinoreStraniero['min'])) {
                $this->addUsingAlias(StudentiPeer::FLAG_MINORE_STRANIERO, $flagMinoreStraniero['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($flagMinoreStraniero['max'])) {
                $this->addUsingAlias(StudentiPeer::FLAG_MINORE_STRANIERO, $flagMinoreStraniero['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FLAG_MINORE_STRANIERO, $flagMinoreStraniero, $comparison);
    }

    /**
     * Filter the query on the chiave column
     *
     * Example usage:
     * <code>
     * $query->filterByChiave('fooValue');   // WHERE chiave = 'fooValue'
     * $query->filterByChiave('%fooValue%'); // WHERE chiave LIKE '%fooValue%'
     * </code>
     *
     * @param     string $chiave The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByChiave($chiave = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($chiave)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $chiave)) {
                $chiave = str_replace('*', '%', $chiave);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CHIAVE, $chiave, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_italiano column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieItaliano('fooValue');   // WHERE voto_esame_medie_italiano = 'fooValue'
     * $query->filterByVotoEsameMedieItaliano('%fooValue%'); // WHERE voto_esame_medie_italiano LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieItaliano The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieItaliano($votoEsameMedieItaliano = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieItaliano)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieItaliano)) {
                $votoEsameMedieItaliano = str_replace('*', '%', $votoEsameMedieItaliano);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_ITALIANO, $votoEsameMedieItaliano, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_inglese column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieInglese('fooValue');   // WHERE voto_esame_medie_inglese = 'fooValue'
     * $query->filterByVotoEsameMedieInglese('%fooValue%'); // WHERE voto_esame_medie_inglese LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieInglese The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieInglese($votoEsameMedieInglese = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieInglese)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieInglese)) {
                $votoEsameMedieInglese = str_replace('*', '%', $votoEsameMedieInglese);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_INGLESE, $votoEsameMedieInglese, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_matematica column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieMatematica('fooValue');   // WHERE voto_esame_medie_matematica = 'fooValue'
     * $query->filterByVotoEsameMedieMatematica('%fooValue%'); // WHERE voto_esame_medie_matematica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieMatematica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieMatematica($votoEsameMedieMatematica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieMatematica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieMatematica)) {
                $votoEsameMedieMatematica = str_replace('*', '%', $votoEsameMedieMatematica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_MATEMATICA, $votoEsameMedieMatematica, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_seconda_lingua column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieSecondaLingua('fooValue');   // WHERE voto_esame_medie_seconda_lingua = 'fooValue'
     * $query->filterByVotoEsameMedieSecondaLingua('%fooValue%'); // WHERE voto_esame_medie_seconda_lingua LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieSecondaLingua The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieSecondaLingua($votoEsameMedieSecondaLingua = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieSecondaLingua)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieSecondaLingua)) {
                $votoEsameMedieSecondaLingua = str_replace('*', '%', $votoEsameMedieSecondaLingua);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA, $votoEsameMedieSecondaLingua, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_invalsi_ita column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieInvalsiIta('fooValue');   // WHERE voto_esame_medie_invalsi_ita = 'fooValue'
     * $query->filterByVotoEsameMedieInvalsiIta('%fooValue%'); // WHERE voto_esame_medie_invalsi_ita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieInvalsiIta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieInvalsiIta($votoEsameMedieInvalsiIta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieInvalsiIta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieInvalsiIta)) {
                $votoEsameMedieInvalsiIta = str_replace('*', '%', $votoEsameMedieInvalsiIta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA, $votoEsameMedieInvalsiIta, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_invalsi_mat column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieInvalsiMat('fooValue');   // WHERE voto_esame_medie_invalsi_mat = 'fooValue'
     * $query->filterByVotoEsameMedieInvalsiMat('%fooValue%'); // WHERE voto_esame_medie_invalsi_mat LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieInvalsiMat The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieInvalsiMat($votoEsameMedieInvalsiMat = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieInvalsiMat)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieInvalsiMat)) {
                $votoEsameMedieInvalsiMat = str_replace('*', '%', $votoEsameMedieInvalsiMat);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT, $votoEsameMedieInvalsiMat, $comparison);
    }

    /**
     * Filter the query on the voto_esame_medie_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoEsameMedieOrale('fooValue');   // WHERE voto_esame_medie_orale = 'fooValue'
     * $query->filterByVotoEsameMedieOrale('%fooValue%'); // WHERE voto_esame_medie_orale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoEsameMedieOrale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoEsameMedieOrale($votoEsameMedieOrale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoEsameMedieOrale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoEsameMedieOrale)) {
                $votoEsameMedieOrale = str_replace('*', '%', $votoEsameMedieOrale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_ESAME_MEDIE_ORALE, $votoEsameMedieOrale, $comparison);
    }

    /**
     * Filter the query on the voto_ammissione_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByVotoAmmissioneMedie('fooValue');   // WHERE voto_ammissione_medie = 'fooValue'
     * $query->filterByVotoAmmissioneMedie('%fooValue%'); // WHERE voto_ammissione_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $votoAmmissioneMedie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByVotoAmmissioneMedie($votoAmmissioneMedie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($votoAmmissioneMedie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $votoAmmissioneMedie)) {
                $votoAmmissioneMedie = str_replace('*', '%', $votoAmmissioneMedie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::VOTO_AMMISSIONE_MEDIE, $votoAmmissioneMedie, $comparison);
    }

    /**
     * Filter the query on the esito_prima_elementare column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoPrimaElementare('fooValue');   // WHERE esito_prima_elementare = 'fooValue'
     * $query->filterByEsitoPrimaElementare('%fooValue%'); // WHERE esito_prima_elementare LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoPrimaElementare The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoPrimaElementare($esitoPrimaElementare = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoPrimaElementare)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoPrimaElementare)) {
                $esitoPrimaElementare = str_replace('*', '%', $esitoPrimaElementare);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_PRIMA_ELEMENTARE, $esitoPrimaElementare, $comparison);
    }

    /**
     * Filter the query on the esito_seconda_elementare column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoSecondaElementare('fooValue');   // WHERE esito_seconda_elementare = 'fooValue'
     * $query->filterByEsitoSecondaElementare('%fooValue%'); // WHERE esito_seconda_elementare LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoSecondaElementare The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoSecondaElementare($esitoSecondaElementare = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoSecondaElementare)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoSecondaElementare)) {
                $esitoSecondaElementare = str_replace('*', '%', $esitoSecondaElementare);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_SECONDA_ELEMENTARE, $esitoSecondaElementare, $comparison);
    }

    /**
     * Filter the query on the esito_terza_elementare column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoTerzaElementare('fooValue');   // WHERE esito_terza_elementare = 'fooValue'
     * $query->filterByEsitoTerzaElementare('%fooValue%'); // WHERE esito_terza_elementare LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoTerzaElementare The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoTerzaElementare($esitoTerzaElementare = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoTerzaElementare)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoTerzaElementare)) {
                $esitoTerzaElementare = str_replace('*', '%', $esitoTerzaElementare);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_TERZA_ELEMENTARE, $esitoTerzaElementare, $comparison);
    }

    /**
     * Filter the query on the esito_quarta_elementare column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoQuartaElementare('fooValue');   // WHERE esito_quarta_elementare = 'fooValue'
     * $query->filterByEsitoQuartaElementare('%fooValue%'); // WHERE esito_quarta_elementare LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoQuartaElementare The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoQuartaElementare($esitoQuartaElementare = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoQuartaElementare)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoQuartaElementare)) {
                $esitoQuartaElementare = str_replace('*', '%', $esitoQuartaElementare);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_QUARTA_ELEMENTARE, $esitoQuartaElementare, $comparison);
    }

    /**
     * Filter the query on the esito_quinta_elementare column
     *
     * Example usage:
     * <code>
     * $query->filterByEsitoQuintaElementare('fooValue');   // WHERE esito_quinta_elementare = 'fooValue'
     * $query->filterByEsitoQuintaElementare('%fooValue%'); // WHERE esito_quinta_elementare LIKE '%fooValue%'
     * </code>
     *
     * @param     string $esitoQuintaElementare The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByEsitoQuintaElementare($esitoQuintaElementare = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($esitoQuintaElementare)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $esitoQuintaElementare)) {
                $esitoQuintaElementare = str_replace('*', '%', $esitoQuintaElementare);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ESITO_QUINTA_ELEMENTARE, $esitoQuintaElementare, $comparison);
    }

    /**
     * Filter the query on the tipo_voto_esame_medie_italiano column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoVotoEsameMedieItaliano('fooValue');   // WHERE tipo_voto_esame_medie_italiano = 'fooValue'
     * $query->filterByTipoVotoEsameMedieItaliano('%fooValue%'); // WHERE tipo_voto_esame_medie_italiano LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoVotoEsameMedieItaliano The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoVotoEsameMedieItaliano($tipoVotoEsameMedieItaliano = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoVotoEsameMedieItaliano)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoVotoEsameMedieItaliano)) {
                $tipoVotoEsameMedieItaliano = str_replace('*', '%', $tipoVotoEsameMedieItaliano);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO, $tipoVotoEsameMedieItaliano, $comparison);
    }

    /**
     * Filter the query on the tipo_voto_esame_medie_inglese column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoVotoEsameMedieInglese('fooValue');   // WHERE tipo_voto_esame_medie_inglese = 'fooValue'
     * $query->filterByTipoVotoEsameMedieInglese('%fooValue%'); // WHERE tipo_voto_esame_medie_inglese LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoVotoEsameMedieInglese The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByTipoVotoEsameMedieInglese($tipoVotoEsameMedieInglese = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoVotoEsameMedieInglese)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoVotoEsameMedieInglese)) {
                $tipoVotoEsameMedieInglese = str_replace('*', '%', $tipoVotoEsameMedieInglese);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE, $tipoVotoEsameMedieInglese, $comparison);
    }

    /**
     * Filter the query on the giudizio_1_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizio1Medie('fooValue');   // WHERE giudizio_1_medie = 'fooValue'
     * $query->filterByGiudizio1Medie('%fooValue%'); // WHERE giudizio_1_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizio1Medie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizio1Medie($giudizio1Medie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizio1Medie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizio1Medie)) {
                $giudizio1Medie = str_replace('*', '%', $giudizio1Medie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_1_MEDIE, $giudizio1Medie, $comparison);
    }

    /**
     * Filter the query on the giudizio_2_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizio2Medie('fooValue');   // WHERE giudizio_2_medie = 'fooValue'
     * $query->filterByGiudizio2Medie('%fooValue%'); // WHERE giudizio_2_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizio2Medie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizio2Medie($giudizio2Medie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizio2Medie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizio2Medie)) {
                $giudizio2Medie = str_replace('*', '%', $giudizio2Medie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_2_MEDIE, $giudizio2Medie, $comparison);
    }

    /**
     * Filter the query on the giudizio_3_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizio3Medie('fooValue');   // WHERE giudizio_3_medie = 'fooValue'
     * $query->filterByGiudizio3Medie('%fooValue%'); // WHERE giudizio_3_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizio3Medie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizio3Medie($giudizio3Medie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizio3Medie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizio3Medie)) {
                $giudizio3Medie = str_replace('*', '%', $giudizio3Medie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_3_MEDIE, $giudizio3Medie, $comparison);
    }

    /**
     * Filter the query on the argomenti_orali_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByArgomentiOraliMedie('fooValue');   // WHERE argomenti_orali_medie = 'fooValue'
     * $query->filterByArgomentiOraliMedie('%fooValue%'); // WHERE argomenti_orali_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $argomentiOraliMedie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByArgomentiOraliMedie($argomentiOraliMedie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($argomentiOraliMedie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $argomentiOraliMedie)) {
                $argomentiOraliMedie = str_replace('*', '%', $argomentiOraliMedie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::ARGOMENTI_ORALI_MEDIE, $argomentiOraliMedie, $comparison);
    }

    /**
     * Filter the query on the giudizio_finale_1_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioFinale1Medie('fooValue');   // WHERE giudizio_finale_1_medie = 'fooValue'
     * $query->filterByGiudizioFinale1Medie('%fooValue%'); // WHERE giudizio_finale_1_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioFinale1Medie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioFinale1Medie($giudizioFinale1Medie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioFinale1Medie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioFinale1Medie)) {
                $giudizioFinale1Medie = str_replace('*', '%', $giudizioFinale1Medie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_FINALE_1_MEDIE, $giudizioFinale1Medie, $comparison);
    }

    /**
     * Filter the query on the giudizio_finale_2_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioFinale2Medie('fooValue');   // WHERE giudizio_finale_2_medie = 'fooValue'
     * $query->filterByGiudizioFinale2Medie('%fooValue%'); // WHERE giudizio_finale_2_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioFinale2Medie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioFinale2Medie($giudizioFinale2Medie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioFinale2Medie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioFinale2Medie)) {
                $giudizioFinale2Medie = str_replace('*', '%', $giudizioFinale2Medie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_FINALE_2_MEDIE, $giudizioFinale2Medie, $comparison);
    }

    /**
     * Filter the query on the giudizio_finale_3_medie column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioFinale3Medie('fooValue');   // WHERE giudizio_finale_3_medie = 'fooValue'
     * $query->filterByGiudizioFinale3Medie('%fooValue%'); // WHERE giudizio_finale_3_medie LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioFinale3Medie The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioFinale3Medie($giudizioFinale3Medie = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioFinale3Medie)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioFinale3Medie)) {
                $giudizioFinale3Medie = str_replace('*', '%', $giudizioFinale3Medie);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_FINALE_3_MEDIE, $giudizioFinale3Medie, $comparison);
    }

    /**
     * Filter the query on the consiglio_terza_media column
     *
     * Example usage:
     * <code>
     * $query->filterByConsiglioTerzaMedia('fooValue');   // WHERE consiglio_terza_media = 'fooValue'
     * $query->filterByConsiglioTerzaMedia('%fooValue%'); // WHERE consiglio_terza_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $consiglioTerzaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByConsiglioTerzaMedia($consiglioTerzaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($consiglioTerzaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $consiglioTerzaMedia)) {
                $consiglioTerzaMedia = str_replace('*', '%', $consiglioTerzaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CONSIGLIO_TERZA_MEDIA, $consiglioTerzaMedia, $comparison);
    }

    /**
     * Filter the query on the giudizio_sintetico_esame_terza_media column
     *
     * Example usage:
     * <code>
     * $query->filterByGiudizioSinteticoEsameTerzaMedia('fooValue');   // WHERE giudizio_sintetico_esame_terza_media = 'fooValue'
     * $query->filterByGiudizioSinteticoEsameTerzaMedia('%fooValue%'); // WHERE giudizio_sintetico_esame_terza_media LIKE '%fooValue%'
     * </code>
     *
     * @param     string $giudizioSinteticoEsameTerzaMedia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByGiudizioSinteticoEsameTerzaMedia($giudizioSinteticoEsameTerzaMedia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($giudizioSinteticoEsameTerzaMedia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $giudizioSinteticoEsameTerzaMedia)) {
                $giudizioSinteticoEsameTerzaMedia = str_replace('*', '%', $giudizioSinteticoEsameTerzaMedia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA, $giudizioSinteticoEsameTerzaMedia, $comparison);
    }

    /**
     * Filter the query on the data_arrivo_in_italia column
     *
     * Example usage:
     * <code>
     * $query->filterByDataArrivoInItalia(1234); // WHERE data_arrivo_in_italia = 1234
     * $query->filterByDataArrivoInItalia(array(12, 34)); // WHERE data_arrivo_in_italia IN (12, 34)
     * $query->filterByDataArrivoInItalia(array('min' => 12)); // WHERE data_arrivo_in_italia >= 12
     * $query->filterByDataArrivoInItalia(array('max' => 12)); // WHERE data_arrivo_in_italia <= 12
     * </code>
     *
     * @param     mixed $dataArrivoInItalia The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataArrivoInItalia($dataArrivoInItalia = null, $comparison = null)
    {
        if (is_array($dataArrivoInItalia)) {
            $useMinMax = false;
            if (isset($dataArrivoInItalia['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_ARRIVO_IN_ITALIA, $dataArrivoInItalia['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataArrivoInItalia['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_ARRIVO_IN_ITALIA, $dataArrivoInItalia['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_ARRIVO_IN_ITALIA, $dataArrivoInItalia, $comparison);
    }

    /**
     * Filter the query on the frequenza_asilo_nido column
     *
     * Example usage:
     * <code>
     * $query->filterByFrequenzaAsiloNido(1234); // WHERE frequenza_asilo_nido = 1234
     * $query->filterByFrequenzaAsiloNido(array(12, 34)); // WHERE frequenza_asilo_nido IN (12, 34)
     * $query->filterByFrequenzaAsiloNido(array('min' => 12)); // WHERE frequenza_asilo_nido >= 12
     * $query->filterByFrequenzaAsiloNido(array('max' => 12)); // WHERE frequenza_asilo_nido <= 12
     * </code>
     *
     * @param     mixed $frequenzaAsiloNido The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFrequenzaAsiloNido($frequenzaAsiloNido = null, $comparison = null)
    {
        if (is_array($frequenzaAsiloNido)) {
            $useMinMax = false;
            if (isset($frequenzaAsiloNido['min'])) {
                $this->addUsingAlias(StudentiPeer::FREQUENZA_ASILO_NIDO, $frequenzaAsiloNido['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($frequenzaAsiloNido['max'])) {
                $this->addUsingAlias(StudentiPeer::FREQUENZA_ASILO_NIDO, $frequenzaAsiloNido['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FREQUENZA_ASILO_NIDO, $frequenzaAsiloNido, $comparison);
    }

    /**
     * Filter the query on the frequenza_scuola_materna column
     *
     * Example usage:
     * <code>
     * $query->filterByFrequenzaScuolaMaterna(1234); // WHERE frequenza_scuola_materna = 1234
     * $query->filterByFrequenzaScuolaMaterna(array(12, 34)); // WHERE frequenza_scuola_materna IN (12, 34)
     * $query->filterByFrequenzaScuolaMaterna(array('min' => 12)); // WHERE frequenza_scuola_materna >= 12
     * $query->filterByFrequenzaScuolaMaterna(array('max' => 12)); // WHERE frequenza_scuola_materna <= 12
     * </code>
     *
     * @param     mixed $frequenzaScuolaMaterna The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByFrequenzaScuolaMaterna($frequenzaScuolaMaterna = null, $comparison = null)
    {
        if (is_array($frequenzaScuolaMaterna)) {
            $useMinMax = false;
            if (isset($frequenzaScuolaMaterna['min'])) {
                $this->addUsingAlias(StudentiPeer::FREQUENZA_SCUOLA_MATERNA, $frequenzaScuolaMaterna['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($frequenzaScuolaMaterna['max'])) {
                $this->addUsingAlias(StudentiPeer::FREQUENZA_SCUOLA_MATERNA, $frequenzaScuolaMaterna['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::FREQUENZA_SCUOLA_MATERNA, $frequenzaScuolaMaterna, $comparison);
    }

    /**
     * Filter the query on the data_aggiornamento_sidi column
     *
     * Example usage:
     * <code>
     * $query->filterByDataAggiornamentoSidi(1234); // WHERE data_aggiornamento_sidi = 1234
     * $query->filterByDataAggiornamentoSidi(array(12, 34)); // WHERE data_aggiornamento_sidi IN (12, 34)
     * $query->filterByDataAggiornamentoSidi(array('min' => 12)); // WHERE data_aggiornamento_sidi >= 12
     * $query->filterByDataAggiornamentoSidi(array('max' => 12)); // WHERE data_aggiornamento_sidi <= 12
     * </code>
     *
     * @param     mixed $dataAggiornamentoSidi The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByDataAggiornamentoSidi($dataAggiornamentoSidi = null, $comparison = null)
    {
        if (is_array($dataAggiornamentoSidi)) {
            $useMinMax = false;
            if (isset($dataAggiornamentoSidi['min'])) {
                $this->addUsingAlias(StudentiPeer::DATA_AGGIORNAMENTO_SIDI, $dataAggiornamentoSidi['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataAggiornamentoSidi['max'])) {
                $this->addUsingAlias(StudentiPeer::DATA_AGGIORNAMENTO_SIDI, $dataAggiornamentoSidi['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StudentiPeer::DATA_AGGIORNAMENTO_SIDI, $dataAggiornamentoSidi, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_val_ita column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupValIta('fooValue');   // WHERE cmp_sup_val_ita = 'fooValue'
     * $query->filterByCmpSupValIta('%fooValue%'); // WHERE cmp_sup_val_ita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupValIta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupValIta($cmpSupValIta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupValIta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupValIta)) {
                $cmpSupValIta = str_replace('*', '%', $cmpSupValIta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_VAL_ITA, $cmpSupValIta, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_txt_ita column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupTxtIta('fooValue');   // WHERE cmp_sup_txt_ita = 'fooValue'
     * $query->filterByCmpSupTxtIta('%fooValue%'); // WHERE cmp_sup_txt_ita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupTxtIta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupTxtIta($cmpSupTxtIta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupTxtIta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupTxtIta)) {
                $cmpSupTxtIta = str_replace('*', '%', $cmpSupTxtIta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_TXT_ITA, $cmpSupTxtIta, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_val_ing column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupValIng('fooValue');   // WHERE cmp_sup_val_ing = 'fooValue'
     * $query->filterByCmpSupValIng('%fooValue%'); // WHERE cmp_sup_val_ing LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupValIng The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupValIng($cmpSupValIng = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupValIng)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupValIng)) {
                $cmpSupValIng = str_replace('*', '%', $cmpSupValIng);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_VAL_ING, $cmpSupValIng, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_txt_ing column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupTxtIng('fooValue');   // WHERE cmp_sup_txt_ing = 'fooValue'
     * $query->filterByCmpSupTxtIng('%fooValue%'); // WHERE cmp_sup_txt_ing LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupTxtIng The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupTxtIng($cmpSupTxtIng = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupTxtIng)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupTxtIng)) {
                $cmpSupTxtIng = str_replace('*', '%', $cmpSupTxtIng);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_TXT_ING, $cmpSupTxtIng, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_val_altri column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupValAltri('fooValue');   // WHERE cmp_sup_val_altri = 'fooValue'
     * $query->filterByCmpSupValAltri('%fooValue%'); // WHERE cmp_sup_val_altri LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupValAltri The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupValAltri($cmpSupValAltri = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupValAltri)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupValAltri)) {
                $cmpSupValAltri = str_replace('*', '%', $cmpSupValAltri);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_VAL_ALTRI, $cmpSupValAltri, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_txt_altri column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupTxtAltri('fooValue');   // WHERE cmp_sup_txt_altri = 'fooValue'
     * $query->filterByCmpSupTxtAltri('%fooValue%'); // WHERE cmp_sup_txt_altri LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupTxtAltri The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupTxtAltri($cmpSupTxtAltri = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupTxtAltri)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupTxtAltri)) {
                $cmpSupTxtAltri = str_replace('*', '%', $cmpSupTxtAltri);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_TXT_ALTRI, $cmpSupTxtAltri, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_val_mat column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupValMat('fooValue');   // WHERE cmp_sup_val_mat = 'fooValue'
     * $query->filterByCmpSupValMat('%fooValue%'); // WHERE cmp_sup_val_mat LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupValMat The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupValMat($cmpSupValMat = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupValMat)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupValMat)) {
                $cmpSupValMat = str_replace('*', '%', $cmpSupValMat);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_VAL_MAT, $cmpSupValMat, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_txt_mat column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupTxtMat('fooValue');   // WHERE cmp_sup_txt_mat = 'fooValue'
     * $query->filterByCmpSupTxtMat('%fooValue%'); // WHERE cmp_sup_txt_mat LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupTxtMat The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupTxtMat($cmpSupTxtMat = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupTxtMat)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupTxtMat)) {
                $cmpSupTxtMat = str_replace('*', '%', $cmpSupTxtMat);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_TXT_MAT, $cmpSupTxtMat, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_val_sci_tec column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupValSciTec('fooValue');   // WHERE cmp_sup_val_sci_tec = 'fooValue'
     * $query->filterByCmpSupValSciTec('%fooValue%'); // WHERE cmp_sup_val_sci_tec LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupValSciTec The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupValSciTec($cmpSupValSciTec = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupValSciTec)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupValSciTec)) {
                $cmpSupValSciTec = str_replace('*', '%', $cmpSupValSciTec);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_VAL_SCI_TEC, $cmpSupValSciTec, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_txt_sci_tec column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupTxtSciTec('fooValue');   // WHERE cmp_sup_txt_sci_tec = 'fooValue'
     * $query->filterByCmpSupTxtSciTec('%fooValue%'); // WHERE cmp_sup_txt_sci_tec LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupTxtSciTec The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupTxtSciTec($cmpSupTxtSciTec = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupTxtSciTec)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupTxtSciTec)) {
                $cmpSupTxtSciTec = str_replace('*', '%', $cmpSupTxtSciTec);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_TXT_SCI_TEC, $cmpSupTxtSciTec, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_val_sto_soc column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupValStoSoc('fooValue');   // WHERE cmp_sup_val_sto_soc = 'fooValue'
     * $query->filterByCmpSupValStoSoc('%fooValue%'); // WHERE cmp_sup_val_sto_soc LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupValStoSoc The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupValStoSoc($cmpSupValStoSoc = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupValStoSoc)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupValStoSoc)) {
                $cmpSupValStoSoc = str_replace('*', '%', $cmpSupValStoSoc);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_VAL_STO_SOC, $cmpSupValStoSoc, $comparison);
    }

    /**
     * Filter the query on the cmp_sup_txt_sto_soc column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpSupTxtStoSoc('fooValue');   // WHERE cmp_sup_txt_sto_soc = 'fooValue'
     * $query->filterByCmpSupTxtStoSoc('%fooValue%'); // WHERE cmp_sup_txt_sto_soc LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpSupTxtStoSoc The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpSupTxtStoSoc($cmpSupTxtStoSoc = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpSupTxtStoSoc)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpSupTxtStoSoc)) {
                $cmpSupTxtStoSoc = str_replace('*', '%', $cmpSupTxtStoSoc);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_SUP_TXT_STO_SOC, $cmpSupTxtStoSoc, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_ita column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValIta('fooValue');   // WHERE cmp_med_val_ita = 'fooValue'
     * $query->filterByCmpMedValIta('%fooValue%'); // WHERE cmp_med_val_ita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValIta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValIta($cmpMedValIta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValIta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValIta)) {
                $cmpMedValIta = str_replace('*', '%', $cmpMedValIta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_ITA, $cmpMedValIta, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_ita column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtIta('fooValue');   // WHERE cmp_med_txt_ita = 'fooValue'
     * $query->filterByCmpMedTxtIta('%fooValue%'); // WHERE cmp_med_txt_ita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtIta The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtIta($cmpMedTxtIta = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtIta)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtIta)) {
                $cmpMedTxtIta = str_replace('*', '%', $cmpMedTxtIta);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_ITA, $cmpMedTxtIta, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_ing column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValIng('fooValue');   // WHERE cmp_med_val_ing = 'fooValue'
     * $query->filterByCmpMedValIng('%fooValue%'); // WHERE cmp_med_val_ing LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValIng The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValIng($cmpMedValIng = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValIng)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValIng)) {
                $cmpMedValIng = str_replace('*', '%', $cmpMedValIng);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_ING, $cmpMedValIng, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_ing column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtIng('fooValue');   // WHERE cmp_med_txt_ing = 'fooValue'
     * $query->filterByCmpMedTxtIng('%fooValue%'); // WHERE cmp_med_txt_ing LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtIng The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtIng($cmpMedTxtIng = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtIng)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtIng)) {
                $cmpMedTxtIng = str_replace('*', '%', $cmpMedTxtIng);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_ING, $cmpMedTxtIng, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_altri column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValAltri('fooValue');   // WHERE cmp_med_val_altri = 'fooValue'
     * $query->filterByCmpMedValAltri('%fooValue%'); // WHERE cmp_med_val_altri LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValAltri The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValAltri($cmpMedValAltri = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValAltri)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValAltri)) {
                $cmpMedValAltri = str_replace('*', '%', $cmpMedValAltri);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_ALTRI, $cmpMedValAltri, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_altri column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtAltri('fooValue');   // WHERE cmp_med_txt_altri = 'fooValue'
     * $query->filterByCmpMedTxtAltri('%fooValue%'); // WHERE cmp_med_txt_altri LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtAltri The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtAltri($cmpMedTxtAltri = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtAltri)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtAltri)) {
                $cmpMedTxtAltri = str_replace('*', '%', $cmpMedTxtAltri);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_ALTRI, $cmpMedTxtAltri, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_mat column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValMat('fooValue');   // WHERE cmp_med_val_mat = 'fooValue'
     * $query->filterByCmpMedValMat('%fooValue%'); // WHERE cmp_med_val_mat LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValMat The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValMat($cmpMedValMat = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValMat)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValMat)) {
                $cmpMedValMat = str_replace('*', '%', $cmpMedValMat);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_MAT, $cmpMedValMat, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_mat column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtMat('fooValue');   // WHERE cmp_med_txt_mat = 'fooValue'
     * $query->filterByCmpMedTxtMat('%fooValue%'); // WHERE cmp_med_txt_mat LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtMat The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtMat($cmpMedTxtMat = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtMat)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtMat)) {
                $cmpMedTxtMat = str_replace('*', '%', $cmpMedTxtMat);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_MAT, $cmpMedTxtMat, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_sci_tec column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValSciTec('fooValue');   // WHERE cmp_med_val_sci_tec = 'fooValue'
     * $query->filterByCmpMedValSciTec('%fooValue%'); // WHERE cmp_med_val_sci_tec LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValSciTec The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValSciTec($cmpMedValSciTec = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValSciTec)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValSciTec)) {
                $cmpMedValSciTec = str_replace('*', '%', $cmpMedValSciTec);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_SCI_TEC, $cmpMedValSciTec, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_sci_tec column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtSciTec('fooValue');   // WHERE cmp_med_txt_sci_tec = 'fooValue'
     * $query->filterByCmpMedTxtSciTec('%fooValue%'); // WHERE cmp_med_txt_sci_tec LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtSciTec The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtSciTec($cmpMedTxtSciTec = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtSciTec)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtSciTec)) {
                $cmpMedTxtSciTec = str_replace('*', '%', $cmpMedTxtSciTec);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_SCI_TEC, $cmpMedTxtSciTec, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_sto_soc column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValStoSoc('fooValue');   // WHERE cmp_med_val_sto_soc = 'fooValue'
     * $query->filterByCmpMedValStoSoc('%fooValue%'); // WHERE cmp_med_val_sto_soc LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValStoSoc The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValStoSoc($cmpMedValStoSoc = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValStoSoc)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValStoSoc)) {
                $cmpMedValStoSoc = str_replace('*', '%', $cmpMedValStoSoc);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_STO_SOC, $cmpMedValStoSoc, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_sto_soc column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtStoSoc('fooValue');   // WHERE cmp_med_txt_sto_soc = 'fooValue'
     * $query->filterByCmpMedTxtStoSoc('%fooValue%'); // WHERE cmp_med_txt_sto_soc LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtStoSoc The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtStoSoc($cmpMedTxtStoSoc = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtStoSoc)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtStoSoc)) {
                $cmpMedTxtStoSoc = str_replace('*', '%', $cmpMedTxtStoSoc);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_STO_SOC, $cmpMedTxtStoSoc, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_l2 column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValL2('fooValue');   // WHERE cmp_med_val_l2 = 'fooValue'
     * $query->filterByCmpMedValL2('%fooValue%'); // WHERE cmp_med_val_l2 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValL2 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValL2($cmpMedValL2 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValL2)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValL2)) {
                $cmpMedValL2 = str_replace('*', '%', $cmpMedValL2);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_L2, $cmpMedValL2, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_l2 column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtL2('fooValue');   // WHERE cmp_med_txt_l2 = 'fooValue'
     * $query->filterByCmpMedTxtL2('%fooValue%'); // WHERE cmp_med_txt_l2 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtL2 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtL2($cmpMedTxtL2 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtL2)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtL2)) {
                $cmpMedTxtL2 = str_replace('*', '%', $cmpMedTxtL2);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_L2, $cmpMedTxtL2, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_l3 column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValL3('fooValue');   // WHERE cmp_med_val_l3 = 'fooValue'
     * $query->filterByCmpMedValL3('%fooValue%'); // WHERE cmp_med_val_l3 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValL3 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValL3($cmpMedValL3 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValL3)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValL3)) {
                $cmpMedValL3 = str_replace('*', '%', $cmpMedValL3);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_L3, $cmpMedValL3, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_l3 column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtL3('fooValue');   // WHERE cmp_med_txt_l3 = 'fooValue'
     * $query->filterByCmpMedTxtL3('%fooValue%'); // WHERE cmp_med_txt_l3 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtL3 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtL3($cmpMedTxtL3 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtL3)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtL3)) {
                $cmpMedTxtL3 = str_replace('*', '%', $cmpMedTxtL3);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_L3, $cmpMedTxtL3, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_arte column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValArte('fooValue');   // WHERE cmp_med_val_arte = 'fooValue'
     * $query->filterByCmpMedValArte('%fooValue%'); // WHERE cmp_med_val_arte LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValArte The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValArte($cmpMedValArte = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValArte)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValArte)) {
                $cmpMedValArte = str_replace('*', '%', $cmpMedValArte);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_ARTE, $cmpMedValArte, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_arte column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtArte('fooValue');   // WHERE cmp_med_txt_arte = 'fooValue'
     * $query->filterByCmpMedTxtArte('%fooValue%'); // WHERE cmp_med_txt_arte LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtArte The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtArte($cmpMedTxtArte = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtArte)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtArte)) {
                $cmpMedTxtArte = str_replace('*', '%', $cmpMedTxtArte);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_ARTE, $cmpMedTxtArte, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_mus column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValMus('fooValue');   // WHERE cmp_med_val_mus = 'fooValue'
     * $query->filterByCmpMedValMus('%fooValue%'); // WHERE cmp_med_val_mus LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValMus The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValMus($cmpMedValMus = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValMus)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValMus)) {
                $cmpMedValMus = str_replace('*', '%', $cmpMedValMus);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_MUS, $cmpMedValMus, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_mus column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtMus('fooValue');   // WHERE cmp_med_txt_mus = 'fooValue'
     * $query->filterByCmpMedTxtMus('%fooValue%'); // WHERE cmp_med_txt_mus LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtMus The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtMus($cmpMedTxtMus = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtMus)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtMus)) {
                $cmpMedTxtMus = str_replace('*', '%', $cmpMedTxtMus);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_MUS, $cmpMedTxtMus, $comparison);
    }

    /**
     * Filter the query on the cmp_med_val_mot column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedValMot('fooValue');   // WHERE cmp_med_val_mot = 'fooValue'
     * $query->filterByCmpMedValMot('%fooValue%'); // WHERE cmp_med_val_mot LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedValMot The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedValMot($cmpMedValMot = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedValMot)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedValMot)) {
                $cmpMedValMot = str_replace('*', '%', $cmpMedValMot);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_VAL_MOT, $cmpMedValMot, $comparison);
    }

    /**
     * Filter the query on the cmp_med_txt_mot column
     *
     * Example usage:
     * <code>
     * $query->filterByCmpMedTxtMot('fooValue');   // WHERE cmp_med_txt_mot = 'fooValue'
     * $query->filterByCmpMedTxtMot('%fooValue%'); // WHERE cmp_med_txt_mot LIKE '%fooValue%'
     * </code>
     *
     * @param     string $cmpMedTxtMot The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function filterByCmpMedTxtMot($cmpMedTxtMot = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($cmpMedTxtMot)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $cmpMedTxtMot)) {
                $cmpMedTxtMot = str_replace('*', '%', $cmpMedTxtMot);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StudentiPeer::CMP_MED_TXT_MOT, $cmpMedTxtMot, $comparison);
    }

    /**
     * Filter the query by a related Tasse object
     *
     * @param   Tasse|PropelObjectCollection $tasse  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 StudentiQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTasse($tasse, $comparison = null)
    {
        if ($tasse instanceof Tasse) {
            return $this
                ->addUsingAlias(StudentiPeer::ID_STUDENTE, $tasse->getIdStudente(), $comparison);
        } elseif ($tasse instanceof PropelObjectCollection) {
            return $this
                ->useTasseQuery()
                ->filterByPrimaryKeys($tasse->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByTasse() only accepts arguments of type Tasse or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Tasse relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function joinTasse($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Tasse');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Tasse');
        }

        return $this;
    }

    /**
     * Use the Tasse relation Tasse object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Ccp\TasseQuery A secondary query class using the current class as primary query
     */
    public function useTasseQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinTasse($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Tasse', '\Ccp\TasseQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Studenti $studenti Object to remove from the list of results
     *
     * @return StudentiQuery The current query, for fluid interface
     */
    public function prune($studenti = null)
    {
        if ($studenti) {
            $this->addUsingAlias(StudentiPeer::ID_STUDENTE, $studenti->getIdStudente(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
