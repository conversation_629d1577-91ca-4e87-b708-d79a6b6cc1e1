<?php

namespace Ccp\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\StudentiCompleti;
use Ccp\StudentiCompletiPeer;
use Ccp\Tasse;
use Ccp\TasseQuery;

/**
 * Base class that represents a row from the 'studenti_completi' table.
 *
 *
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseStudentiCompleti extends BaseObject
{
    /**
     * Peer class name
     */
    const PEER = 'Ccp\\StudentiCompletiPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        StudentiCompletiPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id_studente field.
     * @var        int
     */
    protected $id_studente;

    /**
     * The value for the nome field.
     * Note: this column has a database default value of: 'STUDENTE IGNOTO'
     * @var        string
     */
    protected $nome;

    /**
     * The value for the cognome field.
     * Note: this column has a database default value of: 'STUDENTE IGNOTO'
     * @var        string
     */
    protected $cognome;

    /**
     * The value for the indirizzo field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $indirizzo;

    /**
     * The value for the citta field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $citta;

    /**
     * The value for the cap field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cap;

    /**
     * The value for the provincia field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $provincia;

    /**
     * The value for the sesso field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $sesso;

    /**
     * The value for the telefono field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $telefono;

    /**
     * The value for the cellulare1 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cellulare1;

    /**
     * The value for the cellulare2 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cellulare2;

    /**
     * The value for the email1 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $email1;

    /**
     * The value for the email2 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $email2;

    /**
     * The value for the invio_email field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $invio_email;

    /**
     * The value for the invio_email_cumulativo field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $invio_email_cumulativo;

    /**
     * The value for the invio_email_parametrico field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $invio_email_parametrico;

    /**
     * The value for the invio_email_temporale field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $invio_email_temporale;

    /**
     * The value for the tipo_sms field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $tipo_sms;

    /**
     * The value for the tipo_sms_cumulativo field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $tipo_sms_cumulativo;

    /**
     * The value for the tipo_sms_parametrico field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $tipo_sms_parametrico;

    /**
     * The value for the tipo_sms_temporale field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $tipo_sms_temporale;

    /**
     * The value for the aut_entrata_ritardo field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $aut_entrata_ritardo;

    /**
     * The value for the aut_uscita_anticipo field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $aut_uscita_anticipo;

    /**
     * The value for the aut_pomeriggio field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $aut_pomeriggio;

    /**
     * The value for the acconsente field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $acconsente;

    /**
     * The value for the ritirato field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $ritirato;

    /**
     * The value for the data_nascita field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $data_nascita;

    /**
     * The value for the codice_studente field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_studente;

    /**
     * The value for the password_studente field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $password_studente;

    /**
     * The value for the codice_giustificazioni_studente field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $codice_giustificazioni_studente;

    /**
     * The value for the esonero_religione field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $esonero_religione;

    /**
     * The value for the materia_sostitutiva_religione field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $materia_sostitutiva_religione;

    /**
     * The value for the esonero_ed_fisica field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $esonero_ed_fisica;

    /**
     * The value for the materia_sostitutiva_edfisica field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $materia_sostitutiva_edfisica;

    /**
     * The value for the crediti_terza field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_terza;

    /**
     * The value for the media_voti_terza field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $media_voti_terza;

    /**
     * The value for the debiti_terza field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $debiti_terza;

    /**
     * The value for the crediti_sospesi_terza field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_sospesi_terza;

    /**
     * The value for the crediti_reintegrati_terza field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_reintegrati_terza;

    /**
     * The value for the crediti_quarta field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_quarta;

    /**
     * The value for the media_voti_quarta field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $media_voti_quarta;

    /**
     * The value for the debiti_quarta field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $debiti_quarta;

    /**
     * The value for the crediti_sospesi_quarta field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_sospesi_quarta;

    /**
     * The value for the crediti_reintegrati_quarta field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_reintegrati_quarta;

    /**
     * The value for the crediti_quinta field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_quinta;

    /**
     * The value for the media_voti_quinta field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $media_voti_quinta;

    /**
     * The value for the crediti_finali_agg field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $crediti_finali_agg;

    /**
     * The value for the matricola field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $matricola;

    /**
     * The value for the luogo_nascita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $luogo_nascita;

    /**
     * The value for the provincia_nascita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $provincia_nascita;

    /**
     * The value for the motivi_crediti_terza field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $motivi_crediti_terza;

    /**
     * The value for the motivi_crediti_quarta field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $motivi_crediti_quarta;

    /**
     * The value for the motivi_crediti_quinta field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $motivi_crediti_quinta;

    /**
     * The value for the motivi_crediti_agg field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $motivi_crediti_agg;

    /**
     * The value for the codice_comune_nascita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_comune_nascita;

    /**
     * The value for the stato_nascita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $stato_nascita;

    /**
     * The value for the cittadinanza field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $cittadinanza;

    /**
     * The value for the seconda_cittadinanza field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $seconda_cittadinanza;

    /**
     * The value for the codice_comune_residenza field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_comune_residenza;

    /**
     * The value for the distretto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $distretto;

    /**
     * The value for the codice_fiscale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_fiscale;

    /**
     * The value for the medico field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $medico;

    /**
     * The value for the telefono_medico field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $telefono_medico;

    /**
     * The value for the intolleranze_alim field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $intolleranze_alim;

    /**
     * The value for the gruppo_sanguigno field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $gruppo_sanguigno;

    /**
     * The value for the gruppo_rh field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $gruppo_rh;

    /**
     * The value for the codice_asl field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_asl;

    /**
     * The value for the annotazioni field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $annotazioni;

    /**
     * The value for the stato_civile field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $stato_civile;

    /**
     * The value for the voto_primo_scritto field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $voto_primo_scritto;

    /**
     * The value for the voto_secondo_scritto field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $voto_secondo_scritto;

    /**
     * The value for the voto_terzo_scritto field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $voto_terzo_scritto;

    /**
     * The value for the voto_orale field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $voto_orale;

    /**
     * The value for the voto_bonus field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $voto_bonus;

    /**
     * The value for the materia_secondo_scr field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $materia_secondo_scr;

    /**
     * The value for the ulteriori_specif_diploma field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $ulteriori_specif_diploma;

    /**
     * The value for the numero_diploma field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $numero_diploma;

    /**
     * The value for the chi_inserisce field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $chi_inserisce;

    /**
     * The value for the data_inserimento field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_inserimento;

    /**
     * The value for the tipo_inserimento field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_inserimento;

    /**
     * The value for the chi_modifica field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $chi_modifica;

    /**
     * The value for the data_modifica field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_modifica;

    /**
     * The value for the tipo_modifica field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_modifica;

    /**
     * The value for the flag_canc field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $flag_canc;

    /**
     * The value for the stato_avanzamento field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $stato_avanzamento;

    /**
     * The value for the data_stato_avanzamento field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_stato_avanzamento;

    /**
     * The value for the cap_provincia_nascita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cap_provincia_nascita;

    /**
     * The value for the badge field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $badge;

    /**
     * The value for the cap_residenza field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cap_residenza;

    /**
     * The value for the codice_comune_domicilio field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_comune_domicilio;

    /**
     * The value for the cap_domicilio field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cap_domicilio;

    /**
     * The value for the cap_nascita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cap_nascita;

    /**
     * The value for the indirizzo_domicilio field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $indirizzo_domicilio;

    /**
     * The value for the citta_nascita_straniera field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $citta_nascita_straniera;

    /**
     * The value for the cellulare_allievo field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cellulare_allievo;

    /**
     * The value for the handicap field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $handicap;

    /**
     * The value for the stato_convittore field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $stato_convittore;

    /**
     * The value for the data_ritiro field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_ritiro;

    /**
     * The value for the voto_ammissione field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $voto_ammissione;

    /**
     * The value for the differenza_punteggio field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $differenza_punteggio;

    /**
     * The value for the voto_qualifica field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $voto_qualifica;

    /**
     * The value for the voto_esame_sc1_qual field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $voto_esame_sc1_qual;

    /**
     * The value for the voto_esame_sc2_qual field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $voto_esame_sc2_qual;

    /**
     * The value for the voto_esame_or_qual field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $voto_esame_or_qual;

    /**
     * The value for the stato_privatista field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $stato_privatista;

    /**
     * The value for the foto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $foto;

    /**
     * The value for the rappresentante field.
     * Note: this column has a database default value of: 'NO#NO#NO'
     * @var        string
     */
    protected $rappresentante;

    /**
     * The value for the obbligo_formativo field.
     * Note: this column has a database default value of: '01'
     * @var        string
     */
    protected $obbligo_formativo;

    /**
     * The value for the id_lingua_1 field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $id_lingua_1;

    /**
     * The value for the id_lingua_2 field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $id_lingua_2;

    /**
     * The value for the id_lingua_3 field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $id_lingua_3;

    /**
     * The value for the id_lingua_4 field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $id_lingua_4;

    /**
     * The value for the id_lingua_5 field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $id_lingua_5;

    /**
     * The value for the id_provenienza_scolastica field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $id_provenienza_scolastica;

    /**
     * The value for the id_scuola_media field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $id_scuola_media;

    /**
     * The value for the lingua_scuola_media field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $lingua_scuola_media;

    /**
     * The value for the lingua_scuola_media_2 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $lingua_scuola_media_2;

    /**
     * The value for the giudizio_scuola_media field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_scuola_media;

    /**
     * The value for the trasporto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $trasporto;

    /**
     * The value for the data_iscrizione field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_iscrizione;

    /**
     * The value for the pei field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $pei;

    /**
     * The value for the ammesso_esame_qualifica field.
     * Note: this column has a database default value of: '--'
     * @var        string
     */
    protected $ammesso_esame_qualifica;

    /**
     * The value for the ammesso_esame_quinta field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $ammesso_esame_quinta;

    /**
     * The value for the giudizio_ammissione_quinta field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_ammissione_quinta;

    /**
     * The value for the grado_handicap field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $grado_handicap;

    /**
     * The value for the tipo_handicap field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_handicap;

    /**
     * The value for the stato_licenza_maestro field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $stato_licenza_maestro;

    /**
     * The value for the id_studente_sissi field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $id_studente_sissi;

    /**
     * The value for the badge_rfid field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $badge_rfid;

    /**
     * The value for the lode field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $lode;

    /**
     * The value for the distretto_scolastico field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $distretto_scolastico;

    /**
     * The value for the giudizio_ammissione_terza field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_ammissione_terza;

    /**
     * The value for the esito_prima_media field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $esito_prima_media;

    /**
     * The value for the esito_seconda_media field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $esito_seconda_media;

    /**
     * The value for the esito_terza_media field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $esito_terza_media;

    /**
     * The value for the giudizio_esame_sc1_qual field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_esame_sc1_qual;

    /**
     * The value for the giudizio_esame_sc2_qual field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_esame_sc2_qual;

    /**
     * The value for the giudizio_esame_or_qual field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_esame_or_qual;

    /**
     * The value for the giudizio_complessivo_esame_qual field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_complessivo_esame_qual;

    /**
     * The value for the acconsente_aziende field.
     * Note: this column has a database default value of: 1
     * @var        int
     */
    protected $acconsente_aziende;

    /**
     * The value for the curriculum_prima field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $curriculum_prima;

    /**
     * The value for the curriculum_seconda field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $curriculum_seconda;

    /**
     * The value for the stage_professionali field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $stage_professionali;

    /**
     * The value for the data_orale field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_orale;

    /**
     * The value for the ordine_esame_orale field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $ordine_esame_orale;

    /**
     * The value for the tipo_primo_scritto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_primo_scritto;

    /**
     * The value for the tipo_secondo_scritto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_secondo_scritto;

    /**
     * The value for the tipo_terzo_scritto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_terzo_scritto;

    /**
     * The value for the unanimita_primo_scritto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $unanimita_primo_scritto;

    /**
     * The value for the unanimita_secondo_scritto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $unanimita_secondo_scritto;

    /**
     * The value for the unanimita_terzo_scritto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $unanimita_terzo_scritto;

    /**
     * The value for the argomento_scelto_orale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $argomento_scelto_orale;

    /**
     * The value for the area_disc_1_orale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $area_disc_1_orale;

    /**
     * The value for the area_disc_2_orale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $area_disc_2_orale;

    /**
     * The value for the disc_elaborati_orale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $disc_elaborati_orale;

    /**
     * The value for the unanimita_voto_finale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $unanimita_voto_finale;

    /**
     * The value for the presente_esame_quinta field.
     * Note: this column has a database default value of: 'SI'
     * @var        string
     */
    protected $presente_esame_quinta;

    /**
     * The value for the stampa_badge field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $stampa_badge;

    /**
     * The value for the id_classe_destinazione field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $id_classe_destinazione;

    /**
     * The value for the sconto_rette field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $sconto_rette;

    /**
     * The value for the carta_studente_numero field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $carta_studente_numero;

    /**
     * The value for the carta_studente_scadenza field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $carta_studente_scadenza;

    /**
     * The value for the esito_corrente_calcolato field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $esito_corrente_calcolato;

    /**
     * The value for the id_flusso field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $id_flusso;

    /**
     * The value for the data_aggiornamento_sogei field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $data_aggiornamento_sogei;

    /**
     * The value for the codice_alunno_ministeriale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_alunno_ministeriale;

    /**
     * The value for the flag_cf_fittizio field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $flag_cf_fittizio;

    /**
     * The value for the flag_s2f field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $flag_s2f;

    /**
     * The value for the codice_stato_sogei field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_stato_sogei;

    /**
     * The value for the codice_gruppo_nomade field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_gruppo_nomade;

    /**
     * The value for the flag_minore_straniero field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $flag_minore_straniero;

    /**
     * The value for the chiave field.
     * @var        string
     */
    protected $chiave;

    /**
     * The value for the voto_esame_medie_italiano field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_italiano;

    /**
     * The value for the voto_esame_medie_inglese field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_inglese;

    /**
     * The value for the voto_esame_medie_matematica field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_matematica;

    /**
     * The value for the voto_esame_medie_seconda_lingua field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_seconda_lingua;

    /**
     * The value for the voto_esame_medie_invalsi_ita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_invalsi_ita;

    /**
     * The value for the voto_esame_medie_invalsi_mat field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_invalsi_mat;

    /**
     * The value for the voto_esame_medie_orale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_esame_medie_orale;

    /**
     * The value for the voto_ammissione_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $voto_ammissione_medie;

    /**
     * The value for the esito_prima_elementare field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $esito_prima_elementare;

    /**
     * The value for the esito_seconda_elementare field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $esito_seconda_elementare;

    /**
     * The value for the esito_terza_elementare field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $esito_terza_elementare;

    /**
     * The value for the esito_quarta_elementare field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $esito_quarta_elementare;

    /**
     * The value for the esito_quinta_elementare field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $esito_quinta_elementare;

    /**
     * The value for the tipo_voto_esame_medie_italiano field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_voto_esame_medie_italiano;

    /**
     * The value for the tipo_voto_esame_medie_inglese field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_voto_esame_medie_inglese;

    /**
     * The value for the giudizio_1_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_1_medie;

    /**
     * The value for the giudizio_2_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_2_medie;

    /**
     * The value for the giudizio_3_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_3_medie;

    /**
     * The value for the argomenti_orali_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $argomenti_orali_medie;

    /**
     * The value for the giudizio_finale_1_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_finale_1_medie;

    /**
     * The value for the giudizio_finale_2_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_finale_2_medie;

    /**
     * The value for the giudizio_finale_3_medie field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_finale_3_medie;

    /**
     * The value for the consiglio_terza_media field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $consiglio_terza_media;

    /**
     * The value for the giudizio_sintetico_esame_terza_media field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $giudizio_sintetico_esame_terza_media;

    /**
     * The value for the data_arrivo_in_italia field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $data_arrivo_in_italia;

    /**
     * The value for the frequenza_asilo_nido field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $frequenza_asilo_nido;

    /**
     * The value for the frequenza_scuola_materna field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $frequenza_scuola_materna;

    /**
     * The value for the data_aggiornamento_sidi field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $data_aggiornamento_sidi;

    /**
     * The value for the cmp_sup_val_ita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_val_ita;

    /**
     * The value for the cmp_sup_txt_ita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_txt_ita;

    /**
     * The value for the cmp_sup_val_ing field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_val_ing;

    /**
     * The value for the cmp_sup_txt_ing field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_txt_ing;

    /**
     * The value for the cmp_sup_val_altri field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_val_altri;

    /**
     * The value for the cmp_sup_txt_altri field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_txt_altri;

    /**
     * The value for the cmp_sup_val_mat field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_val_mat;

    /**
     * The value for the cmp_sup_txt_mat field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_txt_mat;

    /**
     * The value for the cmp_sup_val_sci_tec field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_val_sci_tec;

    /**
     * The value for the cmp_sup_txt_sci_tec field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_txt_sci_tec;

    /**
     * The value for the cmp_sup_val_sto_soc field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_val_sto_soc;

    /**
     * The value for the cmp_sup_txt_sto_soc field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_sup_txt_sto_soc;

    /**
     * The value for the cmp_med_val_ita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_ita;

    /**
     * The value for the cmp_med_txt_ita field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_ita;

    /**
     * The value for the cmp_med_val_ing field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_ing;

    /**
     * The value for the cmp_med_txt_ing field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_ing;

    /**
     * The value for the cmp_med_val_altri field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_altri;

    /**
     * The value for the cmp_med_txt_altri field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_altri;

    /**
     * The value for the cmp_med_val_mat field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_mat;

    /**
     * The value for the cmp_med_txt_mat field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_mat;

    /**
     * The value for the cmp_med_val_sci_tec field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_sci_tec;

    /**
     * The value for the cmp_med_txt_sci_tec field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_sci_tec;

    /**
     * The value for the cmp_med_val_sto_soc field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_sto_soc;

    /**
     * The value for the cmp_med_txt_sto_soc field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_sto_soc;

    /**
     * The value for the cmp_med_val_l2 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_l2;

    /**
     * The value for the cmp_med_txt_l2 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_l2;

    /**
     * The value for the cmp_med_val_l3 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_l3;

    /**
     * The value for the cmp_med_txt_l3 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_l3;

    /**
     * The value for the cmp_med_val_arte field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_arte;

    /**
     * The value for the cmp_med_txt_arte field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_arte;

    /**
     * The value for the cmp_med_val_mus field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_mus;

    /**
     * The value for the cmp_med_txt_mus field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_mus;

    /**
     * The value for the cmp_med_val_mot field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_val_mot;

    /**
     * The value for the cmp_med_txt_mot field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $cmp_med_txt_mot;

    /**
     * @var        PropelObjectCollection|Tasse[] Collection to store aggregation of Tasse objects.
     */
    protected $collTasses;
    protected $collTassesPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $tassesScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->nome = 'STUDENTE IGNOTO';
        $this->cognome = 'STUDENTE IGNOTO';
        $this->indirizzo = '';
        $this->citta = '';
        $this->cap = '';
        $this->provincia = '';
        $this->sesso = '';
        $this->telefono = '';
        $this->cellulare1 = '';
        $this->cellulare2 = '';
        $this->email1 = '';
        $this->email2 = '';
        $this->invio_email = '0';
        $this->invio_email_cumulativo = '0';
        $this->invio_email_parametrico = '0';
        $this->invio_email_temporale = '0';
        $this->tipo_sms = '0';
        $this->tipo_sms_cumulativo = '0';
        $this->tipo_sms_parametrico = '0';
        $this->tipo_sms_temporale = '0';
        $this->aut_entrata_ritardo = '0';
        $this->aut_uscita_anticipo = '0';
        $this->aut_pomeriggio = '0';
        $this->acconsente = '0';
        $this->ritirato = '0';
        $this->data_nascita = 0;
        $this->codice_studente = '';
        $this->password_studente = '';
        $this->codice_giustificazioni_studente = '0';
        $this->esonero_religione = '0';
        $this->materia_sostitutiva_religione = 0;
        $this->esonero_ed_fisica = '0';
        $this->materia_sostitutiva_edfisica = 0;
        $this->crediti_terza = 0;
        $this->media_voti_terza = '0';
        $this->debiti_terza = '0';
        $this->crediti_sospesi_terza = 0;
        $this->crediti_reintegrati_terza = 0;
        $this->crediti_quarta = 0;
        $this->media_voti_quarta = '0';
        $this->debiti_quarta = '0';
        $this->crediti_sospesi_quarta = 0;
        $this->crediti_reintegrati_quarta = 0;
        $this->crediti_quinta = 0;
        $this->media_voti_quinta = '0';
        $this->crediti_finali_agg = 0;
        $this->matricola = '0';
        $this->luogo_nascita = '';
        $this->provincia_nascita = '';
        $this->motivi_crediti_terza = '';
        $this->motivi_crediti_quarta = '';
        $this->motivi_crediti_quinta = '';
        $this->motivi_crediti_agg = '';
        $this->codice_comune_nascita = '';
        $this->stato_nascita = '';
        $this->cittadinanza = '-1';
        $this->seconda_cittadinanza = '-1';
        $this->codice_comune_residenza = '';
        $this->distretto = '';
        $this->codice_fiscale = '';
        $this->medico = '';
        $this->telefono_medico = '';
        $this->intolleranze_alim = '';
        $this->gruppo_sanguigno = '';
        $this->gruppo_rh = '';
        $this->codice_asl = '';
        $this->annotazioni = '';
        $this->stato_civile = 0;
        $this->voto_primo_scritto = 0;
        $this->voto_secondo_scritto = 0;
        $this->voto_terzo_scritto = 0;
        $this->voto_orale = 0;
        $this->voto_bonus = 0;
        $this->materia_secondo_scr = '';
        $this->ulteriori_specif_diploma = '';
        $this->numero_diploma = 0;
        $this->chi_inserisce = '(-1)';
        $this->data_inserimento = '0';
        $this->tipo_inserimento = '';
        $this->chi_modifica = '(-1)';
        $this->data_modifica = '0';
        $this->tipo_modifica = '';
        $this->flag_canc = '0';
        $this->stato_avanzamento = '';
        $this->data_stato_avanzamento = '0';
        $this->cap_provincia_nascita = '';
        $this->badge = '0';
        $this->cap_residenza = '';
        $this->codice_comune_domicilio = '';
        $this->cap_domicilio = '';
        $this->cap_nascita = '';
        $this->indirizzo_domicilio = '';
        $this->citta_nascita_straniera = '';
        $this->cellulare_allievo = '';
        $this->handicap = 'NO';
        $this->stato_convittore = 'NO';
        $this->data_ritiro = '0';
        $this->voto_ammissione = '0';
        $this->differenza_punteggio = '0';
        $this->voto_qualifica = '0';
        $this->voto_esame_sc1_qual = '0';
        $this->voto_esame_sc2_qual = '0';
        $this->voto_esame_or_qual = '0';
        $this->stato_privatista = 'NO';
        $this->foto = '';
        $this->rappresentante = 'NO#NO#NO';
        $this->obbligo_formativo = '01';
        $this->id_lingua_1 = '(-1)';
        $this->id_lingua_2 = '(-1)';
        $this->id_lingua_3 = '(-1)';
        $this->id_lingua_4 = '(-1)';
        $this->id_lingua_5 = '(-1)';
        $this->id_provenienza_scolastica = '';
        $this->id_scuola_media = '';
        $this->lingua_scuola_media = '';
        $this->lingua_scuola_media_2 = '';
        $this->giudizio_scuola_media = '';
        $this->trasporto = '';
        $this->data_iscrizione = '0';
        $this->pei = 'NO';
        $this->ammesso_esame_qualifica = '--';
        $this->ammesso_esame_quinta = 'NO';
        $this->giudizio_ammissione_quinta = '';
        $this->grado_handicap = '0';
        $this->tipo_handicap = '';
        $this->stato_licenza_maestro = 'NO';
        $this->id_studente_sissi = '';
        $this->badge_rfid = '';
        $this->lode = 'NO';
        $this->distretto_scolastico = '';
        $this->giudizio_ammissione_terza = '';
        $this->esito_prima_media = 'NO';
        $this->esito_seconda_media = 'NO';
        $this->esito_terza_media = 'NO';
        $this->giudizio_esame_sc1_qual = '';
        $this->giudizio_esame_sc2_qual = '';
        $this->giudizio_esame_or_qual = '';
        $this->giudizio_complessivo_esame_qual = '';
        $this->acconsente_aziende = 1;
        $this->curriculum_prima = '0';
        $this->curriculum_seconda = '0';
        $this->stage_professionali = '0';
        $this->data_orale = '0';
        $this->ordine_esame_orale = '0';
        $this->tipo_primo_scritto = '';
        $this->tipo_secondo_scritto = '';
        $this->tipo_terzo_scritto = '';
        $this->unanimita_primo_scritto = '';
        $this->unanimita_secondo_scritto = '';
        $this->unanimita_terzo_scritto = '';
        $this->argomento_scelto_orale = '';
        $this->area_disc_1_orale = '';
        $this->area_disc_2_orale = '';
        $this->disc_elaborati_orale = '';
        $this->unanimita_voto_finale = '';
        $this->presente_esame_quinta = 'SI';
        $this->stampa_badge = 'NO';
        $this->id_classe_destinazione = 0;
        $this->sconto_rette = 0;
        $this->carta_studente_numero = '0';
        $this->carta_studente_scadenza = 0;
        $this->esito_corrente_calcolato = '';
        $this->id_flusso = '0';
        $this->data_aggiornamento_sogei = '';
        $this->codice_alunno_ministeriale = '';
        $this->flag_cf_fittizio = 0;
        $this->flag_s2f = '';
        $this->codice_stato_sogei = '';
        $this->codice_gruppo_nomade = '';
        $this->flag_minore_straniero = 0;
        $this->voto_esame_medie_italiano = '';
        $this->voto_esame_medie_inglese = '';
        $this->voto_esame_medie_matematica = '';
        $this->voto_esame_medie_seconda_lingua = '';
        $this->voto_esame_medie_invalsi_ita = '';
        $this->voto_esame_medie_invalsi_mat = '';
        $this->voto_esame_medie_orale = '';
        $this->voto_ammissione_medie = '';
        $this->esito_prima_elementare = '';
        $this->esito_seconda_elementare = '';
        $this->esito_terza_elementare = '';
        $this->esito_quarta_elementare = '';
        $this->esito_quinta_elementare = '';
        $this->tipo_voto_esame_medie_italiano = '';
        $this->tipo_voto_esame_medie_inglese = '';
        $this->giudizio_1_medie = '';
        $this->giudizio_2_medie = '';
        $this->giudizio_3_medie = '';
        $this->argomenti_orali_medie = '';
        $this->giudizio_finale_1_medie = '';
        $this->giudizio_finale_2_medie = '';
        $this->giudizio_finale_3_medie = '';
        $this->consiglio_terza_media = '';
        $this->giudizio_sintetico_esame_terza_media = '';
        $this->data_arrivo_in_italia = 0;
        $this->frequenza_asilo_nido = 0;
        $this->frequenza_scuola_materna = 0;
        $this->data_aggiornamento_sidi = 0;
        $this->cmp_sup_val_ita = '';
        $this->cmp_sup_txt_ita = '';
        $this->cmp_sup_val_ing = '';
        $this->cmp_sup_txt_ing = '';
        $this->cmp_sup_val_altri = '';
        $this->cmp_sup_txt_altri = '';
        $this->cmp_sup_val_mat = '';
        $this->cmp_sup_txt_mat = '';
        $this->cmp_sup_val_sci_tec = '';
        $this->cmp_sup_txt_sci_tec = '';
        $this->cmp_sup_val_sto_soc = '';
        $this->cmp_sup_txt_sto_soc = '';
        $this->cmp_med_val_ita = '';
        $this->cmp_med_txt_ita = '';
        $this->cmp_med_val_ing = '';
        $this->cmp_med_txt_ing = '';
        $this->cmp_med_val_altri = '';
        $this->cmp_med_txt_altri = '';
        $this->cmp_med_val_mat = '';
        $this->cmp_med_txt_mat = '';
        $this->cmp_med_val_sci_tec = '';
        $this->cmp_med_txt_sci_tec = '';
        $this->cmp_med_val_sto_soc = '';
        $this->cmp_med_txt_sto_soc = '';
        $this->cmp_med_val_l2 = '';
        $this->cmp_med_txt_l2 = '';
        $this->cmp_med_val_l3 = '';
        $this->cmp_med_txt_l3 = '';
        $this->cmp_med_val_arte = '';
        $this->cmp_med_txt_arte = '';
        $this->cmp_med_val_mus = '';
        $this->cmp_med_txt_mus = '';
        $this->cmp_med_val_mot = '';
        $this->cmp_med_txt_mot = '';
    }

    /**
     * Initializes internal state of BaseStudentiCompleti object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id_studente] column value.
     *
     * @return int
     */
    public function getIdStudente()
    {

        return $this->id_studente;
    }

    /**
     * Get the [nome] column value.
     *
     * @return string
     */
    public function getNome()
    {

        return $this->nome;
    }

    /**
     * Get the [cognome] column value.
     *
     * @return string
     */
    public function getCognome()
    {

        return $this->cognome;
    }

    /**
     * Get the [indirizzo] column value.
     *
     * @return string
     */
    public function getIndirizzo()
    {

        return $this->indirizzo;
    }

    /**
     * Get the [citta] column value.
     *
     * @return string
     */
    public function getCitta()
    {

        return $this->citta;
    }

    /**
     * Get the [cap] column value.
     *
     * @return string
     */
    public function getCap()
    {

        return $this->cap;
    }

    /**
     * Get the [provincia] column value.
     *
     * @return string
     */
    public function getProvincia()
    {

        return $this->provincia;
    }

    /**
     * Get the [sesso] column value.
     *
     * @return string
     */
    public function getSesso()
    {

        return $this->sesso;
    }

    /**
     * Get the [telefono] column value.
     *
     * @return string
     */
    public function getTelefono()
    {

        return $this->telefono;
    }

    /**
     * Get the [cellulare1] column value.
     *
     * @return string
     */
    public function getCellulare1()
    {

        return $this->cellulare1;
    }

    /**
     * Get the [cellulare2] column value.
     *
     * @return string
     */
    public function getCellulare2()
    {

        return $this->cellulare2;
    }

    /**
     * Get the [email1] column value.
     *
     * @return string
     */
    public function getEmail1()
    {

        return $this->email1;
    }

    /**
     * Get the [email2] column value.
     *
     * @return string
     */
    public function getEmail2()
    {

        return $this->email2;
    }

    /**
     * Get the [invio_email] column value.
     *
     * @return string
     */
    public function getInvioEmail()
    {

        return $this->invio_email;
    }

    /**
     * Get the [invio_email_cumulativo] column value.
     *
     * @return string
     */
    public function getInvioEmailCumulativo()
    {

        return $this->invio_email_cumulativo;
    }

    /**
     * Get the [invio_email_parametrico] column value.
     *
     * @return string
     */
    public function getInvioEmailParametrico()
    {

        return $this->invio_email_parametrico;
    }

    /**
     * Get the [invio_email_temporale] column value.
     *
     * @return string
     */
    public function getInvioEmailTemporale()
    {

        return $this->invio_email_temporale;
    }

    /**
     * Get the [tipo_sms] column value.
     *
     * @return string
     */
    public function getTipoSms()
    {

        return $this->tipo_sms;
    }

    /**
     * Get the [tipo_sms_cumulativo] column value.
     *
     * @return string
     */
    public function getTipoSmsCumulativo()
    {

        return $this->tipo_sms_cumulativo;
    }

    /**
     * Get the [tipo_sms_parametrico] column value.
     *
     * @return string
     */
    public function getTipoSmsParametrico()
    {

        return $this->tipo_sms_parametrico;
    }

    /**
     * Get the [tipo_sms_temporale] column value.
     *
     * @return string
     */
    public function getTipoSmsTemporale()
    {

        return $this->tipo_sms_temporale;
    }

    /**
     * Get the [aut_entrata_ritardo] column value.
     *
     * @return string
     */
    public function getAutEntrataRitardo()
    {

        return $this->aut_entrata_ritardo;
    }

    /**
     * Get the [aut_uscita_anticipo] column value.
     *
     * @return string
     */
    public function getAutUscitaAnticipo()
    {

        return $this->aut_uscita_anticipo;
    }

    /**
     * Get the [aut_pomeriggio] column value.
     *
     * @return string
     */
    public function getAutPomeriggio()
    {

        return $this->aut_pomeriggio;
    }

    /**
     * Get the [acconsente] column value.
     *
     * @return string
     */
    public function getAcconsente()
    {

        return $this->acconsente;
    }

    /**
     * Get the [ritirato] column value.
     *
     * @return string
     */
    public function getRitirato()
    {

        return $this->ritirato;
    }

    /**
     * Get the [data_nascita] column value.
     *
     * @return int
     */
    public function getDataNascita()
    {

        return $this->data_nascita;
    }

    /**
     * Get the [codice_studente] column value.
     *
     * @return string
     */
    public function getCodiceStudente()
    {

        return $this->codice_studente;
    }

    /**
     * Get the [password_studente] column value.
     *
     * @return string
     */
    public function getPasswordStudente()
    {

        return $this->password_studente;
    }

    /**
     * Get the [codice_giustificazioni_studente] column value.
     *
     * @return string
     */
    public function getCodiceGiustificazioniStudente()
    {

        return $this->codice_giustificazioni_studente;
    }

    /**
     * Get the [esonero_religione] column value.
     *
     * @return string
     */
    public function getEsoneroReligione()
    {

        return $this->esonero_religione;
    }

    /**
     * Get the [materia_sostitutiva_religione] column value.
     *
     * @return int
     */
    public function getMateriaSostitutivaReligione()
    {

        return $this->materia_sostitutiva_religione;
    }

    /**
     * Get the [esonero_ed_fisica] column value.
     *
     * @return string
     */
    public function getEsoneroEdFisica()
    {

        return $this->esonero_ed_fisica;
    }

    /**
     * Get the [materia_sostitutiva_edfisica] column value.
     *
     * @return int
     */
    public function getMateriaSostitutivaEdfisica()
    {

        return $this->materia_sostitutiva_edfisica;
    }

    /**
     * Get the [crediti_terza] column value.
     *
     * @return int
     */
    public function getCreditiTerza()
    {

        return $this->crediti_terza;
    }

    /**
     * Get the [media_voti_terza] column value.
     *
     * @return string
     */
    public function getMediaVotiTerza()
    {

        return $this->media_voti_terza;
    }

    /**
     * Get the [debiti_terza] column value.
     *
     * @return string
     */
    public function getDebitiTerza()
    {

        return $this->debiti_terza;
    }

    /**
     * Get the [crediti_sospesi_terza] column value.
     *
     * @return int
     */
    public function getCreditiSospesiTerza()
    {

        return $this->crediti_sospesi_terza;
    }

    /**
     * Get the [crediti_reintegrati_terza] column value.
     *
     * @return int
     */
    public function getCreditiReintegratiTerza()
    {

        return $this->crediti_reintegrati_terza;
    }

    /**
     * Get the [crediti_quarta] column value.
     *
     * @return int
     */
    public function getCreditiQuarta()
    {

        return $this->crediti_quarta;
    }

    /**
     * Get the [media_voti_quarta] column value.
     *
     * @return string
     */
    public function getMediaVotiQuarta()
    {

        return $this->media_voti_quarta;
    }

    /**
     * Get the [debiti_quarta] column value.
     *
     * @return string
     */
    public function getDebitiQuarta()
    {

        return $this->debiti_quarta;
    }

    /**
     * Get the [crediti_sospesi_quarta] column value.
     *
     * @return int
     */
    public function getCreditiSospesiQuarta()
    {

        return $this->crediti_sospesi_quarta;
    }

    /**
     * Get the [crediti_reintegrati_quarta] column value.
     *
     * @return int
     */
    public function getCreditiReintegratiQuarta()
    {

        return $this->crediti_reintegrati_quarta;
    }

    /**
     * Get the [crediti_quinta] column value.
     *
     * @return int
     */
    public function getCreditiQuinta()
    {

        return $this->crediti_quinta;
    }

    /**
     * Get the [media_voti_quinta] column value.
     *
     * @return string
     */
    public function getMediaVotiQuinta()
    {

        return $this->media_voti_quinta;
    }

    /**
     * Get the [crediti_finali_agg] column value.
     *
     * @return int
     */
    public function getCreditiFinaliAgg()
    {

        return $this->crediti_finali_agg;
    }

    /**
     * Get the [matricola] column value.
     *
     * @return string
     */
    public function getMatricola()
    {

        return $this->matricola;
    }

    /**
     * Get the [luogo_nascita] column value.
     *
     * @return string
     */
    public function getLuogoNascita()
    {

        return $this->luogo_nascita;
    }

    /**
     * Get the [provincia_nascita] column value.
     *
     * @return string
     */
    public function getProvinciaNascita()
    {

        return $this->provincia_nascita;
    }

    /**
     * Get the [motivi_crediti_terza] column value.
     *
     * @return string
     */
    public function getMotiviCreditiTerza()
    {

        return $this->motivi_crediti_terza;
    }

    /**
     * Get the [motivi_crediti_quarta] column value.
     *
     * @return string
     */
    public function getMotiviCreditiQuarta()
    {

        return $this->motivi_crediti_quarta;
    }

    /**
     * Get the [motivi_crediti_quinta] column value.
     *
     * @return string
     */
    public function getMotiviCreditiQuinta()
    {

        return $this->motivi_crediti_quinta;
    }

    /**
     * Get the [motivi_crediti_agg] column value.
     *
     * @return string
     */
    public function getMotiviCreditiAgg()
    {

        return $this->motivi_crediti_agg;
    }

    /**
     * Get the [codice_comune_nascita] column value.
     *
     * @return string
     */
    public function getCodiceComuneNascita()
    {

        return $this->codice_comune_nascita;
    }

    /**
     * Get the [stato_nascita] column value.
     *
     * @return string
     */
    public function getStatoNascita()
    {

        return $this->stato_nascita;
    }

    /**
     * Get the [cittadinanza] column value.
     *
     * @return string
     */
    public function getCittadinanza()
    {

        return $this->cittadinanza;
    }

    /**
     * Get the [seconda_cittadinanza] column value.
     *
     * @return string
     */
    public function getSecondaCittadinanza()
    {

        return $this->seconda_cittadinanza;
    }

    /**
     * Get the [codice_comune_residenza] column value.
     *
     * @return string
     */
    public function getCodiceComuneResidenza()
    {

        return $this->codice_comune_residenza;
    }

    /**
     * Get the [distretto] column value.
     *
     * @return string
     */
    public function getDistretto()
    {

        return $this->distretto;
    }

    /**
     * Get the [codice_fiscale] column value.
     *
     * @return string
     */
    public function getCodiceFiscale()
    {

        return $this->codice_fiscale;
    }

    /**
     * Get the [medico] column value.
     *
     * @return string
     */
    public function getMedico()
    {

        return $this->medico;
    }

    /**
     * Get the [telefono_medico] column value.
     *
     * @return string
     */
    public function getTelefonoMedico()
    {

        return $this->telefono_medico;
    }

    /**
     * Get the [intolleranze_alim] column value.
     *
     * @return string
     */
    public function getIntolleranzeAlim()
    {

        return $this->intolleranze_alim;
    }

    /**
     * Get the [gruppo_sanguigno] column value.
     *
     * @return string
     */
    public function getGruppoSanguigno()
    {

        return $this->gruppo_sanguigno;
    }

    /**
     * Get the [gruppo_rh] column value.
     *
     * @return string
     */
    public function getGruppoRh()
    {

        return $this->gruppo_rh;
    }

    /**
     * Get the [codice_asl] column value.
     *
     * @return string
     */
    public function getCodiceAsl()
    {

        return $this->codice_asl;
    }

    /**
     * Get the [annotazioni] column value.
     *
     * @return string
     */
    public function getAnnotazioni()
    {

        return $this->annotazioni;
    }

    /**
     * Get the [stato_civile] column value.
     *
     * @return int
     */
    public function getStatoCivile()
    {

        return $this->stato_civile;
    }

    /**
     * Get the [voto_primo_scritto] column value.
     *
     * @return int
     */
    public function getVotoPrimoScritto()
    {

        return $this->voto_primo_scritto;
    }

    /**
     * Get the [voto_secondo_scritto] column value.
     *
     * @return int
     */
    public function getVotoSecondoScritto()
    {

        return $this->voto_secondo_scritto;
    }

    /**
     * Get the [voto_terzo_scritto] column value.
     *
     * @return int
     */
    public function getVotoTerzoScritto()
    {

        return $this->voto_terzo_scritto;
    }

    /**
     * Get the [voto_orale] column value.
     *
     * @return int
     */
    public function getVotoOrale()
    {

        return $this->voto_orale;
    }

    /**
     * Get the [voto_bonus] column value.
     *
     * @return int
     */
    public function getVotoBonus()
    {

        return $this->voto_bonus;
    }

    /**
     * Get the [materia_secondo_scr] column value.
     *
     * @return string
     */
    public function getMateriaSecondoScr()
    {

        return $this->materia_secondo_scr;
    }

    /**
     * Get the [ulteriori_specif_diploma] column value.
     *
     * @return string
     */
    public function getUlterioriSpecifDiploma()
    {

        return $this->ulteriori_specif_diploma;
    }

    /**
     * Get the [numero_diploma] column value.
     *
     * @return int
     */
    public function getNumeroDiploma()
    {

        return $this->numero_diploma;
    }

    /**
     * Get the [chi_inserisce] column value.
     *
     * @return string
     */
    public function getChiInserisce()
    {

        return $this->chi_inserisce;
    }

    /**
     * Get the [data_inserimento] column value.
     *
     * @return string
     */
    public function getDataInserimento()
    {

        return $this->data_inserimento;
    }

    /**
     * Get the [tipo_inserimento] column value.
     *
     * @return string
     */
    public function getTipoInserimento()
    {

        return $this->tipo_inserimento;
    }

    /**
     * Get the [chi_modifica] column value.
     *
     * @return string
     */
    public function getChiModifica()
    {

        return $this->chi_modifica;
    }

    /**
     * Get the [data_modifica] column value.
     *
     * @return string
     */
    public function getDataModifica()
    {

        return $this->data_modifica;
    }

    /**
     * Get the [tipo_modifica] column value.
     *
     * @return string
     */
    public function getTipoModifica()
    {

        return $this->tipo_modifica;
    }

    /**
     * Get the [flag_canc] column value.
     *
     * @return string
     */
    public function getFlagCanc()
    {

        return $this->flag_canc;
    }

    /**
     * Get the [stato_avanzamento] column value.
     *
     * @return string
     */
    public function getStatoAvanzamento()
    {

        return $this->stato_avanzamento;
    }

    /**
     * Get the [data_stato_avanzamento] column value.
     *
     * @return string
     */
    public function getDataStatoAvanzamento()
    {

        return $this->data_stato_avanzamento;
    }

    /**
     * Get the [cap_provincia_nascita] column value.
     *
     * @return string
     */
    public function getCapProvinciaNascita()
    {

        return $this->cap_provincia_nascita;
    }

    /**
     * Get the [badge] column value.
     *
     * @return string
     */
    public function getBadge()
    {

        return $this->badge;
    }

    /**
     * Get the [cap_residenza] column value.
     *
     * @return string
     */
    public function getCapResidenza()
    {

        return $this->cap_residenza;
    }

    /**
     * Get the [codice_comune_domicilio] column value.
     *
     * @return string
     */
    public function getCodiceComuneDomicilio()
    {

        return $this->codice_comune_domicilio;
    }

    /**
     * Get the [cap_domicilio] column value.
     *
     * @return string
     */
    public function getCapDomicilio()
    {

        return $this->cap_domicilio;
    }

    /**
     * Get the [cap_nascita] column value.
     *
     * @return string
     */
    public function getCapNascita()
    {

        return $this->cap_nascita;
    }

    /**
     * Get the [indirizzo_domicilio] column value.
     *
     * @return string
     */
    public function getIndirizzoDomicilio()
    {

        return $this->indirizzo_domicilio;
    }

    /**
     * Get the [citta_nascita_straniera] column value.
     *
     * @return string
     */
    public function getCittaNascitaStraniera()
    {

        return $this->citta_nascita_straniera;
    }

    /**
     * Get the [cellulare_allievo] column value.
     *
     * @return string
     */
    public function getCellulareAllievo()
    {

        return $this->cellulare_allievo;
    }

    /**
     * Get the [handicap] column value.
     *
     * @return string
     */
    public function getHandicap()
    {

        return $this->handicap;
    }

    /**
     * Get the [stato_convittore] column value.
     *
     * @return string
     */
    public function getStatoConvittore()
    {

        return $this->stato_convittore;
    }

    /**
     * Get the [data_ritiro] column value.
     *
     * @return string
     */
    public function getDataRitiro()
    {

        return $this->data_ritiro;
    }

    /**
     * Get the [voto_ammissione] column value.
     *
     * @return string
     */
    public function getVotoAmmissione()
    {

        return $this->voto_ammissione;
    }

    /**
     * Get the [differenza_punteggio] column value.
     *
     * @return string
     */
    public function getDifferenzaPunteggio()
    {

        return $this->differenza_punteggio;
    }

    /**
     * Get the [voto_qualifica] column value.
     *
     * @return string
     */
    public function getVotoQualifica()
    {

        return $this->voto_qualifica;
    }

    /**
     * Get the [voto_esame_sc1_qual] column value.
     *
     * @return string
     */
    public function getVotoEsameSc1Qual()
    {

        return $this->voto_esame_sc1_qual;
    }

    /**
     * Get the [voto_esame_sc2_qual] column value.
     *
     * @return string
     */
    public function getVotoEsameSc2Qual()
    {

        return $this->voto_esame_sc2_qual;
    }

    /**
     * Get the [voto_esame_or_qual] column value.
     *
     * @return string
     */
    public function getVotoEsameOrQual()
    {

        return $this->voto_esame_or_qual;
    }

    /**
     * Get the [stato_privatista] column value.
     *
     * @return string
     */
    public function getStatoPrivatista()
    {

        return $this->stato_privatista;
    }

    /**
     * Get the [foto] column value.
     *
     * @return string
     */
    public function getFoto()
    {

        return $this->foto;
    }

    /**
     * Get the [rappresentante] column value.
     *
     * @return string
     */
    public function getRappresentante()
    {

        return $this->rappresentante;
    }

    /**
     * Get the [obbligo_formativo] column value.
     *
     * @return string
     */
    public function getObbligoFormativo()
    {

        return $this->obbligo_formativo;
    }

    /**
     * Get the [id_lingua_1] column value.
     *
     * @return string
     */
    public function getIdLingua1()
    {

        return $this->id_lingua_1;
    }

    /**
     * Get the [id_lingua_2] column value.
     *
     * @return string
     */
    public function getIdLingua2()
    {

        return $this->id_lingua_2;
    }

    /**
     * Get the [id_lingua_3] column value.
     *
     * @return string
     */
    public function getIdLingua3()
    {

        return $this->id_lingua_3;
    }

    /**
     * Get the [id_lingua_4] column value.
     *
     * @return string
     */
    public function getIdLingua4()
    {

        return $this->id_lingua_4;
    }

    /**
     * Get the [id_lingua_5] column value.
     *
     * @return string
     */
    public function getIdLingua5()
    {

        return $this->id_lingua_5;
    }

    /**
     * Get the [id_provenienza_scolastica] column value.
     *
     * @return string
     */
    public function getIdProvenienzaScolastica()
    {

        return $this->id_provenienza_scolastica;
    }

    /**
     * Get the [id_scuola_media] column value.
     *
     * @return string
     */
    public function getIdScuolaMedia()
    {

        return $this->id_scuola_media;
    }

    /**
     * Get the [lingua_scuola_media] column value.
     *
     * @return string
     */
    public function getLinguaScuolaMedia()
    {

        return $this->lingua_scuola_media;
    }

    /**
     * Get the [lingua_scuola_media_2] column value.
     *
     * @return string
     */
    public function getLinguaScuolaMedia2()
    {

        return $this->lingua_scuola_media_2;
    }

    /**
     * Get the [giudizio_scuola_media] column value.
     *
     * @return string
     */
    public function getGiudizioScuolaMedia()
    {

        return $this->giudizio_scuola_media;
    }

    /**
     * Get the [trasporto] column value.
     *
     * @return string
     */
    public function getTrasporto()
    {

        return $this->trasporto;
    }

    /**
     * Get the [data_iscrizione] column value.
     *
     * @return string
     */
    public function getDataIscrizione()
    {

        return $this->data_iscrizione;
    }

    /**
     * Get the [pei] column value.
     *
     * @return string
     */
    public function getPei()
    {

        return $this->pei;
    }

    /**
     * Get the [ammesso_esame_qualifica] column value.
     *
     * @return string
     */
    public function getAmmessoEsameQualifica()
    {

        return $this->ammesso_esame_qualifica;
    }

    /**
     * Get the [ammesso_esame_quinta] column value.
     *
     * @return string
     */
    public function getAmmessoEsameQuinta()
    {

        return $this->ammesso_esame_quinta;
    }

    /**
     * Get the [giudizio_ammissione_quinta] column value.
     *
     * @return string
     */
    public function getGiudizioAmmissioneQuinta()
    {

        return $this->giudizio_ammissione_quinta;
    }

    /**
     * Get the [grado_handicap] column value.
     *
     * @return string
     */
    public function getGradoHandicap()
    {

        return $this->grado_handicap;
    }

    /**
     * Get the [tipo_handicap] column value.
     *
     * @return string
     */
    public function getTipoHandicap()
    {

        return $this->tipo_handicap;
    }

    /**
     * Get the [stato_licenza_maestro] column value.
     *
     * @return string
     */
    public function getStatoLicenzaMaestro()
    {

        return $this->stato_licenza_maestro;
    }

    /**
     * Get the [id_studente_sissi] column value.
     *
     * @return string
     */
    public function getIdStudenteSissi()
    {

        return $this->id_studente_sissi;
    }

    /**
     * Get the [badge_rfid] column value.
     *
     * @return string
     */
    public function getBadgeRfid()
    {

        return $this->badge_rfid;
    }

    /**
     * Get the [lode] column value.
     *
     * @return string
     */
    public function getLode()
    {

        return $this->lode;
    }

    /**
     * Get the [distretto_scolastico] column value.
     *
     * @return string
     */
    public function getDistrettoScolastico()
    {

        return $this->distretto_scolastico;
    }

    /**
     * Get the [giudizio_ammissione_terza] column value.
     *
     * @return string
     */
    public function getGiudizioAmmissioneTerza()
    {

        return $this->giudizio_ammissione_terza;
    }

    /**
     * Get the [esito_prima_media] column value.
     *
     * @return string
     */
    public function getEsitoPrimaMedia()
    {

        return $this->esito_prima_media;
    }

    /**
     * Get the [esito_seconda_media] column value.
     *
     * @return string
     */
    public function getEsitoSecondaMedia()
    {

        return $this->esito_seconda_media;
    }

    /**
     * Get the [esito_terza_media] column value.
     *
     * @return string
     */
    public function getEsitoTerzaMedia()
    {

        return $this->esito_terza_media;
    }

    /**
     * Get the [giudizio_esame_sc1_qual] column value.
     *
     * @return string
     */
    public function getGiudizioEsameSc1Qual()
    {

        return $this->giudizio_esame_sc1_qual;
    }

    /**
     * Get the [giudizio_esame_sc2_qual] column value.
     *
     * @return string
     */
    public function getGiudizioEsameSc2Qual()
    {

        return $this->giudizio_esame_sc2_qual;
    }

    /**
     * Get the [giudizio_esame_or_qual] column value.
     *
     * @return string
     */
    public function getGiudizioEsameOrQual()
    {

        return $this->giudizio_esame_or_qual;
    }

    /**
     * Get the [giudizio_complessivo_esame_qual] column value.
     *
     * @return string
     */
    public function getGiudizioComplessivoEsameQual()
    {

        return $this->giudizio_complessivo_esame_qual;
    }

    /**
     * Get the [acconsente_aziende] column value.
     *
     * @return int
     */
    public function getAcconsenteAziende()
    {

        return $this->acconsente_aziende;
    }

    /**
     * Get the [curriculum_prima] column value.
     *
     * @return string
     */
    public function getCurriculumPrima()
    {

        return $this->curriculum_prima;
    }

    /**
     * Get the [curriculum_seconda] column value.
     *
     * @return string
     */
    public function getCurriculumSeconda()
    {

        return $this->curriculum_seconda;
    }

    /**
     * Get the [stage_professionali] column value.
     *
     * @return string
     */
    public function getStageProfessionali()
    {

        return $this->stage_professionali;
    }

    /**
     * Get the [data_orale] column value.
     *
     * @return string
     */
    public function getDataOrale()
    {

        return $this->data_orale;
    }

    /**
     * Get the [ordine_esame_orale] column value.
     *
     * @return string
     */
    public function getOrdineEsameOrale()
    {

        return $this->ordine_esame_orale;
    }

    /**
     * Get the [tipo_primo_scritto] column value.
     *
     * @return string
     */
    public function getTipoPrimoScritto()
    {

        return $this->tipo_primo_scritto;
    }

    /**
     * Get the [tipo_secondo_scritto] column value.
     *
     * @return string
     */
    public function getTipoSecondoScritto()
    {

        return $this->tipo_secondo_scritto;
    }

    /**
     * Get the [tipo_terzo_scritto] column value.
     *
     * @return string
     */
    public function getTipoTerzoScritto()
    {

        return $this->tipo_terzo_scritto;
    }

    /**
     * Get the [unanimita_primo_scritto] column value.
     *
     * @return string
     */
    public function getUnanimitaPrimoScritto()
    {

        return $this->unanimita_primo_scritto;
    }

    /**
     * Get the [unanimita_secondo_scritto] column value.
     *
     * @return string
     */
    public function getUnanimitaSecondoScritto()
    {

        return $this->unanimita_secondo_scritto;
    }

    /**
     * Get the [unanimita_terzo_scritto] column value.
     *
     * @return string
     */
    public function getUnanimitaTerzoScritto()
    {

        return $this->unanimita_terzo_scritto;
    }

    /**
     * Get the [argomento_scelto_orale] column value.
     *
     * @return string
     */
    public function getArgomentoSceltoOrale()
    {

        return $this->argomento_scelto_orale;
    }

    /**
     * Get the [area_disc_1_orale] column value.
     *
     * @return string
     */
    public function getAreaDisc1Orale()
    {

        return $this->area_disc_1_orale;
    }

    /**
     * Get the [area_disc_2_orale] column value.
     *
     * @return string
     */
    public function getAreaDisc2Orale()
    {

        return $this->area_disc_2_orale;
    }

    /**
     * Get the [disc_elaborati_orale] column value.
     *
     * @return string
     */
    public function getDiscElaboratiOrale()
    {

        return $this->disc_elaborati_orale;
    }

    /**
     * Get the [unanimita_voto_finale] column value.
     *
     * @return string
     */
    public function getUnanimitaVotoFinale()
    {

        return $this->unanimita_voto_finale;
    }

    /**
     * Get the [presente_esame_quinta] column value.
     *
     * @return string
     */
    public function getPresenteEsameQuinta()
    {

        return $this->presente_esame_quinta;
    }

    /**
     * Get the [stampa_badge] column value.
     *
     * @return string
     */
    public function getStampaBadge()
    {

        return $this->stampa_badge;
    }

    /**
     * Get the [id_classe_destinazione] column value.
     *
     * @return int
     */
    public function getIdClasseDestinazione()
    {

        return $this->id_classe_destinazione;
    }

    /**
     * Get the [sconto_rette] column value.
     *
     * @return int
     */
    public function getScontoRette()
    {

        return $this->sconto_rette;
    }

    /**
     * Get the [carta_studente_numero] column value.
     *
     * @return string
     */
    public function getCartaStudenteNumero()
    {

        return $this->carta_studente_numero;
    }

    /**
     * Get the [carta_studente_scadenza] column value.
     *
     * @return int
     */
    public function getCartaStudenteScadenza()
    {

        return $this->carta_studente_scadenza;
    }

    /**
     * Get the [esito_corrente_calcolato] column value.
     *
     * @return string
     */
    public function getEsitoCorrenteCalcolato()
    {

        return $this->esito_corrente_calcolato;
    }

    /**
     * Get the [id_flusso] column value.
     *
     * @return string
     */
    public function getIdFlusso()
    {

        return $this->id_flusso;
    }

    /**
     * Get the [data_aggiornamento_sogei] column value.
     *
     * @return string
     */
    public function getDataAggiornamentoSogei()
    {

        return $this->data_aggiornamento_sogei;
    }

    /**
     * Get the [codice_alunno_ministeriale] column value.
     *
     * @return string
     */
    public function getCodiceAlunnoMinisteriale()
    {

        return $this->codice_alunno_ministeriale;
    }

    /**
     * Get the [flag_cf_fittizio] column value.
     *
     * @return int
     */
    public function getFlagCfFittizio()
    {

        return $this->flag_cf_fittizio;
    }

    /**
     * Get the [flag_s2f] column value.
     *
     * @return string
     */
    public function getFlagS2f()
    {

        return $this->flag_s2f;
    }

    /**
     * Get the [codice_stato_sogei] column value.
     *
     * @return string
     */
    public function getCodiceStatoSogei()
    {

        return $this->codice_stato_sogei;
    }

    /**
     * Get the [codice_gruppo_nomade] column value.
     *
     * @return string
     */
    public function getCodiceGruppoNomade()
    {

        return $this->codice_gruppo_nomade;
    }

    /**
     * Get the [flag_minore_straniero] column value.
     *
     * @return int
     */
    public function getFlagMinoreStraniero()
    {

        return $this->flag_minore_straniero;
    }

    /**
     * Get the [chiave] column value.
     *
     * @return string
     */
    public function getChiave()
    {

        return $this->chiave;
    }

    /**
     * Get the [voto_esame_medie_italiano] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieItaliano()
    {

        return $this->voto_esame_medie_italiano;
    }

    /**
     * Get the [voto_esame_medie_inglese] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieInglese()
    {

        return $this->voto_esame_medie_inglese;
    }

    /**
     * Get the [voto_esame_medie_matematica] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieMatematica()
    {

        return $this->voto_esame_medie_matematica;
    }

    /**
     * Get the [voto_esame_medie_seconda_lingua] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieSecondaLingua()
    {

        return $this->voto_esame_medie_seconda_lingua;
    }

    /**
     * Get the [voto_esame_medie_invalsi_ita] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieInvalsiIta()
    {

        return $this->voto_esame_medie_invalsi_ita;
    }

    /**
     * Get the [voto_esame_medie_invalsi_mat] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieInvalsiMat()
    {

        return $this->voto_esame_medie_invalsi_mat;
    }

    /**
     * Get the [voto_esame_medie_orale] column value.
     *
     * @return string
     */
    public function getVotoEsameMedieOrale()
    {

        return $this->voto_esame_medie_orale;
    }

    /**
     * Get the [voto_ammissione_medie] column value.
     *
     * @return string
     */
    public function getVotoAmmissioneMedie()
    {

        return $this->voto_ammissione_medie;
    }

    /**
     * Get the [esito_prima_elementare] column value.
     *
     * @return string
     */
    public function getEsitoPrimaElementare()
    {

        return $this->esito_prima_elementare;
    }

    /**
     * Get the [esito_seconda_elementare] column value.
     *
     * @return string
     */
    public function getEsitoSecondaElementare()
    {

        return $this->esito_seconda_elementare;
    }

    /**
     * Get the [esito_terza_elementare] column value.
     *
     * @return string
     */
    public function getEsitoTerzaElementare()
    {

        return $this->esito_terza_elementare;
    }

    /**
     * Get the [esito_quarta_elementare] column value.
     *
     * @return string
     */
    public function getEsitoQuartaElementare()
    {

        return $this->esito_quarta_elementare;
    }

    /**
     * Get the [esito_quinta_elementare] column value.
     *
     * @return string
     */
    public function getEsitoQuintaElementare()
    {

        return $this->esito_quinta_elementare;
    }

    /**
     * Get the [tipo_voto_esame_medie_italiano] column value.
     *
     * @return string
     */
    public function getTipoVotoEsameMedieItaliano()
    {

        return $this->tipo_voto_esame_medie_italiano;
    }

    /**
     * Get the [tipo_voto_esame_medie_inglese] column value.
     *
     * @return string
     */
    public function getTipoVotoEsameMedieInglese()
    {

        return $this->tipo_voto_esame_medie_inglese;
    }

    /**
     * Get the [giudizio_1_medie] column value.
     *
     * @return string
     */
    public function getGiudizio1Medie()
    {

        return $this->giudizio_1_medie;
    }

    /**
     * Get the [giudizio_2_medie] column value.
     *
     * @return string
     */
    public function getGiudizio2Medie()
    {

        return $this->giudizio_2_medie;
    }

    /**
     * Get the [giudizio_3_medie] column value.
     *
     * @return string
     */
    public function getGiudizio3Medie()
    {

        return $this->giudizio_3_medie;
    }

    /**
     * Get the [argomenti_orali_medie] column value.
     *
     * @return string
     */
    public function getArgomentiOraliMedie()
    {

        return $this->argomenti_orali_medie;
    }

    /**
     * Get the [giudizio_finale_1_medie] column value.
     *
     * @return string
     */
    public function getGiudizioFinale1Medie()
    {

        return $this->giudizio_finale_1_medie;
    }

    /**
     * Get the [giudizio_finale_2_medie] column value.
     *
     * @return string
     */
    public function getGiudizioFinale2Medie()
    {

        return $this->giudizio_finale_2_medie;
    }

    /**
     * Get the [giudizio_finale_3_medie] column value.
     *
     * @return string
     */
    public function getGiudizioFinale3Medie()
    {

        return $this->giudizio_finale_3_medie;
    }

    /**
     * Get the [consiglio_terza_media] column value.
     *
     * @return string
     */
    public function getConsiglioTerzaMedia()
    {

        return $this->consiglio_terza_media;
    }

    /**
     * Get the [giudizio_sintetico_esame_terza_media] column value.
     *
     * @return string
     */
    public function getGiudizioSinteticoEsameTerzaMedia()
    {

        return $this->giudizio_sintetico_esame_terza_media;
    }

    /**
     * Get the [data_arrivo_in_italia] column value.
     *
     * @return int
     */
    public function getDataArrivoInItalia()
    {

        return $this->data_arrivo_in_italia;
    }

    /**
     * Get the [frequenza_asilo_nido] column value.
     *
     * @return int
     */
    public function getFrequenzaAsiloNido()
    {

        return $this->frequenza_asilo_nido;
    }

    /**
     * Get the [frequenza_scuola_materna] column value.
     *
     * @return int
     */
    public function getFrequenzaScuolaMaterna()
    {

        return $this->frequenza_scuola_materna;
    }

    /**
     * Get the [data_aggiornamento_sidi] column value.
     *
     * @return int
     */
    public function getDataAggiornamentoSidi()
    {

        return $this->data_aggiornamento_sidi;
    }

    /**
     * Get the [cmp_sup_val_ita] column value.
     *
     * @return string
     */
    public function getCmpSupValIta()
    {

        return $this->cmp_sup_val_ita;
    }

    /**
     * Get the [cmp_sup_txt_ita] column value.
     *
     * @return string
     */
    public function getCmpSupTxtIta()
    {

        return $this->cmp_sup_txt_ita;
    }

    /**
     * Get the [cmp_sup_val_ing] column value.
     *
     * @return string
     */
    public function getCmpSupValIng()
    {

        return $this->cmp_sup_val_ing;
    }

    /**
     * Get the [cmp_sup_txt_ing] column value.
     *
     * @return string
     */
    public function getCmpSupTxtIng()
    {

        return $this->cmp_sup_txt_ing;
    }

    /**
     * Get the [cmp_sup_val_altri] column value.
     *
     * @return string
     */
    public function getCmpSupValAltri()
    {

        return $this->cmp_sup_val_altri;
    }

    /**
     * Get the [cmp_sup_txt_altri] column value.
     *
     * @return string
     */
    public function getCmpSupTxtAltri()
    {

        return $this->cmp_sup_txt_altri;
    }

    /**
     * Get the [cmp_sup_val_mat] column value.
     *
     * @return string
     */
    public function getCmpSupValMat()
    {

        return $this->cmp_sup_val_mat;
    }

    /**
     * Get the [cmp_sup_txt_mat] column value.
     *
     * @return string
     */
    public function getCmpSupTxtMat()
    {

        return $this->cmp_sup_txt_mat;
    }

    /**
     * Get the [cmp_sup_val_sci_tec] column value.
     *
     * @return string
     */
    public function getCmpSupValSciTec()
    {

        return $this->cmp_sup_val_sci_tec;
    }

    /**
     * Get the [cmp_sup_txt_sci_tec] column value.
     *
     * @return string
     */
    public function getCmpSupTxtSciTec()
    {

        return $this->cmp_sup_txt_sci_tec;
    }

    /**
     * Get the [cmp_sup_val_sto_soc] column value.
     *
     * @return string
     */
    public function getCmpSupValStoSoc()
    {

        return $this->cmp_sup_val_sto_soc;
    }

    /**
     * Get the [cmp_sup_txt_sto_soc] column value.
     *
     * @return string
     */
    public function getCmpSupTxtStoSoc()
    {

        return $this->cmp_sup_txt_sto_soc;
    }

    /**
     * Get the [cmp_med_val_ita] column value.
     *
     * @return string
     */
    public function getCmpMedValIta()
    {

        return $this->cmp_med_val_ita;
    }

    /**
     * Get the [cmp_med_txt_ita] column value.
     *
     * @return string
     */
    public function getCmpMedTxtIta()
    {

        return $this->cmp_med_txt_ita;
    }

    /**
     * Get the [cmp_med_val_ing] column value.
     *
     * @return string
     */
    public function getCmpMedValIng()
    {

        return $this->cmp_med_val_ing;
    }

    /**
     * Get the [cmp_med_txt_ing] column value.
     *
     * @return string
     */
    public function getCmpMedTxtIng()
    {

        return $this->cmp_med_txt_ing;
    }

    /**
     * Get the [cmp_med_val_altri] column value.
     *
     * @return string
     */
    public function getCmpMedValAltri()
    {

        return $this->cmp_med_val_altri;
    }

    /**
     * Get the [cmp_med_txt_altri] column value.
     *
     * @return string
     */
    public function getCmpMedTxtAltri()
    {

        return $this->cmp_med_txt_altri;
    }

    /**
     * Get the [cmp_med_val_mat] column value.
     *
     * @return string
     */
    public function getCmpMedValMat()
    {

        return $this->cmp_med_val_mat;
    }

    /**
     * Get the [cmp_med_txt_mat] column value.
     *
     * @return string
     */
    public function getCmpMedTxtMat()
    {

        return $this->cmp_med_txt_mat;
    }

    /**
     * Get the [cmp_med_val_sci_tec] column value.
     *
     * @return string
     */
    public function getCmpMedValSciTec()
    {

        return $this->cmp_med_val_sci_tec;
    }

    /**
     * Get the [cmp_med_txt_sci_tec] column value.
     *
     * @return string
     */
    public function getCmpMedTxtSciTec()
    {

        return $this->cmp_med_txt_sci_tec;
    }

    /**
     * Get the [cmp_med_val_sto_soc] column value.
     *
     * @return string
     */
    public function getCmpMedValStoSoc()
    {

        return $this->cmp_med_val_sto_soc;
    }

    /**
     * Get the [cmp_med_txt_sto_soc] column value.
     *
     * @return string
     */
    public function getCmpMedTxtStoSoc()
    {

        return $this->cmp_med_txt_sto_soc;
    }

    /**
     * Get the [cmp_med_val_l2] column value.
     *
     * @return string
     */
    public function getCmpMedValL2()
    {

        return $this->cmp_med_val_l2;
    }

    /**
     * Get the [cmp_med_txt_l2] column value.
     *
     * @return string
     */
    public function getCmpMedTxtL2()
    {

        return $this->cmp_med_txt_l2;
    }

    /**
     * Get the [cmp_med_val_l3] column value.
     *
     * @return string
     */
    public function getCmpMedValL3()
    {

        return $this->cmp_med_val_l3;
    }

    /**
     * Get the [cmp_med_txt_l3] column value.
     *
     * @return string
     */
    public function getCmpMedTxtL3()
    {

        return $this->cmp_med_txt_l3;
    }

    /**
     * Get the [cmp_med_val_arte] column value.
     *
     * @return string
     */
    public function getCmpMedValArte()
    {

        return $this->cmp_med_val_arte;
    }

    /**
     * Get the [cmp_med_txt_arte] column value.
     *
     * @return string
     */
    public function getCmpMedTxtArte()
    {

        return $this->cmp_med_txt_arte;
    }

    /**
     * Get the [cmp_med_val_mus] column value.
     *
     * @return string
     */
    public function getCmpMedValMus()
    {

        return $this->cmp_med_val_mus;
    }

    /**
     * Get the [cmp_med_txt_mus] column value.
     *
     * @return string
     */
    public function getCmpMedTxtMus()
    {

        return $this->cmp_med_txt_mus;
    }

    /**
     * Get the [cmp_med_val_mot] column value.
     *
     * @return string
     */
    public function getCmpMedValMot()
    {

        return $this->cmp_med_val_mot;
    }

    /**
     * Get the [cmp_med_txt_mot] column value.
     *
     * @return string
     */
    public function getCmpMedTxtMot()
    {

        return $this->cmp_med_txt_mot;
    }

    /**
     * Set the value of [id_studente] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdStudente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_studente !== $v) {
            $this->id_studente = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_STUDENTE;
        }


        return $this;
    } // setIdStudente()

    /**
     * Set the value of [nome] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setNome($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->nome !== $v) {
            $this->nome = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::NOME;
        }


        return $this;
    } // setNome()

    /**
     * Set the value of [cognome] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCognome($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cognome !== $v) {
            $this->cognome = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::COGNOME;
        }


        return $this;
    } // setCognome()

    /**
     * Set the value of [indirizzo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIndirizzo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->indirizzo !== $v) {
            $this->indirizzo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INDIRIZZO;
        }


        return $this;
    } // setIndirizzo()

    /**
     * Set the value of [citta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCitta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->citta !== $v) {
            $this->citta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CITTA;
        }


        return $this;
    } // setCitta()

    /**
     * Set the value of [cap] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCap($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cap !== $v) {
            $this->cap = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CAP;
        }


        return $this;
    } // setCap()

    /**
     * Set the value of [provincia] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setProvincia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->provincia !== $v) {
            $this->provincia = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::PROVINCIA;
        }


        return $this;
    } // setProvincia()

    /**
     * Set the value of [sesso] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setSesso($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->sesso !== $v) {
            $this->sesso = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::SESSO;
        }


        return $this;
    } // setSesso()

    /**
     * Set the value of [telefono] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTelefono($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->telefono !== $v) {
            $this->telefono = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TELEFONO;
        }


        return $this;
    } // setTelefono()

    /**
     * Set the value of [cellulare1] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCellulare1($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cellulare1 !== $v) {
            $this->cellulare1 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CELLULARE1;
        }


        return $this;
    } // setCellulare1()

    /**
     * Set the value of [cellulare2] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCellulare2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cellulare2 !== $v) {
            $this->cellulare2 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CELLULARE2;
        }


        return $this;
    } // setCellulare2()

    /**
     * Set the value of [email1] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEmail1($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->email1 !== $v) {
            $this->email1 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::EMAIL1;
        }


        return $this;
    } // setEmail1()

    /**
     * Set the value of [email2] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEmail2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->email2 !== $v) {
            $this->email2 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::EMAIL2;
        }


        return $this;
    } // setEmail2()

    /**
     * Set the value of [invio_email] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setInvioEmail($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->invio_email !== $v) {
            $this->invio_email = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INVIO_EMAIL;
        }


        return $this;
    } // setInvioEmail()

    /**
     * Set the value of [invio_email_cumulativo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setInvioEmailCumulativo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->invio_email_cumulativo !== $v) {
            $this->invio_email_cumulativo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INVIO_EMAIL_CUMULATIVO;
        }


        return $this;
    } // setInvioEmailCumulativo()

    /**
     * Set the value of [invio_email_parametrico] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setInvioEmailParametrico($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->invio_email_parametrico !== $v) {
            $this->invio_email_parametrico = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INVIO_EMAIL_PARAMETRICO;
        }


        return $this;
    } // setInvioEmailParametrico()

    /**
     * Set the value of [invio_email_temporale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setInvioEmailTemporale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->invio_email_temporale !== $v) {
            $this->invio_email_temporale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INVIO_EMAIL_TEMPORALE;
        }


        return $this;
    } // setInvioEmailTemporale()

    /**
     * Set the value of [tipo_sms] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoSms($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_sms !== $v) {
            $this->tipo_sms = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_SMS;
        }


        return $this;
    } // setTipoSms()

    /**
     * Set the value of [tipo_sms_cumulativo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoSmsCumulativo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_sms_cumulativo !== $v) {
            $this->tipo_sms_cumulativo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_SMS_CUMULATIVO;
        }


        return $this;
    } // setTipoSmsCumulativo()

    /**
     * Set the value of [tipo_sms_parametrico] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoSmsParametrico($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_sms_parametrico !== $v) {
            $this->tipo_sms_parametrico = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_SMS_PARAMETRICO;
        }


        return $this;
    } // setTipoSmsParametrico()

    /**
     * Set the value of [tipo_sms_temporale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoSmsTemporale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_sms_temporale !== $v) {
            $this->tipo_sms_temporale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_SMS_TEMPORALE;
        }


        return $this;
    } // setTipoSmsTemporale()

    /**
     * Set the value of [aut_entrata_ritardo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAutEntrataRitardo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->aut_entrata_ritardo !== $v) {
            $this->aut_entrata_ritardo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AUT_ENTRATA_RITARDO;
        }


        return $this;
    } // setAutEntrataRitardo()

    /**
     * Set the value of [aut_uscita_anticipo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAutUscitaAnticipo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->aut_uscita_anticipo !== $v) {
            $this->aut_uscita_anticipo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AUT_USCITA_ANTICIPO;
        }


        return $this;
    } // setAutUscitaAnticipo()

    /**
     * Set the value of [aut_pomeriggio] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAutPomeriggio($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->aut_pomeriggio !== $v) {
            $this->aut_pomeriggio = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AUT_POMERIGGIO;
        }


        return $this;
    } // setAutPomeriggio()

    /**
     * Set the value of [acconsente] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAcconsente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->acconsente !== $v) {
            $this->acconsente = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ACCONSENTE;
        }


        return $this;
    } // setAcconsente()

    /**
     * Set the value of [ritirato] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setRitirato($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ritirato !== $v) {
            $this->ritirato = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::RITIRATO;
        }


        return $this;
    } // setRitirato()

    /**
     * Set the value of [data_nascita] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->data_nascita !== $v) {
            $this->data_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_NASCITA;
        }


        return $this;
    } // setDataNascita()

    /**
     * Set the value of [codice_studente] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceStudente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_studente !== $v) {
            $this->codice_studente = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_STUDENTE;
        }


        return $this;
    } // setCodiceStudente()

    /**
     * Set the value of [password_studente] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setPasswordStudente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->password_studente !== $v) {
            $this->password_studente = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::PASSWORD_STUDENTE;
        }


        return $this;
    } // setPasswordStudente()

    /**
     * Set the value of [codice_giustificazioni_studente] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceGiustificazioniStudente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_giustificazioni_studente !== $v) {
            $this->codice_giustificazioni_studente = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE;
        }


        return $this;
    } // setCodiceGiustificazioniStudente()

    /**
     * Set the value of [esonero_religione] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsoneroReligione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esonero_religione !== $v) {
            $this->esonero_religione = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESONERO_RELIGIONE;
        }


        return $this;
    } // setEsoneroReligione()

    /**
     * Set the value of [materia_sostitutiva_religione] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMateriaSostitutivaReligione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->materia_sostitutiva_religione !== $v) {
            $this->materia_sostitutiva_religione = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MATERIA_SOSTITUTIVA_RELIGIONE;
        }


        return $this;
    } // setMateriaSostitutivaReligione()

    /**
     * Set the value of [esonero_ed_fisica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsoneroEdFisica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esonero_ed_fisica !== $v) {
            $this->esonero_ed_fisica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESONERO_ED_FISICA;
        }


        return $this;
    } // setEsoneroEdFisica()

    /**
     * Set the value of [materia_sostitutiva_edfisica] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMateriaSostitutivaEdfisica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->materia_sostitutiva_edfisica !== $v) {
            $this->materia_sostitutiva_edfisica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MATERIA_SOSTITUTIVA_EDFISICA;
        }


        return $this;
    } // setMateriaSostitutivaEdfisica()

    /**
     * Set the value of [crediti_terza] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_terza !== $v) {
            $this->crediti_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_TERZA;
        }


        return $this;
    } // setCreditiTerza()

    /**
     * Set the value of [media_voti_terza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMediaVotiTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->media_voti_terza !== $v) {
            $this->media_voti_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MEDIA_VOTI_TERZA;
        }


        return $this;
    } // setMediaVotiTerza()

    /**
     * Set the value of [debiti_terza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDebitiTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->debiti_terza !== $v) {
            $this->debiti_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DEBITI_TERZA;
        }


        return $this;
    } // setDebitiTerza()

    /**
     * Set the value of [crediti_sospesi_terza] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiSospesiTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_sospesi_terza !== $v) {
            $this->crediti_sospesi_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_SOSPESI_TERZA;
        }


        return $this;
    } // setCreditiSospesiTerza()

    /**
     * Set the value of [crediti_reintegrati_terza] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiReintegratiTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_reintegrati_terza !== $v) {
            $this->crediti_reintegrati_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_REINTEGRATI_TERZA;
        }


        return $this;
    } // setCreditiReintegratiTerza()

    /**
     * Set the value of [crediti_quarta] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiQuarta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_quarta !== $v) {
            $this->crediti_quarta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_QUARTA;
        }


        return $this;
    } // setCreditiQuarta()

    /**
     * Set the value of [media_voti_quarta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMediaVotiQuarta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->media_voti_quarta !== $v) {
            $this->media_voti_quarta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MEDIA_VOTI_QUARTA;
        }


        return $this;
    } // setMediaVotiQuarta()

    /**
     * Set the value of [debiti_quarta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDebitiQuarta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->debiti_quarta !== $v) {
            $this->debiti_quarta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DEBITI_QUARTA;
        }


        return $this;
    } // setDebitiQuarta()

    /**
     * Set the value of [crediti_sospesi_quarta] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiSospesiQuarta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_sospesi_quarta !== $v) {
            $this->crediti_sospesi_quarta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_SOSPESI_QUARTA;
        }


        return $this;
    } // setCreditiSospesiQuarta()

    /**
     * Set the value of [crediti_reintegrati_quarta] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiReintegratiQuarta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_reintegrati_quarta !== $v) {
            $this->crediti_reintegrati_quarta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_REINTEGRATI_QUARTA;
        }


        return $this;
    } // setCreditiReintegratiQuarta()

    /**
     * Set the value of [crediti_quinta] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiQuinta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_quinta !== $v) {
            $this->crediti_quinta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_QUINTA;
        }


        return $this;
    } // setCreditiQuinta()

    /**
     * Set the value of [media_voti_quinta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMediaVotiQuinta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->media_voti_quinta !== $v) {
            $this->media_voti_quinta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MEDIA_VOTI_QUINTA;
        }


        return $this;
    } // setMediaVotiQuinta()

    /**
     * Set the value of [crediti_finali_agg] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCreditiFinaliAgg($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->crediti_finali_agg !== $v) {
            $this->crediti_finali_agg = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CREDITI_FINALI_AGG;
        }


        return $this;
    } // setCreditiFinaliAgg()

    /**
     * Set the value of [matricola] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMatricola($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->matricola !== $v) {
            $this->matricola = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MATRICOLA;
        }


        return $this;
    } // setMatricola()

    /**
     * Set the value of [luogo_nascita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setLuogoNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->luogo_nascita !== $v) {
            $this->luogo_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::LUOGO_NASCITA;
        }


        return $this;
    } // setLuogoNascita()

    /**
     * Set the value of [provincia_nascita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setProvinciaNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->provincia_nascita !== $v) {
            $this->provincia_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::PROVINCIA_NASCITA;
        }


        return $this;
    } // setProvinciaNascita()

    /**
     * Set the value of [motivi_crediti_terza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMotiviCreditiTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->motivi_crediti_terza !== $v) {
            $this->motivi_crediti_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MOTIVI_CREDITI_TERZA;
        }


        return $this;
    } // setMotiviCreditiTerza()

    /**
     * Set the value of [motivi_crediti_quarta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMotiviCreditiQuarta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->motivi_crediti_quarta !== $v) {
            $this->motivi_crediti_quarta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MOTIVI_CREDITI_QUARTA;
        }


        return $this;
    } // setMotiviCreditiQuarta()

    /**
     * Set the value of [motivi_crediti_quinta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMotiviCreditiQuinta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->motivi_crediti_quinta !== $v) {
            $this->motivi_crediti_quinta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MOTIVI_CREDITI_QUINTA;
        }


        return $this;
    } // setMotiviCreditiQuinta()

    /**
     * Set the value of [motivi_crediti_agg] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMotiviCreditiAgg($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->motivi_crediti_agg !== $v) {
            $this->motivi_crediti_agg = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MOTIVI_CREDITI_AGG;
        }


        return $this;
    } // setMotiviCreditiAgg()

    /**
     * Set the value of [codice_comune_nascita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceComuneNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_comune_nascita !== $v) {
            $this->codice_comune_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_COMUNE_NASCITA;
        }


        return $this;
    } // setCodiceComuneNascita()

    /**
     * Set the value of [stato_nascita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStatoNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stato_nascita !== $v) {
            $this->stato_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STATO_NASCITA;
        }


        return $this;
    } // setStatoNascita()

    /**
     * Set the value of [cittadinanza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCittadinanza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cittadinanza !== $v) {
            $this->cittadinanza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CITTADINANZA;
        }


        return $this;
    } // setCittadinanza()

    /**
     * Set the value of [seconda_cittadinanza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setSecondaCittadinanza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->seconda_cittadinanza !== $v) {
            $this->seconda_cittadinanza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::SECONDA_CITTADINANZA;
        }


        return $this;
    } // setSecondaCittadinanza()

    /**
     * Set the value of [codice_comune_residenza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceComuneResidenza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_comune_residenza !== $v) {
            $this->codice_comune_residenza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_COMUNE_RESIDENZA;
        }


        return $this;
    } // setCodiceComuneResidenza()

    /**
     * Set the value of [distretto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDistretto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->distretto !== $v) {
            $this->distretto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DISTRETTO;
        }


        return $this;
    } // setDistretto()

    /**
     * Set the value of [codice_fiscale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceFiscale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_fiscale !== $v) {
            $this->codice_fiscale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_FISCALE;
        }


        return $this;
    } // setCodiceFiscale()

    /**
     * Set the value of [medico] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMedico($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->medico !== $v) {
            $this->medico = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MEDICO;
        }


        return $this;
    } // setMedico()

    /**
     * Set the value of [telefono_medico] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTelefonoMedico($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->telefono_medico !== $v) {
            $this->telefono_medico = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TELEFONO_MEDICO;
        }


        return $this;
    } // setTelefonoMedico()

    /**
     * Set the value of [intolleranze_alim] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIntolleranzeAlim($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->intolleranze_alim !== $v) {
            $this->intolleranze_alim = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INTOLLERANZE_ALIM;
        }


        return $this;
    } // setIntolleranzeAlim()

    /**
     * Set the value of [gruppo_sanguigno] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGruppoSanguigno($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->gruppo_sanguigno !== $v) {
            $this->gruppo_sanguigno = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GRUPPO_SANGUIGNO;
        }


        return $this;
    } // setGruppoSanguigno()

    /**
     * Set the value of [gruppo_rh] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGruppoRh($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->gruppo_rh !== $v) {
            $this->gruppo_rh = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GRUPPO_RH;
        }


        return $this;
    } // setGruppoRh()

    /**
     * Set the value of [codice_asl] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceAsl($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_asl !== $v) {
            $this->codice_asl = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_ASL;
        }


        return $this;
    } // setCodiceAsl()

    /**
     * Set the value of [annotazioni] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAnnotazioni($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->annotazioni !== $v) {
            $this->annotazioni = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ANNOTAZIONI;
        }


        return $this;
    } // setAnnotazioni()

    /**
     * Set the value of [stato_civile] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStatoCivile($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->stato_civile !== $v) {
            $this->stato_civile = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STATO_CIVILE;
        }


        return $this;
    } // setStatoCivile()

    /**
     * Set the value of [voto_primo_scritto] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoPrimoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->voto_primo_scritto !== $v) {
            $this->voto_primo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_PRIMO_SCRITTO;
        }


        return $this;
    } // setVotoPrimoScritto()

    /**
     * Set the value of [voto_secondo_scritto] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoSecondoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->voto_secondo_scritto !== $v) {
            $this->voto_secondo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_SECONDO_SCRITTO;
        }


        return $this;
    } // setVotoSecondoScritto()

    /**
     * Set the value of [voto_terzo_scritto] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoTerzoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->voto_terzo_scritto !== $v) {
            $this->voto_terzo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_TERZO_SCRITTO;
        }


        return $this;
    } // setVotoTerzoScritto()

    /**
     * Set the value of [voto_orale] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->voto_orale !== $v) {
            $this->voto_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ORALE;
        }


        return $this;
    } // setVotoOrale()

    /**
     * Set the value of [voto_bonus] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoBonus($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->voto_bonus !== $v) {
            $this->voto_bonus = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_BONUS;
        }


        return $this;
    } // setVotoBonus()

    /**
     * Set the value of [materia_secondo_scr] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setMateriaSecondoScr($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->materia_secondo_scr !== $v) {
            $this->materia_secondo_scr = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::MATERIA_SECONDO_SCR;
        }


        return $this;
    } // setMateriaSecondoScr()

    /**
     * Set the value of [ulteriori_specif_diploma] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setUlterioriSpecifDiploma($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ulteriori_specif_diploma !== $v) {
            $this->ulteriori_specif_diploma = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ULTERIORI_SPECIF_DIPLOMA;
        }


        return $this;
    } // setUlterioriSpecifDiploma()

    /**
     * Set the value of [numero_diploma] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setNumeroDiploma($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->numero_diploma !== $v) {
            $this->numero_diploma = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::NUMERO_DIPLOMA;
        }


        return $this;
    } // setNumeroDiploma()

    /**
     * Set the value of [chi_inserisce] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setChiInserisce($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->chi_inserisce !== $v) {
            $this->chi_inserisce = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CHI_INSERISCE;
        }


        return $this;
    } // setChiInserisce()

    /**
     * Set the value of [data_inserimento] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataInserimento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_inserimento !== $v) {
            $this->data_inserimento = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_INSERIMENTO;
        }


        return $this;
    } // setDataInserimento()

    /**
     * Set the value of [tipo_inserimento] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoInserimento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_inserimento !== $v) {
            $this->tipo_inserimento = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_INSERIMENTO;
        }


        return $this;
    } // setTipoInserimento()

    /**
     * Set the value of [chi_modifica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setChiModifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->chi_modifica !== $v) {
            $this->chi_modifica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CHI_MODIFICA;
        }


        return $this;
    } // setChiModifica()

    /**
     * Set the value of [data_modifica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataModifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_modifica !== $v) {
            $this->data_modifica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_MODIFICA;
        }


        return $this;
    } // setDataModifica()

    /**
     * Set the value of [tipo_modifica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoModifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_modifica !== $v) {
            $this->tipo_modifica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_MODIFICA;
        }


        return $this;
    } // setTipoModifica()

    /**
     * Set the value of [flag_canc] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFlagCanc($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->flag_canc !== $v) {
            $this->flag_canc = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FLAG_CANC;
        }


        return $this;
    } // setFlagCanc()

    /**
     * Set the value of [stato_avanzamento] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStatoAvanzamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stato_avanzamento !== $v) {
            $this->stato_avanzamento = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STATO_AVANZAMENTO;
        }


        return $this;
    } // setStatoAvanzamento()

    /**
     * Set the value of [data_stato_avanzamento] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataStatoAvanzamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_stato_avanzamento !== $v) {
            $this->data_stato_avanzamento = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_STATO_AVANZAMENTO;
        }


        return $this;
    } // setDataStatoAvanzamento()

    /**
     * Set the value of [cap_provincia_nascita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCapProvinciaNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cap_provincia_nascita !== $v) {
            $this->cap_provincia_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CAP_PROVINCIA_NASCITA;
        }


        return $this;
    } // setCapProvinciaNascita()

    /**
     * Set the value of [badge] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setBadge($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->badge !== $v) {
            $this->badge = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::BADGE;
        }


        return $this;
    } // setBadge()

    /**
     * Set the value of [cap_residenza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCapResidenza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cap_residenza !== $v) {
            $this->cap_residenza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CAP_RESIDENZA;
        }


        return $this;
    } // setCapResidenza()

    /**
     * Set the value of [codice_comune_domicilio] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceComuneDomicilio($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_comune_domicilio !== $v) {
            $this->codice_comune_domicilio = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_COMUNE_DOMICILIO;
        }


        return $this;
    } // setCodiceComuneDomicilio()

    /**
     * Set the value of [cap_domicilio] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCapDomicilio($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cap_domicilio !== $v) {
            $this->cap_domicilio = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CAP_DOMICILIO;
        }


        return $this;
    } // setCapDomicilio()

    /**
     * Set the value of [cap_nascita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCapNascita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cap_nascita !== $v) {
            $this->cap_nascita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CAP_NASCITA;
        }


        return $this;
    } // setCapNascita()

    /**
     * Set the value of [indirizzo_domicilio] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIndirizzoDomicilio($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->indirizzo_domicilio !== $v) {
            $this->indirizzo_domicilio = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::INDIRIZZO_DOMICILIO;
        }


        return $this;
    } // setIndirizzoDomicilio()

    /**
     * Set the value of [citta_nascita_straniera] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCittaNascitaStraniera($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->citta_nascita_straniera !== $v) {
            $this->citta_nascita_straniera = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CITTA_NASCITA_STRANIERA;
        }


        return $this;
    } // setCittaNascitaStraniera()

    /**
     * Set the value of [cellulare_allievo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCellulareAllievo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cellulare_allievo !== $v) {
            $this->cellulare_allievo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CELLULARE_ALLIEVO;
        }


        return $this;
    } // setCellulareAllievo()

    /**
     * Set the value of [handicap] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setHandicap($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->handicap !== $v) {
            $this->handicap = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::HANDICAP;
        }


        return $this;
    } // setHandicap()

    /**
     * Set the value of [stato_convittore] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStatoConvittore($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stato_convittore !== $v) {
            $this->stato_convittore = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STATO_CONVITTORE;
        }


        return $this;
    } // setStatoConvittore()

    /**
     * Set the value of [data_ritiro] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataRitiro($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_ritiro !== $v) {
            $this->data_ritiro = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_RITIRO;
        }


        return $this;
    } // setDataRitiro()

    /**
     * Set the value of [voto_ammissione] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoAmmissione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_ammissione !== $v) {
            $this->voto_ammissione = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_AMMISSIONE;
        }


        return $this;
    } // setVotoAmmissione()

    /**
     * Set the value of [differenza_punteggio] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDifferenzaPunteggio($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->differenza_punteggio !== $v) {
            $this->differenza_punteggio = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DIFFERENZA_PUNTEGGIO;
        }


        return $this;
    } // setDifferenzaPunteggio()

    /**
     * Set the value of [voto_qualifica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoQualifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_qualifica !== $v) {
            $this->voto_qualifica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_QUALIFICA;
        }


        return $this;
    } // setVotoQualifica()

    /**
     * Set the value of [voto_esame_sc1_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameSc1Qual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_sc1_qual !== $v) {
            $this->voto_esame_sc1_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_SC1_QUAL;
        }


        return $this;
    } // setVotoEsameSc1Qual()

    /**
     * Set the value of [voto_esame_sc2_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameSc2Qual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_sc2_qual !== $v) {
            $this->voto_esame_sc2_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_SC2_QUAL;
        }


        return $this;
    } // setVotoEsameSc2Qual()

    /**
     * Set the value of [voto_esame_or_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameOrQual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_or_qual !== $v) {
            $this->voto_esame_or_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_OR_QUAL;
        }


        return $this;
    } // setVotoEsameOrQual()

    /**
     * Set the value of [stato_privatista] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStatoPrivatista($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stato_privatista !== $v) {
            $this->stato_privatista = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STATO_PRIVATISTA;
        }


        return $this;
    } // setStatoPrivatista()

    /**
     * Set the value of [foto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFoto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->foto !== $v) {
            $this->foto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FOTO;
        }


        return $this;
    } // setFoto()

    /**
     * Set the value of [rappresentante] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setRappresentante($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->rappresentante !== $v) {
            $this->rappresentante = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::RAPPRESENTANTE;
        }


        return $this;
    } // setRappresentante()

    /**
     * Set the value of [obbligo_formativo] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setObbligoFormativo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->obbligo_formativo !== $v) {
            $this->obbligo_formativo = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::OBBLIGO_FORMATIVO;
        }


        return $this;
    } // setObbligoFormativo()

    /**
     * Set the value of [id_lingua_1] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdLingua1($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_lingua_1 !== $v) {
            $this->id_lingua_1 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_LINGUA_1;
        }


        return $this;
    } // setIdLingua1()

    /**
     * Set the value of [id_lingua_2] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdLingua2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_lingua_2 !== $v) {
            $this->id_lingua_2 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_LINGUA_2;
        }


        return $this;
    } // setIdLingua2()

    /**
     * Set the value of [id_lingua_3] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdLingua3($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_lingua_3 !== $v) {
            $this->id_lingua_3 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_LINGUA_3;
        }


        return $this;
    } // setIdLingua3()

    /**
     * Set the value of [id_lingua_4] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdLingua4($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_lingua_4 !== $v) {
            $this->id_lingua_4 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_LINGUA_4;
        }


        return $this;
    } // setIdLingua4()

    /**
     * Set the value of [id_lingua_5] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdLingua5($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_lingua_5 !== $v) {
            $this->id_lingua_5 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_LINGUA_5;
        }


        return $this;
    } // setIdLingua5()

    /**
     * Set the value of [id_provenienza_scolastica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdProvenienzaScolastica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_provenienza_scolastica !== $v) {
            $this->id_provenienza_scolastica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_PROVENIENZA_SCOLASTICA;
        }


        return $this;
    } // setIdProvenienzaScolastica()

    /**
     * Set the value of [id_scuola_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdScuolaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_scuola_media !== $v) {
            $this->id_scuola_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_SCUOLA_MEDIA;
        }


        return $this;
    } // setIdScuolaMedia()

    /**
     * Set the value of [lingua_scuola_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setLinguaScuolaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->lingua_scuola_media !== $v) {
            $this->lingua_scuola_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA;
        }


        return $this;
    } // setLinguaScuolaMedia()

    /**
     * Set the value of [lingua_scuola_media_2] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setLinguaScuolaMedia2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->lingua_scuola_media_2 !== $v) {
            $this->lingua_scuola_media_2 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA_2;
        }


        return $this;
    } // setLinguaScuolaMedia2()

    /**
     * Set the value of [giudizio_scuola_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioScuolaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_scuola_media !== $v) {
            $this->giudizio_scuola_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_SCUOLA_MEDIA;
        }


        return $this;
    } // setGiudizioScuolaMedia()

    /**
     * Set the value of [trasporto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTrasporto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->trasporto !== $v) {
            $this->trasporto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TRASPORTO;
        }


        return $this;
    } // setTrasporto()

    /**
     * Set the value of [data_iscrizione] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataIscrizione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_iscrizione !== $v) {
            $this->data_iscrizione = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_ISCRIZIONE;
        }


        return $this;
    } // setDataIscrizione()

    /**
     * Set the value of [pei] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setPei($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pei !== $v) {
            $this->pei = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::PEI;
        }


        return $this;
    } // setPei()

    /**
     * Set the value of [ammesso_esame_qualifica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAmmessoEsameQualifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ammesso_esame_qualifica !== $v) {
            $this->ammesso_esame_qualifica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AMMESSO_ESAME_QUALIFICA;
        }


        return $this;
    } // setAmmessoEsameQualifica()

    /**
     * Set the value of [ammesso_esame_quinta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAmmessoEsameQuinta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ammesso_esame_quinta !== $v) {
            $this->ammesso_esame_quinta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AMMESSO_ESAME_QUINTA;
        }


        return $this;
    } // setAmmessoEsameQuinta()

    /**
     * Set the value of [giudizio_ammissione_quinta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioAmmissioneQuinta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_ammissione_quinta !== $v) {
            $this->giudizio_ammissione_quinta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_QUINTA;
        }


        return $this;
    } // setGiudizioAmmissioneQuinta()

    /**
     * Set the value of [grado_handicap] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGradoHandicap($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->grado_handicap !== $v) {
            $this->grado_handicap = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GRADO_HANDICAP;
        }


        return $this;
    } // setGradoHandicap()

    /**
     * Set the value of [tipo_handicap] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoHandicap($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_handicap !== $v) {
            $this->tipo_handicap = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_HANDICAP;
        }


        return $this;
    } // setTipoHandicap()

    /**
     * Set the value of [stato_licenza_maestro] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStatoLicenzaMaestro($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stato_licenza_maestro !== $v) {
            $this->stato_licenza_maestro = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STATO_LICENZA_MAESTRO;
        }


        return $this;
    } // setStatoLicenzaMaestro()

    /**
     * Set the value of [id_studente_sissi] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdStudenteSissi($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_studente_sissi !== $v) {
            $this->id_studente_sissi = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_STUDENTE_SISSI;
        }


        return $this;
    } // setIdStudenteSissi()

    /**
     * Set the value of [badge_rfid] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setBadgeRfid($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->badge_rfid !== $v) {
            $this->badge_rfid = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::BADGE_RFID;
        }


        return $this;
    } // setBadgeRfid()

    /**
     * Set the value of [lode] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setLode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->lode !== $v) {
            $this->lode = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::LODE;
        }


        return $this;
    } // setLode()

    /**
     * Set the value of [distretto_scolastico] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDistrettoScolastico($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->distretto_scolastico !== $v) {
            $this->distretto_scolastico = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DISTRETTO_SCOLASTICO;
        }


        return $this;
    } // setDistrettoScolastico()

    /**
     * Set the value of [giudizio_ammissione_terza] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioAmmissioneTerza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_ammissione_terza !== $v) {
            $this->giudizio_ammissione_terza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_TERZA;
        }


        return $this;
    } // setGiudizioAmmissioneTerza()

    /**
     * Set the value of [esito_prima_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoPrimaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_prima_media !== $v) {
            $this->esito_prima_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_PRIMA_MEDIA;
        }


        return $this;
    } // setEsitoPrimaMedia()

    /**
     * Set the value of [esito_seconda_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoSecondaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_seconda_media !== $v) {
            $this->esito_seconda_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_SECONDA_MEDIA;
        }


        return $this;
    } // setEsitoSecondaMedia()

    /**
     * Set the value of [esito_terza_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoTerzaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_terza_media !== $v) {
            $this->esito_terza_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_TERZA_MEDIA;
        }


        return $this;
    } // setEsitoTerzaMedia()

    /**
     * Set the value of [giudizio_esame_sc1_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioEsameSc1Qual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_esame_sc1_qual !== $v) {
            $this->giudizio_esame_sc1_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_ESAME_SC1_QUAL;
        }


        return $this;
    } // setGiudizioEsameSc1Qual()

    /**
     * Set the value of [giudizio_esame_sc2_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioEsameSc2Qual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_esame_sc2_qual !== $v) {
            $this->giudizio_esame_sc2_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_ESAME_SC2_QUAL;
        }


        return $this;
    } // setGiudizioEsameSc2Qual()

    /**
     * Set the value of [giudizio_esame_or_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioEsameOrQual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_esame_or_qual !== $v) {
            $this->giudizio_esame_or_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_ESAME_OR_QUAL;
        }


        return $this;
    } // setGiudizioEsameOrQual()

    /**
     * Set the value of [giudizio_complessivo_esame_qual] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioComplessivoEsameQual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_complessivo_esame_qual !== $v) {
            $this->giudizio_complessivo_esame_qual = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL;
        }


        return $this;
    } // setGiudizioComplessivoEsameQual()

    /**
     * Set the value of [acconsente_aziende] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAcconsenteAziende($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->acconsente_aziende !== $v) {
            $this->acconsente_aziende = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ACCONSENTE_AZIENDE;
        }


        return $this;
    } // setAcconsenteAziende()

    /**
     * Set the value of [curriculum_prima] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCurriculumPrima($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->curriculum_prima !== $v) {
            $this->curriculum_prima = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CURRICULUM_PRIMA;
        }


        return $this;
    } // setCurriculumPrima()

    /**
     * Set the value of [curriculum_seconda] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCurriculumSeconda($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->curriculum_seconda !== $v) {
            $this->curriculum_seconda = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CURRICULUM_SECONDA;
        }


        return $this;
    } // setCurriculumSeconda()

    /**
     * Set the value of [stage_professionali] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStageProfessionali($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stage_professionali !== $v) {
            $this->stage_professionali = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STAGE_PROFESSIONALI;
        }


        return $this;
    } // setStageProfessionali()

    /**
     * Set the value of [data_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_orale !== $v) {
            $this->data_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_ORALE;
        }


        return $this;
    } // setDataOrale()

    /**
     * Set the value of [ordine_esame_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setOrdineEsameOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ordine_esame_orale !== $v) {
            $this->ordine_esame_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ORDINE_ESAME_ORALE;
        }


        return $this;
    } // setOrdineEsameOrale()

    /**
     * Set the value of [tipo_primo_scritto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoPrimoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_primo_scritto !== $v) {
            $this->tipo_primo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_PRIMO_SCRITTO;
        }


        return $this;
    } // setTipoPrimoScritto()

    /**
     * Set the value of [tipo_secondo_scritto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoSecondoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_secondo_scritto !== $v) {
            $this->tipo_secondo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_SECONDO_SCRITTO;
        }


        return $this;
    } // setTipoSecondoScritto()

    /**
     * Set the value of [tipo_terzo_scritto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoTerzoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_terzo_scritto !== $v) {
            $this->tipo_terzo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_TERZO_SCRITTO;
        }


        return $this;
    } // setTipoTerzoScritto()

    /**
     * Set the value of [unanimita_primo_scritto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setUnanimitaPrimoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unanimita_primo_scritto !== $v) {
            $this->unanimita_primo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::UNANIMITA_PRIMO_SCRITTO;
        }


        return $this;
    } // setUnanimitaPrimoScritto()

    /**
     * Set the value of [unanimita_secondo_scritto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setUnanimitaSecondoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unanimita_secondo_scritto !== $v) {
            $this->unanimita_secondo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::UNANIMITA_SECONDO_SCRITTO;
        }


        return $this;
    } // setUnanimitaSecondoScritto()

    /**
     * Set the value of [unanimita_terzo_scritto] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setUnanimitaTerzoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unanimita_terzo_scritto !== $v) {
            $this->unanimita_terzo_scritto = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::UNANIMITA_TERZO_SCRITTO;
        }


        return $this;
    } // setUnanimitaTerzoScritto()

    /**
     * Set the value of [argomento_scelto_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setArgomentoSceltoOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->argomento_scelto_orale !== $v) {
            $this->argomento_scelto_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ARGOMENTO_SCELTO_ORALE;
        }


        return $this;
    } // setArgomentoSceltoOrale()

    /**
     * Set the value of [area_disc_1_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAreaDisc1Orale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->area_disc_1_orale !== $v) {
            $this->area_disc_1_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AREA_DISC_1_ORALE;
        }


        return $this;
    } // setAreaDisc1Orale()

    /**
     * Set the value of [area_disc_2_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setAreaDisc2Orale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->area_disc_2_orale !== $v) {
            $this->area_disc_2_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::AREA_DISC_2_ORALE;
        }


        return $this;
    } // setAreaDisc2Orale()

    /**
     * Set the value of [disc_elaborati_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDiscElaboratiOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->disc_elaborati_orale !== $v) {
            $this->disc_elaborati_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DISC_ELABORATI_ORALE;
        }


        return $this;
    } // setDiscElaboratiOrale()

    /**
     * Set the value of [unanimita_voto_finale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setUnanimitaVotoFinale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unanimita_voto_finale !== $v) {
            $this->unanimita_voto_finale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::UNANIMITA_VOTO_FINALE;
        }


        return $this;
    } // setUnanimitaVotoFinale()

    /**
     * Set the value of [presente_esame_quinta] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setPresenteEsameQuinta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->presente_esame_quinta !== $v) {
            $this->presente_esame_quinta = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::PRESENTE_ESAME_QUINTA;
        }


        return $this;
    } // setPresenteEsameQuinta()

    /**
     * Set the value of [stampa_badge] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setStampaBadge($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stampa_badge !== $v) {
            $this->stampa_badge = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::STAMPA_BADGE;
        }


        return $this;
    } // setStampaBadge()

    /**
     * Set the value of [id_classe_destinazione] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdClasseDestinazione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_classe_destinazione !== $v) {
            $this->id_classe_destinazione = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_CLASSE_DESTINAZIONE;
        }


        return $this;
    } // setIdClasseDestinazione()

    /**
     * Set the value of [sconto_rette] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setScontoRette($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->sconto_rette !== $v) {
            $this->sconto_rette = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::SCONTO_RETTE;
        }


        return $this;
    } // setScontoRette()

    /**
     * Set the value of [carta_studente_numero] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCartaStudenteNumero($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->carta_studente_numero !== $v) {
            $this->carta_studente_numero = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CARTA_STUDENTE_NUMERO;
        }


        return $this;
    } // setCartaStudenteNumero()

    /**
     * Set the value of [carta_studente_scadenza] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCartaStudenteScadenza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->carta_studente_scadenza !== $v) {
            $this->carta_studente_scadenza = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CARTA_STUDENTE_SCADENZA;
        }


        return $this;
    } // setCartaStudenteScadenza()

    /**
     * Set the value of [esito_corrente_calcolato] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoCorrenteCalcolato($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_corrente_calcolato !== $v) {
            $this->esito_corrente_calcolato = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_CORRENTE_CALCOLATO;
        }


        return $this;
    } // setEsitoCorrenteCalcolato()

    /**
     * Set the value of [id_flusso] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setIdFlusso($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_flusso !== $v) {
            $this->id_flusso = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ID_FLUSSO;
        }


        return $this;
    } // setIdFlusso()

    /**
     * Set the value of [data_aggiornamento_sogei] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataAggiornamentoSogei($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_aggiornamento_sogei !== $v) {
            $this->data_aggiornamento_sogei = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_AGGIORNAMENTO_SOGEI;
        }


        return $this;
    } // setDataAggiornamentoSogei()

    /**
     * Set the value of [codice_alunno_ministeriale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceAlunnoMinisteriale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_alunno_ministeriale !== $v) {
            $this->codice_alunno_ministeriale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_ALUNNO_MINISTERIALE;
        }


        return $this;
    } // setCodiceAlunnoMinisteriale()

    /**
     * Set the value of [flag_cf_fittizio] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFlagCfFittizio($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->flag_cf_fittizio !== $v) {
            $this->flag_cf_fittizio = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FLAG_CF_FITTIZIO;
        }


        return $this;
    } // setFlagCfFittizio()

    /**
     * Set the value of [flag_s2f] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFlagS2f($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->flag_s2f !== $v) {
            $this->flag_s2f = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FLAG_S2F;
        }


        return $this;
    } // setFlagS2f()

    /**
     * Set the value of [codice_stato_sogei] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceStatoSogei($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_stato_sogei !== $v) {
            $this->codice_stato_sogei = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_STATO_SOGEI;
        }


        return $this;
    } // setCodiceStatoSogei()

    /**
     * Set the value of [codice_gruppo_nomade] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCodiceGruppoNomade($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_gruppo_nomade !== $v) {
            $this->codice_gruppo_nomade = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CODICE_GRUPPO_NOMADE;
        }


        return $this;
    } // setCodiceGruppoNomade()

    /**
     * Set the value of [flag_minore_straniero] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFlagMinoreStraniero($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->flag_minore_straniero !== $v) {
            $this->flag_minore_straniero = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FLAG_MINORE_STRANIERO;
        }


        return $this;
    } // setFlagMinoreStraniero()

    /**
     * Set the value of [chiave] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setChiave($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->chiave !== $v) {
            $this->chiave = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CHIAVE;
        }


        return $this;
    } // setChiave()

    /**
     * Set the value of [voto_esame_medie_italiano] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieItaliano($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_italiano !== $v) {
            $this->voto_esame_medie_italiano = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_ITALIANO;
        }


        return $this;
    } // setVotoEsameMedieItaliano()

    /**
     * Set the value of [voto_esame_medie_inglese] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieInglese($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_inglese !== $v) {
            $this->voto_esame_medie_inglese = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_INGLESE;
        }


        return $this;
    } // setVotoEsameMedieInglese()

    /**
     * Set the value of [voto_esame_medie_matematica] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieMatematica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_matematica !== $v) {
            $this->voto_esame_medie_matematica = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_MATEMATICA;
        }


        return $this;
    } // setVotoEsameMedieMatematica()

    /**
     * Set the value of [voto_esame_medie_seconda_lingua] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieSecondaLingua($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_seconda_lingua !== $v) {
            $this->voto_esame_medie_seconda_lingua = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA;
        }


        return $this;
    } // setVotoEsameMedieSecondaLingua()

    /**
     * Set the value of [voto_esame_medie_invalsi_ita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieInvalsiIta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_invalsi_ita !== $v) {
            $this->voto_esame_medie_invalsi_ita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA;
        }


        return $this;
    } // setVotoEsameMedieInvalsiIta()

    /**
     * Set the value of [voto_esame_medie_invalsi_mat] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieInvalsiMat($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_invalsi_mat !== $v) {
            $this->voto_esame_medie_invalsi_mat = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT;
        }


        return $this;
    } // setVotoEsameMedieInvalsiMat()

    /**
     * Set the value of [voto_esame_medie_orale] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoEsameMedieOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_esame_medie_orale !== $v) {
            $this->voto_esame_medie_orale = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_ESAME_MEDIE_ORALE;
        }


        return $this;
    } // setVotoEsameMedieOrale()

    /**
     * Set the value of [voto_ammissione_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setVotoAmmissioneMedie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->voto_ammissione_medie !== $v) {
            $this->voto_ammissione_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::VOTO_AMMISSIONE_MEDIE;
        }


        return $this;
    } // setVotoAmmissioneMedie()

    /**
     * Set the value of [esito_prima_elementare] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoPrimaElementare($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_prima_elementare !== $v) {
            $this->esito_prima_elementare = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_PRIMA_ELEMENTARE;
        }


        return $this;
    } // setEsitoPrimaElementare()

    /**
     * Set the value of [esito_seconda_elementare] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoSecondaElementare($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_seconda_elementare !== $v) {
            $this->esito_seconda_elementare = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_SECONDA_ELEMENTARE;
        }


        return $this;
    } // setEsitoSecondaElementare()

    /**
     * Set the value of [esito_terza_elementare] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoTerzaElementare($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_terza_elementare !== $v) {
            $this->esito_terza_elementare = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_TERZA_ELEMENTARE;
        }


        return $this;
    } // setEsitoTerzaElementare()

    /**
     * Set the value of [esito_quarta_elementare] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoQuartaElementare($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_quarta_elementare !== $v) {
            $this->esito_quarta_elementare = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_QUARTA_ELEMENTARE;
        }


        return $this;
    } // setEsitoQuartaElementare()

    /**
     * Set the value of [esito_quinta_elementare] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setEsitoQuintaElementare($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->esito_quinta_elementare !== $v) {
            $this->esito_quinta_elementare = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ESITO_QUINTA_ELEMENTARE;
        }


        return $this;
    } // setEsitoQuintaElementare()

    /**
     * Set the value of [tipo_voto_esame_medie_italiano] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoVotoEsameMedieItaliano($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_voto_esame_medie_italiano !== $v) {
            $this->tipo_voto_esame_medie_italiano = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO;
        }


        return $this;
    } // setTipoVotoEsameMedieItaliano()

    /**
     * Set the value of [tipo_voto_esame_medie_inglese] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTipoVotoEsameMedieInglese($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_voto_esame_medie_inglese !== $v) {
            $this->tipo_voto_esame_medie_inglese = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE;
        }


        return $this;
    } // setTipoVotoEsameMedieInglese()

    /**
     * Set the value of [giudizio_1_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizio1Medie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_1_medie !== $v) {
            $this->giudizio_1_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_1_MEDIE;
        }


        return $this;
    } // setGiudizio1Medie()

    /**
     * Set the value of [giudizio_2_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizio2Medie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_2_medie !== $v) {
            $this->giudizio_2_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_2_MEDIE;
        }


        return $this;
    } // setGiudizio2Medie()

    /**
     * Set the value of [giudizio_3_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizio3Medie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_3_medie !== $v) {
            $this->giudizio_3_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_3_MEDIE;
        }


        return $this;
    } // setGiudizio3Medie()

    /**
     * Set the value of [argomenti_orali_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setArgomentiOraliMedie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->argomenti_orali_medie !== $v) {
            $this->argomenti_orali_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::ARGOMENTI_ORALI_MEDIE;
        }


        return $this;
    } // setArgomentiOraliMedie()

    /**
     * Set the value of [giudizio_finale_1_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioFinale1Medie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_finale_1_medie !== $v) {
            $this->giudizio_finale_1_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_FINALE_1_MEDIE;
        }


        return $this;
    } // setGiudizioFinale1Medie()

    /**
     * Set the value of [giudizio_finale_2_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioFinale2Medie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_finale_2_medie !== $v) {
            $this->giudizio_finale_2_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_FINALE_2_MEDIE;
        }


        return $this;
    } // setGiudizioFinale2Medie()

    /**
     * Set the value of [giudizio_finale_3_medie] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioFinale3Medie($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_finale_3_medie !== $v) {
            $this->giudizio_finale_3_medie = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_FINALE_3_MEDIE;
        }


        return $this;
    } // setGiudizioFinale3Medie()

    /**
     * Set the value of [consiglio_terza_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setConsiglioTerzaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->consiglio_terza_media !== $v) {
            $this->consiglio_terza_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CONSIGLIO_TERZA_MEDIA;
        }


        return $this;
    } // setConsiglioTerzaMedia()

    /**
     * Set the value of [giudizio_sintetico_esame_terza_media] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setGiudizioSinteticoEsameTerzaMedia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->giudizio_sintetico_esame_terza_media !== $v) {
            $this->giudizio_sintetico_esame_terza_media = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA;
        }


        return $this;
    } // setGiudizioSinteticoEsameTerzaMedia()

    /**
     * Set the value of [data_arrivo_in_italia] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataArrivoInItalia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->data_arrivo_in_italia !== $v) {
            $this->data_arrivo_in_italia = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_ARRIVO_IN_ITALIA;
        }


        return $this;
    } // setDataArrivoInItalia()

    /**
     * Set the value of [frequenza_asilo_nido] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFrequenzaAsiloNido($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->frequenza_asilo_nido !== $v) {
            $this->frequenza_asilo_nido = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FREQUENZA_ASILO_NIDO;
        }


        return $this;
    } // setFrequenzaAsiloNido()

    /**
     * Set the value of [frequenza_scuola_materna] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setFrequenzaScuolaMaterna($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->frequenza_scuola_materna !== $v) {
            $this->frequenza_scuola_materna = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::FREQUENZA_SCUOLA_MATERNA;
        }


        return $this;
    } // setFrequenzaScuolaMaterna()

    /**
     * Set the value of [data_aggiornamento_sidi] column.
     *
     * @param  int $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setDataAggiornamentoSidi($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->data_aggiornamento_sidi !== $v) {
            $this->data_aggiornamento_sidi = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::DATA_AGGIORNAMENTO_SIDI;
        }


        return $this;
    } // setDataAggiornamentoSidi()

    /**
     * Set the value of [cmp_sup_val_ita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupValIta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_val_ita !== $v) {
            $this->cmp_sup_val_ita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_VAL_ITA;
        }


        return $this;
    } // setCmpSupValIta()

    /**
     * Set the value of [cmp_sup_txt_ita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupTxtIta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_txt_ita !== $v) {
            $this->cmp_sup_txt_ita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_TXT_ITA;
        }


        return $this;
    } // setCmpSupTxtIta()

    /**
     * Set the value of [cmp_sup_val_ing] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupValIng($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_val_ing !== $v) {
            $this->cmp_sup_val_ing = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_VAL_ING;
        }


        return $this;
    } // setCmpSupValIng()

    /**
     * Set the value of [cmp_sup_txt_ing] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupTxtIng($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_txt_ing !== $v) {
            $this->cmp_sup_txt_ing = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_TXT_ING;
        }


        return $this;
    } // setCmpSupTxtIng()

    /**
     * Set the value of [cmp_sup_val_altri] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupValAltri($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_val_altri !== $v) {
            $this->cmp_sup_val_altri = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_VAL_ALTRI;
        }


        return $this;
    } // setCmpSupValAltri()

    /**
     * Set the value of [cmp_sup_txt_altri] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupTxtAltri($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_txt_altri !== $v) {
            $this->cmp_sup_txt_altri = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_TXT_ALTRI;
        }


        return $this;
    } // setCmpSupTxtAltri()

    /**
     * Set the value of [cmp_sup_val_mat] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupValMat($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_val_mat !== $v) {
            $this->cmp_sup_val_mat = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_VAL_MAT;
        }


        return $this;
    } // setCmpSupValMat()

    /**
     * Set the value of [cmp_sup_txt_mat] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupTxtMat($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_txt_mat !== $v) {
            $this->cmp_sup_txt_mat = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_TXT_MAT;
        }


        return $this;
    } // setCmpSupTxtMat()

    /**
     * Set the value of [cmp_sup_val_sci_tec] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupValSciTec($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_val_sci_tec !== $v) {
            $this->cmp_sup_val_sci_tec = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_VAL_SCI_TEC;
        }


        return $this;
    } // setCmpSupValSciTec()

    /**
     * Set the value of [cmp_sup_txt_sci_tec] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupTxtSciTec($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_txt_sci_tec !== $v) {
            $this->cmp_sup_txt_sci_tec = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_TXT_SCI_TEC;
        }


        return $this;
    } // setCmpSupTxtSciTec()

    /**
     * Set the value of [cmp_sup_val_sto_soc] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupValStoSoc($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_val_sto_soc !== $v) {
            $this->cmp_sup_val_sto_soc = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_VAL_STO_SOC;
        }


        return $this;
    } // setCmpSupValStoSoc()

    /**
     * Set the value of [cmp_sup_txt_sto_soc] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpSupTxtStoSoc($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_sup_txt_sto_soc !== $v) {
            $this->cmp_sup_txt_sto_soc = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_SUP_TXT_STO_SOC;
        }


        return $this;
    } // setCmpSupTxtStoSoc()

    /**
     * Set the value of [cmp_med_val_ita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValIta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_ita !== $v) {
            $this->cmp_med_val_ita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_ITA;
        }


        return $this;
    } // setCmpMedValIta()

    /**
     * Set the value of [cmp_med_txt_ita] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtIta($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_ita !== $v) {
            $this->cmp_med_txt_ita = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_ITA;
        }


        return $this;
    } // setCmpMedTxtIta()

    /**
     * Set the value of [cmp_med_val_ing] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValIng($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_ing !== $v) {
            $this->cmp_med_val_ing = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_ING;
        }


        return $this;
    } // setCmpMedValIng()

    /**
     * Set the value of [cmp_med_txt_ing] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtIng($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_ing !== $v) {
            $this->cmp_med_txt_ing = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_ING;
        }


        return $this;
    } // setCmpMedTxtIng()

    /**
     * Set the value of [cmp_med_val_altri] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValAltri($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_altri !== $v) {
            $this->cmp_med_val_altri = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_ALTRI;
        }


        return $this;
    } // setCmpMedValAltri()

    /**
     * Set the value of [cmp_med_txt_altri] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtAltri($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_altri !== $v) {
            $this->cmp_med_txt_altri = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_ALTRI;
        }


        return $this;
    } // setCmpMedTxtAltri()

    /**
     * Set the value of [cmp_med_val_mat] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValMat($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_mat !== $v) {
            $this->cmp_med_val_mat = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_MAT;
        }


        return $this;
    } // setCmpMedValMat()

    /**
     * Set the value of [cmp_med_txt_mat] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtMat($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_mat !== $v) {
            $this->cmp_med_txt_mat = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_MAT;
        }


        return $this;
    } // setCmpMedTxtMat()

    /**
     * Set the value of [cmp_med_val_sci_tec] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValSciTec($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_sci_tec !== $v) {
            $this->cmp_med_val_sci_tec = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_SCI_TEC;
        }


        return $this;
    } // setCmpMedValSciTec()

    /**
     * Set the value of [cmp_med_txt_sci_tec] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtSciTec($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_sci_tec !== $v) {
            $this->cmp_med_txt_sci_tec = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_SCI_TEC;
        }


        return $this;
    } // setCmpMedTxtSciTec()

    /**
     * Set the value of [cmp_med_val_sto_soc] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValStoSoc($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_sto_soc !== $v) {
            $this->cmp_med_val_sto_soc = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_STO_SOC;
        }


        return $this;
    } // setCmpMedValStoSoc()

    /**
     * Set the value of [cmp_med_txt_sto_soc] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtStoSoc($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_sto_soc !== $v) {
            $this->cmp_med_txt_sto_soc = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_STO_SOC;
        }


        return $this;
    } // setCmpMedTxtStoSoc()

    /**
     * Set the value of [cmp_med_val_l2] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValL2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_l2 !== $v) {
            $this->cmp_med_val_l2 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_L2;
        }


        return $this;
    } // setCmpMedValL2()

    /**
     * Set the value of [cmp_med_txt_l2] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtL2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_l2 !== $v) {
            $this->cmp_med_txt_l2 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_L2;
        }


        return $this;
    } // setCmpMedTxtL2()

    /**
     * Set the value of [cmp_med_val_l3] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValL3($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_l3 !== $v) {
            $this->cmp_med_val_l3 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_L3;
        }


        return $this;
    } // setCmpMedValL3()

    /**
     * Set the value of [cmp_med_txt_l3] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtL3($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_l3 !== $v) {
            $this->cmp_med_txt_l3 = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_L3;
        }


        return $this;
    } // setCmpMedTxtL3()

    /**
     * Set the value of [cmp_med_val_arte] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValArte($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_arte !== $v) {
            $this->cmp_med_val_arte = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_ARTE;
        }


        return $this;
    } // setCmpMedValArte()

    /**
     * Set the value of [cmp_med_txt_arte] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtArte($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_arte !== $v) {
            $this->cmp_med_txt_arte = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_ARTE;
        }


        return $this;
    } // setCmpMedTxtArte()

    /**
     * Set the value of [cmp_med_val_mus] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValMus($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_mus !== $v) {
            $this->cmp_med_val_mus = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_MUS;
        }


        return $this;
    } // setCmpMedValMus()

    /**
     * Set the value of [cmp_med_txt_mus] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtMus($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_mus !== $v) {
            $this->cmp_med_txt_mus = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_MUS;
        }


        return $this;
    } // setCmpMedTxtMus()

    /**
     * Set the value of [cmp_med_val_mot] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedValMot($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_val_mot !== $v) {
            $this->cmp_med_val_mot = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_VAL_MOT;
        }


        return $this;
    } // setCmpMedValMot()

    /**
     * Set the value of [cmp_med_txt_mot] column.
     *
     * @param  string $v new value
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setCmpMedTxtMot($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->cmp_med_txt_mot !== $v) {
            $this->cmp_med_txt_mot = $v;
            $this->modifiedColumns[] = StudentiCompletiPeer::CMP_MED_TXT_MOT;
        }


        return $this;
    } // setCmpMedTxtMot()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->nome !== 'STUDENTE IGNOTO') {
                return false;
            }

            if ($this->cognome !== 'STUDENTE IGNOTO') {
                return false;
            }

            if ($this->indirizzo !== '') {
                return false;
            }

            if ($this->citta !== '') {
                return false;
            }

            if ($this->cap !== '') {
                return false;
            }

            if ($this->provincia !== '') {
                return false;
            }

            if ($this->sesso !== '') {
                return false;
            }

            if ($this->telefono !== '') {
                return false;
            }

            if ($this->cellulare1 !== '') {
                return false;
            }

            if ($this->cellulare2 !== '') {
                return false;
            }

            if ($this->email1 !== '') {
                return false;
            }

            if ($this->email2 !== '') {
                return false;
            }

            if ($this->invio_email !== '0') {
                return false;
            }

            if ($this->invio_email_cumulativo !== '0') {
                return false;
            }

            if ($this->invio_email_parametrico !== '0') {
                return false;
            }

            if ($this->invio_email_temporale !== '0') {
                return false;
            }

            if ($this->tipo_sms !== '0') {
                return false;
            }

            if ($this->tipo_sms_cumulativo !== '0') {
                return false;
            }

            if ($this->tipo_sms_parametrico !== '0') {
                return false;
            }

            if ($this->tipo_sms_temporale !== '0') {
                return false;
            }

            if ($this->aut_entrata_ritardo !== '0') {
                return false;
            }

            if ($this->aut_uscita_anticipo !== '0') {
                return false;
            }

            if ($this->aut_pomeriggio !== '0') {
                return false;
            }

            if ($this->acconsente !== '0') {
                return false;
            }

            if ($this->ritirato !== '0') {
                return false;
            }

            if ($this->data_nascita !== 0) {
                return false;
            }

            if ($this->codice_studente !== '') {
                return false;
            }

            if ($this->password_studente !== '') {
                return false;
            }

            if ($this->codice_giustificazioni_studente !== '0') {
                return false;
            }

            if ($this->esonero_religione !== '0') {
                return false;
            }

            if ($this->materia_sostitutiva_religione !== 0) {
                return false;
            }

            if ($this->esonero_ed_fisica !== '0') {
                return false;
            }

            if ($this->materia_sostitutiva_edfisica !== 0) {
                return false;
            }

            if ($this->crediti_terza !== 0) {
                return false;
            }

            if ($this->media_voti_terza !== '0') {
                return false;
            }

            if ($this->debiti_terza !== '0') {
                return false;
            }

            if ($this->crediti_sospesi_terza !== 0) {
                return false;
            }

            if ($this->crediti_reintegrati_terza !== 0) {
                return false;
            }

            if ($this->crediti_quarta !== 0) {
                return false;
            }

            if ($this->media_voti_quarta !== '0') {
                return false;
            }

            if ($this->debiti_quarta !== '0') {
                return false;
            }

            if ($this->crediti_sospesi_quarta !== 0) {
                return false;
            }

            if ($this->crediti_reintegrati_quarta !== 0) {
                return false;
            }

            if ($this->crediti_quinta !== 0) {
                return false;
            }

            if ($this->media_voti_quinta !== '0') {
                return false;
            }

            if ($this->crediti_finali_agg !== 0) {
                return false;
            }

            if ($this->matricola !== '0') {
                return false;
            }

            if ($this->luogo_nascita !== '') {
                return false;
            }

            if ($this->provincia_nascita !== '') {
                return false;
            }

            if ($this->motivi_crediti_terza !== '') {
                return false;
            }

            if ($this->motivi_crediti_quarta !== '') {
                return false;
            }

            if ($this->motivi_crediti_quinta !== '') {
                return false;
            }

            if ($this->motivi_crediti_agg !== '') {
                return false;
            }

            if ($this->codice_comune_nascita !== '') {
                return false;
            }

            if ($this->stato_nascita !== '') {
                return false;
            }

            if ($this->cittadinanza !== '-1') {
                return false;
            }

            if ($this->seconda_cittadinanza !== '-1') {
                return false;
            }

            if ($this->codice_comune_residenza !== '') {
                return false;
            }

            if ($this->distretto !== '') {
                return false;
            }

            if ($this->codice_fiscale !== '') {
                return false;
            }

            if ($this->medico !== '') {
                return false;
            }

            if ($this->telefono_medico !== '') {
                return false;
            }

            if ($this->intolleranze_alim !== '') {
                return false;
            }

            if ($this->gruppo_sanguigno !== '') {
                return false;
            }

            if ($this->gruppo_rh !== '') {
                return false;
            }

            if ($this->codice_asl !== '') {
                return false;
            }

            if ($this->annotazioni !== '') {
                return false;
            }

            if ($this->stato_civile !== 0) {
                return false;
            }

            if ($this->voto_primo_scritto !== 0) {
                return false;
            }

            if ($this->voto_secondo_scritto !== 0) {
                return false;
            }

            if ($this->voto_terzo_scritto !== 0) {
                return false;
            }

            if ($this->voto_orale !== 0) {
                return false;
            }

            if ($this->voto_bonus !== 0) {
                return false;
            }

            if ($this->materia_secondo_scr !== '') {
                return false;
            }

            if ($this->ulteriori_specif_diploma !== '') {
                return false;
            }

            if ($this->numero_diploma !== 0) {
                return false;
            }

            if ($this->chi_inserisce !== '(-1)') {
                return false;
            }

            if ($this->data_inserimento !== '0') {
                return false;
            }

            if ($this->tipo_inserimento !== '') {
                return false;
            }

            if ($this->chi_modifica !== '(-1)') {
                return false;
            }

            if ($this->data_modifica !== '0') {
                return false;
            }

            if ($this->tipo_modifica !== '') {
                return false;
            }

            if ($this->flag_canc !== '0') {
                return false;
            }

            if ($this->stato_avanzamento !== '') {
                return false;
            }

            if ($this->data_stato_avanzamento !== '0') {
                return false;
            }

            if ($this->cap_provincia_nascita !== '') {
                return false;
            }

            if ($this->badge !== '0') {
                return false;
            }

            if ($this->cap_residenza !== '') {
                return false;
            }

            if ($this->codice_comune_domicilio !== '') {
                return false;
            }

            if ($this->cap_domicilio !== '') {
                return false;
            }

            if ($this->cap_nascita !== '') {
                return false;
            }

            if ($this->indirizzo_domicilio !== '') {
                return false;
            }

            if ($this->citta_nascita_straniera !== '') {
                return false;
            }

            if ($this->cellulare_allievo !== '') {
                return false;
            }

            if ($this->handicap !== 'NO') {
                return false;
            }

            if ($this->stato_convittore !== 'NO') {
                return false;
            }

            if ($this->data_ritiro !== '0') {
                return false;
            }

            if ($this->voto_ammissione !== '0') {
                return false;
            }

            if ($this->differenza_punteggio !== '0') {
                return false;
            }

            if ($this->voto_qualifica !== '0') {
                return false;
            }

            if ($this->voto_esame_sc1_qual !== '0') {
                return false;
            }

            if ($this->voto_esame_sc2_qual !== '0') {
                return false;
            }

            if ($this->voto_esame_or_qual !== '0') {
                return false;
            }

            if ($this->stato_privatista !== 'NO') {
                return false;
            }

            if ($this->foto !== '') {
                return false;
            }

            if ($this->rappresentante !== 'NO#NO#NO') {
                return false;
            }

            if ($this->obbligo_formativo !== '01') {
                return false;
            }

            if ($this->id_lingua_1 !== '(-1)') {
                return false;
            }

            if ($this->id_lingua_2 !== '(-1)') {
                return false;
            }

            if ($this->id_lingua_3 !== '(-1)') {
                return false;
            }

            if ($this->id_lingua_4 !== '(-1)') {
                return false;
            }

            if ($this->id_lingua_5 !== '(-1)') {
                return false;
            }

            if ($this->id_provenienza_scolastica !== '') {
                return false;
            }

            if ($this->id_scuola_media !== '') {
                return false;
            }

            if ($this->lingua_scuola_media !== '') {
                return false;
            }

            if ($this->lingua_scuola_media_2 !== '') {
                return false;
            }

            if ($this->giudizio_scuola_media !== '') {
                return false;
            }

            if ($this->trasporto !== '') {
                return false;
            }

            if ($this->data_iscrizione !== '0') {
                return false;
            }

            if ($this->pei !== 'NO') {
                return false;
            }

            if ($this->ammesso_esame_qualifica !== '--') {
                return false;
            }

            if ($this->ammesso_esame_quinta !== 'NO') {
                return false;
            }

            if ($this->giudizio_ammissione_quinta !== '') {
                return false;
            }

            if ($this->grado_handicap !== '0') {
                return false;
            }

            if ($this->tipo_handicap !== '') {
                return false;
            }

            if ($this->stato_licenza_maestro !== 'NO') {
                return false;
            }

            if ($this->id_studente_sissi !== '') {
                return false;
            }

            if ($this->badge_rfid !== '') {
                return false;
            }

            if ($this->lode !== 'NO') {
                return false;
            }

            if ($this->distretto_scolastico !== '') {
                return false;
            }

            if ($this->giudizio_ammissione_terza !== '') {
                return false;
            }

            if ($this->esito_prima_media !== 'NO') {
                return false;
            }

            if ($this->esito_seconda_media !== 'NO') {
                return false;
            }

            if ($this->esito_terza_media !== 'NO') {
                return false;
            }

            if ($this->giudizio_esame_sc1_qual !== '') {
                return false;
            }

            if ($this->giudizio_esame_sc2_qual !== '') {
                return false;
            }

            if ($this->giudizio_esame_or_qual !== '') {
                return false;
            }

            if ($this->giudizio_complessivo_esame_qual !== '') {
                return false;
            }

            if ($this->acconsente_aziende !== 1) {
                return false;
            }

            if ($this->curriculum_prima !== '0') {
                return false;
            }

            if ($this->curriculum_seconda !== '0') {
                return false;
            }

            if ($this->stage_professionali !== '0') {
                return false;
            }

            if ($this->data_orale !== '0') {
                return false;
            }

            if ($this->ordine_esame_orale !== '0') {
                return false;
            }

            if ($this->tipo_primo_scritto !== '') {
                return false;
            }

            if ($this->tipo_secondo_scritto !== '') {
                return false;
            }

            if ($this->tipo_terzo_scritto !== '') {
                return false;
            }

            if ($this->unanimita_primo_scritto !== '') {
                return false;
            }

            if ($this->unanimita_secondo_scritto !== '') {
                return false;
            }

            if ($this->unanimita_terzo_scritto !== '') {
                return false;
            }

            if ($this->argomento_scelto_orale !== '') {
                return false;
            }

            if ($this->area_disc_1_orale !== '') {
                return false;
            }

            if ($this->area_disc_2_orale !== '') {
                return false;
            }

            if ($this->disc_elaborati_orale !== '') {
                return false;
            }

            if ($this->unanimita_voto_finale !== '') {
                return false;
            }

            if ($this->presente_esame_quinta !== 'SI') {
                return false;
            }

            if ($this->stampa_badge !== 'NO') {
                return false;
            }

            if ($this->id_classe_destinazione !== 0) {
                return false;
            }

            if ($this->sconto_rette !== 0) {
                return false;
            }

            if ($this->carta_studente_numero !== '0') {
                return false;
            }

            if ($this->carta_studente_scadenza !== 0) {
                return false;
            }

            if ($this->esito_corrente_calcolato !== '') {
                return false;
            }

            if ($this->id_flusso !== '0') {
                return false;
            }

            if ($this->data_aggiornamento_sogei !== '') {
                return false;
            }

            if ($this->codice_alunno_ministeriale !== '') {
                return false;
            }

            if ($this->flag_cf_fittizio !== 0) {
                return false;
            }

            if ($this->flag_s2f !== '') {
                return false;
            }

            if ($this->codice_stato_sogei !== '') {
                return false;
            }

            if ($this->codice_gruppo_nomade !== '') {
                return false;
            }

            if ($this->flag_minore_straniero !== 0) {
                return false;
            }

            if ($this->voto_esame_medie_italiano !== '') {
                return false;
            }

            if ($this->voto_esame_medie_inglese !== '') {
                return false;
            }

            if ($this->voto_esame_medie_matematica !== '') {
                return false;
            }

            if ($this->voto_esame_medie_seconda_lingua !== '') {
                return false;
            }

            if ($this->voto_esame_medie_invalsi_ita !== '') {
                return false;
            }

            if ($this->voto_esame_medie_invalsi_mat !== '') {
                return false;
            }

            if ($this->voto_esame_medie_orale !== '') {
                return false;
            }

            if ($this->voto_ammissione_medie !== '') {
                return false;
            }

            if ($this->esito_prima_elementare !== '') {
                return false;
            }

            if ($this->esito_seconda_elementare !== '') {
                return false;
            }

            if ($this->esito_terza_elementare !== '') {
                return false;
            }

            if ($this->esito_quarta_elementare !== '') {
                return false;
            }

            if ($this->esito_quinta_elementare !== '') {
                return false;
            }

            if ($this->tipo_voto_esame_medie_italiano !== '') {
                return false;
            }

            if ($this->tipo_voto_esame_medie_inglese !== '') {
                return false;
            }

            if ($this->giudizio_1_medie !== '') {
                return false;
            }

            if ($this->giudizio_2_medie !== '') {
                return false;
            }

            if ($this->giudizio_3_medie !== '') {
                return false;
            }

            if ($this->argomenti_orali_medie !== '') {
                return false;
            }

            if ($this->giudizio_finale_1_medie !== '') {
                return false;
            }

            if ($this->giudizio_finale_2_medie !== '') {
                return false;
            }

            if ($this->giudizio_finale_3_medie !== '') {
                return false;
            }

            if ($this->consiglio_terza_media !== '') {
                return false;
            }

            if ($this->giudizio_sintetico_esame_terza_media !== '') {
                return false;
            }

            if ($this->data_arrivo_in_italia !== 0) {
                return false;
            }

            if ($this->frequenza_asilo_nido !== 0) {
                return false;
            }

            if ($this->frequenza_scuola_materna !== 0) {
                return false;
            }

            if ($this->data_aggiornamento_sidi !== 0) {
                return false;
            }

            if ($this->cmp_sup_val_ita !== '') {
                return false;
            }

            if ($this->cmp_sup_txt_ita !== '') {
                return false;
            }

            if ($this->cmp_sup_val_ing !== '') {
                return false;
            }

            if ($this->cmp_sup_txt_ing !== '') {
                return false;
            }

            if ($this->cmp_sup_val_altri !== '') {
                return false;
            }

            if ($this->cmp_sup_txt_altri !== '') {
                return false;
            }

            if ($this->cmp_sup_val_mat !== '') {
                return false;
            }

            if ($this->cmp_sup_txt_mat !== '') {
                return false;
            }

            if ($this->cmp_sup_val_sci_tec !== '') {
                return false;
            }

            if ($this->cmp_sup_txt_sci_tec !== '') {
                return false;
            }

            if ($this->cmp_sup_val_sto_soc !== '') {
                return false;
            }

            if ($this->cmp_sup_txt_sto_soc !== '') {
                return false;
            }

            if ($this->cmp_med_val_ita !== '') {
                return false;
            }

            if ($this->cmp_med_txt_ita !== '') {
                return false;
            }

            if ($this->cmp_med_val_ing !== '') {
                return false;
            }

            if ($this->cmp_med_txt_ing !== '') {
                return false;
            }

            if ($this->cmp_med_val_altri !== '') {
                return false;
            }

            if ($this->cmp_med_txt_altri !== '') {
                return false;
            }

            if ($this->cmp_med_val_mat !== '') {
                return false;
            }

            if ($this->cmp_med_txt_mat !== '') {
                return false;
            }

            if ($this->cmp_med_val_sci_tec !== '') {
                return false;
            }

            if ($this->cmp_med_txt_sci_tec !== '') {
                return false;
            }

            if ($this->cmp_med_val_sto_soc !== '') {
                return false;
            }

            if ($this->cmp_med_txt_sto_soc !== '') {
                return false;
            }

            if ($this->cmp_med_val_l2 !== '') {
                return false;
            }

            if ($this->cmp_med_txt_l2 !== '') {
                return false;
            }

            if ($this->cmp_med_val_l3 !== '') {
                return false;
            }

            if ($this->cmp_med_txt_l3 !== '') {
                return false;
            }

            if ($this->cmp_med_val_arte !== '') {
                return false;
            }

            if ($this->cmp_med_txt_arte !== '') {
                return false;
            }

            if ($this->cmp_med_val_mus !== '') {
                return false;
            }

            if ($this->cmp_med_txt_mus !== '') {
                return false;
            }

            if ($this->cmp_med_val_mot !== '') {
                return false;
            }

            if ($this->cmp_med_txt_mot !== '') {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id_studente = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->nome = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->cognome = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->indirizzo = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->citta = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->cap = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->provincia = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->sesso = ($row[$startcol + 7] !== null) ? (string) $row[$startcol + 7] : null;
            $this->telefono = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->cellulare1 = ($row[$startcol + 9] !== null) ? (string) $row[$startcol + 9] : null;
            $this->cellulare2 = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->email1 = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->email2 = ($row[$startcol + 12] !== null) ? (string) $row[$startcol + 12] : null;
            $this->invio_email = ($row[$startcol + 13] !== null) ? (string) $row[$startcol + 13] : null;
            $this->invio_email_cumulativo = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->invio_email_parametrico = ($row[$startcol + 15] !== null) ? (string) $row[$startcol + 15] : null;
            $this->invio_email_temporale = ($row[$startcol + 16] !== null) ? (string) $row[$startcol + 16] : null;
            $this->tipo_sms = ($row[$startcol + 17] !== null) ? (string) $row[$startcol + 17] : null;
            $this->tipo_sms_cumulativo = ($row[$startcol + 18] !== null) ? (string) $row[$startcol + 18] : null;
            $this->tipo_sms_parametrico = ($row[$startcol + 19] !== null) ? (string) $row[$startcol + 19] : null;
            $this->tipo_sms_temporale = ($row[$startcol + 20] !== null) ? (string) $row[$startcol + 20] : null;
            $this->aut_entrata_ritardo = ($row[$startcol + 21] !== null) ? (string) $row[$startcol + 21] : null;
            $this->aut_uscita_anticipo = ($row[$startcol + 22] !== null) ? (string) $row[$startcol + 22] : null;
            $this->aut_pomeriggio = ($row[$startcol + 23] !== null) ? (string) $row[$startcol + 23] : null;
            $this->acconsente = ($row[$startcol + 24] !== null) ? (string) $row[$startcol + 24] : null;
            $this->ritirato = ($row[$startcol + 25] !== null) ? (string) $row[$startcol + 25] : null;
            $this->data_nascita = ($row[$startcol + 26] !== null) ? (int) $row[$startcol + 26] : null;
            $this->codice_studente = ($row[$startcol + 27] !== null) ? (string) $row[$startcol + 27] : null;
            $this->password_studente = ($row[$startcol + 28] !== null) ? (string) $row[$startcol + 28] : null;
            $this->codice_giustificazioni_studente = ($row[$startcol + 29] !== null) ? (string) $row[$startcol + 29] : null;
            $this->esonero_religione = ($row[$startcol + 30] !== null) ? (string) $row[$startcol + 30] : null;
            $this->materia_sostitutiva_religione = ($row[$startcol + 31] !== null) ? (int) $row[$startcol + 31] : null;
            $this->esonero_ed_fisica = ($row[$startcol + 32] !== null) ? (string) $row[$startcol + 32] : null;
            $this->materia_sostitutiva_edfisica = ($row[$startcol + 33] !== null) ? (int) $row[$startcol + 33] : null;
            $this->crediti_terza = ($row[$startcol + 34] !== null) ? (int) $row[$startcol + 34] : null;
            $this->media_voti_terza = ($row[$startcol + 35] !== null) ? (string) $row[$startcol + 35] : null;
            $this->debiti_terza = ($row[$startcol + 36] !== null) ? (string) $row[$startcol + 36] : null;
            $this->crediti_sospesi_terza = ($row[$startcol + 37] !== null) ? (int) $row[$startcol + 37] : null;
            $this->crediti_reintegrati_terza = ($row[$startcol + 38] !== null) ? (int) $row[$startcol + 38] : null;
            $this->crediti_quarta = ($row[$startcol + 39] !== null) ? (int) $row[$startcol + 39] : null;
            $this->media_voti_quarta = ($row[$startcol + 40] !== null) ? (string) $row[$startcol + 40] : null;
            $this->debiti_quarta = ($row[$startcol + 41] !== null) ? (string) $row[$startcol + 41] : null;
            $this->crediti_sospesi_quarta = ($row[$startcol + 42] !== null) ? (int) $row[$startcol + 42] : null;
            $this->crediti_reintegrati_quarta = ($row[$startcol + 43] !== null) ? (int) $row[$startcol + 43] : null;
            $this->crediti_quinta = ($row[$startcol + 44] !== null) ? (int) $row[$startcol + 44] : null;
            $this->media_voti_quinta = ($row[$startcol + 45] !== null) ? (string) $row[$startcol + 45] : null;
            $this->crediti_finali_agg = ($row[$startcol + 46] !== null) ? (int) $row[$startcol + 46] : null;
            $this->matricola = ($row[$startcol + 47] !== null) ? (string) $row[$startcol + 47] : null;
            $this->luogo_nascita = ($row[$startcol + 48] !== null) ? (string) $row[$startcol + 48] : null;
            $this->provincia_nascita = ($row[$startcol + 49] !== null) ? (string) $row[$startcol + 49] : null;
            $this->motivi_crediti_terza = ($row[$startcol + 50] !== null) ? (string) $row[$startcol + 50] : null;
            $this->motivi_crediti_quarta = ($row[$startcol + 51] !== null) ? (string) $row[$startcol + 51] : null;
            $this->motivi_crediti_quinta = ($row[$startcol + 52] !== null) ? (string) $row[$startcol + 52] : null;
            $this->motivi_crediti_agg = ($row[$startcol + 53] !== null) ? (string) $row[$startcol + 53] : null;
            $this->codice_comune_nascita = ($row[$startcol + 54] !== null) ? (string) $row[$startcol + 54] : null;
            $this->stato_nascita = ($row[$startcol + 55] !== null) ? (string) $row[$startcol + 55] : null;
            $this->cittadinanza = ($row[$startcol + 56] !== null) ? (string) $row[$startcol + 56] : null;
            $this->seconda_cittadinanza = ($row[$startcol + 57] !== null) ? (string) $row[$startcol + 57] : null;
            $this->codice_comune_residenza = ($row[$startcol + 58] !== null) ? (string) $row[$startcol + 58] : null;
            $this->distretto = ($row[$startcol + 59] !== null) ? (string) $row[$startcol + 59] : null;
            $this->codice_fiscale = ($row[$startcol + 60] !== null) ? (string) $row[$startcol + 60] : null;
            $this->medico = ($row[$startcol + 61] !== null) ? (string) $row[$startcol + 61] : null;
            $this->telefono_medico = ($row[$startcol + 62] !== null) ? (string) $row[$startcol + 62] : null;
            $this->intolleranze_alim = ($row[$startcol + 63] !== null) ? (string) $row[$startcol + 63] : null;
            $this->gruppo_sanguigno = ($row[$startcol + 64] !== null) ? (string) $row[$startcol + 64] : null;
            $this->gruppo_rh = ($row[$startcol + 65] !== null) ? (string) $row[$startcol + 65] : null;
            $this->codice_asl = ($row[$startcol + 66] !== null) ? (string) $row[$startcol + 66] : null;
            $this->annotazioni = ($row[$startcol + 67] !== null) ? (string) $row[$startcol + 67] : null;
            $this->stato_civile = ($row[$startcol + 68] !== null) ? (int) $row[$startcol + 68] : null;
            $this->voto_primo_scritto = ($row[$startcol + 69] !== null) ? (int) $row[$startcol + 69] : null;
            $this->voto_secondo_scritto = ($row[$startcol + 70] !== null) ? (int) $row[$startcol + 70] : null;
            $this->voto_terzo_scritto = ($row[$startcol + 71] !== null) ? (int) $row[$startcol + 71] : null;
            $this->voto_orale = ($row[$startcol + 72] !== null) ? (int) $row[$startcol + 72] : null;
            $this->voto_bonus = ($row[$startcol + 73] !== null) ? (int) $row[$startcol + 73] : null;
            $this->materia_secondo_scr = ($row[$startcol + 74] !== null) ? (string) $row[$startcol + 74] : null;
            $this->ulteriori_specif_diploma = ($row[$startcol + 75] !== null) ? (string) $row[$startcol + 75] : null;
            $this->numero_diploma = ($row[$startcol + 76] !== null) ? (int) $row[$startcol + 76] : null;
            $this->chi_inserisce = ($row[$startcol + 77] !== null) ? (string) $row[$startcol + 77] : null;
            $this->data_inserimento = ($row[$startcol + 78] !== null) ? (string) $row[$startcol + 78] : null;
            $this->tipo_inserimento = ($row[$startcol + 79] !== null) ? (string) $row[$startcol + 79] : null;
            $this->chi_modifica = ($row[$startcol + 80] !== null) ? (string) $row[$startcol + 80] : null;
            $this->data_modifica = ($row[$startcol + 81] !== null) ? (string) $row[$startcol + 81] : null;
            $this->tipo_modifica = ($row[$startcol + 82] !== null) ? (string) $row[$startcol + 82] : null;
            $this->flag_canc = ($row[$startcol + 83] !== null) ? (string) $row[$startcol + 83] : null;
            $this->stato_avanzamento = ($row[$startcol + 84] !== null) ? (string) $row[$startcol + 84] : null;
            $this->data_stato_avanzamento = ($row[$startcol + 85] !== null) ? (string) $row[$startcol + 85] : null;
            $this->cap_provincia_nascita = ($row[$startcol + 86] !== null) ? (string) $row[$startcol + 86] : null;
            $this->badge = ($row[$startcol + 87] !== null) ? (string) $row[$startcol + 87] : null;
            $this->cap_residenza = ($row[$startcol + 88] !== null) ? (string) $row[$startcol + 88] : null;
            $this->codice_comune_domicilio = ($row[$startcol + 89] !== null) ? (string) $row[$startcol + 89] : null;
            $this->cap_domicilio = ($row[$startcol + 90] !== null) ? (string) $row[$startcol + 90] : null;
            $this->cap_nascita = ($row[$startcol + 91] !== null) ? (string) $row[$startcol + 91] : null;
            $this->indirizzo_domicilio = ($row[$startcol + 92] !== null) ? (string) $row[$startcol + 92] : null;
            $this->citta_nascita_straniera = ($row[$startcol + 93] !== null) ? (string) $row[$startcol + 93] : null;
            $this->cellulare_allievo = ($row[$startcol + 94] !== null) ? (string) $row[$startcol + 94] : null;
            $this->handicap = ($row[$startcol + 95] !== null) ? (string) $row[$startcol + 95] : null;
            $this->stato_convittore = ($row[$startcol + 96] !== null) ? (string) $row[$startcol + 96] : null;
            $this->data_ritiro = ($row[$startcol + 97] !== null) ? (string) $row[$startcol + 97] : null;
            $this->voto_ammissione = ($row[$startcol + 98] !== null) ? (string) $row[$startcol + 98] : null;
            $this->differenza_punteggio = ($row[$startcol + 99] !== null) ? (string) $row[$startcol + 99] : null;
            $this->voto_qualifica = ($row[$startcol + 100] !== null) ? (string) $row[$startcol + 100] : null;
            $this->voto_esame_sc1_qual = ($row[$startcol + 101] !== null) ? (string) $row[$startcol + 101] : null;
            $this->voto_esame_sc2_qual = ($row[$startcol + 102] !== null) ? (string) $row[$startcol + 102] : null;
            $this->voto_esame_or_qual = ($row[$startcol + 103] !== null) ? (string) $row[$startcol + 103] : null;
            $this->stato_privatista = ($row[$startcol + 104] !== null) ? (string) $row[$startcol + 104] : null;
            $this->foto = ($row[$startcol + 105] !== null) ? (string) $row[$startcol + 105] : null;
            $this->rappresentante = ($row[$startcol + 106] !== null) ? (string) $row[$startcol + 106] : null;
            $this->obbligo_formativo = ($row[$startcol + 107] !== null) ? (string) $row[$startcol + 107] : null;
            $this->id_lingua_1 = ($row[$startcol + 108] !== null) ? (string) $row[$startcol + 108] : null;
            $this->id_lingua_2 = ($row[$startcol + 109] !== null) ? (string) $row[$startcol + 109] : null;
            $this->id_lingua_3 = ($row[$startcol + 110] !== null) ? (string) $row[$startcol + 110] : null;
            $this->id_lingua_4 = ($row[$startcol + 111] !== null) ? (string) $row[$startcol + 111] : null;
            $this->id_lingua_5 = ($row[$startcol + 112] !== null) ? (string) $row[$startcol + 112] : null;
            $this->id_provenienza_scolastica = ($row[$startcol + 113] !== null) ? (string) $row[$startcol + 113] : null;
            $this->id_scuola_media = ($row[$startcol + 114] !== null) ? (string) $row[$startcol + 114] : null;
            $this->lingua_scuola_media = ($row[$startcol + 115] !== null) ? (string) $row[$startcol + 115] : null;
            $this->lingua_scuola_media_2 = ($row[$startcol + 116] !== null) ? (string) $row[$startcol + 116] : null;
            $this->giudizio_scuola_media = ($row[$startcol + 117] !== null) ? (string) $row[$startcol + 117] : null;
            $this->trasporto = ($row[$startcol + 118] !== null) ? (string) $row[$startcol + 118] : null;
            $this->data_iscrizione = ($row[$startcol + 119] !== null) ? (string) $row[$startcol + 119] : null;
            $this->pei = ($row[$startcol + 120] !== null) ? (string) $row[$startcol + 120] : null;
            $this->ammesso_esame_qualifica = ($row[$startcol + 121] !== null) ? (string) $row[$startcol + 121] : null;
            $this->ammesso_esame_quinta = ($row[$startcol + 122] !== null) ? (string) $row[$startcol + 122] : null;
            $this->giudizio_ammissione_quinta = ($row[$startcol + 123] !== null) ? (string) $row[$startcol + 123] : null;
            $this->grado_handicap = ($row[$startcol + 124] !== null) ? (string) $row[$startcol + 124] : null;
            $this->tipo_handicap = ($row[$startcol + 125] !== null) ? (string) $row[$startcol + 125] : null;
            $this->stato_licenza_maestro = ($row[$startcol + 126] !== null) ? (string) $row[$startcol + 126] : null;
            $this->id_studente_sissi = ($row[$startcol + 127] !== null) ? (string) $row[$startcol + 127] : null;
            $this->badge_rfid = ($row[$startcol + 128] !== null) ? (string) $row[$startcol + 128] : null;
            $this->lode = ($row[$startcol + 129] !== null) ? (string) $row[$startcol + 129] : null;
            $this->distretto_scolastico = ($row[$startcol + 130] !== null) ? (string) $row[$startcol + 130] : null;
            $this->giudizio_ammissione_terza = ($row[$startcol + 131] !== null) ? (string) $row[$startcol + 131] : null;
            $this->esito_prima_media = ($row[$startcol + 132] !== null) ? (string) $row[$startcol + 132] : null;
            $this->esito_seconda_media = ($row[$startcol + 133] !== null) ? (string) $row[$startcol + 133] : null;
            $this->esito_terza_media = ($row[$startcol + 134] !== null) ? (string) $row[$startcol + 134] : null;
            $this->giudizio_esame_sc1_qual = ($row[$startcol + 135] !== null) ? (string) $row[$startcol + 135] : null;
            $this->giudizio_esame_sc2_qual = ($row[$startcol + 136] !== null) ? (string) $row[$startcol + 136] : null;
            $this->giudizio_esame_or_qual = ($row[$startcol + 137] !== null) ? (string) $row[$startcol + 137] : null;
            $this->giudizio_complessivo_esame_qual = ($row[$startcol + 138] !== null) ? (string) $row[$startcol + 138] : null;
            $this->acconsente_aziende = ($row[$startcol + 139] !== null) ? (int) $row[$startcol + 139] : null;
            $this->curriculum_prima = ($row[$startcol + 140] !== null) ? (string) $row[$startcol + 140] : null;
            $this->curriculum_seconda = ($row[$startcol + 141] !== null) ? (string) $row[$startcol + 141] : null;
            $this->stage_professionali = ($row[$startcol + 142] !== null) ? (string) $row[$startcol + 142] : null;
            $this->data_orale = ($row[$startcol + 143] !== null) ? (string) $row[$startcol + 143] : null;
            $this->ordine_esame_orale = ($row[$startcol + 144] !== null) ? (string) $row[$startcol + 144] : null;
            $this->tipo_primo_scritto = ($row[$startcol + 145] !== null) ? (string) $row[$startcol + 145] : null;
            $this->tipo_secondo_scritto = ($row[$startcol + 146] !== null) ? (string) $row[$startcol + 146] : null;
            $this->tipo_terzo_scritto = ($row[$startcol + 147] !== null) ? (string) $row[$startcol + 147] : null;
            $this->unanimita_primo_scritto = ($row[$startcol + 148] !== null) ? (string) $row[$startcol + 148] : null;
            $this->unanimita_secondo_scritto = ($row[$startcol + 149] !== null) ? (string) $row[$startcol + 149] : null;
            $this->unanimita_terzo_scritto = ($row[$startcol + 150] !== null) ? (string) $row[$startcol + 150] : null;
            $this->argomento_scelto_orale = ($row[$startcol + 151] !== null) ? (string) $row[$startcol + 151] : null;
            $this->area_disc_1_orale = ($row[$startcol + 152] !== null) ? (string) $row[$startcol + 152] : null;
            $this->area_disc_2_orale = ($row[$startcol + 153] !== null) ? (string) $row[$startcol + 153] : null;
            $this->disc_elaborati_orale = ($row[$startcol + 154] !== null) ? (string) $row[$startcol + 154] : null;
            $this->unanimita_voto_finale = ($row[$startcol + 155] !== null) ? (string) $row[$startcol + 155] : null;
            $this->presente_esame_quinta = ($row[$startcol + 156] !== null) ? (string) $row[$startcol + 156] : null;
            $this->stampa_badge = ($row[$startcol + 157] !== null) ? (string) $row[$startcol + 157] : null;
            $this->id_classe_destinazione = ($row[$startcol + 158] !== null) ? (int) $row[$startcol + 158] : null;
            $this->sconto_rette = ($row[$startcol + 159] !== null) ? (int) $row[$startcol + 159] : null;
            $this->carta_studente_numero = ($row[$startcol + 160] !== null) ? (string) $row[$startcol + 160] : null;
            $this->carta_studente_scadenza = ($row[$startcol + 161] !== null) ? (int) $row[$startcol + 161] : null;
            $this->esito_corrente_calcolato = ($row[$startcol + 162] !== null) ? (string) $row[$startcol + 162] : null;
            $this->id_flusso = ($row[$startcol + 163] !== null) ? (string) $row[$startcol + 163] : null;
            $this->data_aggiornamento_sogei = ($row[$startcol + 164] !== null) ? (string) $row[$startcol + 164] : null;
            $this->codice_alunno_ministeriale = ($row[$startcol + 165] !== null) ? (string) $row[$startcol + 165] : null;
            $this->flag_cf_fittizio = ($row[$startcol + 166] !== null) ? (int) $row[$startcol + 166] : null;
            $this->flag_s2f = ($row[$startcol + 167] !== null) ? (string) $row[$startcol + 167] : null;
            $this->codice_stato_sogei = ($row[$startcol + 168] !== null) ? (string) $row[$startcol + 168] : null;
            $this->codice_gruppo_nomade = ($row[$startcol + 169] !== null) ? (string) $row[$startcol + 169] : null;
            $this->flag_minore_straniero = ($row[$startcol + 170] !== null) ? (int) $row[$startcol + 170] : null;
            $this->chiave = ($row[$startcol + 171] !== null) ? (string) $row[$startcol + 171] : null;
            $this->voto_esame_medie_italiano = ($row[$startcol + 172] !== null) ? (string) $row[$startcol + 172] : null;
            $this->voto_esame_medie_inglese = ($row[$startcol + 173] !== null) ? (string) $row[$startcol + 173] : null;
            $this->voto_esame_medie_matematica = ($row[$startcol + 174] !== null) ? (string) $row[$startcol + 174] : null;
            $this->voto_esame_medie_seconda_lingua = ($row[$startcol + 175] !== null) ? (string) $row[$startcol + 175] : null;
            $this->voto_esame_medie_invalsi_ita = ($row[$startcol + 176] !== null) ? (string) $row[$startcol + 176] : null;
            $this->voto_esame_medie_invalsi_mat = ($row[$startcol + 177] !== null) ? (string) $row[$startcol + 177] : null;
            $this->voto_esame_medie_orale = ($row[$startcol + 178] !== null) ? (string) $row[$startcol + 178] : null;
            $this->voto_ammissione_medie = ($row[$startcol + 179] !== null) ? (string) $row[$startcol + 179] : null;
            $this->esito_prima_elementare = ($row[$startcol + 180] !== null) ? (string) $row[$startcol + 180] : null;
            $this->esito_seconda_elementare = ($row[$startcol + 181] !== null) ? (string) $row[$startcol + 181] : null;
            $this->esito_terza_elementare = ($row[$startcol + 182] !== null) ? (string) $row[$startcol + 182] : null;
            $this->esito_quarta_elementare = ($row[$startcol + 183] !== null) ? (string) $row[$startcol + 183] : null;
            $this->esito_quinta_elementare = ($row[$startcol + 184] !== null) ? (string) $row[$startcol + 184] : null;
            $this->tipo_voto_esame_medie_italiano = ($row[$startcol + 185] !== null) ? (string) $row[$startcol + 185] : null;
            $this->tipo_voto_esame_medie_inglese = ($row[$startcol + 186] !== null) ? (string) $row[$startcol + 186] : null;
            $this->giudizio_1_medie = ($row[$startcol + 187] !== null) ? (string) $row[$startcol + 187] : null;
            $this->giudizio_2_medie = ($row[$startcol + 188] !== null) ? (string) $row[$startcol + 188] : null;
            $this->giudizio_3_medie = ($row[$startcol + 189] !== null) ? (string) $row[$startcol + 189] : null;
            $this->argomenti_orali_medie = ($row[$startcol + 190] !== null) ? (string) $row[$startcol + 190] : null;
            $this->giudizio_finale_1_medie = ($row[$startcol + 191] !== null) ? (string) $row[$startcol + 191] : null;
            $this->giudizio_finale_2_medie = ($row[$startcol + 192] !== null) ? (string) $row[$startcol + 192] : null;
            $this->giudizio_finale_3_medie = ($row[$startcol + 193] !== null) ? (string) $row[$startcol + 193] : null;
            $this->consiglio_terza_media = ($row[$startcol + 194] !== null) ? (string) $row[$startcol + 194] : null;
            $this->giudizio_sintetico_esame_terza_media = ($row[$startcol + 195] !== null) ? (string) $row[$startcol + 195] : null;
            $this->data_arrivo_in_italia = ($row[$startcol + 196] !== null) ? (int) $row[$startcol + 196] : null;
            $this->frequenza_asilo_nido = ($row[$startcol + 197] !== null) ? (int) $row[$startcol + 197] : null;
            $this->frequenza_scuola_materna = ($row[$startcol + 198] !== null) ? (int) $row[$startcol + 198] : null;
            $this->data_aggiornamento_sidi = ($row[$startcol + 199] !== null) ? (int) $row[$startcol + 199] : null;
            $this->cmp_sup_val_ita = ($row[$startcol + 200] !== null) ? (string) $row[$startcol + 200] : null;
            $this->cmp_sup_txt_ita = ($row[$startcol + 201] !== null) ? (string) $row[$startcol + 201] : null;
            $this->cmp_sup_val_ing = ($row[$startcol + 202] !== null) ? (string) $row[$startcol + 202] : null;
            $this->cmp_sup_txt_ing = ($row[$startcol + 203] !== null) ? (string) $row[$startcol + 203] : null;
            $this->cmp_sup_val_altri = ($row[$startcol + 204] !== null) ? (string) $row[$startcol + 204] : null;
            $this->cmp_sup_txt_altri = ($row[$startcol + 205] !== null) ? (string) $row[$startcol + 205] : null;
            $this->cmp_sup_val_mat = ($row[$startcol + 206] !== null) ? (string) $row[$startcol + 206] : null;
            $this->cmp_sup_txt_mat = ($row[$startcol + 207] !== null) ? (string) $row[$startcol + 207] : null;
            $this->cmp_sup_val_sci_tec = ($row[$startcol + 208] !== null) ? (string) $row[$startcol + 208] : null;
            $this->cmp_sup_txt_sci_tec = ($row[$startcol + 209] !== null) ? (string) $row[$startcol + 209] : null;
            $this->cmp_sup_val_sto_soc = ($row[$startcol + 210] !== null) ? (string) $row[$startcol + 210] : null;
            $this->cmp_sup_txt_sto_soc = ($row[$startcol + 211] !== null) ? (string) $row[$startcol + 211] : null;
            $this->cmp_med_val_ita = ($row[$startcol + 212] !== null) ? (string) $row[$startcol + 212] : null;
            $this->cmp_med_txt_ita = ($row[$startcol + 213] !== null) ? (string) $row[$startcol + 213] : null;
            $this->cmp_med_val_ing = ($row[$startcol + 214] !== null) ? (string) $row[$startcol + 214] : null;
            $this->cmp_med_txt_ing = ($row[$startcol + 215] !== null) ? (string) $row[$startcol + 215] : null;
            $this->cmp_med_val_altri = ($row[$startcol + 216] !== null) ? (string) $row[$startcol + 216] : null;
            $this->cmp_med_txt_altri = ($row[$startcol + 217] !== null) ? (string) $row[$startcol + 217] : null;
            $this->cmp_med_val_mat = ($row[$startcol + 218] !== null) ? (string) $row[$startcol + 218] : null;
            $this->cmp_med_txt_mat = ($row[$startcol + 219] !== null) ? (string) $row[$startcol + 219] : null;
            $this->cmp_med_val_sci_tec = ($row[$startcol + 220] !== null) ? (string) $row[$startcol + 220] : null;
            $this->cmp_med_txt_sci_tec = ($row[$startcol + 221] !== null) ? (string) $row[$startcol + 221] : null;
            $this->cmp_med_val_sto_soc = ($row[$startcol + 222] !== null) ? (string) $row[$startcol + 222] : null;
            $this->cmp_med_txt_sto_soc = ($row[$startcol + 223] !== null) ? (string) $row[$startcol + 223] : null;
            $this->cmp_med_val_l2 = ($row[$startcol + 224] !== null) ? (string) $row[$startcol + 224] : null;
            $this->cmp_med_txt_l2 = ($row[$startcol + 225] !== null) ? (string) $row[$startcol + 225] : null;
            $this->cmp_med_val_l3 = ($row[$startcol + 226] !== null) ? (string) $row[$startcol + 226] : null;
            $this->cmp_med_txt_l3 = ($row[$startcol + 227] !== null) ? (string) $row[$startcol + 227] : null;
            $this->cmp_med_val_arte = ($row[$startcol + 228] !== null) ? (string) $row[$startcol + 228] : null;
            $this->cmp_med_txt_arte = ($row[$startcol + 229] !== null) ? (string) $row[$startcol + 229] : null;
            $this->cmp_med_val_mus = ($row[$startcol + 230] !== null) ? (string) $row[$startcol + 230] : null;
            $this->cmp_med_txt_mus = ($row[$startcol + 231] !== null) ? (string) $row[$startcol + 231] : null;
            $this->cmp_med_val_mot = ($row[$startcol + 232] !== null) ? (string) $row[$startcol + 232] : null;
            $this->cmp_med_txt_mot = ($row[$startcol + 233] !== null) ? (string) $row[$startcol + 233] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 234; // 234 = StudentiCompletiPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating StudentiCompleti object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = StudentiCompletiPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collTasses !== null) {
                    foreach ($this->collTasses as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = StudentiCompletiPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getIdStudente();
                break;
            case 1:
                return $this->getNome();
                break;
            case 2:
                return $this->getCognome();
                break;
            case 3:
                return $this->getIndirizzo();
                break;
            case 4:
                return $this->getCitta();
                break;
            case 5:
                return $this->getCap();
                break;
            case 6:
                return $this->getProvincia();
                break;
            case 7:
                return $this->getSesso();
                break;
            case 8:
                return $this->getTelefono();
                break;
            case 9:
                return $this->getCellulare1();
                break;
            case 10:
                return $this->getCellulare2();
                break;
            case 11:
                return $this->getEmail1();
                break;
            case 12:
                return $this->getEmail2();
                break;
            case 13:
                return $this->getInvioEmail();
                break;
            case 14:
                return $this->getInvioEmailCumulativo();
                break;
            case 15:
                return $this->getInvioEmailParametrico();
                break;
            case 16:
                return $this->getInvioEmailTemporale();
                break;
            case 17:
                return $this->getTipoSms();
                break;
            case 18:
                return $this->getTipoSmsCumulativo();
                break;
            case 19:
                return $this->getTipoSmsParametrico();
                break;
            case 20:
                return $this->getTipoSmsTemporale();
                break;
            case 21:
                return $this->getAutEntrataRitardo();
                break;
            case 22:
                return $this->getAutUscitaAnticipo();
                break;
            case 23:
                return $this->getAutPomeriggio();
                break;
            case 24:
                return $this->getAcconsente();
                break;
            case 25:
                return $this->getRitirato();
                break;
            case 26:
                return $this->getDataNascita();
                break;
            case 27:
                return $this->getCodiceStudente();
                break;
            case 28:
                return $this->getPasswordStudente();
                break;
            case 29:
                return $this->getCodiceGiustificazioniStudente();
                break;
            case 30:
                return $this->getEsoneroReligione();
                break;
            case 31:
                return $this->getMateriaSostitutivaReligione();
                break;
            case 32:
                return $this->getEsoneroEdFisica();
                break;
            case 33:
                return $this->getMateriaSostitutivaEdfisica();
                break;
            case 34:
                return $this->getCreditiTerza();
                break;
            case 35:
                return $this->getMediaVotiTerza();
                break;
            case 36:
                return $this->getDebitiTerza();
                break;
            case 37:
                return $this->getCreditiSospesiTerza();
                break;
            case 38:
                return $this->getCreditiReintegratiTerza();
                break;
            case 39:
                return $this->getCreditiQuarta();
                break;
            case 40:
                return $this->getMediaVotiQuarta();
                break;
            case 41:
                return $this->getDebitiQuarta();
                break;
            case 42:
                return $this->getCreditiSospesiQuarta();
                break;
            case 43:
                return $this->getCreditiReintegratiQuarta();
                break;
            case 44:
                return $this->getCreditiQuinta();
                break;
            case 45:
                return $this->getMediaVotiQuinta();
                break;
            case 46:
                return $this->getCreditiFinaliAgg();
                break;
            case 47:
                return $this->getMatricola();
                break;
            case 48:
                return $this->getLuogoNascita();
                break;
            case 49:
                return $this->getProvinciaNascita();
                break;
            case 50:
                return $this->getMotiviCreditiTerza();
                break;
            case 51:
                return $this->getMotiviCreditiQuarta();
                break;
            case 52:
                return $this->getMotiviCreditiQuinta();
                break;
            case 53:
                return $this->getMotiviCreditiAgg();
                break;
            case 54:
                return $this->getCodiceComuneNascita();
                break;
            case 55:
                return $this->getStatoNascita();
                break;
            case 56:
                return $this->getCittadinanza();
                break;
            case 57:
                return $this->getSecondaCittadinanza();
                break;
            case 58:
                return $this->getCodiceComuneResidenza();
                break;
            case 59:
                return $this->getDistretto();
                break;
            case 60:
                return $this->getCodiceFiscale();
                break;
            case 61:
                return $this->getMedico();
                break;
            case 62:
                return $this->getTelefonoMedico();
                break;
            case 63:
                return $this->getIntolleranzeAlim();
                break;
            case 64:
                return $this->getGruppoSanguigno();
                break;
            case 65:
                return $this->getGruppoRh();
                break;
            case 66:
                return $this->getCodiceAsl();
                break;
            case 67:
                return $this->getAnnotazioni();
                break;
            case 68:
                return $this->getStatoCivile();
                break;
            case 69:
                return $this->getVotoPrimoScritto();
                break;
            case 70:
                return $this->getVotoSecondoScritto();
                break;
            case 71:
                return $this->getVotoTerzoScritto();
                break;
            case 72:
                return $this->getVotoOrale();
                break;
            case 73:
                return $this->getVotoBonus();
                break;
            case 74:
                return $this->getMateriaSecondoScr();
                break;
            case 75:
                return $this->getUlterioriSpecifDiploma();
                break;
            case 76:
                return $this->getNumeroDiploma();
                break;
            case 77:
                return $this->getChiInserisce();
                break;
            case 78:
                return $this->getDataInserimento();
                break;
            case 79:
                return $this->getTipoInserimento();
                break;
            case 80:
                return $this->getChiModifica();
                break;
            case 81:
                return $this->getDataModifica();
                break;
            case 82:
                return $this->getTipoModifica();
                break;
            case 83:
                return $this->getFlagCanc();
                break;
            case 84:
                return $this->getStatoAvanzamento();
                break;
            case 85:
                return $this->getDataStatoAvanzamento();
                break;
            case 86:
                return $this->getCapProvinciaNascita();
                break;
            case 87:
                return $this->getBadge();
                break;
            case 88:
                return $this->getCapResidenza();
                break;
            case 89:
                return $this->getCodiceComuneDomicilio();
                break;
            case 90:
                return $this->getCapDomicilio();
                break;
            case 91:
                return $this->getCapNascita();
                break;
            case 92:
                return $this->getIndirizzoDomicilio();
                break;
            case 93:
                return $this->getCittaNascitaStraniera();
                break;
            case 94:
                return $this->getCellulareAllievo();
                break;
            case 95:
                return $this->getHandicap();
                break;
            case 96:
                return $this->getStatoConvittore();
                break;
            case 97:
                return $this->getDataRitiro();
                break;
            case 98:
                return $this->getVotoAmmissione();
                break;
            case 99:
                return $this->getDifferenzaPunteggio();
                break;
            case 100:
                return $this->getVotoQualifica();
                break;
            case 101:
                return $this->getVotoEsameSc1Qual();
                break;
            case 102:
                return $this->getVotoEsameSc2Qual();
                break;
            case 103:
                return $this->getVotoEsameOrQual();
                break;
            case 104:
                return $this->getStatoPrivatista();
                break;
            case 105:
                return $this->getFoto();
                break;
            case 106:
                return $this->getRappresentante();
                break;
            case 107:
                return $this->getObbligoFormativo();
                break;
            case 108:
                return $this->getIdLingua1();
                break;
            case 109:
                return $this->getIdLingua2();
                break;
            case 110:
                return $this->getIdLingua3();
                break;
            case 111:
                return $this->getIdLingua4();
                break;
            case 112:
                return $this->getIdLingua5();
                break;
            case 113:
                return $this->getIdProvenienzaScolastica();
                break;
            case 114:
                return $this->getIdScuolaMedia();
                break;
            case 115:
                return $this->getLinguaScuolaMedia();
                break;
            case 116:
                return $this->getLinguaScuolaMedia2();
                break;
            case 117:
                return $this->getGiudizioScuolaMedia();
                break;
            case 118:
                return $this->getTrasporto();
                break;
            case 119:
                return $this->getDataIscrizione();
                break;
            case 120:
                return $this->getPei();
                break;
            case 121:
                return $this->getAmmessoEsameQualifica();
                break;
            case 122:
                return $this->getAmmessoEsameQuinta();
                break;
            case 123:
                return $this->getGiudizioAmmissioneQuinta();
                break;
            case 124:
                return $this->getGradoHandicap();
                break;
            case 125:
                return $this->getTipoHandicap();
                break;
            case 126:
                return $this->getStatoLicenzaMaestro();
                break;
            case 127:
                return $this->getIdStudenteSissi();
                break;
            case 128:
                return $this->getBadgeRfid();
                break;
            case 129:
                return $this->getLode();
                break;
            case 130:
                return $this->getDistrettoScolastico();
                break;
            case 131:
                return $this->getGiudizioAmmissioneTerza();
                break;
            case 132:
                return $this->getEsitoPrimaMedia();
                break;
            case 133:
                return $this->getEsitoSecondaMedia();
                break;
            case 134:
                return $this->getEsitoTerzaMedia();
                break;
            case 135:
                return $this->getGiudizioEsameSc1Qual();
                break;
            case 136:
                return $this->getGiudizioEsameSc2Qual();
                break;
            case 137:
                return $this->getGiudizioEsameOrQual();
                break;
            case 138:
                return $this->getGiudizioComplessivoEsameQual();
                break;
            case 139:
                return $this->getAcconsenteAziende();
                break;
            case 140:
                return $this->getCurriculumPrima();
                break;
            case 141:
                return $this->getCurriculumSeconda();
                break;
            case 142:
                return $this->getStageProfessionali();
                break;
            case 143:
                return $this->getDataOrale();
                break;
            case 144:
                return $this->getOrdineEsameOrale();
                break;
            case 145:
                return $this->getTipoPrimoScritto();
                break;
            case 146:
                return $this->getTipoSecondoScritto();
                break;
            case 147:
                return $this->getTipoTerzoScritto();
                break;
            case 148:
                return $this->getUnanimitaPrimoScritto();
                break;
            case 149:
                return $this->getUnanimitaSecondoScritto();
                break;
            case 150:
                return $this->getUnanimitaTerzoScritto();
                break;
            case 151:
                return $this->getArgomentoSceltoOrale();
                break;
            case 152:
                return $this->getAreaDisc1Orale();
                break;
            case 153:
                return $this->getAreaDisc2Orale();
                break;
            case 154:
                return $this->getDiscElaboratiOrale();
                break;
            case 155:
                return $this->getUnanimitaVotoFinale();
                break;
            case 156:
                return $this->getPresenteEsameQuinta();
                break;
            case 157:
                return $this->getStampaBadge();
                break;
            case 158:
                return $this->getIdClasseDestinazione();
                break;
            case 159:
                return $this->getScontoRette();
                break;
            case 160:
                return $this->getCartaStudenteNumero();
                break;
            case 161:
                return $this->getCartaStudenteScadenza();
                break;
            case 162:
                return $this->getEsitoCorrenteCalcolato();
                break;
            case 163:
                return $this->getIdFlusso();
                break;
            case 164:
                return $this->getDataAggiornamentoSogei();
                break;
            case 165:
                return $this->getCodiceAlunnoMinisteriale();
                break;
            case 166:
                return $this->getFlagCfFittizio();
                break;
            case 167:
                return $this->getFlagS2f();
                break;
            case 168:
                return $this->getCodiceStatoSogei();
                break;
            case 169:
                return $this->getCodiceGruppoNomade();
                break;
            case 170:
                return $this->getFlagMinoreStraniero();
                break;
            case 171:
                return $this->getChiave();
                break;
            case 172:
                return $this->getVotoEsameMedieItaliano();
                break;
            case 173:
                return $this->getVotoEsameMedieInglese();
                break;
            case 174:
                return $this->getVotoEsameMedieMatematica();
                break;
            case 175:
                return $this->getVotoEsameMedieSecondaLingua();
                break;
            case 176:
                return $this->getVotoEsameMedieInvalsiIta();
                break;
            case 177:
                return $this->getVotoEsameMedieInvalsiMat();
                break;
            case 178:
                return $this->getVotoEsameMedieOrale();
                break;
            case 179:
                return $this->getVotoAmmissioneMedie();
                break;
            case 180:
                return $this->getEsitoPrimaElementare();
                break;
            case 181:
                return $this->getEsitoSecondaElementare();
                break;
            case 182:
                return $this->getEsitoTerzaElementare();
                break;
            case 183:
                return $this->getEsitoQuartaElementare();
                break;
            case 184:
                return $this->getEsitoQuintaElementare();
                break;
            case 185:
                return $this->getTipoVotoEsameMedieItaliano();
                break;
            case 186:
                return $this->getTipoVotoEsameMedieInglese();
                break;
            case 187:
                return $this->getGiudizio1Medie();
                break;
            case 188:
                return $this->getGiudizio2Medie();
                break;
            case 189:
                return $this->getGiudizio3Medie();
                break;
            case 190:
                return $this->getArgomentiOraliMedie();
                break;
            case 191:
                return $this->getGiudizioFinale1Medie();
                break;
            case 192:
                return $this->getGiudizioFinale2Medie();
                break;
            case 193:
                return $this->getGiudizioFinale3Medie();
                break;
            case 194:
                return $this->getConsiglioTerzaMedia();
                break;
            case 195:
                return $this->getGiudizioSinteticoEsameTerzaMedia();
                break;
            case 196:
                return $this->getDataArrivoInItalia();
                break;
            case 197:
                return $this->getFrequenzaAsiloNido();
                break;
            case 198:
                return $this->getFrequenzaScuolaMaterna();
                break;
            case 199:
                return $this->getDataAggiornamentoSidi();
                break;
            case 200:
                return $this->getCmpSupValIta();
                break;
            case 201:
                return $this->getCmpSupTxtIta();
                break;
            case 202:
                return $this->getCmpSupValIng();
                break;
            case 203:
                return $this->getCmpSupTxtIng();
                break;
            case 204:
                return $this->getCmpSupValAltri();
                break;
            case 205:
                return $this->getCmpSupTxtAltri();
                break;
            case 206:
                return $this->getCmpSupValMat();
                break;
            case 207:
                return $this->getCmpSupTxtMat();
                break;
            case 208:
                return $this->getCmpSupValSciTec();
                break;
            case 209:
                return $this->getCmpSupTxtSciTec();
                break;
            case 210:
                return $this->getCmpSupValStoSoc();
                break;
            case 211:
                return $this->getCmpSupTxtStoSoc();
                break;
            case 212:
                return $this->getCmpMedValIta();
                break;
            case 213:
                return $this->getCmpMedTxtIta();
                break;
            case 214:
                return $this->getCmpMedValIng();
                break;
            case 215:
                return $this->getCmpMedTxtIng();
                break;
            case 216:
                return $this->getCmpMedValAltri();
                break;
            case 217:
                return $this->getCmpMedTxtAltri();
                break;
            case 218:
                return $this->getCmpMedValMat();
                break;
            case 219:
                return $this->getCmpMedTxtMat();
                break;
            case 220:
                return $this->getCmpMedValSciTec();
                break;
            case 221:
                return $this->getCmpMedTxtSciTec();
                break;
            case 222:
                return $this->getCmpMedValStoSoc();
                break;
            case 223:
                return $this->getCmpMedTxtStoSoc();
                break;
            case 224:
                return $this->getCmpMedValL2();
                break;
            case 225:
                return $this->getCmpMedTxtL2();
                break;
            case 226:
                return $this->getCmpMedValL3();
                break;
            case 227:
                return $this->getCmpMedTxtL3();
                break;
            case 228:
                return $this->getCmpMedValArte();
                break;
            case 229:
                return $this->getCmpMedTxtArte();
                break;
            case 230:
                return $this->getCmpMedValMus();
                break;
            case 231:
                return $this->getCmpMedTxtMus();
                break;
            case 232:
                return $this->getCmpMedValMot();
                break;
            case 233:
                return $this->getCmpMedTxtMot();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['StudentiCompleti'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['StudentiCompleti'][$this->getPrimaryKey()] = true;
        $keys = StudentiCompletiPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getIdStudente(),
            $keys[1] => $this->getNome(),
            $keys[2] => $this->getCognome(),
            $keys[3] => $this->getIndirizzo(),
            $keys[4] => $this->getCitta(),
            $keys[5] => $this->getCap(),
            $keys[6] => $this->getProvincia(),
            $keys[7] => $this->getSesso(),
            $keys[8] => $this->getTelefono(),
            $keys[9] => $this->getCellulare1(),
            $keys[10] => $this->getCellulare2(),
            $keys[11] => $this->getEmail1(),
            $keys[12] => $this->getEmail2(),
            $keys[13] => $this->getInvioEmail(),
            $keys[14] => $this->getInvioEmailCumulativo(),
            $keys[15] => $this->getInvioEmailParametrico(),
            $keys[16] => $this->getInvioEmailTemporale(),
            $keys[17] => $this->getTipoSms(),
            $keys[18] => $this->getTipoSmsCumulativo(),
            $keys[19] => $this->getTipoSmsParametrico(),
            $keys[20] => $this->getTipoSmsTemporale(),
            $keys[21] => $this->getAutEntrataRitardo(),
            $keys[22] => $this->getAutUscitaAnticipo(),
            $keys[23] => $this->getAutPomeriggio(),
            $keys[24] => $this->getAcconsente(),
            $keys[25] => $this->getRitirato(),
            $keys[26] => $this->getDataNascita(),
            $keys[27] => $this->getCodiceStudente(),
            $keys[28] => $this->getPasswordStudente(),
            $keys[29] => $this->getCodiceGiustificazioniStudente(),
            $keys[30] => $this->getEsoneroReligione(),
            $keys[31] => $this->getMateriaSostitutivaReligione(),
            $keys[32] => $this->getEsoneroEdFisica(),
            $keys[33] => $this->getMateriaSostitutivaEdfisica(),
            $keys[34] => $this->getCreditiTerza(),
            $keys[35] => $this->getMediaVotiTerza(),
            $keys[36] => $this->getDebitiTerza(),
            $keys[37] => $this->getCreditiSospesiTerza(),
            $keys[38] => $this->getCreditiReintegratiTerza(),
            $keys[39] => $this->getCreditiQuarta(),
            $keys[40] => $this->getMediaVotiQuarta(),
            $keys[41] => $this->getDebitiQuarta(),
            $keys[42] => $this->getCreditiSospesiQuarta(),
            $keys[43] => $this->getCreditiReintegratiQuarta(),
            $keys[44] => $this->getCreditiQuinta(),
            $keys[45] => $this->getMediaVotiQuinta(),
            $keys[46] => $this->getCreditiFinaliAgg(),
            $keys[47] => $this->getMatricola(),
            $keys[48] => $this->getLuogoNascita(),
            $keys[49] => $this->getProvinciaNascita(),
            $keys[50] => $this->getMotiviCreditiTerza(),
            $keys[51] => $this->getMotiviCreditiQuarta(),
            $keys[52] => $this->getMotiviCreditiQuinta(),
            $keys[53] => $this->getMotiviCreditiAgg(),
            $keys[54] => $this->getCodiceComuneNascita(),
            $keys[55] => $this->getStatoNascita(),
            $keys[56] => $this->getCittadinanza(),
            $keys[57] => $this->getSecondaCittadinanza(),
            $keys[58] => $this->getCodiceComuneResidenza(),
            $keys[59] => $this->getDistretto(),
            $keys[60] => $this->getCodiceFiscale(),
            $keys[61] => $this->getMedico(),
            $keys[62] => $this->getTelefonoMedico(),
            $keys[63] => $this->getIntolleranzeAlim(),
            $keys[64] => $this->getGruppoSanguigno(),
            $keys[65] => $this->getGruppoRh(),
            $keys[66] => $this->getCodiceAsl(),
            $keys[67] => $this->getAnnotazioni(),
            $keys[68] => $this->getStatoCivile(),
            $keys[69] => $this->getVotoPrimoScritto(),
            $keys[70] => $this->getVotoSecondoScritto(),
            $keys[71] => $this->getVotoTerzoScritto(),
            $keys[72] => $this->getVotoOrale(),
            $keys[73] => $this->getVotoBonus(),
            $keys[74] => $this->getMateriaSecondoScr(),
            $keys[75] => $this->getUlterioriSpecifDiploma(),
            $keys[76] => $this->getNumeroDiploma(),
            $keys[77] => $this->getChiInserisce(),
            $keys[78] => $this->getDataInserimento(),
            $keys[79] => $this->getTipoInserimento(),
            $keys[80] => $this->getChiModifica(),
            $keys[81] => $this->getDataModifica(),
            $keys[82] => $this->getTipoModifica(),
            $keys[83] => $this->getFlagCanc(),
            $keys[84] => $this->getStatoAvanzamento(),
            $keys[85] => $this->getDataStatoAvanzamento(),
            $keys[86] => $this->getCapProvinciaNascita(),
            $keys[87] => $this->getBadge(),
            $keys[88] => $this->getCapResidenza(),
            $keys[89] => $this->getCodiceComuneDomicilio(),
            $keys[90] => $this->getCapDomicilio(),
            $keys[91] => $this->getCapNascita(),
            $keys[92] => $this->getIndirizzoDomicilio(),
            $keys[93] => $this->getCittaNascitaStraniera(),
            $keys[94] => $this->getCellulareAllievo(),
            $keys[95] => $this->getHandicap(),
            $keys[96] => $this->getStatoConvittore(),
            $keys[97] => $this->getDataRitiro(),
            $keys[98] => $this->getVotoAmmissione(),
            $keys[99] => $this->getDifferenzaPunteggio(),
            $keys[100] => $this->getVotoQualifica(),
            $keys[101] => $this->getVotoEsameSc1Qual(),
            $keys[102] => $this->getVotoEsameSc2Qual(),
            $keys[103] => $this->getVotoEsameOrQual(),
            $keys[104] => $this->getStatoPrivatista(),
            $keys[105] => $this->getFoto(),
            $keys[106] => $this->getRappresentante(),
            $keys[107] => $this->getObbligoFormativo(),
            $keys[108] => $this->getIdLingua1(),
            $keys[109] => $this->getIdLingua2(),
            $keys[110] => $this->getIdLingua3(),
            $keys[111] => $this->getIdLingua4(),
            $keys[112] => $this->getIdLingua5(),
            $keys[113] => $this->getIdProvenienzaScolastica(),
            $keys[114] => $this->getIdScuolaMedia(),
            $keys[115] => $this->getLinguaScuolaMedia(),
            $keys[116] => $this->getLinguaScuolaMedia2(),
            $keys[117] => $this->getGiudizioScuolaMedia(),
            $keys[118] => $this->getTrasporto(),
            $keys[119] => $this->getDataIscrizione(),
            $keys[120] => $this->getPei(),
            $keys[121] => $this->getAmmessoEsameQualifica(),
            $keys[122] => $this->getAmmessoEsameQuinta(),
            $keys[123] => $this->getGiudizioAmmissioneQuinta(),
            $keys[124] => $this->getGradoHandicap(),
            $keys[125] => $this->getTipoHandicap(),
            $keys[126] => $this->getStatoLicenzaMaestro(),
            $keys[127] => $this->getIdStudenteSissi(),
            $keys[128] => $this->getBadgeRfid(),
            $keys[129] => $this->getLode(),
            $keys[130] => $this->getDistrettoScolastico(),
            $keys[131] => $this->getGiudizioAmmissioneTerza(),
            $keys[132] => $this->getEsitoPrimaMedia(),
            $keys[133] => $this->getEsitoSecondaMedia(),
            $keys[134] => $this->getEsitoTerzaMedia(),
            $keys[135] => $this->getGiudizioEsameSc1Qual(),
            $keys[136] => $this->getGiudizioEsameSc2Qual(),
            $keys[137] => $this->getGiudizioEsameOrQual(),
            $keys[138] => $this->getGiudizioComplessivoEsameQual(),
            $keys[139] => $this->getAcconsenteAziende(),
            $keys[140] => $this->getCurriculumPrima(),
            $keys[141] => $this->getCurriculumSeconda(),
            $keys[142] => $this->getStageProfessionali(),
            $keys[143] => $this->getDataOrale(),
            $keys[144] => $this->getOrdineEsameOrale(),
            $keys[145] => $this->getTipoPrimoScritto(),
            $keys[146] => $this->getTipoSecondoScritto(),
            $keys[147] => $this->getTipoTerzoScritto(),
            $keys[148] => $this->getUnanimitaPrimoScritto(),
            $keys[149] => $this->getUnanimitaSecondoScritto(),
            $keys[150] => $this->getUnanimitaTerzoScritto(),
            $keys[151] => $this->getArgomentoSceltoOrale(),
            $keys[152] => $this->getAreaDisc1Orale(),
            $keys[153] => $this->getAreaDisc2Orale(),
            $keys[154] => $this->getDiscElaboratiOrale(),
            $keys[155] => $this->getUnanimitaVotoFinale(),
            $keys[156] => $this->getPresenteEsameQuinta(),
            $keys[157] => $this->getStampaBadge(),
            $keys[158] => $this->getIdClasseDestinazione(),
            $keys[159] => $this->getScontoRette(),
            $keys[160] => $this->getCartaStudenteNumero(),
            $keys[161] => $this->getCartaStudenteScadenza(),
            $keys[162] => $this->getEsitoCorrenteCalcolato(),
            $keys[163] => $this->getIdFlusso(),
            $keys[164] => $this->getDataAggiornamentoSogei(),
            $keys[165] => $this->getCodiceAlunnoMinisteriale(),
            $keys[166] => $this->getFlagCfFittizio(),
            $keys[167] => $this->getFlagS2f(),
            $keys[168] => $this->getCodiceStatoSogei(),
            $keys[169] => $this->getCodiceGruppoNomade(),
            $keys[170] => $this->getFlagMinoreStraniero(),
            $keys[171] => $this->getChiave(),
            $keys[172] => $this->getVotoEsameMedieItaliano(),
            $keys[173] => $this->getVotoEsameMedieInglese(),
            $keys[174] => $this->getVotoEsameMedieMatematica(),
            $keys[175] => $this->getVotoEsameMedieSecondaLingua(),
            $keys[176] => $this->getVotoEsameMedieInvalsiIta(),
            $keys[177] => $this->getVotoEsameMedieInvalsiMat(),
            $keys[178] => $this->getVotoEsameMedieOrale(),
            $keys[179] => $this->getVotoAmmissioneMedie(),
            $keys[180] => $this->getEsitoPrimaElementare(),
            $keys[181] => $this->getEsitoSecondaElementare(),
            $keys[182] => $this->getEsitoTerzaElementare(),
            $keys[183] => $this->getEsitoQuartaElementare(),
            $keys[184] => $this->getEsitoQuintaElementare(),
            $keys[185] => $this->getTipoVotoEsameMedieItaliano(),
            $keys[186] => $this->getTipoVotoEsameMedieInglese(),
            $keys[187] => $this->getGiudizio1Medie(),
            $keys[188] => $this->getGiudizio2Medie(),
            $keys[189] => $this->getGiudizio3Medie(),
            $keys[190] => $this->getArgomentiOraliMedie(),
            $keys[191] => $this->getGiudizioFinale1Medie(),
            $keys[192] => $this->getGiudizioFinale2Medie(),
            $keys[193] => $this->getGiudizioFinale3Medie(),
            $keys[194] => $this->getConsiglioTerzaMedia(),
            $keys[195] => $this->getGiudizioSinteticoEsameTerzaMedia(),
            $keys[196] => $this->getDataArrivoInItalia(),
            $keys[197] => $this->getFrequenzaAsiloNido(),
            $keys[198] => $this->getFrequenzaScuolaMaterna(),
            $keys[199] => $this->getDataAggiornamentoSidi(),
            $keys[200] => $this->getCmpSupValIta(),
            $keys[201] => $this->getCmpSupTxtIta(),
            $keys[202] => $this->getCmpSupValIng(),
            $keys[203] => $this->getCmpSupTxtIng(),
            $keys[204] => $this->getCmpSupValAltri(),
            $keys[205] => $this->getCmpSupTxtAltri(),
            $keys[206] => $this->getCmpSupValMat(),
            $keys[207] => $this->getCmpSupTxtMat(),
            $keys[208] => $this->getCmpSupValSciTec(),
            $keys[209] => $this->getCmpSupTxtSciTec(),
            $keys[210] => $this->getCmpSupValStoSoc(),
            $keys[211] => $this->getCmpSupTxtStoSoc(),
            $keys[212] => $this->getCmpMedValIta(),
            $keys[213] => $this->getCmpMedTxtIta(),
            $keys[214] => $this->getCmpMedValIng(),
            $keys[215] => $this->getCmpMedTxtIng(),
            $keys[216] => $this->getCmpMedValAltri(),
            $keys[217] => $this->getCmpMedTxtAltri(),
            $keys[218] => $this->getCmpMedValMat(),
            $keys[219] => $this->getCmpMedTxtMat(),
            $keys[220] => $this->getCmpMedValSciTec(),
            $keys[221] => $this->getCmpMedTxtSciTec(),
            $keys[222] => $this->getCmpMedValStoSoc(),
            $keys[223] => $this->getCmpMedTxtStoSoc(),
            $keys[224] => $this->getCmpMedValL2(),
            $keys[225] => $this->getCmpMedTxtL2(),
            $keys[226] => $this->getCmpMedValL3(),
            $keys[227] => $this->getCmpMedTxtL3(),
            $keys[228] => $this->getCmpMedValArte(),
            $keys[229] => $this->getCmpMedTxtArte(),
            $keys[230] => $this->getCmpMedValMus(),
            $keys[231] => $this->getCmpMedTxtMus(),
            $keys[232] => $this->getCmpMedValMot(),
            $keys[233] => $this->getCmpMedTxtMot(),
        );
        if ($includeForeignObjects) {
            if (null !== $this->collTasses) {
                $result['Tasses'] = $this->collTasses->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(StudentiCompletiPeer::DATABASE_NAME);

        if ($this->isColumnModified(StudentiCompletiPeer::ID_STUDENTE)) $criteria->add(StudentiCompletiPeer::ID_STUDENTE, $this->id_studente);
        if ($this->isColumnModified(StudentiCompletiPeer::NOME)) $criteria->add(StudentiCompletiPeer::NOME, $this->nome);
        if ($this->isColumnModified(StudentiCompletiPeer::COGNOME)) $criteria->add(StudentiCompletiPeer::COGNOME, $this->cognome);
        if ($this->isColumnModified(StudentiCompletiPeer::INDIRIZZO)) $criteria->add(StudentiCompletiPeer::INDIRIZZO, $this->indirizzo);
        if ($this->isColumnModified(StudentiCompletiPeer::CITTA)) $criteria->add(StudentiCompletiPeer::CITTA, $this->citta);
        if ($this->isColumnModified(StudentiCompletiPeer::CAP)) $criteria->add(StudentiCompletiPeer::CAP, $this->cap);
        if ($this->isColumnModified(StudentiCompletiPeer::PROVINCIA)) $criteria->add(StudentiCompletiPeer::PROVINCIA, $this->provincia);
        if ($this->isColumnModified(StudentiCompletiPeer::SESSO)) $criteria->add(StudentiCompletiPeer::SESSO, $this->sesso);
        if ($this->isColumnModified(StudentiCompletiPeer::TELEFONO)) $criteria->add(StudentiCompletiPeer::TELEFONO, $this->telefono);
        if ($this->isColumnModified(StudentiCompletiPeer::CELLULARE1)) $criteria->add(StudentiCompletiPeer::CELLULARE1, $this->cellulare1);
        if ($this->isColumnModified(StudentiCompletiPeer::CELLULARE2)) $criteria->add(StudentiCompletiPeer::CELLULARE2, $this->cellulare2);
        if ($this->isColumnModified(StudentiCompletiPeer::EMAIL1)) $criteria->add(StudentiCompletiPeer::EMAIL1, $this->email1);
        if ($this->isColumnModified(StudentiCompletiPeer::EMAIL2)) $criteria->add(StudentiCompletiPeer::EMAIL2, $this->email2);
        if ($this->isColumnModified(StudentiCompletiPeer::INVIO_EMAIL)) $criteria->add(StudentiCompletiPeer::INVIO_EMAIL, $this->invio_email);
        if ($this->isColumnModified(StudentiCompletiPeer::INVIO_EMAIL_CUMULATIVO)) $criteria->add(StudentiCompletiPeer::INVIO_EMAIL_CUMULATIVO, $this->invio_email_cumulativo);
        if ($this->isColumnModified(StudentiCompletiPeer::INVIO_EMAIL_PARAMETRICO)) $criteria->add(StudentiCompletiPeer::INVIO_EMAIL_PARAMETRICO, $this->invio_email_parametrico);
        if ($this->isColumnModified(StudentiCompletiPeer::INVIO_EMAIL_TEMPORALE)) $criteria->add(StudentiCompletiPeer::INVIO_EMAIL_TEMPORALE, $this->invio_email_temporale);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_SMS)) $criteria->add(StudentiCompletiPeer::TIPO_SMS, $this->tipo_sms);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_SMS_CUMULATIVO)) $criteria->add(StudentiCompletiPeer::TIPO_SMS_CUMULATIVO, $this->tipo_sms_cumulativo);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_SMS_PARAMETRICO)) $criteria->add(StudentiCompletiPeer::TIPO_SMS_PARAMETRICO, $this->tipo_sms_parametrico);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_SMS_TEMPORALE)) $criteria->add(StudentiCompletiPeer::TIPO_SMS_TEMPORALE, $this->tipo_sms_temporale);
        if ($this->isColumnModified(StudentiCompletiPeer::AUT_ENTRATA_RITARDO)) $criteria->add(StudentiCompletiPeer::AUT_ENTRATA_RITARDO, $this->aut_entrata_ritardo);
        if ($this->isColumnModified(StudentiCompletiPeer::AUT_USCITA_ANTICIPO)) $criteria->add(StudentiCompletiPeer::AUT_USCITA_ANTICIPO, $this->aut_uscita_anticipo);
        if ($this->isColumnModified(StudentiCompletiPeer::AUT_POMERIGGIO)) $criteria->add(StudentiCompletiPeer::AUT_POMERIGGIO, $this->aut_pomeriggio);
        if ($this->isColumnModified(StudentiCompletiPeer::ACCONSENTE)) $criteria->add(StudentiCompletiPeer::ACCONSENTE, $this->acconsente);
        if ($this->isColumnModified(StudentiCompletiPeer::RITIRATO)) $criteria->add(StudentiCompletiPeer::RITIRATO, $this->ritirato);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_NASCITA)) $criteria->add(StudentiCompletiPeer::DATA_NASCITA, $this->data_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_STUDENTE)) $criteria->add(StudentiCompletiPeer::CODICE_STUDENTE, $this->codice_studente);
        if ($this->isColumnModified(StudentiCompletiPeer::PASSWORD_STUDENTE)) $criteria->add(StudentiCompletiPeer::PASSWORD_STUDENTE, $this->password_studente);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE)) $criteria->add(StudentiCompletiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE, $this->codice_giustificazioni_studente);
        if ($this->isColumnModified(StudentiCompletiPeer::ESONERO_RELIGIONE)) $criteria->add(StudentiCompletiPeer::ESONERO_RELIGIONE, $this->esonero_religione);
        if ($this->isColumnModified(StudentiCompletiPeer::MATERIA_SOSTITUTIVA_RELIGIONE)) $criteria->add(StudentiCompletiPeer::MATERIA_SOSTITUTIVA_RELIGIONE, $this->materia_sostitutiva_religione);
        if ($this->isColumnModified(StudentiCompletiPeer::ESONERO_ED_FISICA)) $criteria->add(StudentiCompletiPeer::ESONERO_ED_FISICA, $this->esonero_ed_fisica);
        if ($this->isColumnModified(StudentiCompletiPeer::MATERIA_SOSTITUTIVA_EDFISICA)) $criteria->add(StudentiCompletiPeer::MATERIA_SOSTITUTIVA_EDFISICA, $this->materia_sostitutiva_edfisica);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_TERZA)) $criteria->add(StudentiCompletiPeer::CREDITI_TERZA, $this->crediti_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::MEDIA_VOTI_TERZA)) $criteria->add(StudentiCompletiPeer::MEDIA_VOTI_TERZA, $this->media_voti_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::DEBITI_TERZA)) $criteria->add(StudentiCompletiPeer::DEBITI_TERZA, $this->debiti_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_SOSPESI_TERZA)) $criteria->add(StudentiCompletiPeer::CREDITI_SOSPESI_TERZA, $this->crediti_sospesi_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_REINTEGRATI_TERZA)) $criteria->add(StudentiCompletiPeer::CREDITI_REINTEGRATI_TERZA, $this->crediti_reintegrati_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_QUARTA)) $criteria->add(StudentiCompletiPeer::CREDITI_QUARTA, $this->crediti_quarta);
        if ($this->isColumnModified(StudentiCompletiPeer::MEDIA_VOTI_QUARTA)) $criteria->add(StudentiCompletiPeer::MEDIA_VOTI_QUARTA, $this->media_voti_quarta);
        if ($this->isColumnModified(StudentiCompletiPeer::DEBITI_QUARTA)) $criteria->add(StudentiCompletiPeer::DEBITI_QUARTA, $this->debiti_quarta);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_SOSPESI_QUARTA)) $criteria->add(StudentiCompletiPeer::CREDITI_SOSPESI_QUARTA, $this->crediti_sospesi_quarta);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_REINTEGRATI_QUARTA)) $criteria->add(StudentiCompletiPeer::CREDITI_REINTEGRATI_QUARTA, $this->crediti_reintegrati_quarta);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_QUINTA)) $criteria->add(StudentiCompletiPeer::CREDITI_QUINTA, $this->crediti_quinta);
        if ($this->isColumnModified(StudentiCompletiPeer::MEDIA_VOTI_QUINTA)) $criteria->add(StudentiCompletiPeer::MEDIA_VOTI_QUINTA, $this->media_voti_quinta);
        if ($this->isColumnModified(StudentiCompletiPeer::CREDITI_FINALI_AGG)) $criteria->add(StudentiCompletiPeer::CREDITI_FINALI_AGG, $this->crediti_finali_agg);
        if ($this->isColumnModified(StudentiCompletiPeer::MATRICOLA)) $criteria->add(StudentiCompletiPeer::MATRICOLA, $this->matricola);
        if ($this->isColumnModified(StudentiCompletiPeer::LUOGO_NASCITA)) $criteria->add(StudentiCompletiPeer::LUOGO_NASCITA, $this->luogo_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::PROVINCIA_NASCITA)) $criteria->add(StudentiCompletiPeer::PROVINCIA_NASCITA, $this->provincia_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::MOTIVI_CREDITI_TERZA)) $criteria->add(StudentiCompletiPeer::MOTIVI_CREDITI_TERZA, $this->motivi_crediti_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::MOTIVI_CREDITI_QUARTA)) $criteria->add(StudentiCompletiPeer::MOTIVI_CREDITI_QUARTA, $this->motivi_crediti_quarta);
        if ($this->isColumnModified(StudentiCompletiPeer::MOTIVI_CREDITI_QUINTA)) $criteria->add(StudentiCompletiPeer::MOTIVI_CREDITI_QUINTA, $this->motivi_crediti_quinta);
        if ($this->isColumnModified(StudentiCompletiPeer::MOTIVI_CREDITI_AGG)) $criteria->add(StudentiCompletiPeer::MOTIVI_CREDITI_AGG, $this->motivi_crediti_agg);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_COMUNE_NASCITA)) $criteria->add(StudentiCompletiPeer::CODICE_COMUNE_NASCITA, $this->codice_comune_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::STATO_NASCITA)) $criteria->add(StudentiCompletiPeer::STATO_NASCITA, $this->stato_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::CITTADINANZA)) $criteria->add(StudentiCompletiPeer::CITTADINANZA, $this->cittadinanza);
        if ($this->isColumnModified(StudentiCompletiPeer::SECONDA_CITTADINANZA)) $criteria->add(StudentiCompletiPeer::SECONDA_CITTADINANZA, $this->seconda_cittadinanza);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_COMUNE_RESIDENZA)) $criteria->add(StudentiCompletiPeer::CODICE_COMUNE_RESIDENZA, $this->codice_comune_residenza);
        if ($this->isColumnModified(StudentiCompletiPeer::DISTRETTO)) $criteria->add(StudentiCompletiPeer::DISTRETTO, $this->distretto);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_FISCALE)) $criteria->add(StudentiCompletiPeer::CODICE_FISCALE, $this->codice_fiscale);
        if ($this->isColumnModified(StudentiCompletiPeer::MEDICO)) $criteria->add(StudentiCompletiPeer::MEDICO, $this->medico);
        if ($this->isColumnModified(StudentiCompletiPeer::TELEFONO_MEDICO)) $criteria->add(StudentiCompletiPeer::TELEFONO_MEDICO, $this->telefono_medico);
        if ($this->isColumnModified(StudentiCompletiPeer::INTOLLERANZE_ALIM)) $criteria->add(StudentiCompletiPeer::INTOLLERANZE_ALIM, $this->intolleranze_alim);
        if ($this->isColumnModified(StudentiCompletiPeer::GRUPPO_SANGUIGNO)) $criteria->add(StudentiCompletiPeer::GRUPPO_SANGUIGNO, $this->gruppo_sanguigno);
        if ($this->isColumnModified(StudentiCompletiPeer::GRUPPO_RH)) $criteria->add(StudentiCompletiPeer::GRUPPO_RH, $this->gruppo_rh);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_ASL)) $criteria->add(StudentiCompletiPeer::CODICE_ASL, $this->codice_asl);
        if ($this->isColumnModified(StudentiCompletiPeer::ANNOTAZIONI)) $criteria->add(StudentiCompletiPeer::ANNOTAZIONI, $this->annotazioni);
        if ($this->isColumnModified(StudentiCompletiPeer::STATO_CIVILE)) $criteria->add(StudentiCompletiPeer::STATO_CIVILE, $this->stato_civile);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_PRIMO_SCRITTO)) $criteria->add(StudentiCompletiPeer::VOTO_PRIMO_SCRITTO, $this->voto_primo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_SECONDO_SCRITTO)) $criteria->add(StudentiCompletiPeer::VOTO_SECONDO_SCRITTO, $this->voto_secondo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_TERZO_SCRITTO)) $criteria->add(StudentiCompletiPeer::VOTO_TERZO_SCRITTO, $this->voto_terzo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ORALE)) $criteria->add(StudentiCompletiPeer::VOTO_ORALE, $this->voto_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_BONUS)) $criteria->add(StudentiCompletiPeer::VOTO_BONUS, $this->voto_bonus);
        if ($this->isColumnModified(StudentiCompletiPeer::MATERIA_SECONDO_SCR)) $criteria->add(StudentiCompletiPeer::MATERIA_SECONDO_SCR, $this->materia_secondo_scr);
        if ($this->isColumnModified(StudentiCompletiPeer::ULTERIORI_SPECIF_DIPLOMA)) $criteria->add(StudentiCompletiPeer::ULTERIORI_SPECIF_DIPLOMA, $this->ulteriori_specif_diploma);
        if ($this->isColumnModified(StudentiCompletiPeer::NUMERO_DIPLOMA)) $criteria->add(StudentiCompletiPeer::NUMERO_DIPLOMA, $this->numero_diploma);
        if ($this->isColumnModified(StudentiCompletiPeer::CHI_INSERISCE)) $criteria->add(StudentiCompletiPeer::CHI_INSERISCE, $this->chi_inserisce);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_INSERIMENTO)) $criteria->add(StudentiCompletiPeer::DATA_INSERIMENTO, $this->data_inserimento);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_INSERIMENTO)) $criteria->add(StudentiCompletiPeer::TIPO_INSERIMENTO, $this->tipo_inserimento);
        if ($this->isColumnModified(StudentiCompletiPeer::CHI_MODIFICA)) $criteria->add(StudentiCompletiPeer::CHI_MODIFICA, $this->chi_modifica);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_MODIFICA)) $criteria->add(StudentiCompletiPeer::DATA_MODIFICA, $this->data_modifica);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_MODIFICA)) $criteria->add(StudentiCompletiPeer::TIPO_MODIFICA, $this->tipo_modifica);
        if ($this->isColumnModified(StudentiCompletiPeer::FLAG_CANC)) $criteria->add(StudentiCompletiPeer::FLAG_CANC, $this->flag_canc);
        if ($this->isColumnModified(StudentiCompletiPeer::STATO_AVANZAMENTO)) $criteria->add(StudentiCompletiPeer::STATO_AVANZAMENTO, $this->stato_avanzamento);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_STATO_AVANZAMENTO)) $criteria->add(StudentiCompletiPeer::DATA_STATO_AVANZAMENTO, $this->data_stato_avanzamento);
        if ($this->isColumnModified(StudentiCompletiPeer::CAP_PROVINCIA_NASCITA)) $criteria->add(StudentiCompletiPeer::CAP_PROVINCIA_NASCITA, $this->cap_provincia_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::BADGE)) $criteria->add(StudentiCompletiPeer::BADGE, $this->badge);
        if ($this->isColumnModified(StudentiCompletiPeer::CAP_RESIDENZA)) $criteria->add(StudentiCompletiPeer::CAP_RESIDENZA, $this->cap_residenza);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_COMUNE_DOMICILIO)) $criteria->add(StudentiCompletiPeer::CODICE_COMUNE_DOMICILIO, $this->codice_comune_domicilio);
        if ($this->isColumnModified(StudentiCompletiPeer::CAP_DOMICILIO)) $criteria->add(StudentiCompletiPeer::CAP_DOMICILIO, $this->cap_domicilio);
        if ($this->isColumnModified(StudentiCompletiPeer::CAP_NASCITA)) $criteria->add(StudentiCompletiPeer::CAP_NASCITA, $this->cap_nascita);
        if ($this->isColumnModified(StudentiCompletiPeer::INDIRIZZO_DOMICILIO)) $criteria->add(StudentiCompletiPeer::INDIRIZZO_DOMICILIO, $this->indirizzo_domicilio);
        if ($this->isColumnModified(StudentiCompletiPeer::CITTA_NASCITA_STRANIERA)) $criteria->add(StudentiCompletiPeer::CITTA_NASCITA_STRANIERA, $this->citta_nascita_straniera);
        if ($this->isColumnModified(StudentiCompletiPeer::CELLULARE_ALLIEVO)) $criteria->add(StudentiCompletiPeer::CELLULARE_ALLIEVO, $this->cellulare_allievo);
        if ($this->isColumnModified(StudentiCompletiPeer::HANDICAP)) $criteria->add(StudentiCompletiPeer::HANDICAP, $this->handicap);
        if ($this->isColumnModified(StudentiCompletiPeer::STATO_CONVITTORE)) $criteria->add(StudentiCompletiPeer::STATO_CONVITTORE, $this->stato_convittore);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_RITIRO)) $criteria->add(StudentiCompletiPeer::DATA_RITIRO, $this->data_ritiro);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_AMMISSIONE)) $criteria->add(StudentiCompletiPeer::VOTO_AMMISSIONE, $this->voto_ammissione);
        if ($this->isColumnModified(StudentiCompletiPeer::DIFFERENZA_PUNTEGGIO)) $criteria->add(StudentiCompletiPeer::DIFFERENZA_PUNTEGGIO, $this->differenza_punteggio);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_QUALIFICA)) $criteria->add(StudentiCompletiPeer::VOTO_QUALIFICA, $this->voto_qualifica);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_SC1_QUAL)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_SC1_QUAL, $this->voto_esame_sc1_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_SC2_QUAL)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_SC2_QUAL, $this->voto_esame_sc2_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_OR_QUAL)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_OR_QUAL, $this->voto_esame_or_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::STATO_PRIVATISTA)) $criteria->add(StudentiCompletiPeer::STATO_PRIVATISTA, $this->stato_privatista);
        if ($this->isColumnModified(StudentiCompletiPeer::FOTO)) $criteria->add(StudentiCompletiPeer::FOTO, $this->foto);
        if ($this->isColumnModified(StudentiCompletiPeer::RAPPRESENTANTE)) $criteria->add(StudentiCompletiPeer::RAPPRESENTANTE, $this->rappresentante);
        if ($this->isColumnModified(StudentiCompletiPeer::OBBLIGO_FORMATIVO)) $criteria->add(StudentiCompletiPeer::OBBLIGO_FORMATIVO, $this->obbligo_formativo);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_LINGUA_1)) $criteria->add(StudentiCompletiPeer::ID_LINGUA_1, $this->id_lingua_1);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_LINGUA_2)) $criteria->add(StudentiCompletiPeer::ID_LINGUA_2, $this->id_lingua_2);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_LINGUA_3)) $criteria->add(StudentiCompletiPeer::ID_LINGUA_3, $this->id_lingua_3);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_LINGUA_4)) $criteria->add(StudentiCompletiPeer::ID_LINGUA_4, $this->id_lingua_4);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_LINGUA_5)) $criteria->add(StudentiCompletiPeer::ID_LINGUA_5, $this->id_lingua_5);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_PROVENIENZA_SCOLASTICA)) $criteria->add(StudentiCompletiPeer::ID_PROVENIENZA_SCOLASTICA, $this->id_provenienza_scolastica);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_SCUOLA_MEDIA)) $criteria->add(StudentiCompletiPeer::ID_SCUOLA_MEDIA, $this->id_scuola_media);
        if ($this->isColumnModified(StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA)) $criteria->add(StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA, $this->lingua_scuola_media);
        if ($this->isColumnModified(StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA_2)) $criteria->add(StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA_2, $this->lingua_scuola_media_2);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_SCUOLA_MEDIA)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_SCUOLA_MEDIA, $this->giudizio_scuola_media);
        if ($this->isColumnModified(StudentiCompletiPeer::TRASPORTO)) $criteria->add(StudentiCompletiPeer::TRASPORTO, $this->trasporto);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_ISCRIZIONE)) $criteria->add(StudentiCompletiPeer::DATA_ISCRIZIONE, $this->data_iscrizione);
        if ($this->isColumnModified(StudentiCompletiPeer::PEI)) $criteria->add(StudentiCompletiPeer::PEI, $this->pei);
        if ($this->isColumnModified(StudentiCompletiPeer::AMMESSO_ESAME_QUALIFICA)) $criteria->add(StudentiCompletiPeer::AMMESSO_ESAME_QUALIFICA, $this->ammesso_esame_qualifica);
        if ($this->isColumnModified(StudentiCompletiPeer::AMMESSO_ESAME_QUINTA)) $criteria->add(StudentiCompletiPeer::AMMESSO_ESAME_QUINTA, $this->ammesso_esame_quinta);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_QUINTA)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_QUINTA, $this->giudizio_ammissione_quinta);
        if ($this->isColumnModified(StudentiCompletiPeer::GRADO_HANDICAP)) $criteria->add(StudentiCompletiPeer::GRADO_HANDICAP, $this->grado_handicap);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_HANDICAP)) $criteria->add(StudentiCompletiPeer::TIPO_HANDICAP, $this->tipo_handicap);
        if ($this->isColumnModified(StudentiCompletiPeer::STATO_LICENZA_MAESTRO)) $criteria->add(StudentiCompletiPeer::STATO_LICENZA_MAESTRO, $this->stato_licenza_maestro);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_STUDENTE_SISSI)) $criteria->add(StudentiCompletiPeer::ID_STUDENTE_SISSI, $this->id_studente_sissi);
        if ($this->isColumnModified(StudentiCompletiPeer::BADGE_RFID)) $criteria->add(StudentiCompletiPeer::BADGE_RFID, $this->badge_rfid);
        if ($this->isColumnModified(StudentiCompletiPeer::LODE)) $criteria->add(StudentiCompletiPeer::LODE, $this->lode);
        if ($this->isColumnModified(StudentiCompletiPeer::DISTRETTO_SCOLASTICO)) $criteria->add(StudentiCompletiPeer::DISTRETTO_SCOLASTICO, $this->distretto_scolastico);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_TERZA)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_TERZA, $this->giudizio_ammissione_terza);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_PRIMA_MEDIA)) $criteria->add(StudentiCompletiPeer::ESITO_PRIMA_MEDIA, $this->esito_prima_media);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_SECONDA_MEDIA)) $criteria->add(StudentiCompletiPeer::ESITO_SECONDA_MEDIA, $this->esito_seconda_media);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_TERZA_MEDIA)) $criteria->add(StudentiCompletiPeer::ESITO_TERZA_MEDIA, $this->esito_terza_media);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_ESAME_SC1_QUAL)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_ESAME_SC1_QUAL, $this->giudizio_esame_sc1_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_ESAME_SC2_QUAL)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_ESAME_SC2_QUAL, $this->giudizio_esame_sc2_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_ESAME_OR_QUAL)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_ESAME_OR_QUAL, $this->giudizio_esame_or_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL, $this->giudizio_complessivo_esame_qual);
        if ($this->isColumnModified(StudentiCompletiPeer::ACCONSENTE_AZIENDE)) $criteria->add(StudentiCompletiPeer::ACCONSENTE_AZIENDE, $this->acconsente_aziende);
        if ($this->isColumnModified(StudentiCompletiPeer::CURRICULUM_PRIMA)) $criteria->add(StudentiCompletiPeer::CURRICULUM_PRIMA, $this->curriculum_prima);
        if ($this->isColumnModified(StudentiCompletiPeer::CURRICULUM_SECONDA)) $criteria->add(StudentiCompletiPeer::CURRICULUM_SECONDA, $this->curriculum_seconda);
        if ($this->isColumnModified(StudentiCompletiPeer::STAGE_PROFESSIONALI)) $criteria->add(StudentiCompletiPeer::STAGE_PROFESSIONALI, $this->stage_professionali);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_ORALE)) $criteria->add(StudentiCompletiPeer::DATA_ORALE, $this->data_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::ORDINE_ESAME_ORALE)) $criteria->add(StudentiCompletiPeer::ORDINE_ESAME_ORALE, $this->ordine_esame_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_PRIMO_SCRITTO)) $criteria->add(StudentiCompletiPeer::TIPO_PRIMO_SCRITTO, $this->tipo_primo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_SECONDO_SCRITTO)) $criteria->add(StudentiCompletiPeer::TIPO_SECONDO_SCRITTO, $this->tipo_secondo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_TERZO_SCRITTO)) $criteria->add(StudentiCompletiPeer::TIPO_TERZO_SCRITTO, $this->tipo_terzo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::UNANIMITA_PRIMO_SCRITTO)) $criteria->add(StudentiCompletiPeer::UNANIMITA_PRIMO_SCRITTO, $this->unanimita_primo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::UNANIMITA_SECONDO_SCRITTO)) $criteria->add(StudentiCompletiPeer::UNANIMITA_SECONDO_SCRITTO, $this->unanimita_secondo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::UNANIMITA_TERZO_SCRITTO)) $criteria->add(StudentiCompletiPeer::UNANIMITA_TERZO_SCRITTO, $this->unanimita_terzo_scritto);
        if ($this->isColumnModified(StudentiCompletiPeer::ARGOMENTO_SCELTO_ORALE)) $criteria->add(StudentiCompletiPeer::ARGOMENTO_SCELTO_ORALE, $this->argomento_scelto_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::AREA_DISC_1_ORALE)) $criteria->add(StudentiCompletiPeer::AREA_DISC_1_ORALE, $this->area_disc_1_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::AREA_DISC_2_ORALE)) $criteria->add(StudentiCompletiPeer::AREA_DISC_2_ORALE, $this->area_disc_2_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::DISC_ELABORATI_ORALE)) $criteria->add(StudentiCompletiPeer::DISC_ELABORATI_ORALE, $this->disc_elaborati_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::UNANIMITA_VOTO_FINALE)) $criteria->add(StudentiCompletiPeer::UNANIMITA_VOTO_FINALE, $this->unanimita_voto_finale);
        if ($this->isColumnModified(StudentiCompletiPeer::PRESENTE_ESAME_QUINTA)) $criteria->add(StudentiCompletiPeer::PRESENTE_ESAME_QUINTA, $this->presente_esame_quinta);
        if ($this->isColumnModified(StudentiCompletiPeer::STAMPA_BADGE)) $criteria->add(StudentiCompletiPeer::STAMPA_BADGE, $this->stampa_badge);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_CLASSE_DESTINAZIONE)) $criteria->add(StudentiCompletiPeer::ID_CLASSE_DESTINAZIONE, $this->id_classe_destinazione);
        if ($this->isColumnModified(StudentiCompletiPeer::SCONTO_RETTE)) $criteria->add(StudentiCompletiPeer::SCONTO_RETTE, $this->sconto_rette);
        if ($this->isColumnModified(StudentiCompletiPeer::CARTA_STUDENTE_NUMERO)) $criteria->add(StudentiCompletiPeer::CARTA_STUDENTE_NUMERO, $this->carta_studente_numero);
        if ($this->isColumnModified(StudentiCompletiPeer::CARTA_STUDENTE_SCADENZA)) $criteria->add(StudentiCompletiPeer::CARTA_STUDENTE_SCADENZA, $this->carta_studente_scadenza);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_CORRENTE_CALCOLATO)) $criteria->add(StudentiCompletiPeer::ESITO_CORRENTE_CALCOLATO, $this->esito_corrente_calcolato);
        if ($this->isColumnModified(StudentiCompletiPeer::ID_FLUSSO)) $criteria->add(StudentiCompletiPeer::ID_FLUSSO, $this->id_flusso);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_AGGIORNAMENTO_SOGEI)) $criteria->add(StudentiCompletiPeer::DATA_AGGIORNAMENTO_SOGEI, $this->data_aggiornamento_sogei);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_ALUNNO_MINISTERIALE)) $criteria->add(StudentiCompletiPeer::CODICE_ALUNNO_MINISTERIALE, $this->codice_alunno_ministeriale);
        if ($this->isColumnModified(StudentiCompletiPeer::FLAG_CF_FITTIZIO)) $criteria->add(StudentiCompletiPeer::FLAG_CF_FITTIZIO, $this->flag_cf_fittizio);
        if ($this->isColumnModified(StudentiCompletiPeer::FLAG_S2F)) $criteria->add(StudentiCompletiPeer::FLAG_S2F, $this->flag_s2f);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_STATO_SOGEI)) $criteria->add(StudentiCompletiPeer::CODICE_STATO_SOGEI, $this->codice_stato_sogei);
        if ($this->isColumnModified(StudentiCompletiPeer::CODICE_GRUPPO_NOMADE)) $criteria->add(StudentiCompletiPeer::CODICE_GRUPPO_NOMADE, $this->codice_gruppo_nomade);
        if ($this->isColumnModified(StudentiCompletiPeer::FLAG_MINORE_STRANIERO)) $criteria->add(StudentiCompletiPeer::FLAG_MINORE_STRANIERO, $this->flag_minore_straniero);
        if ($this->isColumnModified(StudentiCompletiPeer::CHIAVE)) $criteria->add(StudentiCompletiPeer::CHIAVE, $this->chiave);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_ITALIANO)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_ITALIANO, $this->voto_esame_medie_italiano);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INGLESE)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INGLESE, $this->voto_esame_medie_inglese);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_MATEMATICA)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_MATEMATICA, $this->voto_esame_medie_matematica);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA, $this->voto_esame_medie_seconda_lingua);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA, $this->voto_esame_medie_invalsi_ita);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT, $this->voto_esame_medie_invalsi_mat);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_ESAME_MEDIE_ORALE)) $criteria->add(StudentiCompletiPeer::VOTO_ESAME_MEDIE_ORALE, $this->voto_esame_medie_orale);
        if ($this->isColumnModified(StudentiCompletiPeer::VOTO_AMMISSIONE_MEDIE)) $criteria->add(StudentiCompletiPeer::VOTO_AMMISSIONE_MEDIE, $this->voto_ammissione_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_PRIMA_ELEMENTARE)) $criteria->add(StudentiCompletiPeer::ESITO_PRIMA_ELEMENTARE, $this->esito_prima_elementare);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_SECONDA_ELEMENTARE)) $criteria->add(StudentiCompletiPeer::ESITO_SECONDA_ELEMENTARE, $this->esito_seconda_elementare);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_TERZA_ELEMENTARE)) $criteria->add(StudentiCompletiPeer::ESITO_TERZA_ELEMENTARE, $this->esito_terza_elementare);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_QUARTA_ELEMENTARE)) $criteria->add(StudentiCompletiPeer::ESITO_QUARTA_ELEMENTARE, $this->esito_quarta_elementare);
        if ($this->isColumnModified(StudentiCompletiPeer::ESITO_QUINTA_ELEMENTARE)) $criteria->add(StudentiCompletiPeer::ESITO_QUINTA_ELEMENTARE, $this->esito_quinta_elementare);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO)) $criteria->add(StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO, $this->tipo_voto_esame_medie_italiano);
        if ($this->isColumnModified(StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE)) $criteria->add(StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE, $this->tipo_voto_esame_medie_inglese);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_1_MEDIE)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_1_MEDIE, $this->giudizio_1_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_2_MEDIE)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_2_MEDIE, $this->giudizio_2_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_3_MEDIE)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_3_MEDIE, $this->giudizio_3_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::ARGOMENTI_ORALI_MEDIE)) $criteria->add(StudentiCompletiPeer::ARGOMENTI_ORALI_MEDIE, $this->argomenti_orali_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_FINALE_1_MEDIE)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_FINALE_1_MEDIE, $this->giudizio_finale_1_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_FINALE_2_MEDIE)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_FINALE_2_MEDIE, $this->giudizio_finale_2_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_FINALE_3_MEDIE)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_FINALE_3_MEDIE, $this->giudizio_finale_3_medie);
        if ($this->isColumnModified(StudentiCompletiPeer::CONSIGLIO_TERZA_MEDIA)) $criteria->add(StudentiCompletiPeer::CONSIGLIO_TERZA_MEDIA, $this->consiglio_terza_media);
        if ($this->isColumnModified(StudentiCompletiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA)) $criteria->add(StudentiCompletiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA, $this->giudizio_sintetico_esame_terza_media);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_ARRIVO_IN_ITALIA)) $criteria->add(StudentiCompletiPeer::DATA_ARRIVO_IN_ITALIA, $this->data_arrivo_in_italia);
        if ($this->isColumnModified(StudentiCompletiPeer::FREQUENZA_ASILO_NIDO)) $criteria->add(StudentiCompletiPeer::FREQUENZA_ASILO_NIDO, $this->frequenza_asilo_nido);
        if ($this->isColumnModified(StudentiCompletiPeer::FREQUENZA_SCUOLA_MATERNA)) $criteria->add(StudentiCompletiPeer::FREQUENZA_SCUOLA_MATERNA, $this->frequenza_scuola_materna);
        if ($this->isColumnModified(StudentiCompletiPeer::DATA_AGGIORNAMENTO_SIDI)) $criteria->add(StudentiCompletiPeer::DATA_AGGIORNAMENTO_SIDI, $this->data_aggiornamento_sidi);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_VAL_ITA)) $criteria->add(StudentiCompletiPeer::CMP_SUP_VAL_ITA, $this->cmp_sup_val_ita);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_TXT_ITA)) $criteria->add(StudentiCompletiPeer::CMP_SUP_TXT_ITA, $this->cmp_sup_txt_ita);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_VAL_ING)) $criteria->add(StudentiCompletiPeer::CMP_SUP_VAL_ING, $this->cmp_sup_val_ing);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_TXT_ING)) $criteria->add(StudentiCompletiPeer::CMP_SUP_TXT_ING, $this->cmp_sup_txt_ing);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_VAL_ALTRI)) $criteria->add(StudentiCompletiPeer::CMP_SUP_VAL_ALTRI, $this->cmp_sup_val_altri);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_TXT_ALTRI)) $criteria->add(StudentiCompletiPeer::CMP_SUP_TXT_ALTRI, $this->cmp_sup_txt_altri);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_VAL_MAT)) $criteria->add(StudentiCompletiPeer::CMP_SUP_VAL_MAT, $this->cmp_sup_val_mat);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_TXT_MAT)) $criteria->add(StudentiCompletiPeer::CMP_SUP_TXT_MAT, $this->cmp_sup_txt_mat);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_VAL_SCI_TEC)) $criteria->add(StudentiCompletiPeer::CMP_SUP_VAL_SCI_TEC, $this->cmp_sup_val_sci_tec);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_TXT_SCI_TEC)) $criteria->add(StudentiCompletiPeer::CMP_SUP_TXT_SCI_TEC, $this->cmp_sup_txt_sci_tec);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_VAL_STO_SOC)) $criteria->add(StudentiCompletiPeer::CMP_SUP_VAL_STO_SOC, $this->cmp_sup_val_sto_soc);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_SUP_TXT_STO_SOC)) $criteria->add(StudentiCompletiPeer::CMP_SUP_TXT_STO_SOC, $this->cmp_sup_txt_sto_soc);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_ITA)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_ITA, $this->cmp_med_val_ita);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_ITA)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_ITA, $this->cmp_med_txt_ita);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_ING)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_ING, $this->cmp_med_val_ing);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_ING)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_ING, $this->cmp_med_txt_ing);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_ALTRI)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_ALTRI, $this->cmp_med_val_altri);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_ALTRI)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_ALTRI, $this->cmp_med_txt_altri);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_MAT)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_MAT, $this->cmp_med_val_mat);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_MAT)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_MAT, $this->cmp_med_txt_mat);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_SCI_TEC)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_SCI_TEC, $this->cmp_med_val_sci_tec);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_SCI_TEC)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_SCI_TEC, $this->cmp_med_txt_sci_tec);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_STO_SOC)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_STO_SOC, $this->cmp_med_val_sto_soc);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_STO_SOC)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_STO_SOC, $this->cmp_med_txt_sto_soc);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_L2)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_L2, $this->cmp_med_val_l2);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_L2)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_L2, $this->cmp_med_txt_l2);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_L3)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_L3, $this->cmp_med_val_l3);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_L3)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_L3, $this->cmp_med_txt_l3);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_ARTE)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_ARTE, $this->cmp_med_val_arte);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_ARTE)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_ARTE, $this->cmp_med_txt_arte);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_MUS)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_MUS, $this->cmp_med_val_mus);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_MUS)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_MUS, $this->cmp_med_txt_mus);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_VAL_MOT)) $criteria->add(StudentiCompletiPeer::CMP_MED_VAL_MOT, $this->cmp_med_val_mot);
        if ($this->isColumnModified(StudentiCompletiPeer::CMP_MED_TXT_MOT)) $criteria->add(StudentiCompletiPeer::CMP_MED_TXT_MOT, $this->cmp_med_txt_mot);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(StudentiCompletiPeer::DATABASE_NAME);
        $criteria->add(StudentiCompletiPeer::ID_STUDENTE, $this->id_studente);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getIdStudente();
    }

    /**
     * Generic method to set the primary key (id_studente column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setIdStudente($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getIdStudente();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of StudentiCompleti (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setNome($this->getNome());
        $copyObj->setCognome($this->getCognome());
        $copyObj->setIndirizzo($this->getIndirizzo());
        $copyObj->setCitta($this->getCitta());
        $copyObj->setCap($this->getCap());
        $copyObj->setProvincia($this->getProvincia());
        $copyObj->setSesso($this->getSesso());
        $copyObj->setTelefono($this->getTelefono());
        $copyObj->setCellulare1($this->getCellulare1());
        $copyObj->setCellulare2($this->getCellulare2());
        $copyObj->setEmail1($this->getEmail1());
        $copyObj->setEmail2($this->getEmail2());
        $copyObj->setInvioEmail($this->getInvioEmail());
        $copyObj->setInvioEmailCumulativo($this->getInvioEmailCumulativo());
        $copyObj->setInvioEmailParametrico($this->getInvioEmailParametrico());
        $copyObj->setInvioEmailTemporale($this->getInvioEmailTemporale());
        $copyObj->setTipoSms($this->getTipoSms());
        $copyObj->setTipoSmsCumulativo($this->getTipoSmsCumulativo());
        $copyObj->setTipoSmsParametrico($this->getTipoSmsParametrico());
        $copyObj->setTipoSmsTemporale($this->getTipoSmsTemporale());
        $copyObj->setAutEntrataRitardo($this->getAutEntrataRitardo());
        $copyObj->setAutUscitaAnticipo($this->getAutUscitaAnticipo());
        $copyObj->setAutPomeriggio($this->getAutPomeriggio());
        $copyObj->setAcconsente($this->getAcconsente());
        $copyObj->setRitirato($this->getRitirato());
        $copyObj->setDataNascita($this->getDataNascita());
        $copyObj->setCodiceStudente($this->getCodiceStudente());
        $copyObj->setPasswordStudente($this->getPasswordStudente());
        $copyObj->setCodiceGiustificazioniStudente($this->getCodiceGiustificazioniStudente());
        $copyObj->setEsoneroReligione($this->getEsoneroReligione());
        $copyObj->setMateriaSostitutivaReligione($this->getMateriaSostitutivaReligione());
        $copyObj->setEsoneroEdFisica($this->getEsoneroEdFisica());
        $copyObj->setMateriaSostitutivaEdfisica($this->getMateriaSostitutivaEdfisica());
        $copyObj->setCreditiTerza($this->getCreditiTerza());
        $copyObj->setMediaVotiTerza($this->getMediaVotiTerza());
        $copyObj->setDebitiTerza($this->getDebitiTerza());
        $copyObj->setCreditiSospesiTerza($this->getCreditiSospesiTerza());
        $copyObj->setCreditiReintegratiTerza($this->getCreditiReintegratiTerza());
        $copyObj->setCreditiQuarta($this->getCreditiQuarta());
        $copyObj->setMediaVotiQuarta($this->getMediaVotiQuarta());
        $copyObj->setDebitiQuarta($this->getDebitiQuarta());
        $copyObj->setCreditiSospesiQuarta($this->getCreditiSospesiQuarta());
        $copyObj->setCreditiReintegratiQuarta($this->getCreditiReintegratiQuarta());
        $copyObj->setCreditiQuinta($this->getCreditiQuinta());
        $copyObj->setMediaVotiQuinta($this->getMediaVotiQuinta());
        $copyObj->setCreditiFinaliAgg($this->getCreditiFinaliAgg());
        $copyObj->setMatricola($this->getMatricola());
        $copyObj->setLuogoNascita($this->getLuogoNascita());
        $copyObj->setProvinciaNascita($this->getProvinciaNascita());
        $copyObj->setMotiviCreditiTerza($this->getMotiviCreditiTerza());
        $copyObj->setMotiviCreditiQuarta($this->getMotiviCreditiQuarta());
        $copyObj->setMotiviCreditiQuinta($this->getMotiviCreditiQuinta());
        $copyObj->setMotiviCreditiAgg($this->getMotiviCreditiAgg());
        $copyObj->setCodiceComuneNascita($this->getCodiceComuneNascita());
        $copyObj->setStatoNascita($this->getStatoNascita());
        $copyObj->setCittadinanza($this->getCittadinanza());
        $copyObj->setSecondaCittadinanza($this->getSecondaCittadinanza());
        $copyObj->setCodiceComuneResidenza($this->getCodiceComuneResidenza());
        $copyObj->setDistretto($this->getDistretto());
        $copyObj->setCodiceFiscale($this->getCodiceFiscale());
        $copyObj->setMedico($this->getMedico());
        $copyObj->setTelefonoMedico($this->getTelefonoMedico());
        $copyObj->setIntolleranzeAlim($this->getIntolleranzeAlim());
        $copyObj->setGruppoSanguigno($this->getGruppoSanguigno());
        $copyObj->setGruppoRh($this->getGruppoRh());
        $copyObj->setCodiceAsl($this->getCodiceAsl());
        $copyObj->setAnnotazioni($this->getAnnotazioni());
        $copyObj->setStatoCivile($this->getStatoCivile());
        $copyObj->setVotoPrimoScritto($this->getVotoPrimoScritto());
        $copyObj->setVotoSecondoScritto($this->getVotoSecondoScritto());
        $copyObj->setVotoTerzoScritto($this->getVotoTerzoScritto());
        $copyObj->setVotoOrale($this->getVotoOrale());
        $copyObj->setVotoBonus($this->getVotoBonus());
        $copyObj->setMateriaSecondoScr($this->getMateriaSecondoScr());
        $copyObj->setUlterioriSpecifDiploma($this->getUlterioriSpecifDiploma());
        $copyObj->setNumeroDiploma($this->getNumeroDiploma());
        $copyObj->setChiInserisce($this->getChiInserisce());
        $copyObj->setDataInserimento($this->getDataInserimento());
        $copyObj->setTipoInserimento($this->getTipoInserimento());
        $copyObj->setChiModifica($this->getChiModifica());
        $copyObj->setDataModifica($this->getDataModifica());
        $copyObj->setTipoModifica($this->getTipoModifica());
        $copyObj->setFlagCanc($this->getFlagCanc());
        $copyObj->setStatoAvanzamento($this->getStatoAvanzamento());
        $copyObj->setDataStatoAvanzamento($this->getDataStatoAvanzamento());
        $copyObj->setCapProvinciaNascita($this->getCapProvinciaNascita());
        $copyObj->setBadge($this->getBadge());
        $copyObj->setCapResidenza($this->getCapResidenza());
        $copyObj->setCodiceComuneDomicilio($this->getCodiceComuneDomicilio());
        $copyObj->setCapDomicilio($this->getCapDomicilio());
        $copyObj->setCapNascita($this->getCapNascita());
        $copyObj->setIndirizzoDomicilio($this->getIndirizzoDomicilio());
        $copyObj->setCittaNascitaStraniera($this->getCittaNascitaStraniera());
        $copyObj->setCellulareAllievo($this->getCellulareAllievo());
        $copyObj->setHandicap($this->getHandicap());
        $copyObj->setStatoConvittore($this->getStatoConvittore());
        $copyObj->setDataRitiro($this->getDataRitiro());
        $copyObj->setVotoAmmissione($this->getVotoAmmissione());
        $copyObj->setDifferenzaPunteggio($this->getDifferenzaPunteggio());
        $copyObj->setVotoQualifica($this->getVotoQualifica());
        $copyObj->setVotoEsameSc1Qual($this->getVotoEsameSc1Qual());
        $copyObj->setVotoEsameSc2Qual($this->getVotoEsameSc2Qual());
        $copyObj->setVotoEsameOrQual($this->getVotoEsameOrQual());
        $copyObj->setStatoPrivatista($this->getStatoPrivatista());
        $copyObj->setFoto($this->getFoto());
        $copyObj->setRappresentante($this->getRappresentante());
        $copyObj->setObbligoFormativo($this->getObbligoFormativo());
        $copyObj->setIdLingua1($this->getIdLingua1());
        $copyObj->setIdLingua2($this->getIdLingua2());
        $copyObj->setIdLingua3($this->getIdLingua3());
        $copyObj->setIdLingua4($this->getIdLingua4());
        $copyObj->setIdLingua5($this->getIdLingua5());
        $copyObj->setIdProvenienzaScolastica($this->getIdProvenienzaScolastica());
        $copyObj->setIdScuolaMedia($this->getIdScuolaMedia());
        $copyObj->setLinguaScuolaMedia($this->getLinguaScuolaMedia());
        $copyObj->setLinguaScuolaMedia2($this->getLinguaScuolaMedia2());
        $copyObj->setGiudizioScuolaMedia($this->getGiudizioScuolaMedia());
        $copyObj->setTrasporto($this->getTrasporto());
        $copyObj->setDataIscrizione($this->getDataIscrizione());
        $copyObj->setPei($this->getPei());
        $copyObj->setAmmessoEsameQualifica($this->getAmmessoEsameQualifica());
        $copyObj->setAmmessoEsameQuinta($this->getAmmessoEsameQuinta());
        $copyObj->setGiudizioAmmissioneQuinta($this->getGiudizioAmmissioneQuinta());
        $copyObj->setGradoHandicap($this->getGradoHandicap());
        $copyObj->setTipoHandicap($this->getTipoHandicap());
        $copyObj->setStatoLicenzaMaestro($this->getStatoLicenzaMaestro());
        $copyObj->setIdStudenteSissi($this->getIdStudenteSissi());
        $copyObj->setBadgeRfid($this->getBadgeRfid());
        $copyObj->setLode($this->getLode());
        $copyObj->setDistrettoScolastico($this->getDistrettoScolastico());
        $copyObj->setGiudizioAmmissioneTerza($this->getGiudizioAmmissioneTerza());
        $copyObj->setEsitoPrimaMedia($this->getEsitoPrimaMedia());
        $copyObj->setEsitoSecondaMedia($this->getEsitoSecondaMedia());
        $copyObj->setEsitoTerzaMedia($this->getEsitoTerzaMedia());
        $copyObj->setGiudizioEsameSc1Qual($this->getGiudizioEsameSc1Qual());
        $copyObj->setGiudizioEsameSc2Qual($this->getGiudizioEsameSc2Qual());
        $copyObj->setGiudizioEsameOrQual($this->getGiudizioEsameOrQual());
        $copyObj->setGiudizioComplessivoEsameQual($this->getGiudizioComplessivoEsameQual());
        $copyObj->setAcconsenteAziende($this->getAcconsenteAziende());
        $copyObj->setCurriculumPrima($this->getCurriculumPrima());
        $copyObj->setCurriculumSeconda($this->getCurriculumSeconda());
        $copyObj->setStageProfessionali($this->getStageProfessionali());
        $copyObj->setDataOrale($this->getDataOrale());
        $copyObj->setOrdineEsameOrale($this->getOrdineEsameOrale());
        $copyObj->setTipoPrimoScritto($this->getTipoPrimoScritto());
        $copyObj->setTipoSecondoScritto($this->getTipoSecondoScritto());
        $copyObj->setTipoTerzoScritto($this->getTipoTerzoScritto());
        $copyObj->setUnanimitaPrimoScritto($this->getUnanimitaPrimoScritto());
        $copyObj->setUnanimitaSecondoScritto($this->getUnanimitaSecondoScritto());
        $copyObj->setUnanimitaTerzoScritto($this->getUnanimitaTerzoScritto());
        $copyObj->setArgomentoSceltoOrale($this->getArgomentoSceltoOrale());
        $copyObj->setAreaDisc1Orale($this->getAreaDisc1Orale());
        $copyObj->setAreaDisc2Orale($this->getAreaDisc2Orale());
        $copyObj->setDiscElaboratiOrale($this->getDiscElaboratiOrale());
        $copyObj->setUnanimitaVotoFinale($this->getUnanimitaVotoFinale());
        $copyObj->setPresenteEsameQuinta($this->getPresenteEsameQuinta());
        $copyObj->setStampaBadge($this->getStampaBadge());
        $copyObj->setIdClasseDestinazione($this->getIdClasseDestinazione());
        $copyObj->setScontoRette($this->getScontoRette());
        $copyObj->setCartaStudenteNumero($this->getCartaStudenteNumero());
        $copyObj->setCartaStudenteScadenza($this->getCartaStudenteScadenza());
        $copyObj->setEsitoCorrenteCalcolato($this->getEsitoCorrenteCalcolato());
        $copyObj->setIdFlusso($this->getIdFlusso());
        $copyObj->setDataAggiornamentoSogei($this->getDataAggiornamentoSogei());
        $copyObj->setCodiceAlunnoMinisteriale($this->getCodiceAlunnoMinisteriale());
        $copyObj->setFlagCfFittizio($this->getFlagCfFittizio());
        $copyObj->setFlagS2f($this->getFlagS2f());
        $copyObj->setCodiceStatoSogei($this->getCodiceStatoSogei());
        $copyObj->setCodiceGruppoNomade($this->getCodiceGruppoNomade());
        $copyObj->setFlagMinoreStraniero($this->getFlagMinoreStraniero());
        $copyObj->setChiave($this->getChiave());
        $copyObj->setVotoEsameMedieItaliano($this->getVotoEsameMedieItaliano());
        $copyObj->setVotoEsameMedieInglese($this->getVotoEsameMedieInglese());
        $copyObj->setVotoEsameMedieMatematica($this->getVotoEsameMedieMatematica());
        $copyObj->setVotoEsameMedieSecondaLingua($this->getVotoEsameMedieSecondaLingua());
        $copyObj->setVotoEsameMedieInvalsiIta($this->getVotoEsameMedieInvalsiIta());
        $copyObj->setVotoEsameMedieInvalsiMat($this->getVotoEsameMedieInvalsiMat());
        $copyObj->setVotoEsameMedieOrale($this->getVotoEsameMedieOrale());
        $copyObj->setVotoAmmissioneMedie($this->getVotoAmmissioneMedie());
        $copyObj->setEsitoPrimaElementare($this->getEsitoPrimaElementare());
        $copyObj->setEsitoSecondaElementare($this->getEsitoSecondaElementare());
        $copyObj->setEsitoTerzaElementare($this->getEsitoTerzaElementare());
        $copyObj->setEsitoQuartaElementare($this->getEsitoQuartaElementare());
        $copyObj->setEsitoQuintaElementare($this->getEsitoQuintaElementare());
        $copyObj->setTipoVotoEsameMedieItaliano($this->getTipoVotoEsameMedieItaliano());
        $copyObj->setTipoVotoEsameMedieInglese($this->getTipoVotoEsameMedieInglese());
        $copyObj->setGiudizio1Medie($this->getGiudizio1Medie());
        $copyObj->setGiudizio2Medie($this->getGiudizio2Medie());
        $copyObj->setGiudizio3Medie($this->getGiudizio3Medie());
        $copyObj->setArgomentiOraliMedie($this->getArgomentiOraliMedie());
        $copyObj->setGiudizioFinale1Medie($this->getGiudizioFinale1Medie());
        $copyObj->setGiudizioFinale2Medie($this->getGiudizioFinale2Medie());
        $copyObj->setGiudizioFinale3Medie($this->getGiudizioFinale3Medie());
        $copyObj->setConsiglioTerzaMedia($this->getConsiglioTerzaMedia());
        $copyObj->setGiudizioSinteticoEsameTerzaMedia($this->getGiudizioSinteticoEsameTerzaMedia());
        $copyObj->setDataArrivoInItalia($this->getDataArrivoInItalia());
        $copyObj->setFrequenzaAsiloNido($this->getFrequenzaAsiloNido());
        $copyObj->setFrequenzaScuolaMaterna($this->getFrequenzaScuolaMaterna());
        $copyObj->setDataAggiornamentoSidi($this->getDataAggiornamentoSidi());
        $copyObj->setCmpSupValIta($this->getCmpSupValIta());
        $copyObj->setCmpSupTxtIta($this->getCmpSupTxtIta());
        $copyObj->setCmpSupValIng($this->getCmpSupValIng());
        $copyObj->setCmpSupTxtIng($this->getCmpSupTxtIng());
        $copyObj->setCmpSupValAltri($this->getCmpSupValAltri());
        $copyObj->setCmpSupTxtAltri($this->getCmpSupTxtAltri());
        $copyObj->setCmpSupValMat($this->getCmpSupValMat());
        $copyObj->setCmpSupTxtMat($this->getCmpSupTxtMat());
        $copyObj->setCmpSupValSciTec($this->getCmpSupValSciTec());
        $copyObj->setCmpSupTxtSciTec($this->getCmpSupTxtSciTec());
        $copyObj->setCmpSupValStoSoc($this->getCmpSupValStoSoc());
        $copyObj->setCmpSupTxtStoSoc($this->getCmpSupTxtStoSoc());
        $copyObj->setCmpMedValIta($this->getCmpMedValIta());
        $copyObj->setCmpMedTxtIta($this->getCmpMedTxtIta());
        $copyObj->setCmpMedValIng($this->getCmpMedValIng());
        $copyObj->setCmpMedTxtIng($this->getCmpMedTxtIng());
        $copyObj->setCmpMedValAltri($this->getCmpMedValAltri());
        $copyObj->setCmpMedTxtAltri($this->getCmpMedTxtAltri());
        $copyObj->setCmpMedValMat($this->getCmpMedValMat());
        $copyObj->setCmpMedTxtMat($this->getCmpMedTxtMat());
        $copyObj->setCmpMedValSciTec($this->getCmpMedValSciTec());
        $copyObj->setCmpMedTxtSciTec($this->getCmpMedTxtSciTec());
        $copyObj->setCmpMedValStoSoc($this->getCmpMedValStoSoc());
        $copyObj->setCmpMedTxtStoSoc($this->getCmpMedTxtStoSoc());
        $copyObj->setCmpMedValL2($this->getCmpMedValL2());
        $copyObj->setCmpMedTxtL2($this->getCmpMedTxtL2());
        $copyObj->setCmpMedValL3($this->getCmpMedValL3());
        $copyObj->setCmpMedTxtL3($this->getCmpMedTxtL3());
        $copyObj->setCmpMedValArte($this->getCmpMedValArte());
        $copyObj->setCmpMedTxtArte($this->getCmpMedTxtArte());
        $copyObj->setCmpMedValMus($this->getCmpMedValMus());
        $copyObj->setCmpMedTxtMus($this->getCmpMedTxtMus());
        $copyObj->setCmpMedValMot($this->getCmpMedValMot());
        $copyObj->setCmpMedTxtMot($this->getCmpMedTxtMot());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getTasses() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addTasse($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setIdStudente(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return StudentiCompleti Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return StudentiCompletiPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new StudentiCompletiPeer();
        }

        return self::$peer;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Tasse' == $relationName) {
            $this->initTasses();
        }
    }

    /**
     * Clears out the collTasses collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return StudentiCompleti The current object (for fluent API support)
     * @see        addTasses()
     */
    public function clearTasses()
    {
        $this->collTasses = null; // important to set this to null since that means it is uninitialized
        $this->collTassesPartial = null;

        return $this;
    }

    /**
     * reset is the collTasses collection loaded partially
     *
     * @return void
     */
    public function resetPartialTasses($v = true)
    {
        $this->collTassesPartial = $v;
    }

    /**
     * Initializes the collTasses collection.
     *
     * By default this just sets the collTasses collection to an empty array (like clearcollTasses());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initTasses($overrideExisting = true)
    {
        if (null !== $this->collTasses && !$overrideExisting) {
            return;
        }
        $this->collTasses = new PropelObjectCollection();
        $this->collTasses->setModel('Tasse');
    }

    /**
     * Gets an array of Tasse objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this StudentiCompleti is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Tasse[] List of Tasse objects
     * @throws PropelException
     */
    public function getTasses($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collTassesPartial && !$this->isNew();
        if (null === $this->collTasses || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collTasses) {
                // return empty collection
                $this->initTasses();
            } else {
                $collTasses = TasseQuery::create(null, $criteria)
                    ->filterByStudentiCompleti($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collTassesPartial && count($collTasses)) {
                      $this->initTasses(false);

                      foreach ($collTasses as $obj) {
                        if (false == $this->collTasses->contains($obj)) {
                          $this->collTasses->append($obj);
                        }
                      }

                      $this->collTassesPartial = true;
                    }

                    $collTasses->getInternalIterator()->rewind();

                    return $collTasses;
                }

                if ($partial && $this->collTasses) {
                    foreach ($this->collTasses as $obj) {
                        if ($obj->isNew()) {
                            $collTasses[] = $obj;
                        }
                    }
                }

                $this->collTasses = $collTasses;
                $this->collTassesPartial = false;
            }
        }

        return $this->collTasses;
    }

    /**
     * Sets a collection of Tasse objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $tasses A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function setTasses(PropelCollection $tasses, PropelPDO $con = null)
    {
        $tassesToDelete = $this->getTasses(new Criteria(), $con)->diff($tasses);


        $this->tassesScheduledForDeletion = $tassesToDelete;

        foreach ($tassesToDelete as $tasseRemoved) {
            $tasseRemoved->setStudentiCompleti(null);
        }

        $this->collTasses = null;
        foreach ($tasses as $tasse) {
            $this->addTasse($tasse);
        }

        $this->collTasses = $tasses;
        $this->collTassesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Tasse objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Tasse objects.
     * @throws PropelException
     */
    public function countTasses(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collTassesPartial && !$this->isNew();
        if (null === $this->collTasses || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collTasses) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getTasses());
            }
            $query = TasseQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByStudentiCompleti($this)
                ->count($con);
        }

        return count($this->collTasses);
    }

    /**
     * Method called to associate a Tasse object to this object
     * through the Tasse foreign key attribute.
     *
     * @param    Tasse $l Tasse
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function addTasse(Tasse $l)
    {
        if ($this->collTasses === null) {
            $this->initTasses();
            $this->collTassesPartial = true;
        }
        if (!in_array($l, $this->collTasses->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddTasse($l);
        }

        return $this;
    }

    /**
     * @param	Tasse $tasse The tasse object to add.
     */
    protected function doAddTasse($tasse)
    {
        $this->collTasses[]= $tasse;
        $tasse->setStudentiCompleti($this);
    }

    /**
     * @param	Tasse $tasse The tasse object to remove.
     * @return StudentiCompleti The current object (for fluent API support)
     */
    public function removeTasse($tasse)
    {
        if ($this->getTasses()->contains($tasse)) {
            $this->collTasses->remove($this->collTasses->search($tasse));
            if (null === $this->tassesScheduledForDeletion) {
                $this->tassesScheduledForDeletion = clone $this->collTasses;
                $this->tassesScheduledForDeletion->clear();
            }
            $this->tassesScheduledForDeletion[]= $tasse;
            $tasse->setStudentiCompleti(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this StudentiCompleti is new, it will return
     * an empty collection; or if this StudentiCompleti has previously
     * been saved, it will retrieve related Tasses from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in StudentiCompleti.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Tasse[] List of Tasse objects
     */
    public function getTassesJoinTipiTasse($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = TasseQuery::create(null, $criteria);
        $query->joinWith('TipiTasse', $join_behavior);

        return $this->getTasses($query, $con);
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this StudentiCompleti is new, it will return
     * an empty collection; or if this StudentiCompleti has previously
     * been saved, it will retrieve related Tasses from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in StudentiCompleti.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Tasse[] List of Tasse objects
     */
    public function getTassesJoinStudenti($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = TasseQuery::create(null, $criteria);
        $query->joinWith('Studenti', $join_behavior);

        return $this->getTasses($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id_studente = null;
        $this->nome = null;
        $this->cognome = null;
        $this->indirizzo = null;
        $this->citta = null;
        $this->cap = null;
        $this->provincia = null;
        $this->sesso = null;
        $this->telefono = null;
        $this->cellulare1 = null;
        $this->cellulare2 = null;
        $this->email1 = null;
        $this->email2 = null;
        $this->invio_email = null;
        $this->invio_email_cumulativo = null;
        $this->invio_email_parametrico = null;
        $this->invio_email_temporale = null;
        $this->tipo_sms = null;
        $this->tipo_sms_cumulativo = null;
        $this->tipo_sms_parametrico = null;
        $this->tipo_sms_temporale = null;
        $this->aut_entrata_ritardo = null;
        $this->aut_uscita_anticipo = null;
        $this->aut_pomeriggio = null;
        $this->acconsente = null;
        $this->ritirato = null;
        $this->data_nascita = null;
        $this->codice_studente = null;
        $this->password_studente = null;
        $this->codice_giustificazioni_studente = null;
        $this->esonero_religione = null;
        $this->materia_sostitutiva_religione = null;
        $this->esonero_ed_fisica = null;
        $this->materia_sostitutiva_edfisica = null;
        $this->crediti_terza = null;
        $this->media_voti_terza = null;
        $this->debiti_terza = null;
        $this->crediti_sospesi_terza = null;
        $this->crediti_reintegrati_terza = null;
        $this->crediti_quarta = null;
        $this->media_voti_quarta = null;
        $this->debiti_quarta = null;
        $this->crediti_sospesi_quarta = null;
        $this->crediti_reintegrati_quarta = null;
        $this->crediti_quinta = null;
        $this->media_voti_quinta = null;
        $this->crediti_finali_agg = null;
        $this->matricola = null;
        $this->luogo_nascita = null;
        $this->provincia_nascita = null;
        $this->motivi_crediti_terza = null;
        $this->motivi_crediti_quarta = null;
        $this->motivi_crediti_quinta = null;
        $this->motivi_crediti_agg = null;
        $this->codice_comune_nascita = null;
        $this->stato_nascita = null;
        $this->cittadinanza = null;
        $this->seconda_cittadinanza = null;
        $this->codice_comune_residenza = null;
        $this->distretto = null;
        $this->codice_fiscale = null;
        $this->medico = null;
        $this->telefono_medico = null;
        $this->intolleranze_alim = null;
        $this->gruppo_sanguigno = null;
        $this->gruppo_rh = null;
        $this->codice_asl = null;
        $this->annotazioni = null;
        $this->stato_civile = null;
        $this->voto_primo_scritto = null;
        $this->voto_secondo_scritto = null;
        $this->voto_terzo_scritto = null;
        $this->voto_orale = null;
        $this->voto_bonus = null;
        $this->materia_secondo_scr = null;
        $this->ulteriori_specif_diploma = null;
        $this->numero_diploma = null;
        $this->chi_inserisce = null;
        $this->data_inserimento = null;
        $this->tipo_inserimento = null;
        $this->chi_modifica = null;
        $this->data_modifica = null;
        $this->tipo_modifica = null;
        $this->flag_canc = null;
        $this->stato_avanzamento = null;
        $this->data_stato_avanzamento = null;
        $this->cap_provincia_nascita = null;
        $this->badge = null;
        $this->cap_residenza = null;
        $this->codice_comune_domicilio = null;
        $this->cap_domicilio = null;
        $this->cap_nascita = null;
        $this->indirizzo_domicilio = null;
        $this->citta_nascita_straniera = null;
        $this->cellulare_allievo = null;
        $this->handicap = null;
        $this->stato_convittore = null;
        $this->data_ritiro = null;
        $this->voto_ammissione = null;
        $this->differenza_punteggio = null;
        $this->voto_qualifica = null;
        $this->voto_esame_sc1_qual = null;
        $this->voto_esame_sc2_qual = null;
        $this->voto_esame_or_qual = null;
        $this->stato_privatista = null;
        $this->foto = null;
        $this->rappresentante = null;
        $this->obbligo_formativo = null;
        $this->id_lingua_1 = null;
        $this->id_lingua_2 = null;
        $this->id_lingua_3 = null;
        $this->id_lingua_4 = null;
        $this->id_lingua_5 = null;
        $this->id_provenienza_scolastica = null;
        $this->id_scuola_media = null;
        $this->lingua_scuola_media = null;
        $this->lingua_scuola_media_2 = null;
        $this->giudizio_scuola_media = null;
        $this->trasporto = null;
        $this->data_iscrizione = null;
        $this->pei = null;
        $this->ammesso_esame_qualifica = null;
        $this->ammesso_esame_quinta = null;
        $this->giudizio_ammissione_quinta = null;
        $this->grado_handicap = null;
        $this->tipo_handicap = null;
        $this->stato_licenza_maestro = null;
        $this->id_studente_sissi = null;
        $this->badge_rfid = null;
        $this->lode = null;
        $this->distretto_scolastico = null;
        $this->giudizio_ammissione_terza = null;
        $this->esito_prima_media = null;
        $this->esito_seconda_media = null;
        $this->esito_terza_media = null;
        $this->giudizio_esame_sc1_qual = null;
        $this->giudizio_esame_sc2_qual = null;
        $this->giudizio_esame_or_qual = null;
        $this->giudizio_complessivo_esame_qual = null;
        $this->acconsente_aziende = null;
        $this->curriculum_prima = null;
        $this->curriculum_seconda = null;
        $this->stage_professionali = null;
        $this->data_orale = null;
        $this->ordine_esame_orale = null;
        $this->tipo_primo_scritto = null;
        $this->tipo_secondo_scritto = null;
        $this->tipo_terzo_scritto = null;
        $this->unanimita_primo_scritto = null;
        $this->unanimita_secondo_scritto = null;
        $this->unanimita_terzo_scritto = null;
        $this->argomento_scelto_orale = null;
        $this->area_disc_1_orale = null;
        $this->area_disc_2_orale = null;
        $this->disc_elaborati_orale = null;
        $this->unanimita_voto_finale = null;
        $this->presente_esame_quinta = null;
        $this->stampa_badge = null;
        $this->id_classe_destinazione = null;
        $this->sconto_rette = null;
        $this->carta_studente_numero = null;
        $this->carta_studente_scadenza = null;
        $this->esito_corrente_calcolato = null;
        $this->id_flusso = null;
        $this->data_aggiornamento_sogei = null;
        $this->codice_alunno_ministeriale = null;
        $this->flag_cf_fittizio = null;
        $this->flag_s2f = null;
        $this->codice_stato_sogei = null;
        $this->codice_gruppo_nomade = null;
        $this->flag_minore_straniero = null;
        $this->chiave = null;
        $this->voto_esame_medie_italiano = null;
        $this->voto_esame_medie_inglese = null;
        $this->voto_esame_medie_matematica = null;
        $this->voto_esame_medie_seconda_lingua = null;
        $this->voto_esame_medie_invalsi_ita = null;
        $this->voto_esame_medie_invalsi_mat = null;
        $this->voto_esame_medie_orale = null;
        $this->voto_ammissione_medie = null;
        $this->esito_prima_elementare = null;
        $this->esito_seconda_elementare = null;
        $this->esito_terza_elementare = null;
        $this->esito_quarta_elementare = null;
        $this->esito_quinta_elementare = null;
        $this->tipo_voto_esame_medie_italiano = null;
        $this->tipo_voto_esame_medie_inglese = null;
        $this->giudizio_1_medie = null;
        $this->giudizio_2_medie = null;
        $this->giudizio_3_medie = null;
        $this->argomenti_orali_medie = null;
        $this->giudizio_finale_1_medie = null;
        $this->giudizio_finale_2_medie = null;
        $this->giudizio_finale_3_medie = null;
        $this->consiglio_terza_media = null;
        $this->giudizio_sintetico_esame_terza_media = null;
        $this->data_arrivo_in_italia = null;
        $this->frequenza_asilo_nido = null;
        $this->frequenza_scuola_materna = null;
        $this->data_aggiornamento_sidi = null;
        $this->cmp_sup_val_ita = null;
        $this->cmp_sup_txt_ita = null;
        $this->cmp_sup_val_ing = null;
        $this->cmp_sup_txt_ing = null;
        $this->cmp_sup_val_altri = null;
        $this->cmp_sup_txt_altri = null;
        $this->cmp_sup_val_mat = null;
        $this->cmp_sup_txt_mat = null;
        $this->cmp_sup_val_sci_tec = null;
        $this->cmp_sup_txt_sci_tec = null;
        $this->cmp_sup_val_sto_soc = null;
        $this->cmp_sup_txt_sto_soc = null;
        $this->cmp_med_val_ita = null;
        $this->cmp_med_txt_ita = null;
        $this->cmp_med_val_ing = null;
        $this->cmp_med_txt_ing = null;
        $this->cmp_med_val_altri = null;
        $this->cmp_med_txt_altri = null;
        $this->cmp_med_val_mat = null;
        $this->cmp_med_txt_mat = null;
        $this->cmp_med_val_sci_tec = null;
        $this->cmp_med_txt_sci_tec = null;
        $this->cmp_med_val_sto_soc = null;
        $this->cmp_med_txt_sto_soc = null;
        $this->cmp_med_val_l2 = null;
        $this->cmp_med_txt_l2 = null;
        $this->cmp_med_val_l3 = null;
        $this->cmp_med_txt_l3 = null;
        $this->cmp_med_val_arte = null;
        $this->cmp_med_txt_arte = null;
        $this->cmp_med_val_mus = null;
        $this->cmp_med_txt_mus = null;
        $this->cmp_med_val_mot = null;
        $this->cmp_med_txt_mot = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collTasses) {
                foreach ($this->collTasses as $o) {
                    $o->clearAllReferences($deep);
                }
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collTasses instanceof PropelCollection) {
            $this->collTasses->clearIterator();
        }
        $this->collTasses = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(StudentiCompletiPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
