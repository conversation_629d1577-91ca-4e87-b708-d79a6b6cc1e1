<?php

namespace Ccp\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Ccp\Studenti;
use Ccp\StudentiCompleti;
use Ccp\StudentiCompletiQuery;
use Ccp\StudentiQuery;
use Ccp\Tasse;
use Ccp\TassePeer;
use Ccp\TasseQuery;
use Ccp\TipiTasse;
use Ccp\TipiTasseQuery;

/**
 * Base class that represents a row from the 'tasse' table.
 *
 *
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseTasse extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Ccp\\TassePeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        TassePeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id_tasse field.
     * @var        int
     */
    protected $id_tasse;

    /**
     * The value for the id_tipo_tassa field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $id_tipo_tassa;

    /**
     * The value for the id_studente field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $id_studente;

    /**
     * The value for the sede_ufficio_postale field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $sede_ufficio_postale;

    /**
     * The value for the numero_versamento field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $numero_versamento;

    /**
     * The value for the data_versamento field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_versamento;

    /**
     * The value for the note field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $note;

    /**
     * The value for the anno_scolastico field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $anno_scolastico;

    /**
     * The value for the riferimento_estratto_conto field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $riferimento_estratto_conto;

    /**
     * The value for the data_estratto_conto field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_estratto_conto;

    /**
     * The value for the dati_debitore field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $dati_debitore;

    /**
     * The value for the numero_bollettino field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $numero_bollettino;

    /**
     * The value for the is_incoming field.
     * Note: this column has a database default value of: true
     * @var        boolean
     */
    protected $is_incoming;

    /**
     * The value for the employee_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $employee_id;

    /**
     * The value for the tipologia_uscita field.
     * Note: this column has a database default value of: 'DIVERSI'
     * @var        string
     */
    protected $tipologia_uscita;

    /**
     * The value for the destinazione_pagamento field.
     * Note: this column has a database default value of: 'ccp_0'
     * @var        string
     */
    protected $destinazione_pagamento;

    /**
     * The value for the modalita_pagamento field.
     * @var        string
     */
    protected $modalita_pagamento;

    /**
     * The value for the sede_studente field.
     * @var        int
     */
    protected $sede_studente;

    /**
     * The value for the classe field.
     * @var        string
     */
    protected $classe;

    /**
     * The value for the importo_versato field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $importo_versato;

    /**
     * @var        TipiTasse
     */
    protected $aTipiTasse;

    /**
     * @var        StudentiCompleti
     */
    protected $aStudentiCompleti;

    /**
     * @var        Studenti
     */
    protected $aStudenti;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->id_tipo_tassa = 0;
        $this->id_studente = 0;
        $this->sede_ufficio_postale = '';
        $this->numero_versamento = '';
        $this->data_versamento = '0';
        $this->note = '';
        $this->anno_scolastico = '';
        $this->riferimento_estratto_conto = '';
        $this->data_estratto_conto = '0';
        $this->dati_debitore = '';
        $this->numero_bollettino = '';
        $this->is_incoming = true;
        $this->employee_id = 0;
        $this->tipologia_uscita = 'DIVERSI';
        $this->destinazione_pagamento = 'ccp_0';
        $this->importo_versato = 0;
    }

    /**
     * Initializes internal state of BaseTasse object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id_tasse] column value.
     *
     * @return int
     */
    public function getIdTasse()
    {

        return $this->id_tasse;
    }

    /**
     * Get the [id_tipo_tassa] column value.
     *
     * @return int
     */
    public function getIdTipoTassa()
    {

        return $this->id_tipo_tassa;
    }

    /**
     * Get the [id_studente] column value.
     *
     * @return int
     */
    public function getIdStudente()
    {

        return $this->id_studente;
    }

    /**
     * Get the [sede_ufficio_postale] column value.
     *
     * @return string
     */
    public function getSedeUfficioPostale()
    {

        return $this->sede_ufficio_postale;
    }

    /**
     * Get the [numero_versamento] column value.
     *
     * @return string
     */
    public function getNumeroVersamento()
    {

        return $this->numero_versamento;
    }

    /**
     * Get the [data_versamento] column value.
     *
     * @return string
     */
    public function getDataVersamento()
    {

        return $this->data_versamento;
    }

    /**
     * Get the [note] column value.
     *
     * @return string
     */
    public function getNote()
    {

        return $this->note;
    }

    /**
     * Get the [anno_scolastico] column value.
     *
     * @return string
     */
    public function getAnnoScolastico()
    {

        return $this->anno_scolastico;
    }

    /**
     * Get the [riferimento_estratto_conto] column value.
     *
     * @return string
     */
    public function getRiferimentoEstrattoConto()
    {

        return $this->riferimento_estratto_conto;
    }

    /**
     * Get the [data_estratto_conto] column value.
     *
     * @return string
     */
    public function getDataEstrattoConto()
    {

        return $this->data_estratto_conto;
    }

    /**
     * Get the [dati_debitore] column value.
     *
     * @return string
     */
    public function getDatiDebitore()
    {

        return $this->dati_debitore;
    }

    /**
     * Get the [numero_bollettino] column value.
     *
     * @return string
     */
    public function getNumeroBollettino()
    {

        return $this->numero_bollettino;
    }

    /**
     * Get the [is_incoming] column value.
     *
     * @return boolean
     */
    public function getIsIncoming()
    {

        return $this->is_incoming;
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return int
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [tipologia_uscita] column value.
     *
     * @return string
     */
    public function getTipologiaUscita()
    {

        return $this->tipologia_uscita;
    }

    /**
     * Get the [destinazione_pagamento] column value.
     *
     * @return string
     */
    public function getDestinazionePagamento()
    {

        return $this->destinazione_pagamento;
    }

    /**
     * Get the [modalita_pagamento] column value.
     *
     * @return string
     */
    public function getModalitaPagamento()
    {

        return $this->modalita_pagamento;
    }

    /**
     * Get the [sede_studente] column value.
     *
     * @return int
     */
    public function getSedeStudente()
    {

        return $this->sede_studente;
    }

    /**
     * Get the [classe] column value.
     *
     * @return string
     */
    public function getClasse()
    {

        return $this->classe;
    }

    /**
     * Get the [importo_versato] column value.
     *
     * @return double
     */
    public function getImportoVersato()
    {

        return $this->importo_versato;
    }

    /**
     * Set the value of [id_tasse] column.
     *
     * @param  int $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setIdTasse($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_tasse !== $v) {
            $this->id_tasse = $v;
            $this->modifiedColumns[] = TassePeer::ID_TASSE;
        }


        return $this;
    } // setIdTasse()

    /**
     * Set the value of [id_tipo_tassa] column.
     *
     * @param  int $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setIdTipoTassa($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_tipo_tassa !== $v) {
            $this->id_tipo_tassa = $v;
            $this->modifiedColumns[] = TassePeer::ID_TIPO_TASSA;
        }

        if ($this->aTipiTasse !== null && $this->aTipiTasse->getIdTipoTassa() !== $v) {
            $this->aTipiTasse = null;
        }


        return $this;
    } // setIdTipoTassa()

    /**
     * Set the value of [id_studente] column.
     *
     * @param  int $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setIdStudente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_studente !== $v) {
            $this->id_studente = $v;
            $this->modifiedColumns[] = TassePeer::ID_STUDENTE;
        }

        if ($this->aStudentiCompleti !== null && $this->aStudentiCompleti->getIdStudente() !== $v) {
            $this->aStudentiCompleti = null;
        }

        if ($this->aStudenti !== null && $this->aStudenti->getIdStudente() !== $v) {
            $this->aStudenti = null;
        }


        return $this;
    } // setIdStudente()

    /**
     * Set the value of [sede_ufficio_postale] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setSedeUfficioPostale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->sede_ufficio_postale !== $v) {
            $this->sede_ufficio_postale = $v;
            $this->modifiedColumns[] = TassePeer::SEDE_UFFICIO_POSTALE;
        }


        return $this;
    } // setSedeUfficioPostale()

    /**
     * Set the value of [numero_versamento] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setNumeroVersamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->numero_versamento !== $v) {
            $this->numero_versamento = $v;
            $this->modifiedColumns[] = TassePeer::NUMERO_VERSAMENTO;
        }


        return $this;
    } // setNumeroVersamento()

    /**
     * Set the value of [data_versamento] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setDataVersamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_versamento !== $v) {
            $this->data_versamento = $v;
            $this->modifiedColumns[] = TassePeer::DATA_VERSAMENTO;
        }


        return $this;
    } // setDataVersamento()

    /**
     * Set the value of [note] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setNote($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->note !== $v) {
            $this->note = $v;
            $this->modifiedColumns[] = TassePeer::NOTE;
        }


        return $this;
    } // setNote()

    /**
     * Set the value of [anno_scolastico] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setAnnoScolastico($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->anno_scolastico !== $v) {
            $this->anno_scolastico = $v;
            $this->modifiedColumns[] = TassePeer::ANNO_SCOLASTICO;
        }


        return $this;
    } // setAnnoScolastico()

    /**
     * Set the value of [riferimento_estratto_conto] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setRiferimentoEstrattoConto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->riferimento_estratto_conto !== $v) {
            $this->riferimento_estratto_conto = $v;
            $this->modifiedColumns[] = TassePeer::RIFERIMENTO_ESTRATTO_CONTO;
        }


        return $this;
    } // setRiferimentoEstrattoConto()

    /**
     * Set the value of [data_estratto_conto] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setDataEstrattoConto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_estratto_conto !== $v) {
            $this->data_estratto_conto = $v;
            $this->modifiedColumns[] = TassePeer::DATA_ESTRATTO_CONTO;
        }


        return $this;
    } // setDataEstrattoConto()

    /**
     * Set the value of [dati_debitore] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setDatiDebitore($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->dati_debitore !== $v) {
            $this->dati_debitore = $v;
            $this->modifiedColumns[] = TassePeer::DATI_DEBITORE;
        }


        return $this;
    } // setDatiDebitore()

    /**
     * Set the value of [numero_bollettino] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setNumeroBollettino($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->numero_bollettino !== $v) {
            $this->numero_bollettino = $v;
            $this->modifiedColumns[] = TassePeer::NUMERO_BOLLETTINO;
        }


        return $this;
    } // setNumeroBollettino()

    /**
     * Sets the value of the [is_incoming] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setIsIncoming($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->is_incoming !== $v) {
            $this->is_incoming = $v;
            $this->modifiedColumns[] = TassePeer::IS_INCOMING;
        }


        return $this;
    } // setIsIncoming()

    /**
     * Set the value of [employee_id] column.
     *
     * @param  int $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = TassePeer::EMPLOYEE_ID;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [tipologia_uscita] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setTipologiaUscita($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipologia_uscita !== $v) {
            $this->tipologia_uscita = $v;
            $this->modifiedColumns[] = TassePeer::TIPOLOGIA_USCITA;
        }


        return $this;
    } // setTipologiaUscita()

    /**
     * Set the value of [destinazione_pagamento] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setDestinazionePagamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->destinazione_pagamento !== $v) {
            $this->destinazione_pagamento = $v;
            $this->modifiedColumns[] = TassePeer::DESTINAZIONE_PAGAMENTO;
        }


        return $this;
    } // setDestinazionePagamento()

    /**
     * Set the value of [modalita_pagamento] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setModalitaPagamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->modalita_pagamento !== $v) {
            $this->modalita_pagamento = $v;
            $this->modifiedColumns[] = TassePeer::MODALITA_PAGAMENTO;
        }


        return $this;
    } // setModalitaPagamento()

    /**
     * Set the value of [sede_studente] column.
     *
     * @param  int $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setSedeStudente($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->sede_studente !== $v) {
            $this->sede_studente = $v;
            $this->modifiedColumns[] = TassePeer::SEDE_STUDENTE;
        }


        return $this;
    } // setSedeStudente()

    /**
     * Set the value of [classe] column.
     *
     * @param  string $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setClasse($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->classe !== $v) {
            $this->classe = $v;
            $this->modifiedColumns[] = TassePeer::CLASSE;
        }


        return $this;
    } // setClasse()

    /**
     * Set the value of [importo_versato] column.
     *
     * @param  double $v new value
     * @return Tasse The current object (for fluent API support)
     */
    public function setImportoVersato($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->importo_versato !== $v) {
            $this->importo_versato = $v;
            $this->modifiedColumns[] = TassePeer::IMPORTO_VERSATO;
        }


        return $this;
    } // setImportoVersato()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->id_tipo_tassa !== 0) {
                return false;
            }

            if ($this->id_studente !== 0) {
                return false;
            }

            if ($this->sede_ufficio_postale !== '') {
                return false;
            }

            if ($this->numero_versamento !== '') {
                return false;
            }

            if ($this->data_versamento !== '0') {
                return false;
            }

            if ($this->note !== '') {
                return false;
            }

            if ($this->anno_scolastico !== '') {
                return false;
            }

            if ($this->riferimento_estratto_conto !== '') {
                return false;
            }

            if ($this->data_estratto_conto !== '0') {
                return false;
            }

            if ($this->dati_debitore !== '') {
                return false;
            }

            if ($this->numero_bollettino !== '') {
                return false;
            }

            if ($this->is_incoming !== true) {
                return false;
            }

            if ($this->employee_id !== 0) {
                return false;
            }

            if ($this->tipologia_uscita !== 'DIVERSI') {
                return false;
            }

            if ($this->destinazione_pagamento !== 'ccp_0') {
                return false;
            }

            if ($this->importo_versato !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id_tasse = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->id_tipo_tassa = ($row[$startcol + 1] !== null) ? (int) $row[$startcol + 1] : null;
            $this->id_studente = ($row[$startcol + 2] !== null) ? (int) $row[$startcol + 2] : null;
            $this->sede_ufficio_postale = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->numero_versamento = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->data_versamento = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->note = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->anno_scolastico = ($row[$startcol + 7] !== null) ? (string) $row[$startcol + 7] : null;
            $this->riferimento_estratto_conto = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->data_estratto_conto = ($row[$startcol + 9] !== null) ? (string) $row[$startcol + 9] : null;
            $this->dati_debitore = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->numero_bollettino = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->is_incoming = ($row[$startcol + 12] !== null) ? (boolean) $row[$startcol + 12] : null;
            $this->employee_id = ($row[$startcol + 13] !== null) ? (int) $row[$startcol + 13] : null;
            $this->tipologia_uscita = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->destinazione_pagamento = ($row[$startcol + 15] !== null) ? (string) $row[$startcol + 15] : null;
            $this->modalita_pagamento = ($row[$startcol + 16] !== null) ? (string) $row[$startcol + 16] : null;
            $this->sede_studente = ($row[$startcol + 17] !== null) ? (int) $row[$startcol + 17] : null;
            $this->classe = ($row[$startcol + 18] !== null) ? (string) $row[$startcol + 18] : null;
            $this->importo_versato = ($row[$startcol + 19] !== null) ? (double) $row[$startcol + 19] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 20; // 20 = TassePeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Tasse object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aTipiTasse !== null && $this->id_tipo_tassa !== $this->aTipiTasse->getIdTipoTassa()) {
            $this->aTipiTasse = null;
        }
        if ($this->aStudentiCompleti !== null && $this->id_studente !== $this->aStudentiCompleti->getIdStudente()) {
            $this->aStudentiCompleti = null;
        }
        if ($this->aStudenti !== null && $this->id_studente !== $this->aStudenti->getIdStudente()) {
            $this->aStudenti = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = TassePeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aTipiTasse = null;
            $this->aStudentiCompleti = null;
            $this->aStudenti = null;
        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = TasseQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                TassePeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aTipiTasse !== null) {
                if ($this->aTipiTasse->isModified() || $this->aTipiTasse->isNew()) {
                    $affectedRows += $this->aTipiTasse->save($con);
                }
                $this->setTipiTasse($this->aTipiTasse);
            }

            if ($this->aStudentiCompleti !== null) {
                if ($this->aStudentiCompleti->isModified() || $this->aStudentiCompleti->isNew()) {
                    $affectedRows += $this->aStudentiCompleti->save($con);
                }
                $this->setStudentiCompleti($this->aStudentiCompleti);
            }

            if ($this->aStudenti !== null) {
                if ($this->aStudenti->isModified() || $this->aStudenti->isNew()) {
                    $affectedRows += $this->aStudenti->save($con);
                }
                $this->setStudenti($this->aStudenti);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = TassePeer::ID_TASSE;
        if (null !== $this->id_tasse) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . TassePeer::ID_TASSE . ')');
        }
        if (null === $this->id_tasse) {
            try {
                $stmt = $con->query("SELECT nextval('tasse_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id_tasse = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(TassePeer::ID_TASSE)) {
            $modifiedColumns[':p' . $index++]  = '"id_tasse"';
        }
        if ($this->isColumnModified(TassePeer::ID_TIPO_TASSA)) {
            $modifiedColumns[':p' . $index++]  = '"id_tipo_tassa"';
        }
        if ($this->isColumnModified(TassePeer::ID_STUDENTE)) {
            $modifiedColumns[':p' . $index++]  = '"id_studente"';
        }
        if ($this->isColumnModified(TassePeer::SEDE_UFFICIO_POSTALE)) {
            $modifiedColumns[':p' . $index++]  = '"sede_ufficio_postale"';
        }
        if ($this->isColumnModified(TassePeer::NUMERO_VERSAMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"numero_versamento"';
        }
        if ($this->isColumnModified(TassePeer::DATA_VERSAMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"data_versamento"';
        }
        if ($this->isColumnModified(TassePeer::NOTE)) {
            $modifiedColumns[':p' . $index++]  = '"note"';
        }
        if ($this->isColumnModified(TassePeer::ANNO_SCOLASTICO)) {
            $modifiedColumns[':p' . $index++]  = '"anno_scolastico"';
        }
        if ($this->isColumnModified(TassePeer::RIFERIMENTO_ESTRATTO_CONTO)) {
            $modifiedColumns[':p' . $index++]  = '"riferimento_estratto_conto"';
        }
        if ($this->isColumnModified(TassePeer::DATA_ESTRATTO_CONTO)) {
            $modifiedColumns[':p' . $index++]  = '"data_estratto_conto"';
        }
        if ($this->isColumnModified(TassePeer::DATI_DEBITORE)) {
            $modifiedColumns[':p' . $index++]  = '"dati_debitore"';
        }
        if ($this->isColumnModified(TassePeer::NUMERO_BOLLETTINO)) {
            $modifiedColumns[':p' . $index++]  = '"numero_bollettino"';
        }
        if ($this->isColumnModified(TassePeer::IS_INCOMING)) {
            $modifiedColumns[':p' . $index++]  = '"is_incoming"';
        }
        if ($this->isColumnModified(TassePeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(TassePeer::TIPOLOGIA_USCITA)) {
            $modifiedColumns[':p' . $index++]  = '"tipologia_uscita"';
        }
        if ($this->isColumnModified(TassePeer::DESTINAZIONE_PAGAMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"destinazione_pagamento"';
        }
        if ($this->isColumnModified(TassePeer::MODALITA_PAGAMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"modalita_pagamento"';
        }
        if ($this->isColumnModified(TassePeer::SEDE_STUDENTE)) {
            $modifiedColumns[':p' . $index++]  = '"sede_studente"';
        }
        if ($this->isColumnModified(TassePeer::CLASSE)) {
            $modifiedColumns[':p' . $index++]  = '"classe"';
        }
        if ($this->isColumnModified(TassePeer::IMPORTO_VERSATO)) {
            $modifiedColumns[':p' . $index++]  = '"importo_versato"';
        }

        $sql = sprintf(
            'INSERT INTO "tasse" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id_tasse"':
                        $stmt->bindValue($identifier, $this->id_tasse, PDO::PARAM_INT);
                        break;
                    case '"id_tipo_tassa"':
                        $stmt->bindValue($identifier, $this->id_tipo_tassa, PDO::PARAM_INT);
                        break;
                    case '"id_studente"':
                        $stmt->bindValue($identifier, $this->id_studente, PDO::PARAM_INT);
                        break;
                    case '"sede_ufficio_postale"':
                        $stmt->bindValue($identifier, $this->sede_ufficio_postale, PDO::PARAM_STR);
                        break;
                    case '"numero_versamento"':
                        $stmt->bindValue($identifier, $this->numero_versamento, PDO::PARAM_STR);
                        break;
                    case '"data_versamento"':
                        $stmt->bindValue($identifier, $this->data_versamento, PDO::PARAM_STR);
                        break;
                    case '"note"':
                        $stmt->bindValue($identifier, $this->note, PDO::PARAM_STR);
                        break;
                    case '"anno_scolastico"':
                        $stmt->bindValue($identifier, $this->anno_scolastico, PDO::PARAM_STR);
                        break;
                    case '"riferimento_estratto_conto"':
                        $stmt->bindValue($identifier, $this->riferimento_estratto_conto, PDO::PARAM_STR);
                        break;
                    case '"data_estratto_conto"':
                        $stmt->bindValue($identifier, $this->data_estratto_conto, PDO::PARAM_STR);
                        break;
                    case '"dati_debitore"':
                        $stmt->bindValue($identifier, $this->dati_debitore, PDO::PARAM_STR);
                        break;
                    case '"numero_bollettino"':
                        $stmt->bindValue($identifier, $this->numero_bollettino, PDO::PARAM_STR);
                        break;
                    case '"is_incoming"':
                        $stmt->bindValue($identifier, $this->is_incoming, PDO::PARAM_BOOL);
                        break;
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_INT);
                        break;
                    case '"tipologia_uscita"':
                        $stmt->bindValue($identifier, $this->tipologia_uscita, PDO::PARAM_STR);
                        break;
                    case '"destinazione_pagamento"':
                        $stmt->bindValue($identifier, $this->destinazione_pagamento, PDO::PARAM_STR);
                        break;
                    case '"modalita_pagamento"':
                        $stmt->bindValue($identifier, $this->modalita_pagamento, PDO::PARAM_STR);
                        break;
                    case '"sede_studente"':
                        $stmt->bindValue($identifier, $this->sede_studente, PDO::PARAM_INT);
                        break;
                    case '"classe"':
                        $stmt->bindValue($identifier, $this->classe, PDO::PARAM_STR);
                        break;
                    case '"importo_versato"':
                        $stmt->bindValue($identifier, $this->importo_versato, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aTipiTasse !== null) {
                if (!$this->aTipiTasse->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aTipiTasse->getValidationFailures());
                }
            }

            if ($this->aStudentiCompleti !== null) {
                if (!$this->aStudentiCompleti->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aStudentiCompleti->getValidationFailures());
                }
            }

            if ($this->aStudenti !== null) {
                if (!$this->aStudenti->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aStudenti->getValidationFailures());
                }
            }


            if (($retval = TassePeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = TassePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getIdTasse();
                break;
            case 1:
                return $this->getIdTipoTassa();
                break;
            case 2:
                return $this->getIdStudente();
                break;
            case 3:
                return $this->getSedeUfficioPostale();
                break;
            case 4:
                return $this->getNumeroVersamento();
                break;
            case 5:
                return $this->getDataVersamento();
                break;
            case 6:
                return $this->getNote();
                break;
            case 7:
                return $this->getAnnoScolastico();
                break;
            case 8:
                return $this->getRiferimentoEstrattoConto();
                break;
            case 9:
                return $this->getDataEstrattoConto();
                break;
            case 10:
                return $this->getDatiDebitore();
                break;
            case 11:
                return $this->getNumeroBollettino();
                break;
            case 12:
                return $this->getIsIncoming();
                break;
            case 13:
                return $this->getEmployeeId();
                break;
            case 14:
                return $this->getTipologiaUscita();
                break;
            case 15:
                return $this->getDestinazionePagamento();
                break;
            case 16:
                return $this->getModalitaPagamento();
                break;
            case 17:
                return $this->getSedeStudente();
                break;
            case 18:
                return $this->getClasse();
                break;
            case 19:
                return $this->getImportoVersato();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Tasse'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Tasse'][$this->getPrimaryKey()] = true;
        $keys = TassePeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getIdTasse(),
            $keys[1] => $this->getIdTipoTassa(),
            $keys[2] => $this->getIdStudente(),
            $keys[3] => $this->getSedeUfficioPostale(),
            $keys[4] => $this->getNumeroVersamento(),
            $keys[5] => $this->getDataVersamento(),
            $keys[6] => $this->getNote(),
            $keys[7] => $this->getAnnoScolastico(),
            $keys[8] => $this->getRiferimentoEstrattoConto(),
            $keys[9] => $this->getDataEstrattoConto(),
            $keys[10] => $this->getDatiDebitore(),
            $keys[11] => $this->getNumeroBollettino(),
            $keys[12] => $this->getIsIncoming(),
            $keys[13] => $this->getEmployeeId(),
            $keys[14] => $this->getTipologiaUscita(),
            $keys[15] => $this->getDestinazionePagamento(),
            $keys[16] => $this->getModalitaPagamento(),
            $keys[17] => $this->getSedeStudente(),
            $keys[18] => $this->getClasse(),
            $keys[19] => $this->getImportoVersato(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aTipiTasse) {
                $result['TipiTasse'] = $this->aTipiTasse->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aStudentiCompleti) {
                $result['StudentiCompleti'] = $this->aStudentiCompleti->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aStudenti) {
                $result['Studenti'] = $this->aStudenti->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = TassePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setIdTasse($value);
                break;
            case 1:
                $this->setIdTipoTassa($value);
                break;
            case 2:
                $this->setIdStudente($value);
                break;
            case 3:
                $this->setSedeUfficioPostale($value);
                break;
            case 4:
                $this->setNumeroVersamento($value);
                break;
            case 5:
                $this->setDataVersamento($value);
                break;
            case 6:
                $this->setNote($value);
                break;
            case 7:
                $this->setAnnoScolastico($value);
                break;
            case 8:
                $this->setRiferimentoEstrattoConto($value);
                break;
            case 9:
                $this->setDataEstrattoConto($value);
                break;
            case 10:
                $this->setDatiDebitore($value);
                break;
            case 11:
                $this->setNumeroBollettino($value);
                break;
            case 12:
                $this->setIsIncoming($value);
                break;
            case 13:
                $this->setEmployeeId($value);
                break;
            case 14:
                $this->setTipologiaUscita($value);
                break;
            case 15:
                $this->setDestinazionePagamento($value);
                break;
            case 16:
                $this->setModalitaPagamento($value);
                break;
            case 17:
                $this->setSedeStudente($value);
                break;
            case 18:
                $this->setClasse($value);
                break;
            case 19:
                $this->setImportoVersato($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = TassePeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setIdTasse($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setIdTipoTassa($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setIdStudente($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setSedeUfficioPostale($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setNumeroVersamento($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setDataVersamento($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setNote($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setAnnoScolastico($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setRiferimentoEstrattoConto($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setDataEstrattoConto($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setDatiDebitore($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setNumeroBollettino($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setIsIncoming($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setEmployeeId($arr[$keys[13]]);
        if (array_key_exists($keys[14], $arr)) $this->setTipologiaUscita($arr[$keys[14]]);
        if (array_key_exists($keys[15], $arr)) $this->setDestinazionePagamento($arr[$keys[15]]);
        if (array_key_exists($keys[16], $arr)) $this->setModalitaPagamento($arr[$keys[16]]);
        if (array_key_exists($keys[17], $arr)) $this->setSedeStudente($arr[$keys[17]]);
        if (array_key_exists($keys[18], $arr)) $this->setClasse($arr[$keys[18]]);
        if (array_key_exists($keys[19], $arr)) $this->setImportoVersato($arr[$keys[19]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(TassePeer::DATABASE_NAME);

        if ($this->isColumnModified(TassePeer::ID_TASSE)) $criteria->add(TassePeer::ID_TASSE, $this->id_tasse);
        if ($this->isColumnModified(TassePeer::ID_TIPO_TASSA)) $criteria->add(TassePeer::ID_TIPO_TASSA, $this->id_tipo_tassa);
        if ($this->isColumnModified(TassePeer::ID_STUDENTE)) $criteria->add(TassePeer::ID_STUDENTE, $this->id_studente);
        if ($this->isColumnModified(TassePeer::SEDE_UFFICIO_POSTALE)) $criteria->add(TassePeer::SEDE_UFFICIO_POSTALE, $this->sede_ufficio_postale);
        if ($this->isColumnModified(TassePeer::NUMERO_VERSAMENTO)) $criteria->add(TassePeer::NUMERO_VERSAMENTO, $this->numero_versamento);
        if ($this->isColumnModified(TassePeer::DATA_VERSAMENTO)) $criteria->add(TassePeer::DATA_VERSAMENTO, $this->data_versamento);
        if ($this->isColumnModified(TassePeer::NOTE)) $criteria->add(TassePeer::NOTE, $this->note);
        if ($this->isColumnModified(TassePeer::ANNO_SCOLASTICO)) $criteria->add(TassePeer::ANNO_SCOLASTICO, $this->anno_scolastico);
        if ($this->isColumnModified(TassePeer::RIFERIMENTO_ESTRATTO_CONTO)) $criteria->add(TassePeer::RIFERIMENTO_ESTRATTO_CONTO, $this->riferimento_estratto_conto);
        if ($this->isColumnModified(TassePeer::DATA_ESTRATTO_CONTO)) $criteria->add(TassePeer::DATA_ESTRATTO_CONTO, $this->data_estratto_conto);
        if ($this->isColumnModified(TassePeer::DATI_DEBITORE)) $criteria->add(TassePeer::DATI_DEBITORE, $this->dati_debitore);
        if ($this->isColumnModified(TassePeer::NUMERO_BOLLETTINO)) $criteria->add(TassePeer::NUMERO_BOLLETTINO, $this->numero_bollettino);
        if ($this->isColumnModified(TassePeer::IS_INCOMING)) $criteria->add(TassePeer::IS_INCOMING, $this->is_incoming);
        if ($this->isColumnModified(TassePeer::EMPLOYEE_ID)) $criteria->add(TassePeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(TassePeer::TIPOLOGIA_USCITA)) $criteria->add(TassePeer::TIPOLOGIA_USCITA, $this->tipologia_uscita);
        if ($this->isColumnModified(TassePeer::DESTINAZIONE_PAGAMENTO)) $criteria->add(TassePeer::DESTINAZIONE_PAGAMENTO, $this->destinazione_pagamento);
        if ($this->isColumnModified(TassePeer::MODALITA_PAGAMENTO)) $criteria->add(TassePeer::MODALITA_PAGAMENTO, $this->modalita_pagamento);
        if ($this->isColumnModified(TassePeer::SEDE_STUDENTE)) $criteria->add(TassePeer::SEDE_STUDENTE, $this->sede_studente);
        if ($this->isColumnModified(TassePeer::CLASSE)) $criteria->add(TassePeer::CLASSE, $this->classe);
        if ($this->isColumnModified(TassePeer::IMPORTO_VERSATO)) $criteria->add(TassePeer::IMPORTO_VERSATO, $this->importo_versato);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(TassePeer::DATABASE_NAME);
        $criteria->add(TassePeer::ID_TASSE, $this->id_tasse);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getIdTasse();
    }

    /**
     * Generic method to set the primary key (id_tasse column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setIdTasse($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getIdTasse();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Tasse (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setIdTipoTassa($this->getIdTipoTassa());
        $copyObj->setIdStudente($this->getIdStudente());
        $copyObj->setSedeUfficioPostale($this->getSedeUfficioPostale());
        $copyObj->setNumeroVersamento($this->getNumeroVersamento());
        $copyObj->setDataVersamento($this->getDataVersamento());
        $copyObj->setNote($this->getNote());
        $copyObj->setAnnoScolastico($this->getAnnoScolastico());
        $copyObj->setRiferimentoEstrattoConto($this->getRiferimentoEstrattoConto());
        $copyObj->setDataEstrattoConto($this->getDataEstrattoConto());
        $copyObj->setDatiDebitore($this->getDatiDebitore());
        $copyObj->setNumeroBollettino($this->getNumeroBollettino());
        $copyObj->setIsIncoming($this->getIsIncoming());
        $copyObj->setEmployeeId($this->getEmployeeId());
        $copyObj->setTipologiaUscita($this->getTipologiaUscita());
        $copyObj->setDestinazionePagamento($this->getDestinazionePagamento());
        $copyObj->setModalitaPagamento($this->getModalitaPagamento());
        $copyObj->setSedeStudente($this->getSedeStudente());
        $copyObj->setClasse($this->getClasse());
        $copyObj->setImportoVersato($this->getImportoVersato());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setIdTasse(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Tasse Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return TassePeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new TassePeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a TipiTasse object.
     *
     * @param                  TipiTasse $v
     * @return Tasse The current object (for fluent API support)
     * @throws PropelException
     */
    public function setTipiTasse(TipiTasse $v = null)
    {
        if ($v === null) {
            $this->setIdTipoTassa(0);
        } else {
            $this->setIdTipoTassa($v->getIdTipoTassa());
        }

        $this->aTipiTasse = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the TipiTasse object, it will not be re-added.
        if ($v !== null) {
            $v->addTasse($this);
        }


        return $this;
    }


    /**
     * Get the associated TipiTasse object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return TipiTasse The associated TipiTasse object.
     * @throws PropelException
     */
    public function getTipiTasse(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aTipiTasse === null && ($this->id_tipo_tassa !== null) && $doQuery) {
            $this->aTipiTasse = TipiTasseQuery::create()->findPk($this->id_tipo_tassa, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aTipiTasse->addTasses($this);
             */
        }

        return $this->aTipiTasse;
    }

    /**
     * Declares an association between this object and a StudentiCompleti object.
     *
     * @param                  StudentiCompleti $v
     * @return Tasse The current object (for fluent API support)
     * @throws PropelException
     */
    public function setStudentiCompleti(StudentiCompleti $v = null)
    {
        if ($v === null) {
            $this->setIdStudente(0);
        } else {
            $this->setIdStudente($v->getIdStudente());
        }

        $this->aStudentiCompleti = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the StudentiCompleti object, it will not be re-added.
        if ($v !== null) {
            $v->addTasse($this);
        }


        return $this;
    }


    /**
     * Get the associated StudentiCompleti object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return StudentiCompleti The associated StudentiCompleti object.
     * @throws PropelException
     */
    public function getStudentiCompleti(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aStudentiCompleti === null && ($this->id_studente !== null) && $doQuery) {
            $this->aStudentiCompleti = StudentiCompletiQuery::create()->findPk($this->id_studente, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aStudentiCompleti->addTasses($this);
             */
        }

        return $this->aStudentiCompleti;
    }

    /**
     * Declares an association between this object and a Studenti object.
     *
     * @param                  Studenti $v
     * @return Tasse The current object (for fluent API support)
     * @throws PropelException
     */
    public function setStudenti(Studenti $v = null)
    {
        if ($v === null) {
            $this->setIdStudente(0);
        } else {
            $this->setIdStudente($v->getIdStudente());
        }

        $this->aStudenti = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Studenti object, it will not be re-added.
        if ($v !== null) {
            $v->addTasse($this);
        }


        return $this;
    }


    /**
     * Get the associated Studenti object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Studenti The associated Studenti object.
     * @throws PropelException
     */
    public function getStudenti(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aStudenti === null && ($this->id_studente !== null) && $doQuery) {
            $this->aStudenti = StudentiQuery::create()->findPk($this->id_studente, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aStudenti->addTasses($this);
             */
        }

        return $this->aStudenti;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id_tasse = null;
        $this->id_tipo_tassa = null;
        $this->id_studente = null;
        $this->sede_ufficio_postale = null;
        $this->numero_versamento = null;
        $this->data_versamento = null;
        $this->note = null;
        $this->anno_scolastico = null;
        $this->riferimento_estratto_conto = null;
        $this->data_estratto_conto = null;
        $this->dati_debitore = null;
        $this->numero_bollettino = null;
        $this->is_incoming = null;
        $this->employee_id = null;
        $this->tipologia_uscita = null;
        $this->destinazione_pagamento = null;
        $this->modalita_pagamento = null;
        $this->sede_studente = null;
        $this->classe = null;
        $this->importo_versato = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->aTipiTasse instanceof Persistent) {
              $this->aTipiTasse->clearAllReferences($deep);
            }
            if ($this->aStudentiCompleti instanceof Persistent) {
              $this->aStudentiCompleti->clearAllReferences($deep);
            }
            if ($this->aStudenti instanceof Persistent) {
              $this->aStudenti->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        $this->aTipiTasse = null;
        $this->aStudentiCompleti = null;
        $this->aStudenti = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(TassePeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
