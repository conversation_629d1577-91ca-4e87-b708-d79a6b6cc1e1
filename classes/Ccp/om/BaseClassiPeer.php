<?php

namespace Ccp\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Ccp\Classi;
use Ccp\ClassiPeer;
use Ccp\map\ClassiTableMap;

/**
 * Base static class for performing query and update operations on the 'classi' table.
 *
 *
 *
 * @package propel.generator.Ccp.om
 */
abstract class BaseClassiPeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'classi';

    /** the related Propel class for this table */
    const OM_CLASS = 'Ccp\\Classi';

    /** the related TableMap class for this table */
    const TM_CLASS = 'ClassiTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 27;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 27;

    /** the column name for the id_classe field */
    const ID_CLASSE = 'classi.id_classe';

    /** the column name for the classe field */
    const CLASSE = 'classi.classe';

    /** the column name for the sezione field */
    const SEZIONE = 'classi.sezione';

    /** the column name for the id_indirizzo field */
    const ID_INDIRIZZO = 'classi.id_indirizzo';

    /** the column name for the codice_registro field */
    const CODICE_REGISTRO = 'classi.codice_registro';

    /** the column name for the ordinamento field */
    const ORDINAMENTO = 'classi.ordinamento';

    /** the column name for the chi_inserisce field */
    const CHI_INSERISCE = 'classi.chi_inserisce';

    /** the column name for the data_inserimento field */
    const DATA_INSERIMENTO = 'classi.data_inserimento';

    /** the column name for the tipo_inserimento field */
    const TIPO_INSERIMENTO = 'classi.tipo_inserimento';

    /** the column name for the chi_modifica field */
    const CHI_MODIFICA = 'classi.chi_modifica';

    /** the column name for the data_modifica field */
    const DATA_MODIFICA = 'classi.data_modifica';

    /** the column name for the tipo_modifica field */
    const TIPO_MODIFICA = 'classi.tipo_modifica';

    /** the column name for the flag_canc field */
    const FLAG_CANC = 'classi.flag_canc';

    /** the column name for the codice_registro_2 field */
    const CODICE_REGISTRO_2 = 'classi.codice_registro_2';

    /** the column name for the codice_registro_3 field */
    const CODICE_REGISTRO_3 = 'classi.codice_registro_3';

    /** the column name for the codice_registro_4 field */
    const CODICE_REGISTRO_4 = 'classi.codice_registro_4';

    /** the column name for the blocco_scrutini field */
    const BLOCCO_SCRUTINI = 'classi.blocco_scrutini';

    /** the column name for the consiglio_classe_attivo field */
    const CONSIGLIO_CLASSE_ATTIVO = 'classi.consiglio_classe_attivo';

    /** the column name for the tempo_funzionamento field */
    const TEMPO_FUNZIONAMENTO = 'classi.tempo_funzionamento';

    /** the column name for the pubb_primo_scritto field */
    const PUBB_PRIMO_SCRITTO = 'classi.pubb_primo_scritto';

    /** the column name for the pubb_secondo_scritto field */
    const PUBB_SECONDO_SCRITTO = 'classi.pubb_secondo_scritto';

    /** the column name for the pubb_terzo_scritto field */
    const PUBB_TERZO_SCRITTO = 'classi.pubb_terzo_scritto';

    /** the column name for the pubb_orale field */
    const PUBB_ORALE = 'classi.pubb_orale';

    /** the column name for the id_flusso field */
    const ID_FLUSSO = 'classi.id_flusso';

    /** the column name for the autenticazione_alternativa field */
    const AUTENTICAZIONE_ALTERNATIVA = 'classi.autenticazione_alternativa';

    /** the column name for the effettua_controllo_gate field */
    const EFFETTUA_CONTROLLO_GATE = 'classi.effettua_controllo_gate';

    /** the column name for the data_aggiornamento_sidi field */
    const DATA_AGGIORNAMENTO_SIDI = 'classi.data_aggiornamento_sidi';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of Classi objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array Classi[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. ClassiPeer::$fieldNames[ClassiPeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('IdClasse', 'Classe', 'Sezione', 'IdIndirizzo', 'CodiceRegistro', 'Ordinamento', 'ChiInserisce', 'DataInserimento', 'TipoInserimento', 'ChiModifica', 'DataModifica', 'TipoModifica', 'FlagCanc', 'CodiceRegistro2', 'CodiceRegistro3', 'CodiceRegistro4', 'BloccoScrutini', 'ConsiglioClasseAttivo', 'TempoFunzionamento', 'PubbPrimoScritto', 'PubbSecondoScritto', 'PubbTerzoScritto', 'PubbOrale', 'IdFlusso', 'AutenticazioneAlternativa', 'EffettuaControlloGate', 'DataAggiornamentoSidi', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('idClasse', 'classe', 'sezione', 'idIndirizzo', 'codiceRegistro', 'ordinamento', 'chiInserisce', 'dataInserimento', 'tipoInserimento', 'chiModifica', 'dataModifica', 'tipoModifica', 'flagCanc', 'codiceRegistro2', 'codiceRegistro3', 'codiceRegistro4', 'bloccoScrutini', 'consiglioClasseAttivo', 'tempoFunzionamento', 'pubbPrimoScritto', 'pubbSecondoScritto', 'pubbTerzoScritto', 'pubbOrale', 'idFlusso', 'autenticazioneAlternativa', 'effettuaControlloGate', 'dataAggiornamentoSidi', ),
        BasePeer::TYPE_COLNAME => array (ClassiPeer::ID_CLASSE, ClassiPeer::CLASSE, ClassiPeer::SEZIONE, ClassiPeer::ID_INDIRIZZO, ClassiPeer::CODICE_REGISTRO, ClassiPeer::ORDINAMENTO, ClassiPeer::CHI_INSERISCE, ClassiPeer::DATA_INSERIMENTO, ClassiPeer::TIPO_INSERIMENTO, ClassiPeer::CHI_MODIFICA, ClassiPeer::DATA_MODIFICA, ClassiPeer::TIPO_MODIFICA, ClassiPeer::FLAG_CANC, ClassiPeer::CODICE_REGISTRO_2, ClassiPeer::CODICE_REGISTRO_3, ClassiPeer::CODICE_REGISTRO_4, ClassiPeer::BLOCCO_SCRUTINI, ClassiPeer::CONSIGLIO_CLASSE_ATTIVO, ClassiPeer::TEMPO_FUNZIONAMENTO, ClassiPeer::PUBB_PRIMO_SCRITTO, ClassiPeer::PUBB_SECONDO_SCRITTO, ClassiPeer::PUBB_TERZO_SCRITTO, ClassiPeer::PUBB_ORALE, ClassiPeer::ID_FLUSSO, ClassiPeer::AUTENTICAZIONE_ALTERNATIVA, ClassiPeer::EFFETTUA_CONTROLLO_GATE, ClassiPeer::DATA_AGGIORNAMENTO_SIDI, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID_CLASSE', 'CLASSE', 'SEZIONE', 'ID_INDIRIZZO', 'CODICE_REGISTRO', 'ORDINAMENTO', 'CHI_INSERISCE', 'DATA_INSERIMENTO', 'TIPO_INSERIMENTO', 'CHI_MODIFICA', 'DATA_MODIFICA', 'TIPO_MODIFICA', 'FLAG_CANC', 'CODICE_REGISTRO_2', 'CODICE_REGISTRO_3', 'CODICE_REGISTRO_4', 'BLOCCO_SCRUTINI', 'CONSIGLIO_CLASSE_ATTIVO', 'TEMPO_FUNZIONAMENTO', 'PUBB_PRIMO_SCRITTO', 'PUBB_SECONDO_SCRITTO', 'PUBB_TERZO_SCRITTO', 'PUBB_ORALE', 'ID_FLUSSO', 'AUTENTICAZIONE_ALTERNATIVA', 'EFFETTUA_CONTROLLO_GATE', 'DATA_AGGIORNAMENTO_SIDI', ),
        BasePeer::TYPE_FIELDNAME => array ('id_classe', 'classe', 'sezione', 'id_indirizzo', 'codice_registro', 'ordinamento', 'chi_inserisce', 'data_inserimento', 'tipo_inserimento', 'chi_modifica', 'data_modifica', 'tipo_modifica', 'flag_canc', 'codice_registro_2', 'codice_registro_3', 'codice_registro_4', 'blocco_scrutini', 'consiglio_classe_attivo', 'tempo_funzionamento', 'pubb_primo_scritto', 'pubb_secondo_scritto', 'pubb_terzo_scritto', 'pubb_orale', 'id_flusso', 'autenticazione_alternativa', 'effettua_controllo_gate', 'data_aggiornamento_sidi', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. ClassiPeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('IdClasse' => 0, 'Classe' => 1, 'Sezione' => 2, 'IdIndirizzo' => 3, 'CodiceRegistro' => 4, 'Ordinamento' => 5, 'ChiInserisce' => 6, 'DataInserimento' => 7, 'TipoInserimento' => 8, 'ChiModifica' => 9, 'DataModifica' => 10, 'TipoModifica' => 11, 'FlagCanc' => 12, 'CodiceRegistro2' => 13, 'CodiceRegistro3' => 14, 'CodiceRegistro4' => 15, 'BloccoScrutini' => 16, 'ConsiglioClasseAttivo' => 17, 'TempoFunzionamento' => 18, 'PubbPrimoScritto' => 19, 'PubbSecondoScritto' => 20, 'PubbTerzoScritto' => 21, 'PubbOrale' => 22, 'IdFlusso' => 23, 'AutenticazioneAlternativa' => 24, 'EffettuaControlloGate' => 25, 'DataAggiornamentoSidi' => 26, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('idClasse' => 0, 'classe' => 1, 'sezione' => 2, 'idIndirizzo' => 3, 'codiceRegistro' => 4, 'ordinamento' => 5, 'chiInserisce' => 6, 'dataInserimento' => 7, 'tipoInserimento' => 8, 'chiModifica' => 9, 'dataModifica' => 10, 'tipoModifica' => 11, 'flagCanc' => 12, 'codiceRegistro2' => 13, 'codiceRegistro3' => 14, 'codiceRegistro4' => 15, 'bloccoScrutini' => 16, 'consiglioClasseAttivo' => 17, 'tempoFunzionamento' => 18, 'pubbPrimoScritto' => 19, 'pubbSecondoScritto' => 20, 'pubbTerzoScritto' => 21, 'pubbOrale' => 22, 'idFlusso' => 23, 'autenticazioneAlternativa' => 24, 'effettuaControlloGate' => 25, 'dataAggiornamentoSidi' => 26, ),
        BasePeer::TYPE_COLNAME => array (ClassiPeer::ID_CLASSE => 0, ClassiPeer::CLASSE => 1, ClassiPeer::SEZIONE => 2, ClassiPeer::ID_INDIRIZZO => 3, ClassiPeer::CODICE_REGISTRO => 4, ClassiPeer::ORDINAMENTO => 5, ClassiPeer::CHI_INSERISCE => 6, ClassiPeer::DATA_INSERIMENTO => 7, ClassiPeer::TIPO_INSERIMENTO => 8, ClassiPeer::CHI_MODIFICA => 9, ClassiPeer::DATA_MODIFICA => 10, ClassiPeer::TIPO_MODIFICA => 11, ClassiPeer::FLAG_CANC => 12, ClassiPeer::CODICE_REGISTRO_2 => 13, ClassiPeer::CODICE_REGISTRO_3 => 14, ClassiPeer::CODICE_REGISTRO_4 => 15, ClassiPeer::BLOCCO_SCRUTINI => 16, ClassiPeer::CONSIGLIO_CLASSE_ATTIVO => 17, ClassiPeer::TEMPO_FUNZIONAMENTO => 18, ClassiPeer::PUBB_PRIMO_SCRITTO => 19, ClassiPeer::PUBB_SECONDO_SCRITTO => 20, ClassiPeer::PUBB_TERZO_SCRITTO => 21, ClassiPeer::PUBB_ORALE => 22, ClassiPeer::ID_FLUSSO => 23, ClassiPeer::AUTENTICAZIONE_ALTERNATIVA => 24, ClassiPeer::EFFETTUA_CONTROLLO_GATE => 25, ClassiPeer::DATA_AGGIORNAMENTO_SIDI => 26, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID_CLASSE' => 0, 'CLASSE' => 1, 'SEZIONE' => 2, 'ID_INDIRIZZO' => 3, 'CODICE_REGISTRO' => 4, 'ORDINAMENTO' => 5, 'CHI_INSERISCE' => 6, 'DATA_INSERIMENTO' => 7, 'TIPO_INSERIMENTO' => 8, 'CHI_MODIFICA' => 9, 'DATA_MODIFICA' => 10, 'TIPO_MODIFICA' => 11, 'FLAG_CANC' => 12, 'CODICE_REGISTRO_2' => 13, 'CODICE_REGISTRO_3' => 14, 'CODICE_REGISTRO_4' => 15, 'BLOCCO_SCRUTINI' => 16, 'CONSIGLIO_CLASSE_ATTIVO' => 17, 'TEMPO_FUNZIONAMENTO' => 18, 'PUBB_PRIMO_SCRITTO' => 19, 'PUBB_SECONDO_SCRITTO' => 20, 'PUBB_TERZO_SCRITTO' => 21, 'PUBB_ORALE' => 22, 'ID_FLUSSO' => 23, 'AUTENTICAZIONE_ALTERNATIVA' => 24, 'EFFETTUA_CONTROLLO_GATE' => 25, 'DATA_AGGIORNAMENTO_SIDI' => 26, ),
        BasePeer::TYPE_FIELDNAME => array ('id_classe' => 0, 'classe' => 1, 'sezione' => 2, 'id_indirizzo' => 3, 'codice_registro' => 4, 'ordinamento' => 5, 'chi_inserisce' => 6, 'data_inserimento' => 7, 'tipo_inserimento' => 8, 'chi_modifica' => 9, 'data_modifica' => 10, 'tipo_modifica' => 11, 'flag_canc' => 12, 'codice_registro_2' => 13, 'codice_registro_3' => 14, 'codice_registro_4' => 15, 'blocco_scrutini' => 16, 'consiglio_classe_attivo' => 17, 'tempo_funzionamento' => 18, 'pubb_primo_scritto' => 19, 'pubb_secondo_scritto' => 20, 'pubb_terzo_scritto' => 21, 'pubb_orale' => 22, 'id_flusso' => 23, 'autenticazione_alternativa' => 24, 'effettua_controllo_gate' => 25, 'data_aggiornamento_sidi' => 26, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = ClassiPeer::getFieldNames($toType);
        $key = isset(ClassiPeer::$fieldKeys[$fromType][$name]) ? ClassiPeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(ClassiPeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, ClassiPeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return ClassiPeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. ClassiPeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(ClassiPeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(ClassiPeer::ID_CLASSE);
            $criteria->addSelectColumn(ClassiPeer::CLASSE);
            $criteria->addSelectColumn(ClassiPeer::SEZIONE);
            $criteria->addSelectColumn(ClassiPeer::ID_INDIRIZZO);
            $criteria->addSelectColumn(ClassiPeer::CODICE_REGISTRO);
            $criteria->addSelectColumn(ClassiPeer::ORDINAMENTO);
            $criteria->addSelectColumn(ClassiPeer::CHI_INSERISCE);
            $criteria->addSelectColumn(ClassiPeer::DATA_INSERIMENTO);
            $criteria->addSelectColumn(ClassiPeer::TIPO_INSERIMENTO);
            $criteria->addSelectColumn(ClassiPeer::CHI_MODIFICA);
            $criteria->addSelectColumn(ClassiPeer::DATA_MODIFICA);
            $criteria->addSelectColumn(ClassiPeer::TIPO_MODIFICA);
            $criteria->addSelectColumn(ClassiPeer::FLAG_CANC);
            $criteria->addSelectColumn(ClassiPeer::CODICE_REGISTRO_2);
            $criteria->addSelectColumn(ClassiPeer::CODICE_REGISTRO_3);
            $criteria->addSelectColumn(ClassiPeer::CODICE_REGISTRO_4);
            $criteria->addSelectColumn(ClassiPeer::BLOCCO_SCRUTINI);
            $criteria->addSelectColumn(ClassiPeer::CONSIGLIO_CLASSE_ATTIVO);
            $criteria->addSelectColumn(ClassiPeer::TEMPO_FUNZIONAMENTO);
            $criteria->addSelectColumn(ClassiPeer::PUBB_PRIMO_SCRITTO);
            $criteria->addSelectColumn(ClassiPeer::PUBB_SECONDO_SCRITTO);
            $criteria->addSelectColumn(ClassiPeer::PUBB_TERZO_SCRITTO);
            $criteria->addSelectColumn(ClassiPeer::PUBB_ORALE);
            $criteria->addSelectColumn(ClassiPeer::ID_FLUSSO);
            $criteria->addSelectColumn(ClassiPeer::AUTENTICAZIONE_ALTERNATIVA);
            $criteria->addSelectColumn(ClassiPeer::EFFETTUA_CONTROLLO_GATE);
            $criteria->addSelectColumn(ClassiPeer::DATA_AGGIORNAMENTO_SIDI);
        } else {
            $criteria->addSelectColumn($alias . '.id_classe');
            $criteria->addSelectColumn($alias . '.classe');
            $criteria->addSelectColumn($alias . '.sezione');
            $criteria->addSelectColumn($alias . '.id_indirizzo');
            $criteria->addSelectColumn($alias . '.codice_registro');
            $criteria->addSelectColumn($alias . '.ordinamento');
            $criteria->addSelectColumn($alias . '.chi_inserisce');
            $criteria->addSelectColumn($alias . '.data_inserimento');
            $criteria->addSelectColumn($alias . '.tipo_inserimento');
            $criteria->addSelectColumn($alias . '.chi_modifica');
            $criteria->addSelectColumn($alias . '.data_modifica');
            $criteria->addSelectColumn($alias . '.tipo_modifica');
            $criteria->addSelectColumn($alias . '.flag_canc');
            $criteria->addSelectColumn($alias . '.codice_registro_2');
            $criteria->addSelectColumn($alias . '.codice_registro_3');
            $criteria->addSelectColumn($alias . '.codice_registro_4');
            $criteria->addSelectColumn($alias . '.blocco_scrutini');
            $criteria->addSelectColumn($alias . '.consiglio_classe_attivo');
            $criteria->addSelectColumn($alias . '.tempo_funzionamento');
            $criteria->addSelectColumn($alias . '.pubb_primo_scritto');
            $criteria->addSelectColumn($alias . '.pubb_secondo_scritto');
            $criteria->addSelectColumn($alias . '.pubb_terzo_scritto');
            $criteria->addSelectColumn($alias . '.pubb_orale');
            $criteria->addSelectColumn($alias . '.id_flusso');
            $criteria->addSelectColumn($alias . '.autenticazione_alternativa');
            $criteria->addSelectColumn($alias . '.effettua_controllo_gate');
            $criteria->addSelectColumn($alias . '.data_aggiornamento_sidi');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(ClassiPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            ClassiPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(ClassiPeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return Classi
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = ClassiPeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return ClassiPeer::populateObjects(ClassiPeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            ClassiPeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(ClassiPeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param Classi $obj A Classi object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getIdClasse();
            } // if key === null
            ClassiPeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A Classi object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof Classi) {
                $key = (string) $value->getIdClasse();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or Classi object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(ClassiPeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return Classi Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(ClassiPeer::$instances[$key])) {
                return ClassiPeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (ClassiPeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        ClassiPeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to classi
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = ClassiPeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = ClassiPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = ClassiPeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                ClassiPeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (Classi object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = ClassiPeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = ClassiPeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + ClassiPeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = ClassiPeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            ClassiPeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(ClassiPeer::DATABASE_NAME)->getTable(ClassiPeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseClassiPeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseClassiPeer::TABLE_NAME)) {
        $dbMap->addTableObject(new ClassiTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return ClassiPeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a Classi or Criteria object.
     *
     * @param      mixed $values Criteria or Classi object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from Classi object
        }

        if ($criteria->containsKey(ClassiPeer::ID_CLASSE) && $criteria->keyContainsValue(ClassiPeer::ID_CLASSE) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.ClassiPeer::ID_CLASSE.')');
        }


        // Set the correct dbName
        $criteria->setDbName(ClassiPeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a Classi or Criteria object.
     *
     * @param      mixed $values Criteria or Classi object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(ClassiPeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(ClassiPeer::ID_CLASSE);
            $value = $criteria->remove(ClassiPeer::ID_CLASSE);
            if ($value) {
                $selectCriteria->add(ClassiPeer::ID_CLASSE, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(ClassiPeer::TABLE_NAME);
            }

        } else { // $values is Classi object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(ClassiPeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the classi table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(ClassiPeer::TABLE_NAME, $con, ClassiPeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            ClassiPeer::clearInstancePool();
            ClassiPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a Classi or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or Classi object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            ClassiPeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof Classi) { // it's a model object
            // invalidate the cache for this single object
            ClassiPeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(ClassiPeer::DATABASE_NAME);
            $criteria->add(ClassiPeer::ID_CLASSE, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                ClassiPeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(ClassiPeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            ClassiPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given Classi object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param Classi $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(ClassiPeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(ClassiPeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(ClassiPeer::DATABASE_NAME, ClassiPeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return Classi
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = ClassiPeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(ClassiPeer::DATABASE_NAME);
        $criteria->add(ClassiPeer::ID_CLASSE, $pk);

        $v = ClassiPeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return Classi[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(ClassiPeer::DATABASE_NAME);
            $criteria->add(ClassiPeer::ID_CLASSE, $pks, Criteria::IN);
            $objs = ClassiPeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseClassiPeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseClassiPeer::buildTableMap();

