<?php

namespace Ccp\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\Tasse;
use Ccp\TipiTasse;
use Ccp\TipiTassePeer;
use Ccp\TipiTasseQuery;

/**
 * Base class that represents a query for the 'tipi_tasse' table.
 *
 *
 *
 * @method TipiTasseQuery orderByIdTipoTassa($order = Criteria::ASC) Order by the id_tipo_tassa column
 * @method TipiTasseQuery orderByDescrizione($order = Criteria::ASC) Order by the descrizione column
 * @method TipiTasseQuery orderByTipologia($order = Criteria::ASC) Order by the tipologia column
 * @method TipiTasseQuery orderByTassaGovernativa($order = Criteria::ASC) Order by the tassa_governativa column
 * @method TipiTasseQuery orderByDataScadenza($order = Criteria::ASC) Order by the data_scadenza column
 * @method TipiTasseQuery orderByCumulativa($order = Criteria::ASC) Order by the cumulativa column
 * @method TipiTasseQuery orderByAnnoScolasticoRiferimento($order = Criteria::ASC) Order by the anno_scolastico_riferimento column
 * @method TipiTasseQuery orderByImportoBase($order = Criteria::ASC) Order by the importo_base column
 *
 * @method TipiTasseQuery groupByIdTipoTassa() Group by the id_tipo_tassa column
 * @method TipiTasseQuery groupByDescrizione() Group by the descrizione column
 * @method TipiTasseQuery groupByTipologia() Group by the tipologia column
 * @method TipiTasseQuery groupByTassaGovernativa() Group by the tassa_governativa column
 * @method TipiTasseQuery groupByDataScadenza() Group by the data_scadenza column
 * @method TipiTasseQuery groupByCumulativa() Group by the cumulativa column
 * @method TipiTasseQuery groupByAnnoScolasticoRiferimento() Group by the anno_scolastico_riferimento column
 * @method TipiTasseQuery groupByImportoBase() Group by the importo_base column
 *
 * @method TipiTasseQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method TipiTasseQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method TipiTasseQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method TipiTasseQuery leftJoinTasse($relationAlias = null) Adds a LEFT JOIN clause to the query using the Tasse relation
 * @method TipiTasseQuery rightJoinTasse($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Tasse relation
 * @method TipiTasseQuery innerJoinTasse($relationAlias = null) Adds a INNER JOIN clause to the query using the Tasse relation
 *
 * @method TipiTasse findOne(PropelPDO $con = null) Return the first TipiTasse matching the query
 * @method TipiTasse findOneOrCreate(PropelPDO $con = null) Return the first TipiTasse matching the query, or a new TipiTasse object populated from the query conditions when no match is found
 *
 * @method TipiTasse findOneByDescrizione(string $descrizione) Return the first TipiTasse filtered by the descrizione column
 * @method TipiTasse findOneByTipologia(string $tipologia) Return the first TipiTasse filtered by the tipologia column
 * @method TipiTasse findOneByTassaGovernativa(boolean $tassa_governativa) Return the first TipiTasse filtered by the tassa_governativa column
 * @method TipiTasse findOneByDataScadenza(string $data_scadenza) Return the first TipiTasse filtered by the data_scadenza column
 * @method TipiTasse findOneByCumulativa(int $cumulativa) Return the first TipiTasse filtered by the cumulativa column
 * @method TipiTasse findOneByAnnoScolasticoRiferimento(string $anno_scolastico_riferimento) Return the first TipiTasse filtered by the anno_scolastico_riferimento column
 * @method TipiTasse findOneByImportoBase(double $importo_base) Return the first TipiTasse filtered by the importo_base column
 *
 * @method array findByIdTipoTassa(int $id_tipo_tassa) Return TipiTasse objects filtered by the id_tipo_tassa column
 * @method array findByDescrizione(string $descrizione) Return TipiTasse objects filtered by the descrizione column
 * @method array findByTipologia(string $tipologia) Return TipiTasse objects filtered by the tipologia column
 * @method array findByTassaGovernativa(boolean $tassa_governativa) Return TipiTasse objects filtered by the tassa_governativa column
 * @method array findByDataScadenza(string $data_scadenza) Return TipiTasse objects filtered by the data_scadenza column
 * @method array findByCumulativa(int $cumulativa) Return TipiTasse objects filtered by the cumulativa column
 * @method array findByAnnoScolasticoRiferimento(string $anno_scolastico_riferimento) Return TipiTasse objects filtered by the anno_scolastico_riferimento column
 * @method array findByImportoBase(double $importo_base) Return TipiTasse objects filtered by the importo_base column
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseTipiTasseQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseTipiTasseQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Ccp\\TipiTasse';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new TipiTasseQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   TipiTasseQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return TipiTasseQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof TipiTasseQuery) {
            return $criteria;
        }
        $query = new TipiTasseQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   TipiTasse|TipiTasse[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = TipiTassePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(TipiTassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 TipiTasse A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByIdTipoTassa($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 TipiTasse A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id_tipo_tassa", "descrizione", "tipologia", "tassa_governativa", "data_scadenza", "cumulativa", "anno_scolastico_riferimento", "importo_base" FROM "tipi_tasse" WHERE "id_tipo_tassa" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new TipiTasse();
            $obj->hydrate($row);
            TipiTassePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return TipiTasse|TipiTasse[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|TipiTasse[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id_tipo_tassa column
     *
     * Example usage:
     * <code>
     * $query->filterByIdTipoTassa(1234); // WHERE id_tipo_tassa = 1234
     * $query->filterByIdTipoTassa(array(12, 34)); // WHERE id_tipo_tassa IN (12, 34)
     * $query->filterByIdTipoTassa(array('min' => 12)); // WHERE id_tipo_tassa >= 12
     * $query->filterByIdTipoTassa(array('max' => 12)); // WHERE id_tipo_tassa <= 12
     * </code>
     *
     * @param     mixed $idTipoTassa The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByIdTipoTassa($idTipoTassa = null, $comparison = null)
    {
        if (is_array($idTipoTassa)) {
            $useMinMax = false;
            if (isset($idTipoTassa['min'])) {
                $this->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $idTipoTassa['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idTipoTassa['max'])) {
                $this->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $idTipoTassa['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $idTipoTassa, $comparison);
    }

    /**
     * Filter the query on the descrizione column
     *
     * Example usage:
     * <code>
     * $query->filterByDescrizione('fooValue');   // WHERE descrizione = 'fooValue'
     * $query->filterByDescrizione('%fooValue%'); // WHERE descrizione LIKE '%fooValue%'
     * </code>
     *
     * @param     string $descrizione The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByDescrizione($descrizione = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($descrizione)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $descrizione)) {
                $descrizione = str_replace('*', '%', $descrizione);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::DESCRIZIONE, $descrizione, $comparison);
    }

    /**
     * Filter the query on the tipologia column
     *
     * Example usage:
     * <code>
     * $query->filterByTipologia('fooValue');   // WHERE tipologia = 'fooValue'
     * $query->filterByTipologia('%fooValue%'); // WHERE tipologia LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipologia The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByTipologia($tipologia = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipologia)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipologia)) {
                $tipologia = str_replace('*', '%', $tipologia);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::TIPOLOGIA, $tipologia, $comparison);
    }

    /**
     * Filter the query on the tassa_governativa column
     *
     * Example usage:
     * <code>
     * $query->filterByTassaGovernativa(true); // WHERE tassa_governativa = true
     * $query->filterByTassaGovernativa('yes'); // WHERE tassa_governativa = true
     * </code>
     *
     * @param     boolean|string $tassaGovernativa The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByTassaGovernativa($tassaGovernativa = null, $comparison = null)
    {
        if (is_string($tassaGovernativa)) {
            $tassaGovernativa = in_array(strtolower($tassaGovernativa), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(TipiTassePeer::TASSA_GOVERNATIVA, $tassaGovernativa, $comparison);
    }

    /**
     * Filter the query on the data_scadenza column
     *
     * Example usage:
     * <code>
     * $query->filterByDataScadenza(1234); // WHERE data_scadenza = 1234
     * $query->filterByDataScadenza(array(12, 34)); // WHERE data_scadenza IN (12, 34)
     * $query->filterByDataScadenza(array('min' => 12)); // WHERE data_scadenza >= 12
     * $query->filterByDataScadenza(array('max' => 12)); // WHERE data_scadenza <= 12
     * </code>
     *
     * @param     mixed $dataScadenza The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByDataScadenza($dataScadenza = null, $comparison = null)
    {
        if (is_array($dataScadenza)) {
            $useMinMax = false;
            if (isset($dataScadenza['min'])) {
                $this->addUsingAlias(TipiTassePeer::DATA_SCADENZA, $dataScadenza['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataScadenza['max'])) {
                $this->addUsingAlias(TipiTassePeer::DATA_SCADENZA, $dataScadenza['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::DATA_SCADENZA, $dataScadenza, $comparison);
    }

    /**
     * Filter the query on the cumulativa column
     *
     * Example usage:
     * <code>
     * $query->filterByCumulativa(1234); // WHERE cumulativa = 1234
     * $query->filterByCumulativa(array(12, 34)); // WHERE cumulativa IN (12, 34)
     * $query->filterByCumulativa(array('min' => 12)); // WHERE cumulativa >= 12
     * $query->filterByCumulativa(array('max' => 12)); // WHERE cumulativa <= 12
     * </code>
     *
     * @param     mixed $cumulativa The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByCumulativa($cumulativa = null, $comparison = null)
    {
        if (is_array($cumulativa)) {
            $useMinMax = false;
            if (isset($cumulativa['min'])) {
                $this->addUsingAlias(TipiTassePeer::CUMULATIVA, $cumulativa['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($cumulativa['max'])) {
                $this->addUsingAlias(TipiTassePeer::CUMULATIVA, $cumulativa['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::CUMULATIVA, $cumulativa, $comparison);
    }

    /**
     * Filter the query on the anno_scolastico_riferimento column
     *
     * Example usage:
     * <code>
     * $query->filterByAnnoScolasticoRiferimento('fooValue');   // WHERE anno_scolastico_riferimento = 'fooValue'
     * $query->filterByAnnoScolasticoRiferimento('%fooValue%'); // WHERE anno_scolastico_riferimento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $annoScolasticoRiferimento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByAnnoScolasticoRiferimento($annoScolasticoRiferimento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($annoScolasticoRiferimento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $annoScolasticoRiferimento)) {
                $annoScolasticoRiferimento = str_replace('*', '%', $annoScolasticoRiferimento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::ANNO_SCOLASTICO_RIFERIMENTO, $annoScolasticoRiferimento, $comparison);
    }

    /**
     * Filter the query on the importo_base column
     *
     * Example usage:
     * <code>
     * $query->filterByImportoBase(1234); // WHERE importo_base = 1234
     * $query->filterByImportoBase(array(12, 34)); // WHERE importo_base IN (12, 34)
     * $query->filterByImportoBase(array('min' => 12)); // WHERE importo_base >= 12
     * $query->filterByImportoBase(array('max' => 12)); // WHERE importo_base <= 12
     * </code>
     *
     * @param     mixed $importoBase The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function filterByImportoBase($importoBase = null, $comparison = null)
    {
        if (is_array($importoBase)) {
            $useMinMax = false;
            if (isset($importoBase['min'])) {
                $this->addUsingAlias(TipiTassePeer::IMPORTO_BASE, $importoBase['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($importoBase['max'])) {
                $this->addUsingAlias(TipiTassePeer::IMPORTO_BASE, $importoBase['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TipiTassePeer::IMPORTO_BASE, $importoBase, $comparison);
    }

    /**
     * Filter the query by a related Tasse object
     *
     * @param   Tasse|PropelObjectCollection $tasse  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TipiTasseQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTasse($tasse, $comparison = null)
    {
        if ($tasse instanceof Tasse) {
            return $this
                ->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $tasse->getIdTipoTassa(), $comparison);
        } elseif ($tasse instanceof PropelObjectCollection) {
            return $this
                ->useTasseQuery()
                ->filterByPrimaryKeys($tasse->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByTasse() only accepts arguments of type Tasse or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Tasse relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function joinTasse($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Tasse');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Tasse');
        }

        return $this;
    }

    /**
     * Use the Tasse relation Tasse object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Ccp\TasseQuery A secondary query class using the current class as primary query
     */
    public function useTasseQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinTasse($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Tasse', '\Ccp\TasseQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   TipiTasse $tipiTasse Object to remove from the list of results
     *
     * @return TipiTasseQuery The current query, for fluid interface
     */
    public function prune($tipiTasse = null)
    {
        if ($tipiTasse) {
            $this->addUsingAlias(TipiTassePeer::ID_TIPO_TASSA, $tipiTasse->getIdTipoTassa(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
