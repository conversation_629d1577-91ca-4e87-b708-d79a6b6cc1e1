<?php

namespace Ccp\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Ccp\Classi;
use Ccp\ClassiPeer;
use Ccp\ClassiQuery;

/**
 * Base class that represents a row from the 'classi' table.
 *
 *
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseClassi extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Ccp\\ClassiPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        ClassiPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id_classe field.
     * @var        int
     */
    protected $id_classe;

    /**
     * The value for the classe field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $classe;

    /**
     * The value for the sezione field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $sezione;

    /**
     * The value for the id_indirizzo field.
     * @var        int
     */
    protected $id_indirizzo;

    /**
     * The value for the codice_registro field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_registro;

    /**
     * The value for the ordinamento field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $ordinamento;

    /**
     * The value for the chi_inserisce field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $chi_inserisce;

    /**
     * The value for the data_inserimento field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_inserimento;

    /**
     * The value for the tipo_inserimento field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_inserimento;

    /**
     * The value for the chi_modifica field.
     * Note: this column has a database default value of: '(-1)'
     * @var        string
     */
    protected $chi_modifica;

    /**
     * The value for the data_modifica field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_modifica;

    /**
     * The value for the tipo_modifica field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tipo_modifica;

    /**
     * The value for the flag_canc field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $flag_canc;

    /**
     * The value for the codice_registro_2 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_registro_2;

    /**
     * The value for the codice_registro_3 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_registro_3;

    /**
     * The value for the codice_registro_4 field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $codice_registro_4;

    /**
     * The value for the blocco_scrutini field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $blocco_scrutini;

    /**
     * The value for the consiglio_classe_attivo field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $consiglio_classe_attivo;

    /**
     * The value for the tempo_funzionamento field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $tempo_funzionamento;

    /**
     * The value for the pubb_primo_scritto field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $pubb_primo_scritto;

    /**
     * The value for the pubb_secondo_scritto field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $pubb_secondo_scritto;

    /**
     * The value for the pubb_terzo_scritto field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $pubb_terzo_scritto;

    /**
     * The value for the pubb_orale field.
     * Note: this column has a database default value of: 'NO'
     * @var        string
     */
    protected $pubb_orale;

    /**
     * The value for the id_flusso field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $id_flusso;

    /**
     * The value for the autenticazione_alternativa field.
     * @var        string
     */
    protected $autenticazione_alternativa;

    /**
     * The value for the effettua_controllo_gate field.
     * Note: this column has a database default value of: 'SI'
     * @var        string
     */
    protected $effettua_controllo_gate;

    /**
     * The value for the data_aggiornamento_sidi field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $data_aggiornamento_sidi;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->classe = '';
        $this->sezione = '';
        $this->codice_registro = '';
        $this->ordinamento = '0';
        $this->chi_inserisce = '(-1)';
        $this->data_inserimento = '0';
        $this->tipo_inserimento = '';
        $this->chi_modifica = '(-1)';
        $this->data_modifica = '0';
        $this->tipo_modifica = '';
        $this->flag_canc = '0';
        $this->codice_registro_2 = '';
        $this->codice_registro_3 = '';
        $this->codice_registro_4 = '';
        $this->blocco_scrutini = '';
        $this->consiglio_classe_attivo = 'NO';
        $this->tempo_funzionamento = '';
        $this->pubb_primo_scritto = 'NO';
        $this->pubb_secondo_scritto = 'NO';
        $this->pubb_terzo_scritto = 'NO';
        $this->pubb_orale = 'NO';
        $this->id_flusso = '0';
        $this->effettua_controllo_gate = 'SI';
        $this->data_aggiornamento_sidi = 0;
    }

    /**
     * Initializes internal state of BaseClassi object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id_classe] column value.
     *
     * @return int
     */
    public function getIdClasse()
    {

        return $this->id_classe;
    }

    /**
     * Get the [classe] column value.
     *
     * @return string
     */
    public function getClasse()
    {

        return $this->classe;
    }

    /**
     * Get the [sezione] column value.
     *
     * @return string
     */
    public function getSezione()
    {

        return $this->sezione;
    }

    /**
     * Get the [id_indirizzo] column value.
     *
     * @return int
     */
    public function getIdIndirizzo()
    {

        return $this->id_indirizzo;
    }

    /**
     * Get the [codice_registro] column value.
     *
     * @return string
     */
    public function getCodiceRegistro()
    {

        return $this->codice_registro;
    }

    /**
     * Get the [ordinamento] column value.
     *
     * @return string
     */
    public function getOrdinamento()
    {

        return $this->ordinamento;
    }

    /**
     * Get the [chi_inserisce] column value.
     *
     * @return string
     */
    public function getChiInserisce()
    {

        return $this->chi_inserisce;
    }

    /**
     * Get the [data_inserimento] column value.
     *
     * @return string
     */
    public function getDataInserimento()
    {

        return $this->data_inserimento;
    }

    /**
     * Get the [tipo_inserimento] column value.
     *
     * @return string
     */
    public function getTipoInserimento()
    {

        return $this->tipo_inserimento;
    }

    /**
     * Get the [chi_modifica] column value.
     *
     * @return string
     */
    public function getChiModifica()
    {

        return $this->chi_modifica;
    }

    /**
     * Get the [data_modifica] column value.
     *
     * @return string
     */
    public function getDataModifica()
    {

        return $this->data_modifica;
    }

    /**
     * Get the [tipo_modifica] column value.
     *
     * @return string
     */
    public function getTipoModifica()
    {

        return $this->tipo_modifica;
    }

    /**
     * Get the [flag_canc] column value.
     *
     * @return string
     */
    public function getFlagCanc()
    {

        return $this->flag_canc;
    }

    /**
     * Get the [codice_registro_2] column value.
     *
     * @return string
     */
    public function getCodiceRegistro2()
    {

        return $this->codice_registro_2;
    }

    /**
     * Get the [codice_registro_3] column value.
     *
     * @return string
     */
    public function getCodiceRegistro3()
    {

        return $this->codice_registro_3;
    }

    /**
     * Get the [codice_registro_4] column value.
     *
     * @return string
     */
    public function getCodiceRegistro4()
    {

        return $this->codice_registro_4;
    }

    /**
     * Get the [blocco_scrutini] column value.
     *
     * @return string
     */
    public function getBloccoScrutini()
    {

        return $this->blocco_scrutini;
    }

    /**
     * Get the [consiglio_classe_attivo] column value.
     *
     * @return string
     */
    public function getConsiglioClasseAttivo()
    {

        return $this->consiglio_classe_attivo;
    }

    /**
     * Get the [tempo_funzionamento] column value.
     *
     * @return string
     */
    public function getTempoFunzionamento()
    {

        return $this->tempo_funzionamento;
    }

    /**
     * Get the [pubb_primo_scritto] column value.
     *
     * @return string
     */
    public function getPubbPrimoScritto()
    {

        return $this->pubb_primo_scritto;
    }

    /**
     * Get the [pubb_secondo_scritto] column value.
     *
     * @return string
     */
    public function getPubbSecondoScritto()
    {

        return $this->pubb_secondo_scritto;
    }

    /**
     * Get the [pubb_terzo_scritto] column value.
     *
     * @return string
     */
    public function getPubbTerzoScritto()
    {

        return $this->pubb_terzo_scritto;
    }

    /**
     * Get the [pubb_orale] column value.
     *
     * @return string
     */
    public function getPubbOrale()
    {

        return $this->pubb_orale;
    }

    /**
     * Get the [id_flusso] column value.
     *
     * @return string
     */
    public function getIdFlusso()
    {

        return $this->id_flusso;
    }

    /**
     * Get the [autenticazione_alternativa] column value.
     *
     * @return string
     */
    public function getAutenticazioneAlternativa()
    {

        return $this->autenticazione_alternativa;
    }

    /**
     * Get the [effettua_controllo_gate] column value.
     *
     * @return string
     */
    public function getEffettuaControlloGate()
    {

        return $this->effettua_controllo_gate;
    }

    /**
     * Get the [data_aggiornamento_sidi] column value.
     *
     * @return int
     */
    public function getDataAggiornamentoSidi()
    {

        return $this->data_aggiornamento_sidi;
    }

    /**
     * Set the value of [id_classe] column.
     *
     * @param  int $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setIdClasse($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_classe !== $v) {
            $this->id_classe = $v;
            $this->modifiedColumns[] = ClassiPeer::ID_CLASSE;
        }


        return $this;
    } // setIdClasse()

    /**
     * Set the value of [classe] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setClasse($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->classe !== $v) {
            $this->classe = $v;
            $this->modifiedColumns[] = ClassiPeer::CLASSE;
        }


        return $this;
    } // setClasse()

    /**
     * Set the value of [sezione] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setSezione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->sezione !== $v) {
            $this->sezione = $v;
            $this->modifiedColumns[] = ClassiPeer::SEZIONE;
        }


        return $this;
    } // setSezione()

    /**
     * Set the value of [id_indirizzo] column.
     *
     * @param  int $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setIdIndirizzo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_indirizzo !== $v) {
            $this->id_indirizzo = $v;
            $this->modifiedColumns[] = ClassiPeer::ID_INDIRIZZO;
        }


        return $this;
    } // setIdIndirizzo()

    /**
     * Set the value of [codice_registro] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setCodiceRegistro($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_registro !== $v) {
            $this->codice_registro = $v;
            $this->modifiedColumns[] = ClassiPeer::CODICE_REGISTRO;
        }


        return $this;
    } // setCodiceRegistro()

    /**
     * Set the value of [ordinamento] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setOrdinamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ordinamento !== $v) {
            $this->ordinamento = $v;
            $this->modifiedColumns[] = ClassiPeer::ORDINAMENTO;
        }


        return $this;
    } // setOrdinamento()

    /**
     * Set the value of [chi_inserisce] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setChiInserisce($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->chi_inserisce !== $v) {
            $this->chi_inserisce = $v;
            $this->modifiedColumns[] = ClassiPeer::CHI_INSERISCE;
        }


        return $this;
    } // setChiInserisce()

    /**
     * Set the value of [data_inserimento] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setDataInserimento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_inserimento !== $v) {
            $this->data_inserimento = $v;
            $this->modifiedColumns[] = ClassiPeer::DATA_INSERIMENTO;
        }


        return $this;
    } // setDataInserimento()

    /**
     * Set the value of [tipo_inserimento] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setTipoInserimento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_inserimento !== $v) {
            $this->tipo_inserimento = $v;
            $this->modifiedColumns[] = ClassiPeer::TIPO_INSERIMENTO;
        }


        return $this;
    } // setTipoInserimento()

    /**
     * Set the value of [chi_modifica] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setChiModifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->chi_modifica !== $v) {
            $this->chi_modifica = $v;
            $this->modifiedColumns[] = ClassiPeer::CHI_MODIFICA;
        }


        return $this;
    } // setChiModifica()

    /**
     * Set the value of [data_modifica] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setDataModifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_modifica !== $v) {
            $this->data_modifica = $v;
            $this->modifiedColumns[] = ClassiPeer::DATA_MODIFICA;
        }


        return $this;
    } // setDataModifica()

    /**
     * Set the value of [tipo_modifica] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setTipoModifica($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipo_modifica !== $v) {
            $this->tipo_modifica = $v;
            $this->modifiedColumns[] = ClassiPeer::TIPO_MODIFICA;
        }


        return $this;
    } // setTipoModifica()

    /**
     * Set the value of [flag_canc] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setFlagCanc($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->flag_canc !== $v) {
            $this->flag_canc = $v;
            $this->modifiedColumns[] = ClassiPeer::FLAG_CANC;
        }


        return $this;
    } // setFlagCanc()

    /**
     * Set the value of [codice_registro_2] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setCodiceRegistro2($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_registro_2 !== $v) {
            $this->codice_registro_2 = $v;
            $this->modifiedColumns[] = ClassiPeer::CODICE_REGISTRO_2;
        }


        return $this;
    } // setCodiceRegistro2()

    /**
     * Set the value of [codice_registro_3] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setCodiceRegistro3($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_registro_3 !== $v) {
            $this->codice_registro_3 = $v;
            $this->modifiedColumns[] = ClassiPeer::CODICE_REGISTRO_3;
        }


        return $this;
    } // setCodiceRegistro3()

    /**
     * Set the value of [codice_registro_4] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setCodiceRegistro4($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->codice_registro_4 !== $v) {
            $this->codice_registro_4 = $v;
            $this->modifiedColumns[] = ClassiPeer::CODICE_REGISTRO_4;
        }


        return $this;
    } // setCodiceRegistro4()

    /**
     * Set the value of [blocco_scrutini] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setBloccoScrutini($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->blocco_scrutini !== $v) {
            $this->blocco_scrutini = $v;
            $this->modifiedColumns[] = ClassiPeer::BLOCCO_SCRUTINI;
        }


        return $this;
    } // setBloccoScrutini()

    /**
     * Set the value of [consiglio_classe_attivo] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setConsiglioClasseAttivo($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->consiglio_classe_attivo !== $v) {
            $this->consiglio_classe_attivo = $v;
            $this->modifiedColumns[] = ClassiPeer::CONSIGLIO_CLASSE_ATTIVO;
        }


        return $this;
    } // setConsiglioClasseAttivo()

    /**
     * Set the value of [tempo_funzionamento] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setTempoFunzionamento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tempo_funzionamento !== $v) {
            $this->tempo_funzionamento = $v;
            $this->modifiedColumns[] = ClassiPeer::TEMPO_FUNZIONAMENTO;
        }


        return $this;
    } // setTempoFunzionamento()

    /**
     * Set the value of [pubb_primo_scritto] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setPubbPrimoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pubb_primo_scritto !== $v) {
            $this->pubb_primo_scritto = $v;
            $this->modifiedColumns[] = ClassiPeer::PUBB_PRIMO_SCRITTO;
        }


        return $this;
    } // setPubbPrimoScritto()

    /**
     * Set the value of [pubb_secondo_scritto] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setPubbSecondoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pubb_secondo_scritto !== $v) {
            $this->pubb_secondo_scritto = $v;
            $this->modifiedColumns[] = ClassiPeer::PUBB_SECONDO_SCRITTO;
        }


        return $this;
    } // setPubbSecondoScritto()

    /**
     * Set the value of [pubb_terzo_scritto] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setPubbTerzoScritto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pubb_terzo_scritto !== $v) {
            $this->pubb_terzo_scritto = $v;
            $this->modifiedColumns[] = ClassiPeer::PUBB_TERZO_SCRITTO;
        }


        return $this;
    } // setPubbTerzoScritto()

    /**
     * Set the value of [pubb_orale] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setPubbOrale($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->pubb_orale !== $v) {
            $this->pubb_orale = $v;
            $this->modifiedColumns[] = ClassiPeer::PUBB_ORALE;
        }


        return $this;
    } // setPubbOrale()

    /**
     * Set the value of [id_flusso] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setIdFlusso($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_flusso !== $v) {
            $this->id_flusso = $v;
            $this->modifiedColumns[] = ClassiPeer::ID_FLUSSO;
        }


        return $this;
    } // setIdFlusso()

    /**
     * Set the value of [autenticazione_alternativa] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setAutenticazioneAlternativa($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->autenticazione_alternativa !== $v) {
            $this->autenticazione_alternativa = $v;
            $this->modifiedColumns[] = ClassiPeer::AUTENTICAZIONE_ALTERNATIVA;
        }


        return $this;
    } // setAutenticazioneAlternativa()

    /**
     * Set the value of [effettua_controllo_gate] column.
     *
     * @param  string $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setEffettuaControlloGate($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->effettua_controllo_gate !== $v) {
            $this->effettua_controllo_gate = $v;
            $this->modifiedColumns[] = ClassiPeer::EFFETTUA_CONTROLLO_GATE;
        }


        return $this;
    } // setEffettuaControlloGate()

    /**
     * Set the value of [data_aggiornamento_sidi] column.
     *
     * @param  int $v new value
     * @return Classi The current object (for fluent API support)
     */
    public function setDataAggiornamentoSidi($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->data_aggiornamento_sidi !== $v) {
            $this->data_aggiornamento_sidi = $v;
            $this->modifiedColumns[] = ClassiPeer::DATA_AGGIORNAMENTO_SIDI;
        }


        return $this;
    } // setDataAggiornamentoSidi()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->classe !== '') {
                return false;
            }

            if ($this->sezione !== '') {
                return false;
            }

            if ($this->codice_registro !== '') {
                return false;
            }

            if ($this->ordinamento !== '0') {
                return false;
            }

            if ($this->chi_inserisce !== '(-1)') {
                return false;
            }

            if ($this->data_inserimento !== '0') {
                return false;
            }

            if ($this->tipo_inserimento !== '') {
                return false;
            }

            if ($this->chi_modifica !== '(-1)') {
                return false;
            }

            if ($this->data_modifica !== '0') {
                return false;
            }

            if ($this->tipo_modifica !== '') {
                return false;
            }

            if ($this->flag_canc !== '0') {
                return false;
            }

            if ($this->codice_registro_2 !== '') {
                return false;
            }

            if ($this->codice_registro_3 !== '') {
                return false;
            }

            if ($this->codice_registro_4 !== '') {
                return false;
            }

            if ($this->blocco_scrutini !== '') {
                return false;
            }

            if ($this->consiglio_classe_attivo !== 'NO') {
                return false;
            }

            if ($this->tempo_funzionamento !== '') {
                return false;
            }

            if ($this->pubb_primo_scritto !== 'NO') {
                return false;
            }

            if ($this->pubb_secondo_scritto !== 'NO') {
                return false;
            }

            if ($this->pubb_terzo_scritto !== 'NO') {
                return false;
            }

            if ($this->pubb_orale !== 'NO') {
                return false;
            }

            if ($this->id_flusso !== '0') {
                return false;
            }

            if ($this->effettua_controllo_gate !== 'SI') {
                return false;
            }

            if ($this->data_aggiornamento_sidi !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id_classe = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->classe = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->sezione = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->id_indirizzo = ($row[$startcol + 3] !== null) ? (int) $row[$startcol + 3] : null;
            $this->codice_registro = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->ordinamento = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->chi_inserisce = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->data_inserimento = ($row[$startcol + 7] !== null) ? (string) $row[$startcol + 7] : null;
            $this->tipo_inserimento = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->chi_modifica = ($row[$startcol + 9] !== null) ? (string) $row[$startcol + 9] : null;
            $this->data_modifica = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->tipo_modifica = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->flag_canc = ($row[$startcol + 12] !== null) ? (string) $row[$startcol + 12] : null;
            $this->codice_registro_2 = ($row[$startcol + 13] !== null) ? (string) $row[$startcol + 13] : null;
            $this->codice_registro_3 = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->codice_registro_4 = ($row[$startcol + 15] !== null) ? (string) $row[$startcol + 15] : null;
            $this->blocco_scrutini = ($row[$startcol + 16] !== null) ? (string) $row[$startcol + 16] : null;
            $this->consiglio_classe_attivo = ($row[$startcol + 17] !== null) ? (string) $row[$startcol + 17] : null;
            $this->tempo_funzionamento = ($row[$startcol + 18] !== null) ? (string) $row[$startcol + 18] : null;
            $this->pubb_primo_scritto = ($row[$startcol + 19] !== null) ? (string) $row[$startcol + 19] : null;
            $this->pubb_secondo_scritto = ($row[$startcol + 20] !== null) ? (string) $row[$startcol + 20] : null;
            $this->pubb_terzo_scritto = ($row[$startcol + 21] !== null) ? (string) $row[$startcol + 21] : null;
            $this->pubb_orale = ($row[$startcol + 22] !== null) ? (string) $row[$startcol + 22] : null;
            $this->id_flusso = ($row[$startcol + 23] !== null) ? (string) $row[$startcol + 23] : null;
            $this->autenticazione_alternativa = ($row[$startcol + 24] !== null) ? (string) $row[$startcol + 24] : null;
            $this->effettua_controllo_gate = ($row[$startcol + 25] !== null) ? (string) $row[$startcol + 25] : null;
            $this->data_aggiornamento_sidi = ($row[$startcol + 26] !== null) ? (int) $row[$startcol + 26] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 27; // 27 = ClassiPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Classi object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = ClassiPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = ClassiQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                ClassiPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = ClassiPeer::ID_CLASSE;
        if (null !== $this->id_classe) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . ClassiPeer::ID_CLASSE . ')');
        }
        if (null === $this->id_classe) {
            try {
                $stmt = $con->query("SELECT nextval('classi_id_classe_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id_classe = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(ClassiPeer::ID_CLASSE)) {
            $modifiedColumns[':p' . $index++]  = '"id_classe"';
        }
        if ($this->isColumnModified(ClassiPeer::CLASSE)) {
            $modifiedColumns[':p' . $index++]  = '"classe"';
        }
        if ($this->isColumnModified(ClassiPeer::SEZIONE)) {
            $modifiedColumns[':p' . $index++]  = '"sezione"';
        }
        if ($this->isColumnModified(ClassiPeer::ID_INDIRIZZO)) {
            $modifiedColumns[':p' . $index++]  = '"id_indirizzo"';
        }
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO)) {
            $modifiedColumns[':p' . $index++]  = '"codice_registro"';
        }
        if ($this->isColumnModified(ClassiPeer::ORDINAMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"ordinamento"';
        }
        if ($this->isColumnModified(ClassiPeer::CHI_INSERISCE)) {
            $modifiedColumns[':p' . $index++]  = '"chi_inserisce"';
        }
        if ($this->isColumnModified(ClassiPeer::DATA_INSERIMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"data_inserimento"';
        }
        if ($this->isColumnModified(ClassiPeer::TIPO_INSERIMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"tipo_inserimento"';
        }
        if ($this->isColumnModified(ClassiPeer::CHI_MODIFICA)) {
            $modifiedColumns[':p' . $index++]  = '"chi_modifica"';
        }
        if ($this->isColumnModified(ClassiPeer::DATA_MODIFICA)) {
            $modifiedColumns[':p' . $index++]  = '"data_modifica"';
        }
        if ($this->isColumnModified(ClassiPeer::TIPO_MODIFICA)) {
            $modifiedColumns[':p' . $index++]  = '"tipo_modifica"';
        }
        if ($this->isColumnModified(ClassiPeer::FLAG_CANC)) {
            $modifiedColumns[':p' . $index++]  = '"flag_canc"';
        }
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO_2)) {
            $modifiedColumns[':p' . $index++]  = '"codice_registro_2"';
        }
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO_3)) {
            $modifiedColumns[':p' . $index++]  = '"codice_registro_3"';
        }
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO_4)) {
            $modifiedColumns[':p' . $index++]  = '"codice_registro_4"';
        }
        if ($this->isColumnModified(ClassiPeer::BLOCCO_SCRUTINI)) {
            $modifiedColumns[':p' . $index++]  = '"blocco_scrutini"';
        }
        if ($this->isColumnModified(ClassiPeer::CONSIGLIO_CLASSE_ATTIVO)) {
            $modifiedColumns[':p' . $index++]  = '"consiglio_classe_attivo"';
        }
        if ($this->isColumnModified(ClassiPeer::TEMPO_FUNZIONAMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"tempo_funzionamento"';
        }
        if ($this->isColumnModified(ClassiPeer::PUBB_PRIMO_SCRITTO)) {
            $modifiedColumns[':p' . $index++]  = '"pubb_primo_scritto"';
        }
        if ($this->isColumnModified(ClassiPeer::PUBB_SECONDO_SCRITTO)) {
            $modifiedColumns[':p' . $index++]  = '"pubb_secondo_scritto"';
        }
        if ($this->isColumnModified(ClassiPeer::PUBB_TERZO_SCRITTO)) {
            $modifiedColumns[':p' . $index++]  = '"pubb_terzo_scritto"';
        }
        if ($this->isColumnModified(ClassiPeer::PUBB_ORALE)) {
            $modifiedColumns[':p' . $index++]  = '"pubb_orale"';
        }
        if ($this->isColumnModified(ClassiPeer::ID_FLUSSO)) {
            $modifiedColumns[':p' . $index++]  = '"id_flusso"';
        }
        if ($this->isColumnModified(ClassiPeer::AUTENTICAZIONE_ALTERNATIVA)) {
            $modifiedColumns[':p' . $index++]  = '"autenticazione_alternativa"';
        }
        if ($this->isColumnModified(ClassiPeer::EFFETTUA_CONTROLLO_GATE)) {
            $modifiedColumns[':p' . $index++]  = '"effettua_controllo_gate"';
        }
        if ($this->isColumnModified(ClassiPeer::DATA_AGGIORNAMENTO_SIDI)) {
            $modifiedColumns[':p' . $index++]  = '"data_aggiornamento_sidi"';
        }

        $sql = sprintf(
            'INSERT INTO "classi" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id_classe"':
                        $stmt->bindValue($identifier, $this->id_classe, PDO::PARAM_INT);
                        break;
                    case '"classe"':
                        $stmt->bindValue($identifier, $this->classe, PDO::PARAM_STR);
                        break;
                    case '"sezione"':
                        $stmt->bindValue($identifier, $this->sezione, PDO::PARAM_STR);
                        break;
                    case '"id_indirizzo"':
                        $stmt->bindValue($identifier, $this->id_indirizzo, PDO::PARAM_INT);
                        break;
                    case '"codice_registro"':
                        $stmt->bindValue($identifier, $this->codice_registro, PDO::PARAM_STR);
                        break;
                    case '"ordinamento"':
                        $stmt->bindValue($identifier, $this->ordinamento, PDO::PARAM_STR);
                        break;
                    case '"chi_inserisce"':
                        $stmt->bindValue($identifier, $this->chi_inserisce, PDO::PARAM_STR);
                        break;
                    case '"data_inserimento"':
                        $stmt->bindValue($identifier, $this->data_inserimento, PDO::PARAM_STR);
                        break;
                    case '"tipo_inserimento"':
                        $stmt->bindValue($identifier, $this->tipo_inserimento, PDO::PARAM_STR);
                        break;
                    case '"chi_modifica"':
                        $stmt->bindValue($identifier, $this->chi_modifica, PDO::PARAM_STR);
                        break;
                    case '"data_modifica"':
                        $stmt->bindValue($identifier, $this->data_modifica, PDO::PARAM_STR);
                        break;
                    case '"tipo_modifica"':
                        $stmt->bindValue($identifier, $this->tipo_modifica, PDO::PARAM_STR);
                        break;
                    case '"flag_canc"':
                        $stmt->bindValue($identifier, $this->flag_canc, PDO::PARAM_STR);
                        break;
                    case '"codice_registro_2"':
                        $stmt->bindValue($identifier, $this->codice_registro_2, PDO::PARAM_STR);
                        break;
                    case '"codice_registro_3"':
                        $stmt->bindValue($identifier, $this->codice_registro_3, PDO::PARAM_STR);
                        break;
                    case '"codice_registro_4"':
                        $stmt->bindValue($identifier, $this->codice_registro_4, PDO::PARAM_STR);
                        break;
                    case '"blocco_scrutini"':
                        $stmt->bindValue($identifier, $this->blocco_scrutini, PDO::PARAM_STR);
                        break;
                    case '"consiglio_classe_attivo"':
                        $stmt->bindValue($identifier, $this->consiglio_classe_attivo, PDO::PARAM_STR);
                        break;
                    case '"tempo_funzionamento"':
                        $stmt->bindValue($identifier, $this->tempo_funzionamento, PDO::PARAM_STR);
                        break;
                    case '"pubb_primo_scritto"':
                        $stmt->bindValue($identifier, $this->pubb_primo_scritto, PDO::PARAM_STR);
                        break;
                    case '"pubb_secondo_scritto"':
                        $stmt->bindValue($identifier, $this->pubb_secondo_scritto, PDO::PARAM_STR);
                        break;
                    case '"pubb_terzo_scritto"':
                        $stmt->bindValue($identifier, $this->pubb_terzo_scritto, PDO::PARAM_STR);
                        break;
                    case '"pubb_orale"':
                        $stmt->bindValue($identifier, $this->pubb_orale, PDO::PARAM_STR);
                        break;
                    case '"id_flusso"':
                        $stmt->bindValue($identifier, $this->id_flusso, PDO::PARAM_STR);
                        break;
                    case '"autenticazione_alternativa"':
                        $stmt->bindValue($identifier, $this->autenticazione_alternativa, PDO::PARAM_STR);
                        break;
                    case '"effettua_controllo_gate"':
                        $stmt->bindValue($identifier, $this->effettua_controllo_gate, PDO::PARAM_STR);
                        break;
                    case '"data_aggiornamento_sidi"':
                        $stmt->bindValue($identifier, $this->data_aggiornamento_sidi, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = ClassiPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = ClassiPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getIdClasse();
                break;
            case 1:
                return $this->getClasse();
                break;
            case 2:
                return $this->getSezione();
                break;
            case 3:
                return $this->getIdIndirizzo();
                break;
            case 4:
                return $this->getCodiceRegistro();
                break;
            case 5:
                return $this->getOrdinamento();
                break;
            case 6:
                return $this->getChiInserisce();
                break;
            case 7:
                return $this->getDataInserimento();
                break;
            case 8:
                return $this->getTipoInserimento();
                break;
            case 9:
                return $this->getChiModifica();
                break;
            case 10:
                return $this->getDataModifica();
                break;
            case 11:
                return $this->getTipoModifica();
                break;
            case 12:
                return $this->getFlagCanc();
                break;
            case 13:
                return $this->getCodiceRegistro2();
                break;
            case 14:
                return $this->getCodiceRegistro3();
                break;
            case 15:
                return $this->getCodiceRegistro4();
                break;
            case 16:
                return $this->getBloccoScrutini();
                break;
            case 17:
                return $this->getConsiglioClasseAttivo();
                break;
            case 18:
                return $this->getTempoFunzionamento();
                break;
            case 19:
                return $this->getPubbPrimoScritto();
                break;
            case 20:
                return $this->getPubbSecondoScritto();
                break;
            case 21:
                return $this->getPubbTerzoScritto();
                break;
            case 22:
                return $this->getPubbOrale();
                break;
            case 23:
                return $this->getIdFlusso();
                break;
            case 24:
                return $this->getAutenticazioneAlternativa();
                break;
            case 25:
                return $this->getEffettuaControlloGate();
                break;
            case 26:
                return $this->getDataAggiornamentoSidi();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array())
    {
        if (isset($alreadyDumpedObjects['Classi'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Classi'][$this->getPrimaryKey()] = true;
        $keys = ClassiPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getIdClasse(),
            $keys[1] => $this->getClasse(),
            $keys[2] => $this->getSezione(),
            $keys[3] => $this->getIdIndirizzo(),
            $keys[4] => $this->getCodiceRegistro(),
            $keys[5] => $this->getOrdinamento(),
            $keys[6] => $this->getChiInserisce(),
            $keys[7] => $this->getDataInserimento(),
            $keys[8] => $this->getTipoInserimento(),
            $keys[9] => $this->getChiModifica(),
            $keys[10] => $this->getDataModifica(),
            $keys[11] => $this->getTipoModifica(),
            $keys[12] => $this->getFlagCanc(),
            $keys[13] => $this->getCodiceRegistro2(),
            $keys[14] => $this->getCodiceRegistro3(),
            $keys[15] => $this->getCodiceRegistro4(),
            $keys[16] => $this->getBloccoScrutini(),
            $keys[17] => $this->getConsiglioClasseAttivo(),
            $keys[18] => $this->getTempoFunzionamento(),
            $keys[19] => $this->getPubbPrimoScritto(),
            $keys[20] => $this->getPubbSecondoScritto(),
            $keys[21] => $this->getPubbTerzoScritto(),
            $keys[22] => $this->getPubbOrale(),
            $keys[23] => $this->getIdFlusso(),
            $keys[24] => $this->getAutenticazioneAlternativa(),
            $keys[25] => $this->getEffettuaControlloGate(),
            $keys[26] => $this->getDataAggiornamentoSidi(),
        );

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = ClassiPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setIdClasse($value);
                break;
            case 1:
                $this->setClasse($value);
                break;
            case 2:
                $this->setSezione($value);
                break;
            case 3:
                $this->setIdIndirizzo($value);
                break;
            case 4:
                $this->setCodiceRegistro($value);
                break;
            case 5:
                $this->setOrdinamento($value);
                break;
            case 6:
                $this->setChiInserisce($value);
                break;
            case 7:
                $this->setDataInserimento($value);
                break;
            case 8:
                $this->setTipoInserimento($value);
                break;
            case 9:
                $this->setChiModifica($value);
                break;
            case 10:
                $this->setDataModifica($value);
                break;
            case 11:
                $this->setTipoModifica($value);
                break;
            case 12:
                $this->setFlagCanc($value);
                break;
            case 13:
                $this->setCodiceRegistro2($value);
                break;
            case 14:
                $this->setCodiceRegistro3($value);
                break;
            case 15:
                $this->setCodiceRegistro4($value);
                break;
            case 16:
                $this->setBloccoScrutini($value);
                break;
            case 17:
                $this->setConsiglioClasseAttivo($value);
                break;
            case 18:
                $this->setTempoFunzionamento($value);
                break;
            case 19:
                $this->setPubbPrimoScritto($value);
                break;
            case 20:
                $this->setPubbSecondoScritto($value);
                break;
            case 21:
                $this->setPubbTerzoScritto($value);
                break;
            case 22:
                $this->setPubbOrale($value);
                break;
            case 23:
                $this->setIdFlusso($value);
                break;
            case 24:
                $this->setAutenticazioneAlternativa($value);
                break;
            case 25:
                $this->setEffettuaControlloGate($value);
                break;
            case 26:
                $this->setDataAggiornamentoSidi($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = ClassiPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setIdClasse($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setClasse($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setSezione($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setIdIndirizzo($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setCodiceRegistro($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setOrdinamento($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setChiInserisce($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setDataInserimento($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setTipoInserimento($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setChiModifica($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setDataModifica($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setTipoModifica($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setFlagCanc($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setCodiceRegistro2($arr[$keys[13]]);
        if (array_key_exists($keys[14], $arr)) $this->setCodiceRegistro3($arr[$keys[14]]);
        if (array_key_exists($keys[15], $arr)) $this->setCodiceRegistro4($arr[$keys[15]]);
        if (array_key_exists($keys[16], $arr)) $this->setBloccoScrutini($arr[$keys[16]]);
        if (array_key_exists($keys[17], $arr)) $this->setConsiglioClasseAttivo($arr[$keys[17]]);
        if (array_key_exists($keys[18], $arr)) $this->setTempoFunzionamento($arr[$keys[18]]);
        if (array_key_exists($keys[19], $arr)) $this->setPubbPrimoScritto($arr[$keys[19]]);
        if (array_key_exists($keys[20], $arr)) $this->setPubbSecondoScritto($arr[$keys[20]]);
        if (array_key_exists($keys[21], $arr)) $this->setPubbTerzoScritto($arr[$keys[21]]);
        if (array_key_exists($keys[22], $arr)) $this->setPubbOrale($arr[$keys[22]]);
        if (array_key_exists($keys[23], $arr)) $this->setIdFlusso($arr[$keys[23]]);
        if (array_key_exists($keys[24], $arr)) $this->setAutenticazioneAlternativa($arr[$keys[24]]);
        if (array_key_exists($keys[25], $arr)) $this->setEffettuaControlloGate($arr[$keys[25]]);
        if (array_key_exists($keys[26], $arr)) $this->setDataAggiornamentoSidi($arr[$keys[26]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(ClassiPeer::DATABASE_NAME);

        if ($this->isColumnModified(ClassiPeer::ID_CLASSE)) $criteria->add(ClassiPeer::ID_CLASSE, $this->id_classe);
        if ($this->isColumnModified(ClassiPeer::CLASSE)) $criteria->add(ClassiPeer::CLASSE, $this->classe);
        if ($this->isColumnModified(ClassiPeer::SEZIONE)) $criteria->add(ClassiPeer::SEZIONE, $this->sezione);
        if ($this->isColumnModified(ClassiPeer::ID_INDIRIZZO)) $criteria->add(ClassiPeer::ID_INDIRIZZO, $this->id_indirizzo);
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO)) $criteria->add(ClassiPeer::CODICE_REGISTRO, $this->codice_registro);
        if ($this->isColumnModified(ClassiPeer::ORDINAMENTO)) $criteria->add(ClassiPeer::ORDINAMENTO, $this->ordinamento);
        if ($this->isColumnModified(ClassiPeer::CHI_INSERISCE)) $criteria->add(ClassiPeer::CHI_INSERISCE, $this->chi_inserisce);
        if ($this->isColumnModified(ClassiPeer::DATA_INSERIMENTO)) $criteria->add(ClassiPeer::DATA_INSERIMENTO, $this->data_inserimento);
        if ($this->isColumnModified(ClassiPeer::TIPO_INSERIMENTO)) $criteria->add(ClassiPeer::TIPO_INSERIMENTO, $this->tipo_inserimento);
        if ($this->isColumnModified(ClassiPeer::CHI_MODIFICA)) $criteria->add(ClassiPeer::CHI_MODIFICA, $this->chi_modifica);
        if ($this->isColumnModified(ClassiPeer::DATA_MODIFICA)) $criteria->add(ClassiPeer::DATA_MODIFICA, $this->data_modifica);
        if ($this->isColumnModified(ClassiPeer::TIPO_MODIFICA)) $criteria->add(ClassiPeer::TIPO_MODIFICA, $this->tipo_modifica);
        if ($this->isColumnModified(ClassiPeer::FLAG_CANC)) $criteria->add(ClassiPeer::FLAG_CANC, $this->flag_canc);
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO_2)) $criteria->add(ClassiPeer::CODICE_REGISTRO_2, $this->codice_registro_2);
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO_3)) $criteria->add(ClassiPeer::CODICE_REGISTRO_3, $this->codice_registro_3);
        if ($this->isColumnModified(ClassiPeer::CODICE_REGISTRO_4)) $criteria->add(ClassiPeer::CODICE_REGISTRO_4, $this->codice_registro_4);
        if ($this->isColumnModified(ClassiPeer::BLOCCO_SCRUTINI)) $criteria->add(ClassiPeer::BLOCCO_SCRUTINI, $this->blocco_scrutini);
        if ($this->isColumnModified(ClassiPeer::CONSIGLIO_CLASSE_ATTIVO)) $criteria->add(ClassiPeer::CONSIGLIO_CLASSE_ATTIVO, $this->consiglio_classe_attivo);
        if ($this->isColumnModified(ClassiPeer::TEMPO_FUNZIONAMENTO)) $criteria->add(ClassiPeer::TEMPO_FUNZIONAMENTO, $this->tempo_funzionamento);
        if ($this->isColumnModified(ClassiPeer::PUBB_PRIMO_SCRITTO)) $criteria->add(ClassiPeer::PUBB_PRIMO_SCRITTO, $this->pubb_primo_scritto);
        if ($this->isColumnModified(ClassiPeer::PUBB_SECONDO_SCRITTO)) $criteria->add(ClassiPeer::PUBB_SECONDO_SCRITTO, $this->pubb_secondo_scritto);
        if ($this->isColumnModified(ClassiPeer::PUBB_TERZO_SCRITTO)) $criteria->add(ClassiPeer::PUBB_TERZO_SCRITTO, $this->pubb_terzo_scritto);
        if ($this->isColumnModified(ClassiPeer::PUBB_ORALE)) $criteria->add(ClassiPeer::PUBB_ORALE, $this->pubb_orale);
        if ($this->isColumnModified(ClassiPeer::ID_FLUSSO)) $criteria->add(ClassiPeer::ID_FLUSSO, $this->id_flusso);
        if ($this->isColumnModified(ClassiPeer::AUTENTICAZIONE_ALTERNATIVA)) $criteria->add(ClassiPeer::AUTENTICAZIONE_ALTERNATIVA, $this->autenticazione_alternativa);
        if ($this->isColumnModified(ClassiPeer::EFFETTUA_CONTROLLO_GATE)) $criteria->add(ClassiPeer::EFFETTUA_CONTROLLO_GATE, $this->effettua_controllo_gate);
        if ($this->isColumnModified(ClassiPeer::DATA_AGGIORNAMENTO_SIDI)) $criteria->add(ClassiPeer::DATA_AGGIORNAMENTO_SIDI, $this->data_aggiornamento_sidi);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(ClassiPeer::DATABASE_NAME);
        $criteria->add(ClassiPeer::ID_CLASSE, $this->id_classe);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getIdClasse();
    }

    /**
     * Generic method to set the primary key (id_classe column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setIdClasse($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getIdClasse();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Classi (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setClasse($this->getClasse());
        $copyObj->setSezione($this->getSezione());
        $copyObj->setIdIndirizzo($this->getIdIndirizzo());
        $copyObj->setCodiceRegistro($this->getCodiceRegistro());
        $copyObj->setOrdinamento($this->getOrdinamento());
        $copyObj->setChiInserisce($this->getChiInserisce());
        $copyObj->setDataInserimento($this->getDataInserimento());
        $copyObj->setTipoInserimento($this->getTipoInserimento());
        $copyObj->setChiModifica($this->getChiModifica());
        $copyObj->setDataModifica($this->getDataModifica());
        $copyObj->setTipoModifica($this->getTipoModifica());
        $copyObj->setFlagCanc($this->getFlagCanc());
        $copyObj->setCodiceRegistro2($this->getCodiceRegistro2());
        $copyObj->setCodiceRegistro3($this->getCodiceRegistro3());
        $copyObj->setCodiceRegistro4($this->getCodiceRegistro4());
        $copyObj->setBloccoScrutini($this->getBloccoScrutini());
        $copyObj->setConsiglioClasseAttivo($this->getConsiglioClasseAttivo());
        $copyObj->setTempoFunzionamento($this->getTempoFunzionamento());
        $copyObj->setPubbPrimoScritto($this->getPubbPrimoScritto());
        $copyObj->setPubbSecondoScritto($this->getPubbSecondoScritto());
        $copyObj->setPubbTerzoScritto($this->getPubbTerzoScritto());
        $copyObj->setPubbOrale($this->getPubbOrale());
        $copyObj->setIdFlusso($this->getIdFlusso());
        $copyObj->setAutenticazioneAlternativa($this->getAutenticazioneAlternativa());
        $copyObj->setEffettuaControlloGate($this->getEffettuaControlloGate());
        $copyObj->setDataAggiornamentoSidi($this->getDataAggiornamentoSidi());
        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setIdClasse(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Classi Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return ClassiPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new ClassiPeer();
        }

        return self::$peer;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id_classe = null;
        $this->classe = null;
        $this->sezione = null;
        $this->id_indirizzo = null;
        $this->codice_registro = null;
        $this->ordinamento = null;
        $this->chi_inserisce = null;
        $this->data_inserimento = null;
        $this->tipo_inserimento = null;
        $this->chi_modifica = null;
        $this->data_modifica = null;
        $this->tipo_modifica = null;
        $this->flag_canc = null;
        $this->codice_registro_2 = null;
        $this->codice_registro_3 = null;
        $this->codice_registro_4 = null;
        $this->blocco_scrutini = null;
        $this->consiglio_classe_attivo = null;
        $this->tempo_funzionamento = null;
        $this->pubb_primo_scritto = null;
        $this->pubb_secondo_scritto = null;
        $this->pubb_terzo_scritto = null;
        $this->pubb_orale = null;
        $this->id_flusso = null;
        $this->autenticazione_alternativa = null;
        $this->effettua_controllo_gate = null;
        $this->data_aggiornamento_sidi = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(ClassiPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
