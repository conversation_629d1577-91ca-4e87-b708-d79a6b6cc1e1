<?php

namespace Ccp\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\Tasse;
use Ccp\TasseQuery;
use Ccp\TipiTasse;
use Ccp\TipiTassePeer;
use Ccp\TipiTasseQuery;

/**
 * Base class that represents a row from the 'tipi_tasse' table.
 *
 *
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseTipiTasse extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Ccp\\TipiTassePeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        TipiTassePeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id_tipo_tassa field.
     * @var        int
     */
    protected $id_tipo_tassa;

    /**
     * The value for the descrizione field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $descrizione;

    /**
     * The value for the tipologia field.
     * Note: this column has a database default value of: 'DIVERSI'
     * @var        string
     */
    protected $tipologia;

    /**
     * The value for the tassa_governativa field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $tassa_governativa;

    /**
     * The value for the data_scadenza field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $data_scadenza;

    /**
     * The value for the cumulativa field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $cumulativa;

    /**
     * The value for the anno_scolastico_riferimento field.
     * Note: this column has a database default value of: 'TUTTI'
     * @var        string
     */
    protected $anno_scolastico_riferimento;

    /**
     * The value for the importo_base field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $importo_base;

    /**
     * @var        PropelObjectCollection|Tasse[] Collection to store aggregation of Tasse objects.
     */
    protected $collTasses;
    protected $collTassesPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $tassesScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->descrizione = '';
        $this->tipologia = 'DIVERSI';
        $this->tassa_governativa = false;
        $this->data_scadenza = '0';
        $this->cumulativa = 0;
        $this->anno_scolastico_riferimento = 'TUTTI';
        $this->importo_base = 0;
    }

    /**
     * Initializes internal state of BaseTipiTasse object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id_tipo_tassa] column value.
     *
     * @return int
     */
    public function getIdTipoTassa()
    {

        return $this->id_tipo_tassa;
    }

    /**
     * Get the [descrizione] column value.
     *
     * @return string
     */
    public function getDescrizione()
    {

        return $this->descrizione;
    }

    /**
     * Get the [tipologia] column value.
     *
     * @return string
     */
    public function getTipologia()
    {

        return $this->tipologia;
    }

    /**
     * Get the [tassa_governativa] column value.
     *
     * @return boolean
     */
    public function getTassaGovernativa()
    {

        return $this->tassa_governativa;
    }

    /**
     * Get the [data_scadenza] column value.
     *
     * @return string
     */
    public function getDataScadenza()
    {

        return $this->data_scadenza;
    }

    /**
     * Get the [cumulativa] column value.
     *
     * @return int
     */
    public function getCumulativa()
    {

        return $this->cumulativa;
    }

    /**
     * Get the [anno_scolastico_riferimento] column value.
     *
     * @return string
     */
    public function getAnnoScolasticoRiferimento()
    {

        return $this->anno_scolastico_riferimento;
    }

    /**
     * Get the [importo_base] column value.
     *
     * @return double
     */
    public function getImportoBase()
    {

        return $this->importo_base;
    }

    /**
     * Set the value of [id_tipo_tassa] column.
     *
     * @param  int $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setIdTipoTassa($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id_tipo_tassa !== $v) {
            $this->id_tipo_tassa = $v;
            $this->modifiedColumns[] = TipiTassePeer::ID_TIPO_TASSA;
        }


        return $this;
    } // setIdTipoTassa()

    /**
     * Set the value of [descrizione] column.
     *
     * @param  string $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setDescrizione($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->descrizione !== $v) {
            $this->descrizione = $v;
            $this->modifiedColumns[] = TipiTassePeer::DESCRIZIONE;
        }


        return $this;
    } // setDescrizione()

    /**
     * Set the value of [tipologia] column.
     *
     * @param  string $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setTipologia($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->tipologia !== $v) {
            $this->tipologia = $v;
            $this->modifiedColumns[] = TipiTassePeer::TIPOLOGIA;
        }


        return $this;
    } // setTipologia()

    /**
     * Sets the value of the [tassa_governativa] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setTassaGovernativa($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->tassa_governativa !== $v) {
            $this->tassa_governativa = $v;
            $this->modifiedColumns[] = TipiTassePeer::TASSA_GOVERNATIVA;
        }


        return $this;
    } // setTassaGovernativa()

    /**
     * Set the value of [data_scadenza] column.
     *
     * @param  string $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setDataScadenza($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->data_scadenza !== $v) {
            $this->data_scadenza = $v;
            $this->modifiedColumns[] = TipiTassePeer::DATA_SCADENZA;
        }


        return $this;
    } // setDataScadenza()

    /**
     * Set the value of [cumulativa] column.
     *
     * @param  int $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setCumulativa($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->cumulativa !== $v) {
            $this->cumulativa = $v;
            $this->modifiedColumns[] = TipiTassePeer::CUMULATIVA;
        }


        return $this;
    } // setCumulativa()

    /**
     * Set the value of [anno_scolastico_riferimento] column.
     *
     * @param  string $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setAnnoScolasticoRiferimento($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->anno_scolastico_riferimento !== $v) {
            $this->anno_scolastico_riferimento = $v;
            $this->modifiedColumns[] = TipiTassePeer::ANNO_SCOLASTICO_RIFERIMENTO;
        }


        return $this;
    } // setAnnoScolasticoRiferimento()

    /**
     * Set the value of [importo_base] column.
     *
     * @param  double $v new value
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setImportoBase($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->importo_base !== $v) {
            $this->importo_base = $v;
            $this->modifiedColumns[] = TipiTassePeer::IMPORTO_BASE;
        }


        return $this;
    } // setImportoBase()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->descrizione !== '') {
                return false;
            }

            if ($this->tipologia !== 'DIVERSI') {
                return false;
            }

            if ($this->tassa_governativa !== false) {
                return false;
            }

            if ($this->data_scadenza !== '0') {
                return false;
            }

            if ($this->cumulativa !== 0) {
                return false;
            }

            if ($this->anno_scolastico_riferimento !== 'TUTTI') {
                return false;
            }

            if ($this->importo_base !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id_tipo_tassa = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->descrizione = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->tipologia = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->tassa_governativa = ($row[$startcol + 3] !== null) ? (boolean) $row[$startcol + 3] : null;
            $this->data_scadenza = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->cumulativa = ($row[$startcol + 5] !== null) ? (int) $row[$startcol + 5] : null;
            $this->anno_scolastico_riferimento = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->importo_base = ($row[$startcol + 7] !== null) ? (double) $row[$startcol + 7] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 8; // 8 = TipiTassePeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating TipiTasse object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TipiTassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = TipiTassePeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->collTasses = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TipiTassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = TipiTasseQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TipiTassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                TipiTassePeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->tassesScheduledForDeletion !== null) {
                if (!$this->tassesScheduledForDeletion->isEmpty()) {
                    TasseQuery::create()
                        ->filterByPrimaryKeys($this->tassesScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->tassesScheduledForDeletion = null;
                }
            }

            if ($this->collTasses !== null) {
                foreach ($this->collTasses as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = TipiTassePeer::ID_TIPO_TASSA;
        if (null !== $this->id_tipo_tassa) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . TipiTassePeer::ID_TIPO_TASSA . ')');
        }
        if (null === $this->id_tipo_tassa) {
            try {
                $stmt = $con->query("SELECT nextval('tipi_tasse_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id_tipo_tassa = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(TipiTassePeer::ID_TIPO_TASSA)) {
            $modifiedColumns[':p' . $index++]  = '"id_tipo_tassa"';
        }
        if ($this->isColumnModified(TipiTassePeer::DESCRIZIONE)) {
            $modifiedColumns[':p' . $index++]  = '"descrizione"';
        }
        if ($this->isColumnModified(TipiTassePeer::TIPOLOGIA)) {
            $modifiedColumns[':p' . $index++]  = '"tipologia"';
        }
        if ($this->isColumnModified(TipiTassePeer::TASSA_GOVERNATIVA)) {
            $modifiedColumns[':p' . $index++]  = '"tassa_governativa"';
        }
        if ($this->isColumnModified(TipiTassePeer::DATA_SCADENZA)) {
            $modifiedColumns[':p' . $index++]  = '"data_scadenza"';
        }
        if ($this->isColumnModified(TipiTassePeer::CUMULATIVA)) {
            $modifiedColumns[':p' . $index++]  = '"cumulativa"';
        }
        if ($this->isColumnModified(TipiTassePeer::ANNO_SCOLASTICO_RIFERIMENTO)) {
            $modifiedColumns[':p' . $index++]  = '"anno_scolastico_riferimento"';
        }
        if ($this->isColumnModified(TipiTassePeer::IMPORTO_BASE)) {
            $modifiedColumns[':p' . $index++]  = '"importo_base"';
        }

        $sql = sprintf(
            'INSERT INTO "tipi_tasse" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id_tipo_tassa"':
                        $stmt->bindValue($identifier, $this->id_tipo_tassa, PDO::PARAM_INT);
                        break;
                    case '"descrizione"':
                        $stmt->bindValue($identifier, $this->descrizione, PDO::PARAM_STR);
                        break;
                    case '"tipologia"':
                        $stmt->bindValue($identifier, $this->tipologia, PDO::PARAM_STR);
                        break;
                    case '"tassa_governativa"':
                        $stmt->bindValue($identifier, $this->tassa_governativa, PDO::PARAM_BOOL);
                        break;
                    case '"data_scadenza"':
                        $stmt->bindValue($identifier, $this->data_scadenza, PDO::PARAM_STR);
                        break;
                    case '"cumulativa"':
                        $stmt->bindValue($identifier, $this->cumulativa, PDO::PARAM_INT);
                        break;
                    case '"anno_scolastico_riferimento"':
                        $stmt->bindValue($identifier, $this->anno_scolastico_riferimento, PDO::PARAM_STR);
                        break;
                    case '"importo_base"':
                        $stmt->bindValue($identifier, $this->importo_base, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = TipiTassePeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collTasses !== null) {
                    foreach ($this->collTasses as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = TipiTassePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getIdTipoTassa();
                break;
            case 1:
                return $this->getDescrizione();
                break;
            case 2:
                return $this->getTipologia();
                break;
            case 3:
                return $this->getTassaGovernativa();
                break;
            case 4:
                return $this->getDataScadenza();
                break;
            case 5:
                return $this->getCumulativa();
                break;
            case 6:
                return $this->getAnnoScolasticoRiferimento();
                break;
            case 7:
                return $this->getImportoBase();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['TipiTasse'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['TipiTasse'][$this->getPrimaryKey()] = true;
        $keys = TipiTassePeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getIdTipoTassa(),
            $keys[1] => $this->getDescrizione(),
            $keys[2] => $this->getTipologia(),
            $keys[3] => $this->getTassaGovernativa(),
            $keys[4] => $this->getDataScadenza(),
            $keys[5] => $this->getCumulativa(),
            $keys[6] => $this->getAnnoScolasticoRiferimento(),
            $keys[7] => $this->getImportoBase(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->collTasses) {
                $result['Tasses'] = $this->collTasses->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = TipiTassePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setIdTipoTassa($value);
                break;
            case 1:
                $this->setDescrizione($value);
                break;
            case 2:
                $this->setTipologia($value);
                break;
            case 3:
                $this->setTassaGovernativa($value);
                break;
            case 4:
                $this->setDataScadenza($value);
                break;
            case 5:
                $this->setCumulativa($value);
                break;
            case 6:
                $this->setAnnoScolasticoRiferimento($value);
                break;
            case 7:
                $this->setImportoBase($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = TipiTassePeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setIdTipoTassa($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setDescrizione($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setTipologia($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setTassaGovernativa($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setDataScadenza($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setCumulativa($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setAnnoScolasticoRiferimento($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setImportoBase($arr[$keys[7]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(TipiTassePeer::DATABASE_NAME);

        if ($this->isColumnModified(TipiTassePeer::ID_TIPO_TASSA)) $criteria->add(TipiTassePeer::ID_TIPO_TASSA, $this->id_tipo_tassa);
        if ($this->isColumnModified(TipiTassePeer::DESCRIZIONE)) $criteria->add(TipiTassePeer::DESCRIZIONE, $this->descrizione);
        if ($this->isColumnModified(TipiTassePeer::TIPOLOGIA)) $criteria->add(TipiTassePeer::TIPOLOGIA, $this->tipologia);
        if ($this->isColumnModified(TipiTassePeer::TASSA_GOVERNATIVA)) $criteria->add(TipiTassePeer::TASSA_GOVERNATIVA, $this->tassa_governativa);
        if ($this->isColumnModified(TipiTassePeer::DATA_SCADENZA)) $criteria->add(TipiTassePeer::DATA_SCADENZA, $this->data_scadenza);
        if ($this->isColumnModified(TipiTassePeer::CUMULATIVA)) $criteria->add(TipiTassePeer::CUMULATIVA, $this->cumulativa);
        if ($this->isColumnModified(TipiTassePeer::ANNO_SCOLASTICO_RIFERIMENTO)) $criteria->add(TipiTassePeer::ANNO_SCOLASTICO_RIFERIMENTO, $this->anno_scolastico_riferimento);
        if ($this->isColumnModified(TipiTassePeer::IMPORTO_BASE)) $criteria->add(TipiTassePeer::IMPORTO_BASE, $this->importo_base);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(TipiTassePeer::DATABASE_NAME);
        $criteria->add(TipiTassePeer::ID_TIPO_TASSA, $this->id_tipo_tassa);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getIdTipoTassa();
    }

    /**
     * Generic method to set the primary key (id_tipo_tassa column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setIdTipoTassa($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getIdTipoTassa();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of TipiTasse (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setDescrizione($this->getDescrizione());
        $copyObj->setTipologia($this->getTipologia());
        $copyObj->setTassaGovernativa($this->getTassaGovernativa());
        $copyObj->setDataScadenza($this->getDataScadenza());
        $copyObj->setCumulativa($this->getCumulativa());
        $copyObj->setAnnoScolasticoRiferimento($this->getAnnoScolasticoRiferimento());
        $copyObj->setImportoBase($this->getImportoBase());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getTasses() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addTasse($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setIdTipoTassa(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return TipiTasse Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return TipiTassePeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new TipiTassePeer();
        }

        return self::$peer;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Tasse' == $relationName) {
            $this->initTasses();
        }
    }

    /**
     * Clears out the collTasses collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return TipiTasse The current object (for fluent API support)
     * @see        addTasses()
     */
    public function clearTasses()
    {
        $this->collTasses = null; // important to set this to null since that means it is uninitialized
        $this->collTassesPartial = null;

        return $this;
    }

    /**
     * reset is the collTasses collection loaded partially
     *
     * @return void
     */
    public function resetPartialTasses($v = true)
    {
        $this->collTassesPartial = $v;
    }

    /**
     * Initializes the collTasses collection.
     *
     * By default this just sets the collTasses collection to an empty array (like clearcollTasses());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initTasses($overrideExisting = true)
    {
        if (null !== $this->collTasses && !$overrideExisting) {
            return;
        }
        $this->collTasses = new PropelObjectCollection();
        $this->collTasses->setModel('Tasse');
    }

    /**
     * Gets an array of Tasse objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this TipiTasse is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Tasse[] List of Tasse objects
     * @throws PropelException
     */
    public function getTasses($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collTassesPartial && !$this->isNew();
        if (null === $this->collTasses || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collTasses) {
                // return empty collection
                $this->initTasses();
            } else {
                $collTasses = TasseQuery::create(null, $criteria)
                    ->filterByTipiTasse($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collTassesPartial && count($collTasses)) {
                      $this->initTasses(false);

                      foreach ($collTasses as $obj) {
                        if (false == $this->collTasses->contains($obj)) {
                          $this->collTasses->append($obj);
                        }
                      }

                      $this->collTassesPartial = true;
                    }

                    $collTasses->getInternalIterator()->rewind();

                    return $collTasses;
                }

                if ($partial && $this->collTasses) {
                    foreach ($this->collTasses as $obj) {
                        if ($obj->isNew()) {
                            $collTasses[] = $obj;
                        }
                    }
                }

                $this->collTasses = $collTasses;
                $this->collTassesPartial = false;
            }
        }

        return $this->collTasses;
    }

    /**
     * Sets a collection of Tasse objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $tasses A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return TipiTasse The current object (for fluent API support)
     */
    public function setTasses(PropelCollection $tasses, PropelPDO $con = null)
    {
        $tassesToDelete = $this->getTasses(new Criteria(), $con)->diff($tasses);


        $this->tassesScheduledForDeletion = $tassesToDelete;

        foreach ($tassesToDelete as $tasseRemoved) {
            $tasseRemoved->setTipiTasse(null);
        }

        $this->collTasses = null;
        foreach ($tasses as $tasse) {
            $this->addTasse($tasse);
        }

        $this->collTasses = $tasses;
        $this->collTassesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Tasse objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Tasse objects.
     * @throws PropelException
     */
    public function countTasses(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collTassesPartial && !$this->isNew();
        if (null === $this->collTasses || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collTasses) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getTasses());
            }
            $query = TasseQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByTipiTasse($this)
                ->count($con);
        }

        return count($this->collTasses);
    }

    /**
     * Method called to associate a Tasse object to this object
     * through the Tasse foreign key attribute.
     *
     * @param    Tasse $l Tasse
     * @return TipiTasse The current object (for fluent API support)
     */
    public function addTasse(Tasse $l)
    {
        if ($this->collTasses === null) {
            $this->initTasses();
            $this->collTassesPartial = true;
        }

        if (!in_array($l, $this->collTasses->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddTasse($l);

            if ($this->tassesScheduledForDeletion and $this->tassesScheduledForDeletion->contains($l)) {
                $this->tassesScheduledForDeletion->remove($this->tassesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Tasse $tasse The tasse object to add.
     */
    protected function doAddTasse($tasse)
    {
        $this->collTasses[]= $tasse;
        $tasse->setTipiTasse($this);
    }

    /**
     * @param	Tasse $tasse The tasse object to remove.
     * @return TipiTasse The current object (for fluent API support)
     */
    public function removeTasse($tasse)
    {
        if ($this->getTasses()->contains($tasse)) {
            $this->collTasses->remove($this->collTasses->search($tasse));
            if (null === $this->tassesScheduledForDeletion) {
                $this->tassesScheduledForDeletion = clone $this->collTasses;
                $this->tassesScheduledForDeletion->clear();
            }
            $this->tassesScheduledForDeletion[]= clone $tasse;
            $tasse->setTipiTasse(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this TipiTasse is new, it will return
     * an empty collection; or if this TipiTasse has previously
     * been saved, it will retrieve related Tasses from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in TipiTasse.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Tasse[] List of Tasse objects
     */
    public function getTassesJoinStudentiCompleti($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = TasseQuery::create(null, $criteria);
        $query->joinWith('StudentiCompleti', $join_behavior);

        return $this->getTasses($query, $con);
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this TipiTasse is new, it will return
     * an empty collection; or if this TipiTasse has previously
     * been saved, it will retrieve related Tasses from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in TipiTasse.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Tasse[] List of Tasse objects
     */
    public function getTassesJoinStudenti($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = TasseQuery::create(null, $criteria);
        $query->joinWith('Studenti', $join_behavior);

        return $this->getTasses($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id_tipo_tassa = null;
        $this->descrizione = null;
        $this->tipologia = null;
        $this->tassa_governativa = null;
        $this->data_scadenza = null;
        $this->cumulativa = null;
        $this->anno_scolastico_riferimento = null;
        $this->importo_base = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collTasses) {
                foreach ($this->collTasses as $o) {
                    $o->clearAllReferences($deep);
                }
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collTasses instanceof PropelCollection) {
            $this->collTasses->clearIterator();
        }
        $this->collTasses = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(TipiTassePeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
