<?php

namespace Ccp\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\Studenti;
use Ccp\StudentiCompleti;
use Ccp\Tasse;
use Ccp\TassePeer;
use Ccp\TasseQuery;
use Ccp\TipiTasse;

/**
 * Base class that represents a query for the 'tasse' table.
 *
 *
 *
 * @method TasseQuery orderByIdTasse($order = Criteria::ASC) Order by the id_tasse column
 * @method TasseQuery orderByIdTipoTassa($order = Criteria::ASC) Order by the id_tipo_tassa column
 * @method TasseQuery orderByIdStudente($order = Criteria::ASC) Order by the id_studente column
 * @method TasseQuery orderBySedeUfficioPostale($order = Criteria::ASC) Order by the sede_ufficio_postale column
 * @method TasseQuery orderByNumeroVersamento($order = Criteria::ASC) Order by the numero_versamento column
 * @method TasseQuery orderByDataVersamento($order = Criteria::ASC) Order by the data_versamento column
 * @method TasseQuery orderByNote($order = Criteria::ASC) Order by the note column
 * @method TasseQuery orderByAnnoScolastico($order = Criteria::ASC) Order by the anno_scolastico column
 * @method TasseQuery orderByRiferimentoEstrattoConto($order = Criteria::ASC) Order by the riferimento_estratto_conto column
 * @method TasseQuery orderByDataEstrattoConto($order = Criteria::ASC) Order by the data_estratto_conto column
 * @method TasseQuery orderByDatiDebitore($order = Criteria::ASC) Order by the dati_debitore column
 * @method TasseQuery orderByNumeroBollettino($order = Criteria::ASC) Order by the numero_bollettino column
 * @method TasseQuery orderByIsIncoming($order = Criteria::ASC) Order by the is_incoming column
 * @method TasseQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method TasseQuery orderByTipologiaUscita($order = Criteria::ASC) Order by the tipologia_uscita column
 * @method TasseQuery orderByDestinazionePagamento($order = Criteria::ASC) Order by the destinazione_pagamento column
 * @method TasseQuery orderByModalitaPagamento($order = Criteria::ASC) Order by the modalita_pagamento column
 * @method TasseQuery orderBySedeStudente($order = Criteria::ASC) Order by the sede_studente column
 * @method TasseQuery orderByClasse($order = Criteria::ASC) Order by the classe column
 * @method TasseQuery orderByImportoVersato($order = Criteria::ASC) Order by the importo_versato column
 *
 * @method TasseQuery groupByIdTasse() Group by the id_tasse column
 * @method TasseQuery groupByIdTipoTassa() Group by the id_tipo_tassa column
 * @method TasseQuery groupByIdStudente() Group by the id_studente column
 * @method TasseQuery groupBySedeUfficioPostale() Group by the sede_ufficio_postale column
 * @method TasseQuery groupByNumeroVersamento() Group by the numero_versamento column
 * @method TasseQuery groupByDataVersamento() Group by the data_versamento column
 * @method TasseQuery groupByNote() Group by the note column
 * @method TasseQuery groupByAnnoScolastico() Group by the anno_scolastico column
 * @method TasseQuery groupByRiferimentoEstrattoConto() Group by the riferimento_estratto_conto column
 * @method TasseQuery groupByDataEstrattoConto() Group by the data_estratto_conto column
 * @method TasseQuery groupByDatiDebitore() Group by the dati_debitore column
 * @method TasseQuery groupByNumeroBollettino() Group by the numero_bollettino column
 * @method TasseQuery groupByIsIncoming() Group by the is_incoming column
 * @method TasseQuery groupByEmployeeId() Group by the employee_id column
 * @method TasseQuery groupByTipologiaUscita() Group by the tipologia_uscita column
 * @method TasseQuery groupByDestinazionePagamento() Group by the destinazione_pagamento column
 * @method TasseQuery groupByModalitaPagamento() Group by the modalita_pagamento column
 * @method TasseQuery groupBySedeStudente() Group by the sede_studente column
 * @method TasseQuery groupByClasse() Group by the classe column
 * @method TasseQuery groupByImportoVersato() Group by the importo_versato column
 *
 * @method TasseQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method TasseQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method TasseQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method TasseQuery leftJoinTipiTasse($relationAlias = null) Adds a LEFT JOIN clause to the query using the TipiTasse relation
 * @method TasseQuery rightJoinTipiTasse($relationAlias = null) Adds a RIGHT JOIN clause to the query using the TipiTasse relation
 * @method TasseQuery innerJoinTipiTasse($relationAlias = null) Adds a INNER JOIN clause to the query using the TipiTasse relation
 *
 * @method TasseQuery leftJoinStudentiCompleti($relationAlias = null) Adds a LEFT JOIN clause to the query using the StudentiCompleti relation
 * @method TasseQuery rightJoinStudentiCompleti($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StudentiCompleti relation
 * @method TasseQuery innerJoinStudentiCompleti($relationAlias = null) Adds a INNER JOIN clause to the query using the StudentiCompleti relation
 *
 * @method TasseQuery leftJoinStudenti($relationAlias = null) Adds a LEFT JOIN clause to the query using the Studenti relation
 * @method TasseQuery rightJoinStudenti($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Studenti relation
 * @method TasseQuery innerJoinStudenti($relationAlias = null) Adds a INNER JOIN clause to the query using the Studenti relation
 *
 * @method Tasse findOne(PropelPDO $con = null) Return the first Tasse matching the query
 * @method Tasse findOneOrCreate(PropelPDO $con = null) Return the first Tasse matching the query, or a new Tasse object populated from the query conditions when no match is found
 *
 * @method Tasse findOneByIdTipoTassa(int $id_tipo_tassa) Return the first Tasse filtered by the id_tipo_tassa column
 * @method Tasse findOneByIdStudente(int $id_studente) Return the first Tasse filtered by the id_studente column
 * @method Tasse findOneBySedeUfficioPostale(string $sede_ufficio_postale) Return the first Tasse filtered by the sede_ufficio_postale column
 * @method Tasse findOneByNumeroVersamento(string $numero_versamento) Return the first Tasse filtered by the numero_versamento column
 * @method Tasse findOneByDataVersamento(string $data_versamento) Return the first Tasse filtered by the data_versamento column
 * @method Tasse findOneByNote(string $note) Return the first Tasse filtered by the note column
 * @method Tasse findOneByAnnoScolastico(string $anno_scolastico) Return the first Tasse filtered by the anno_scolastico column
 * @method Tasse findOneByRiferimentoEstrattoConto(string $riferimento_estratto_conto) Return the first Tasse filtered by the riferimento_estratto_conto column
 * @method Tasse findOneByDataEstrattoConto(string $data_estratto_conto) Return the first Tasse filtered by the data_estratto_conto column
 * @method Tasse findOneByDatiDebitore(string $dati_debitore) Return the first Tasse filtered by the dati_debitore column
 * @method Tasse findOneByNumeroBollettino(string $numero_bollettino) Return the first Tasse filtered by the numero_bollettino column
 * @method Tasse findOneByIsIncoming(boolean $is_incoming) Return the first Tasse filtered by the is_incoming column
 * @method Tasse findOneByEmployeeId(int $employee_id) Return the first Tasse filtered by the employee_id column
 * @method Tasse findOneByTipologiaUscita(string $tipologia_uscita) Return the first Tasse filtered by the tipologia_uscita column
 * @method Tasse findOneByDestinazionePagamento(string $destinazione_pagamento) Return the first Tasse filtered by the destinazione_pagamento column
 * @method Tasse findOneByModalitaPagamento(string $modalita_pagamento) Return the first Tasse filtered by the modalita_pagamento column
 * @method Tasse findOneBySedeStudente(int $sede_studente) Return the first Tasse filtered by the sede_studente column
 * @method Tasse findOneByClasse(string $classe) Return the first Tasse filtered by the classe column
 * @method Tasse findOneByImportoVersato(double $importo_versato) Return the first Tasse filtered by the importo_versato column
 *
 * @method array findByIdTasse(int $id_tasse) Return Tasse objects filtered by the id_tasse column
 * @method array findByIdTipoTassa(int $id_tipo_tassa) Return Tasse objects filtered by the id_tipo_tassa column
 * @method array findByIdStudente(int $id_studente) Return Tasse objects filtered by the id_studente column
 * @method array findBySedeUfficioPostale(string $sede_ufficio_postale) Return Tasse objects filtered by the sede_ufficio_postale column
 * @method array findByNumeroVersamento(string $numero_versamento) Return Tasse objects filtered by the numero_versamento column
 * @method array findByDataVersamento(string $data_versamento) Return Tasse objects filtered by the data_versamento column
 * @method array findByNote(string $note) Return Tasse objects filtered by the note column
 * @method array findByAnnoScolastico(string $anno_scolastico) Return Tasse objects filtered by the anno_scolastico column
 * @method array findByRiferimentoEstrattoConto(string $riferimento_estratto_conto) Return Tasse objects filtered by the riferimento_estratto_conto column
 * @method array findByDataEstrattoConto(string $data_estratto_conto) Return Tasse objects filtered by the data_estratto_conto column
 * @method array findByDatiDebitore(string $dati_debitore) Return Tasse objects filtered by the dati_debitore column
 * @method array findByNumeroBollettino(string $numero_bollettino) Return Tasse objects filtered by the numero_bollettino column
 * @method array findByIsIncoming(boolean $is_incoming) Return Tasse objects filtered by the is_incoming column
 * @method array findByEmployeeId(int $employee_id) Return Tasse objects filtered by the employee_id column
 * @method array findByTipologiaUscita(string $tipologia_uscita) Return Tasse objects filtered by the tipologia_uscita column
 * @method array findByDestinazionePagamento(string $destinazione_pagamento) Return Tasse objects filtered by the destinazione_pagamento column
 * @method array findByModalitaPagamento(string $modalita_pagamento) Return Tasse objects filtered by the modalita_pagamento column
 * @method array findBySedeStudente(int $sede_studente) Return Tasse objects filtered by the sede_studente column
 * @method array findByClasse(string $classe) Return Tasse objects filtered by the classe column
 * @method array findByImportoVersato(double $importo_versato) Return Tasse objects filtered by the importo_versato column
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseTasseQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseTasseQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Ccp\\Tasse';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new TasseQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   TasseQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return TasseQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof TasseQuery) {
            return $criteria;
        }
        $query = new TasseQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Tasse|Tasse[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = TassePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Tasse A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByIdTasse($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Tasse A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id_tasse", "id_tipo_tassa", "id_studente", "sede_ufficio_postale", "numero_versamento", "data_versamento", "note", "anno_scolastico", "riferimento_estratto_conto", "data_estratto_conto", "dati_debitore", "numero_bollettino", "is_incoming", "employee_id", "tipologia_uscita", "destinazione_pagamento", "modalita_pagamento", "sede_studente", "classe", "importo_versato" FROM "tasse" WHERE "id_tasse" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Tasse();
            $obj->hydrate($row);
            TassePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Tasse|Tasse[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Tasse[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(TassePeer::ID_TASSE, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(TassePeer::ID_TASSE, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id_tasse column
     *
     * Example usage:
     * <code>
     * $query->filterByIdTasse(1234); // WHERE id_tasse = 1234
     * $query->filterByIdTasse(array(12, 34)); // WHERE id_tasse IN (12, 34)
     * $query->filterByIdTasse(array('min' => 12)); // WHERE id_tasse >= 12
     * $query->filterByIdTasse(array('max' => 12)); // WHERE id_tasse <= 12
     * </code>
     *
     * @param     mixed $idTasse The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByIdTasse($idTasse = null, $comparison = null)
    {
        if (is_array($idTasse)) {
            $useMinMax = false;
            if (isset($idTasse['min'])) {
                $this->addUsingAlias(TassePeer::ID_TASSE, $idTasse['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idTasse['max'])) {
                $this->addUsingAlias(TassePeer::ID_TASSE, $idTasse['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::ID_TASSE, $idTasse, $comparison);
    }

    /**
     * Filter the query on the id_tipo_tassa column
     *
     * Example usage:
     * <code>
     * $query->filterByIdTipoTassa(1234); // WHERE id_tipo_tassa = 1234
     * $query->filterByIdTipoTassa(array(12, 34)); // WHERE id_tipo_tassa IN (12, 34)
     * $query->filterByIdTipoTassa(array('min' => 12)); // WHERE id_tipo_tassa >= 12
     * $query->filterByIdTipoTassa(array('max' => 12)); // WHERE id_tipo_tassa <= 12
     * </code>
     *
     * @see       filterByTipiTasse()
     *
     * @param     mixed $idTipoTassa The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByIdTipoTassa($idTipoTassa = null, $comparison = null)
    {
        if (is_array($idTipoTassa)) {
            $useMinMax = false;
            if (isset($idTipoTassa['min'])) {
                $this->addUsingAlias(TassePeer::ID_TIPO_TASSA, $idTipoTassa['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idTipoTassa['max'])) {
                $this->addUsingAlias(TassePeer::ID_TIPO_TASSA, $idTipoTassa['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::ID_TIPO_TASSA, $idTipoTassa, $comparison);
    }

    /**
     * Filter the query on the id_studente column
     *
     * Example usage:
     * <code>
     * $query->filterByIdStudente(1234); // WHERE id_studente = 1234
     * $query->filterByIdStudente(array(12, 34)); // WHERE id_studente IN (12, 34)
     * $query->filterByIdStudente(array('min' => 12)); // WHERE id_studente >= 12
     * $query->filterByIdStudente(array('max' => 12)); // WHERE id_studente <= 12
     * </code>
     *
     * @see       filterByStudentiCompleti()
     *
     * @see       filterByStudenti()
     *
     * @param     mixed $idStudente The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByIdStudente($idStudente = null, $comparison = null)
    {
        if (is_array($idStudente)) {
            $useMinMax = false;
            if (isset($idStudente['min'])) {
                $this->addUsingAlias(TassePeer::ID_STUDENTE, $idStudente['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idStudente['max'])) {
                $this->addUsingAlias(TassePeer::ID_STUDENTE, $idStudente['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::ID_STUDENTE, $idStudente, $comparison);
    }

    /**
     * Filter the query on the sede_ufficio_postale column
     *
     * Example usage:
     * <code>
     * $query->filterBySedeUfficioPostale('fooValue');   // WHERE sede_ufficio_postale = 'fooValue'
     * $query->filterBySedeUfficioPostale('%fooValue%'); // WHERE sede_ufficio_postale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $sedeUfficioPostale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterBySedeUfficioPostale($sedeUfficioPostale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($sedeUfficioPostale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $sedeUfficioPostale)) {
                $sedeUfficioPostale = str_replace('*', '%', $sedeUfficioPostale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::SEDE_UFFICIO_POSTALE, $sedeUfficioPostale, $comparison);
    }

    /**
     * Filter the query on the numero_versamento column
     *
     * Example usage:
     * <code>
     * $query->filterByNumeroVersamento('fooValue');   // WHERE numero_versamento = 'fooValue'
     * $query->filterByNumeroVersamento('%fooValue%'); // WHERE numero_versamento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $numeroVersamento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByNumeroVersamento($numeroVersamento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($numeroVersamento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $numeroVersamento)) {
                $numeroVersamento = str_replace('*', '%', $numeroVersamento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::NUMERO_VERSAMENTO, $numeroVersamento, $comparison);
    }

    /**
     * Filter the query on the data_versamento column
     *
     * Example usage:
     * <code>
     * $query->filterByDataVersamento(1234); // WHERE data_versamento = 1234
     * $query->filterByDataVersamento(array(12, 34)); // WHERE data_versamento IN (12, 34)
     * $query->filterByDataVersamento(array('min' => 12)); // WHERE data_versamento >= 12
     * $query->filterByDataVersamento(array('max' => 12)); // WHERE data_versamento <= 12
     * </code>
     *
     * @param     mixed $dataVersamento The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByDataVersamento($dataVersamento = null, $comparison = null)
    {
        if (is_array($dataVersamento)) {
            $useMinMax = false;
            if (isset($dataVersamento['min'])) {
                $this->addUsingAlias(TassePeer::DATA_VERSAMENTO, $dataVersamento['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataVersamento['max'])) {
                $this->addUsingAlias(TassePeer::DATA_VERSAMENTO, $dataVersamento['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::DATA_VERSAMENTO, $dataVersamento, $comparison);
    }

    /**
     * Filter the query on the note column
     *
     * Example usage:
     * <code>
     * $query->filterByNote('fooValue');   // WHERE note = 'fooValue'
     * $query->filterByNote('%fooValue%'); // WHERE note LIKE '%fooValue%'
     * </code>
     *
     * @param     string $note The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByNote($note = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($note)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $note)) {
                $note = str_replace('*', '%', $note);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::NOTE, $note, $comparison);
    }

    /**
     * Filter the query on the anno_scolastico column
     *
     * Example usage:
     * <code>
     * $query->filterByAnnoScolastico('fooValue');   // WHERE anno_scolastico = 'fooValue'
     * $query->filterByAnnoScolastico('%fooValue%'); // WHERE anno_scolastico LIKE '%fooValue%'
     * </code>
     *
     * @param     string $annoScolastico The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByAnnoScolastico($annoScolastico = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($annoScolastico)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $annoScolastico)) {
                $annoScolastico = str_replace('*', '%', $annoScolastico);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::ANNO_SCOLASTICO, $annoScolastico, $comparison);
    }

    /**
     * Filter the query on the riferimento_estratto_conto column
     *
     * Example usage:
     * <code>
     * $query->filterByRiferimentoEstrattoConto('fooValue');   // WHERE riferimento_estratto_conto = 'fooValue'
     * $query->filterByRiferimentoEstrattoConto('%fooValue%'); // WHERE riferimento_estratto_conto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $riferimentoEstrattoConto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByRiferimentoEstrattoConto($riferimentoEstrattoConto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($riferimentoEstrattoConto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $riferimentoEstrattoConto)) {
                $riferimentoEstrattoConto = str_replace('*', '%', $riferimentoEstrattoConto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::RIFERIMENTO_ESTRATTO_CONTO, $riferimentoEstrattoConto, $comparison);
    }

    /**
     * Filter the query on the data_estratto_conto column
     *
     * Example usage:
     * <code>
     * $query->filterByDataEstrattoConto(1234); // WHERE data_estratto_conto = 1234
     * $query->filterByDataEstrattoConto(array(12, 34)); // WHERE data_estratto_conto IN (12, 34)
     * $query->filterByDataEstrattoConto(array('min' => 12)); // WHERE data_estratto_conto >= 12
     * $query->filterByDataEstrattoConto(array('max' => 12)); // WHERE data_estratto_conto <= 12
     * </code>
     *
     * @param     mixed $dataEstrattoConto The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByDataEstrattoConto($dataEstrattoConto = null, $comparison = null)
    {
        if (is_array($dataEstrattoConto)) {
            $useMinMax = false;
            if (isset($dataEstrattoConto['min'])) {
                $this->addUsingAlias(TassePeer::DATA_ESTRATTO_CONTO, $dataEstrattoConto['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataEstrattoConto['max'])) {
                $this->addUsingAlias(TassePeer::DATA_ESTRATTO_CONTO, $dataEstrattoConto['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::DATA_ESTRATTO_CONTO, $dataEstrattoConto, $comparison);
    }

    /**
     * Filter the query on the dati_debitore column
     *
     * Example usage:
     * <code>
     * $query->filterByDatiDebitore('fooValue');   // WHERE dati_debitore = 'fooValue'
     * $query->filterByDatiDebitore('%fooValue%'); // WHERE dati_debitore LIKE '%fooValue%'
     * </code>
     *
     * @param     string $datiDebitore The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByDatiDebitore($datiDebitore = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($datiDebitore)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $datiDebitore)) {
                $datiDebitore = str_replace('*', '%', $datiDebitore);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::DATI_DEBITORE, $datiDebitore, $comparison);
    }

    /**
     * Filter the query on the numero_bollettino column
     *
     * Example usage:
     * <code>
     * $query->filterByNumeroBollettino('fooValue');   // WHERE numero_bollettino = 'fooValue'
     * $query->filterByNumeroBollettino('%fooValue%'); // WHERE numero_bollettino LIKE '%fooValue%'
     * </code>
     *
     * @param     string $numeroBollettino The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByNumeroBollettino($numeroBollettino = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($numeroBollettino)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $numeroBollettino)) {
                $numeroBollettino = str_replace('*', '%', $numeroBollettino);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::NUMERO_BOLLETTINO, $numeroBollettino, $comparison);
    }

    /**
     * Filter the query on the is_incoming column
     *
     * Example usage:
     * <code>
     * $query->filterByIsIncoming(true); // WHERE is_incoming = true
     * $query->filterByIsIncoming('yes'); // WHERE is_incoming = true
     * </code>
     *
     * @param     boolean|string $isIncoming The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByIsIncoming($isIncoming = null, $comparison = null)
    {
        if (is_string($isIncoming)) {
            $isIncoming = in_array(strtolower($isIncoming), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(TassePeer::IS_INCOMING, $isIncoming, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(TassePeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(TassePeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the tipologia_uscita column
     *
     * Example usage:
     * <code>
     * $query->filterByTipologiaUscita('fooValue');   // WHERE tipologia_uscita = 'fooValue'
     * $query->filterByTipologiaUscita('%fooValue%'); // WHERE tipologia_uscita LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipologiaUscita The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByTipologiaUscita($tipologiaUscita = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipologiaUscita)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipologiaUscita)) {
                $tipologiaUscita = str_replace('*', '%', $tipologiaUscita);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::TIPOLOGIA_USCITA, $tipologiaUscita, $comparison);
    }

    /**
     * Filter the query on the destinazione_pagamento column
     *
     * Example usage:
     * <code>
     * $query->filterByDestinazionePagamento('fooValue');   // WHERE destinazione_pagamento = 'fooValue'
     * $query->filterByDestinazionePagamento('%fooValue%'); // WHERE destinazione_pagamento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $destinazionePagamento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByDestinazionePagamento($destinazionePagamento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($destinazionePagamento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $destinazionePagamento)) {
                $destinazionePagamento = str_replace('*', '%', $destinazionePagamento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::DESTINAZIONE_PAGAMENTO, $destinazionePagamento, $comparison);
    }

    /**
     * Filter the query on the modalita_pagamento column
     *
     * Example usage:
     * <code>
     * $query->filterByModalitaPagamento('fooValue');   // WHERE modalita_pagamento = 'fooValue'
     * $query->filterByModalitaPagamento('%fooValue%'); // WHERE modalita_pagamento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $modalitaPagamento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByModalitaPagamento($modalitaPagamento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($modalitaPagamento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $modalitaPagamento)) {
                $modalitaPagamento = str_replace('*', '%', $modalitaPagamento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::MODALITA_PAGAMENTO, $modalitaPagamento, $comparison);
    }

    /**
     * Filter the query on the sede_studente column
     *
     * Example usage:
     * <code>
     * $query->filterBySedeStudente(1234); // WHERE sede_studente = 1234
     * $query->filterBySedeStudente(array(12, 34)); // WHERE sede_studente IN (12, 34)
     * $query->filterBySedeStudente(array('min' => 12)); // WHERE sede_studente >= 12
     * $query->filterBySedeStudente(array('max' => 12)); // WHERE sede_studente <= 12
     * </code>
     *
     * @param     mixed $sedeStudente The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterBySedeStudente($sedeStudente = null, $comparison = null)
    {
        if (is_array($sedeStudente)) {
            $useMinMax = false;
            if (isset($sedeStudente['min'])) {
                $this->addUsingAlias(TassePeer::SEDE_STUDENTE, $sedeStudente['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($sedeStudente['max'])) {
                $this->addUsingAlias(TassePeer::SEDE_STUDENTE, $sedeStudente['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::SEDE_STUDENTE, $sedeStudente, $comparison);
    }

    /**
     * Filter the query on the classe column
     *
     * Example usage:
     * <code>
     * $query->filterByClasse('fooValue');   // WHERE classe = 'fooValue'
     * $query->filterByClasse('%fooValue%'); // WHERE classe LIKE '%fooValue%'
     * </code>
     *
     * @param     string $classe The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByClasse($classe = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($classe)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $classe)) {
                $classe = str_replace('*', '%', $classe);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TassePeer::CLASSE, $classe, $comparison);
    }

    /**
     * Filter the query on the importo_versato column
     *
     * Example usage:
     * <code>
     * $query->filterByImportoVersato(1234); // WHERE importo_versato = 1234
     * $query->filterByImportoVersato(array(12, 34)); // WHERE importo_versato IN (12, 34)
     * $query->filterByImportoVersato(array('min' => 12)); // WHERE importo_versato >= 12
     * $query->filterByImportoVersato(array('max' => 12)); // WHERE importo_versato <= 12
     * </code>
     *
     * @param     mixed $importoVersato The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function filterByImportoVersato($importoVersato = null, $comparison = null)
    {
        if (is_array($importoVersato)) {
            $useMinMax = false;
            if (isset($importoVersato['min'])) {
                $this->addUsingAlias(TassePeer::IMPORTO_VERSATO, $importoVersato['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($importoVersato['max'])) {
                $this->addUsingAlias(TassePeer::IMPORTO_VERSATO, $importoVersato['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TassePeer::IMPORTO_VERSATO, $importoVersato, $comparison);
    }

    /**
     * Filter the query by a related TipiTasse object
     *
     * @param   TipiTasse|PropelObjectCollection $tipiTasse The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TasseQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTipiTasse($tipiTasse, $comparison = null)
    {
        if ($tipiTasse instanceof TipiTasse) {
            return $this
                ->addUsingAlias(TassePeer::ID_TIPO_TASSA, $tipiTasse->getIdTipoTassa(), $comparison);
        } elseif ($tipiTasse instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(TassePeer::ID_TIPO_TASSA, $tipiTasse->toKeyValue('PrimaryKey', 'IdTipoTassa'), $comparison);
        } else {
            throw new PropelException('filterByTipiTasse() only accepts arguments of type TipiTasse or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the TipiTasse relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function joinTipiTasse($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('TipiTasse');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'TipiTasse');
        }

        return $this;
    }

    /**
     * Use the TipiTasse relation TipiTasse object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Ccp\TipiTasseQuery A secondary query class using the current class as primary query
     */
    public function useTipiTasseQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinTipiTasse($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'TipiTasse', '\Ccp\TipiTasseQuery');
    }

    /**
     * Filter the query by a related StudentiCompleti object
     *
     * @param   StudentiCompleti|PropelObjectCollection $studentiCompleti The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TasseQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStudentiCompleti($studentiCompleti, $comparison = null)
    {
        if ($studentiCompleti instanceof StudentiCompleti) {
            return $this
                ->addUsingAlias(TassePeer::ID_STUDENTE, $studentiCompleti->getIdStudente(), $comparison);
        } elseif ($studentiCompleti instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(TassePeer::ID_STUDENTE, $studentiCompleti->toKeyValue('PrimaryKey', 'IdStudente'), $comparison);
        } else {
            throw new PropelException('filterByStudentiCompleti() only accepts arguments of type StudentiCompleti or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StudentiCompleti relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function joinStudentiCompleti($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StudentiCompleti');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StudentiCompleti');
        }

        return $this;
    }

    /**
     * Use the StudentiCompleti relation StudentiCompleti object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Ccp\StudentiCompletiQuery A secondary query class using the current class as primary query
     */
    public function useStudentiCompletiQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinStudentiCompleti($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StudentiCompleti', '\Ccp\StudentiCompletiQuery');
    }

    /**
     * Filter the query by a related Studenti object
     *
     * @param   Studenti|PropelObjectCollection $studenti The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TasseQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStudenti($studenti, $comparison = null)
    {
        if ($studenti instanceof Studenti) {
            return $this
                ->addUsingAlias(TassePeer::ID_STUDENTE, $studenti->getIdStudente(), $comparison);
        } elseif ($studenti instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(TassePeer::ID_STUDENTE, $studenti->toKeyValue('PrimaryKey', 'IdStudente'), $comparison);
        } else {
            throw new PropelException('filterByStudenti() only accepts arguments of type Studenti or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Studenti relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function joinStudenti($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Studenti');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Studenti');
        }

        return $this;
    }

    /**
     * Use the Studenti relation Studenti object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Ccp\StudentiQuery A secondary query class using the current class as primary query
     */
    public function useStudentiQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinStudenti($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Studenti', '\Ccp\StudentiQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Tasse $tasse Object to remove from the list of results
     *
     * @return TasseQuery The current query, for fluid interface
     */
    public function prune($tasse = null)
    {
        if ($tasse) {
            $this->addUsingAlias(TassePeer::ID_TASSE, $tasse->getIdTasse(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
