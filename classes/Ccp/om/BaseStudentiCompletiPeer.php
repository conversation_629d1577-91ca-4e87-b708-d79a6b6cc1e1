<?php

namespace Ccp\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Ccp\StudentiCompleti;
use Ccp\StudentiCompletiPeer;
use Ccp\map\StudentiCompletiTableMap;

/**
 * Base static class for performing query and update operations on the 'studenti_completi' table.
 *
 *
 *
 * @package propel.generator.Ccp.om
 */
abstract class BaseStudentiCompletiPeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'studenti_completi';

    /** the related Propel class for this table */
    const OM_CLASS = 'Ccp\\StudentiCompleti';

    /** the related TableMap class for this table */
    const TM_CLASS = 'StudentiCompletiTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 234;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 234;

    /** the column name for the id_studente field */
    const ID_STUDENTE = 'studenti_completi.id_studente';

    /** the column name for the nome field */
    const NOME = 'studenti_completi.nome';

    /** the column name for the cognome field */
    const COGNOME = 'studenti_completi.cognome';

    /** the column name for the indirizzo field */
    const INDIRIZZO = 'studenti_completi.indirizzo';

    /** the column name for the citta field */
    const CITTA = 'studenti_completi.citta';

    /** the column name for the cap field */
    const CAP = 'studenti_completi.cap';

    /** the column name for the provincia field */
    const PROVINCIA = 'studenti_completi.provincia';

    /** the column name for the sesso field */
    const SESSO = 'studenti_completi.sesso';

    /** the column name for the telefono field */
    const TELEFONO = 'studenti_completi.telefono';

    /** the column name for the cellulare1 field */
    const CELLULARE1 = 'studenti_completi.cellulare1';

    /** the column name for the cellulare2 field */
    const CELLULARE2 = 'studenti_completi.cellulare2';

    /** the column name for the email1 field */
    const EMAIL1 = 'studenti_completi.email1';

    /** the column name for the email2 field */
    const EMAIL2 = 'studenti_completi.email2';

    /** the column name for the invio_email field */
    const INVIO_EMAIL = 'studenti_completi.invio_email';

    /** the column name for the invio_email_cumulativo field */
    const INVIO_EMAIL_CUMULATIVO = 'studenti_completi.invio_email_cumulativo';

    /** the column name for the invio_email_parametrico field */
    const INVIO_EMAIL_PARAMETRICO = 'studenti_completi.invio_email_parametrico';

    /** the column name for the invio_email_temporale field */
    const INVIO_EMAIL_TEMPORALE = 'studenti_completi.invio_email_temporale';

    /** the column name for the tipo_sms field */
    const TIPO_SMS = 'studenti_completi.tipo_sms';

    /** the column name for the tipo_sms_cumulativo field */
    const TIPO_SMS_CUMULATIVO = 'studenti_completi.tipo_sms_cumulativo';

    /** the column name for the tipo_sms_parametrico field */
    const TIPO_SMS_PARAMETRICO = 'studenti_completi.tipo_sms_parametrico';

    /** the column name for the tipo_sms_temporale field */
    const TIPO_SMS_TEMPORALE = 'studenti_completi.tipo_sms_temporale';

    /** the column name for the aut_entrata_ritardo field */
    const AUT_ENTRATA_RITARDO = 'studenti_completi.aut_entrata_ritardo';

    /** the column name for the aut_uscita_anticipo field */
    const AUT_USCITA_ANTICIPO = 'studenti_completi.aut_uscita_anticipo';

    /** the column name for the aut_pomeriggio field */
    const AUT_POMERIGGIO = 'studenti_completi.aut_pomeriggio';

    /** the column name for the acconsente field */
    const ACCONSENTE = 'studenti_completi.acconsente';

    /** the column name for the ritirato field */
    const RITIRATO = 'studenti_completi.ritirato';

    /** the column name for the data_nascita field */
    const DATA_NASCITA = 'studenti_completi.data_nascita';

    /** the column name for the codice_studente field */
    const CODICE_STUDENTE = 'studenti_completi.codice_studente';

    /** the column name for the password_studente field */
    const PASSWORD_STUDENTE = 'studenti_completi.password_studente';

    /** the column name for the codice_giustificazioni_studente field */
    const CODICE_GIUSTIFICAZIONI_STUDENTE = 'studenti_completi.codice_giustificazioni_studente';

    /** the column name for the esonero_religione field */
    const ESONERO_RELIGIONE = 'studenti_completi.esonero_religione';

    /** the column name for the materia_sostitutiva_religione field */
    const MATERIA_SOSTITUTIVA_RELIGIONE = 'studenti_completi.materia_sostitutiva_religione';

    /** the column name for the esonero_ed_fisica field */
    const ESONERO_ED_FISICA = 'studenti_completi.esonero_ed_fisica';

    /** the column name for the materia_sostitutiva_edfisica field */
    const MATERIA_SOSTITUTIVA_EDFISICA = 'studenti_completi.materia_sostitutiva_edfisica';

    /** the column name for the crediti_terza field */
    const CREDITI_TERZA = 'studenti_completi.crediti_terza';

    /** the column name for the media_voti_terza field */
    const MEDIA_VOTI_TERZA = 'studenti_completi.media_voti_terza';

    /** the column name for the debiti_terza field */
    const DEBITI_TERZA = 'studenti_completi.debiti_terza';

    /** the column name for the crediti_sospesi_terza field */
    const CREDITI_SOSPESI_TERZA = 'studenti_completi.crediti_sospesi_terza';

    /** the column name for the crediti_reintegrati_terza field */
    const CREDITI_REINTEGRATI_TERZA = 'studenti_completi.crediti_reintegrati_terza';

    /** the column name for the crediti_quarta field */
    const CREDITI_QUARTA = 'studenti_completi.crediti_quarta';

    /** the column name for the media_voti_quarta field */
    const MEDIA_VOTI_QUARTA = 'studenti_completi.media_voti_quarta';

    /** the column name for the debiti_quarta field */
    const DEBITI_QUARTA = 'studenti_completi.debiti_quarta';

    /** the column name for the crediti_sospesi_quarta field */
    const CREDITI_SOSPESI_QUARTA = 'studenti_completi.crediti_sospesi_quarta';

    /** the column name for the crediti_reintegrati_quarta field */
    const CREDITI_REINTEGRATI_QUARTA = 'studenti_completi.crediti_reintegrati_quarta';

    /** the column name for the crediti_quinta field */
    const CREDITI_QUINTA = 'studenti_completi.crediti_quinta';

    /** the column name for the media_voti_quinta field */
    const MEDIA_VOTI_QUINTA = 'studenti_completi.media_voti_quinta';

    /** the column name for the crediti_finali_agg field */
    const CREDITI_FINALI_AGG = 'studenti_completi.crediti_finali_agg';

    /** the column name for the matricola field */
    const MATRICOLA = 'studenti_completi.matricola';

    /** the column name for the luogo_nascita field */
    const LUOGO_NASCITA = 'studenti_completi.luogo_nascita';

    /** the column name for the provincia_nascita field */
    const PROVINCIA_NASCITA = 'studenti_completi.provincia_nascita';

    /** the column name for the motivi_crediti_terza field */
    const MOTIVI_CREDITI_TERZA = 'studenti_completi.motivi_crediti_terza';

    /** the column name for the motivi_crediti_quarta field */
    const MOTIVI_CREDITI_QUARTA = 'studenti_completi.motivi_crediti_quarta';

    /** the column name for the motivi_crediti_quinta field */
    const MOTIVI_CREDITI_QUINTA = 'studenti_completi.motivi_crediti_quinta';

    /** the column name for the motivi_crediti_agg field */
    const MOTIVI_CREDITI_AGG = 'studenti_completi.motivi_crediti_agg';

    /** the column name for the codice_comune_nascita field */
    const CODICE_COMUNE_NASCITA = 'studenti_completi.codice_comune_nascita';

    /** the column name for the stato_nascita field */
    const STATO_NASCITA = 'studenti_completi.stato_nascita';

    /** the column name for the cittadinanza field */
    const CITTADINANZA = 'studenti_completi.cittadinanza';

    /** the column name for the seconda_cittadinanza field */
    const SECONDA_CITTADINANZA = 'studenti_completi.seconda_cittadinanza';

    /** the column name for the codice_comune_residenza field */
    const CODICE_COMUNE_RESIDENZA = 'studenti_completi.codice_comune_residenza';

    /** the column name for the distretto field */
    const DISTRETTO = 'studenti_completi.distretto';

    /** the column name for the codice_fiscale field */
    const CODICE_FISCALE = 'studenti_completi.codice_fiscale';

    /** the column name for the medico field */
    const MEDICO = 'studenti_completi.medico';

    /** the column name for the telefono_medico field */
    const TELEFONO_MEDICO = 'studenti_completi.telefono_medico';

    /** the column name for the intolleranze_alim field */
    const INTOLLERANZE_ALIM = 'studenti_completi.intolleranze_alim';

    /** the column name for the gruppo_sanguigno field */
    const GRUPPO_SANGUIGNO = 'studenti_completi.gruppo_sanguigno';

    /** the column name for the gruppo_rh field */
    const GRUPPO_RH = 'studenti_completi.gruppo_rh';

    /** the column name for the codice_asl field */
    const CODICE_ASL = 'studenti_completi.codice_asl';

    /** the column name for the annotazioni field */
    const ANNOTAZIONI = 'studenti_completi.annotazioni';

    /** the column name for the stato_civile field */
    const STATO_CIVILE = 'studenti_completi.stato_civile';

    /** the column name for the voto_primo_scritto field */
    const VOTO_PRIMO_SCRITTO = 'studenti_completi.voto_primo_scritto';

    /** the column name for the voto_secondo_scritto field */
    const VOTO_SECONDO_SCRITTO = 'studenti_completi.voto_secondo_scritto';

    /** the column name for the voto_terzo_scritto field */
    const VOTO_TERZO_SCRITTO = 'studenti_completi.voto_terzo_scritto';

    /** the column name for the voto_orale field */
    const VOTO_ORALE = 'studenti_completi.voto_orale';

    /** the column name for the voto_bonus field */
    const VOTO_BONUS = 'studenti_completi.voto_bonus';

    /** the column name for the materia_secondo_scr field */
    const MATERIA_SECONDO_SCR = 'studenti_completi.materia_secondo_scr';

    /** the column name for the ulteriori_specif_diploma field */
    const ULTERIORI_SPECIF_DIPLOMA = 'studenti_completi.ulteriori_specif_diploma';

    /** the column name for the numero_diploma field */
    const NUMERO_DIPLOMA = 'studenti_completi.numero_diploma';

    /** the column name for the chi_inserisce field */
    const CHI_INSERISCE = 'studenti_completi.chi_inserisce';

    /** the column name for the data_inserimento field */
    const DATA_INSERIMENTO = 'studenti_completi.data_inserimento';

    /** the column name for the tipo_inserimento field */
    const TIPO_INSERIMENTO = 'studenti_completi.tipo_inserimento';

    /** the column name for the chi_modifica field */
    const CHI_MODIFICA = 'studenti_completi.chi_modifica';

    /** the column name for the data_modifica field */
    const DATA_MODIFICA = 'studenti_completi.data_modifica';

    /** the column name for the tipo_modifica field */
    const TIPO_MODIFICA = 'studenti_completi.tipo_modifica';

    /** the column name for the flag_canc field */
    const FLAG_CANC = 'studenti_completi.flag_canc';

    /** the column name for the stato_avanzamento field */
    const STATO_AVANZAMENTO = 'studenti_completi.stato_avanzamento';

    /** the column name for the data_stato_avanzamento field */
    const DATA_STATO_AVANZAMENTO = 'studenti_completi.data_stato_avanzamento';

    /** the column name for the cap_provincia_nascita field */
    const CAP_PROVINCIA_NASCITA = 'studenti_completi.cap_provincia_nascita';

    /** the column name for the badge field */
    const BADGE = 'studenti_completi.badge';

    /** the column name for the cap_residenza field */
    const CAP_RESIDENZA = 'studenti_completi.cap_residenza';

    /** the column name for the codice_comune_domicilio field */
    const CODICE_COMUNE_DOMICILIO = 'studenti_completi.codice_comune_domicilio';

    /** the column name for the cap_domicilio field */
    const CAP_DOMICILIO = 'studenti_completi.cap_domicilio';

    /** the column name for the cap_nascita field */
    const CAP_NASCITA = 'studenti_completi.cap_nascita';

    /** the column name for the indirizzo_domicilio field */
    const INDIRIZZO_DOMICILIO = 'studenti_completi.indirizzo_domicilio';

    /** the column name for the citta_nascita_straniera field */
    const CITTA_NASCITA_STRANIERA = 'studenti_completi.citta_nascita_straniera';

    /** the column name for the cellulare_allievo field */
    const CELLULARE_ALLIEVO = 'studenti_completi.cellulare_allievo';

    /** the column name for the handicap field */
    const HANDICAP = 'studenti_completi.handicap';

    /** the column name for the stato_convittore field */
    const STATO_CONVITTORE = 'studenti_completi.stato_convittore';

    /** the column name for the data_ritiro field */
    const DATA_RITIRO = 'studenti_completi.data_ritiro';

    /** the column name for the voto_ammissione field */
    const VOTO_AMMISSIONE = 'studenti_completi.voto_ammissione';

    /** the column name for the differenza_punteggio field */
    const DIFFERENZA_PUNTEGGIO = 'studenti_completi.differenza_punteggio';

    /** the column name for the voto_qualifica field */
    const VOTO_QUALIFICA = 'studenti_completi.voto_qualifica';

    /** the column name for the voto_esame_sc1_qual field */
    const VOTO_ESAME_SC1_QUAL = 'studenti_completi.voto_esame_sc1_qual';

    /** the column name for the voto_esame_sc2_qual field */
    const VOTO_ESAME_SC2_QUAL = 'studenti_completi.voto_esame_sc2_qual';

    /** the column name for the voto_esame_or_qual field */
    const VOTO_ESAME_OR_QUAL = 'studenti_completi.voto_esame_or_qual';

    /** the column name for the stato_privatista field */
    const STATO_PRIVATISTA = 'studenti_completi.stato_privatista';

    /** the column name for the foto field */
    const FOTO = 'studenti_completi.foto';

    /** the column name for the rappresentante field */
    const RAPPRESENTANTE = 'studenti_completi.rappresentante';

    /** the column name for the obbligo_formativo field */
    const OBBLIGO_FORMATIVO = 'studenti_completi.obbligo_formativo';

    /** the column name for the id_lingua_1 field */
    const ID_LINGUA_1 = 'studenti_completi.id_lingua_1';

    /** the column name for the id_lingua_2 field */
    const ID_LINGUA_2 = 'studenti_completi.id_lingua_2';

    /** the column name for the id_lingua_3 field */
    const ID_LINGUA_3 = 'studenti_completi.id_lingua_3';

    /** the column name for the id_lingua_4 field */
    const ID_LINGUA_4 = 'studenti_completi.id_lingua_4';

    /** the column name for the id_lingua_5 field */
    const ID_LINGUA_5 = 'studenti_completi.id_lingua_5';

    /** the column name for the id_provenienza_scolastica field */
    const ID_PROVENIENZA_SCOLASTICA = 'studenti_completi.id_provenienza_scolastica';

    /** the column name for the id_scuola_media field */
    const ID_SCUOLA_MEDIA = 'studenti_completi.id_scuola_media';

    /** the column name for the lingua_scuola_media field */
    const LINGUA_SCUOLA_MEDIA = 'studenti_completi.lingua_scuola_media';

    /** the column name for the lingua_scuola_media_2 field */
    const LINGUA_SCUOLA_MEDIA_2 = 'studenti_completi.lingua_scuola_media_2';

    /** the column name for the giudizio_scuola_media field */
    const GIUDIZIO_SCUOLA_MEDIA = 'studenti_completi.giudizio_scuola_media';

    /** the column name for the trasporto field */
    const TRASPORTO = 'studenti_completi.trasporto';

    /** the column name for the data_iscrizione field */
    const DATA_ISCRIZIONE = 'studenti_completi.data_iscrizione';

    /** the column name for the pei field */
    const PEI = 'studenti_completi.pei';

    /** the column name for the ammesso_esame_qualifica field */
    const AMMESSO_ESAME_QUALIFICA = 'studenti_completi.ammesso_esame_qualifica';

    /** the column name for the ammesso_esame_quinta field */
    const AMMESSO_ESAME_QUINTA = 'studenti_completi.ammesso_esame_quinta';

    /** the column name for the giudizio_ammissione_quinta field */
    const GIUDIZIO_AMMISSIONE_QUINTA = 'studenti_completi.giudizio_ammissione_quinta';

    /** the column name for the grado_handicap field */
    const GRADO_HANDICAP = 'studenti_completi.grado_handicap';

    /** the column name for the tipo_handicap field */
    const TIPO_HANDICAP = 'studenti_completi.tipo_handicap';

    /** the column name for the stato_licenza_maestro field */
    const STATO_LICENZA_MAESTRO = 'studenti_completi.stato_licenza_maestro';

    /** the column name for the id_studente_sissi field */
    const ID_STUDENTE_SISSI = 'studenti_completi.id_studente_sissi';

    /** the column name for the badge_rfid field */
    const BADGE_RFID = 'studenti_completi.badge_rfid';

    /** the column name for the lode field */
    const LODE = 'studenti_completi.lode';

    /** the column name for the distretto_scolastico field */
    const DISTRETTO_SCOLASTICO = 'studenti_completi.distretto_scolastico';

    /** the column name for the giudizio_ammissione_terza field */
    const GIUDIZIO_AMMISSIONE_TERZA = 'studenti_completi.giudizio_ammissione_terza';

    /** the column name for the esito_prima_media field */
    const ESITO_PRIMA_MEDIA = 'studenti_completi.esito_prima_media';

    /** the column name for the esito_seconda_media field */
    const ESITO_SECONDA_MEDIA = 'studenti_completi.esito_seconda_media';

    /** the column name for the esito_terza_media field */
    const ESITO_TERZA_MEDIA = 'studenti_completi.esito_terza_media';

    /** the column name for the giudizio_esame_sc1_qual field */
    const GIUDIZIO_ESAME_SC1_QUAL = 'studenti_completi.giudizio_esame_sc1_qual';

    /** the column name for the giudizio_esame_sc2_qual field */
    const GIUDIZIO_ESAME_SC2_QUAL = 'studenti_completi.giudizio_esame_sc2_qual';

    /** the column name for the giudizio_esame_or_qual field */
    const GIUDIZIO_ESAME_OR_QUAL = 'studenti_completi.giudizio_esame_or_qual';

    /** the column name for the giudizio_complessivo_esame_qual field */
    const GIUDIZIO_COMPLESSIVO_ESAME_QUAL = 'studenti_completi.giudizio_complessivo_esame_qual';

    /** the column name for the acconsente_aziende field */
    const ACCONSENTE_AZIENDE = 'studenti_completi.acconsente_aziende';

    /** the column name for the curriculum_prima field */
    const CURRICULUM_PRIMA = 'studenti_completi.curriculum_prima';

    /** the column name for the curriculum_seconda field */
    const CURRICULUM_SECONDA = 'studenti_completi.curriculum_seconda';

    /** the column name for the stage_professionali field */
    const STAGE_PROFESSIONALI = 'studenti_completi.stage_professionali';

    /** the column name for the data_orale field */
    const DATA_ORALE = 'studenti_completi.data_orale';

    /** the column name for the ordine_esame_orale field */
    const ORDINE_ESAME_ORALE = 'studenti_completi.ordine_esame_orale';

    /** the column name for the tipo_primo_scritto field */
    const TIPO_PRIMO_SCRITTO = 'studenti_completi.tipo_primo_scritto';

    /** the column name for the tipo_secondo_scritto field */
    const TIPO_SECONDO_SCRITTO = 'studenti_completi.tipo_secondo_scritto';

    /** the column name for the tipo_terzo_scritto field */
    const TIPO_TERZO_SCRITTO = 'studenti_completi.tipo_terzo_scritto';

    /** the column name for the unanimita_primo_scritto field */
    const UNANIMITA_PRIMO_SCRITTO = 'studenti_completi.unanimita_primo_scritto';

    /** the column name for the unanimita_secondo_scritto field */
    const UNANIMITA_SECONDO_SCRITTO = 'studenti_completi.unanimita_secondo_scritto';

    /** the column name for the unanimita_terzo_scritto field */
    const UNANIMITA_TERZO_SCRITTO = 'studenti_completi.unanimita_terzo_scritto';

    /** the column name for the argomento_scelto_orale field */
    const ARGOMENTO_SCELTO_ORALE = 'studenti_completi.argomento_scelto_orale';

    /** the column name for the area_disc_1_orale field */
    const AREA_DISC_1_ORALE = 'studenti_completi.area_disc_1_orale';

    /** the column name for the area_disc_2_orale field */
    const AREA_DISC_2_ORALE = 'studenti_completi.area_disc_2_orale';

    /** the column name for the disc_elaborati_orale field */
    const DISC_ELABORATI_ORALE = 'studenti_completi.disc_elaborati_orale';

    /** the column name for the unanimita_voto_finale field */
    const UNANIMITA_VOTO_FINALE = 'studenti_completi.unanimita_voto_finale';

    /** the column name for the presente_esame_quinta field */
    const PRESENTE_ESAME_QUINTA = 'studenti_completi.presente_esame_quinta';

    /** the column name for the stampa_badge field */
    const STAMPA_BADGE = 'studenti_completi.stampa_badge';

    /** the column name for the id_classe_destinazione field */
    const ID_CLASSE_DESTINAZIONE = 'studenti_completi.id_classe_destinazione';

    /** the column name for the sconto_rette field */
    const SCONTO_RETTE = 'studenti_completi.sconto_rette';

    /** the column name for the carta_studente_numero field */
    const CARTA_STUDENTE_NUMERO = 'studenti_completi.carta_studente_numero';

    /** the column name for the carta_studente_scadenza field */
    const CARTA_STUDENTE_SCADENZA = 'studenti_completi.carta_studente_scadenza';

    /** the column name for the esito_corrente_calcolato field */
    const ESITO_CORRENTE_CALCOLATO = 'studenti_completi.esito_corrente_calcolato';

    /** the column name for the id_flusso field */
    const ID_FLUSSO = 'studenti_completi.id_flusso';

    /** the column name for the data_aggiornamento_sogei field */
    const DATA_AGGIORNAMENTO_SOGEI = 'studenti_completi.data_aggiornamento_sogei';

    /** the column name for the codice_alunno_ministeriale field */
    const CODICE_ALUNNO_MINISTERIALE = 'studenti_completi.codice_alunno_ministeriale';

    /** the column name for the flag_cf_fittizio field */
    const FLAG_CF_FITTIZIO = 'studenti_completi.flag_cf_fittizio';

    /** the column name for the flag_s2f field */
    const FLAG_S2F = 'studenti_completi.flag_s2f';

    /** the column name for the codice_stato_sogei field */
    const CODICE_STATO_SOGEI = 'studenti_completi.codice_stato_sogei';

    /** the column name for the codice_gruppo_nomade field */
    const CODICE_GRUPPO_NOMADE = 'studenti_completi.codice_gruppo_nomade';

    /** the column name for the flag_minore_straniero field */
    const FLAG_MINORE_STRANIERO = 'studenti_completi.flag_minore_straniero';

    /** the column name for the chiave field */
    const CHIAVE = 'studenti_completi.chiave';

    /** the column name for the voto_esame_medie_italiano field */
    const VOTO_ESAME_MEDIE_ITALIANO = 'studenti_completi.voto_esame_medie_italiano';

    /** the column name for the voto_esame_medie_inglese field */
    const VOTO_ESAME_MEDIE_INGLESE = 'studenti_completi.voto_esame_medie_inglese';

    /** the column name for the voto_esame_medie_matematica field */
    const VOTO_ESAME_MEDIE_MATEMATICA = 'studenti_completi.voto_esame_medie_matematica';

    /** the column name for the voto_esame_medie_seconda_lingua field */
    const VOTO_ESAME_MEDIE_SECONDA_LINGUA = 'studenti_completi.voto_esame_medie_seconda_lingua';

    /** the column name for the voto_esame_medie_invalsi_ita field */
    const VOTO_ESAME_MEDIE_INVALSI_ITA = 'studenti_completi.voto_esame_medie_invalsi_ita';

    /** the column name for the voto_esame_medie_invalsi_mat field */
    const VOTO_ESAME_MEDIE_INVALSI_MAT = 'studenti_completi.voto_esame_medie_invalsi_mat';

    /** the column name for the voto_esame_medie_orale field */
    const VOTO_ESAME_MEDIE_ORALE = 'studenti_completi.voto_esame_medie_orale';

    /** the column name for the voto_ammissione_medie field */
    const VOTO_AMMISSIONE_MEDIE = 'studenti_completi.voto_ammissione_medie';

    /** the column name for the esito_prima_elementare field */
    const ESITO_PRIMA_ELEMENTARE = 'studenti_completi.esito_prima_elementare';

    /** the column name for the esito_seconda_elementare field */
    const ESITO_SECONDA_ELEMENTARE = 'studenti_completi.esito_seconda_elementare';

    /** the column name for the esito_terza_elementare field */
    const ESITO_TERZA_ELEMENTARE = 'studenti_completi.esito_terza_elementare';

    /** the column name for the esito_quarta_elementare field */
    const ESITO_QUARTA_ELEMENTARE = 'studenti_completi.esito_quarta_elementare';

    /** the column name for the esito_quinta_elementare field */
    const ESITO_QUINTA_ELEMENTARE = 'studenti_completi.esito_quinta_elementare';

    /** the column name for the tipo_voto_esame_medie_italiano field */
    const TIPO_VOTO_ESAME_MEDIE_ITALIANO = 'studenti_completi.tipo_voto_esame_medie_italiano';

    /** the column name for the tipo_voto_esame_medie_inglese field */
    const TIPO_VOTO_ESAME_MEDIE_INGLESE = 'studenti_completi.tipo_voto_esame_medie_inglese';

    /** the column name for the giudizio_1_medie field */
    const GIUDIZIO_1_MEDIE = 'studenti_completi.giudizio_1_medie';

    /** the column name for the giudizio_2_medie field */
    const GIUDIZIO_2_MEDIE = 'studenti_completi.giudizio_2_medie';

    /** the column name for the giudizio_3_medie field */
    const GIUDIZIO_3_MEDIE = 'studenti_completi.giudizio_3_medie';

    /** the column name for the argomenti_orali_medie field */
    const ARGOMENTI_ORALI_MEDIE = 'studenti_completi.argomenti_orali_medie';

    /** the column name for the giudizio_finale_1_medie field */
    const GIUDIZIO_FINALE_1_MEDIE = 'studenti_completi.giudizio_finale_1_medie';

    /** the column name for the giudizio_finale_2_medie field */
    const GIUDIZIO_FINALE_2_MEDIE = 'studenti_completi.giudizio_finale_2_medie';

    /** the column name for the giudizio_finale_3_medie field */
    const GIUDIZIO_FINALE_3_MEDIE = 'studenti_completi.giudizio_finale_3_medie';

    /** the column name for the consiglio_terza_media field */
    const CONSIGLIO_TERZA_MEDIA = 'studenti_completi.consiglio_terza_media';

    /** the column name for the giudizio_sintetico_esame_terza_media field */
    const GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA = 'studenti_completi.giudizio_sintetico_esame_terza_media';

    /** the column name for the data_arrivo_in_italia field */
    const DATA_ARRIVO_IN_ITALIA = 'studenti_completi.data_arrivo_in_italia';

    /** the column name for the frequenza_asilo_nido field */
    const FREQUENZA_ASILO_NIDO = 'studenti_completi.frequenza_asilo_nido';

    /** the column name for the frequenza_scuola_materna field */
    const FREQUENZA_SCUOLA_MATERNA = 'studenti_completi.frequenza_scuola_materna';

    /** the column name for the data_aggiornamento_sidi field */
    const DATA_AGGIORNAMENTO_SIDI = 'studenti_completi.data_aggiornamento_sidi';

    /** the column name for the cmp_sup_val_ita field */
    const CMP_SUP_VAL_ITA = 'studenti_completi.cmp_sup_val_ita';

    /** the column name for the cmp_sup_txt_ita field */
    const CMP_SUP_TXT_ITA = 'studenti_completi.cmp_sup_txt_ita';

    /** the column name for the cmp_sup_val_ing field */
    const CMP_SUP_VAL_ING = 'studenti_completi.cmp_sup_val_ing';

    /** the column name for the cmp_sup_txt_ing field */
    const CMP_SUP_TXT_ING = 'studenti_completi.cmp_sup_txt_ing';

    /** the column name for the cmp_sup_val_altri field */
    const CMP_SUP_VAL_ALTRI = 'studenti_completi.cmp_sup_val_altri';

    /** the column name for the cmp_sup_txt_altri field */
    const CMP_SUP_TXT_ALTRI = 'studenti_completi.cmp_sup_txt_altri';

    /** the column name for the cmp_sup_val_mat field */
    const CMP_SUP_VAL_MAT = 'studenti_completi.cmp_sup_val_mat';

    /** the column name for the cmp_sup_txt_mat field */
    const CMP_SUP_TXT_MAT = 'studenti_completi.cmp_sup_txt_mat';

    /** the column name for the cmp_sup_val_sci_tec field */
    const CMP_SUP_VAL_SCI_TEC = 'studenti_completi.cmp_sup_val_sci_tec';

    /** the column name for the cmp_sup_txt_sci_tec field */
    const CMP_SUP_TXT_SCI_TEC = 'studenti_completi.cmp_sup_txt_sci_tec';

    /** the column name for the cmp_sup_val_sto_soc field */
    const CMP_SUP_VAL_STO_SOC = 'studenti_completi.cmp_sup_val_sto_soc';

    /** the column name for the cmp_sup_txt_sto_soc field */
    const CMP_SUP_TXT_STO_SOC = 'studenti_completi.cmp_sup_txt_sto_soc';

    /** the column name for the cmp_med_val_ita field */
    const CMP_MED_VAL_ITA = 'studenti_completi.cmp_med_val_ita';

    /** the column name for the cmp_med_txt_ita field */
    const CMP_MED_TXT_ITA = 'studenti_completi.cmp_med_txt_ita';

    /** the column name for the cmp_med_val_ing field */
    const CMP_MED_VAL_ING = 'studenti_completi.cmp_med_val_ing';

    /** the column name for the cmp_med_txt_ing field */
    const CMP_MED_TXT_ING = 'studenti_completi.cmp_med_txt_ing';

    /** the column name for the cmp_med_val_altri field */
    const CMP_MED_VAL_ALTRI = 'studenti_completi.cmp_med_val_altri';

    /** the column name for the cmp_med_txt_altri field */
    const CMP_MED_TXT_ALTRI = 'studenti_completi.cmp_med_txt_altri';

    /** the column name for the cmp_med_val_mat field */
    const CMP_MED_VAL_MAT = 'studenti_completi.cmp_med_val_mat';

    /** the column name for the cmp_med_txt_mat field */
    const CMP_MED_TXT_MAT = 'studenti_completi.cmp_med_txt_mat';

    /** the column name for the cmp_med_val_sci_tec field */
    const CMP_MED_VAL_SCI_TEC = 'studenti_completi.cmp_med_val_sci_tec';

    /** the column name for the cmp_med_txt_sci_tec field */
    const CMP_MED_TXT_SCI_TEC = 'studenti_completi.cmp_med_txt_sci_tec';

    /** the column name for the cmp_med_val_sto_soc field */
    const CMP_MED_VAL_STO_SOC = 'studenti_completi.cmp_med_val_sto_soc';

    /** the column name for the cmp_med_txt_sto_soc field */
    const CMP_MED_TXT_STO_SOC = 'studenti_completi.cmp_med_txt_sto_soc';

    /** the column name for the cmp_med_val_l2 field */
    const CMP_MED_VAL_L2 = 'studenti_completi.cmp_med_val_l2';

    /** the column name for the cmp_med_txt_l2 field */
    const CMP_MED_TXT_L2 = 'studenti_completi.cmp_med_txt_l2';

    /** the column name for the cmp_med_val_l3 field */
    const CMP_MED_VAL_L3 = 'studenti_completi.cmp_med_val_l3';

    /** the column name for the cmp_med_txt_l3 field */
    const CMP_MED_TXT_L3 = 'studenti_completi.cmp_med_txt_l3';

    /** the column name for the cmp_med_val_arte field */
    const CMP_MED_VAL_ARTE = 'studenti_completi.cmp_med_val_arte';

    /** the column name for the cmp_med_txt_arte field */
    const CMP_MED_TXT_ARTE = 'studenti_completi.cmp_med_txt_arte';

    /** the column name for the cmp_med_val_mus field */
    const CMP_MED_VAL_MUS = 'studenti_completi.cmp_med_val_mus';

    /** the column name for the cmp_med_txt_mus field */
    const CMP_MED_TXT_MUS = 'studenti_completi.cmp_med_txt_mus';

    /** the column name for the cmp_med_val_mot field */
    const CMP_MED_VAL_MOT = 'studenti_completi.cmp_med_val_mot';

    /** the column name for the cmp_med_txt_mot field */
    const CMP_MED_TXT_MOT = 'studenti_completi.cmp_med_txt_mot';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of StudentiCompleti objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array StudentiCompleti[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. StudentiCompletiPeer::$fieldNames[StudentiCompletiPeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('IdStudente', 'Nome', 'Cognome', 'Indirizzo', 'Citta', 'Cap', 'Provincia', 'Sesso', 'Telefono', 'Cellulare1', 'Cellulare2', 'Email1', 'Email2', 'InvioEmail', 'InvioEmailCumulativo', 'InvioEmailParametrico', 'InvioEmailTemporale', 'TipoSms', 'TipoSmsCumulativo', 'TipoSmsParametrico', 'TipoSmsTemporale', 'AutEntrataRitardo', 'AutUscitaAnticipo', 'AutPomeriggio', 'Acconsente', 'Ritirato', 'DataNascita', 'CodiceStudente', 'PasswordStudente', 'CodiceGiustificazioniStudente', 'EsoneroReligione', 'MateriaSostitutivaReligione', 'EsoneroEdFisica', 'MateriaSostitutivaEdfisica', 'CreditiTerza', 'MediaVotiTerza', 'DebitiTerza', 'CreditiSospesiTerza', 'CreditiReintegratiTerza', 'CreditiQuarta', 'MediaVotiQuarta', 'DebitiQuarta', 'CreditiSospesiQuarta', 'CreditiReintegratiQuarta', 'CreditiQuinta', 'MediaVotiQuinta', 'CreditiFinaliAgg', 'Matricola', 'LuogoNascita', 'ProvinciaNascita', 'MotiviCreditiTerza', 'MotiviCreditiQuarta', 'MotiviCreditiQuinta', 'MotiviCreditiAgg', 'CodiceComuneNascita', 'StatoNascita', 'Cittadinanza', 'SecondaCittadinanza', 'CodiceComuneResidenza', 'Distretto', 'CodiceFiscale', 'Medico', 'TelefonoMedico', 'IntolleranzeAlim', 'GruppoSanguigno', 'GruppoRh', 'CodiceAsl', 'Annotazioni', 'StatoCivile', 'VotoPrimoScritto', 'VotoSecondoScritto', 'VotoTerzoScritto', 'VotoOrale', 'VotoBonus', 'MateriaSecondoScr', 'UlterioriSpecifDiploma', 'NumeroDiploma', 'ChiInserisce', 'DataInserimento', 'TipoInserimento', 'ChiModifica', 'DataModifica', 'TipoModifica', 'FlagCanc', 'StatoAvanzamento', 'DataStatoAvanzamento', 'CapProvinciaNascita', 'Badge', 'CapResidenza', 'CodiceComuneDomicilio', 'CapDomicilio', 'CapNascita', 'IndirizzoDomicilio', 'CittaNascitaStraniera', 'CellulareAllievo', 'Handicap', 'StatoConvittore', 'DataRitiro', 'VotoAmmissione', 'DifferenzaPunteggio', 'VotoQualifica', 'VotoEsameSc1Qual', 'VotoEsameSc2Qual', 'VotoEsameOrQual', 'StatoPrivatista', 'Foto', 'Rappresentante', 'ObbligoFormativo', 'IdLingua1', 'IdLingua2', 'IdLingua3', 'IdLingua4', 'IdLingua5', 'IdProvenienzaScolastica', 'IdScuolaMedia', 'LinguaScuolaMedia', 'LinguaScuolaMedia2', 'GiudizioScuolaMedia', 'Trasporto', 'DataIscrizione', 'Pei', 'AmmessoEsameQualifica', 'AmmessoEsameQuinta', 'GiudizioAmmissioneQuinta', 'GradoHandicap', 'TipoHandicap', 'StatoLicenzaMaestro', 'IdStudenteSissi', 'BadgeRfid', 'Lode', 'DistrettoScolastico', 'GiudizioAmmissioneTerza', 'EsitoPrimaMedia', 'EsitoSecondaMedia', 'EsitoTerzaMedia', 'GiudizioEsameSc1Qual', 'GiudizioEsameSc2Qual', 'GiudizioEsameOrQual', 'GiudizioComplessivoEsameQual', 'AcconsenteAziende', 'CurriculumPrima', 'CurriculumSeconda', 'StageProfessionali', 'DataOrale', 'OrdineEsameOrale', 'TipoPrimoScritto', 'TipoSecondoScritto', 'TipoTerzoScritto', 'UnanimitaPrimoScritto', 'UnanimitaSecondoScritto', 'UnanimitaTerzoScritto', 'ArgomentoSceltoOrale', 'AreaDisc1Orale', 'AreaDisc2Orale', 'DiscElaboratiOrale', 'UnanimitaVotoFinale', 'PresenteEsameQuinta', 'StampaBadge', 'IdClasseDestinazione', 'ScontoRette', 'CartaStudenteNumero', 'CartaStudenteScadenza', 'EsitoCorrenteCalcolato', 'IdFlusso', 'DataAggiornamentoSogei', 'CodiceAlunnoMinisteriale', 'FlagCfFittizio', 'FlagS2f', 'CodiceStatoSogei', 'CodiceGruppoNomade', 'FlagMinoreStraniero', 'Chiave', 'VotoEsameMedieItaliano', 'VotoEsameMedieInglese', 'VotoEsameMedieMatematica', 'VotoEsameMedieSecondaLingua', 'VotoEsameMedieInvalsiIta', 'VotoEsameMedieInvalsiMat', 'VotoEsameMedieOrale', 'VotoAmmissioneMedie', 'EsitoPrimaElementare', 'EsitoSecondaElementare', 'EsitoTerzaElementare', 'EsitoQuartaElementare', 'EsitoQuintaElementare', 'TipoVotoEsameMedieItaliano', 'TipoVotoEsameMedieInglese', 'Giudizio1Medie', 'Giudizio2Medie', 'Giudizio3Medie', 'ArgomentiOraliMedie', 'GiudizioFinale1Medie', 'GiudizioFinale2Medie', 'GiudizioFinale3Medie', 'ConsiglioTerzaMedia', 'GiudizioSinteticoEsameTerzaMedia', 'DataArrivoInItalia', 'FrequenzaAsiloNido', 'FrequenzaScuolaMaterna', 'DataAggiornamentoSidi', 'CmpSupValIta', 'CmpSupTxtIta', 'CmpSupValIng', 'CmpSupTxtIng', 'CmpSupValAltri', 'CmpSupTxtAltri', 'CmpSupValMat', 'CmpSupTxtMat', 'CmpSupValSciTec', 'CmpSupTxtSciTec', 'CmpSupValStoSoc', 'CmpSupTxtStoSoc', 'CmpMedValIta', 'CmpMedTxtIta', 'CmpMedValIng', 'CmpMedTxtIng', 'CmpMedValAltri', 'CmpMedTxtAltri', 'CmpMedValMat', 'CmpMedTxtMat', 'CmpMedValSciTec', 'CmpMedTxtSciTec', 'CmpMedValStoSoc', 'CmpMedTxtStoSoc', 'CmpMedValL2', 'CmpMedTxtL2', 'CmpMedValL3', 'CmpMedTxtL3', 'CmpMedValArte', 'CmpMedTxtArte', 'CmpMedValMus', 'CmpMedTxtMus', 'CmpMedValMot', 'CmpMedTxtMot', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('idStudente', 'nome', 'cognome', 'indirizzo', 'citta', 'cap', 'provincia', 'sesso', 'telefono', 'cellulare1', 'cellulare2', 'email1', 'email2', 'invioEmail', 'invioEmailCumulativo', 'invioEmailParametrico', 'invioEmailTemporale', 'tipoSms', 'tipoSmsCumulativo', 'tipoSmsParametrico', 'tipoSmsTemporale', 'autEntrataRitardo', 'autUscitaAnticipo', 'autPomeriggio', 'acconsente', 'ritirato', 'dataNascita', 'codiceStudente', 'passwordStudente', 'codiceGiustificazioniStudente', 'esoneroReligione', 'materiaSostitutivaReligione', 'esoneroEdFisica', 'materiaSostitutivaEdfisica', 'creditiTerza', 'mediaVotiTerza', 'debitiTerza', 'creditiSospesiTerza', 'creditiReintegratiTerza', 'creditiQuarta', 'mediaVotiQuarta', 'debitiQuarta', 'creditiSospesiQuarta', 'creditiReintegratiQuarta', 'creditiQuinta', 'mediaVotiQuinta', 'creditiFinaliAgg', 'matricola', 'luogoNascita', 'provinciaNascita', 'motiviCreditiTerza', 'motiviCreditiQuarta', 'motiviCreditiQuinta', 'motiviCreditiAgg', 'codiceComuneNascita', 'statoNascita', 'cittadinanza', 'secondaCittadinanza', 'codiceComuneResidenza', 'distretto', 'codiceFiscale', 'medico', 'telefonoMedico', 'intolleranzeAlim', 'gruppoSanguigno', 'gruppoRh', 'codiceAsl', 'annotazioni', 'statoCivile', 'votoPrimoScritto', 'votoSecondoScritto', 'votoTerzoScritto', 'votoOrale', 'votoBonus', 'materiaSecondoScr', 'ulterioriSpecifDiploma', 'numeroDiploma', 'chiInserisce', 'dataInserimento', 'tipoInserimento', 'chiModifica', 'dataModifica', 'tipoModifica', 'flagCanc', 'statoAvanzamento', 'dataStatoAvanzamento', 'capProvinciaNascita', 'badge', 'capResidenza', 'codiceComuneDomicilio', 'capDomicilio', 'capNascita', 'indirizzoDomicilio', 'cittaNascitaStraniera', 'cellulareAllievo', 'handicap', 'statoConvittore', 'dataRitiro', 'votoAmmissione', 'differenzaPunteggio', 'votoQualifica', 'votoEsameSc1Qual', 'votoEsameSc2Qual', 'votoEsameOrQual', 'statoPrivatista', 'foto', 'rappresentante', 'obbligoFormativo', 'idLingua1', 'idLingua2', 'idLingua3', 'idLingua4', 'idLingua5', 'idProvenienzaScolastica', 'idScuolaMedia', 'linguaScuolaMedia', 'linguaScuolaMedia2', 'giudizioScuolaMedia', 'trasporto', 'dataIscrizione', 'pei', 'ammessoEsameQualifica', 'ammessoEsameQuinta', 'giudizioAmmissioneQuinta', 'gradoHandicap', 'tipoHandicap', 'statoLicenzaMaestro', 'idStudenteSissi', 'badgeRfid', 'lode', 'distrettoScolastico', 'giudizioAmmissioneTerza', 'esitoPrimaMedia', 'esitoSecondaMedia', 'esitoTerzaMedia', 'giudizioEsameSc1Qual', 'giudizioEsameSc2Qual', 'giudizioEsameOrQual', 'giudizioComplessivoEsameQual', 'acconsenteAziende', 'curriculumPrima', 'curriculumSeconda', 'stageProfessionali', 'dataOrale', 'ordineEsameOrale', 'tipoPrimoScritto', 'tipoSecondoScritto', 'tipoTerzoScritto', 'unanimitaPrimoScritto', 'unanimitaSecondoScritto', 'unanimitaTerzoScritto', 'argomentoSceltoOrale', 'areaDisc1Orale', 'areaDisc2Orale', 'discElaboratiOrale', 'unanimitaVotoFinale', 'presenteEsameQuinta', 'stampaBadge', 'idClasseDestinazione', 'scontoRette', 'cartaStudenteNumero', 'cartaStudenteScadenza', 'esitoCorrenteCalcolato', 'idFlusso', 'dataAggiornamentoSogei', 'codiceAlunnoMinisteriale', 'flagCfFittizio', 'flagS2f', 'codiceStatoSogei', 'codiceGruppoNomade', 'flagMinoreStraniero', 'chiave', 'votoEsameMedieItaliano', 'votoEsameMedieInglese', 'votoEsameMedieMatematica', 'votoEsameMedieSecondaLingua', 'votoEsameMedieInvalsiIta', 'votoEsameMedieInvalsiMat', 'votoEsameMedieOrale', 'votoAmmissioneMedie', 'esitoPrimaElementare', 'esitoSecondaElementare', 'esitoTerzaElementare', 'esitoQuartaElementare', 'esitoQuintaElementare', 'tipoVotoEsameMedieItaliano', 'tipoVotoEsameMedieInglese', 'giudizio1Medie', 'giudizio2Medie', 'giudizio3Medie', 'argomentiOraliMedie', 'giudizioFinale1Medie', 'giudizioFinale2Medie', 'giudizioFinale3Medie', 'consiglioTerzaMedia', 'giudizioSinteticoEsameTerzaMedia', 'dataArrivoInItalia', 'frequenzaAsiloNido', 'frequenzaScuolaMaterna', 'dataAggiornamentoSidi', 'cmpSupValIta', 'cmpSupTxtIta', 'cmpSupValIng', 'cmpSupTxtIng', 'cmpSupValAltri', 'cmpSupTxtAltri', 'cmpSupValMat', 'cmpSupTxtMat', 'cmpSupValSciTec', 'cmpSupTxtSciTec', 'cmpSupValStoSoc', 'cmpSupTxtStoSoc', 'cmpMedValIta', 'cmpMedTxtIta', 'cmpMedValIng', 'cmpMedTxtIng', 'cmpMedValAltri', 'cmpMedTxtAltri', 'cmpMedValMat', 'cmpMedTxtMat', 'cmpMedValSciTec', 'cmpMedTxtSciTec', 'cmpMedValStoSoc', 'cmpMedTxtStoSoc', 'cmpMedValL2', 'cmpMedTxtL2', 'cmpMedValL3', 'cmpMedTxtL3', 'cmpMedValArte', 'cmpMedTxtArte', 'cmpMedValMus', 'cmpMedTxtMus', 'cmpMedValMot', 'cmpMedTxtMot', ),
        BasePeer::TYPE_COLNAME => array (StudentiCompletiPeer::ID_STUDENTE, StudentiCompletiPeer::NOME, StudentiCompletiPeer::COGNOME, StudentiCompletiPeer::INDIRIZZO, StudentiCompletiPeer::CITTA, StudentiCompletiPeer::CAP, StudentiCompletiPeer::PROVINCIA, StudentiCompletiPeer::SESSO, StudentiCompletiPeer::TELEFONO, StudentiCompletiPeer::CELLULARE1, StudentiCompletiPeer::CELLULARE2, StudentiCompletiPeer::EMAIL1, StudentiCompletiPeer::EMAIL2, StudentiCompletiPeer::INVIO_EMAIL, StudentiCompletiPeer::INVIO_EMAIL_CUMULATIVO, StudentiCompletiPeer::INVIO_EMAIL_PARAMETRICO, StudentiCompletiPeer::INVIO_EMAIL_TEMPORALE, StudentiCompletiPeer::TIPO_SMS, StudentiCompletiPeer::TIPO_SMS_CUMULATIVO, StudentiCompletiPeer::TIPO_SMS_PARAMETRICO, StudentiCompletiPeer::TIPO_SMS_TEMPORALE, StudentiCompletiPeer::AUT_ENTRATA_RITARDO, StudentiCompletiPeer::AUT_USCITA_ANTICIPO, StudentiCompletiPeer::AUT_POMERIGGIO, StudentiCompletiPeer::ACCONSENTE, StudentiCompletiPeer::RITIRATO, StudentiCompletiPeer::DATA_NASCITA, StudentiCompletiPeer::CODICE_STUDENTE, StudentiCompletiPeer::PASSWORD_STUDENTE, StudentiCompletiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE, StudentiCompletiPeer::ESONERO_RELIGIONE, StudentiCompletiPeer::MATERIA_SOSTITUTIVA_RELIGIONE, StudentiCompletiPeer::ESONERO_ED_FISICA, StudentiCompletiPeer::MATERIA_SOSTITUTIVA_EDFISICA, StudentiCompletiPeer::CREDITI_TERZA, StudentiCompletiPeer::MEDIA_VOTI_TERZA, StudentiCompletiPeer::DEBITI_TERZA, StudentiCompletiPeer::CREDITI_SOSPESI_TERZA, StudentiCompletiPeer::CREDITI_REINTEGRATI_TERZA, StudentiCompletiPeer::CREDITI_QUARTA, StudentiCompletiPeer::MEDIA_VOTI_QUARTA, StudentiCompletiPeer::DEBITI_QUARTA, StudentiCompletiPeer::CREDITI_SOSPESI_QUARTA, StudentiCompletiPeer::CREDITI_REINTEGRATI_QUARTA, StudentiCompletiPeer::CREDITI_QUINTA, StudentiCompletiPeer::MEDIA_VOTI_QUINTA, StudentiCompletiPeer::CREDITI_FINALI_AGG, StudentiCompletiPeer::MATRICOLA, StudentiCompletiPeer::LUOGO_NASCITA, StudentiCompletiPeer::PROVINCIA_NASCITA, StudentiCompletiPeer::MOTIVI_CREDITI_TERZA, StudentiCompletiPeer::MOTIVI_CREDITI_QUARTA, StudentiCompletiPeer::MOTIVI_CREDITI_QUINTA, StudentiCompletiPeer::MOTIVI_CREDITI_AGG, StudentiCompletiPeer::CODICE_COMUNE_NASCITA, StudentiCompletiPeer::STATO_NASCITA, StudentiCompletiPeer::CITTADINANZA, StudentiCompletiPeer::SECONDA_CITTADINANZA, StudentiCompletiPeer::CODICE_COMUNE_RESIDENZA, StudentiCompletiPeer::DISTRETTO, StudentiCompletiPeer::CODICE_FISCALE, StudentiCompletiPeer::MEDICO, StudentiCompletiPeer::TELEFONO_MEDICO, StudentiCompletiPeer::INTOLLERANZE_ALIM, StudentiCompletiPeer::GRUPPO_SANGUIGNO, StudentiCompletiPeer::GRUPPO_RH, StudentiCompletiPeer::CODICE_ASL, StudentiCompletiPeer::ANNOTAZIONI, StudentiCompletiPeer::STATO_CIVILE, StudentiCompletiPeer::VOTO_PRIMO_SCRITTO, StudentiCompletiPeer::VOTO_SECONDO_SCRITTO, StudentiCompletiPeer::VOTO_TERZO_SCRITTO, StudentiCompletiPeer::VOTO_ORALE, StudentiCompletiPeer::VOTO_BONUS, StudentiCompletiPeer::MATERIA_SECONDO_SCR, StudentiCompletiPeer::ULTERIORI_SPECIF_DIPLOMA, StudentiCompletiPeer::NUMERO_DIPLOMA, StudentiCompletiPeer::CHI_INSERISCE, StudentiCompletiPeer::DATA_INSERIMENTO, StudentiCompletiPeer::TIPO_INSERIMENTO, StudentiCompletiPeer::CHI_MODIFICA, StudentiCompletiPeer::DATA_MODIFICA, StudentiCompletiPeer::TIPO_MODIFICA, StudentiCompletiPeer::FLAG_CANC, StudentiCompletiPeer::STATO_AVANZAMENTO, StudentiCompletiPeer::DATA_STATO_AVANZAMENTO, StudentiCompletiPeer::CAP_PROVINCIA_NASCITA, StudentiCompletiPeer::BADGE, StudentiCompletiPeer::CAP_RESIDENZA, StudentiCompletiPeer::CODICE_COMUNE_DOMICILIO, StudentiCompletiPeer::CAP_DOMICILIO, StudentiCompletiPeer::CAP_NASCITA, StudentiCompletiPeer::INDIRIZZO_DOMICILIO, StudentiCompletiPeer::CITTA_NASCITA_STRANIERA, StudentiCompletiPeer::CELLULARE_ALLIEVO, StudentiCompletiPeer::HANDICAP, StudentiCompletiPeer::STATO_CONVITTORE, StudentiCompletiPeer::DATA_RITIRO, StudentiCompletiPeer::VOTO_AMMISSIONE, StudentiCompletiPeer::DIFFERENZA_PUNTEGGIO, StudentiCompletiPeer::VOTO_QUALIFICA, StudentiCompletiPeer::VOTO_ESAME_SC1_QUAL, StudentiCompletiPeer::VOTO_ESAME_SC2_QUAL, StudentiCompletiPeer::VOTO_ESAME_OR_QUAL, StudentiCompletiPeer::STATO_PRIVATISTA, StudentiCompletiPeer::FOTO, StudentiCompletiPeer::RAPPRESENTANTE, StudentiCompletiPeer::OBBLIGO_FORMATIVO, StudentiCompletiPeer::ID_LINGUA_1, StudentiCompletiPeer::ID_LINGUA_2, StudentiCompletiPeer::ID_LINGUA_3, StudentiCompletiPeer::ID_LINGUA_4, StudentiCompletiPeer::ID_LINGUA_5, StudentiCompletiPeer::ID_PROVENIENZA_SCOLASTICA, StudentiCompletiPeer::ID_SCUOLA_MEDIA, StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA, StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA_2, StudentiCompletiPeer::GIUDIZIO_SCUOLA_MEDIA, StudentiCompletiPeer::TRASPORTO, StudentiCompletiPeer::DATA_ISCRIZIONE, StudentiCompletiPeer::PEI, StudentiCompletiPeer::AMMESSO_ESAME_QUALIFICA, StudentiCompletiPeer::AMMESSO_ESAME_QUINTA, StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_QUINTA, StudentiCompletiPeer::GRADO_HANDICAP, StudentiCompletiPeer::TIPO_HANDICAP, StudentiCompletiPeer::STATO_LICENZA_MAESTRO, StudentiCompletiPeer::ID_STUDENTE_SISSI, StudentiCompletiPeer::BADGE_RFID, StudentiCompletiPeer::LODE, StudentiCompletiPeer::DISTRETTO_SCOLASTICO, StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_TERZA, StudentiCompletiPeer::ESITO_PRIMA_MEDIA, StudentiCompletiPeer::ESITO_SECONDA_MEDIA, StudentiCompletiPeer::ESITO_TERZA_MEDIA, StudentiCompletiPeer::GIUDIZIO_ESAME_SC1_QUAL, StudentiCompletiPeer::GIUDIZIO_ESAME_SC2_QUAL, StudentiCompletiPeer::GIUDIZIO_ESAME_OR_QUAL, StudentiCompletiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL, StudentiCompletiPeer::ACCONSENTE_AZIENDE, StudentiCompletiPeer::CURRICULUM_PRIMA, StudentiCompletiPeer::CURRICULUM_SECONDA, StudentiCompletiPeer::STAGE_PROFESSIONALI, StudentiCompletiPeer::DATA_ORALE, StudentiCompletiPeer::ORDINE_ESAME_ORALE, StudentiCompletiPeer::TIPO_PRIMO_SCRITTO, StudentiCompletiPeer::TIPO_SECONDO_SCRITTO, StudentiCompletiPeer::TIPO_TERZO_SCRITTO, StudentiCompletiPeer::UNANIMITA_PRIMO_SCRITTO, StudentiCompletiPeer::UNANIMITA_SECONDO_SCRITTO, StudentiCompletiPeer::UNANIMITA_TERZO_SCRITTO, StudentiCompletiPeer::ARGOMENTO_SCELTO_ORALE, StudentiCompletiPeer::AREA_DISC_1_ORALE, StudentiCompletiPeer::AREA_DISC_2_ORALE, StudentiCompletiPeer::DISC_ELABORATI_ORALE, StudentiCompletiPeer::UNANIMITA_VOTO_FINALE, StudentiCompletiPeer::PRESENTE_ESAME_QUINTA, StudentiCompletiPeer::STAMPA_BADGE, StudentiCompletiPeer::ID_CLASSE_DESTINAZIONE, StudentiCompletiPeer::SCONTO_RETTE, StudentiCompletiPeer::CARTA_STUDENTE_NUMERO, StudentiCompletiPeer::CARTA_STUDENTE_SCADENZA, StudentiCompletiPeer::ESITO_CORRENTE_CALCOLATO, StudentiCompletiPeer::ID_FLUSSO, StudentiCompletiPeer::DATA_AGGIORNAMENTO_SOGEI, StudentiCompletiPeer::CODICE_ALUNNO_MINISTERIALE, StudentiCompletiPeer::FLAG_CF_FITTIZIO, StudentiCompletiPeer::FLAG_S2F, StudentiCompletiPeer::CODICE_STATO_SOGEI, StudentiCompletiPeer::CODICE_GRUPPO_NOMADE, StudentiCompletiPeer::FLAG_MINORE_STRANIERO, StudentiCompletiPeer::CHIAVE, StudentiCompletiPeer::VOTO_ESAME_MEDIE_ITALIANO, StudentiCompletiPeer::VOTO_ESAME_MEDIE_INGLESE, StudentiCompletiPeer::VOTO_ESAME_MEDIE_MATEMATICA, StudentiCompletiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA, StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA, StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT, StudentiCompletiPeer::VOTO_ESAME_MEDIE_ORALE, StudentiCompletiPeer::VOTO_AMMISSIONE_MEDIE, StudentiCompletiPeer::ESITO_PRIMA_ELEMENTARE, StudentiCompletiPeer::ESITO_SECONDA_ELEMENTARE, StudentiCompletiPeer::ESITO_TERZA_ELEMENTARE, StudentiCompletiPeer::ESITO_QUARTA_ELEMENTARE, StudentiCompletiPeer::ESITO_QUINTA_ELEMENTARE, StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO, StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE, StudentiCompletiPeer::GIUDIZIO_1_MEDIE, StudentiCompletiPeer::GIUDIZIO_2_MEDIE, StudentiCompletiPeer::GIUDIZIO_3_MEDIE, StudentiCompletiPeer::ARGOMENTI_ORALI_MEDIE, StudentiCompletiPeer::GIUDIZIO_FINALE_1_MEDIE, StudentiCompletiPeer::GIUDIZIO_FINALE_2_MEDIE, StudentiCompletiPeer::GIUDIZIO_FINALE_3_MEDIE, StudentiCompletiPeer::CONSIGLIO_TERZA_MEDIA, StudentiCompletiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA, StudentiCompletiPeer::DATA_ARRIVO_IN_ITALIA, StudentiCompletiPeer::FREQUENZA_ASILO_NIDO, StudentiCompletiPeer::FREQUENZA_SCUOLA_MATERNA, StudentiCompletiPeer::DATA_AGGIORNAMENTO_SIDI, StudentiCompletiPeer::CMP_SUP_VAL_ITA, StudentiCompletiPeer::CMP_SUP_TXT_ITA, StudentiCompletiPeer::CMP_SUP_VAL_ING, StudentiCompletiPeer::CMP_SUP_TXT_ING, StudentiCompletiPeer::CMP_SUP_VAL_ALTRI, StudentiCompletiPeer::CMP_SUP_TXT_ALTRI, StudentiCompletiPeer::CMP_SUP_VAL_MAT, StudentiCompletiPeer::CMP_SUP_TXT_MAT, StudentiCompletiPeer::CMP_SUP_VAL_SCI_TEC, StudentiCompletiPeer::CMP_SUP_TXT_SCI_TEC, StudentiCompletiPeer::CMP_SUP_VAL_STO_SOC, StudentiCompletiPeer::CMP_SUP_TXT_STO_SOC, StudentiCompletiPeer::CMP_MED_VAL_ITA, StudentiCompletiPeer::CMP_MED_TXT_ITA, StudentiCompletiPeer::CMP_MED_VAL_ING, StudentiCompletiPeer::CMP_MED_TXT_ING, StudentiCompletiPeer::CMP_MED_VAL_ALTRI, StudentiCompletiPeer::CMP_MED_TXT_ALTRI, StudentiCompletiPeer::CMP_MED_VAL_MAT, StudentiCompletiPeer::CMP_MED_TXT_MAT, StudentiCompletiPeer::CMP_MED_VAL_SCI_TEC, StudentiCompletiPeer::CMP_MED_TXT_SCI_TEC, StudentiCompletiPeer::CMP_MED_VAL_STO_SOC, StudentiCompletiPeer::CMP_MED_TXT_STO_SOC, StudentiCompletiPeer::CMP_MED_VAL_L2, StudentiCompletiPeer::CMP_MED_TXT_L2, StudentiCompletiPeer::CMP_MED_VAL_L3, StudentiCompletiPeer::CMP_MED_TXT_L3, StudentiCompletiPeer::CMP_MED_VAL_ARTE, StudentiCompletiPeer::CMP_MED_TXT_ARTE, StudentiCompletiPeer::CMP_MED_VAL_MUS, StudentiCompletiPeer::CMP_MED_TXT_MUS, StudentiCompletiPeer::CMP_MED_VAL_MOT, StudentiCompletiPeer::CMP_MED_TXT_MOT, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID_STUDENTE', 'NOME', 'COGNOME', 'INDIRIZZO', 'CITTA', 'CAP', 'PROVINCIA', 'SESSO', 'TELEFONO', 'CELLULARE1', 'CELLULARE2', 'EMAIL1', 'EMAIL2', 'INVIO_EMAIL', 'INVIO_EMAIL_CUMULATIVO', 'INVIO_EMAIL_PARAMETRICO', 'INVIO_EMAIL_TEMPORALE', 'TIPO_SMS', 'TIPO_SMS_CUMULATIVO', 'TIPO_SMS_PARAMETRICO', 'TIPO_SMS_TEMPORALE', 'AUT_ENTRATA_RITARDO', 'AUT_USCITA_ANTICIPO', 'AUT_POMERIGGIO', 'ACCONSENTE', 'RITIRATO', 'DATA_NASCITA', 'CODICE_STUDENTE', 'PASSWORD_STUDENTE', 'CODICE_GIUSTIFICAZIONI_STUDENTE', 'ESONERO_RELIGIONE', 'MATERIA_SOSTITUTIVA_RELIGIONE', 'ESONERO_ED_FISICA', 'MATERIA_SOSTITUTIVA_EDFISICA', 'CREDITI_TERZA', 'MEDIA_VOTI_TERZA', 'DEBITI_TERZA', 'CREDITI_SOSPESI_TERZA', 'CREDITI_REINTEGRATI_TERZA', 'CREDITI_QUARTA', 'MEDIA_VOTI_QUARTA', 'DEBITI_QUARTA', 'CREDITI_SOSPESI_QUARTA', 'CREDITI_REINTEGRATI_QUARTA', 'CREDITI_QUINTA', 'MEDIA_VOTI_QUINTA', 'CREDITI_FINALI_AGG', 'MATRICOLA', 'LUOGO_NASCITA', 'PROVINCIA_NASCITA', 'MOTIVI_CREDITI_TERZA', 'MOTIVI_CREDITI_QUARTA', 'MOTIVI_CREDITI_QUINTA', 'MOTIVI_CREDITI_AGG', 'CODICE_COMUNE_NASCITA', 'STATO_NASCITA', 'CITTADINANZA', 'SECONDA_CITTADINANZA', 'CODICE_COMUNE_RESIDENZA', 'DISTRETTO', 'CODICE_FISCALE', 'MEDICO', 'TELEFONO_MEDICO', 'INTOLLERANZE_ALIM', 'GRUPPO_SANGUIGNO', 'GRUPPO_RH', 'CODICE_ASL', 'ANNOTAZIONI', 'STATO_CIVILE', 'VOTO_PRIMO_SCRITTO', 'VOTO_SECONDO_SCRITTO', 'VOTO_TERZO_SCRITTO', 'VOTO_ORALE', 'VOTO_BONUS', 'MATERIA_SECONDO_SCR', 'ULTERIORI_SPECIF_DIPLOMA', 'NUMERO_DIPLOMA', 'CHI_INSERISCE', 'DATA_INSERIMENTO', 'TIPO_INSERIMENTO', 'CHI_MODIFICA', 'DATA_MODIFICA', 'TIPO_MODIFICA', 'FLAG_CANC', 'STATO_AVANZAMENTO', 'DATA_STATO_AVANZAMENTO', 'CAP_PROVINCIA_NASCITA', 'BADGE', 'CAP_RESIDENZA', 'CODICE_COMUNE_DOMICILIO', 'CAP_DOMICILIO', 'CAP_NASCITA', 'INDIRIZZO_DOMICILIO', 'CITTA_NASCITA_STRANIERA', 'CELLULARE_ALLIEVO', 'HANDICAP', 'STATO_CONVITTORE', 'DATA_RITIRO', 'VOTO_AMMISSIONE', 'DIFFERENZA_PUNTEGGIO', 'VOTO_QUALIFICA', 'VOTO_ESAME_SC1_QUAL', 'VOTO_ESAME_SC2_QUAL', 'VOTO_ESAME_OR_QUAL', 'STATO_PRIVATISTA', 'FOTO', 'RAPPRESENTANTE', 'OBBLIGO_FORMATIVO', 'ID_LINGUA_1', 'ID_LINGUA_2', 'ID_LINGUA_3', 'ID_LINGUA_4', 'ID_LINGUA_5', 'ID_PROVENIENZA_SCOLASTICA', 'ID_SCUOLA_MEDIA', 'LINGUA_SCUOLA_MEDIA', 'LINGUA_SCUOLA_MEDIA_2', 'GIUDIZIO_SCUOLA_MEDIA', 'TRASPORTO', 'DATA_ISCRIZIONE', 'PEI', 'AMMESSO_ESAME_QUALIFICA', 'AMMESSO_ESAME_QUINTA', 'GIUDIZIO_AMMISSIONE_QUINTA', 'GRADO_HANDICAP', 'TIPO_HANDICAP', 'STATO_LICENZA_MAESTRO', 'ID_STUDENTE_SISSI', 'BADGE_RFID', 'LODE', 'DISTRETTO_SCOLASTICO', 'GIUDIZIO_AMMISSIONE_TERZA', 'ESITO_PRIMA_MEDIA', 'ESITO_SECONDA_MEDIA', 'ESITO_TERZA_MEDIA', 'GIUDIZIO_ESAME_SC1_QUAL', 'GIUDIZIO_ESAME_SC2_QUAL', 'GIUDIZIO_ESAME_OR_QUAL', 'GIUDIZIO_COMPLESSIVO_ESAME_QUAL', 'ACCONSENTE_AZIENDE', 'CURRICULUM_PRIMA', 'CURRICULUM_SECONDA', 'STAGE_PROFESSIONALI', 'DATA_ORALE', 'ORDINE_ESAME_ORALE', 'TIPO_PRIMO_SCRITTO', 'TIPO_SECONDO_SCRITTO', 'TIPO_TERZO_SCRITTO', 'UNANIMITA_PRIMO_SCRITTO', 'UNANIMITA_SECONDO_SCRITTO', 'UNANIMITA_TERZO_SCRITTO', 'ARGOMENTO_SCELTO_ORALE', 'AREA_DISC_1_ORALE', 'AREA_DISC_2_ORALE', 'DISC_ELABORATI_ORALE', 'UNANIMITA_VOTO_FINALE', 'PRESENTE_ESAME_QUINTA', 'STAMPA_BADGE', 'ID_CLASSE_DESTINAZIONE', 'SCONTO_RETTE', 'CARTA_STUDENTE_NUMERO', 'CARTA_STUDENTE_SCADENZA', 'ESITO_CORRENTE_CALCOLATO', 'ID_FLUSSO', 'DATA_AGGIORNAMENTO_SOGEI', 'CODICE_ALUNNO_MINISTERIALE', 'FLAG_CF_FITTIZIO', 'FLAG_S2F', 'CODICE_STATO_SOGEI', 'CODICE_GRUPPO_NOMADE', 'FLAG_MINORE_STRANIERO', 'CHIAVE', 'VOTO_ESAME_MEDIE_ITALIANO', 'VOTO_ESAME_MEDIE_INGLESE', 'VOTO_ESAME_MEDIE_MATEMATICA', 'VOTO_ESAME_MEDIE_SECONDA_LINGUA', 'VOTO_ESAME_MEDIE_INVALSI_ITA', 'VOTO_ESAME_MEDIE_INVALSI_MAT', 'VOTO_ESAME_MEDIE_ORALE', 'VOTO_AMMISSIONE_MEDIE', 'ESITO_PRIMA_ELEMENTARE', 'ESITO_SECONDA_ELEMENTARE', 'ESITO_TERZA_ELEMENTARE', 'ESITO_QUARTA_ELEMENTARE', 'ESITO_QUINTA_ELEMENTARE', 'TIPO_VOTO_ESAME_MEDIE_ITALIANO', 'TIPO_VOTO_ESAME_MEDIE_INGLESE', 'GIUDIZIO_1_MEDIE', 'GIUDIZIO_2_MEDIE', 'GIUDIZIO_3_MEDIE', 'ARGOMENTI_ORALI_MEDIE', 'GIUDIZIO_FINALE_1_MEDIE', 'GIUDIZIO_FINALE_2_MEDIE', 'GIUDIZIO_FINALE_3_MEDIE', 'CONSIGLIO_TERZA_MEDIA', 'GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA', 'DATA_ARRIVO_IN_ITALIA', 'FREQUENZA_ASILO_NIDO', 'FREQUENZA_SCUOLA_MATERNA', 'DATA_AGGIORNAMENTO_SIDI', 'CMP_SUP_VAL_ITA', 'CMP_SUP_TXT_ITA', 'CMP_SUP_VAL_ING', 'CMP_SUP_TXT_ING', 'CMP_SUP_VAL_ALTRI', 'CMP_SUP_TXT_ALTRI', 'CMP_SUP_VAL_MAT', 'CMP_SUP_TXT_MAT', 'CMP_SUP_VAL_SCI_TEC', 'CMP_SUP_TXT_SCI_TEC', 'CMP_SUP_VAL_STO_SOC', 'CMP_SUP_TXT_STO_SOC', 'CMP_MED_VAL_ITA', 'CMP_MED_TXT_ITA', 'CMP_MED_VAL_ING', 'CMP_MED_TXT_ING', 'CMP_MED_VAL_ALTRI', 'CMP_MED_TXT_ALTRI', 'CMP_MED_VAL_MAT', 'CMP_MED_TXT_MAT', 'CMP_MED_VAL_SCI_TEC', 'CMP_MED_TXT_SCI_TEC', 'CMP_MED_VAL_STO_SOC', 'CMP_MED_TXT_STO_SOC', 'CMP_MED_VAL_L2', 'CMP_MED_TXT_L2', 'CMP_MED_VAL_L3', 'CMP_MED_TXT_L3', 'CMP_MED_VAL_ARTE', 'CMP_MED_TXT_ARTE', 'CMP_MED_VAL_MUS', 'CMP_MED_TXT_MUS', 'CMP_MED_VAL_MOT', 'CMP_MED_TXT_MOT', ),
        BasePeer::TYPE_FIELDNAME => array ('id_studente', 'nome', 'cognome', 'indirizzo', 'citta', 'cap', 'provincia', 'sesso', 'telefono', 'cellulare1', 'cellulare2', 'email1', 'email2', 'invio_email', 'invio_email_cumulativo', 'invio_email_parametrico', 'invio_email_temporale', 'tipo_sms', 'tipo_sms_cumulativo', 'tipo_sms_parametrico', 'tipo_sms_temporale', 'aut_entrata_ritardo', 'aut_uscita_anticipo', 'aut_pomeriggio', 'acconsente', 'ritirato', 'data_nascita', 'codice_studente', 'password_studente', 'codice_giustificazioni_studente', 'esonero_religione', 'materia_sostitutiva_religione', 'esonero_ed_fisica', 'materia_sostitutiva_edfisica', 'crediti_terza', 'media_voti_terza', 'debiti_terza', 'crediti_sospesi_terza', 'crediti_reintegrati_terza', 'crediti_quarta', 'media_voti_quarta', 'debiti_quarta', 'crediti_sospesi_quarta', 'crediti_reintegrati_quarta', 'crediti_quinta', 'media_voti_quinta', 'crediti_finali_agg', 'matricola', 'luogo_nascita', 'provincia_nascita', 'motivi_crediti_terza', 'motivi_crediti_quarta', 'motivi_crediti_quinta', 'motivi_crediti_agg', 'codice_comune_nascita', 'stato_nascita', 'cittadinanza', 'seconda_cittadinanza', 'codice_comune_residenza', 'distretto', 'codice_fiscale', 'medico', 'telefono_medico', 'intolleranze_alim', 'gruppo_sanguigno', 'gruppo_rh', 'codice_asl', 'annotazioni', 'stato_civile', 'voto_primo_scritto', 'voto_secondo_scritto', 'voto_terzo_scritto', 'voto_orale', 'voto_bonus', 'materia_secondo_scr', 'ulteriori_specif_diploma', 'numero_diploma', 'chi_inserisce', 'data_inserimento', 'tipo_inserimento', 'chi_modifica', 'data_modifica', 'tipo_modifica', 'flag_canc', 'stato_avanzamento', 'data_stato_avanzamento', 'cap_provincia_nascita', 'badge', 'cap_residenza', 'codice_comune_domicilio', 'cap_domicilio', 'cap_nascita', 'indirizzo_domicilio', 'citta_nascita_straniera', 'cellulare_allievo', 'handicap', 'stato_convittore', 'data_ritiro', 'voto_ammissione', 'differenza_punteggio', 'voto_qualifica', 'voto_esame_sc1_qual', 'voto_esame_sc2_qual', 'voto_esame_or_qual', 'stato_privatista', 'foto', 'rappresentante', 'obbligo_formativo', 'id_lingua_1', 'id_lingua_2', 'id_lingua_3', 'id_lingua_4', 'id_lingua_5', 'id_provenienza_scolastica', 'id_scuola_media', 'lingua_scuola_media', 'lingua_scuola_media_2', 'giudizio_scuola_media', 'trasporto', 'data_iscrizione', 'pei', 'ammesso_esame_qualifica', 'ammesso_esame_quinta', 'giudizio_ammissione_quinta', 'grado_handicap', 'tipo_handicap', 'stato_licenza_maestro', 'id_studente_sissi', 'badge_rfid', 'lode', 'distretto_scolastico', 'giudizio_ammissione_terza', 'esito_prima_media', 'esito_seconda_media', 'esito_terza_media', 'giudizio_esame_sc1_qual', 'giudizio_esame_sc2_qual', 'giudizio_esame_or_qual', 'giudizio_complessivo_esame_qual', 'acconsente_aziende', 'curriculum_prima', 'curriculum_seconda', 'stage_professionali', 'data_orale', 'ordine_esame_orale', 'tipo_primo_scritto', 'tipo_secondo_scritto', 'tipo_terzo_scritto', 'unanimita_primo_scritto', 'unanimita_secondo_scritto', 'unanimita_terzo_scritto', 'argomento_scelto_orale', 'area_disc_1_orale', 'area_disc_2_orale', 'disc_elaborati_orale', 'unanimita_voto_finale', 'presente_esame_quinta', 'stampa_badge', 'id_classe_destinazione', 'sconto_rette', 'carta_studente_numero', 'carta_studente_scadenza', 'esito_corrente_calcolato', 'id_flusso', 'data_aggiornamento_sogei', 'codice_alunno_ministeriale', 'flag_cf_fittizio', 'flag_s2f', 'codice_stato_sogei', 'codice_gruppo_nomade', 'flag_minore_straniero', 'chiave', 'voto_esame_medie_italiano', 'voto_esame_medie_inglese', 'voto_esame_medie_matematica', 'voto_esame_medie_seconda_lingua', 'voto_esame_medie_invalsi_ita', 'voto_esame_medie_invalsi_mat', 'voto_esame_medie_orale', 'voto_ammissione_medie', 'esito_prima_elementare', 'esito_seconda_elementare', 'esito_terza_elementare', 'esito_quarta_elementare', 'esito_quinta_elementare', 'tipo_voto_esame_medie_italiano', 'tipo_voto_esame_medie_inglese', 'giudizio_1_medie', 'giudizio_2_medie', 'giudizio_3_medie', 'argomenti_orali_medie', 'giudizio_finale_1_medie', 'giudizio_finale_2_medie', 'giudizio_finale_3_medie', 'consiglio_terza_media', 'giudizio_sintetico_esame_terza_media', 'data_arrivo_in_italia', 'frequenza_asilo_nido', 'frequenza_scuola_materna', 'data_aggiornamento_sidi', 'cmp_sup_val_ita', 'cmp_sup_txt_ita', 'cmp_sup_val_ing', 'cmp_sup_txt_ing', 'cmp_sup_val_altri', 'cmp_sup_txt_altri', 'cmp_sup_val_mat', 'cmp_sup_txt_mat', 'cmp_sup_val_sci_tec', 'cmp_sup_txt_sci_tec', 'cmp_sup_val_sto_soc', 'cmp_sup_txt_sto_soc', 'cmp_med_val_ita', 'cmp_med_txt_ita', 'cmp_med_val_ing', 'cmp_med_txt_ing', 'cmp_med_val_altri', 'cmp_med_txt_altri', 'cmp_med_val_mat', 'cmp_med_txt_mat', 'cmp_med_val_sci_tec', 'cmp_med_txt_sci_tec', 'cmp_med_val_sto_soc', 'cmp_med_txt_sto_soc', 'cmp_med_val_l2', 'cmp_med_txt_l2', 'cmp_med_val_l3', 'cmp_med_txt_l3', 'cmp_med_val_arte', 'cmp_med_txt_arte', 'cmp_med_val_mus', 'cmp_med_txt_mus', 'cmp_med_val_mot', 'cmp_med_txt_mot', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. StudentiCompletiPeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('IdStudente' => 0, 'Nome' => 1, 'Cognome' => 2, 'Indirizzo' => 3, 'Citta' => 4, 'Cap' => 5, 'Provincia' => 6, 'Sesso' => 7, 'Telefono' => 8, 'Cellulare1' => 9, 'Cellulare2' => 10, 'Email1' => 11, 'Email2' => 12, 'InvioEmail' => 13, 'InvioEmailCumulativo' => 14, 'InvioEmailParametrico' => 15, 'InvioEmailTemporale' => 16, 'TipoSms' => 17, 'TipoSmsCumulativo' => 18, 'TipoSmsParametrico' => 19, 'TipoSmsTemporale' => 20, 'AutEntrataRitardo' => 21, 'AutUscitaAnticipo' => 22, 'AutPomeriggio' => 23, 'Acconsente' => 24, 'Ritirato' => 25, 'DataNascita' => 26, 'CodiceStudente' => 27, 'PasswordStudente' => 28, 'CodiceGiustificazioniStudente' => 29, 'EsoneroReligione' => 30, 'MateriaSostitutivaReligione' => 31, 'EsoneroEdFisica' => 32, 'MateriaSostitutivaEdfisica' => 33, 'CreditiTerza' => 34, 'MediaVotiTerza' => 35, 'DebitiTerza' => 36, 'CreditiSospesiTerza' => 37, 'CreditiReintegratiTerza' => 38, 'CreditiQuarta' => 39, 'MediaVotiQuarta' => 40, 'DebitiQuarta' => 41, 'CreditiSospesiQuarta' => 42, 'CreditiReintegratiQuarta' => 43, 'CreditiQuinta' => 44, 'MediaVotiQuinta' => 45, 'CreditiFinaliAgg' => 46, 'Matricola' => 47, 'LuogoNascita' => 48, 'ProvinciaNascita' => 49, 'MotiviCreditiTerza' => 50, 'MotiviCreditiQuarta' => 51, 'MotiviCreditiQuinta' => 52, 'MotiviCreditiAgg' => 53, 'CodiceComuneNascita' => 54, 'StatoNascita' => 55, 'Cittadinanza' => 56, 'SecondaCittadinanza' => 57, 'CodiceComuneResidenza' => 58, 'Distretto' => 59, 'CodiceFiscale' => 60, 'Medico' => 61, 'TelefonoMedico' => 62, 'IntolleranzeAlim' => 63, 'GruppoSanguigno' => 64, 'GruppoRh' => 65, 'CodiceAsl' => 66, 'Annotazioni' => 67, 'StatoCivile' => 68, 'VotoPrimoScritto' => 69, 'VotoSecondoScritto' => 70, 'VotoTerzoScritto' => 71, 'VotoOrale' => 72, 'VotoBonus' => 73, 'MateriaSecondoScr' => 74, 'UlterioriSpecifDiploma' => 75, 'NumeroDiploma' => 76, 'ChiInserisce' => 77, 'DataInserimento' => 78, 'TipoInserimento' => 79, 'ChiModifica' => 80, 'DataModifica' => 81, 'TipoModifica' => 82, 'FlagCanc' => 83, 'StatoAvanzamento' => 84, 'DataStatoAvanzamento' => 85, 'CapProvinciaNascita' => 86, 'Badge' => 87, 'CapResidenza' => 88, 'CodiceComuneDomicilio' => 89, 'CapDomicilio' => 90, 'CapNascita' => 91, 'IndirizzoDomicilio' => 92, 'CittaNascitaStraniera' => 93, 'CellulareAllievo' => 94, 'Handicap' => 95, 'StatoConvittore' => 96, 'DataRitiro' => 97, 'VotoAmmissione' => 98, 'DifferenzaPunteggio' => 99, 'VotoQualifica' => 100, 'VotoEsameSc1Qual' => 101, 'VotoEsameSc2Qual' => 102, 'VotoEsameOrQual' => 103, 'StatoPrivatista' => 104, 'Foto' => 105, 'Rappresentante' => 106, 'ObbligoFormativo' => 107, 'IdLingua1' => 108, 'IdLingua2' => 109, 'IdLingua3' => 110, 'IdLingua4' => 111, 'IdLingua5' => 112, 'IdProvenienzaScolastica' => 113, 'IdScuolaMedia' => 114, 'LinguaScuolaMedia' => 115, 'LinguaScuolaMedia2' => 116, 'GiudizioScuolaMedia' => 117, 'Trasporto' => 118, 'DataIscrizione' => 119, 'Pei' => 120, 'AmmessoEsameQualifica' => 121, 'AmmessoEsameQuinta' => 122, 'GiudizioAmmissioneQuinta' => 123, 'GradoHandicap' => 124, 'TipoHandicap' => 125, 'StatoLicenzaMaestro' => 126, 'IdStudenteSissi' => 127, 'BadgeRfid' => 128, 'Lode' => 129, 'DistrettoScolastico' => 130, 'GiudizioAmmissioneTerza' => 131, 'EsitoPrimaMedia' => 132, 'EsitoSecondaMedia' => 133, 'EsitoTerzaMedia' => 134, 'GiudizioEsameSc1Qual' => 135, 'GiudizioEsameSc2Qual' => 136, 'GiudizioEsameOrQual' => 137, 'GiudizioComplessivoEsameQual' => 138, 'AcconsenteAziende' => 139, 'CurriculumPrima' => 140, 'CurriculumSeconda' => 141, 'StageProfessionali' => 142, 'DataOrale' => 143, 'OrdineEsameOrale' => 144, 'TipoPrimoScritto' => 145, 'TipoSecondoScritto' => 146, 'TipoTerzoScritto' => 147, 'UnanimitaPrimoScritto' => 148, 'UnanimitaSecondoScritto' => 149, 'UnanimitaTerzoScritto' => 150, 'ArgomentoSceltoOrale' => 151, 'AreaDisc1Orale' => 152, 'AreaDisc2Orale' => 153, 'DiscElaboratiOrale' => 154, 'UnanimitaVotoFinale' => 155, 'PresenteEsameQuinta' => 156, 'StampaBadge' => 157, 'IdClasseDestinazione' => 158, 'ScontoRette' => 159, 'CartaStudenteNumero' => 160, 'CartaStudenteScadenza' => 161, 'EsitoCorrenteCalcolato' => 162, 'IdFlusso' => 163, 'DataAggiornamentoSogei' => 164, 'CodiceAlunnoMinisteriale' => 165, 'FlagCfFittizio' => 166, 'FlagS2f' => 167, 'CodiceStatoSogei' => 168, 'CodiceGruppoNomade' => 169, 'FlagMinoreStraniero' => 170, 'Chiave' => 171, 'VotoEsameMedieItaliano' => 172, 'VotoEsameMedieInglese' => 173, 'VotoEsameMedieMatematica' => 174, 'VotoEsameMedieSecondaLingua' => 175, 'VotoEsameMedieInvalsiIta' => 176, 'VotoEsameMedieInvalsiMat' => 177, 'VotoEsameMedieOrale' => 178, 'VotoAmmissioneMedie' => 179, 'EsitoPrimaElementare' => 180, 'EsitoSecondaElementare' => 181, 'EsitoTerzaElementare' => 182, 'EsitoQuartaElementare' => 183, 'EsitoQuintaElementare' => 184, 'TipoVotoEsameMedieItaliano' => 185, 'TipoVotoEsameMedieInglese' => 186, 'Giudizio1Medie' => 187, 'Giudizio2Medie' => 188, 'Giudizio3Medie' => 189, 'ArgomentiOraliMedie' => 190, 'GiudizioFinale1Medie' => 191, 'GiudizioFinale2Medie' => 192, 'GiudizioFinale3Medie' => 193, 'ConsiglioTerzaMedia' => 194, 'GiudizioSinteticoEsameTerzaMedia' => 195, 'DataArrivoInItalia' => 196, 'FrequenzaAsiloNido' => 197, 'FrequenzaScuolaMaterna' => 198, 'DataAggiornamentoSidi' => 199, 'CmpSupValIta' => 200, 'CmpSupTxtIta' => 201, 'CmpSupValIng' => 202, 'CmpSupTxtIng' => 203, 'CmpSupValAltri' => 204, 'CmpSupTxtAltri' => 205, 'CmpSupValMat' => 206, 'CmpSupTxtMat' => 207, 'CmpSupValSciTec' => 208, 'CmpSupTxtSciTec' => 209, 'CmpSupValStoSoc' => 210, 'CmpSupTxtStoSoc' => 211, 'CmpMedValIta' => 212, 'CmpMedTxtIta' => 213, 'CmpMedValIng' => 214, 'CmpMedTxtIng' => 215, 'CmpMedValAltri' => 216, 'CmpMedTxtAltri' => 217, 'CmpMedValMat' => 218, 'CmpMedTxtMat' => 219, 'CmpMedValSciTec' => 220, 'CmpMedTxtSciTec' => 221, 'CmpMedValStoSoc' => 222, 'CmpMedTxtStoSoc' => 223, 'CmpMedValL2' => 224, 'CmpMedTxtL2' => 225, 'CmpMedValL3' => 226, 'CmpMedTxtL3' => 227, 'CmpMedValArte' => 228, 'CmpMedTxtArte' => 229, 'CmpMedValMus' => 230, 'CmpMedTxtMus' => 231, 'CmpMedValMot' => 232, 'CmpMedTxtMot' => 233, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('idStudente' => 0, 'nome' => 1, 'cognome' => 2, 'indirizzo' => 3, 'citta' => 4, 'cap' => 5, 'provincia' => 6, 'sesso' => 7, 'telefono' => 8, 'cellulare1' => 9, 'cellulare2' => 10, 'email1' => 11, 'email2' => 12, 'invioEmail' => 13, 'invioEmailCumulativo' => 14, 'invioEmailParametrico' => 15, 'invioEmailTemporale' => 16, 'tipoSms' => 17, 'tipoSmsCumulativo' => 18, 'tipoSmsParametrico' => 19, 'tipoSmsTemporale' => 20, 'autEntrataRitardo' => 21, 'autUscitaAnticipo' => 22, 'autPomeriggio' => 23, 'acconsente' => 24, 'ritirato' => 25, 'dataNascita' => 26, 'codiceStudente' => 27, 'passwordStudente' => 28, 'codiceGiustificazioniStudente' => 29, 'esoneroReligione' => 30, 'materiaSostitutivaReligione' => 31, 'esoneroEdFisica' => 32, 'materiaSostitutivaEdfisica' => 33, 'creditiTerza' => 34, 'mediaVotiTerza' => 35, 'debitiTerza' => 36, 'creditiSospesiTerza' => 37, 'creditiReintegratiTerza' => 38, 'creditiQuarta' => 39, 'mediaVotiQuarta' => 40, 'debitiQuarta' => 41, 'creditiSospesiQuarta' => 42, 'creditiReintegratiQuarta' => 43, 'creditiQuinta' => 44, 'mediaVotiQuinta' => 45, 'creditiFinaliAgg' => 46, 'matricola' => 47, 'luogoNascita' => 48, 'provinciaNascita' => 49, 'motiviCreditiTerza' => 50, 'motiviCreditiQuarta' => 51, 'motiviCreditiQuinta' => 52, 'motiviCreditiAgg' => 53, 'codiceComuneNascita' => 54, 'statoNascita' => 55, 'cittadinanza' => 56, 'secondaCittadinanza' => 57, 'codiceComuneResidenza' => 58, 'distretto' => 59, 'codiceFiscale' => 60, 'medico' => 61, 'telefonoMedico' => 62, 'intolleranzeAlim' => 63, 'gruppoSanguigno' => 64, 'gruppoRh' => 65, 'codiceAsl' => 66, 'annotazioni' => 67, 'statoCivile' => 68, 'votoPrimoScritto' => 69, 'votoSecondoScritto' => 70, 'votoTerzoScritto' => 71, 'votoOrale' => 72, 'votoBonus' => 73, 'materiaSecondoScr' => 74, 'ulterioriSpecifDiploma' => 75, 'numeroDiploma' => 76, 'chiInserisce' => 77, 'dataInserimento' => 78, 'tipoInserimento' => 79, 'chiModifica' => 80, 'dataModifica' => 81, 'tipoModifica' => 82, 'flagCanc' => 83, 'statoAvanzamento' => 84, 'dataStatoAvanzamento' => 85, 'capProvinciaNascita' => 86, 'badge' => 87, 'capResidenza' => 88, 'codiceComuneDomicilio' => 89, 'capDomicilio' => 90, 'capNascita' => 91, 'indirizzoDomicilio' => 92, 'cittaNascitaStraniera' => 93, 'cellulareAllievo' => 94, 'handicap' => 95, 'statoConvittore' => 96, 'dataRitiro' => 97, 'votoAmmissione' => 98, 'differenzaPunteggio' => 99, 'votoQualifica' => 100, 'votoEsameSc1Qual' => 101, 'votoEsameSc2Qual' => 102, 'votoEsameOrQual' => 103, 'statoPrivatista' => 104, 'foto' => 105, 'rappresentante' => 106, 'obbligoFormativo' => 107, 'idLingua1' => 108, 'idLingua2' => 109, 'idLingua3' => 110, 'idLingua4' => 111, 'idLingua5' => 112, 'idProvenienzaScolastica' => 113, 'idScuolaMedia' => 114, 'linguaScuolaMedia' => 115, 'linguaScuolaMedia2' => 116, 'giudizioScuolaMedia' => 117, 'trasporto' => 118, 'dataIscrizione' => 119, 'pei' => 120, 'ammessoEsameQualifica' => 121, 'ammessoEsameQuinta' => 122, 'giudizioAmmissioneQuinta' => 123, 'gradoHandicap' => 124, 'tipoHandicap' => 125, 'statoLicenzaMaestro' => 126, 'idStudenteSissi' => 127, 'badgeRfid' => 128, 'lode' => 129, 'distrettoScolastico' => 130, 'giudizioAmmissioneTerza' => 131, 'esitoPrimaMedia' => 132, 'esitoSecondaMedia' => 133, 'esitoTerzaMedia' => 134, 'giudizioEsameSc1Qual' => 135, 'giudizioEsameSc2Qual' => 136, 'giudizioEsameOrQual' => 137, 'giudizioComplessivoEsameQual' => 138, 'acconsenteAziende' => 139, 'curriculumPrima' => 140, 'curriculumSeconda' => 141, 'stageProfessionali' => 142, 'dataOrale' => 143, 'ordineEsameOrale' => 144, 'tipoPrimoScritto' => 145, 'tipoSecondoScritto' => 146, 'tipoTerzoScritto' => 147, 'unanimitaPrimoScritto' => 148, 'unanimitaSecondoScritto' => 149, 'unanimitaTerzoScritto' => 150, 'argomentoSceltoOrale' => 151, 'areaDisc1Orale' => 152, 'areaDisc2Orale' => 153, 'discElaboratiOrale' => 154, 'unanimitaVotoFinale' => 155, 'presenteEsameQuinta' => 156, 'stampaBadge' => 157, 'idClasseDestinazione' => 158, 'scontoRette' => 159, 'cartaStudenteNumero' => 160, 'cartaStudenteScadenza' => 161, 'esitoCorrenteCalcolato' => 162, 'idFlusso' => 163, 'dataAggiornamentoSogei' => 164, 'codiceAlunnoMinisteriale' => 165, 'flagCfFittizio' => 166, 'flagS2f' => 167, 'codiceStatoSogei' => 168, 'codiceGruppoNomade' => 169, 'flagMinoreStraniero' => 170, 'chiave' => 171, 'votoEsameMedieItaliano' => 172, 'votoEsameMedieInglese' => 173, 'votoEsameMedieMatematica' => 174, 'votoEsameMedieSecondaLingua' => 175, 'votoEsameMedieInvalsiIta' => 176, 'votoEsameMedieInvalsiMat' => 177, 'votoEsameMedieOrale' => 178, 'votoAmmissioneMedie' => 179, 'esitoPrimaElementare' => 180, 'esitoSecondaElementare' => 181, 'esitoTerzaElementare' => 182, 'esitoQuartaElementare' => 183, 'esitoQuintaElementare' => 184, 'tipoVotoEsameMedieItaliano' => 185, 'tipoVotoEsameMedieInglese' => 186, 'giudizio1Medie' => 187, 'giudizio2Medie' => 188, 'giudizio3Medie' => 189, 'argomentiOraliMedie' => 190, 'giudizioFinale1Medie' => 191, 'giudizioFinale2Medie' => 192, 'giudizioFinale3Medie' => 193, 'consiglioTerzaMedia' => 194, 'giudizioSinteticoEsameTerzaMedia' => 195, 'dataArrivoInItalia' => 196, 'frequenzaAsiloNido' => 197, 'frequenzaScuolaMaterna' => 198, 'dataAggiornamentoSidi' => 199, 'cmpSupValIta' => 200, 'cmpSupTxtIta' => 201, 'cmpSupValIng' => 202, 'cmpSupTxtIng' => 203, 'cmpSupValAltri' => 204, 'cmpSupTxtAltri' => 205, 'cmpSupValMat' => 206, 'cmpSupTxtMat' => 207, 'cmpSupValSciTec' => 208, 'cmpSupTxtSciTec' => 209, 'cmpSupValStoSoc' => 210, 'cmpSupTxtStoSoc' => 211, 'cmpMedValIta' => 212, 'cmpMedTxtIta' => 213, 'cmpMedValIng' => 214, 'cmpMedTxtIng' => 215, 'cmpMedValAltri' => 216, 'cmpMedTxtAltri' => 217, 'cmpMedValMat' => 218, 'cmpMedTxtMat' => 219, 'cmpMedValSciTec' => 220, 'cmpMedTxtSciTec' => 221, 'cmpMedValStoSoc' => 222, 'cmpMedTxtStoSoc' => 223, 'cmpMedValL2' => 224, 'cmpMedTxtL2' => 225, 'cmpMedValL3' => 226, 'cmpMedTxtL3' => 227, 'cmpMedValArte' => 228, 'cmpMedTxtArte' => 229, 'cmpMedValMus' => 230, 'cmpMedTxtMus' => 231, 'cmpMedValMot' => 232, 'cmpMedTxtMot' => 233, ),
        BasePeer::TYPE_COLNAME => array (StudentiCompletiPeer::ID_STUDENTE => 0, StudentiCompletiPeer::NOME => 1, StudentiCompletiPeer::COGNOME => 2, StudentiCompletiPeer::INDIRIZZO => 3, StudentiCompletiPeer::CITTA => 4, StudentiCompletiPeer::CAP => 5, StudentiCompletiPeer::PROVINCIA => 6, StudentiCompletiPeer::SESSO => 7, StudentiCompletiPeer::TELEFONO => 8, StudentiCompletiPeer::CELLULARE1 => 9, StudentiCompletiPeer::CELLULARE2 => 10, StudentiCompletiPeer::EMAIL1 => 11, StudentiCompletiPeer::EMAIL2 => 12, StudentiCompletiPeer::INVIO_EMAIL => 13, StudentiCompletiPeer::INVIO_EMAIL_CUMULATIVO => 14, StudentiCompletiPeer::INVIO_EMAIL_PARAMETRICO => 15, StudentiCompletiPeer::INVIO_EMAIL_TEMPORALE => 16, StudentiCompletiPeer::TIPO_SMS => 17, StudentiCompletiPeer::TIPO_SMS_CUMULATIVO => 18, StudentiCompletiPeer::TIPO_SMS_PARAMETRICO => 19, StudentiCompletiPeer::TIPO_SMS_TEMPORALE => 20, StudentiCompletiPeer::AUT_ENTRATA_RITARDO => 21, StudentiCompletiPeer::AUT_USCITA_ANTICIPO => 22, StudentiCompletiPeer::AUT_POMERIGGIO => 23, StudentiCompletiPeer::ACCONSENTE => 24, StudentiCompletiPeer::RITIRATO => 25, StudentiCompletiPeer::DATA_NASCITA => 26, StudentiCompletiPeer::CODICE_STUDENTE => 27, StudentiCompletiPeer::PASSWORD_STUDENTE => 28, StudentiCompletiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE => 29, StudentiCompletiPeer::ESONERO_RELIGIONE => 30, StudentiCompletiPeer::MATERIA_SOSTITUTIVA_RELIGIONE => 31, StudentiCompletiPeer::ESONERO_ED_FISICA => 32, StudentiCompletiPeer::MATERIA_SOSTITUTIVA_EDFISICA => 33, StudentiCompletiPeer::CREDITI_TERZA => 34, StudentiCompletiPeer::MEDIA_VOTI_TERZA => 35, StudentiCompletiPeer::DEBITI_TERZA => 36, StudentiCompletiPeer::CREDITI_SOSPESI_TERZA => 37, StudentiCompletiPeer::CREDITI_REINTEGRATI_TERZA => 38, StudentiCompletiPeer::CREDITI_QUARTA => 39, StudentiCompletiPeer::MEDIA_VOTI_QUARTA => 40, StudentiCompletiPeer::DEBITI_QUARTA => 41, StudentiCompletiPeer::CREDITI_SOSPESI_QUARTA => 42, StudentiCompletiPeer::CREDITI_REINTEGRATI_QUARTA => 43, StudentiCompletiPeer::CREDITI_QUINTA => 44, StudentiCompletiPeer::MEDIA_VOTI_QUINTA => 45, StudentiCompletiPeer::CREDITI_FINALI_AGG => 46, StudentiCompletiPeer::MATRICOLA => 47, StudentiCompletiPeer::LUOGO_NASCITA => 48, StudentiCompletiPeer::PROVINCIA_NASCITA => 49, StudentiCompletiPeer::MOTIVI_CREDITI_TERZA => 50, StudentiCompletiPeer::MOTIVI_CREDITI_QUARTA => 51, StudentiCompletiPeer::MOTIVI_CREDITI_QUINTA => 52, StudentiCompletiPeer::MOTIVI_CREDITI_AGG => 53, StudentiCompletiPeer::CODICE_COMUNE_NASCITA => 54, StudentiCompletiPeer::STATO_NASCITA => 55, StudentiCompletiPeer::CITTADINANZA => 56, StudentiCompletiPeer::SECONDA_CITTADINANZA => 57, StudentiCompletiPeer::CODICE_COMUNE_RESIDENZA => 58, StudentiCompletiPeer::DISTRETTO => 59, StudentiCompletiPeer::CODICE_FISCALE => 60, StudentiCompletiPeer::MEDICO => 61, StudentiCompletiPeer::TELEFONO_MEDICO => 62, StudentiCompletiPeer::INTOLLERANZE_ALIM => 63, StudentiCompletiPeer::GRUPPO_SANGUIGNO => 64, StudentiCompletiPeer::GRUPPO_RH => 65, StudentiCompletiPeer::CODICE_ASL => 66, StudentiCompletiPeer::ANNOTAZIONI => 67, StudentiCompletiPeer::STATO_CIVILE => 68, StudentiCompletiPeer::VOTO_PRIMO_SCRITTO => 69, StudentiCompletiPeer::VOTO_SECONDO_SCRITTO => 70, StudentiCompletiPeer::VOTO_TERZO_SCRITTO => 71, StudentiCompletiPeer::VOTO_ORALE => 72, StudentiCompletiPeer::VOTO_BONUS => 73, StudentiCompletiPeer::MATERIA_SECONDO_SCR => 74, StudentiCompletiPeer::ULTERIORI_SPECIF_DIPLOMA => 75, StudentiCompletiPeer::NUMERO_DIPLOMA => 76, StudentiCompletiPeer::CHI_INSERISCE => 77, StudentiCompletiPeer::DATA_INSERIMENTO => 78, StudentiCompletiPeer::TIPO_INSERIMENTO => 79, StudentiCompletiPeer::CHI_MODIFICA => 80, StudentiCompletiPeer::DATA_MODIFICA => 81, StudentiCompletiPeer::TIPO_MODIFICA => 82, StudentiCompletiPeer::FLAG_CANC => 83, StudentiCompletiPeer::STATO_AVANZAMENTO => 84, StudentiCompletiPeer::DATA_STATO_AVANZAMENTO => 85, StudentiCompletiPeer::CAP_PROVINCIA_NASCITA => 86, StudentiCompletiPeer::BADGE => 87, StudentiCompletiPeer::CAP_RESIDENZA => 88, StudentiCompletiPeer::CODICE_COMUNE_DOMICILIO => 89, StudentiCompletiPeer::CAP_DOMICILIO => 90, StudentiCompletiPeer::CAP_NASCITA => 91, StudentiCompletiPeer::INDIRIZZO_DOMICILIO => 92, StudentiCompletiPeer::CITTA_NASCITA_STRANIERA => 93, StudentiCompletiPeer::CELLULARE_ALLIEVO => 94, StudentiCompletiPeer::HANDICAP => 95, StudentiCompletiPeer::STATO_CONVITTORE => 96, StudentiCompletiPeer::DATA_RITIRO => 97, StudentiCompletiPeer::VOTO_AMMISSIONE => 98, StudentiCompletiPeer::DIFFERENZA_PUNTEGGIO => 99, StudentiCompletiPeer::VOTO_QUALIFICA => 100, StudentiCompletiPeer::VOTO_ESAME_SC1_QUAL => 101, StudentiCompletiPeer::VOTO_ESAME_SC2_QUAL => 102, StudentiCompletiPeer::VOTO_ESAME_OR_QUAL => 103, StudentiCompletiPeer::STATO_PRIVATISTA => 104, StudentiCompletiPeer::FOTO => 105, StudentiCompletiPeer::RAPPRESENTANTE => 106, StudentiCompletiPeer::OBBLIGO_FORMATIVO => 107, StudentiCompletiPeer::ID_LINGUA_1 => 108, StudentiCompletiPeer::ID_LINGUA_2 => 109, StudentiCompletiPeer::ID_LINGUA_3 => 110, StudentiCompletiPeer::ID_LINGUA_4 => 111, StudentiCompletiPeer::ID_LINGUA_5 => 112, StudentiCompletiPeer::ID_PROVENIENZA_SCOLASTICA => 113, StudentiCompletiPeer::ID_SCUOLA_MEDIA => 114, StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA => 115, StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA_2 => 116, StudentiCompletiPeer::GIUDIZIO_SCUOLA_MEDIA => 117, StudentiCompletiPeer::TRASPORTO => 118, StudentiCompletiPeer::DATA_ISCRIZIONE => 119, StudentiCompletiPeer::PEI => 120, StudentiCompletiPeer::AMMESSO_ESAME_QUALIFICA => 121, StudentiCompletiPeer::AMMESSO_ESAME_QUINTA => 122, StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_QUINTA => 123, StudentiCompletiPeer::GRADO_HANDICAP => 124, StudentiCompletiPeer::TIPO_HANDICAP => 125, StudentiCompletiPeer::STATO_LICENZA_MAESTRO => 126, StudentiCompletiPeer::ID_STUDENTE_SISSI => 127, StudentiCompletiPeer::BADGE_RFID => 128, StudentiCompletiPeer::LODE => 129, StudentiCompletiPeer::DISTRETTO_SCOLASTICO => 130, StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_TERZA => 131, StudentiCompletiPeer::ESITO_PRIMA_MEDIA => 132, StudentiCompletiPeer::ESITO_SECONDA_MEDIA => 133, StudentiCompletiPeer::ESITO_TERZA_MEDIA => 134, StudentiCompletiPeer::GIUDIZIO_ESAME_SC1_QUAL => 135, StudentiCompletiPeer::GIUDIZIO_ESAME_SC2_QUAL => 136, StudentiCompletiPeer::GIUDIZIO_ESAME_OR_QUAL => 137, StudentiCompletiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL => 138, StudentiCompletiPeer::ACCONSENTE_AZIENDE => 139, StudentiCompletiPeer::CURRICULUM_PRIMA => 140, StudentiCompletiPeer::CURRICULUM_SECONDA => 141, StudentiCompletiPeer::STAGE_PROFESSIONALI => 142, StudentiCompletiPeer::DATA_ORALE => 143, StudentiCompletiPeer::ORDINE_ESAME_ORALE => 144, StudentiCompletiPeer::TIPO_PRIMO_SCRITTO => 145, StudentiCompletiPeer::TIPO_SECONDO_SCRITTO => 146, StudentiCompletiPeer::TIPO_TERZO_SCRITTO => 147, StudentiCompletiPeer::UNANIMITA_PRIMO_SCRITTO => 148, StudentiCompletiPeer::UNANIMITA_SECONDO_SCRITTO => 149, StudentiCompletiPeer::UNANIMITA_TERZO_SCRITTO => 150, StudentiCompletiPeer::ARGOMENTO_SCELTO_ORALE => 151, StudentiCompletiPeer::AREA_DISC_1_ORALE => 152, StudentiCompletiPeer::AREA_DISC_2_ORALE => 153, StudentiCompletiPeer::DISC_ELABORATI_ORALE => 154, StudentiCompletiPeer::UNANIMITA_VOTO_FINALE => 155, StudentiCompletiPeer::PRESENTE_ESAME_QUINTA => 156, StudentiCompletiPeer::STAMPA_BADGE => 157, StudentiCompletiPeer::ID_CLASSE_DESTINAZIONE => 158, StudentiCompletiPeer::SCONTO_RETTE => 159, StudentiCompletiPeer::CARTA_STUDENTE_NUMERO => 160, StudentiCompletiPeer::CARTA_STUDENTE_SCADENZA => 161, StudentiCompletiPeer::ESITO_CORRENTE_CALCOLATO => 162, StudentiCompletiPeer::ID_FLUSSO => 163, StudentiCompletiPeer::DATA_AGGIORNAMENTO_SOGEI => 164, StudentiCompletiPeer::CODICE_ALUNNO_MINISTERIALE => 165, StudentiCompletiPeer::FLAG_CF_FITTIZIO => 166, StudentiCompletiPeer::FLAG_S2F => 167, StudentiCompletiPeer::CODICE_STATO_SOGEI => 168, StudentiCompletiPeer::CODICE_GRUPPO_NOMADE => 169, StudentiCompletiPeer::FLAG_MINORE_STRANIERO => 170, StudentiCompletiPeer::CHIAVE => 171, StudentiCompletiPeer::VOTO_ESAME_MEDIE_ITALIANO => 172, StudentiCompletiPeer::VOTO_ESAME_MEDIE_INGLESE => 173, StudentiCompletiPeer::VOTO_ESAME_MEDIE_MATEMATICA => 174, StudentiCompletiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA => 175, StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA => 176, StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT => 177, StudentiCompletiPeer::VOTO_ESAME_MEDIE_ORALE => 178, StudentiCompletiPeer::VOTO_AMMISSIONE_MEDIE => 179, StudentiCompletiPeer::ESITO_PRIMA_ELEMENTARE => 180, StudentiCompletiPeer::ESITO_SECONDA_ELEMENTARE => 181, StudentiCompletiPeer::ESITO_TERZA_ELEMENTARE => 182, StudentiCompletiPeer::ESITO_QUARTA_ELEMENTARE => 183, StudentiCompletiPeer::ESITO_QUINTA_ELEMENTARE => 184, StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO => 185, StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE => 186, StudentiCompletiPeer::GIUDIZIO_1_MEDIE => 187, StudentiCompletiPeer::GIUDIZIO_2_MEDIE => 188, StudentiCompletiPeer::GIUDIZIO_3_MEDIE => 189, StudentiCompletiPeer::ARGOMENTI_ORALI_MEDIE => 190, StudentiCompletiPeer::GIUDIZIO_FINALE_1_MEDIE => 191, StudentiCompletiPeer::GIUDIZIO_FINALE_2_MEDIE => 192, StudentiCompletiPeer::GIUDIZIO_FINALE_3_MEDIE => 193, StudentiCompletiPeer::CONSIGLIO_TERZA_MEDIA => 194, StudentiCompletiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA => 195, StudentiCompletiPeer::DATA_ARRIVO_IN_ITALIA => 196, StudentiCompletiPeer::FREQUENZA_ASILO_NIDO => 197, StudentiCompletiPeer::FREQUENZA_SCUOLA_MATERNA => 198, StudentiCompletiPeer::DATA_AGGIORNAMENTO_SIDI => 199, StudentiCompletiPeer::CMP_SUP_VAL_ITA => 200, StudentiCompletiPeer::CMP_SUP_TXT_ITA => 201, StudentiCompletiPeer::CMP_SUP_VAL_ING => 202, StudentiCompletiPeer::CMP_SUP_TXT_ING => 203, StudentiCompletiPeer::CMP_SUP_VAL_ALTRI => 204, StudentiCompletiPeer::CMP_SUP_TXT_ALTRI => 205, StudentiCompletiPeer::CMP_SUP_VAL_MAT => 206, StudentiCompletiPeer::CMP_SUP_TXT_MAT => 207, StudentiCompletiPeer::CMP_SUP_VAL_SCI_TEC => 208, StudentiCompletiPeer::CMP_SUP_TXT_SCI_TEC => 209, StudentiCompletiPeer::CMP_SUP_VAL_STO_SOC => 210, StudentiCompletiPeer::CMP_SUP_TXT_STO_SOC => 211, StudentiCompletiPeer::CMP_MED_VAL_ITA => 212, StudentiCompletiPeer::CMP_MED_TXT_ITA => 213, StudentiCompletiPeer::CMP_MED_VAL_ING => 214, StudentiCompletiPeer::CMP_MED_TXT_ING => 215, StudentiCompletiPeer::CMP_MED_VAL_ALTRI => 216, StudentiCompletiPeer::CMP_MED_TXT_ALTRI => 217, StudentiCompletiPeer::CMP_MED_VAL_MAT => 218, StudentiCompletiPeer::CMP_MED_TXT_MAT => 219, StudentiCompletiPeer::CMP_MED_VAL_SCI_TEC => 220, StudentiCompletiPeer::CMP_MED_TXT_SCI_TEC => 221, StudentiCompletiPeer::CMP_MED_VAL_STO_SOC => 222, StudentiCompletiPeer::CMP_MED_TXT_STO_SOC => 223, StudentiCompletiPeer::CMP_MED_VAL_L2 => 224, StudentiCompletiPeer::CMP_MED_TXT_L2 => 225, StudentiCompletiPeer::CMP_MED_VAL_L3 => 226, StudentiCompletiPeer::CMP_MED_TXT_L3 => 227, StudentiCompletiPeer::CMP_MED_VAL_ARTE => 228, StudentiCompletiPeer::CMP_MED_TXT_ARTE => 229, StudentiCompletiPeer::CMP_MED_VAL_MUS => 230, StudentiCompletiPeer::CMP_MED_TXT_MUS => 231, StudentiCompletiPeer::CMP_MED_VAL_MOT => 232, StudentiCompletiPeer::CMP_MED_TXT_MOT => 233, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID_STUDENTE' => 0, 'NOME' => 1, 'COGNOME' => 2, 'INDIRIZZO' => 3, 'CITTA' => 4, 'CAP' => 5, 'PROVINCIA' => 6, 'SESSO' => 7, 'TELEFONO' => 8, 'CELLULARE1' => 9, 'CELLULARE2' => 10, 'EMAIL1' => 11, 'EMAIL2' => 12, 'INVIO_EMAIL' => 13, 'INVIO_EMAIL_CUMULATIVO' => 14, 'INVIO_EMAIL_PARAMETRICO' => 15, 'INVIO_EMAIL_TEMPORALE' => 16, 'TIPO_SMS' => 17, 'TIPO_SMS_CUMULATIVO' => 18, 'TIPO_SMS_PARAMETRICO' => 19, 'TIPO_SMS_TEMPORALE' => 20, 'AUT_ENTRATA_RITARDO' => 21, 'AUT_USCITA_ANTICIPO' => 22, 'AUT_POMERIGGIO' => 23, 'ACCONSENTE' => 24, 'RITIRATO' => 25, 'DATA_NASCITA' => 26, 'CODICE_STUDENTE' => 27, 'PASSWORD_STUDENTE' => 28, 'CODICE_GIUSTIFICAZIONI_STUDENTE' => 29, 'ESONERO_RELIGIONE' => 30, 'MATERIA_SOSTITUTIVA_RELIGIONE' => 31, 'ESONERO_ED_FISICA' => 32, 'MATERIA_SOSTITUTIVA_EDFISICA' => 33, 'CREDITI_TERZA' => 34, 'MEDIA_VOTI_TERZA' => 35, 'DEBITI_TERZA' => 36, 'CREDITI_SOSPESI_TERZA' => 37, 'CREDITI_REINTEGRATI_TERZA' => 38, 'CREDITI_QUARTA' => 39, 'MEDIA_VOTI_QUARTA' => 40, 'DEBITI_QUARTA' => 41, 'CREDITI_SOSPESI_QUARTA' => 42, 'CREDITI_REINTEGRATI_QUARTA' => 43, 'CREDITI_QUINTA' => 44, 'MEDIA_VOTI_QUINTA' => 45, 'CREDITI_FINALI_AGG' => 46, 'MATRICOLA' => 47, 'LUOGO_NASCITA' => 48, 'PROVINCIA_NASCITA' => 49, 'MOTIVI_CREDITI_TERZA' => 50, 'MOTIVI_CREDITI_QUARTA' => 51, 'MOTIVI_CREDITI_QUINTA' => 52, 'MOTIVI_CREDITI_AGG' => 53, 'CODICE_COMUNE_NASCITA' => 54, 'STATO_NASCITA' => 55, 'CITTADINANZA' => 56, 'SECONDA_CITTADINANZA' => 57, 'CODICE_COMUNE_RESIDENZA' => 58, 'DISTRETTO' => 59, 'CODICE_FISCALE' => 60, 'MEDICO' => 61, 'TELEFONO_MEDICO' => 62, 'INTOLLERANZE_ALIM' => 63, 'GRUPPO_SANGUIGNO' => 64, 'GRUPPO_RH' => 65, 'CODICE_ASL' => 66, 'ANNOTAZIONI' => 67, 'STATO_CIVILE' => 68, 'VOTO_PRIMO_SCRITTO' => 69, 'VOTO_SECONDO_SCRITTO' => 70, 'VOTO_TERZO_SCRITTO' => 71, 'VOTO_ORALE' => 72, 'VOTO_BONUS' => 73, 'MATERIA_SECONDO_SCR' => 74, 'ULTERIORI_SPECIF_DIPLOMA' => 75, 'NUMERO_DIPLOMA' => 76, 'CHI_INSERISCE' => 77, 'DATA_INSERIMENTO' => 78, 'TIPO_INSERIMENTO' => 79, 'CHI_MODIFICA' => 80, 'DATA_MODIFICA' => 81, 'TIPO_MODIFICA' => 82, 'FLAG_CANC' => 83, 'STATO_AVANZAMENTO' => 84, 'DATA_STATO_AVANZAMENTO' => 85, 'CAP_PROVINCIA_NASCITA' => 86, 'BADGE' => 87, 'CAP_RESIDENZA' => 88, 'CODICE_COMUNE_DOMICILIO' => 89, 'CAP_DOMICILIO' => 90, 'CAP_NASCITA' => 91, 'INDIRIZZO_DOMICILIO' => 92, 'CITTA_NASCITA_STRANIERA' => 93, 'CELLULARE_ALLIEVO' => 94, 'HANDICAP' => 95, 'STATO_CONVITTORE' => 96, 'DATA_RITIRO' => 97, 'VOTO_AMMISSIONE' => 98, 'DIFFERENZA_PUNTEGGIO' => 99, 'VOTO_QUALIFICA' => 100, 'VOTO_ESAME_SC1_QUAL' => 101, 'VOTO_ESAME_SC2_QUAL' => 102, 'VOTO_ESAME_OR_QUAL' => 103, 'STATO_PRIVATISTA' => 104, 'FOTO' => 105, 'RAPPRESENTANTE' => 106, 'OBBLIGO_FORMATIVO' => 107, 'ID_LINGUA_1' => 108, 'ID_LINGUA_2' => 109, 'ID_LINGUA_3' => 110, 'ID_LINGUA_4' => 111, 'ID_LINGUA_5' => 112, 'ID_PROVENIENZA_SCOLASTICA' => 113, 'ID_SCUOLA_MEDIA' => 114, 'LINGUA_SCUOLA_MEDIA' => 115, 'LINGUA_SCUOLA_MEDIA_2' => 116, 'GIUDIZIO_SCUOLA_MEDIA' => 117, 'TRASPORTO' => 118, 'DATA_ISCRIZIONE' => 119, 'PEI' => 120, 'AMMESSO_ESAME_QUALIFICA' => 121, 'AMMESSO_ESAME_QUINTA' => 122, 'GIUDIZIO_AMMISSIONE_QUINTA' => 123, 'GRADO_HANDICAP' => 124, 'TIPO_HANDICAP' => 125, 'STATO_LICENZA_MAESTRO' => 126, 'ID_STUDENTE_SISSI' => 127, 'BADGE_RFID' => 128, 'LODE' => 129, 'DISTRETTO_SCOLASTICO' => 130, 'GIUDIZIO_AMMISSIONE_TERZA' => 131, 'ESITO_PRIMA_MEDIA' => 132, 'ESITO_SECONDA_MEDIA' => 133, 'ESITO_TERZA_MEDIA' => 134, 'GIUDIZIO_ESAME_SC1_QUAL' => 135, 'GIUDIZIO_ESAME_SC2_QUAL' => 136, 'GIUDIZIO_ESAME_OR_QUAL' => 137, 'GIUDIZIO_COMPLESSIVO_ESAME_QUAL' => 138, 'ACCONSENTE_AZIENDE' => 139, 'CURRICULUM_PRIMA' => 140, 'CURRICULUM_SECONDA' => 141, 'STAGE_PROFESSIONALI' => 142, 'DATA_ORALE' => 143, 'ORDINE_ESAME_ORALE' => 144, 'TIPO_PRIMO_SCRITTO' => 145, 'TIPO_SECONDO_SCRITTO' => 146, 'TIPO_TERZO_SCRITTO' => 147, 'UNANIMITA_PRIMO_SCRITTO' => 148, 'UNANIMITA_SECONDO_SCRITTO' => 149, 'UNANIMITA_TERZO_SCRITTO' => 150, 'ARGOMENTO_SCELTO_ORALE' => 151, 'AREA_DISC_1_ORALE' => 152, 'AREA_DISC_2_ORALE' => 153, 'DISC_ELABORATI_ORALE' => 154, 'UNANIMITA_VOTO_FINALE' => 155, 'PRESENTE_ESAME_QUINTA' => 156, 'STAMPA_BADGE' => 157, 'ID_CLASSE_DESTINAZIONE' => 158, 'SCONTO_RETTE' => 159, 'CARTA_STUDENTE_NUMERO' => 160, 'CARTA_STUDENTE_SCADENZA' => 161, 'ESITO_CORRENTE_CALCOLATO' => 162, 'ID_FLUSSO' => 163, 'DATA_AGGIORNAMENTO_SOGEI' => 164, 'CODICE_ALUNNO_MINISTERIALE' => 165, 'FLAG_CF_FITTIZIO' => 166, 'FLAG_S2F' => 167, 'CODICE_STATO_SOGEI' => 168, 'CODICE_GRUPPO_NOMADE' => 169, 'FLAG_MINORE_STRANIERO' => 170, 'CHIAVE' => 171, 'VOTO_ESAME_MEDIE_ITALIANO' => 172, 'VOTO_ESAME_MEDIE_INGLESE' => 173, 'VOTO_ESAME_MEDIE_MATEMATICA' => 174, 'VOTO_ESAME_MEDIE_SECONDA_LINGUA' => 175, 'VOTO_ESAME_MEDIE_INVALSI_ITA' => 176, 'VOTO_ESAME_MEDIE_INVALSI_MAT' => 177, 'VOTO_ESAME_MEDIE_ORALE' => 178, 'VOTO_AMMISSIONE_MEDIE' => 179, 'ESITO_PRIMA_ELEMENTARE' => 180, 'ESITO_SECONDA_ELEMENTARE' => 181, 'ESITO_TERZA_ELEMENTARE' => 182, 'ESITO_QUARTA_ELEMENTARE' => 183, 'ESITO_QUINTA_ELEMENTARE' => 184, 'TIPO_VOTO_ESAME_MEDIE_ITALIANO' => 185, 'TIPO_VOTO_ESAME_MEDIE_INGLESE' => 186, 'GIUDIZIO_1_MEDIE' => 187, 'GIUDIZIO_2_MEDIE' => 188, 'GIUDIZIO_3_MEDIE' => 189, 'ARGOMENTI_ORALI_MEDIE' => 190, 'GIUDIZIO_FINALE_1_MEDIE' => 191, 'GIUDIZIO_FINALE_2_MEDIE' => 192, 'GIUDIZIO_FINALE_3_MEDIE' => 193, 'CONSIGLIO_TERZA_MEDIA' => 194, 'GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA' => 195, 'DATA_ARRIVO_IN_ITALIA' => 196, 'FREQUENZA_ASILO_NIDO' => 197, 'FREQUENZA_SCUOLA_MATERNA' => 198, 'DATA_AGGIORNAMENTO_SIDI' => 199, 'CMP_SUP_VAL_ITA' => 200, 'CMP_SUP_TXT_ITA' => 201, 'CMP_SUP_VAL_ING' => 202, 'CMP_SUP_TXT_ING' => 203, 'CMP_SUP_VAL_ALTRI' => 204, 'CMP_SUP_TXT_ALTRI' => 205, 'CMP_SUP_VAL_MAT' => 206, 'CMP_SUP_TXT_MAT' => 207, 'CMP_SUP_VAL_SCI_TEC' => 208, 'CMP_SUP_TXT_SCI_TEC' => 209, 'CMP_SUP_VAL_STO_SOC' => 210, 'CMP_SUP_TXT_STO_SOC' => 211, 'CMP_MED_VAL_ITA' => 212, 'CMP_MED_TXT_ITA' => 213, 'CMP_MED_VAL_ING' => 214, 'CMP_MED_TXT_ING' => 215, 'CMP_MED_VAL_ALTRI' => 216, 'CMP_MED_TXT_ALTRI' => 217, 'CMP_MED_VAL_MAT' => 218, 'CMP_MED_TXT_MAT' => 219, 'CMP_MED_VAL_SCI_TEC' => 220, 'CMP_MED_TXT_SCI_TEC' => 221, 'CMP_MED_VAL_STO_SOC' => 222, 'CMP_MED_TXT_STO_SOC' => 223, 'CMP_MED_VAL_L2' => 224, 'CMP_MED_TXT_L2' => 225, 'CMP_MED_VAL_L3' => 226, 'CMP_MED_TXT_L3' => 227, 'CMP_MED_VAL_ARTE' => 228, 'CMP_MED_TXT_ARTE' => 229, 'CMP_MED_VAL_MUS' => 230, 'CMP_MED_TXT_MUS' => 231, 'CMP_MED_VAL_MOT' => 232, 'CMP_MED_TXT_MOT' => 233, ),
        BasePeer::TYPE_FIELDNAME => array ('id_studente' => 0, 'nome' => 1, 'cognome' => 2, 'indirizzo' => 3, 'citta' => 4, 'cap' => 5, 'provincia' => 6, 'sesso' => 7, 'telefono' => 8, 'cellulare1' => 9, 'cellulare2' => 10, 'email1' => 11, 'email2' => 12, 'invio_email' => 13, 'invio_email_cumulativo' => 14, 'invio_email_parametrico' => 15, 'invio_email_temporale' => 16, 'tipo_sms' => 17, 'tipo_sms_cumulativo' => 18, 'tipo_sms_parametrico' => 19, 'tipo_sms_temporale' => 20, 'aut_entrata_ritardo' => 21, 'aut_uscita_anticipo' => 22, 'aut_pomeriggio' => 23, 'acconsente' => 24, 'ritirato' => 25, 'data_nascita' => 26, 'codice_studente' => 27, 'password_studente' => 28, 'codice_giustificazioni_studente' => 29, 'esonero_religione' => 30, 'materia_sostitutiva_religione' => 31, 'esonero_ed_fisica' => 32, 'materia_sostitutiva_edfisica' => 33, 'crediti_terza' => 34, 'media_voti_terza' => 35, 'debiti_terza' => 36, 'crediti_sospesi_terza' => 37, 'crediti_reintegrati_terza' => 38, 'crediti_quarta' => 39, 'media_voti_quarta' => 40, 'debiti_quarta' => 41, 'crediti_sospesi_quarta' => 42, 'crediti_reintegrati_quarta' => 43, 'crediti_quinta' => 44, 'media_voti_quinta' => 45, 'crediti_finali_agg' => 46, 'matricola' => 47, 'luogo_nascita' => 48, 'provincia_nascita' => 49, 'motivi_crediti_terza' => 50, 'motivi_crediti_quarta' => 51, 'motivi_crediti_quinta' => 52, 'motivi_crediti_agg' => 53, 'codice_comune_nascita' => 54, 'stato_nascita' => 55, 'cittadinanza' => 56, 'seconda_cittadinanza' => 57, 'codice_comune_residenza' => 58, 'distretto' => 59, 'codice_fiscale' => 60, 'medico' => 61, 'telefono_medico' => 62, 'intolleranze_alim' => 63, 'gruppo_sanguigno' => 64, 'gruppo_rh' => 65, 'codice_asl' => 66, 'annotazioni' => 67, 'stato_civile' => 68, 'voto_primo_scritto' => 69, 'voto_secondo_scritto' => 70, 'voto_terzo_scritto' => 71, 'voto_orale' => 72, 'voto_bonus' => 73, 'materia_secondo_scr' => 74, 'ulteriori_specif_diploma' => 75, 'numero_diploma' => 76, 'chi_inserisce' => 77, 'data_inserimento' => 78, 'tipo_inserimento' => 79, 'chi_modifica' => 80, 'data_modifica' => 81, 'tipo_modifica' => 82, 'flag_canc' => 83, 'stato_avanzamento' => 84, 'data_stato_avanzamento' => 85, 'cap_provincia_nascita' => 86, 'badge' => 87, 'cap_residenza' => 88, 'codice_comune_domicilio' => 89, 'cap_domicilio' => 90, 'cap_nascita' => 91, 'indirizzo_domicilio' => 92, 'citta_nascita_straniera' => 93, 'cellulare_allievo' => 94, 'handicap' => 95, 'stato_convittore' => 96, 'data_ritiro' => 97, 'voto_ammissione' => 98, 'differenza_punteggio' => 99, 'voto_qualifica' => 100, 'voto_esame_sc1_qual' => 101, 'voto_esame_sc2_qual' => 102, 'voto_esame_or_qual' => 103, 'stato_privatista' => 104, 'foto' => 105, 'rappresentante' => 106, 'obbligo_formativo' => 107, 'id_lingua_1' => 108, 'id_lingua_2' => 109, 'id_lingua_3' => 110, 'id_lingua_4' => 111, 'id_lingua_5' => 112, 'id_provenienza_scolastica' => 113, 'id_scuola_media' => 114, 'lingua_scuola_media' => 115, 'lingua_scuola_media_2' => 116, 'giudizio_scuola_media' => 117, 'trasporto' => 118, 'data_iscrizione' => 119, 'pei' => 120, 'ammesso_esame_qualifica' => 121, 'ammesso_esame_quinta' => 122, 'giudizio_ammissione_quinta' => 123, 'grado_handicap' => 124, 'tipo_handicap' => 125, 'stato_licenza_maestro' => 126, 'id_studente_sissi' => 127, 'badge_rfid' => 128, 'lode' => 129, 'distretto_scolastico' => 130, 'giudizio_ammissione_terza' => 131, 'esito_prima_media' => 132, 'esito_seconda_media' => 133, 'esito_terza_media' => 134, 'giudizio_esame_sc1_qual' => 135, 'giudizio_esame_sc2_qual' => 136, 'giudizio_esame_or_qual' => 137, 'giudizio_complessivo_esame_qual' => 138, 'acconsente_aziende' => 139, 'curriculum_prima' => 140, 'curriculum_seconda' => 141, 'stage_professionali' => 142, 'data_orale' => 143, 'ordine_esame_orale' => 144, 'tipo_primo_scritto' => 145, 'tipo_secondo_scritto' => 146, 'tipo_terzo_scritto' => 147, 'unanimita_primo_scritto' => 148, 'unanimita_secondo_scritto' => 149, 'unanimita_terzo_scritto' => 150, 'argomento_scelto_orale' => 151, 'area_disc_1_orale' => 152, 'area_disc_2_orale' => 153, 'disc_elaborati_orale' => 154, 'unanimita_voto_finale' => 155, 'presente_esame_quinta' => 156, 'stampa_badge' => 157, 'id_classe_destinazione' => 158, 'sconto_rette' => 159, 'carta_studente_numero' => 160, 'carta_studente_scadenza' => 161, 'esito_corrente_calcolato' => 162, 'id_flusso' => 163, 'data_aggiornamento_sogei' => 164, 'codice_alunno_ministeriale' => 165, 'flag_cf_fittizio' => 166, 'flag_s2f' => 167, 'codice_stato_sogei' => 168, 'codice_gruppo_nomade' => 169, 'flag_minore_straniero' => 170, 'chiave' => 171, 'voto_esame_medie_italiano' => 172, 'voto_esame_medie_inglese' => 173, 'voto_esame_medie_matematica' => 174, 'voto_esame_medie_seconda_lingua' => 175, 'voto_esame_medie_invalsi_ita' => 176, 'voto_esame_medie_invalsi_mat' => 177, 'voto_esame_medie_orale' => 178, 'voto_ammissione_medie' => 179, 'esito_prima_elementare' => 180, 'esito_seconda_elementare' => 181, 'esito_terza_elementare' => 182, 'esito_quarta_elementare' => 183, 'esito_quinta_elementare' => 184, 'tipo_voto_esame_medie_italiano' => 185, 'tipo_voto_esame_medie_inglese' => 186, 'giudizio_1_medie' => 187, 'giudizio_2_medie' => 188, 'giudizio_3_medie' => 189, 'argomenti_orali_medie' => 190, 'giudizio_finale_1_medie' => 191, 'giudizio_finale_2_medie' => 192, 'giudizio_finale_3_medie' => 193, 'consiglio_terza_media' => 194, 'giudizio_sintetico_esame_terza_media' => 195, 'data_arrivo_in_italia' => 196, 'frequenza_asilo_nido' => 197, 'frequenza_scuola_materna' => 198, 'data_aggiornamento_sidi' => 199, 'cmp_sup_val_ita' => 200, 'cmp_sup_txt_ita' => 201, 'cmp_sup_val_ing' => 202, 'cmp_sup_txt_ing' => 203, 'cmp_sup_val_altri' => 204, 'cmp_sup_txt_altri' => 205, 'cmp_sup_val_mat' => 206, 'cmp_sup_txt_mat' => 207, 'cmp_sup_val_sci_tec' => 208, 'cmp_sup_txt_sci_tec' => 209, 'cmp_sup_val_sto_soc' => 210, 'cmp_sup_txt_sto_soc' => 211, 'cmp_med_val_ita' => 212, 'cmp_med_txt_ita' => 213, 'cmp_med_val_ing' => 214, 'cmp_med_txt_ing' => 215, 'cmp_med_val_altri' => 216, 'cmp_med_txt_altri' => 217, 'cmp_med_val_mat' => 218, 'cmp_med_txt_mat' => 219, 'cmp_med_val_sci_tec' => 220, 'cmp_med_txt_sci_tec' => 221, 'cmp_med_val_sto_soc' => 222, 'cmp_med_txt_sto_soc' => 223, 'cmp_med_val_l2' => 224, 'cmp_med_txt_l2' => 225, 'cmp_med_val_l3' => 226, 'cmp_med_txt_l3' => 227, 'cmp_med_val_arte' => 228, 'cmp_med_txt_arte' => 229, 'cmp_med_val_mus' => 230, 'cmp_med_txt_mus' => 231, 'cmp_med_val_mot' => 232, 'cmp_med_txt_mot' => 233, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = StudentiCompletiPeer::getFieldNames($toType);
        $key = isset(StudentiCompletiPeer::$fieldKeys[$fromType][$name]) ? StudentiCompletiPeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(StudentiCompletiPeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, StudentiCompletiPeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return StudentiCompletiPeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. StudentiCompletiPeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(StudentiCompletiPeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_STUDENTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::NOME);
            $criteria->addSelectColumn(StudentiCompletiPeer::COGNOME);
            $criteria->addSelectColumn(StudentiCompletiPeer::INDIRIZZO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CITTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CAP);
            $criteria->addSelectColumn(StudentiCompletiPeer::PROVINCIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::SESSO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TELEFONO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CELLULARE1);
            $criteria->addSelectColumn(StudentiCompletiPeer::CELLULARE2);
            $criteria->addSelectColumn(StudentiCompletiPeer::EMAIL1);
            $criteria->addSelectColumn(StudentiCompletiPeer::EMAIL2);
            $criteria->addSelectColumn(StudentiCompletiPeer::INVIO_EMAIL);
            $criteria->addSelectColumn(StudentiCompletiPeer::INVIO_EMAIL_CUMULATIVO);
            $criteria->addSelectColumn(StudentiCompletiPeer::INVIO_EMAIL_PARAMETRICO);
            $criteria->addSelectColumn(StudentiCompletiPeer::INVIO_EMAIL_TEMPORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_SMS);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_SMS_CUMULATIVO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_SMS_PARAMETRICO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_SMS_TEMPORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::AUT_ENTRATA_RITARDO);
            $criteria->addSelectColumn(StudentiCompletiPeer::AUT_USCITA_ANTICIPO);
            $criteria->addSelectColumn(StudentiCompletiPeer::AUT_POMERIGGIO);
            $criteria->addSelectColumn(StudentiCompletiPeer::ACCONSENTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::RITIRATO);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_STUDENTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::PASSWORD_STUDENTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_GIUSTIFICAZIONI_STUDENTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESONERO_RELIGIONE);
            $criteria->addSelectColumn(StudentiCompletiPeer::MATERIA_SOSTITUTIVA_RELIGIONE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESONERO_ED_FISICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MATERIA_SOSTITUTIVA_EDFISICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MEDIA_VOTI_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::DEBITI_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_SOSPESI_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_REINTEGRATI_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_QUARTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MEDIA_VOTI_QUARTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::DEBITI_QUARTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_SOSPESI_QUARTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_REINTEGRATI_QUARTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_QUINTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MEDIA_VOTI_QUINTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CREDITI_FINALI_AGG);
            $criteria->addSelectColumn(StudentiCompletiPeer::MATRICOLA);
            $criteria->addSelectColumn(StudentiCompletiPeer::LUOGO_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::PROVINCIA_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MOTIVI_CREDITI_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MOTIVI_CREDITI_QUARTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MOTIVI_CREDITI_QUINTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::MOTIVI_CREDITI_AGG);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_COMUNE_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::STATO_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CITTADINANZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::SECONDA_CITTADINANZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_COMUNE_RESIDENZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::DISTRETTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_FISCALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::MEDICO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TELEFONO_MEDICO);
            $criteria->addSelectColumn(StudentiCompletiPeer::INTOLLERANZE_ALIM);
            $criteria->addSelectColumn(StudentiCompletiPeer::GRUPPO_SANGUIGNO);
            $criteria->addSelectColumn(StudentiCompletiPeer::GRUPPO_RH);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_ASL);
            $criteria->addSelectColumn(StudentiCompletiPeer::ANNOTAZIONI);
            $criteria->addSelectColumn(StudentiCompletiPeer::STATO_CIVILE);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_PRIMO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_SECONDO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_TERZO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_BONUS);
            $criteria->addSelectColumn(StudentiCompletiPeer::MATERIA_SECONDO_SCR);
            $criteria->addSelectColumn(StudentiCompletiPeer::ULTERIORI_SPECIF_DIPLOMA);
            $criteria->addSelectColumn(StudentiCompletiPeer::NUMERO_DIPLOMA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CHI_INSERISCE);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_INSERIMENTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_INSERIMENTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CHI_MODIFICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_MODIFICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_MODIFICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::FLAG_CANC);
            $criteria->addSelectColumn(StudentiCompletiPeer::STATO_AVANZAMENTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_STATO_AVANZAMENTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CAP_PROVINCIA_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::BADGE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CAP_RESIDENZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_COMUNE_DOMICILIO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CAP_DOMICILIO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CAP_NASCITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::INDIRIZZO_DOMICILIO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CITTA_NASCITA_STRANIERA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CELLULARE_ALLIEVO);
            $criteria->addSelectColumn(StudentiCompletiPeer::HANDICAP);
            $criteria->addSelectColumn(StudentiCompletiPeer::STATO_CONVITTORE);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_RITIRO);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_AMMISSIONE);
            $criteria->addSelectColumn(StudentiCompletiPeer::DIFFERENZA_PUNTEGGIO);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_QUALIFICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_SC1_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_SC2_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_OR_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::STATO_PRIVATISTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::FOTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::RAPPRESENTANTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::OBBLIGO_FORMATIVO);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_LINGUA_1);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_LINGUA_2);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_LINGUA_3);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_LINGUA_4);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_LINGUA_5);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_PROVENIENZA_SCOLASTICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_SCUOLA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::LINGUA_SCUOLA_MEDIA_2);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_SCUOLA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::TRASPORTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_ISCRIZIONE);
            $criteria->addSelectColumn(StudentiCompletiPeer::PEI);
            $criteria->addSelectColumn(StudentiCompletiPeer::AMMESSO_ESAME_QUALIFICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::AMMESSO_ESAME_QUINTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_QUINTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::GRADO_HANDICAP);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_HANDICAP);
            $criteria->addSelectColumn(StudentiCompletiPeer::STATO_LICENZA_MAESTRO);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_STUDENTE_SISSI);
            $criteria->addSelectColumn(StudentiCompletiPeer::BADGE_RFID);
            $criteria->addSelectColumn(StudentiCompletiPeer::LODE);
            $criteria->addSelectColumn(StudentiCompletiPeer::DISTRETTO_SCOLASTICO);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_AMMISSIONE_TERZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_PRIMA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_SECONDA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_TERZA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_ESAME_SC1_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_ESAME_SC2_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_ESAME_OR_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_COMPLESSIVO_ESAME_QUAL);
            $criteria->addSelectColumn(StudentiCompletiPeer::ACCONSENTE_AZIENDE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CURRICULUM_PRIMA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CURRICULUM_SECONDA);
            $criteria->addSelectColumn(StudentiCompletiPeer::STAGE_PROFESSIONALI);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ORDINE_ESAME_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_PRIMO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_SECONDO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_TERZO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::UNANIMITA_PRIMO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::UNANIMITA_SECONDO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::UNANIMITA_TERZO_SCRITTO);
            $criteria->addSelectColumn(StudentiCompletiPeer::ARGOMENTO_SCELTO_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::AREA_DISC_1_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::AREA_DISC_2_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::DISC_ELABORATI_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::UNANIMITA_VOTO_FINALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::PRESENTE_ESAME_QUINTA);
            $criteria->addSelectColumn(StudentiCompletiPeer::STAMPA_BADGE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_CLASSE_DESTINAZIONE);
            $criteria->addSelectColumn(StudentiCompletiPeer::SCONTO_RETTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CARTA_STUDENTE_NUMERO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CARTA_STUDENTE_SCADENZA);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_CORRENTE_CALCOLATO);
            $criteria->addSelectColumn(StudentiCompletiPeer::ID_FLUSSO);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_AGGIORNAMENTO_SOGEI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_ALUNNO_MINISTERIALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::FLAG_CF_FITTIZIO);
            $criteria->addSelectColumn(StudentiCompletiPeer::FLAG_S2F);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_STATO_SOGEI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CODICE_GRUPPO_NOMADE);
            $criteria->addSelectColumn(StudentiCompletiPeer::FLAG_MINORE_STRANIERO);
            $criteria->addSelectColumn(StudentiCompletiPeer::CHIAVE);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_ITALIANO);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INGLESE);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_MATEMATICA);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_SECONDA_LINGUA);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_ITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_INVALSI_MAT);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_ESAME_MEDIE_ORALE);
            $criteria->addSelectColumn(StudentiCompletiPeer::VOTO_AMMISSIONE_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_PRIMA_ELEMENTARE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_SECONDA_ELEMENTARE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_TERZA_ELEMENTARE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_QUARTA_ELEMENTARE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ESITO_QUINTA_ELEMENTARE);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_ITALIANO);
            $criteria->addSelectColumn(StudentiCompletiPeer::TIPO_VOTO_ESAME_MEDIE_INGLESE);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_1_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_2_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_3_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::ARGOMENTI_ORALI_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_FINALE_1_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_FINALE_2_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_FINALE_3_MEDIE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CONSIGLIO_TERZA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::GIUDIZIO_SINTETICO_ESAME_TERZA_MEDIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_ARRIVO_IN_ITALIA);
            $criteria->addSelectColumn(StudentiCompletiPeer::FREQUENZA_ASILO_NIDO);
            $criteria->addSelectColumn(StudentiCompletiPeer::FREQUENZA_SCUOLA_MATERNA);
            $criteria->addSelectColumn(StudentiCompletiPeer::DATA_AGGIORNAMENTO_SIDI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_VAL_ITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_TXT_ITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_VAL_ING);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_TXT_ING);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_VAL_ALTRI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_TXT_ALTRI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_VAL_MAT);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_TXT_MAT);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_VAL_SCI_TEC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_TXT_SCI_TEC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_VAL_STO_SOC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_SUP_TXT_STO_SOC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_ITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_ITA);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_ING);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_ING);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_ALTRI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_ALTRI);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_MAT);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_MAT);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_SCI_TEC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_SCI_TEC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_STO_SOC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_STO_SOC);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_L2);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_L2);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_L3);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_L3);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_ARTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_ARTE);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_MUS);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_MUS);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_VAL_MOT);
            $criteria->addSelectColumn(StudentiCompletiPeer::CMP_MED_TXT_MOT);
        } else {
            $criteria->addSelectColumn($alias . '.id_studente');
            $criteria->addSelectColumn($alias . '.nome');
            $criteria->addSelectColumn($alias . '.cognome');
            $criteria->addSelectColumn($alias . '.indirizzo');
            $criteria->addSelectColumn($alias . '.citta');
            $criteria->addSelectColumn($alias . '.cap');
            $criteria->addSelectColumn($alias . '.provincia');
            $criteria->addSelectColumn($alias . '.sesso');
            $criteria->addSelectColumn($alias . '.telefono');
            $criteria->addSelectColumn($alias . '.cellulare1');
            $criteria->addSelectColumn($alias . '.cellulare2');
            $criteria->addSelectColumn($alias . '.email1');
            $criteria->addSelectColumn($alias . '.email2');
            $criteria->addSelectColumn($alias . '.invio_email');
            $criteria->addSelectColumn($alias . '.invio_email_cumulativo');
            $criteria->addSelectColumn($alias . '.invio_email_parametrico');
            $criteria->addSelectColumn($alias . '.invio_email_temporale');
            $criteria->addSelectColumn($alias . '.tipo_sms');
            $criteria->addSelectColumn($alias . '.tipo_sms_cumulativo');
            $criteria->addSelectColumn($alias . '.tipo_sms_parametrico');
            $criteria->addSelectColumn($alias . '.tipo_sms_temporale');
            $criteria->addSelectColumn($alias . '.aut_entrata_ritardo');
            $criteria->addSelectColumn($alias . '.aut_uscita_anticipo');
            $criteria->addSelectColumn($alias . '.aut_pomeriggio');
            $criteria->addSelectColumn($alias . '.acconsente');
            $criteria->addSelectColumn($alias . '.ritirato');
            $criteria->addSelectColumn($alias . '.data_nascita');
            $criteria->addSelectColumn($alias . '.codice_studente');
            $criteria->addSelectColumn($alias . '.password_studente');
            $criteria->addSelectColumn($alias . '.codice_giustificazioni_studente');
            $criteria->addSelectColumn($alias . '.esonero_religione');
            $criteria->addSelectColumn($alias . '.materia_sostitutiva_religione');
            $criteria->addSelectColumn($alias . '.esonero_ed_fisica');
            $criteria->addSelectColumn($alias . '.materia_sostitutiva_edfisica');
            $criteria->addSelectColumn($alias . '.crediti_terza');
            $criteria->addSelectColumn($alias . '.media_voti_terza');
            $criteria->addSelectColumn($alias . '.debiti_terza');
            $criteria->addSelectColumn($alias . '.crediti_sospesi_terza');
            $criteria->addSelectColumn($alias . '.crediti_reintegrati_terza');
            $criteria->addSelectColumn($alias . '.crediti_quarta');
            $criteria->addSelectColumn($alias . '.media_voti_quarta');
            $criteria->addSelectColumn($alias . '.debiti_quarta');
            $criteria->addSelectColumn($alias . '.crediti_sospesi_quarta');
            $criteria->addSelectColumn($alias . '.crediti_reintegrati_quarta');
            $criteria->addSelectColumn($alias . '.crediti_quinta');
            $criteria->addSelectColumn($alias . '.media_voti_quinta');
            $criteria->addSelectColumn($alias . '.crediti_finali_agg');
            $criteria->addSelectColumn($alias . '.matricola');
            $criteria->addSelectColumn($alias . '.luogo_nascita');
            $criteria->addSelectColumn($alias . '.provincia_nascita');
            $criteria->addSelectColumn($alias . '.motivi_crediti_terza');
            $criteria->addSelectColumn($alias . '.motivi_crediti_quarta');
            $criteria->addSelectColumn($alias . '.motivi_crediti_quinta');
            $criteria->addSelectColumn($alias . '.motivi_crediti_agg');
            $criteria->addSelectColumn($alias . '.codice_comune_nascita');
            $criteria->addSelectColumn($alias . '.stato_nascita');
            $criteria->addSelectColumn($alias . '.cittadinanza');
            $criteria->addSelectColumn($alias . '.seconda_cittadinanza');
            $criteria->addSelectColumn($alias . '.codice_comune_residenza');
            $criteria->addSelectColumn($alias . '.distretto');
            $criteria->addSelectColumn($alias . '.codice_fiscale');
            $criteria->addSelectColumn($alias . '.medico');
            $criteria->addSelectColumn($alias . '.telefono_medico');
            $criteria->addSelectColumn($alias . '.intolleranze_alim');
            $criteria->addSelectColumn($alias . '.gruppo_sanguigno');
            $criteria->addSelectColumn($alias . '.gruppo_rh');
            $criteria->addSelectColumn($alias . '.codice_asl');
            $criteria->addSelectColumn($alias . '.annotazioni');
            $criteria->addSelectColumn($alias . '.stato_civile');
            $criteria->addSelectColumn($alias . '.voto_primo_scritto');
            $criteria->addSelectColumn($alias . '.voto_secondo_scritto');
            $criteria->addSelectColumn($alias . '.voto_terzo_scritto');
            $criteria->addSelectColumn($alias . '.voto_orale');
            $criteria->addSelectColumn($alias . '.voto_bonus');
            $criteria->addSelectColumn($alias . '.materia_secondo_scr');
            $criteria->addSelectColumn($alias . '.ulteriori_specif_diploma');
            $criteria->addSelectColumn($alias . '.numero_diploma');
            $criteria->addSelectColumn($alias . '.chi_inserisce');
            $criteria->addSelectColumn($alias . '.data_inserimento');
            $criteria->addSelectColumn($alias . '.tipo_inserimento');
            $criteria->addSelectColumn($alias . '.chi_modifica');
            $criteria->addSelectColumn($alias . '.data_modifica');
            $criteria->addSelectColumn($alias . '.tipo_modifica');
            $criteria->addSelectColumn($alias . '.flag_canc');
            $criteria->addSelectColumn($alias . '.stato_avanzamento');
            $criteria->addSelectColumn($alias . '.data_stato_avanzamento');
            $criteria->addSelectColumn($alias . '.cap_provincia_nascita');
            $criteria->addSelectColumn($alias . '.badge');
            $criteria->addSelectColumn($alias . '.cap_residenza');
            $criteria->addSelectColumn($alias . '.codice_comune_domicilio');
            $criteria->addSelectColumn($alias . '.cap_domicilio');
            $criteria->addSelectColumn($alias . '.cap_nascita');
            $criteria->addSelectColumn($alias . '.indirizzo_domicilio');
            $criteria->addSelectColumn($alias . '.citta_nascita_straniera');
            $criteria->addSelectColumn($alias . '.cellulare_allievo');
            $criteria->addSelectColumn($alias . '.handicap');
            $criteria->addSelectColumn($alias . '.stato_convittore');
            $criteria->addSelectColumn($alias . '.data_ritiro');
            $criteria->addSelectColumn($alias . '.voto_ammissione');
            $criteria->addSelectColumn($alias . '.differenza_punteggio');
            $criteria->addSelectColumn($alias . '.voto_qualifica');
            $criteria->addSelectColumn($alias . '.voto_esame_sc1_qual');
            $criteria->addSelectColumn($alias . '.voto_esame_sc2_qual');
            $criteria->addSelectColumn($alias . '.voto_esame_or_qual');
            $criteria->addSelectColumn($alias . '.stato_privatista');
            $criteria->addSelectColumn($alias . '.foto');
            $criteria->addSelectColumn($alias . '.rappresentante');
            $criteria->addSelectColumn($alias . '.obbligo_formativo');
            $criteria->addSelectColumn($alias . '.id_lingua_1');
            $criteria->addSelectColumn($alias . '.id_lingua_2');
            $criteria->addSelectColumn($alias . '.id_lingua_3');
            $criteria->addSelectColumn($alias . '.id_lingua_4');
            $criteria->addSelectColumn($alias . '.id_lingua_5');
            $criteria->addSelectColumn($alias . '.id_provenienza_scolastica');
            $criteria->addSelectColumn($alias . '.id_scuola_media');
            $criteria->addSelectColumn($alias . '.lingua_scuola_media');
            $criteria->addSelectColumn($alias . '.lingua_scuola_media_2');
            $criteria->addSelectColumn($alias . '.giudizio_scuola_media');
            $criteria->addSelectColumn($alias . '.trasporto');
            $criteria->addSelectColumn($alias . '.data_iscrizione');
            $criteria->addSelectColumn($alias . '.pei');
            $criteria->addSelectColumn($alias . '.ammesso_esame_qualifica');
            $criteria->addSelectColumn($alias . '.ammesso_esame_quinta');
            $criteria->addSelectColumn($alias . '.giudizio_ammissione_quinta');
            $criteria->addSelectColumn($alias . '.grado_handicap');
            $criteria->addSelectColumn($alias . '.tipo_handicap');
            $criteria->addSelectColumn($alias . '.stato_licenza_maestro');
            $criteria->addSelectColumn($alias . '.id_studente_sissi');
            $criteria->addSelectColumn($alias . '.badge_rfid');
            $criteria->addSelectColumn($alias . '.lode');
            $criteria->addSelectColumn($alias . '.distretto_scolastico');
            $criteria->addSelectColumn($alias . '.giudizio_ammissione_terza');
            $criteria->addSelectColumn($alias . '.esito_prima_media');
            $criteria->addSelectColumn($alias . '.esito_seconda_media');
            $criteria->addSelectColumn($alias . '.esito_terza_media');
            $criteria->addSelectColumn($alias . '.giudizio_esame_sc1_qual');
            $criteria->addSelectColumn($alias . '.giudizio_esame_sc2_qual');
            $criteria->addSelectColumn($alias . '.giudizio_esame_or_qual');
            $criteria->addSelectColumn($alias . '.giudizio_complessivo_esame_qual');
            $criteria->addSelectColumn($alias . '.acconsente_aziende');
            $criteria->addSelectColumn($alias . '.curriculum_prima');
            $criteria->addSelectColumn($alias . '.curriculum_seconda');
            $criteria->addSelectColumn($alias . '.stage_professionali');
            $criteria->addSelectColumn($alias . '.data_orale');
            $criteria->addSelectColumn($alias . '.ordine_esame_orale');
            $criteria->addSelectColumn($alias . '.tipo_primo_scritto');
            $criteria->addSelectColumn($alias . '.tipo_secondo_scritto');
            $criteria->addSelectColumn($alias . '.tipo_terzo_scritto');
            $criteria->addSelectColumn($alias . '.unanimita_primo_scritto');
            $criteria->addSelectColumn($alias . '.unanimita_secondo_scritto');
            $criteria->addSelectColumn($alias . '.unanimita_terzo_scritto');
            $criteria->addSelectColumn($alias . '.argomento_scelto_orale');
            $criteria->addSelectColumn($alias . '.area_disc_1_orale');
            $criteria->addSelectColumn($alias . '.area_disc_2_orale');
            $criteria->addSelectColumn($alias . '.disc_elaborati_orale');
            $criteria->addSelectColumn($alias . '.unanimita_voto_finale');
            $criteria->addSelectColumn($alias . '.presente_esame_quinta');
            $criteria->addSelectColumn($alias . '.stampa_badge');
            $criteria->addSelectColumn($alias . '.id_classe_destinazione');
            $criteria->addSelectColumn($alias . '.sconto_rette');
            $criteria->addSelectColumn($alias . '.carta_studente_numero');
            $criteria->addSelectColumn($alias . '.carta_studente_scadenza');
            $criteria->addSelectColumn($alias . '.esito_corrente_calcolato');
            $criteria->addSelectColumn($alias . '.id_flusso');
            $criteria->addSelectColumn($alias . '.data_aggiornamento_sogei');
            $criteria->addSelectColumn($alias . '.codice_alunno_ministeriale');
            $criteria->addSelectColumn($alias . '.flag_cf_fittizio');
            $criteria->addSelectColumn($alias . '.flag_s2f');
            $criteria->addSelectColumn($alias . '.codice_stato_sogei');
            $criteria->addSelectColumn($alias . '.codice_gruppo_nomade');
            $criteria->addSelectColumn($alias . '.flag_minore_straniero');
            $criteria->addSelectColumn($alias . '.chiave');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_italiano');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_inglese');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_matematica');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_seconda_lingua');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_invalsi_ita');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_invalsi_mat');
            $criteria->addSelectColumn($alias . '.voto_esame_medie_orale');
            $criteria->addSelectColumn($alias . '.voto_ammissione_medie');
            $criteria->addSelectColumn($alias . '.esito_prima_elementare');
            $criteria->addSelectColumn($alias . '.esito_seconda_elementare');
            $criteria->addSelectColumn($alias . '.esito_terza_elementare');
            $criteria->addSelectColumn($alias . '.esito_quarta_elementare');
            $criteria->addSelectColumn($alias . '.esito_quinta_elementare');
            $criteria->addSelectColumn($alias . '.tipo_voto_esame_medie_italiano');
            $criteria->addSelectColumn($alias . '.tipo_voto_esame_medie_inglese');
            $criteria->addSelectColumn($alias . '.giudizio_1_medie');
            $criteria->addSelectColumn($alias . '.giudizio_2_medie');
            $criteria->addSelectColumn($alias . '.giudizio_3_medie');
            $criteria->addSelectColumn($alias . '.argomenti_orali_medie');
            $criteria->addSelectColumn($alias . '.giudizio_finale_1_medie');
            $criteria->addSelectColumn($alias . '.giudizio_finale_2_medie');
            $criteria->addSelectColumn($alias . '.giudizio_finale_3_medie');
            $criteria->addSelectColumn($alias . '.consiglio_terza_media');
            $criteria->addSelectColumn($alias . '.giudizio_sintetico_esame_terza_media');
            $criteria->addSelectColumn($alias . '.data_arrivo_in_italia');
            $criteria->addSelectColumn($alias . '.frequenza_asilo_nido');
            $criteria->addSelectColumn($alias . '.frequenza_scuola_materna');
            $criteria->addSelectColumn($alias . '.data_aggiornamento_sidi');
            $criteria->addSelectColumn($alias . '.cmp_sup_val_ita');
            $criteria->addSelectColumn($alias . '.cmp_sup_txt_ita');
            $criteria->addSelectColumn($alias . '.cmp_sup_val_ing');
            $criteria->addSelectColumn($alias . '.cmp_sup_txt_ing');
            $criteria->addSelectColumn($alias . '.cmp_sup_val_altri');
            $criteria->addSelectColumn($alias . '.cmp_sup_txt_altri');
            $criteria->addSelectColumn($alias . '.cmp_sup_val_mat');
            $criteria->addSelectColumn($alias . '.cmp_sup_txt_mat');
            $criteria->addSelectColumn($alias . '.cmp_sup_val_sci_tec');
            $criteria->addSelectColumn($alias . '.cmp_sup_txt_sci_tec');
            $criteria->addSelectColumn($alias . '.cmp_sup_val_sto_soc');
            $criteria->addSelectColumn($alias . '.cmp_sup_txt_sto_soc');
            $criteria->addSelectColumn($alias . '.cmp_med_val_ita');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_ita');
            $criteria->addSelectColumn($alias . '.cmp_med_val_ing');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_ing');
            $criteria->addSelectColumn($alias . '.cmp_med_val_altri');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_altri');
            $criteria->addSelectColumn($alias . '.cmp_med_val_mat');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_mat');
            $criteria->addSelectColumn($alias . '.cmp_med_val_sci_tec');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_sci_tec');
            $criteria->addSelectColumn($alias . '.cmp_med_val_sto_soc');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_sto_soc');
            $criteria->addSelectColumn($alias . '.cmp_med_val_l2');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_l2');
            $criteria->addSelectColumn($alias . '.cmp_med_val_l3');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_l3');
            $criteria->addSelectColumn($alias . '.cmp_med_val_arte');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_arte');
            $criteria->addSelectColumn($alias . '.cmp_med_val_mus');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_mus');
            $criteria->addSelectColumn($alias . '.cmp_med_val_mot');
            $criteria->addSelectColumn($alias . '.cmp_med_txt_mot');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StudentiCompletiPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StudentiCompletiPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(StudentiCompletiPeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(StudentiCompletiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return StudentiCompleti
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = StudentiCompletiPeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return StudentiCompletiPeer::populateObjects(StudentiCompletiPeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StudentiCompletiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            StudentiCompletiPeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(StudentiCompletiPeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param StudentiCompleti $obj A StudentiCompleti object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getIdStudente();
            } // if key === null
            StudentiCompletiPeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A StudentiCompleti object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof StudentiCompleti) {
                $key = (string) $value->getIdStudente();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or StudentiCompleti object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(StudentiCompletiPeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return StudentiCompleti Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(StudentiCompletiPeer::$instances[$key])) {
                return StudentiCompletiPeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (StudentiCompletiPeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        StudentiCompletiPeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to studenti_completi
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = StudentiCompletiPeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = StudentiCompletiPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = StudentiCompletiPeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                StudentiCompletiPeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (StudentiCompleti object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = StudentiCompletiPeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = StudentiCompletiPeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + StudentiCompletiPeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = StudentiCompletiPeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            StudentiCompletiPeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(StudentiCompletiPeer::DATABASE_NAME)->getTable(StudentiCompletiPeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseStudentiCompletiPeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseStudentiCompletiPeer::TABLE_NAME)) {
        $dbMap->addTableObject(new StudentiCompletiTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return StudentiCompletiPeer::OM_CLASS;
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return StudentiCompleti
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = StudentiCompletiPeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(StudentiCompletiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(StudentiCompletiPeer::DATABASE_NAME);
        $criteria->add(StudentiCompletiPeer::ID_STUDENTE, $pk);

        $v = StudentiCompletiPeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return StudentiCompleti[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StudentiCompletiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(StudentiCompletiPeer::DATABASE_NAME);
            $criteria->add(StudentiCompletiPeer::ID_STUDENTE, $pks, Criteria::IN);
            $objs = StudentiCompletiPeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseStudentiCompletiPeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseStudentiCompletiPeer::buildTableMap();

