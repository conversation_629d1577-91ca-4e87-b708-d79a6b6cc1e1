<?php

namespace Ccp\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Ccp\StudentiCompletiPeer;
use Ccp\StudentiPeer;
use Ccp\Tasse;
use Ccp\TassePeer;
use Ccp\TipiTassePeer;
use Ccp\map\TasseTableMap;

/**
 * Base static class for performing query and update operations on the 'tasse' table.
 *
 *
 *
 * @package propel.generator.Ccp.om
 */
abstract class BaseTassePeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'tasse';

    /** the related Propel class for this table */
    const OM_CLASS = 'Ccp\\Tasse';

    /** the related TableMap class for this table */
    const TM_CLASS = 'TasseTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 20;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 20;

    /** the column name for the id_tasse field */
    const ID_TASSE = 'tasse.id_tasse';

    /** the column name for the id_tipo_tassa field */
    const ID_TIPO_TASSA = 'tasse.id_tipo_tassa';

    /** the column name for the id_studente field */
    const ID_STUDENTE = 'tasse.id_studente';

    /** the column name for the sede_ufficio_postale field */
    const SEDE_UFFICIO_POSTALE = 'tasse.sede_ufficio_postale';

    /** the column name for the numero_versamento field */
    const NUMERO_VERSAMENTO = 'tasse.numero_versamento';

    /** the column name for the data_versamento field */
    const DATA_VERSAMENTO = 'tasse.data_versamento';

    /** the column name for the note field */
    const NOTE = 'tasse.note';

    /** the column name for the anno_scolastico field */
    const ANNO_SCOLASTICO = 'tasse.anno_scolastico';

    /** the column name for the riferimento_estratto_conto field */
    const RIFERIMENTO_ESTRATTO_CONTO = 'tasse.riferimento_estratto_conto';

    /** the column name for the data_estratto_conto field */
    const DATA_ESTRATTO_CONTO = 'tasse.data_estratto_conto';

    /** the column name for the dati_debitore field */
    const DATI_DEBITORE = 'tasse.dati_debitore';

    /** the column name for the numero_bollettino field */
    const NUMERO_BOLLETTINO = 'tasse.numero_bollettino';

    /** the column name for the is_incoming field */
    const IS_INCOMING = 'tasse.is_incoming';

    /** the column name for the employee_id field */
    const EMPLOYEE_ID = 'tasse.employee_id';

    /** the column name for the tipologia_uscita field */
    const TIPOLOGIA_USCITA = 'tasse.tipologia_uscita';

    /** the column name for the destinazione_pagamento field */
    const DESTINAZIONE_PAGAMENTO = 'tasse.destinazione_pagamento';

    /** the column name for the modalita_pagamento field */
    const MODALITA_PAGAMENTO = 'tasse.modalita_pagamento';

    /** the column name for the sede_studente field */
    const SEDE_STUDENTE = 'tasse.sede_studente';

    /** the column name for the classe field */
    const CLASSE = 'tasse.classe';

    /** the column name for the importo_versato field */
    const IMPORTO_VERSATO = 'tasse.importo_versato';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of Tasse objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array Tasse[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. TassePeer::$fieldNames[TassePeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('IdTasse', 'IdTipoTassa', 'IdStudente', 'SedeUfficioPostale', 'NumeroVersamento', 'DataVersamento', 'Note', 'AnnoScolastico', 'RiferimentoEstrattoConto', 'DataEstrattoConto', 'DatiDebitore', 'NumeroBollettino', 'IsIncoming', 'EmployeeId', 'TipologiaUscita', 'DestinazionePagamento', 'ModalitaPagamento', 'SedeStudente', 'Classe', 'ImportoVersato', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('idTasse', 'idTipoTassa', 'idStudente', 'sedeUfficioPostale', 'numeroVersamento', 'dataVersamento', 'note', 'annoScolastico', 'riferimentoEstrattoConto', 'dataEstrattoConto', 'datiDebitore', 'numeroBollettino', 'isIncoming', 'employeeId', 'tipologiaUscita', 'destinazionePagamento', 'modalitaPagamento', 'sedeStudente', 'classe', 'importoVersato', ),
        BasePeer::TYPE_COLNAME => array (TassePeer::ID_TASSE, TassePeer::ID_TIPO_TASSA, TassePeer::ID_STUDENTE, TassePeer::SEDE_UFFICIO_POSTALE, TassePeer::NUMERO_VERSAMENTO, TassePeer::DATA_VERSAMENTO, TassePeer::NOTE, TassePeer::ANNO_SCOLASTICO, TassePeer::RIFERIMENTO_ESTRATTO_CONTO, TassePeer::DATA_ESTRATTO_CONTO, TassePeer::DATI_DEBITORE, TassePeer::NUMERO_BOLLETTINO, TassePeer::IS_INCOMING, TassePeer::EMPLOYEE_ID, TassePeer::TIPOLOGIA_USCITA, TassePeer::DESTINAZIONE_PAGAMENTO, TassePeer::MODALITA_PAGAMENTO, TassePeer::SEDE_STUDENTE, TassePeer::CLASSE, TassePeer::IMPORTO_VERSATO, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID_TASSE', 'ID_TIPO_TASSA', 'ID_STUDENTE', 'SEDE_UFFICIO_POSTALE', 'NUMERO_VERSAMENTO', 'DATA_VERSAMENTO', 'NOTE', 'ANNO_SCOLASTICO', 'RIFERIMENTO_ESTRATTO_CONTO', 'DATA_ESTRATTO_CONTO', 'DATI_DEBITORE', 'NUMERO_BOLLETTINO', 'IS_INCOMING', 'EMPLOYEE_ID', 'TIPOLOGIA_USCITA', 'DESTINAZIONE_PAGAMENTO', 'MODALITA_PAGAMENTO', 'SEDE_STUDENTE', 'CLASSE', 'IMPORTO_VERSATO', ),
        BasePeer::TYPE_FIELDNAME => array ('id_tasse', 'id_tipo_tassa', 'id_studente', 'sede_ufficio_postale', 'numero_versamento', 'data_versamento', 'note', 'anno_scolastico', 'riferimento_estratto_conto', 'data_estratto_conto', 'dati_debitore', 'numero_bollettino', 'is_incoming', 'employee_id', 'tipologia_uscita', 'destinazione_pagamento', 'modalita_pagamento', 'sede_studente', 'classe', 'importo_versato', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. TassePeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('IdTasse' => 0, 'IdTipoTassa' => 1, 'IdStudente' => 2, 'SedeUfficioPostale' => 3, 'NumeroVersamento' => 4, 'DataVersamento' => 5, 'Note' => 6, 'AnnoScolastico' => 7, 'RiferimentoEstrattoConto' => 8, 'DataEstrattoConto' => 9, 'DatiDebitore' => 10, 'NumeroBollettino' => 11, 'IsIncoming' => 12, 'EmployeeId' => 13, 'TipologiaUscita' => 14, 'DestinazionePagamento' => 15, 'ModalitaPagamento' => 16, 'SedeStudente' => 17, 'Classe' => 18, 'ImportoVersato' => 19, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('idTasse' => 0, 'idTipoTassa' => 1, 'idStudente' => 2, 'sedeUfficioPostale' => 3, 'numeroVersamento' => 4, 'dataVersamento' => 5, 'note' => 6, 'annoScolastico' => 7, 'riferimentoEstrattoConto' => 8, 'dataEstrattoConto' => 9, 'datiDebitore' => 10, 'numeroBollettino' => 11, 'isIncoming' => 12, 'employeeId' => 13, 'tipologiaUscita' => 14, 'destinazionePagamento' => 15, 'modalitaPagamento' => 16, 'sedeStudente' => 17, 'classe' => 18, 'importoVersato' => 19, ),
        BasePeer::TYPE_COLNAME => array (TassePeer::ID_TASSE => 0, TassePeer::ID_TIPO_TASSA => 1, TassePeer::ID_STUDENTE => 2, TassePeer::SEDE_UFFICIO_POSTALE => 3, TassePeer::NUMERO_VERSAMENTO => 4, TassePeer::DATA_VERSAMENTO => 5, TassePeer::NOTE => 6, TassePeer::ANNO_SCOLASTICO => 7, TassePeer::RIFERIMENTO_ESTRATTO_CONTO => 8, TassePeer::DATA_ESTRATTO_CONTO => 9, TassePeer::DATI_DEBITORE => 10, TassePeer::NUMERO_BOLLETTINO => 11, TassePeer::IS_INCOMING => 12, TassePeer::EMPLOYEE_ID => 13, TassePeer::TIPOLOGIA_USCITA => 14, TassePeer::DESTINAZIONE_PAGAMENTO => 15, TassePeer::MODALITA_PAGAMENTO => 16, TassePeer::SEDE_STUDENTE => 17, TassePeer::CLASSE => 18, TassePeer::IMPORTO_VERSATO => 19, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID_TASSE' => 0, 'ID_TIPO_TASSA' => 1, 'ID_STUDENTE' => 2, 'SEDE_UFFICIO_POSTALE' => 3, 'NUMERO_VERSAMENTO' => 4, 'DATA_VERSAMENTO' => 5, 'NOTE' => 6, 'ANNO_SCOLASTICO' => 7, 'RIFERIMENTO_ESTRATTO_CONTO' => 8, 'DATA_ESTRATTO_CONTO' => 9, 'DATI_DEBITORE' => 10, 'NUMERO_BOLLETTINO' => 11, 'IS_INCOMING' => 12, 'EMPLOYEE_ID' => 13, 'TIPOLOGIA_USCITA' => 14, 'DESTINAZIONE_PAGAMENTO' => 15, 'MODALITA_PAGAMENTO' => 16, 'SEDE_STUDENTE' => 17, 'CLASSE' => 18, 'IMPORTO_VERSATO' => 19, ),
        BasePeer::TYPE_FIELDNAME => array ('id_tasse' => 0, 'id_tipo_tassa' => 1, 'id_studente' => 2, 'sede_ufficio_postale' => 3, 'numero_versamento' => 4, 'data_versamento' => 5, 'note' => 6, 'anno_scolastico' => 7, 'riferimento_estratto_conto' => 8, 'data_estratto_conto' => 9, 'dati_debitore' => 10, 'numero_bollettino' => 11, 'is_incoming' => 12, 'employee_id' => 13, 'tipologia_uscita' => 14, 'destinazione_pagamento' => 15, 'modalita_pagamento' => 16, 'sede_studente' => 17, 'classe' => 18, 'importo_versato' => 19, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = TassePeer::getFieldNames($toType);
        $key = isset(TassePeer::$fieldKeys[$fromType][$name]) ? TassePeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(TassePeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, TassePeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return TassePeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. TassePeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(TassePeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(TassePeer::ID_TASSE);
            $criteria->addSelectColumn(TassePeer::ID_TIPO_TASSA);
            $criteria->addSelectColumn(TassePeer::ID_STUDENTE);
            $criteria->addSelectColumn(TassePeer::SEDE_UFFICIO_POSTALE);
            $criteria->addSelectColumn(TassePeer::NUMERO_VERSAMENTO);
            $criteria->addSelectColumn(TassePeer::DATA_VERSAMENTO);
            $criteria->addSelectColumn(TassePeer::NOTE);
            $criteria->addSelectColumn(TassePeer::ANNO_SCOLASTICO);
            $criteria->addSelectColumn(TassePeer::RIFERIMENTO_ESTRATTO_CONTO);
            $criteria->addSelectColumn(TassePeer::DATA_ESTRATTO_CONTO);
            $criteria->addSelectColumn(TassePeer::DATI_DEBITORE);
            $criteria->addSelectColumn(TassePeer::NUMERO_BOLLETTINO);
            $criteria->addSelectColumn(TassePeer::IS_INCOMING);
            $criteria->addSelectColumn(TassePeer::EMPLOYEE_ID);
            $criteria->addSelectColumn(TassePeer::TIPOLOGIA_USCITA);
            $criteria->addSelectColumn(TassePeer::DESTINAZIONE_PAGAMENTO);
            $criteria->addSelectColumn(TassePeer::MODALITA_PAGAMENTO);
            $criteria->addSelectColumn(TassePeer::SEDE_STUDENTE);
            $criteria->addSelectColumn(TassePeer::CLASSE);
            $criteria->addSelectColumn(TassePeer::IMPORTO_VERSATO);
        } else {
            $criteria->addSelectColumn($alias . '.id_tasse');
            $criteria->addSelectColumn($alias . '.id_tipo_tassa');
            $criteria->addSelectColumn($alias . '.id_studente');
            $criteria->addSelectColumn($alias . '.sede_ufficio_postale');
            $criteria->addSelectColumn($alias . '.numero_versamento');
            $criteria->addSelectColumn($alias . '.data_versamento');
            $criteria->addSelectColumn($alias . '.note');
            $criteria->addSelectColumn($alias . '.anno_scolastico');
            $criteria->addSelectColumn($alias . '.riferimento_estratto_conto');
            $criteria->addSelectColumn($alias . '.data_estratto_conto');
            $criteria->addSelectColumn($alias . '.dati_debitore');
            $criteria->addSelectColumn($alias . '.numero_bollettino');
            $criteria->addSelectColumn($alias . '.is_incoming');
            $criteria->addSelectColumn($alias . '.employee_id');
            $criteria->addSelectColumn($alias . '.tipologia_uscita');
            $criteria->addSelectColumn($alias . '.destinazione_pagamento');
            $criteria->addSelectColumn($alias . '.modalita_pagamento');
            $criteria->addSelectColumn($alias . '.sede_studente');
            $criteria->addSelectColumn($alias . '.classe');
            $criteria->addSelectColumn($alias . '.importo_versato');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(TassePeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return Tasse
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = TassePeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return TassePeer::populateObjects(TassePeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            TassePeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param Tasse $obj A Tasse object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getIdTasse();
            } // if key === null
            TassePeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A Tasse object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof Tasse) {
                $key = (string) $value->getIdTasse();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or Tasse object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(TassePeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return Tasse Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(TassePeer::$instances[$key])) {
                return TassePeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (TassePeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        TassePeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to tasse
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = TassePeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = TassePeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                TassePeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (Tasse object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = TassePeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = TassePeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + TassePeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = TassePeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            TassePeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }


    /**
     * Returns the number of rows matching criteria, joining the related TipiTasse table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinTipiTasse(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related StudentiCompleti table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinStudentiCompleti(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related Studenti table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinStudenti(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Tasse objects pre-filled with their TipiTasse objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinTipiTasse(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol = TassePeer::NUM_HYDRATE_COLUMNS;
        TipiTassePeer::addSelectColumns($criteria);

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = TipiTassePeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = TipiTassePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = TipiTassePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    TipiTassePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Tasse) to $obj2 (TipiTasse)
                $obj2->addTasse($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Tasse objects pre-filled with their StudentiCompleti objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinStudentiCompleti(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol = TassePeer::NUM_HYDRATE_COLUMNS;
        StudentiCompletiPeer::addSelectColumns($criteria);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = StudentiCompletiPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = StudentiCompletiPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = StudentiCompletiPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    StudentiCompletiPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Tasse) to $obj2 (StudentiCompleti)
                $obj2->addTasse($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Tasse objects pre-filled with their Studenti objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinStudenti(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol = TassePeer::NUM_HYDRATE_COLUMNS;
        StudentiPeer::addSelectColumns($criteria);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = StudentiPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = StudentiPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = StudentiPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    StudentiPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Tasse) to $obj2 (Studenti)
                $obj2->addTasse($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining all related tables
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAll(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }

    /**
     * Selects a collection of Tasse objects pre-filled with all related objects.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAll(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol2 = TassePeer::NUM_HYDRATE_COLUMNS;

        TipiTassePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + TipiTassePeer::NUM_HYDRATE_COLUMNS;

        StudentiCompletiPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + StudentiCompletiPeer::NUM_HYDRATE_COLUMNS;

        StudentiPeer::addSelectColumns($criteria);
        $startcol5 = $startcol4 + StudentiPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            // Add objects for joined TipiTasse rows

            $key2 = TipiTassePeer::getPrimaryKeyHashFromRow($row, $startcol2);
            if ($key2 !== null) {
                $obj2 = TipiTassePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = TipiTassePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    TipiTassePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 loaded

                // Add the $obj1 (Tasse) to the collection in $obj2 (TipiTasse)
                $obj2->addTasse($obj1);
            } // if joined row not null

            // Add objects for joined StudentiCompleti rows

            $key3 = StudentiCompletiPeer::getPrimaryKeyHashFromRow($row, $startcol3);
            if ($key3 !== null) {
                $obj3 = StudentiCompletiPeer::getInstanceFromPool($key3);
                if (!$obj3) {

                    $cls = StudentiCompletiPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    StudentiCompletiPeer::addInstanceToPool($obj3, $key3);
                } // if obj3 loaded

                // Add the $obj1 (Tasse) to the collection in $obj3 (StudentiCompleti)
                $obj3->addTasse($obj1);
            } // if joined row not null

            // Add objects for joined Studenti rows

            $key4 = StudentiPeer::getPrimaryKeyHashFromRow($row, $startcol4);
            if ($key4 !== null) {
                $obj4 = StudentiPeer::getInstanceFromPool($key4);
                if (!$obj4) {

                    $cls = StudentiPeer::getOMClass();

                    $obj4 = new $cls();
                    $obj4->hydrate($row, $startcol4);
                    StudentiPeer::addInstanceToPool($obj4, $key4);
                } // if obj4 loaded

                // Add the $obj1 (Tasse) to the collection in $obj4 (Studenti)
                $obj4->addTasse($obj1);
            } // if joined row not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining the related TipiTasse table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptTipiTasse(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related StudentiCompleti table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptStudentiCompleti(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related Studenti table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptStudenti(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(TassePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            TassePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Tasse objects pre-filled with all related objects except TipiTasse.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptTipiTasse(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol2 = TassePeer::NUM_HYDRATE_COLUMNS;

        StudentiCompletiPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + StudentiCompletiPeer::NUM_HYDRATE_COLUMNS;

        StudentiPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + StudentiPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined StudentiCompleti rows

                $key2 = StudentiCompletiPeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = StudentiCompletiPeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = StudentiCompletiPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    StudentiCompletiPeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Tasse) to the collection in $obj2 (StudentiCompleti)
                $obj2->addTasse($obj1);

            } // if joined row is not null

                // Add objects for joined Studenti rows

                $key3 = StudentiPeer::getPrimaryKeyHashFromRow($row, $startcol3);
                if ($key3 !== null) {
                    $obj3 = StudentiPeer::getInstanceFromPool($key3);
                    if (!$obj3) {

                        $cls = StudentiPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    StudentiPeer::addInstanceToPool($obj3, $key3);
                } // if $obj3 already loaded

                // Add the $obj1 (Tasse) to the collection in $obj3 (Studenti)
                $obj3->addTasse($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Tasse objects pre-filled with all related objects except StudentiCompleti.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptStudentiCompleti(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol2 = TassePeer::NUM_HYDRATE_COLUMNS;

        TipiTassePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + TipiTassePeer::NUM_HYDRATE_COLUMNS;

        StudentiPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + StudentiPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiPeer::ID_STUDENTE, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined TipiTasse rows

                $key2 = TipiTassePeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = TipiTassePeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = TipiTassePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    TipiTassePeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Tasse) to the collection in $obj2 (TipiTasse)
                $obj2->addTasse($obj1);

            } // if joined row is not null

                // Add objects for joined Studenti rows

                $key3 = StudentiPeer::getPrimaryKeyHashFromRow($row, $startcol3);
                if ($key3 !== null) {
                    $obj3 = StudentiPeer::getInstanceFromPool($key3);
                    if (!$obj3) {

                        $cls = StudentiPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    StudentiPeer::addInstanceToPool($obj3, $key3);
                } // if $obj3 already loaded

                // Add the $obj1 (Tasse) to the collection in $obj3 (Studenti)
                $obj3->addTasse($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Tasse objects pre-filled with all related objects except Studenti.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Tasse objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptStudenti(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(TassePeer::DATABASE_NAME);
        }

        TassePeer::addSelectColumns($criteria);
        $startcol2 = TassePeer::NUM_HYDRATE_COLUMNS;

        TipiTassePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + TipiTassePeer::NUM_HYDRATE_COLUMNS;

        StudentiCompletiPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + StudentiCompletiPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(TassePeer::ID_TIPO_TASSA, TipiTassePeer::ID_TIPO_TASSA, $join_behavior);

        $criteria->addJoin(TassePeer::ID_STUDENTE, StudentiCompletiPeer::ID_STUDENTE, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = TassePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = TassePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = TassePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                TassePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined TipiTasse rows

                $key2 = TipiTassePeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = TipiTassePeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = TipiTassePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    TipiTassePeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Tasse) to the collection in $obj2 (TipiTasse)
                $obj2->addTasse($obj1);

            } // if joined row is not null

                // Add objects for joined StudentiCompleti rows

                $key3 = StudentiCompletiPeer::getPrimaryKeyHashFromRow($row, $startcol3);
                if ($key3 !== null) {
                    $obj3 = StudentiCompletiPeer::getInstanceFromPool($key3);
                    if (!$obj3) {

                        $cls = StudentiCompletiPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    StudentiCompletiPeer::addInstanceToPool($obj3, $key3);
                } // if $obj3 already loaded

                // Add the $obj1 (Tasse) to the collection in $obj3 (StudentiCompleti)
                $obj3->addTasse($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(TassePeer::DATABASE_NAME)->getTable(TassePeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseTassePeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseTassePeer::TABLE_NAME)) {
        $dbMap->addTableObject(new TasseTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return TassePeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a Tasse or Criteria object.
     *
     * @param      mixed $values Criteria or Tasse object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from Tasse object
        }

        if ($criteria->containsKey(TassePeer::ID_TASSE) && $criteria->keyContainsValue(TassePeer::ID_TASSE) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.TassePeer::ID_TASSE.')');
        }


        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a Tasse or Criteria object.
     *
     * @param      mixed $values Criteria or Tasse object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(TassePeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(TassePeer::ID_TASSE);
            $value = $criteria->remove(TassePeer::ID_TASSE);
            if ($value) {
                $selectCriteria->add(TassePeer::ID_TASSE, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(TassePeer::TABLE_NAME);
            }

        } else { // $values is Tasse object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the tasse table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(TassePeer::TABLE_NAME, $con, TassePeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            TassePeer::clearInstancePool();
            TassePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a Tasse or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or Tasse object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            TassePeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof Tasse) { // it's a model object
            // invalidate the cache for this single object
            TassePeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(TassePeer::DATABASE_NAME);
            $criteria->add(TassePeer::ID_TASSE, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                TassePeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(TassePeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            TassePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given Tasse object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param Tasse $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(TassePeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(TassePeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        if ($obj->isNew() || $obj->isColumnModified(TassePeer::NUMERO_VERSAMENTO))
            $columns[TassePeer::NUMERO_VERSAMENTO] = $obj->getNumeroVersamento();

        if ($obj->isNew() || $obj->isColumnModified(TassePeer::IMPORTO_VERSATO))
            $columns[TassePeer::IMPORTO_VERSATO] = $obj->getImportoVersato();

        }

        return BasePeer::doValidate(TassePeer::DATABASE_NAME, TassePeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return Tasse
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = TassePeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(TassePeer::DATABASE_NAME);
        $criteria->add(TassePeer::ID_TASSE, $pk);

        $v = TassePeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return Tasse[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(TassePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(TassePeer::DATABASE_NAME);
            $criteria->add(TassePeer::ID_TASSE, $pks, Criteria::IN);
            $objs = TassePeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseTassePeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseTassePeer::buildTableMap();

