<?php

namespace Ccp\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\Classi;
use Ccp\ClassiPeer;
use Ccp\ClassiQuery;

/**
 * Base class that represents a query for the 'classi' table.
 *
 *
 *
 * @method ClassiQuery orderByIdClasse($order = Criteria::ASC) Order by the id_classe column
 * @method ClassiQuery orderByClasse($order = Criteria::ASC) Order by the classe column
 * @method ClassiQuery orderBySezione($order = Criteria::ASC) Order by the sezione column
 * @method ClassiQuery orderByIdIndirizzo($order = Criteria::ASC) Order by the id_indirizzo column
 * @method ClassiQuery orderByCodiceRegistro($order = Criteria::ASC) Order by the codice_registro column
 * @method ClassiQuery orderByOrdinamento($order = Criteria::ASC) Order by the ordinamento column
 * @method ClassiQuery orderByChiInserisce($order = Criteria::ASC) Order by the chi_inserisce column
 * @method ClassiQuery orderByDataInserimento($order = Criteria::ASC) Order by the data_inserimento column
 * @method ClassiQuery orderByTipoInserimento($order = Criteria::ASC) Order by the tipo_inserimento column
 * @method ClassiQuery orderByChiModifica($order = Criteria::ASC) Order by the chi_modifica column
 * @method ClassiQuery orderByDataModifica($order = Criteria::ASC) Order by the data_modifica column
 * @method ClassiQuery orderByTipoModifica($order = Criteria::ASC) Order by the tipo_modifica column
 * @method ClassiQuery orderByFlagCanc($order = Criteria::ASC) Order by the flag_canc column
 * @method ClassiQuery orderByCodiceRegistro2($order = Criteria::ASC) Order by the codice_registro_2 column
 * @method ClassiQuery orderByCodiceRegistro3($order = Criteria::ASC) Order by the codice_registro_3 column
 * @method ClassiQuery orderByCodiceRegistro4($order = Criteria::ASC) Order by the codice_registro_4 column
 * @method ClassiQuery orderByBloccoScrutini($order = Criteria::ASC) Order by the blocco_scrutini column
 * @method ClassiQuery orderByConsiglioClasseAttivo($order = Criteria::ASC) Order by the consiglio_classe_attivo column
 * @method ClassiQuery orderByTempoFunzionamento($order = Criteria::ASC) Order by the tempo_funzionamento column
 * @method ClassiQuery orderByPubbPrimoScritto($order = Criteria::ASC) Order by the pubb_primo_scritto column
 * @method ClassiQuery orderByPubbSecondoScritto($order = Criteria::ASC) Order by the pubb_secondo_scritto column
 * @method ClassiQuery orderByPubbTerzoScritto($order = Criteria::ASC) Order by the pubb_terzo_scritto column
 * @method ClassiQuery orderByPubbOrale($order = Criteria::ASC) Order by the pubb_orale column
 * @method ClassiQuery orderByIdFlusso($order = Criteria::ASC) Order by the id_flusso column
 * @method ClassiQuery orderByAutenticazioneAlternativa($order = Criteria::ASC) Order by the autenticazione_alternativa column
 * @method ClassiQuery orderByEffettuaControlloGate($order = Criteria::ASC) Order by the effettua_controllo_gate column
 * @method ClassiQuery orderByDataAggiornamentoSidi($order = Criteria::ASC) Order by the data_aggiornamento_sidi column
 *
 * @method ClassiQuery groupByIdClasse() Group by the id_classe column
 * @method ClassiQuery groupByClasse() Group by the classe column
 * @method ClassiQuery groupBySezione() Group by the sezione column
 * @method ClassiQuery groupByIdIndirizzo() Group by the id_indirizzo column
 * @method ClassiQuery groupByCodiceRegistro() Group by the codice_registro column
 * @method ClassiQuery groupByOrdinamento() Group by the ordinamento column
 * @method ClassiQuery groupByChiInserisce() Group by the chi_inserisce column
 * @method ClassiQuery groupByDataInserimento() Group by the data_inserimento column
 * @method ClassiQuery groupByTipoInserimento() Group by the tipo_inserimento column
 * @method ClassiQuery groupByChiModifica() Group by the chi_modifica column
 * @method ClassiQuery groupByDataModifica() Group by the data_modifica column
 * @method ClassiQuery groupByTipoModifica() Group by the tipo_modifica column
 * @method ClassiQuery groupByFlagCanc() Group by the flag_canc column
 * @method ClassiQuery groupByCodiceRegistro2() Group by the codice_registro_2 column
 * @method ClassiQuery groupByCodiceRegistro3() Group by the codice_registro_3 column
 * @method ClassiQuery groupByCodiceRegistro4() Group by the codice_registro_4 column
 * @method ClassiQuery groupByBloccoScrutini() Group by the blocco_scrutini column
 * @method ClassiQuery groupByConsiglioClasseAttivo() Group by the consiglio_classe_attivo column
 * @method ClassiQuery groupByTempoFunzionamento() Group by the tempo_funzionamento column
 * @method ClassiQuery groupByPubbPrimoScritto() Group by the pubb_primo_scritto column
 * @method ClassiQuery groupByPubbSecondoScritto() Group by the pubb_secondo_scritto column
 * @method ClassiQuery groupByPubbTerzoScritto() Group by the pubb_terzo_scritto column
 * @method ClassiQuery groupByPubbOrale() Group by the pubb_orale column
 * @method ClassiQuery groupByIdFlusso() Group by the id_flusso column
 * @method ClassiQuery groupByAutenticazioneAlternativa() Group by the autenticazione_alternativa column
 * @method ClassiQuery groupByEffettuaControlloGate() Group by the effettua_controllo_gate column
 * @method ClassiQuery groupByDataAggiornamentoSidi() Group by the data_aggiornamento_sidi column
 *
 * @method ClassiQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method ClassiQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method ClassiQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method Classi findOne(PropelPDO $con = null) Return the first Classi matching the query
 * @method Classi findOneOrCreate(PropelPDO $con = null) Return the first Classi matching the query, or a new Classi object populated from the query conditions when no match is found
 *
 * @method Classi findOneByClasse(string $classe) Return the first Classi filtered by the classe column
 * @method Classi findOneBySezione(string $sezione) Return the first Classi filtered by the sezione column
 * @method Classi findOneByIdIndirizzo(int $id_indirizzo) Return the first Classi filtered by the id_indirizzo column
 * @method Classi findOneByCodiceRegistro(string $codice_registro) Return the first Classi filtered by the codice_registro column
 * @method Classi findOneByOrdinamento(string $ordinamento) Return the first Classi filtered by the ordinamento column
 * @method Classi findOneByChiInserisce(string $chi_inserisce) Return the first Classi filtered by the chi_inserisce column
 * @method Classi findOneByDataInserimento(string $data_inserimento) Return the first Classi filtered by the data_inserimento column
 * @method Classi findOneByTipoInserimento(string $tipo_inserimento) Return the first Classi filtered by the tipo_inserimento column
 * @method Classi findOneByChiModifica(string $chi_modifica) Return the first Classi filtered by the chi_modifica column
 * @method Classi findOneByDataModifica(string $data_modifica) Return the first Classi filtered by the data_modifica column
 * @method Classi findOneByTipoModifica(string $tipo_modifica) Return the first Classi filtered by the tipo_modifica column
 * @method Classi findOneByFlagCanc(string $flag_canc) Return the first Classi filtered by the flag_canc column
 * @method Classi findOneByCodiceRegistro2(string $codice_registro_2) Return the first Classi filtered by the codice_registro_2 column
 * @method Classi findOneByCodiceRegistro3(string $codice_registro_3) Return the first Classi filtered by the codice_registro_3 column
 * @method Classi findOneByCodiceRegistro4(string $codice_registro_4) Return the first Classi filtered by the codice_registro_4 column
 * @method Classi findOneByBloccoScrutini(string $blocco_scrutini) Return the first Classi filtered by the blocco_scrutini column
 * @method Classi findOneByConsiglioClasseAttivo(string $consiglio_classe_attivo) Return the first Classi filtered by the consiglio_classe_attivo column
 * @method Classi findOneByTempoFunzionamento(string $tempo_funzionamento) Return the first Classi filtered by the tempo_funzionamento column
 * @method Classi findOneByPubbPrimoScritto(string $pubb_primo_scritto) Return the first Classi filtered by the pubb_primo_scritto column
 * @method Classi findOneByPubbSecondoScritto(string $pubb_secondo_scritto) Return the first Classi filtered by the pubb_secondo_scritto column
 * @method Classi findOneByPubbTerzoScritto(string $pubb_terzo_scritto) Return the first Classi filtered by the pubb_terzo_scritto column
 * @method Classi findOneByPubbOrale(string $pubb_orale) Return the first Classi filtered by the pubb_orale column
 * @method Classi findOneByIdFlusso(string $id_flusso) Return the first Classi filtered by the id_flusso column
 * @method Classi findOneByAutenticazioneAlternativa(string $autenticazione_alternativa) Return the first Classi filtered by the autenticazione_alternativa column
 * @method Classi findOneByEffettuaControlloGate(string $effettua_controllo_gate) Return the first Classi filtered by the effettua_controllo_gate column
 * @method Classi findOneByDataAggiornamentoSidi(int $data_aggiornamento_sidi) Return the first Classi filtered by the data_aggiornamento_sidi column
 *
 * @method array findByIdClasse(int $id_classe) Return Classi objects filtered by the id_classe column
 * @method array findByClasse(string $classe) Return Classi objects filtered by the classe column
 * @method array findBySezione(string $sezione) Return Classi objects filtered by the sezione column
 * @method array findByIdIndirizzo(int $id_indirizzo) Return Classi objects filtered by the id_indirizzo column
 * @method array findByCodiceRegistro(string $codice_registro) Return Classi objects filtered by the codice_registro column
 * @method array findByOrdinamento(string $ordinamento) Return Classi objects filtered by the ordinamento column
 * @method array findByChiInserisce(string $chi_inserisce) Return Classi objects filtered by the chi_inserisce column
 * @method array findByDataInserimento(string $data_inserimento) Return Classi objects filtered by the data_inserimento column
 * @method array findByTipoInserimento(string $tipo_inserimento) Return Classi objects filtered by the tipo_inserimento column
 * @method array findByChiModifica(string $chi_modifica) Return Classi objects filtered by the chi_modifica column
 * @method array findByDataModifica(string $data_modifica) Return Classi objects filtered by the data_modifica column
 * @method array findByTipoModifica(string $tipo_modifica) Return Classi objects filtered by the tipo_modifica column
 * @method array findByFlagCanc(string $flag_canc) Return Classi objects filtered by the flag_canc column
 * @method array findByCodiceRegistro2(string $codice_registro_2) Return Classi objects filtered by the codice_registro_2 column
 * @method array findByCodiceRegistro3(string $codice_registro_3) Return Classi objects filtered by the codice_registro_3 column
 * @method array findByCodiceRegistro4(string $codice_registro_4) Return Classi objects filtered by the codice_registro_4 column
 * @method array findByBloccoScrutini(string $blocco_scrutini) Return Classi objects filtered by the blocco_scrutini column
 * @method array findByConsiglioClasseAttivo(string $consiglio_classe_attivo) Return Classi objects filtered by the consiglio_classe_attivo column
 * @method array findByTempoFunzionamento(string $tempo_funzionamento) Return Classi objects filtered by the tempo_funzionamento column
 * @method array findByPubbPrimoScritto(string $pubb_primo_scritto) Return Classi objects filtered by the pubb_primo_scritto column
 * @method array findByPubbSecondoScritto(string $pubb_secondo_scritto) Return Classi objects filtered by the pubb_secondo_scritto column
 * @method array findByPubbTerzoScritto(string $pubb_terzo_scritto) Return Classi objects filtered by the pubb_terzo_scritto column
 * @method array findByPubbOrale(string $pubb_orale) Return Classi objects filtered by the pubb_orale column
 * @method array findByIdFlusso(string $id_flusso) Return Classi objects filtered by the id_flusso column
 * @method array findByAutenticazioneAlternativa(string $autenticazione_alternativa) Return Classi objects filtered by the autenticazione_alternativa column
 * @method array findByEffettuaControlloGate(string $effettua_controllo_gate) Return Classi objects filtered by the effettua_controllo_gate column
 * @method array findByDataAggiornamentoSidi(int $data_aggiornamento_sidi) Return Classi objects filtered by the data_aggiornamento_sidi column
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseClassiQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseClassiQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Ccp\\Classi';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new ClassiQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   ClassiQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return ClassiQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof ClassiQuery) {
            return $criteria;
        }
        $query = new ClassiQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Classi|Classi[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = ClassiPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(ClassiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Classi A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByIdClasse($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Classi A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id_classe", "classe", "sezione", "id_indirizzo", "codice_registro", "ordinamento", "chi_inserisce", "data_inserimento", "tipo_inserimento", "chi_modifica", "data_modifica", "tipo_modifica", "flag_canc", "codice_registro_2", "codice_registro_3", "codice_registro_4", "blocco_scrutini", "consiglio_classe_attivo", "tempo_funzionamento", "pubb_primo_scritto", "pubb_secondo_scritto", "pubb_terzo_scritto", "pubb_orale", "id_flusso", "autenticazione_alternativa", "effettua_controllo_gate", "data_aggiornamento_sidi" FROM "classi" WHERE "id_classe" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Classi();
            $obj->hydrate($row);
            ClassiPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Classi|Classi[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Classi[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(ClassiPeer::ID_CLASSE, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(ClassiPeer::ID_CLASSE, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id_classe column
     *
     * Example usage:
     * <code>
     * $query->filterByIdClasse(1234); // WHERE id_classe = 1234
     * $query->filterByIdClasse(array(12, 34)); // WHERE id_classe IN (12, 34)
     * $query->filterByIdClasse(array('min' => 12)); // WHERE id_classe >= 12
     * $query->filterByIdClasse(array('max' => 12)); // WHERE id_classe <= 12
     * </code>
     *
     * @param     mixed $idClasse The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByIdClasse($idClasse = null, $comparison = null)
    {
        if (is_array($idClasse)) {
            $useMinMax = false;
            if (isset($idClasse['min'])) {
                $this->addUsingAlias(ClassiPeer::ID_CLASSE, $idClasse['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idClasse['max'])) {
                $this->addUsingAlias(ClassiPeer::ID_CLASSE, $idClasse['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::ID_CLASSE, $idClasse, $comparison);
    }

    /**
     * Filter the query on the classe column
     *
     * Example usage:
     * <code>
     * $query->filterByClasse('fooValue');   // WHERE classe = 'fooValue'
     * $query->filterByClasse('%fooValue%'); // WHERE classe LIKE '%fooValue%'
     * </code>
     *
     * @param     string $classe The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByClasse($classe = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($classe)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $classe)) {
                $classe = str_replace('*', '%', $classe);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CLASSE, $classe, $comparison);
    }

    /**
     * Filter the query on the sezione column
     *
     * Example usage:
     * <code>
     * $query->filterBySezione('fooValue');   // WHERE sezione = 'fooValue'
     * $query->filterBySezione('%fooValue%'); // WHERE sezione LIKE '%fooValue%'
     * </code>
     *
     * @param     string $sezione The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterBySezione($sezione = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($sezione)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $sezione)) {
                $sezione = str_replace('*', '%', $sezione);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::SEZIONE, $sezione, $comparison);
    }

    /**
     * Filter the query on the id_indirizzo column
     *
     * Example usage:
     * <code>
     * $query->filterByIdIndirizzo(1234); // WHERE id_indirizzo = 1234
     * $query->filterByIdIndirizzo(array(12, 34)); // WHERE id_indirizzo IN (12, 34)
     * $query->filterByIdIndirizzo(array('min' => 12)); // WHERE id_indirizzo >= 12
     * $query->filterByIdIndirizzo(array('max' => 12)); // WHERE id_indirizzo <= 12
     * </code>
     *
     * @param     mixed $idIndirizzo The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByIdIndirizzo($idIndirizzo = null, $comparison = null)
    {
        if (is_array($idIndirizzo)) {
            $useMinMax = false;
            if (isset($idIndirizzo['min'])) {
                $this->addUsingAlias(ClassiPeer::ID_INDIRIZZO, $idIndirizzo['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($idIndirizzo['max'])) {
                $this->addUsingAlias(ClassiPeer::ID_INDIRIZZO, $idIndirizzo['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::ID_INDIRIZZO, $idIndirizzo, $comparison);
    }

    /**
     * Filter the query on the codice_registro column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceRegistro('fooValue');   // WHERE codice_registro = 'fooValue'
     * $query->filterByCodiceRegistro('%fooValue%'); // WHERE codice_registro LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceRegistro The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByCodiceRegistro($codiceRegistro = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceRegistro)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceRegistro)) {
                $codiceRegistro = str_replace('*', '%', $codiceRegistro);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CODICE_REGISTRO, $codiceRegistro, $comparison);
    }

    /**
     * Filter the query on the ordinamento column
     *
     * Example usage:
     * <code>
     * $query->filterByOrdinamento('fooValue');   // WHERE ordinamento = 'fooValue'
     * $query->filterByOrdinamento('%fooValue%'); // WHERE ordinamento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $ordinamento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByOrdinamento($ordinamento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($ordinamento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $ordinamento)) {
                $ordinamento = str_replace('*', '%', $ordinamento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::ORDINAMENTO, $ordinamento, $comparison);
    }

    /**
     * Filter the query on the chi_inserisce column
     *
     * Example usage:
     * <code>
     * $query->filterByChiInserisce(1234); // WHERE chi_inserisce = 1234
     * $query->filterByChiInserisce(array(12, 34)); // WHERE chi_inserisce IN (12, 34)
     * $query->filterByChiInserisce(array('min' => 12)); // WHERE chi_inserisce >= 12
     * $query->filterByChiInserisce(array('max' => 12)); // WHERE chi_inserisce <= 12
     * </code>
     *
     * @param     mixed $chiInserisce The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByChiInserisce($chiInserisce = null, $comparison = null)
    {
        if (is_array($chiInserisce)) {
            $useMinMax = false;
            if (isset($chiInserisce['min'])) {
                $this->addUsingAlias(ClassiPeer::CHI_INSERISCE, $chiInserisce['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($chiInserisce['max'])) {
                $this->addUsingAlias(ClassiPeer::CHI_INSERISCE, $chiInserisce['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CHI_INSERISCE, $chiInserisce, $comparison);
    }

    /**
     * Filter the query on the data_inserimento column
     *
     * Example usage:
     * <code>
     * $query->filterByDataInserimento(1234); // WHERE data_inserimento = 1234
     * $query->filterByDataInserimento(array(12, 34)); // WHERE data_inserimento IN (12, 34)
     * $query->filterByDataInserimento(array('min' => 12)); // WHERE data_inserimento >= 12
     * $query->filterByDataInserimento(array('max' => 12)); // WHERE data_inserimento <= 12
     * </code>
     *
     * @param     mixed $dataInserimento The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByDataInserimento($dataInserimento = null, $comparison = null)
    {
        if (is_array($dataInserimento)) {
            $useMinMax = false;
            if (isset($dataInserimento['min'])) {
                $this->addUsingAlias(ClassiPeer::DATA_INSERIMENTO, $dataInserimento['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataInserimento['max'])) {
                $this->addUsingAlias(ClassiPeer::DATA_INSERIMENTO, $dataInserimento['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::DATA_INSERIMENTO, $dataInserimento, $comparison);
    }

    /**
     * Filter the query on the tipo_inserimento column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoInserimento('fooValue');   // WHERE tipo_inserimento = 'fooValue'
     * $query->filterByTipoInserimento('%fooValue%'); // WHERE tipo_inserimento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoInserimento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByTipoInserimento($tipoInserimento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoInserimento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoInserimento)) {
                $tipoInserimento = str_replace('*', '%', $tipoInserimento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::TIPO_INSERIMENTO, $tipoInserimento, $comparison);
    }

    /**
     * Filter the query on the chi_modifica column
     *
     * Example usage:
     * <code>
     * $query->filterByChiModifica(1234); // WHERE chi_modifica = 1234
     * $query->filterByChiModifica(array(12, 34)); // WHERE chi_modifica IN (12, 34)
     * $query->filterByChiModifica(array('min' => 12)); // WHERE chi_modifica >= 12
     * $query->filterByChiModifica(array('max' => 12)); // WHERE chi_modifica <= 12
     * </code>
     *
     * @param     mixed $chiModifica The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByChiModifica($chiModifica = null, $comparison = null)
    {
        if (is_array($chiModifica)) {
            $useMinMax = false;
            if (isset($chiModifica['min'])) {
                $this->addUsingAlias(ClassiPeer::CHI_MODIFICA, $chiModifica['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($chiModifica['max'])) {
                $this->addUsingAlias(ClassiPeer::CHI_MODIFICA, $chiModifica['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CHI_MODIFICA, $chiModifica, $comparison);
    }

    /**
     * Filter the query on the data_modifica column
     *
     * Example usage:
     * <code>
     * $query->filterByDataModifica(1234); // WHERE data_modifica = 1234
     * $query->filterByDataModifica(array(12, 34)); // WHERE data_modifica IN (12, 34)
     * $query->filterByDataModifica(array('min' => 12)); // WHERE data_modifica >= 12
     * $query->filterByDataModifica(array('max' => 12)); // WHERE data_modifica <= 12
     * </code>
     *
     * @param     mixed $dataModifica The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByDataModifica($dataModifica = null, $comparison = null)
    {
        if (is_array($dataModifica)) {
            $useMinMax = false;
            if (isset($dataModifica['min'])) {
                $this->addUsingAlias(ClassiPeer::DATA_MODIFICA, $dataModifica['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataModifica['max'])) {
                $this->addUsingAlias(ClassiPeer::DATA_MODIFICA, $dataModifica['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::DATA_MODIFICA, $dataModifica, $comparison);
    }

    /**
     * Filter the query on the tipo_modifica column
     *
     * Example usage:
     * <code>
     * $query->filterByTipoModifica('fooValue');   // WHERE tipo_modifica = 'fooValue'
     * $query->filterByTipoModifica('%fooValue%'); // WHERE tipo_modifica LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tipoModifica The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByTipoModifica($tipoModifica = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tipoModifica)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tipoModifica)) {
                $tipoModifica = str_replace('*', '%', $tipoModifica);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::TIPO_MODIFICA, $tipoModifica, $comparison);
    }

    /**
     * Filter the query on the flag_canc column
     *
     * Example usage:
     * <code>
     * $query->filterByFlagCanc(1234); // WHERE flag_canc = 1234
     * $query->filterByFlagCanc(array(12, 34)); // WHERE flag_canc IN (12, 34)
     * $query->filterByFlagCanc(array('min' => 12)); // WHERE flag_canc >= 12
     * $query->filterByFlagCanc(array('max' => 12)); // WHERE flag_canc <= 12
     * </code>
     *
     * @param     mixed $flagCanc The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByFlagCanc($flagCanc = null, $comparison = null)
    {
        if (is_array($flagCanc)) {
            $useMinMax = false;
            if (isset($flagCanc['min'])) {
                $this->addUsingAlias(ClassiPeer::FLAG_CANC, $flagCanc['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($flagCanc['max'])) {
                $this->addUsingAlias(ClassiPeer::FLAG_CANC, $flagCanc['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::FLAG_CANC, $flagCanc, $comparison);
    }

    /**
     * Filter the query on the codice_registro_2 column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceRegistro2('fooValue');   // WHERE codice_registro_2 = 'fooValue'
     * $query->filterByCodiceRegistro2('%fooValue%'); // WHERE codice_registro_2 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceRegistro2 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByCodiceRegistro2($codiceRegistro2 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceRegistro2)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceRegistro2)) {
                $codiceRegistro2 = str_replace('*', '%', $codiceRegistro2);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CODICE_REGISTRO_2, $codiceRegistro2, $comparison);
    }

    /**
     * Filter the query on the codice_registro_3 column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceRegistro3('fooValue');   // WHERE codice_registro_3 = 'fooValue'
     * $query->filterByCodiceRegistro3('%fooValue%'); // WHERE codice_registro_3 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceRegistro3 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByCodiceRegistro3($codiceRegistro3 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceRegistro3)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceRegistro3)) {
                $codiceRegistro3 = str_replace('*', '%', $codiceRegistro3);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CODICE_REGISTRO_3, $codiceRegistro3, $comparison);
    }

    /**
     * Filter the query on the codice_registro_4 column
     *
     * Example usage:
     * <code>
     * $query->filterByCodiceRegistro4('fooValue');   // WHERE codice_registro_4 = 'fooValue'
     * $query->filterByCodiceRegistro4('%fooValue%'); // WHERE codice_registro_4 LIKE '%fooValue%'
     * </code>
     *
     * @param     string $codiceRegistro4 The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByCodiceRegistro4($codiceRegistro4 = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($codiceRegistro4)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $codiceRegistro4)) {
                $codiceRegistro4 = str_replace('*', '%', $codiceRegistro4);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CODICE_REGISTRO_4, $codiceRegistro4, $comparison);
    }

    /**
     * Filter the query on the blocco_scrutini column
     *
     * Example usage:
     * <code>
     * $query->filterByBloccoScrutini('fooValue');   // WHERE blocco_scrutini = 'fooValue'
     * $query->filterByBloccoScrutini('%fooValue%'); // WHERE blocco_scrutini LIKE '%fooValue%'
     * </code>
     *
     * @param     string $bloccoScrutini The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByBloccoScrutini($bloccoScrutini = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($bloccoScrutini)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $bloccoScrutini)) {
                $bloccoScrutini = str_replace('*', '%', $bloccoScrutini);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::BLOCCO_SCRUTINI, $bloccoScrutini, $comparison);
    }

    /**
     * Filter the query on the consiglio_classe_attivo column
     *
     * Example usage:
     * <code>
     * $query->filterByConsiglioClasseAttivo('fooValue');   // WHERE consiglio_classe_attivo = 'fooValue'
     * $query->filterByConsiglioClasseAttivo('%fooValue%'); // WHERE consiglio_classe_attivo LIKE '%fooValue%'
     * </code>
     *
     * @param     string $consiglioClasseAttivo The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByConsiglioClasseAttivo($consiglioClasseAttivo = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($consiglioClasseAttivo)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $consiglioClasseAttivo)) {
                $consiglioClasseAttivo = str_replace('*', '%', $consiglioClasseAttivo);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::CONSIGLIO_CLASSE_ATTIVO, $consiglioClasseAttivo, $comparison);
    }

    /**
     * Filter the query on the tempo_funzionamento column
     *
     * Example usage:
     * <code>
     * $query->filterByTempoFunzionamento('fooValue');   // WHERE tempo_funzionamento = 'fooValue'
     * $query->filterByTempoFunzionamento('%fooValue%'); // WHERE tempo_funzionamento LIKE '%fooValue%'
     * </code>
     *
     * @param     string $tempoFunzionamento The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByTempoFunzionamento($tempoFunzionamento = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($tempoFunzionamento)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $tempoFunzionamento)) {
                $tempoFunzionamento = str_replace('*', '%', $tempoFunzionamento);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::TEMPO_FUNZIONAMENTO, $tempoFunzionamento, $comparison);
    }

    /**
     * Filter the query on the pubb_primo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByPubbPrimoScritto('fooValue');   // WHERE pubb_primo_scritto = 'fooValue'
     * $query->filterByPubbPrimoScritto('%fooValue%'); // WHERE pubb_primo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $pubbPrimoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByPubbPrimoScritto($pubbPrimoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($pubbPrimoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $pubbPrimoScritto)) {
                $pubbPrimoScritto = str_replace('*', '%', $pubbPrimoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::PUBB_PRIMO_SCRITTO, $pubbPrimoScritto, $comparison);
    }

    /**
     * Filter the query on the pubb_secondo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByPubbSecondoScritto('fooValue');   // WHERE pubb_secondo_scritto = 'fooValue'
     * $query->filterByPubbSecondoScritto('%fooValue%'); // WHERE pubb_secondo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $pubbSecondoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByPubbSecondoScritto($pubbSecondoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($pubbSecondoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $pubbSecondoScritto)) {
                $pubbSecondoScritto = str_replace('*', '%', $pubbSecondoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::PUBB_SECONDO_SCRITTO, $pubbSecondoScritto, $comparison);
    }

    /**
     * Filter the query on the pubb_terzo_scritto column
     *
     * Example usage:
     * <code>
     * $query->filterByPubbTerzoScritto('fooValue');   // WHERE pubb_terzo_scritto = 'fooValue'
     * $query->filterByPubbTerzoScritto('%fooValue%'); // WHERE pubb_terzo_scritto LIKE '%fooValue%'
     * </code>
     *
     * @param     string $pubbTerzoScritto The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByPubbTerzoScritto($pubbTerzoScritto = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($pubbTerzoScritto)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $pubbTerzoScritto)) {
                $pubbTerzoScritto = str_replace('*', '%', $pubbTerzoScritto);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::PUBB_TERZO_SCRITTO, $pubbTerzoScritto, $comparison);
    }

    /**
     * Filter the query on the pubb_orale column
     *
     * Example usage:
     * <code>
     * $query->filterByPubbOrale('fooValue');   // WHERE pubb_orale = 'fooValue'
     * $query->filterByPubbOrale('%fooValue%'); // WHERE pubb_orale LIKE '%fooValue%'
     * </code>
     *
     * @param     string $pubbOrale The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByPubbOrale($pubbOrale = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($pubbOrale)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $pubbOrale)) {
                $pubbOrale = str_replace('*', '%', $pubbOrale);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::PUBB_ORALE, $pubbOrale, $comparison);
    }

    /**
     * Filter the query on the id_flusso column
     *
     * Example usage:
     * <code>
     * $query->filterByIdFlusso('fooValue');   // WHERE id_flusso = 'fooValue'
     * $query->filterByIdFlusso('%fooValue%'); // WHERE id_flusso LIKE '%fooValue%'
     * </code>
     *
     * @param     string $idFlusso The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByIdFlusso($idFlusso = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($idFlusso)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $idFlusso)) {
                $idFlusso = str_replace('*', '%', $idFlusso);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::ID_FLUSSO, $idFlusso, $comparison);
    }

    /**
     * Filter the query on the autenticazione_alternativa column
     *
     * Example usage:
     * <code>
     * $query->filterByAutenticazioneAlternativa('fooValue');   // WHERE autenticazione_alternativa = 'fooValue'
     * $query->filterByAutenticazioneAlternativa('%fooValue%'); // WHERE autenticazione_alternativa LIKE '%fooValue%'
     * </code>
     *
     * @param     string $autenticazioneAlternativa The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByAutenticazioneAlternativa($autenticazioneAlternativa = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($autenticazioneAlternativa)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $autenticazioneAlternativa)) {
                $autenticazioneAlternativa = str_replace('*', '%', $autenticazioneAlternativa);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::AUTENTICAZIONE_ALTERNATIVA, $autenticazioneAlternativa, $comparison);
    }

    /**
     * Filter the query on the effettua_controllo_gate column
     *
     * Example usage:
     * <code>
     * $query->filterByEffettuaControlloGate('fooValue');   // WHERE effettua_controllo_gate = 'fooValue'
     * $query->filterByEffettuaControlloGate('%fooValue%'); // WHERE effettua_controllo_gate LIKE '%fooValue%'
     * </code>
     *
     * @param     string $effettuaControlloGate The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByEffettuaControlloGate($effettuaControlloGate = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($effettuaControlloGate)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $effettuaControlloGate)) {
                $effettuaControlloGate = str_replace('*', '%', $effettuaControlloGate);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(ClassiPeer::EFFETTUA_CONTROLLO_GATE, $effettuaControlloGate, $comparison);
    }

    /**
     * Filter the query on the data_aggiornamento_sidi column
     *
     * Example usage:
     * <code>
     * $query->filterByDataAggiornamentoSidi(1234); // WHERE data_aggiornamento_sidi = 1234
     * $query->filterByDataAggiornamentoSidi(array(12, 34)); // WHERE data_aggiornamento_sidi IN (12, 34)
     * $query->filterByDataAggiornamentoSidi(array('min' => 12)); // WHERE data_aggiornamento_sidi >= 12
     * $query->filterByDataAggiornamentoSidi(array('max' => 12)); // WHERE data_aggiornamento_sidi <= 12
     * </code>
     *
     * @param     mixed $dataAggiornamentoSidi The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function filterByDataAggiornamentoSidi($dataAggiornamentoSidi = null, $comparison = null)
    {
        if (is_array($dataAggiornamentoSidi)) {
            $useMinMax = false;
            if (isset($dataAggiornamentoSidi['min'])) {
                $this->addUsingAlias(ClassiPeer::DATA_AGGIORNAMENTO_SIDI, $dataAggiornamentoSidi['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dataAggiornamentoSidi['max'])) {
                $this->addUsingAlias(ClassiPeer::DATA_AGGIORNAMENTO_SIDI, $dataAggiornamentoSidi['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(ClassiPeer::DATA_AGGIORNAMENTO_SIDI, $dataAggiornamentoSidi, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   Classi $classi Object to remove from the list of results
     *
     * @return ClassiQuery The current query, for fluid interface
     */
    public function prune($classi = null)
    {
        if ($classi) {
            $this->addUsingAlias(ClassiPeer::ID_CLASSE, $classi->getIdClasse(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
