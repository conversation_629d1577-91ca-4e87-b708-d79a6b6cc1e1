<?php

namespace Ccp\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Ccp\TaxResiduals;
use Ccp\TaxResidualsPeer;
use Ccp\TaxResidualsQuery;

/**
 * Base class that represents a query for the 'tax_residuals' table.
 *
 *
 *
 * @method TaxResidualsQuery orderById($order = Criteria::ASC) Order by the id column
 * @method TaxResidualsQuery orderByTasse($order = Criteria::ASC) Order by the tasse column
 * @method TaxResidualsQuery orderByContributi($order = Criteria::ASC) Order by the contributi column
 * @method TaxResidualsQuery orderByQuote($order = Criteria::ASC) Order by the quote column
 * @method TaxResidualsQuery orderByDiversi($order = Criteria::ASC) Order by the diversi column
 * @method TaxResidualsQuery orderByDebito($order = Criteria::ASC) Order by the debito column
 *
 * @method TaxResidualsQuery groupById() Group by the id column
 * @method TaxResidualsQuery groupByTasse() Group by the tasse column
 * @method TaxResidualsQuery groupByContributi() Group by the contributi column
 * @method TaxResidualsQuery groupByQuote() Group by the quote column
 * @method TaxResidualsQuery groupByDiversi() Group by the diversi column
 * @method TaxResidualsQuery groupByDebito() Group by the debito column
 *
 * @method TaxResidualsQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method TaxResidualsQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method TaxResidualsQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method TaxResiduals findOne(PropelPDO $con = null) Return the first TaxResiduals matching the query
 * @method TaxResiduals findOneOrCreate(PropelPDO $con = null) Return the first TaxResiduals matching the query, or a new TaxResiduals object populated from the query conditions when no match is found
 *
 * @method TaxResiduals findOneByTasse(double $tasse) Return the first TaxResiduals filtered by the tasse column
 * @method TaxResiduals findOneByContributi(double $contributi) Return the first TaxResiduals filtered by the contributi column
 * @method TaxResiduals findOneByQuote(double $quote) Return the first TaxResiduals filtered by the quote column
 * @method TaxResiduals findOneByDiversi(double $diversi) Return the first TaxResiduals filtered by the diversi column
 * @method TaxResiduals findOneByDebito(double $debito) Return the first TaxResiduals filtered by the debito column
 *
 * @method array findById(int $id) Return TaxResiduals objects filtered by the id column
 * @method array findByTasse(double $tasse) Return TaxResiduals objects filtered by the tasse column
 * @method array findByContributi(double $contributi) Return TaxResiduals objects filtered by the contributi column
 * @method array findByQuote(double $quote) Return TaxResiduals objects filtered by the quote column
 * @method array findByDiversi(double $diversi) Return TaxResiduals objects filtered by the diversi column
 * @method array findByDebito(double $debito) Return TaxResiduals objects filtered by the debito column
 *
 * @package    propel.generator.Ccp.om
 */
abstract class BaseTaxResidualsQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseTaxResidualsQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Ccp\\TaxResiduals';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new TaxResidualsQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   TaxResidualsQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return TaxResidualsQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof TaxResidualsQuery) {
            return $criteria;
        }
        $query = new TaxResidualsQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   TaxResiduals|TaxResiduals[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = TaxResidualsPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(TaxResidualsPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 TaxResiduals A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 TaxResiduals A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "tasse", "contributi", "quote", "diversi", "debito" FROM "tax_residuals" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new TaxResiduals();
            $obj->hydrate($row);
            TaxResidualsPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return TaxResiduals|TaxResiduals[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|TaxResiduals[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(TaxResidualsPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(TaxResidualsPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(TaxResidualsPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(TaxResidualsPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TaxResidualsPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the tasse column
     *
     * Example usage:
     * <code>
     * $query->filterByTasse(1234); // WHERE tasse = 1234
     * $query->filterByTasse(array(12, 34)); // WHERE tasse IN (12, 34)
     * $query->filterByTasse(array('min' => 12)); // WHERE tasse >= 12
     * $query->filterByTasse(array('max' => 12)); // WHERE tasse <= 12
     * </code>
     *
     * @param     mixed $tasse The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByTasse($tasse = null, $comparison = null)
    {
        if (is_array($tasse)) {
            $useMinMax = false;
            if (isset($tasse['min'])) {
                $this->addUsingAlias(TaxResidualsPeer::TASSE, $tasse['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($tasse['max'])) {
                $this->addUsingAlias(TaxResidualsPeer::TASSE, $tasse['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TaxResidualsPeer::TASSE, $tasse, $comparison);
    }

    /**
     * Filter the query on the contributi column
     *
     * Example usage:
     * <code>
     * $query->filterByContributi(1234); // WHERE contributi = 1234
     * $query->filterByContributi(array(12, 34)); // WHERE contributi IN (12, 34)
     * $query->filterByContributi(array('min' => 12)); // WHERE contributi >= 12
     * $query->filterByContributi(array('max' => 12)); // WHERE contributi <= 12
     * </code>
     *
     * @param     mixed $contributi The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByContributi($contributi = null, $comparison = null)
    {
        if (is_array($contributi)) {
            $useMinMax = false;
            if (isset($contributi['min'])) {
                $this->addUsingAlias(TaxResidualsPeer::CONTRIBUTI, $contributi['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($contributi['max'])) {
                $this->addUsingAlias(TaxResidualsPeer::CONTRIBUTI, $contributi['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TaxResidualsPeer::CONTRIBUTI, $contributi, $comparison);
    }

    /**
     * Filter the query on the quote column
     *
     * Example usage:
     * <code>
     * $query->filterByQuote(1234); // WHERE quote = 1234
     * $query->filterByQuote(array(12, 34)); // WHERE quote IN (12, 34)
     * $query->filterByQuote(array('min' => 12)); // WHERE quote >= 12
     * $query->filterByQuote(array('max' => 12)); // WHERE quote <= 12
     * </code>
     *
     * @param     mixed $quote The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByQuote($quote = null, $comparison = null)
    {
        if (is_array($quote)) {
            $useMinMax = false;
            if (isset($quote['min'])) {
                $this->addUsingAlias(TaxResidualsPeer::QUOTE, $quote['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($quote['max'])) {
                $this->addUsingAlias(TaxResidualsPeer::QUOTE, $quote['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TaxResidualsPeer::QUOTE, $quote, $comparison);
    }

    /**
     * Filter the query on the diversi column
     *
     * Example usage:
     * <code>
     * $query->filterByDiversi(1234); // WHERE diversi = 1234
     * $query->filterByDiversi(array(12, 34)); // WHERE diversi IN (12, 34)
     * $query->filterByDiversi(array('min' => 12)); // WHERE diversi >= 12
     * $query->filterByDiversi(array('max' => 12)); // WHERE diversi <= 12
     * </code>
     *
     * @param     mixed $diversi The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByDiversi($diversi = null, $comparison = null)
    {
        if (is_array($diversi)) {
            $useMinMax = false;
            if (isset($diversi['min'])) {
                $this->addUsingAlias(TaxResidualsPeer::DIVERSI, $diversi['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($diversi['max'])) {
                $this->addUsingAlias(TaxResidualsPeer::DIVERSI, $diversi['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TaxResidualsPeer::DIVERSI, $diversi, $comparison);
    }

    /**
     * Filter the query on the debito column
     *
     * Example usage:
     * <code>
     * $query->filterByDebito(1234); // WHERE debito = 1234
     * $query->filterByDebito(array(12, 34)); // WHERE debito IN (12, 34)
     * $query->filterByDebito(array('min' => 12)); // WHERE debito >= 12
     * $query->filterByDebito(array('max' => 12)); // WHERE debito <= 12
     * </code>
     *
     * @param     mixed $debito The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function filterByDebito($debito = null, $comparison = null)
    {
        if (is_array($debito)) {
            $useMinMax = false;
            if (isset($debito['min'])) {
                $this->addUsingAlias(TaxResidualsPeer::DEBITO, $debito['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($debito['max'])) {
                $this->addUsingAlias(TaxResidualsPeer::DEBITO, $debito['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TaxResidualsPeer::DEBITO, $debito, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   TaxResiduals $taxResiduals Object to remove from the list of results
     *
     * @return TaxResidualsQuery The current query, for fluid interface
     */
    public function prune($taxResiduals = null)
    {
        if ($taxResiduals) {
            $this->addUsingAlias(TaxResidualsPeer::ID, $taxResiduals->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
