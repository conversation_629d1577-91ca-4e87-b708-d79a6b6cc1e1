<?php

namespace Ccp;

/*
 * Build and array on subject research in tax insert.
 * We must search in students (3 school year) and employees.
 */

/**
 * mixSearchSubject takes a request data with string query and return a list
 * of corresponding stundents and employee
 *
 * @package    mc2api.CCp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class mixSearchSubject {

	public $list;

	public function __construct($subject) {
		$this->query = $subject;
		$this->list = array();
	}

	public function getFilteredList() {
		/**
		 * Get current, previus and next school year string database will make a search on
		 */
		$taxPeer = new TassePeer();
		$mcDbName = $taxPeer->getMastercomDbName();

		// Get students
		$studentsQ = new StudentiCompletiQuery($mcDbName);
		$students = $studentsQ
				->filterByFlagCanc(0) // only students not deleted
				->where("cognome||' '||nome ILIKE '%" . pg_escape_string($this->query) . "%'")
				->withColumn('id_studente', 'IdSubject')
				->withColumn('cognome', 'Cognome')
				->with<PERSON>olumn('nome', 'Nome')
				->withColumn('classe', 'Classe')
				->withColumn('sezione', 'Sezione')
				->select(array("Nome", "Cognome", 'Classe', 'Sezione'))->find()
				->toArray();

		foreach ($students as $student) {
			$student['AnnoScolastico'] = $strCon['start'] . "/" . $strCon['end'];
			$student['Tipo'] = 'S';
			$this->list[] = $student;
		}

		// Get employees
		$employees = $employeeQ = \Employee\EmployeeQuery::create()
						->where("surname||' '||name ILIKE '%" . pg_escape_string($this->query) . "%'")
						->withColumn('employee_id', 'IdSubject')
						->withColumn('surname', 'Cognome')
						->withColumn('name', 'Nome')
						->select(
								"Nome", "Cognome"
						)->find()->toArray();
		foreach ($employees as $employee) {
			$employee['Tipo'] = 'I';
			$this->list[] = $employee;
		}
		return $this->list;
	}

}
