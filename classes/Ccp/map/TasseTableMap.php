<?php

namespace Ccp\map;

use \RelationMap;
use \TableMap;

/**
 * This class defines the structure of the 'tasse' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Ccp.map
 */
class TasseTableMap extends TableMap {

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Ccp.map.TasseTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize() {
        // attributes
        $this->setName('tasse');
        $this->setPhpName('Tasse');
        $this->setClassname('Ccp\\Tasse');
        $this->setPackage('Ccp');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('tasse_id_seq');
        // columns
        $this->addPrimaryKey('id_tasse', 'IdTasse', 'INTEGER', true, null, null);
        $this->addForeignKey('id_tipo_tassa', 'IdTipoTassa', 'INTEGER', 'tipi_tasse', 'id_tipo_tassa', true, null, 0);
        $this->addForeignKey('id_studente', 'IdStudente', 'INTEGER', 'studenti_completi', 'id_studente', false, null, 0);
        $this->addForeignKey('id_studente', 'IdStudente', 'INTEGER', 'studenti', 'id_studente', false, null, 0);
        $this->addColumn('sede_ufficio_postale', 'SedeUfficioPostale', 'VARCHAR', false, null, '');
        $this->addColumn('numero_versamento', 'NumeroVersamento', 'VARCHAR', false, null, '');
        $this->addColumn('data_versamento', 'DataVersamento', 'BIGINT', false, null, 0);
        $this->addColumn('note', 'Note', 'LONGVARCHAR', false, null, '');
        $this->addColumn('anno_scolastico', 'AnnoScolastico', 'VARCHAR', false, null, '');
        $this->addColumn('riferimento_estratto_conto', 'RiferimentoEstrattoConto', 'VARCHAR', false, 255, '');
        $this->addColumn('data_estratto_conto', 'DataEstrattoConto', 'BIGINT', false, null, 0);
        $this->addColumn('dati_debitore', 'DatiDebitore', 'LONGVARCHAR', false, null, '');
        $this->addColumn('numero_bollettino', 'NumeroBollettino', 'LONGVARCHAR', false, null, '');
        $this->addColumn('is_incoming', 'IsIncoming', 'BOOLEAN', false, null, true);
        $this->addColumn('employee_id', 'EmployeeId', 'INTEGER', false, null, 0);
        $this->addColumn('tipologia_uscita', 'TipologiaUscita', 'VARCHAR', false, null, 'DIVERSI');
        $this->addColumn('destinazione_pagamento', 'DestinazionePagamento', 'VARCHAR', false, 10, 'ccp_0');
        $this->addColumn('modalita_pagamento', 'ModalitaPagamento', 'VARCHAR', false, 10, null);
        $this->addColumn('sede_studente', 'SedeStudente', 'INTEGER', false, null, null);
        $this->addColumn('classe', 'Classe', 'VARCHAR', false, 64, null);
        $this->addColumn('importo_versato', 'ImportoVersato', 'FLOAT', false, null, 0);
        // validators
        $this->addValidator('numero_versamento', 'required', 'propel.validator.RequiredValidator', '', _('Transaction number is mandatory'));
        $this->addValidator('importo_versato', 'minValue', 'propel.validator.MinValueValidator', '0', _('Amount must be positive'));
    }

// initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations() {
        $this->addRelation('TipiTasse', 'Ccp\\TipiTasse', RelationMap::MANY_TO_ONE, array('id_tipo_tassa' => 'id_tipo_tassa',), null, null);
        $this->addRelation('StudentiCompleti', 'Ccp\\StudentiCompleti', RelationMap::MANY_TO_ONE, array('id_studente' => 'id_studente',), null, null);
        $this->addRelation('Studenti', 'Ccp\\Studenti', RelationMap::MANY_TO_ONE, array('id_studente' => 'id_studente',), null, null);
    }

// buildRelations()
}

// TasseTableMap
