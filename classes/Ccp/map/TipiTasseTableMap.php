<?php

namespace Ccp\map;

use \RelationMap;
use \TableMap;

/**
 * This class defines the structure of the 'tipi_tasse' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Ccp.map
 */
class TipiTasseTableMap extends TableMap {

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Ccp.map.TipiTasseTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize() {
        // attributes
        $this->setName('tipi_tasse');
        $this->setPhpName('TipiTasse');
        $this->setClassname('Ccp\\TipiTasse');
        $this->setPackage('Ccp');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('tipi_tasse_id_seq');
        // columns
        $this->addPrimaryKey('id_tipo_tassa', 'IdTipoTassa', 'INTEGER', true, null, null);
        $this->addColumn('descrizione', 'Descrizione', 'VARCHAR', false, null, '');
        $this->addColumn('tipologia', 'Tipologia', 'VARCHAR', false, null, 'DIVERSI');
        $this->addColumn('tassa_governativa', 'TassaGovernativa', 'BOOLEAN', false, null, false);
        $this->addColumn('data_scadenza', 'DataScadenza', 'BIGINT', false, null, 0);
        $this->addColumn('cumulativa', 'Cumulativa', 'INTEGER', false, null, 0);
        $this->addColumn('anno_scolastico_riferimento', 'AnnoScolasticoRiferimento', 'VARCHAR', false, null, 'TUTTI');
        $this->addColumn('importo_base', 'ImportoBase', 'FLOAT', false, null, 0);
        // validators
        $this->addValidator('descrizione', 'required', 'propel.validator.RequiredValidator', '', _('Description is mandatory.'));
        $this->addValidator('importo_base', 'minValue', 'propel.validator.MinValueValidator', '0', _('Amount must be positive'));
    }

// initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations() {
        $this->addRelation('Tasse', 'Ccp\\Tasse', RelationMap::ONE_TO_MANY, array('id_tipo_tassa' => 'id_tipo_tassa',), null, null, 'Tasses');
    }

// buildRelations()
}

// TipiTasseTableMap
