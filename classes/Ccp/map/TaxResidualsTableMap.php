<?php

namespace Ccp\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'tax_residuals' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Ccp.map
 */
class TaxResidualsTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Ccp.map.TaxResidualsTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('tax_residuals');
        $this->setPhpName('TaxResiduals');
        $this->setClassname('Ccp\\TaxResiduals');
        $this->setPackage('Ccp');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('tax_residuals_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('tasse', 'Tasse', 'DOUBLE', false, null, 0);
        $this->addColumn('contributi', 'Contributi', 'DOUBLE', false, null, 0);
        $this->addColumn('quote', 'Quote', 'DOUBLE', false, null, 0);
        $this->addColumn('diversi', 'Diversi', 'DOUBLE', false, null, 0);
        $this->addColumn('debito', 'Debito', 'DOUBLE', false, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // TaxResidualsTableMap
