<?php

namespace Ccp\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'classi' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Ccp.map
 */
class ClassiTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Ccp.map.ClassiTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('classi');
        $this->setPhpName('Classi');
        $this->setClassname('Ccp\\Classi');
        $this->setPackage('Ccp');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('classi_id_classe_seq');
        // columns
        $this->addPrimaryKey('id_classe', 'IdClasse', 'INTEGER', true, null, null);
        $this->addColumn('classe', 'Classe', 'VARCHAR', false, 255, '');
        $this->addColumn('sezione', 'Sezione', 'VARCHAR', false, 255, '');
        $this->addColumn('id_indirizzo', 'IdIndirizzo', 'INTEGER', true, null, null);
        $this->addColumn('codice_registro', 'CodiceRegistro', 'VARCHAR', false, 255, '');
        $this->addColumn('ordinamento', 'Ordinamento', 'VARCHAR', false, null, '0');
        $this->addColumn('chi_inserisce', 'ChiInserisce', 'BIGINT', false, null, 0);
        $this->addColumn('data_inserimento', 'DataInserimento', 'BIGINT', false, null, 0);
        $this->addColumn('tipo_inserimento', 'TipoInserimento', 'VARCHAR', false, null, '');
        $this->addColumn('chi_modifica', 'ChiModifica', 'BIGINT', false, null, 0);
        $this->addColumn('data_modifica', 'DataModifica', 'BIGINT', false, null, 0);
        $this->addColumn('tipo_modifica', 'TipoModifica', 'VARCHAR', false, null, '');
        $this->addColumn('flag_canc', 'FlagCanc', 'BIGINT', false, null, 0);
        $this->addColumn('codice_registro_2', 'CodiceRegistro2', 'VARCHAR', false, null, '');
        $this->addColumn('codice_registro_3', 'CodiceRegistro3', 'VARCHAR', false, null, '');
        $this->addColumn('codice_registro_4', 'CodiceRegistro4', 'VARCHAR', false, null, '');
        $this->addColumn('blocco_scrutini', 'BloccoScrutini', 'VARCHAR', false, null, '');
        $this->addColumn('consiglio_classe_attivo', 'ConsiglioClasseAttivo', 'VARCHAR', false, null, 'NO');
        $this->addColumn('tempo_funzionamento', 'TempoFunzionamento', 'VARCHAR', false, null, '');
        $this->addColumn('pubb_primo_scritto', 'PubbPrimoScritto', 'VARCHAR', false, null, 'NO');
        $this->addColumn('pubb_secondo_scritto', 'PubbSecondoScritto', 'VARCHAR', false, null, 'NO');
        $this->addColumn('pubb_terzo_scritto', 'PubbTerzoScritto', 'VARCHAR', false, null, 'NO');
        $this->addColumn('pubb_orale', 'PubbOrale', 'VARCHAR', false, null, 'NO');
        $this->addColumn('id_flusso', 'IdFlusso', 'VARCHAR', false, null, '0');
        $this->addColumn('autenticazione_alternativa', 'AutenticazioneAlternativa', 'VARCHAR', false, null, null);
        $this->addColumn('effettua_controllo_gate', 'EffettuaControlloGate', 'VARCHAR', false, null, 'SI');
        $this->addColumn('data_aggiornamento_sidi', 'DataAggiornamentoSidi', 'INTEGER', false, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // ClassiTableMap
