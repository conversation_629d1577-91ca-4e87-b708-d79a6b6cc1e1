<?php

namespace Ccp\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'studenti' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Ccp.map
 */
class StudentiTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Ccp.map.StudentiTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('studenti');
        $this->setPhpName('Studenti');
        $this->setClassname('Ccp\\Studenti');
        $this->setPackage('Ccp');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('studenti_id_studente_seq');
        // columns
        $this->addPrimaryKey('id_studente', 'IdStudente', 'INTEGER', true, null, null);
        $this->addColumn('nome', 'Nome', 'VARCHAR', true, 255, 'STUDENTE IGNOTO');
        $this->addColumn('cognome', 'Cognome', 'VARCHAR', true, 255, 'STUDENTE IGNOTO');
        $this->addColumn('indirizzo', 'Indirizzo', 'VARCHAR', false, 255, '');
        $this->addColumn('citta', 'Citta', 'VARCHAR', false, 255, '');
        $this->addColumn('cap', 'Cap', 'VARCHAR', false, 5, '');
        $this->addColumn('provincia', 'Provincia', 'VARCHAR', false, 255, '');
        $this->addColumn('sesso', 'Sesso', 'VARCHAR', false, 1, '');
        $this->addColumn('telefono', 'Telefono', 'VARCHAR', false, 255, '');
        $this->addColumn('cellulare1', 'Cellulare1', 'VARCHAR', false, 255, '');
        $this->addColumn('cellulare2', 'Cellulare2', 'VARCHAR', false, 255, '');
        $this->addColumn('email1', 'Email1', 'VARCHAR', false, 255, '');
        $this->addColumn('email2', 'Email2', 'VARCHAR', false, 255, '');
        $this->addColumn('invio_email', 'InvioEmail', 'VARCHAR', false, 1, '0');
        $this->addColumn('invio_email_cumulativo', 'InvioEmailCumulativo', 'VARCHAR', false, 1, '0');
        $this->addColumn('invio_email_parametrico', 'InvioEmailParametrico', 'VARCHAR', false, 1, '0');
        $this->addColumn('invio_email_temporale', 'InvioEmailTemporale', 'VARCHAR', false, 1, '0');
        $this->addColumn('tipo_sms', 'TipoSms', 'VARCHAR', false, 1, '0');
        $this->addColumn('tipo_sms_cumulativo', 'TipoSmsCumulativo', 'VARCHAR', false, 1, '0');
        $this->addColumn('tipo_sms_parametrico', 'TipoSmsParametrico', 'VARCHAR', false, 1, '0');
        $this->addColumn('tipo_sms_temporale', 'TipoSmsTemporale', 'VARCHAR', false, 1, '0');
        $this->addColumn('aut_entrata_ritardo', 'AutEntrataRitardo', 'VARCHAR', false, 1, '0');
        $this->addColumn('aut_uscita_anticipo', 'AutUscitaAnticipo', 'VARCHAR', false, 1, '0');
        $this->addColumn('aut_pomeriggio', 'AutPomeriggio', 'VARCHAR', false, 1, '0');
        $this->addColumn('acconsente', 'Acconsente', 'VARCHAR', false, 1, '0');
        $this->addColumn('ritirato', 'Ritirato', 'VARCHAR', false, 1, '0');
        $this->addColumn('data_nascita', 'DataNascita', 'INTEGER', false, null, 0);
        $this->addColumn('codice_studente', 'CodiceStudente', 'VARCHAR', false, null, '');
        $this->addColumn('password_studente', 'PasswordStudente', 'VARCHAR', false, null, '');
        $this->addColumn('codice_giustificazioni_studente', 'CodiceGiustificazioniStudente', 'BIGINT', false, null, 0);
        $this->addColumn('esonero_religione', 'EsoneroReligione', 'VARCHAR', false, 1, '0');
        $this->addColumn('materia_sostitutiva_religione', 'MateriaSostitutivaReligione', 'INTEGER', false, null, 0);
        $this->addColumn('esonero_ed_fisica', 'EsoneroEdFisica', 'VARCHAR', false, 1, '0');
        $this->addColumn('materia_sostitutiva_edfisica', 'MateriaSostitutivaEdfisica', 'INTEGER', false, null, 0);
        $this->addColumn('crediti_terza', 'CreditiTerza', 'INTEGER', false, null, 0);
        $this->addColumn('media_voti_terza', 'MediaVotiTerza', 'VARCHAR', false, 255, '0');
        $this->addColumn('debiti_terza', 'DebitiTerza', 'VARCHAR', false, 255, '0');
        $this->addColumn('crediti_sospesi_terza', 'CreditiSospesiTerza', 'INTEGER', false, null, 0);
        $this->addColumn('crediti_reintegrati_terza', 'CreditiReintegratiTerza', 'INTEGER', false, null, 0);
        $this->addColumn('crediti_quarta', 'CreditiQuarta', 'INTEGER', false, null, 0);
        $this->addColumn('media_voti_quarta', 'MediaVotiQuarta', 'VARCHAR', false, 255, '0');
        $this->addColumn('debiti_quarta', 'DebitiQuarta', 'VARCHAR', false, 255, '0');
        $this->addColumn('crediti_sospesi_quarta', 'CreditiSospesiQuarta', 'INTEGER', false, null, 0);
        $this->addColumn('crediti_reintegrati_quarta', 'CreditiReintegratiQuarta', 'INTEGER', false, null, 0);
        $this->addColumn('crediti_quinta', 'CreditiQuinta', 'INTEGER', false, null, 0);
        $this->addColumn('media_voti_quinta', 'MediaVotiQuinta', 'VARCHAR', false, 255, '0');
        $this->addColumn('crediti_finali_agg', 'CreditiFinaliAgg', 'INTEGER', false, null, 0);
        $this->addColumn('matricola', 'Matricola', 'VARCHAR', false, 255, '0');
        $this->addColumn('luogo_nascita', 'LuogoNascita', 'VARCHAR', false, 255, '');
        $this->addColumn('provincia_nascita', 'ProvinciaNascita', 'VARCHAR', false, 50, '');
        $this->addColumn('motivi_crediti_terza', 'MotiviCreditiTerza', 'LONGVARCHAR', false, null, '');
        $this->addColumn('motivi_crediti_quarta', 'MotiviCreditiQuarta', 'LONGVARCHAR', false, null, '');
        $this->addColumn('motivi_crediti_quinta', 'MotiviCreditiQuinta', 'LONGVARCHAR', false, null, '');
        $this->addColumn('motivi_crediti_agg', 'MotiviCreditiAgg', 'LONGVARCHAR', false, null, '');
        $this->addColumn('codice_comune_nascita', 'CodiceComuneNascita', 'VARCHAR', false, null, '');
        $this->addColumn('stato_nascita', 'StatoNascita', 'VARCHAR', false, null, '');
        $this->addColumn('cittadinanza', 'Cittadinanza', 'VARCHAR', false, null, '-1');
        $this->addColumn('seconda_cittadinanza', 'SecondaCittadinanza', 'VARCHAR', false, null, '-1');
        $this->addColumn('codice_comune_residenza', 'CodiceComuneResidenza', 'VARCHAR', false, null, '');
        $this->addColumn('distretto', 'Distretto', 'VARCHAR', false, null, '');
        $this->addColumn('codice_fiscale', 'CodiceFiscale', 'VARCHAR', false, null, '');
        $this->addColumn('medico', 'Medico', 'VARCHAR', false, null, '');
        $this->addColumn('telefono_medico', 'TelefonoMedico', 'VARCHAR', false, null, '');
        $this->addColumn('intolleranze_alim', 'IntolleranzeAlim', 'VARCHAR', false, null, '');
        $this->addColumn('gruppo_sanguigno', 'GruppoSanguigno', 'VARCHAR', false, null, '');
        $this->addColumn('gruppo_rh', 'GruppoRh', 'VARCHAR', false, null, '');
        $this->addColumn('codice_asl', 'CodiceAsl', 'VARCHAR', false, null, '');
        $this->addColumn('annotazioni', 'Annotazioni', 'VARCHAR', false, null, '');
        $this->addColumn('stato_civile', 'StatoCivile', 'INTEGER', false, null, 0);
        $this->addColumn('voto_primo_scritto', 'VotoPrimoScritto', 'INTEGER', false, null, 0);
        $this->addColumn('voto_secondo_scritto', 'VotoSecondoScritto', 'INTEGER', false, null, 0);
        $this->addColumn('voto_terzo_scritto', 'VotoTerzoScritto', 'INTEGER', false, null, 0);
        $this->addColumn('voto_orale', 'VotoOrale', 'INTEGER', false, null, 0);
        $this->addColumn('voto_bonus', 'VotoBonus', 'INTEGER', false, null, 0);
        $this->addColumn('materia_secondo_scr', 'MateriaSecondoScr', 'VARCHAR', false, null, '');
        $this->addColumn('ulteriori_specif_diploma', 'UlterioriSpecifDiploma', 'LONGVARCHAR', false, null, '');
        $this->addColumn('numero_diploma', 'NumeroDiploma', 'INTEGER', false, null, 0);
        $this->addColumn('chi_inserisce', 'ChiInserisce', 'BIGINT', false, null, 0);
        $this->addColumn('data_inserimento', 'DataInserimento', 'BIGINT', false, null, 0);
        $this->addColumn('tipo_inserimento', 'TipoInserimento', 'VARCHAR', false, null, '');
        $this->addColumn('chi_modifica', 'ChiModifica', 'BIGINT', false, null, 0);
        $this->addColumn('data_modifica', 'DataModifica', 'BIGINT', false, null, 0);
        $this->addColumn('tipo_modifica', 'TipoModifica', 'VARCHAR', false, null, '');
        $this->addColumn('flag_canc', 'FlagCanc', 'BIGINT', false, null, 0);
        $this->addColumn('stato_avanzamento', 'StatoAvanzamento', 'VARCHAR', false, null, '');
        $this->addColumn('data_stato_avanzamento', 'DataStatoAvanzamento', 'BIGINT', false, null, 0);
        $this->addColumn('cap_provincia_nascita', 'CapProvinciaNascita', 'VARCHAR', false, null, '');
        $this->addColumn('badge', 'Badge', 'BIGINT', false, null, 0);
        $this->addColumn('cap_residenza', 'CapResidenza', 'VARCHAR', false, null, '');
        $this->addColumn('codice_comune_domicilio', 'CodiceComuneDomicilio', 'VARCHAR', false, null, '');
        $this->addColumn('cap_domicilio', 'CapDomicilio', 'VARCHAR', false, null, '');
        $this->addColumn('cap_nascita', 'CapNascita', 'VARCHAR', false, null, '');
        $this->addColumn('indirizzo_domicilio', 'IndirizzoDomicilio', 'VARCHAR', false, null, '');
        $this->addColumn('citta_nascita_straniera', 'CittaNascitaStraniera', 'VARCHAR', false, null, '');
        $this->addColumn('cellulare_allievo', 'CellulareAllievo', 'VARCHAR', false, null, '');
        $this->addColumn('handicap', 'Handicap', 'VARCHAR', false, null, 'NO');
        $this->addColumn('stato_convittore', 'StatoConvittore', 'VARCHAR', false, null, 'NO');
        $this->addColumn('data_ritiro', 'DataRitiro', 'BIGINT', false, null, 0);
        $this->addColumn('voto_ammissione', 'VotoAmmissione', 'VARCHAR', false, null, '0');
        $this->addColumn('differenza_punteggio', 'DifferenzaPunteggio', 'VARCHAR', false, null, '0');
        $this->addColumn('voto_qualifica', 'VotoQualifica', 'VARCHAR', false, null, '0');
        $this->addColumn('voto_esame_sc1_qual', 'VotoEsameSc1Qual', 'VARCHAR', false, null, '0');
        $this->addColumn('voto_esame_sc2_qual', 'VotoEsameSc2Qual', 'VARCHAR', false, null, '0');
        $this->addColumn('voto_esame_or_qual', 'VotoEsameOrQual', 'VARCHAR', false, null, '0');
        $this->addColumn('stato_privatista', 'StatoPrivatista', 'VARCHAR', false, null, 'NO');
        $this->addColumn('foto', 'Foto', 'VARCHAR', false, null, '');
        $this->addColumn('rappresentante', 'Rappresentante', 'VARCHAR', false, null, 'NO#NO#NO');
        $this->addColumn('obbligo_formativo', 'ObbligoFormativo', 'VARCHAR', false, null, '01');
        $this->addColumn('id_lingua_1', 'IdLingua1', 'BIGINT', false, null, 0);
        $this->addColumn('id_lingua_2', 'IdLingua2', 'BIGINT', false, null, 0);
        $this->addColumn('id_lingua_3', 'IdLingua3', 'BIGINT', false, null, 0);
        $this->addColumn('id_lingua_4', 'IdLingua4', 'BIGINT', false, null, 0);
        $this->addColumn('id_lingua_5', 'IdLingua5', 'BIGINT', false, null, 0);
        $this->addColumn('id_provenienza_scolastica', 'IdProvenienzaScolastica', 'VARCHAR', false, null, '');
        $this->addColumn('id_scuola_media', 'IdScuolaMedia', 'VARCHAR', false, null, '');
        $this->addColumn('lingua_scuola_media', 'LinguaScuolaMedia', 'VARCHAR', false, null, '');
        $this->addColumn('lingua_scuola_media_2', 'LinguaScuolaMedia2', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_scuola_media', 'GiudizioScuolaMedia', 'VARCHAR', false, null, '');
        $this->addColumn('trasporto', 'Trasporto', 'VARCHAR', false, null, '');
        $this->addColumn('data_iscrizione', 'DataIscrizione', 'BIGINT', false, null, 0);
        $this->addColumn('pei', 'Pei', 'VARCHAR', false, null, 'NO');
        $this->addColumn('ammesso_esame_qualifica', 'AmmessoEsameQualifica', 'VARCHAR', false, null, '--');
        $this->addColumn('ammesso_esame_quinta', 'AmmessoEsameQuinta', 'VARCHAR', false, null, 'NO');
        $this->addColumn('giudizio_ammissione_quinta', 'GiudizioAmmissioneQuinta', 'LONGVARCHAR', false, null, '');
        $this->addColumn('grado_handicap', 'GradoHandicap', 'VARCHAR', false, null, '0');
        $this->addColumn('tipo_handicap', 'TipoHandicap', 'VARCHAR', false, null, '');
        $this->addColumn('stato_licenza_maestro', 'StatoLicenzaMaestro', 'VARCHAR', false, null, 'NO');
        $this->addColumn('id_studente_sissi', 'IdStudenteSissi', 'VARCHAR', false, null, '');
        $this->addColumn('badge_rfid', 'BadgeRfid', 'VARCHAR', false, null, '');
        $this->addColumn('lode', 'Lode', 'VARCHAR', false, null, 'NO');
        $this->addColumn('distretto_scolastico', 'DistrettoScolastico', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_ammissione_terza', 'GiudizioAmmissioneTerza', 'LONGVARCHAR', false, null, '');
        $this->addColumn('esito_prima_media', 'EsitoPrimaMedia', 'VARCHAR', false, null, 'NO');
        $this->addColumn('esito_seconda_media', 'EsitoSecondaMedia', 'VARCHAR', false, null, 'NO');
        $this->addColumn('esito_terza_media', 'EsitoTerzaMedia', 'VARCHAR', false, null, 'NO');
        $this->addColumn('giudizio_esame_sc1_qual', 'GiudizioEsameSc1Qual', 'LONGVARCHAR', false, null, '');
        $this->addColumn('giudizio_esame_sc2_qual', 'GiudizioEsameSc2Qual', 'LONGVARCHAR', false, null, '');
        $this->addColumn('giudizio_esame_or_qual', 'GiudizioEsameOrQual', 'LONGVARCHAR', false, null, '');
        $this->addColumn('giudizio_complessivo_esame_qual', 'GiudizioComplessivoEsameQual', 'LONGVARCHAR', false, null, '');
        $this->addColumn('acconsente_aziende', 'AcconsenteAziende', 'INTEGER', false, null, 1);
        $this->addColumn('curriculum_prima', 'CurriculumPrima', 'VARCHAR', false, null, '0');
        $this->addColumn('curriculum_seconda', 'CurriculumSeconda', 'VARCHAR', false, null, '0');
        $this->addColumn('stage_professionali', 'StageProfessionali', 'VARCHAR', false, null, '0');
        $this->addColumn('data_orale', 'DataOrale', 'BIGINT', false, null, 0);
        $this->addColumn('ordine_esame_orale', 'OrdineEsameOrale', 'BIGINT', false, null, 0);
        $this->addColumn('tipo_primo_scritto', 'TipoPrimoScritto', 'VARCHAR', false, null, '');
        $this->addColumn('tipo_secondo_scritto', 'TipoSecondoScritto', 'VARCHAR', false, null, '');
        $this->addColumn('tipo_terzo_scritto', 'TipoTerzoScritto', 'VARCHAR', false, null, '');
        $this->addColumn('unanimita_primo_scritto', 'UnanimitaPrimoScritto', 'VARCHAR', false, null, '');
        $this->addColumn('unanimita_secondo_scritto', 'UnanimitaSecondoScritto', 'VARCHAR', false, null, '');
        $this->addColumn('unanimita_terzo_scritto', 'UnanimitaTerzoScritto', 'VARCHAR', false, null, '');
        $this->addColumn('argomento_scelto_orale', 'ArgomentoSceltoOrale', 'VARCHAR', false, null, '');
        $this->addColumn('area_disc_1_orale', 'AreaDisc1Orale', 'VARCHAR', false, null, '');
        $this->addColumn('area_disc_2_orale', 'AreaDisc2Orale', 'VARCHAR', false, null, '');
        $this->addColumn('disc_elaborati_orale', 'DiscElaboratiOrale', 'VARCHAR', false, null, '');
        $this->addColumn('unanimita_voto_finale', 'UnanimitaVotoFinale', 'VARCHAR', false, null, '');
        $this->addColumn('presente_esame_quinta', 'PresenteEsameQuinta', 'VARCHAR', false, null, 'SI');
        $this->addColumn('stampa_badge', 'StampaBadge', 'VARCHAR', false, null, 'NO');
        $this->addColumn('id_classe_destinazione', 'IdClasseDestinazione', 'INTEGER', false, null, 0);
        $this->addColumn('sconto_rette', 'ScontoRette', 'INTEGER', false, null, 0);
        $this->addColumn('carta_studente_numero', 'CartaStudenteNumero', 'BIGINT', false, null, 0);
        $this->addColumn('carta_studente_scadenza', 'CartaStudenteScadenza', 'INTEGER', false, null, 0);
        $this->addColumn('esito_corrente_calcolato', 'EsitoCorrenteCalcolato', 'VARCHAR', true, null, '');
        $this->addColumn('id_flusso', 'IdFlusso', 'VARCHAR', false, null, '0');
        $this->addColumn('data_aggiornamento_sogei', 'DataAggiornamentoSogei', 'VARCHAR', true, null, '');
        $this->addColumn('codice_alunno_ministeriale', 'CodiceAlunnoMinisteriale', 'VARCHAR', true, null, '');
        $this->addColumn('flag_cf_fittizio', 'FlagCfFittizio', 'INTEGER', true, null, 0);
        $this->addColumn('flag_s2f', 'FlagS2f', 'VARCHAR', true, null, '');
        $this->addColumn('codice_stato_sogei', 'CodiceStatoSogei', 'VARCHAR', true, null, '');
        $this->addColumn('codice_gruppo_nomade', 'CodiceGruppoNomade', 'VARCHAR', true, null, '');
        $this->addColumn('flag_minore_straniero', 'FlagMinoreStraniero', 'INTEGER', true, null, 0);
        $this->addColumn('chiave', 'Chiave', 'VARCHAR', false, null, null);
        $this->addColumn('voto_esame_medie_italiano', 'VotoEsameMedieItaliano', 'VARCHAR', false, null, '');
        $this->addColumn('voto_esame_medie_inglese', 'VotoEsameMedieInglese', 'VARCHAR', false, null, '');
        $this->addColumn('voto_esame_medie_matematica', 'VotoEsameMedieMatematica', 'VARCHAR', false, null, '');
        $this->addColumn('voto_esame_medie_seconda_lingua', 'VotoEsameMedieSecondaLingua', 'VARCHAR', false, null, '');
        $this->addColumn('voto_esame_medie_invalsi_ita', 'VotoEsameMedieInvalsiIta', 'VARCHAR', false, null, '');
        $this->addColumn('voto_esame_medie_invalsi_mat', 'VotoEsameMedieInvalsiMat', 'VARCHAR', false, null, '');
        $this->addColumn('voto_esame_medie_orale', 'VotoEsameMedieOrale', 'VARCHAR', false, null, '');
        $this->addColumn('voto_ammissione_medie', 'VotoAmmissioneMedie', 'VARCHAR', false, null, '');
        $this->addColumn('esito_prima_elementare', 'EsitoPrimaElementare', 'VARCHAR', false, null, '');
        $this->addColumn('esito_seconda_elementare', 'EsitoSecondaElementare', 'VARCHAR', false, null, '');
        $this->addColumn('esito_terza_elementare', 'EsitoTerzaElementare', 'VARCHAR', false, null, '');
        $this->addColumn('esito_quarta_elementare', 'EsitoQuartaElementare', 'VARCHAR', false, null, '');
        $this->addColumn('esito_quinta_elementare', 'EsitoQuintaElementare', 'VARCHAR', false, null, '');
        $this->addColumn('tipo_voto_esame_medie_italiano', 'TipoVotoEsameMedieItaliano', 'VARCHAR', false, null, '');
        $this->addColumn('tipo_voto_esame_medie_inglese', 'TipoVotoEsameMedieInglese', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_1_medie', 'Giudizio1Medie', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_2_medie', 'Giudizio2Medie', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_3_medie', 'Giudizio3Medie', 'VARCHAR', false, null, '');
        $this->addColumn('argomenti_orali_medie', 'ArgomentiOraliMedie', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_finale_1_medie', 'GiudizioFinale1Medie', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_finale_2_medie', 'GiudizioFinale2Medie', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_finale_3_medie', 'GiudizioFinale3Medie', 'VARCHAR', false, null, '');
        $this->addColumn('consiglio_terza_media', 'ConsiglioTerzaMedia', 'VARCHAR', false, null, '');
        $this->addColumn('giudizio_sintetico_esame_terza_media', 'GiudizioSinteticoEsameTerzaMedia', 'VARCHAR', false, null, '');
        $this->addColumn('data_arrivo_in_italia', 'DataArrivoInItalia', 'INTEGER', false, null, 0);
        $this->addColumn('frequenza_asilo_nido', 'FrequenzaAsiloNido', 'INTEGER', false, null, 0);
        $this->addColumn('frequenza_scuola_materna', 'FrequenzaScuolaMaterna', 'INTEGER', false, null, 0);
        $this->addColumn('data_aggiornamento_sidi', 'DataAggiornamentoSidi', 'INTEGER', false, null, 0);
        $this->addColumn('cmp_sup_val_ita', 'CmpSupValIta', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_txt_ita', 'CmpSupTxtIta', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_val_ing', 'CmpSupValIng', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_txt_ing', 'CmpSupTxtIng', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_val_altri', 'CmpSupValAltri', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_txt_altri', 'CmpSupTxtAltri', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_val_mat', 'CmpSupValMat', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_txt_mat', 'CmpSupTxtMat', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_val_sci_tec', 'CmpSupValSciTec', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_txt_sci_tec', 'CmpSupTxtSciTec', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_val_sto_soc', 'CmpSupValStoSoc', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_sup_txt_sto_soc', 'CmpSupTxtStoSoc', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_ita', 'CmpMedValIta', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_ita', 'CmpMedTxtIta', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_ing', 'CmpMedValIng', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_ing', 'CmpMedTxtIng', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_altri', 'CmpMedValAltri', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_altri', 'CmpMedTxtAltri', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_mat', 'CmpMedValMat', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_mat', 'CmpMedTxtMat', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_sci_tec', 'CmpMedValSciTec', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_sci_tec', 'CmpMedTxtSciTec', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_sto_soc', 'CmpMedValStoSoc', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_sto_soc', 'CmpMedTxtStoSoc', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_l2', 'CmpMedValL2', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_l2', 'CmpMedTxtL2', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_l3', 'CmpMedValL3', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_l3', 'CmpMedTxtL3', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_arte', 'CmpMedValArte', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_arte', 'CmpMedTxtArte', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_mus', 'CmpMedValMus', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_mus', 'CmpMedTxtMus', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_val_mot', 'CmpMedValMot', 'VARCHAR', false, null, '');
        $this->addColumn('cmp_med_txt_mot', 'CmpMedTxtMot', 'VARCHAR', false, null, '');
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('Tasse', 'Ccp\\Tasse', RelationMap::ONE_TO_MANY, array('id_studente' => 'id_studente', ), null, null, 'Tasses');
    } // buildRelations()

} // StudentiTableMap
