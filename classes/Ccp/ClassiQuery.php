<?php

namespace Ccp;

use Ccp\om\BaseClassiQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'classi' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Ccp
 */
class ClassiQuery extends BaseClassiQuery {

    public function searchClassi() {
        $filter = array(
            'all'  => true,
            'seat' => gethostname()
        );

        $api     = new \Core\ExternalApi;
        $classes = $api->get("/school/" . SCHOOL_ID . "/class", $filter);

        if ($classes === false) {
            return false;
        } else {
            return $classes['results'];
        }
    }

}
