<?php

namespace Ccp;

/**
 * Print Standard List base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFSelection {

    const spool = true;

    private $_path;
    private $_print_id;
    public $taxes;

    public function __construct($request) {
        $filter = $request;
        $this->dateStart = strtotime($filter['data_estratto_conto_start']);
        $this->dateEnd = strtotime($filter['data_estratto_conto_end']);
        $years = explode('/', $filter['SchoolYear']);
        $this->year = $years[0];

        if (!$this->dateStart) {
            $this->dateStart = strtotime($this->year . "-09-01");
        } else {
            $this->year = date('Y', $this->dateStart);
        }
        if (!$this->dateEnd) {
            $this->dateEnd = strtotime(($this->year + 1) . "-08-31");
        }

        $tasseQ = new \Ccp\TasseQuery();
        $filter['order']['data_estratto_conto'] = 'ASC';
        $filter['order']['numero_versamento'] = 'ASC';
        $this->taxes = $tasseQ->read($filter);
        $this->_print_id = $request['id'];
        $this->_path = $request['path'];
    }

    public function printHeader($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->Cell(100, 5, 'Riepilogo movimenti dal ' . date('d/m/Y', $this->dateStart) . ' al ' . date('d/m/Y', $this->dateEnd), 0, 1, 'L', false, '', 1);
        $p->Ln();
        $p->SetFontSize(8);
        $p->setFont('', 'B');
        $p->SetFillColor(210, 210, 210);
        $p->Cell(10, 5, 'N. Tr.', 1, 0, 'C', true);
        $p->Cell(10, 5, 'N. Bol.', 1, 0, 'C', true);
        $p->Cell(17, 5, 'Data E.C.', 1, 0, 'C', true);
        $p->Cell(25, 5, 'Tipo Transazione', 1, 0, 'C', true, null, 1);
        $p->Cell(45, 5, 'Beneficiario', 1, 0, 'C', true);
        //$p->Cell(25, 5, 'Voce', 1, 0, 'C', true);
        $p->Cell(50, 5, 'Causale', 1, 0, 'C', true);
        $p->Cell(27, 5, 'Ufficio postale', 1, 0, 'C', true);
        $p->Cell(20, 5, 'Anno scolastico', 1, 0, 'C', true, null, 1);
        $p->Cell(42, 5, 'Note', 1, 0, 'C', true);
        $p->Cell(20, 5, 'Accrediti (€)', 1, 0, 'C', true);
        $p->Cell(20, 5, 'Addebiti (€)', 1, 1, 'C', true);
        $p->setFont('', '');
    }

    public function printPageTotals($p, $data = array()) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->SetFillColor(235, 235, 235);
        $p->SetY($p->getY() + 5);
        $p->SetX(209);
        $p->Cell(42, 5, 'Totale Pagina', 1, 0, 'R', true);
        $p->Cell(20, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $data['pag_credito']), 1, 0, 'R', true);
        $p->Cell(20, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $data['pag_debito']), 1, 1, 'R', true);
    }

    public function printTotals($p, $data = array()) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->SetFillColor(210, 210, 210);
        $p->SetX(209);
        $p->Cell(42, 5, 'TOTALE', 1, 0, 'R', true);
        $p->Cell(20, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $data['credito']), 1, 0, 'R', true);
        $p->Cell(20, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $data['debito']), 1, 1, 'R', true);
    }

    public function printFinalData($p, $data = array()) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->SetY($p->getY() + 10);
        $p->SetX(70);
        $p->Cell(30, 7, 'Saldo (€)', 1, 0, 'L');
        $p->Cell(40, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, ($data['credito'] - $data['debito'])), 1, 1, 'R');
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->setPageOrientation('L');
        $p->SetMargins(5, 30);
        $p->addPage();

        // PDF prepare header
        $this->printHeader($p);

        $totals = array(
            'credito' => 0,
            'debito'  => 0
        );

        $oddStyle = false;
        foreach ($this->taxes as $k => $tax) {

            if ($k % 28 === 0 && $k > 0) {
                $this->printPageTotals($p, $totals);
                $this->printTotals($p, $totals);
                $totals['pag_credito'] = 0;
                $totals['pag_debito'] = 0;
                $p->addPage('L');
                $this->printHeader($p);
                $oddStyle = false;
            }

            // Change style row by row
            if ($oddStyle) {
                $p->SetFillColor(235, 235, 235);
                $oddStyle = false;
            } else {
                $p->SetFillColor(250, 250, 250);
                $oddStyle = true;
            }

            $subject = $tax['dati_debitore'] . ' (A)'; // Default debitor
            $class = ''; // Default class
            if ($tax['employee_id'] > 0) {
                $employee = \Employee\EmployeeQuery::create()->findPk($tax['employee_id']);
                $subject = $employee->getSurname() . ' ' . $employee->getName() . ' (I)';
            }
            if ($tax['id_studente'] > 0) {
                $subject = $tax['dati_debitore'] . ' (S)';
                $class = $tax['classe'];
            }

            $p->Cell(10, 5, $tax['numero_versamento'], 1, 0, 'C', true);
            $p->Cell(10, 5, $tax['numero_bollettino'], 1, 0, 'C', true);
            $p->Cell(17, 5, date('d/m/Y', strtotime($tax['data_estratto_conto'])), 1, 0, 'C', true);
            $p->Cell(25, 5, $tax['is_incoming'] === true ? 'ENTRATA' : 'USCITA', 1, 0, 'C', true);
            $p->Cell(45, 5, $subject, 1, 0, 'C', true, null, 1);
            //$p->Cell(25, 5, '5.1.1', 1, 0, 'C', true, null, 1);
            $p->Cell(50, 5, $tax['descrizione'], 1, 0, 'L', true, null, 1);
            $p->Cell(27, 5, $tax['sede_ufficio_postale'], 1, 0, 'L', true);
            $p->Cell(20, 5, $tax['anno_scolastico'], 1, 0, 'C', true);
            $p->Cell(42, 5, $tax['note'], 1, 0, 'L', true, null, 1);
            $p->Cell(20, 5, $tax['is_incoming'] === true ? money_format(MONEY_NO_SIGN_FORMAT_PRINT, $tax['importo_versato']) : '', 1, 0, 'R', true, null, 1);
            $p->Cell(20, 5, $tax['is_incoming'] === false ? money_format(MONEY_NO_SIGN_FORMAT_PRINT, $tax['importo_versato']) : '', 1, 1, 'R', true, null, 1);

            $totals['pag_credito'] += $tax['is_incoming'] === true ? $tax['importo_versato'] : 0;
            $totals['pag_debito'] += $tax['is_incoming'] === false ? $tax['importo_versato'] : 0;

            $totals['credito'] += $tax['is_incoming'] === true ? $tax['importo_versato'] : 0;
            $totals['debito'] += $tax['is_incoming'] === false ? $tax['importo_versato'] : 0;
        }

        $this->printPageTotals($p, $totals);
        $this->printTotals($p, $totals);
        $this->printFinalData($p, $totals);

        $create = $p->createPdf();
        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        $extra = array();
        //$extra['vers'] = $data['numero_versamento'];
        //$extra['boll'] = $data['numero_bollettino'];
        //$extra['taxType'] = $data['id_tipo_tassa'];
        //$extra['payee'] = count($data['employee_id']);
        //$extra['clss'] = count($data['classes_id']);
        //$extra['io'] = $data['is_incoming'] ? "Entrate": "Uscite";
        //$extra['payWay'] = $data['modalita_pagamento'];
        //$extra['paydest'] = $data['destinazione_pagamento'];
        $extra = implode(" - ", $extra);
        if ($extra) {
            $extra = " - {$extra}";
        }
        $dStart = explode("-", $data['data_estratto_conto_start']);
        $dStart = "{$dStart[2]}-{$dStart[1]}-{$dStart[0]}";
        $dEnd = explode("-", $data['data_estratto_conto_end']);
        $dEnd = "{$dEnd[2]}-{$dEnd[1]}-{$dEnd[0]}";
        return "Riepilogo Movimenti - {$dStart} / {$dEnd}{$extra}";
    }

}
