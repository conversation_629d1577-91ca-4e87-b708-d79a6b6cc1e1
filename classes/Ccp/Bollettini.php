<?php

namespace Ccp;

/**
 * Read Bollettini data eventually filtered, from all kind of uses,
 * print, listing etc.
 *
 * @package    mc2api.Cpp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class <PERSON><PERSON><PERSON> {

    public $filter; // Filter fields
    public $dbName; // Mastercom db name
    public $count; // Total element filtered

    public function __construct($filter) {
        $this->filter = $filter;
        $tp = new TassePeer();
        if (!isset($filter['SchoolYear']) || !$filter['SchoolYear']) {
            $filter['SchoolYear'] = null;
        }
        $this->dbName = $tp->getMastercomDbName($filter['SchoolYear']);
    }

    public function read() {
        $filter = array(
            'id_studente' => implode(',', $this->filter['students']),
            'all'         => true,
            'seat'        => gethostname(),
            'school_year' => $this->filter['SchoolYear']
        );

        $api = new \Core\ExternalApi;
        $data = $api->get("/school/" . SCHOOL_ID . "/student", $filter);
        return $data['results'];
    }

}
