<?php

namespace Ccp;

/**
 * Print Standard List base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFCertificate {

    const spool = true;

    private $_print_id;
    private $_path;
    public $tax;

    public function __construct($request) {
        $this->tax = $request;

        $d = explode("-", $this->tax['data_versamento']);
        $d = "{$d[2]}/{$d[1]}/{$d[0]}";
        $this->tax['data_versamento'] = $d;

        if ($this->tax['classe']) {
            $this->tax['classe'] = "({$this->tax['classe']})";
        }

        $db = \Db::getInstance();
        $r = $db->query("SELECT i.name AS school, description AS city, surname, e.name FROM institute AS i LEFT JOIN employee AS e ON job_director_id = employee_id LEFT JOIN contact AS co ON i.contact_id = co.contact_id LEFT JOIN cities AS ci ON co.city_id = ci.city_id WHERE def = 't'");
        if ($r) {
            $r = $db->fetchAll();
            if ($r) {
                $this->tax['school'] = $r[0]['school'];
                $this->tax['city'] = $r[0]['city'];
                $this->tax['d_surname'] = $r[0]['surname'];
                $this->tax['d_name'] = $r[0]['name'];
            }
        }

        $this->_print_id = $request['id'];
        $this->_path = $request['path'];
    }

    public function printHeader($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->Cell(20, 5, "Oggetto: ", 0, 0, 'L', false, '', 1);
        $p->setFont('', '');
        $p->Cell(0, 5, "Dichiarazione attestazione ricevuta di versamento.", 0, 1, 'L', false, '', 1);
        $p->setFont('', '');
    }

    public function printTax($p) {
        $money = money_format(MONEY_NO_SIGN_FORMAT_PRINT, $this->tax['importo_versato']);
        $p->setFont('', 'B');
        $p->SetFontSize(14);
        $p->Cell(0, 5, "IL DIRIGENTE SCOLASTICO", 0, 1, 'C', false, '', 1);
        $p->setFont('', '');
        $p->SetFontSize(10);
        $p->Cell(0, 5, "VISTO l'art. 153 del R.D. 21 Dicembre 1923, n. 2523;", 0, 1, 'L', false, '', 1);
        $p->Cell(0, 5, "VISTO l'art. 53 del R.D.L. 15 Maggio 1924, n. 749", 0, 1, 'L', false, '', 1);
        $p->Cell(0, 5, "VISTA la circolare del Ministero della Pubblica Istruzione n. 213 del 28 Maggio 1960;", 0, 1, 'L', false, '', 1);
        $p->Cell(0, 5, "VISTO il D.P.R. 22 Dicembre 1986 n. 917;", 0, 1, 'L', false, '', 1);
        $p->MultiCell(0, 5, "VISTO il D.I. 1 Febbraio 2001 n. 44, a norma del quale risulta legittima la ricerca da parte delle Istituzioni scolastiche di risorse aggiuntive provenienti da contribuzioni volontarie;", 0, 'L', false, 1, null, null);
        $p->Ln(5);
        $p->MultiCell(0, 5, "CONSIDERATO che nel rispetto dei canoni di trasparenza e di partecipazione alle procedure di adozione del Piano dell'Offerta Formativa è da ritenere consentita, ai sensi della vigente normativa, la richiesta alle famiglie di risorse aggiuntive, a titolo di contribuzione volontaria (fatta eccezione per le somme dovute a titolo di rimborso delle spese sostenutedalla scuola per conto delle stesse) per la realizzazione di particolari iniziative ed attività volte all'arricchimento dell'offerta formativa oltre i livelli essenziali del sistema educativo di istruzione e formazione garantiti dallo Stato;", 0, 'J', false, 1, null, null);
        $p->Ln(5);
        $p->MultiCell(0, 5, "CONSIDERATO che {$this->tax['dati_debitore']} {$this->tax['classe']} ha versato a favore di questa Istituzione scolastica la somma di Euro {$money} in data {$this->tax['data_versamento']} a titolo di contributo volontario;", 0, 'L', false, 1, null, null);
        $p->setFont('', 'B');
        $p->SetFontSize(14);
        $p->Cell(0, 5, "ATTESTA", 0, 1, 'C', false, '', 1);
        $p->setFont('', '');
        $p->SetFontSize(10);
        $p->MultiCell(0, 5, "che in data {$this->tax['data_versamento']} {$this->tax['dati_debitore']} {$this->tax['classe']} ha versato a favore di questa istituzione scolastica, per le finalità sopraindicate, la somma di Euro {$money} tramite versamento su conto corrente postale/bancario instestato a {$this->tax['school']}.", 0, 'L', false, 1, null, null);
        $p->setFont('', '');
    }

    public function printFooter($p) {
        $date = date("d/m/Y");
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->SetY($p->getY() + 20);
        $p->setX(20);
        $p->Cell(30, 7, "{$this->tax['city']}, {$date}", 0, 0, 'C');
        $p->SetY($p->getY() + 10);
        $p->setX(150);
        $p->SetFontSize(10);
        $p->Cell(50, 7, "Il Dirigente Scolastico", 0, 0, 'C');
        $p->SetY($p->getY() + 10);
        $p->setX(150);
        $p->SetFontSize(10);
        $p->Cell(50, 7, "{$this->tax['d_name']} {$this->tax['d_surname']}", 0, 0, 'C');
        $p->setFont('', '');
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->SetMargins(10, 30);
        $p->SetFillColor(250, 250, 250);

        $p->addPage('P');
        $this->printHeader($p);
        $p->Ln();
        $p->Ln();
        $this->printTax($p);
        $this->printFooter($p);

        $create = $p->createPdf();
        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        $date = explode("-", $data['data_estratto_conto']);
        $date = "{$date[2]}-{$date[1]}-{$date[0]}";
        return "Attestato contributo volontario - Movimento n. {$data['numero_versamento']} del {$date}";
    }

}
