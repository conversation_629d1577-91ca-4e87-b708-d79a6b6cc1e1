<?php

namespace Ccp;

use Ccp\om\BaseTasseQuery;
use \Core\FilterCompiler;

/**
 * Skeleton subclass for performing query and update operations on the 'tasse' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Ccp
 */
class TasseQuery extends BaseTasseQuery {

    public $totalFiltered; // Count taxes
    public $totalRows; // For sencha total rows
    public $totSum; // Sum of ImportoVersato for filtered taxes

    public function __construct($dbName = 'mc2api', $modelName = null, $modelAlias = null) {
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Return a list of set school years
     * @return array
     */
    public function getSchoolYears() {
        $con = \Propel::getConnection($this->getDbName());
        $res = $con->query("SELECT distinct(school_year) AS text FROM ccp_movement ORDER BY school_year");

        $res = $res->fetchAll(\PDO::FETCH_ASSOC);
        if ($res !== false) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * Return next numero versamento
     * @return integer
     */
    public function getMaxNumeroVersamento($year = null) {
        if (!$year) {
            $year = strtotime("01-01-" . date("Y", time()));
        }
        $con = \Propel::getConnection($this->getDbName());
        $res = $con->query("SELECT max(numero_versamento::numeric) FROM tasse WHERE data_estratto_conto >= {$year}");

        $max_tax = $res->fetch(\PDO::FETCH_ASSOC);
        return (int) $max_tax['max'];
    }

    /**
     * Return residuals for some &year
     * @param integer $year
     * @return mix => Array fo residuals by type or null if it doesn't exist
     */
    public function getStartResiduals($year) {
        $con = \Propel::getConnection($this->dbName);
        $stmt = $con->query(
                "SELECT
						tasse::float,
						contributi::float,
						quote::float,
						diversi::float,
						debito::float
				FROM tax_residuals
				WHERE year={$year}"
        );

        $res = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($res !== false) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * Get date until you have to calculate residuals and returns the total untile that time in array divided
     * by type
     * @param bigint timestamp $until
     * @return array
     */
    public function getResiduals($until = null) {
        if ($until === null) {
            $date = time();
        } else {
            $date = (int) $until;
        }
        $startYear = strtotime(date("Y", $date) . "-1-1");
        $and = " AND data_estratto_conto < {$date} AND data_estratto_conto >= {$startYear}";

        $residuals = array();
        $res = $this->getStartResiduals(date("Y", $date));
        if ($res !== null) {
            $residuals = $res;
        }

        $conMc = \Propel::getConnection('mc2api');
        $stmtSum = $conMc->query("
                    SELECT
                        (
                        CASE
                            WHEN tt.tipologia != '' THEN
                                tt.tipologia
                            ELSE
                                'DEBITO'
                            END
                        ) AS tipologia,
                        sum(importo_versato) AS importo_versato
                    FROM
                        tasse AS t LEFT JOIN tipi_tasse AS tt ON (tt.id_tipo_tassa = t.id_tipo_tassa)
                    WHERE
                        t.destinazione_pagamento = 'ccp_0'
                        {$and}
                    GROUP BY
                        tt.tipologia
                    "
        );
        $taxSum = $stmtSum->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($taxSum as $tax) {
            $field = strtolower(trim($tax['tipologia']));
            $residuals[$field] += $tax['importo_versato'];
        }
        return $residuals;
    }

    public function getTotalRows($where) {
        $con = \Propel::getConnection($this->getDbName());
        $sql = "SELECT COUNT(id_tasse) FROM tasse AS t LEFT JOIN tipi_tasse AS tt ON (tt.id_tipo_tassa = t.id_tipo_tassa) {$where}";
        $totalRows = $con->query($sql);
        $count = $totalRows->fetchAll(\PDO::FETCH_ASSOC);
        return $count[0]['count'];
    }

    /**
     * Retrun filtered taxes by $filter passed. Employee or Student data are appended
     * @param array $filter
     * @return array
     */
    public function read($filter = array()) {

        // Build filter for SQL
        $sqlFilters = array();
        foreach ($filter as $field => $value) {
            switch ($field) {
                case 'id_tasse':
                    $sqlFilters[] = "t.id_tasse = " . $value;
                    break;
                case 'anno_scolastico':
                    if ($value != 'all') {
                        $sqlFilters[] = "t.anno_scolastico = '{$value}'";
                    }
                    break;
                case 'numero_versamento':
                    $sqlFilters[] = "t.numero_versamento = '{$value}'";
                    break;
                case 'numero_bollettino':
                    $sqlFilters[] = "t.numero_bollettino = '{$value}'";
                    break;
                case 'id_classe':
                    $sqlFilters[] = "sc.id_classe in (" . join(',', json_decode($value)) . ")";
                    break;
                case 'data_estratto_conto_start':
                    $sqlFilters[] = "t.data_estratto_conto >= " . strtotime($value);
                    break;
                case 'data_estratto_conto_end':
                    $sqlFilters[] = "t.data_estratto_conto < " . strtotime($value . ' next day');
                    break;
                case 'id_tipo_tassa':
                    if ($value != 0) {
                        $sqlFilters[] = "t.id_tipo_tassa = {$value}";
                    }
                    break;
                case 'in_out':
                    if ($value == 'in') {
                        $sqlFilters[] = "t.is_incoming = 't'";
                    } else if ($value == 'out') {
                        $sqlFilters[] = "t.is_incoming = 'f'";
                    }
                    break;
                case 'modalita_pagamento':
                    if ($value != 'all') {
                        $sqlFilters[] = "t.modalita_pagamento = '{$value}'";
                    }
                    break;
                case 'destinazione_pagamento':
                    if ($value != 'all') {
                        $sqlFilters[] = "t.destinazione_pagamento = '{$value}'";
                    }
                    break;
                case 'subject':
                    $subjectData = explode('_', $value);
                    $type = $subjectData[1];
                    if ($type == 'S') {
                        $sqlFilters[] = "t.id_studente = '{$subjectData[0]}'";
                    } else if ($type == 'I') {
                        $sqlFilters[] = "t.employee_id = '{$subjectData[0]}'";
                    } else {
                        $sqlFilters[] = "t.dati_debitore ILIKE '%{$value}%'";
                    }
                    break;
                case 'employee_id':
                    $sqlFilters[] = "t.employee_id = {$value}";
                    break;
                case 'id_studente':
                    $sqlFilters[] = "t.id_studente = {$value}";
                    break;
                case 'sede_studente':
                    $sqlFilters[] = "t.sede_studente = {$value}";
                    break;
                default:
                    break;
            }
        }

        $where = count($sqlFilters) > 0 ? " WHERE " . implode(" AND ", $sqlFilters) : "";

        // Build order for SQL
        $sqlOrders = array();

        if (isset($filter['sort'])) {
            $filter['sort'] = json_decode($filter['sort']);

            foreach ($filter['sort'] as $sorter) {
                $sqlOrders[$sorter->property] = "{$sorter->property} {$sorter->direction}";
            }

            if (!isset($sqlOrders['numero_versamento'])) {
                $sqlOrders['numero_versamento'] = "numero_versamento DESC";
            }

            if (!isset($sqlOrders['data_estratto_conto'])) {
                $sqlOrders['data_estratto_conto'] = "data_estratto_conto DESC";
            }
        }

        $order = array_values($sqlOrders);

        $order_by = count($order) > 0 ? "ORDER BY " . implode(", ", $order) :
                "ORDER BY numero_versamento DESC";
        $limit = isset($filter['limit']) ? "LIMIT {$filter['limit']}" : "";
        $start = isset($filter['start']) ? "OFFSET {$filter['start']}" : "";

        $sql = "SELECT DISTINCT
                    t.id_tasse,
                    t.numero_versamento::integer,
                    t.numero_bollettino,
                    to_char(to_timestamp(t.data_estratto_conto), 'YYYY-MM-DD') AS data_estratto_conto,
                    to_char(to_timestamp(t.data_versamento), 'YYYY-MM-DD') AS data_versamento,
                    t.is_incoming,
                    t.id_studente,
                    t.sede_studente,
                    t.sede_studente||'_'||t.id_studente AS student_id,
                    t.dati_debitore,
                    t.classe,
                    t.employee_id,
                    t.id_tipo_tassa,
                    t.importo_versato,
                    t.sede_ufficio_postale,
                    t.note,
                    t.anno_scolastico,
                    t.modalita_pagamento,
                    t.destinazione_pagamento,
                    t.riferimento_estratto_conto,
                    tt.descrizione,
                    tt.tipologia,
                    tt.importo_base,
                    tt.anno_scolastico_riferimento
                FROM tasse AS t LEFT JOIN tipi_tasse AS tt ON (tt.id_tipo_tassa = t.id_tipo_tassa)
                {$where}
                {$order_by}
                {$limit}
                {$start}";

        $con = \Propel::getConnection('mc2api');
        $res = $con->query($sql);
        $taxes = $res->fetchAll(\PDO::FETCH_ASSOC);

        // Get count and sum
        $sqlSum = "SELECT is_incoming,
                    (
                        CASE WHEN is_incoming = 't'
                        THEN sum(importo_versato)
                        ELSE (sum(importo_versato) * -1) END) AS sum
                FROM tasse AS t LEFT JOIN tipi_tasse AS tt ON (tt.id_tipo_tassa = t.id_tipo_tassa)
                {$where}
                GROUP BY t.is_incoming";
        $resTotals = $con->query($sqlSum);
        $totals = $resTotals->fetchAll(\PDO::FETCH_ASSOC);
        $this->totSum = (float) $totals[0]['sum'] + (float) $totals[1]['sum'];
        $this->totalRows = $this->getTotalRows($where);

        return $taxes;
    }

}
