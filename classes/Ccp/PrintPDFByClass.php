<?php

namespace Ccp;

/**
 * Print Standard List base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFByClass {

    const spool = true;

    private $_print_id;
    private $_path;
    public $taxes;

    public function __construct($request) {
        $filter = $request;
        $this->dateStart = strtotime($filter['data_estratto_conto_start']);
        $this->dateEnd = strtotime($filter['data_estratto_conto_end']);
        $years = explode('/', $filter['SchoolYear']);
        $this->year = $years[0];

        if (!$this->dateStart) {
            $this->dateStart = strtotime($this->year . "-09-01");
        } else {
            $this->year = date('Y', $this->dateStart);
        }
        if (!$this->dateEnd) {
            $this->dateEnd = strtotime(($this->year + 1) . "-08-31");
        }

        $tasseQ = new \Ccp\TasseQuery();
        $sorting = array(
            array(
                'property'  => 'classe',
                'direction' => 'ASC'
            )
        );
        $filter['sort'] = json_encode($sorting);
        $this->taxes = $tasseQ->read($filter);
        $this->_print_id = $request['id'];
        $this->_path = $request['path'];
    }

    public function printHeader($p, $class = '-') {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->Cell(100, 5, 'Riepilogo per classi dal ' . date('d/m/Y', $this->dateStart) . ' al ' . date('d/m/Y', $this->dateEnd), 0, 1, 'L', false, '', 1);
        $p->Ln();
        $p->SetFontSize(10);
        $p->Cell(0, 5, "Classe: {$class}", 0, 1, 'C', false, '', 1);
        $p->SetFontSize(8);
        $p->setFont('', 'B');
        $p->SetFillColor(210, 210, 210);
        $p->Cell(90, 5, 'Alunno / Causale', 1, 0, 'C', true);
        $p->Cell(25, 5, 'N. Bol.', 1, 0, 'C', true);
        $p->Cell(25, 5, 'Data E.C.', 1, 0, 'C', true);
        $p->Cell(30, 5, 'Importo versato (€)', 1, 1, 'C', true);
        $p->setFont('', '');
    }

    public function printFinalData($p, $total) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->SetY($p->getY() + 10);
        $p->setX(70);
        $p->Cell(25, 7, 'Saldo (€)', 1, 0, 'L');
        $p->setX(95);
        $p->Cell(30, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $total), 1, 1, 'R');
        $p->setFont('', '');
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->SetMargins(20, 30);

        $oddStyle = true;
        $preSubject = $preClass = null;
        $totalClass = $total = 0;

        foreach ($this->taxes as $tax) {
            // Payee data
            $subject = $tax['dati_debitore'] . ' (A)';
            $class = '-';

            if ($tax['employee_id'] > 0) {
                $employee = \Employee\EmployeeQuery::create()->findPk($tax['employee_id']);
                $subject = $employee->getSurname() . ' ' . $employee->getName() . ' (I)';
            }

            if ($tax['id_studente'] > 0) {
                $subject = $tax['dati_debitore'] . ' (S)';
                $class = $tax['classe'];
            }

            // Class Header logic
            if ($class !== $preClass) {
                $p->setFont('', 'B');
                $p->SetFillColor(210, 210, 210);
                $p->setY($p->getY() + 5);
                $p->setX(135);
                $p->Cell(25, 5, 'TOT. CLASSE (€)', 1, 0, 'L', true, null, 1);
                $p->setX(160);
                $p->Cell(30, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $totalClass), 1, 1, 'R', true, null, 1);
                $totalClass = 0;
                $p->addPage('P');
                $p->setFont('', '');
                $this->printHeader($p, $class);
            }

            if ($subject !== $preSubject) {
                $oddStyle = false;
                $p->SetFillColor(235, 235, 235);
                $p->setY($p->getY() + 2);
                $p->Cell(90, 5, $subject, 1, 1, 'L', true, null, 1);
            }

            $preSubject = $subject;
            $preClass = $class;

            // Tax data
            if ($tax['is_incoming'] === false) {
                $tax['importo_versato'] *= -1;
            }

            // Change style row by row
            if ($oddStyle) {
                $p->SetFillColor(235, 235, 235);
                $oddStyle = false;
            } else {
                $p->SetFillColor(250, 250, 250);
                $oddStyle = true;
            }

            $p->setX(30);
            $p->Cell(80, 5, $tax['descrizione'], 1, 0, 'L', true, null, 1);
            $p->Cell(25, 5, $tax['numero_bollettino'], 1, 0, 'C', true);
            $p->Cell(25, 5, date('d/m/Y', strtotime($tax['data_estratto_conto'])), 1, 0, 'C', true);
            $p->Cell(30, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $tax['importo_versato']), 1, 1, 'R', true, null, 1);

            // Update totals
            $totalClass += $tax['importo_versato'];
            $total += $tax['importo_versato'];
        }

        // Last class
        $p->setFont('', 'B');
        $p->SetFillColor(210, 210, 210);
        $p->setY($p->getY() + 5);
        $p->setX(135);
        $p->Cell(25, 5, 'TOT. CLASSE (€)', 1, 0, 'L', true, null, 1);
        $p->setX(160);
        $p->Cell(30, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $totalClass), 1, 1, 'R', true, null, 1);

        $this->printFinalData($p, $total);

        $create = $p->createPdf();

        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        $clss = count($data['classes']);
        $dStart = explode("-", $data['data_estratto_conto_start']);
        $dStart = "{$dStart[2]}-{$dStart[1]}-{$dStart[0]}";
        $dEnd = explode("-", $data['data_estratto_conto_end']);
        $dEnd = "{$dEnd[2]}-{$dEnd[1]}-{$dEnd[0]}";
        if ($clss == 1) {
            $clss = "1 Classe";
        } else {
            $clss = ($clss > 1 ? $clss : "Tutte le ") . " Classi";
        }
        return "Riepilogo Movimenti per classe - {$dStart} / {$dEnd} - {$clss}";
    }

}
