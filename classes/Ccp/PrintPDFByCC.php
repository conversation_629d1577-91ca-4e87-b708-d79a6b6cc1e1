<?php

namespace Ccp;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Print Cash Journal base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFByCC {

    const spool = true;

    private $_path;
    private $_print_id;
    private $pgs;
    private $pge;
    private $mos;
    private $moe;
    private $sum;
    private $sumr;
    public $taxes;

    public function __construct($request) {
        $filter = $request;
        $this->dateStart = strtotime($filter['data_estratto_conto_start']);
        $this->dateEnd = strtotime($filter['data_estratto_conto_end']);
        $this->ccs = json_decode($filter['ccs'], true);
        $this->_print_id = $request['id'];
        $this->_path = $request['path'];

        // Retrieves residuals
        $tq = new TasseQuery();
        $residuals = $tq->getResiduals($this->dateStart);

//        // Retrieves taxes from residual to start date
//        $tasseQ = new \Ccp\TasseQuery();
//        $sorting = array(
//            array(
//                'property'  => 'data_estratto_conto',
//                'direction' => 'ASC'
//            ),
//            array(
//                'property'  => 'numero_versamento',
//                'direction' => 'ASC'
//            )
//        );
//        $filter['sort'] = json_encode($sorting);
//        $this->taxes = $tasseQ->read($filter);
        // Generate data arrays
        $this->res = [
            'title'      => 'RESIDUI INIZIO ANNO',
            'credito'    => $residuals['tasse'] + $residuals['contributi'] + $residuals['quote'] + $residuals['diversi'],
            'debito'     => $residuals['debito'],
            'saldo'      => $residuals['tasse'] + $residuals['contributi'] + $residuals['quote'] + $residuals['diversi'] - $residuals['debito'],
            'tasse'      => $residuals['tasse'],
            'contributi' => $residuals['contributi'],
            'quote'      => $residuals['quote'],
            'diversi'    => $residuals['diversi']
        ];

        $this->pgs = [
            'title'      => 'RIPORTI PAGINA PRECEDENTE',
            'credito'    => 0,
            'debito'     => 0,
            'saldo'      => "",
            'tasse'      => 0,
            'contributi' => 0,
            'quote'      => 0,
            'diversi'    => 0
        ];

        $this->pge = [
            'title'      => 'TOTALI PAGINA',
            'credito'    => 0,
            'debito'     => 0,
            'saldo'      => "",
            'tasse'      => 0,
            'contributi' => 0,
            'quote'      => 0,
            'diversi'    => 0
        ];

        $this->mos = [
            'title'      => 'RIPORTI MESE PRECEDENTE',
            'credito'    => 0,
            'debito'     => 0,
            'saldo'      => "",
            'tasse'      => 0,
            'contributi' => 0,
            'quote'      => 0,
            'diversi'    => 0
        ];

        $this->moe = [
            'title'      => 'TOTALI MESE',
            'credito'    => 0,
            'debito'     => 0,
            'saldo'      => "",
            'tasse'      => 0,
            'contributi' => 0,
            'quote'      => 0,
            'diversi'    => 0
        ];

        $this->sum = [
            'title'      => 'TOTALI CORRENTI',
            'credito'    => 0,
            'debito'     => 0,
            'saldo'      => 0,
            'tasse'      => 0,
            'contributi' => 0,
            'quote'      => 0,
            'diversi'    => 0
        ];

        $this->sumr = [
            'title'      => 'SALDI (TOTALI CORRENTI + RESIDUI ANNO)',
            'credito'    => $this->res['tasse'] + $this->res['contributi'] + $this->res['quote'] + $this->res['diversi'],
            'debito'     => $this->res['debito'],
            'saldo'      => $this->res['tasse'] + $this->res['contributi'] + $this->res['quote'] + $this->res['diversi'] - $this->res['debito'],
            'tasse'      => $this->res['tasse'],
            'contributi' => $this->res['contributi'],
            'quote'      => $this->res['quote'],
            'diversi'    => $this->res['diversi']
        ];

        // Retrieves taxes
        $tasseQ = new \Ccp\TasseQuery();
        $sorting = [
            [
                'property'  => 'data_estratto_conto',
                'direction' => 'ASC'
            ],
            [
                'property'  => 'numero_versamento',
                'direction' => 'ASC'
            ]
        ];
        $filter['sort'] = json_encode($sorting);
        $this->taxes = $tasseQ->read($filter);
    }

    public function printHeader($p) {
        $ccs = [];
        foreach ($this->ccs as $cc) {
            $ccs[] = "{$cc['denomination']} ({$cc['coordinates']})";
        }
        $ccs = implode(" - ", $ccs);

        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->Cell(0, 5, "Registro di Conto Corrente \"{$ccs}\". Periodo " . date("d/m/Y", $this->dateStart) . " - " . date("d/m/Y", $this->dateEnd), 0, 1, 'L', false, '', 1);
        $p->Ln();
        $p->SetFontSize(8);
        $p->SetFillColor(210, 210, 210);
        $p->Cell(12, 5, 'N. Tr.', 1, 0, 'C', true);
        $p->Cell(50, 5, 'Beneficiario', 1, 0, 'C', true);
        $p->Cell(13, 5, 'Classe', 1, 0, 'C', true);
        $p->Cell(50, 5, 'Causale / Voce', 1, 0, 'C', true);
        $p->Cell(17, 5, 'Data mov.', 1, 0, 'C', true);
        $p->Cell(17, 5, 'Data E.C.', 1, 0, 'C', true);
        $p->Cell(1, 5, '', 0, 0, 'C');
        $p->Cell(19, 5, 'Op. a credito', 1, 0, 'C', true);
        $p->Cell(19, 5, 'Op. a debito', 1, 0, 'C', true);
        $p->Cell(20, 5, 'Saldo conto', 1, 0, 'C', true);
        $p->Cell(1, 5, '', 0, 0, 'C');
        $p->Cell(17, 5, 'Tasse', 1, 0, 'C', true);
        $p->Cell(17, 5, 'Contributi', 1, 0, 'C', true);
        $p->Cell(17, 5, 'Quote varie', 1, 0, 'C', true);
        $p->Cell(17, 5, 'Diversi', 1, 1, 'C', true);
    }

    // Summary row
    private function printSummary($p, $data, $bold = false) {
        $p->SetFillColor(255, 255, 255);
        if ($bold) {
            $p->SetFillColor(220, 220, 220);
        }
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->Cell(159, 6, $data['title'], 1, 0, 'R', $bold);
        $p->Cell(1, 6, '', 0, 0, 'C');
        $p->Cell(19, 6, money_format(MONEY_FORMAT_PRINT, $data['credito']), 1, 0, 'R', $bold, null, 1);
        $p->Cell(19, 6, money_format(MONEY_FORMAT_PRINT, $data['debito']), 1, 0, 'R', $bold, null, 1);
        $p->Cell(20, 6, $data['saldo'] === "" ? '' : money_format(MONEY_FORMAT_PRINT, $data['saldo']), 1, 0, 'R', $bold, null, 1);
        $p->Cell(1, 6, '', 0, 0, 'C');
        $p->Cell(17, 6, money_format(MONEY_FORMAT_PRINT, $data['tasse']), 1, 0, 'R', $bold, null, 1);
        $p->Cell(17, 6, money_format(MONEY_FORMAT_PRINT, $data['contributi']), 1, 0, 'R', $bold, null, 1);
        $p->Cell(17, 6, money_format(MONEY_FORMAT_PRINT, $data['quote']), 1, 0, 'R', $bold, null, 1);
        $p->Cell(17, 6, money_format(MONEY_FORMAT_PRINT, $data['diversi']), 1, 1, 'R', $bold, null, 1);
        $p->SetFillColor(255, 255, 255);
    }

    // Month name
    private function printMonthName($p, $month) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->SetFillColor(235, 235, 235);
        $p->Cell(0, 5, $month, 1, 1, 'C', true);
        $p->SetFillColor(255, 255, 255);
    }

    // Final data
    private function printFinalData($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->SetY($p->getY() + 10);
        $x = 70;
        $p->SetX($x);
        $p->Cell(50, 7, 'Operazioni a credito (€)', 1, 0, 'R', true);
        $p->Cell(46, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $this->sum['credito']), 1, 1, 'R', true);
        $p->SetX($x);
        $p->Cell(50, 7, 'Operazioni a debito (€)', 1, 0, 'R', true);
        $p->Cell(46, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $this->sum['debito']), 1, 1, 'R', true);
        $p->SetX($x);
        $p->Cell(50, 7, 'Saldo cassa iniziale (€)', 1, 0, 'R', true);
        $p->Cell(46, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $this->res['saldo']), 1, 1, 'R', true);
        $p->SetX($x);
        $p->Cell(50, 7, 'Saldo cassa finale (€)', 1, 0, 'R', true);
        $p->Cell(46, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $this->sumr['saldo']), 1, 1, 'R', true);
    }

    // Utility methods
    private function updateValues($amount, &$dataArr, $to_sum = false) {
        if (!is_array($amount)) {
            $amount = [
                'credito'    => $amount,
                'debito'     => $amount,
                'saldo'      => $amount,
                'tasse'      => $amount,
                'contributi' => $amount,
                'quote'      => $amount,
                'diversi'    => $amount
            ];
        }
        foreach (array_keys($amount) as $key) {
            if ($to_sum) {
                $dataArr[$key] += $amount[$key];
            } else {
                $dataArr[$key] = $amount[$key];
            }
        }
    }

    private function updateTotals($tax) {
        if ($tax['is_incoming'] === true) {
            $this->pgs['credito'] += $tax['importo_versato'];
            $this->mos['credito'] += $tax['importo_versato'];
            $this->pge['credito'] += $tax['importo_versato'];
            $this->moe['credito'] += $tax['importo_versato'];
            $this->sum['credito'] += $tax['importo_versato'];
            $this->sumr['credito'] += $tax['importo_versato'];
            $this->sum['saldo'] += $tax['importo_versato'];
            $this->sumr['saldo'] += $tax['importo_versato'];

            switch ($tax['tipologia']) {
                case 'TASSE':
                    $this->pgs['tasse'] += $tax['importo_versato'];
                    $this->mos['tasse'] += $tax['importo_versato'];
                    $this->pge['tasse'] += $tax['importo_versato'];
                    $this->moe['tasse'] += $tax['importo_versato'];
                    $this->sum['tasse'] += $tax['importo_versato'];
                    $this->sumr['tasse'] += $tax['importo_versato'];
                    break;
                case 'CONTRIBUTI':
                    $this->pgs['contributi'] += $tax['importo_versato'];
                    $this->mos['contributi'] += $tax['importo_versato'];
                    $this->pge['contributi'] += $tax['importo_versato'];
                    $this->moe['contributi'] += $tax['importo_versato'];
                    $this->sum['contributi'] += $tax['importo_versato'];
                    $this->sumr['contributi'] += $tax['importo_versato'];
                    break;
                case 'QUOTE':
                    $this->pgs['quote'] += $tax['importo_versato'];
                    $this->mos['quote'] += $tax['importo_versato'];
                    $this->pge['quote'] += $tax['importo_versato'];
                    $this->moe['quote'] += $tax['importo_versato'];
                    $this->sum['quote'] += $tax['importo_versato'];
                    $this->sumr['quote'] += $tax['importo_versato'];
                    break;
                case 'DIVERSI':
                    $this->pgs['diversi'] += $tax['importo_versato'];
                    $this->mos['diversi'] += $tax['importo_versato'];
                    $this->pge['diversi'] += $tax['importo_versato'];
                    $this->moe['diversi'] += $tax['importo_versato'];
                    $this->sum['diversi'] += $tax['importo_versato'];
                    $this->sumr['diversi'] += $tax['importo_versato'];
                    break;
                default:
                    break;
            }
        } else {
            $this->pgs['debito'] += $tax['importo_versato'];
            $this->mos['debito'] += $tax['importo_versato'];
            $this->pge['debito'] += $tax['importo_versato'];
            $this->moe['debito'] += $tax['importo_versato'];
            $this->sum['debito'] += $tax['importo_versato'];
            $this->sumr['debito'] += $tax['importo_versato'];
            $this->sum['saldo'] -= $tax['importo_versato'];
            $this->sumr['saldo'] -= $tax['importo_versato'];

            switch ($tax['tipologia']) {
                case 'TASSE':
                    $this->pgs['tasse'] -= $tax['importo_versato'];
                    $this->mos['tasse'] -= $tax['importo_versato'];
                    $this->pge['tasse'] -= $tax['importo_versato'];
                    $this->moe['tasse'] -= $tax['importo_versato'];
                    $this->sum['tasse'] -= $tax['importo_versato'];
                    $this->sumr['tasse'] -= $tax['importo_versato'];
                    break;
                case 'CONTRIBUTI':
                    $this->pgs['contributi'] -= $tax['importo_versato'];
                    $this->mos['contributi'] -= $tax['importo_versato'];
                    $this->pge['contributi'] -= $tax['importo_versato'];
                    $this->moe['contributi'] -= $tax['importo_versato'];
                    $this->sum['contributi'] -= $tax['importo_versato'];
                    $this->sumr['contributi'] -= $tax['importo_versato'];
                    break;
                case 'QUOTE':
                    $this->pgs['quote'] -= $tax['importo_versato'];
                    $this->mos['quote'] -= $tax['importo_versato'];
                    $this->pge['quote'] -= $tax['importo_versato'];
                    $this->moe['quote'] -= $tax['importo_versato'];
                    $this->sum['quote'] -= $tax['importo_versato'];
                    $this->sumr['quote'] -= $tax['importo_versato'];
                    break;
                case 'DIVERSI':
                    $this->pgs['diversi'] -= $tax['importo_versato'];
                    $this->mos['diversi'] -= $tax['importo_versato'];
                    $this->pge['diversi'] -= $tax['importo_versato'];
                    $this->moe['diversi'] -= $tax['importo_versato'];
                    $this->sum['diversi'] -= $tax['importo_versato'];
                    $this->sumr['diversi'] -= $tax['importo_versato'];
                    break;
                default:
                    break;
            }
        }
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->setPageOrientation('L');
        $p->SetMargins(5, 30);

        $resetArray = [
            'credito'    => 0,
            'debito'     => 0,
            'saldo'      => "",
            'tasse'      => 0,
            'contributi' => 0,
            'quote'      => 0,
            'diversi'    => 0
        ];

        // Splits taxes into each months
        $currentMonth = date("n", $this->taxes[0]['data_estratto_conto']);
        $months = [];
        $cnt = 0;
        for ($cnt = 0; $cnt <= 11; $cnt++) {
            $months[_(date("F", strtotime("1-{$currentMonth}-1970")))] = [];
            if ($currentMonth === 12) {
                $currentMonth = 1;
            } else {
                $currentMonth++;
            }
        }

//        $months = [
//            'Gennaio'   => [],
//            'Febbraio'  => [],
//            'Marzo'     => [],
//            'Aprile'    => [],
//            'Maggio'    => [],
//            'Giugno'    => [],
//            'Luglio'    => [],
//            'Agosto'    => [],
//            'Settembre' => [],
//            'Ottobre'   => [],
//            'Novembre'  => [],
//            'Dicembre'  => []
//        ];
        foreach ($this->taxes as $tax) {
            $months[_(date("F", strtotime($tax['data_estratto_conto'])))][] = $tax;
        }
        foreach ($months as $m => $taxes) {
            if (count($taxes) === 0) {
                unset($months[$m]);
            }
        }

        // Prints all taxes by month
        foreach ($months as $m => $taxes) {
            // New page if max month is changed
            $p->addPage('L');
            $this->printHeader($p);
            $this->printSummary($p, $this->res);
            $this->printSummary($p, $this->sum);
            $p->Cell(0, 1, '', 0, 1, 'C');
            $this->printMonthName($p, $m);
            $this->printSummary($p, $this->mos);

            $i = 0;
            $oddStyle = true;
            foreach ($taxes as $tax) {
                // New page if max row number is reached
                if (($i % 18 === 0 && $i > 0)) {
                    $i = 1;

                    // Prints page and month totals
                    $this->printSummary($p, $this->pge);
                    $this->printSummary($p, $this->moe);

                    // Prints sums
                    $p->Cell(0, 1, '', 0, 1, 'C');
                    $this->printSummary($p, $this->sum);
                    $this->printSummary($p, $this->sumr);

                    // Adds a new page
                    $p->addPage('L');
                    $this->printHeader($p);
                    $this->printSummary($p, $this->res);
                    $this->printSummary($p, $this->sum);
                    $p->Cell(0, 1, '', 0, 1, 'C');
                    $this->printMonthName($p, $m);
                    $this->printSummary($p, $this->pgs);
                    $oddStyle = true;

                    // Resets Page totals
                    $this->updateValues($resetArray, $this->pge);
                } else {
                    $i++;
                }

                // Updates Page and Month totals
                $this->updateTotals($tax);

                // Payee Data
                $class = '';
                $subject = '';
                if (trim($tax['dati_debitore'])) {
                    $subject = "{$tax['dati_debitore']} (A)";
                }
                if ($tax['employee_id'] > 0) {
                    $employee = \Employee\EmployeeQuery::create()->findPk($tax['employee_id']);
                    $subject = $employee->getSurname() . ' ' . $employee->getName() . ' (I)';
                }
                if ($tax['id_studente'] > 0) {
                    $subject = "{$tax['dati_debitore']} (S)";
                    $class = $tax['classe'];
                }

                // Row style
                if ($oddStyle) {
                    $p->SetFillColor(235, 235, 235);
                    $oddStyle = false;
                } else {
                    $p->SetFillColor(250, 250, 250);
                    $oddStyle = true;
                }

                // Tax row
                $p->setFont('', '');
                $p->Cell(12, 5, $tax['numero_versamento'], 1, 0, 'C', true);
                $p->Cell(50, 5, $subject, 1, 0, 'C', true, null, 1);
                $p->Cell(13, 5, $class, 1, 0, 'C', true);
                $p->Cell(50, 5, $tax['descrizione'], 1, 0, 'C', true, null, 1);
                $p->Cell(17, 5, date('d/m/Y', strtotime($tax['data_versamento'])), 1, 0, 'C', true);
                $p->Cell(17, 5, date('d/m/Y', strtotime($tax['data_estratto_conto'])), 1, 0, 'C', true);
                $p->Cell(1, 5, '', 0, 0, 'C');
                $p->Cell(19, 5, $tax['is_incoming'] === true ? money_format(MONEY_FORMAT_PRINT, $tax['importo_versato']) : '', 1, 0, 'R', true, null, 1);
                $p->Cell(19, 5, $tax['is_incoming'] === false ? money_format(MONEY_FORMAT_PRINT, $tax['importo_versato']) : '', 1, 0, 'R', true, null, 1);
                $p->Cell(20, 5, money_format(MONEY_FORMAT_PRINT, $this->sum['saldo']), 1, 0, 'R', true, null, 1);
                $p->Cell(1, 5, '', 0, 0, 'C');
                $p->Cell(17, 5, $tax['tipologia'] == 'TASSE' ? money_format(MONEY_FORMAT_PRINT, $tax['is_incoming'] === true ? $tax['importo_versato'] : ($tax['importo_versato'] * -1)) : '', 1, 0, 'R', true, null, 1);
                $p->Cell(17, 5, $tax['tipologia'] == 'CONTRIBUTI' ? money_format(MONEY_FORMAT_PRINT, $tax['is_incoming'] === true ? $tax['importo_versato'] : ($tax['importo_versato'] * -1)) : '', 1, 0, 'R', true, null, 1);
                $p->Cell(17, 5, $tax['tipologia'] == 'QUOTE' ? money_format(MONEY_FORMAT_PRINT, $tax['is_incoming'] === true ? $tax['importo_versato'] : ($tax['importo_versato'] * -1)) : '', 1, 0, 'R', true, null, 1);
                $p->Cell(17, 5, $tax['tipologia'] == 'DIVERSI' ? money_format(MONEY_FORMAT_PRINT, $tax['is_incoming'] === true ? $tax['importo_versato'] : ($tax['importo_versato'] * -1)) : '', 1, 1, 'R', true, null, 1);
            }

            // Prints page and month totals
            $this->printSummary($p, $this->pge);
            $this->printSummary($p, $this->moe);

            // Prints sums
            $p->Cell(0, 1, '', 0, 1, 'C');
            $this->printSummary($p, $this->sum);
            $this->printSummary($p, $this->sumr, true);

            // Resets Month totals and Page starters
            $this->updateValues($resetArray, $this->pgs);
            $this->updateValues($resetArray, $this->pge);
            $this->updateValues($resetArray, $this->moe);
        }

        // Final data
        $this->printFinalData($p);

        $create = $p->createPdf();

        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        return "Registro conto corrente - " . date("d/m/Y", $data['data_estratto_conto_start']) . " - " . date("d/m/Y", $data['data_estratto_conto_end']);
    }

}
