<?php

namespace Ccp;

/**
 * Print Standard List base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFByDebitor {

    const spool = true;

    private $_print_id;
    private $_path;
    public $taxes;

    public function __construct($request) {
        $filter = $request;
        $this->dateStart = strtotime($filter['data_estratto_conto_start']);
        $this->dateEnd = strtotime($filter['data_estratto_conto_end']);
        $this->as = explode('/', $filter['SchoolYear']);

        if (!$this->dateStart) {
            $this->dateStart = strtotime($this->as[0] . "-01-01");
        } else {
            $this->as[0] = date('Y', $this->dateStart);
        }
        if (!$this->dateEnd) {
            $this->dateEnd = strtotime($this->as[0] . "-12-31");
        }

        $tasseQ = new \Ccp\TasseQuery();
        $sorting = array(
            array(
                'property'  => 'dati_debitore',
                'direction' => 'ASC'
            ),
            array(
                'property'  => 'anno_scolastico',
                'direction' => 'ASC'
            ),
            array(
                'property'  => 'data_versamento',
                'direction' => 'ASC'
            ),
            array(
                'property'  => 'numero_versamento',
                'direction' => 'ASC'
            )
        );
        $filter['sort'] = json_encode($sorting);
        $this->taxes = $tasseQ->read($filter);
        $this->_print_id = $request['id'];
        $this->_path = $request['path'];
    }

    public function printDebitorHeader($p, $debitor) {
        $p->setFont('', 'B');
        $p->SetFontSize(10);
        $p->Cell(0, 5, "Riepilogo movimenti Intestatario: {$debitor} dal " . date('d/m/Y', $this->dateStart) . " al " . date('d/m/Y', $this->dateEnd), 0, 1, 'C', false, '', 1);
        $p->SetFontSize(8);
        $p->Ln();
        $p->SetFillColor(210, 210, 210);
        $p->Cell(10, 5, "Cl.", 1, 0, 'C', true, '', 1);
        $p->Cell(50, 5, "Causale", 1, 0, 'L', true, '', 1);
        $p->Cell(20, 5, "A.S. Rif.", 1, 0, 'C', true, '', 1);
        $p->Cell(20, 5, "Versamento", 1, 0, 'C', true, '', 1);
        $p->Cell(20, 5, "Num. Vers.", 1, 0, 'C', true, '', 1);
        $p->Cell(50, 5, "Note", 1, 0, 'L', true, '', 1);
        $p->Cell(0, 5, "Importo (€)", 1, 1, 'L', true, '', 1);
        $p->setFont('', '');
    }

    public function printDebitorTax($p, $tax = array()) {
        $p->SetFontSize(8);
        $p->setFont('', '');
        $p->Cell(10, 5, "{$tax['classe']}", 1, 0, 'C', true, '', 1);
        $p->Cell(50, 5, "{$tax['causale']}", 1, 0, 'L', true, '', 1);
        $p->Cell(20, 5, "{$tax['anno_scolastico']}", 1, 0, 'C', true, '', 1);
        $p->Cell(20, 5, "{$tax['data_versamento']}", 1, 0, 'C', true, '', 1);
        $p->Cell(20, 5, "{$tax['numero_versamento']}", 1, 0, 'C', true, '', 1);
        $p->Cell(50, 5, "{$tax['note']}", 1, 0, 'L', true, '', 1);
        $p->setFont('', 'B');
        $p->Cell(0, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $tax['importo_versato']), 1, 1, 'R', true, '', 1);
        $p->setFont('', '');
    }

    public function printDebitorTotal($p, $total) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->SetFillColor(210, 210, 210);
        $p->Cell(160, 5, "TOTALE", 1, 0, 'R', true);
        $p->Cell(10, 5, $total['count'], 1, 0, 'C', true);
        $p->Cell(0, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $total['total']), 1, 0, 'R', true);
        $p->setFont('', '');
    }

    public function printDebitorFooter($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->SetY($p->getY() + 10);
        $p->setX(150);
        $p->Cell(30, 7, 'La Segreteria', 0, 0, 'C');
        $p->setFont('', '');
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->SetMargins(10, 30);

        $list = array();

        // Calculates totals by type
        foreach ($this->taxes as $tax) {
            if ($tax['is_incoming'] === false) {
                $tax['importo_versato'] *= -1;
            }

            if ($tax['classe']) {
                $tax['classe'] = "({$tax['classe']})";
            }

            $db = \Db::getInstance();
            $r = $db->query("SELECT descrizione FROM tipi_tasse WHERE id_tipo_tassa = {$tax['id_tipo_tassa']}");
            if ($r) {
                $r = $db->fetchAll();
                if ($r) {
                    $tax['causale'] = $r[0]['descrizione'];
                }
            }

            $d = explode("-", $tax['data_versamento']);
            $d = "{$d[2]}/{$d[1]}/{$d[0]}";
            $tax['data_versamento'] = $d;

            // Splits Students, Employees, Others
            $debitor = "X";
            if ($tax['id_studente'] != -1) {
                $debitor = "S_{$tax['id_studente']}";
            } else if ($tax['employee_id'] != -1) {
                $debitor = "E_{$tax['employee_id']}";
            } else if ($tax['dati_debitore']) {
                $debitor = "A_" . str_replace(" ", "", $tax['dati_debitore']);
            }

            $list['debitors'][$debitor]['name'] = $tax['dati_debitore'] ? $tax['dati_debitore'] : " Non definito";
            $list['debitors'][$debitor]['count'] ++;
            $list['debitors'][$debitor]['total'] += $tax['importo_versato'];
            $list['debitors'][$debitor]['taxes'][$tax['id_tasse']] = $tax;
        }

        foreach ($list['debitors'] as $d) {
            $p->addPage('P');
            // Header
            $this->printDebitorHeader($p, $d['name']);
            // Taxes
            $oddStyle = false;
            foreach ($d['taxes'] as $t) {
                if ($oddStyle) {
                    $p->SetFillColor(235, 235, 235);
                    $oddStyle = false;
                } else {
                    $p->SetFillColor(250, 250, 250);
                    $oddStyle = true;
                }
                $this->printDebitorTax($p, $t);
            }
            // Total
            $this->printDebitorTotal($p, $d);
            // Footer
            $this->printDebitorFooter($p, $d);
        }

        $create = $p->createPdf();

        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        $dStart = explode("-", $data['data_estratto_conto_start']);
        $dStart = "{$dStart[2]}-{$dStart[1]}-{$dStart[0]}";
        $dEnd = explode("-", $data['data_estratto_conto_end']);
        $dEnd = "{$dEnd[2]}-{$dEnd[1]}-{$dEnd[0]}";

        return "Riepilogo Movimenti per debitore - {$dStart} / {$dEnd}";
    }

}
