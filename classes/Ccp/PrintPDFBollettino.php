<?php

namespace Ccp;

/**
 * Print Bollettino, choosed by variable $template passed by $_GET
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFBollettino {

    const spool = true;

    private $_print_id;
    private $_path;
    public $template; // Bo<PERSON>ttino template
    public $data; // Data for svg print
    public $filter; // $_GET filter

    public function __construct($request) {
        $this->filter = $request;
        $this->printSettings = json_decode($request['printSettings']);
        $this->template = $this->printSettings->template;
        $this->schoolYear = $this->printSettings->school_year;
        $this->_print_id = $request['id'];
        $this->_path = $request['path'];

        foreach (json_decode($request['students']) as $student) {
            $student = explode("/", $student);
            $this->students[] = "{$student[1]}_{$student[4]}_{$student[3]}";
        }

        $this->data = $this->getBollettinoData((int) $this->printSettings->id_causale);
    }

    public function getPrintOptions() {
        $printOptions = array();
        foreach ($this->printSettings as $key => $value) {
            $printOptions[$key] = true;
        }

        unset($printOptions['id_causale']);
        unset($printOptions['template']);
        unset($printOptions['school_year']);

        return $printOptions;
    }

    public function getBollettinoData($taxType) {
        $conMc2 = \Propel::getConnection('mc2api');
        $pagesData = array();
        $instClass = \Core\InstituteQuery::create()
                ->join('Contact', \Criteria::LEFT_JOIN)
                ->joinWith('Contact.CityKey', \Criteria::LEFT_JOIN)
                ->withColumn('CityKey.description', 'InstituteCity')
                ->filterByDef(true)
                ->select(array(
                    'Name',
                    'InstituteCity',
                    'PostalAccount'
                ))
                ->findOne();
        if ($taxType) {
            $tipoTassa = TipiTasseQuery::create()->findPk($taxType, $conMc2);
        }

        $filter = array(
            'id_studente' => implode(',', $this->students),
            'all'         => true,
            'school_year' => $this->schoolYear
        );
        $school_id = \Core\ParameterQuery::create()->filterByName('SCHOOL_ID')->findOne();
        $api = new \Core\ExternalApi;
        $data = $api->get("/school/{$school_id->getValue()}/student", $filter);
        $students = $data['results'];

        $printOptions = $this->getPrintOptions();

        $convertNum = new Numbers_Words_it_IT;
        $pagesData ['common']['year'] = date('Y');
        $pagesData ['common']['pages'] = 1;
        $pagesData ['common']['cc_p'] = ($printOptions['cc_p'] ? $instClass['PostalAccount'] : '');
        $tmpSum = preg_split('/[, \.]/', $taxType ? $tipoTassa->getImportoBase() : $taxType);

        $pagesData['common']['euro_i'] = ($printOptions['sum'] ? $tmpSum[0] : '');
        $pagesData['common']['euro_d'] = ($printOptions['sum'] ? str_pad($tmpSum[1], 2, '0', STR_PAD_RIGHT) : '');
        $pagesData['common']['sum_words'] = ($printOptions['sum_words'] ? $convertNum->toWords($tmpSum[0]) . ' / ' . str_pad($tmpSum[1], 2, '0', STR_PAD_RIGHT) : '');
        $pagesData['common']['intestato_a'] = ($printOptions['intestato_a'] ? $instClass['Name'] . ' (' . $instClass['InstituteCity'] . ') - SERVIZIO CASSA' : '');
        $pagesData['common']['doc_type'] = ($printOptions['doc_type'] ? '123' : '');
        $pagesData['common']['causale'] = ($printOptions['causale'] ? strtoupper($taxType ? $tipoTassa->getDescrizione() : $taxType) : '');

        $tp = new TassePeer();
        $schoolYear = $tp->getSchoolYear();

        foreach ($students as $key => $value) {
            $value = (array) $value;

            $eseguito_da = $value['cognome'] . ' ' . $value['nome'];

            $eseguito_da_gen = $value['denominazione_parente'];
            $pagesData[1][$key]['residente_in_via'] = ($printOptions['residente_in_via'] ? mb_convert_encoding(trim($value['indirizzo_residenza']), "UTF-8", "HTML-ENTITIES") : '');
            $pagesData[1][$key]['cap'] = ($printOptions['cap'] ? $value['cap_residenza'] : '');
            $pagesData[1][$key]['localita'] = ($printOptions['localita'] ? mb_convert_encoding($value['comune_residenza'], "UTF-8", "HTML-ENTITIES") : '');
            $pagesData[1][$key]['eseguito_da'] = ($printOptions['eseguito_da'] ? mb_convert_encoding($eseguito_da, "UTF-8", "HTML-ENTITIES") : '');
            if ($printOptions['eseguito_da_gen']) {
                $pagesData[1][$key]['eseguito_da'] = ($printOptions['eseguito_da_gen'] ? mb_convert_encoding($eseguito_da_gen, "UTF-8", "HTML-ENTITIES") : '');
            }
            $pagesData[1][$key]['sm'] = 'x';
            $pagesData[1][$key]['u'] = '';
            $pagesData[1][$key]['i'] = '';
            $pagesData[1][$key]['classe'] = ($printOptions['eseguito_da_classe'] ? $value['classe'] . mb_convert_encoding($value['sezione'], "UTF-8", "HTML-ENTITIES") : '');
            $pagesData[1][$key]['p_n'] = ($printOptions['provincia_nascita'] ? mb_convert_encoding($value['provincia_nascita'], "UTF-8", "HTML-ENTITIES") : '');
            $pagesData[1][$key]['p_r'] = ($printOptions['provincia_nascita'] ? $value['provincia_nascita_sigla'] : '');
            $pagesData[1][$key]['nascita'] = ($printOptions['nascita'] ? mb_convert_encoding(substr($value['comune_nascita'], 0, 10), "UTF-8", "HTML-ENTITIES") : '');
            $pagesData[1][$key]['codice_fiscale'] = ($printOptions['codice_fiscale'] ? $value['codice_fiscale'] : '');
            $pagesData[1][$key]['d_n'] = ($printOptions['data_nascita'] ? $value['data_nascita'] : '');
            $pagesData[1][$key]['sesso'] = ($printOptions['sesso'] ? $value['sesso'] : '');
            $pagesData[1][$key]['anno_scolastico'] = ($printOptions['anno_scolastico'] ? $schoolYear['start'] . '/' . $schoolYear['end'] : '');
            $pagesData[1][$key]['matricola'] = ($printOptions['eseguito_da_matricola'] ? strtoupper('Mat. ' . $value['matricola'] . ' - ') : '');
        }

        return $pagesData;
    }

    public function as_view() {
        $svgPrint = new \Core\PrintSvg("applications/ccp/templates/{$this->template}", $this->data);
        $create = $svgPrint->create($this->_path);
        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($name) {
        $data = json_decode($name['printSettings']);
        $name = explode("_", $data->template);
        if (count($name) == 3) {
            $name[2] = "con sfondo";
        }
        return "Bollettino {$name[0]} {$name[1]} {$name[2]}";
    }

}
