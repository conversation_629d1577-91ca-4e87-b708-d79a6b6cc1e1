<?php

namespace Ccp;

/**
 * Print Standard List base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFReceipt {

    const spool = true;

    private $_print_id;
    private $_path;
    public $tax;

    public function __construct($request) {
        $this->tax = $request;
        $this->tax['causale'] = '';

        $db = \Db::getInstance();
        $r = $db->query("SELECT descrizione FROM tipi_tasse WHERE id_tipo_tassa = {$this->tax['id_tipo_tassa']}");
        if ($r) {
            $r = $db->fetchAll();
            if ($r) {
                $this->tax['causale'] = $r[0]['descrizione'];
            }
        }

        if ($this->tax['classe']) {
            $this->tax['classe'] = "({$this->tax['classe']})";
        }

        $d = explode("-", $this->tax['data_estratto_conto']);
        $d = "{$d[2]}/{$d[1]}/{$d[0]}";
        $this->tax['data_estratto_conto'] = $d;

        $d = explode("-", $this->tax['data_versamento']);
        $d = "{$d[2]}/{$d[1]}/{$d[0]}";
        $this->tax['data_versamento'] = $d;

        $this->_print_id = $request['id'];
        $this->_path = $request['path'];
    }

    public function printHeader($p) {
        $year = date("y");
        $date = date("d/m/Y - H:i");
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->Cell(0, 5, "Ricevuta numero _____/{$year}", 0, 1, 'R', false, '', 1);
        $p->Cell(0, 5, "Prodotta il {$date}", 0, 1, 'R', false, '', 1);
        $p->Ln();
        $p->Ln();
        $p->SetFontSize(12);
        $p->Cell(100, 5, "Ricevuta Movimento numero {$this->tax['numero_versamento']} del {$this->tax['data_estratto_conto']}", 0, 1, 'L', false, '', 1);
        $p->setFont('', '');
    }

    public function printTax($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        // Header
        $p->Cell(40, 5, "Nominativo", 0, 0, 'L', false, '', 1);
        $p->Cell(50, 5, "Causale", 0, 0, 'L', false, '', 1);
        $p->Cell(20, 5, "Versamento", 0, 0, 'L', false, '', 1);
        $p->Cell(20, 5, "A.S. Rif.", 0, 0, 'L', false, '', 1);
        $p->Cell(40, 5, "Note", 0, 0, 'L', false, '', 1);
        $p->Cell(0, 5, "Importo (€)", 0, 1, 'L', false, '', 1);
        // Tax
        $p->setFont('', '');
        $p->Ln();
        $p->Cell(40, 5, "{$this->tax['dati_debitore']} {$this->tax['classe']}", 0, 0, 'L', false, '', 1);
        $p->Cell(50, 5, "{$this->tax['causale']}", 0, 0, 'L', false, '', 1);
        $p->Cell(20, 5, "{$this->tax['data_versamento']}", 0, 0, 'L', false, '', 1);
        $p->Cell(20, 5, "{$this->tax['anno_scolastico']}", 0, 0, 'L', false, '', 1);
        $p->Cell(40, 5, "{$this->tax['note']}", 0, 0, 'L', false, '', 1);
        $p->setFont('', 'B');
        $p->Cell(0, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $this->tax['importo_versato']), 0, 1, 'R', false, '', 1);
        $p->setFont('', '');
    }

    public function printFooter($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->SetY($p->getY() + 40);
        $p->setX(150);
        $p->Cell(30, 7, 'La Segreteria', 0, 0, 'C');
        $p->setFont('', '');
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->SetMargins(10, 30);
        $p->SetFillColor(250, 250, 250);

        $p->addPage('P');
        $this->printHeader($p);
        $p->Ln();
        $p->Ln();
        $p->Ln();
        $p->Ln();
        $p->Ln();
        $this->printTax($p);
        $this->printFooter($p);

        $create = $p->createPdf();
        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        $date = explode("-", $data['data_estratto_conto']);
        $date = "{$date[2]}-{$date[1]}-{$date[0]}";
        return "Ricevuta Movimento - n. {$data['numero_versamento']} del {$date}";
    }

}
