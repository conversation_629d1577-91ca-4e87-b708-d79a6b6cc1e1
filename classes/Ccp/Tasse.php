<?php

namespace Ccp;

use Ccp\om\BaseTasse;

/**
 * Skeleton subclass for representing a row from the 'tasse' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Ccp
 */
class Tasse extends BaseTasse {

	/**
	 * Check for validation about tax.
	 * Return true if there are no errors, array of errors otherwise
	 * @param array $columns
	 * @return mix bool/array
	 */
	public function doValidate($columns = null) {
		$errors = parent::doValidate($columns);
		if ($errors === true) {
			$errors = array();
		}

		// Post uffice check
		if ($this->getIsIncoming() && !$this->getSedeUfficioPostale()
		) {
			$err = new \ValidationFailed(TassePeer::SEDE_UFFICIO_POSTALE, _('Post office is mandatory'));
			$errors = array_merge($errors, array($err));
		}

		// Student check.
		// If is incoming and no debitor are selected =>  error
		if ($this->getIsIncoming() && !$this->getIdStudente() && !$this->getDatiDebitore() && !$this->getEmployeeId()
		) {
			$err = new \ValidationFailed(TassePeer::ID_STUDENTE, _('Subject is mandatory'));
			$errors = array_merge($errors, array($err));
		}

		// If no tax type passed
		if ($this->getIsIncoming() && $this->getIdTipoTassa() < 0
		) {
			$err = new \ValidationFailed(TassePeer::ID_TIPO_TASSA, _('Tax type is mandatory'));
			$errors = array_merge($errors, array($err));
		}

		// Check if select one school year
		if (!$this->getAnnoScolastico()) {
			$err = new \ValidationFailed(TassePeer::ANNO_SCOLASTICO, _('School year is mandatory'));
			$errors = array_merge($errors, array($err));
		} else {
			// Check if data estratto conto if into corrent school year
			$years = explode('/', $this->getAnnoScolastico());
			$limitDateStart = strtotime($years[0] . '-09-01');
			$limitDateEnd = strtotime($years[1] . '-08-31');
			$dateEstrattoConto = $this->getDataEstrattoConto();
			if ($limitDateStart >= $dateEstrattoConto || $limitDateEnd <= $dateEstrattoConto) {
				$err = new \ValidationFailed(TassePeer::DATA_ESTRATTO_CONTO, _('Data estratto conto not in school year range'));
				$errors = array_merge($errors, array($err));
			}
		}
		return empty($errors) ? true : $errors;
	}

}
