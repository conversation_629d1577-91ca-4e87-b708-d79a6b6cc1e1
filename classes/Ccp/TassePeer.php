<?php

namespace Ccp;

use Ccp\om\BaseTassePeer;

/**
 * Skeleton subclass for performing query and update operations on the 'tasse' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Ccp
 */
class TassePeer extends BaseTassePeer {

	/**
	 * Return school start and end year, based on current data or passed data if exists
	 * Ex. 'now' = 20/10/2012 => array('start' => 2012, 'end' => 2013)
	 * @param date $date To check the school year for that
	 * @return array
	 */
	public function getSchoolYear($date = null) {
		// get current data and current year
		if ($date == null) {
			$date = strtotime('now');
			$year = date('Y');
		} else {
			$year = date('Y', $date);
		}

		// Get limit data to change school year
		$limit = strtotime($year . '-08-31');

		// If current data is over 31 August of this year, you
		// are in a next school year
		if ($date > $limit) {
			return array(
				'start'	 => $year,
				'end'	 => $year + 1
			);
		} else {
			return array(
				'start'	 => $year - 1,
				'end'	 => $year
			);
		}
	}

	/**
	 * Get mastercom database of current date.
	 * If schoolYear is passed must have form => 2012/2013 and it returns
	 * mastercom_2012_2013
	 * @return string ex. mastercom_2012_2013
	 */
	public function getMastercomDbName($schoolYear = null) {
		if ($schoolYear == null) {
			if(getenv('MASTERCOM_CURRENT_DB')) {
				$dbName = getenv('MASTERCOM_CURRENT_DB');
			} else {
				$dbName = trim(file_get_contents(PATH_MC_DB));
			}
		} else {
			$dbName = 'mastercom_' . str_replace('/', '_', $schoolYear);
			try {
				\Propel::getConnection($dbName);
			} catch (Exception $e) {
				$dbName = $this->getMastercomDbName();
			}
		}
		return $dbName;
	}

}
