<?php

namespace Ccp;

/**
 * Print Standard List base by interface filter
 *
 * @package    mc2api.Ccp
 * @copyright  Copyright (c) 2013-2013 MasterTraining S.r.l.
 * <AUTHOR>
 */
class PrintPDFByType {

    const spool = true;

    private $_print_id;
    private $_path;
    public $taxes;

    public function __construct($request) {
        $filter = $request;
        $this->dateStart = strtotime($filter['data_estratto_conto_start']);
        $this->dateEnd = strtotime($filter['data_estratto_conto_end']);
        $this->as = explode('/', $filter['SchoolYear']);

        if (!$this->dateStart) {
            $this->dateStart = strtotime($this->as[0] . "-01-01");
        } else {
            $this->as[0] = date('Y', $this->dateStart);
        }
        if (!$this->dateEnd) {
            $this->dateEnd = strtotime($this->as[0] . "-12-31");
        }

        $tasseQ = new \Ccp\TasseQuery();
        $sorting = array(
            array(
                'property'  => 'tipologia',
                'direction' => 'ASC'
            ),
            array(
                'property'  => 'descrizione',
                'direction' => 'ASC'
            )
        );
        $filter['sort'] = json_encode($sorting);
        $this->taxes = $tasseQ->read($filter);
        $this->_print_id = $request['id'];
        $this->_path = $request['path'];
    }

    public function printHeader($p) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->Cell(100, 5, 'Riepilogo per tipo tassa dal ' . date('d/m/Y', $this->dateStart) . ' al ' . date('d/m/Y', $this->dateEnd), 0, 1, 'L', false, '', 1);
        $p->Ln();
        $p->Ln();
        $p->setFont('', '');
    }

    public function printKindHeader($p, $kind) {
        $p->setFont('', 'B');
        $p->SetFontSize(10);
        $p->SetFillColor(210, 210, 210);
        $p->Cell(0, 5, "Tipologia: {$kind}", 0, 1, 'C', false, '', 1);
        $p->SetFontSize(8);
        $p->Cell(80, 5, 'Tipo', 1, 0, 'C', true, "", 1);
        $p->Cell(15, 5, 'Movimenti', 1, 0, 'C', true);
        //$p->Cell(22, 5, 'Imp. Base (€)', 1, 0, 'C', true);
        $p->Cell(30, 5, 'Importo Totale (€)', 1, 0, 'C', true);
        $p->Cell(20, 5, 'A.S. rif.', 1, 1, 'C', true);
        $p->setFont('', '');
    }

    public function printType($p, $type = array()) {
        $p->SetFontSize(8);
        $p->Cell(80, 5, $type['descr'], 1, 0, 'L', true);
        $p->Cell(15, 5, $type['count'], 1, 0, 'C', true);
        //$p->Cell(22, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $type['importo_base']), 1, 0, 'R', true);
        $p->Cell(30, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $type['total']), 1, 0, 'R', true);
        $p->Cell(20, 5, $type['as'], 1, 1, 'C', true);
        $p->setFont('', '');
    }

    public function printTotal($p, $total) {
        $p->setFont('', 'B');
        $p->SetFontSize(12);
        $p->SetY($p->getY() + 10);
        $p->setX(80);
        $p->Cell(30, 7, 'Movimenti', 1, 0, 'C');
        $p->setX(110);
        $p->Cell(30, 7, 'Saldo (€)', 1, 0, 'C');
        $p->SetY($p->getY() + 7);
        $p->setX(50);
        $p->Cell(30, 7, 'Totale', 1, 0, 'L');
        $p->setX(80);
        $p->Cell(30, 7, $total['count'], 1, 0, 'C');
        $p->setX(110);
        $p->Cell(30, 7, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $total['total']), 1, 1, 'R');
        $p->setFont('', '');
    }

    public function printKindTotal($p, $total) {
        $p->setFont('', 'B');
        $p->SetFontSize(8);
        $p->SetFillColor(210, 210, 210);
        $p->Cell(80, 5, "TOTALE", 1, 0, 'R', true);
        $p->Cell(15, 5, $total['count'], 1, 0, 'C', true);
        //$p->Cell(22, 5, "", 1, 0, 'C', true);
        $p->Cell(30, 5, money_format(MONEY_NO_SIGN_FORMAT_PRINT, $total['total']), 1, 0, 'R', true);
        $p->Cell(20, 5, "", 1, 1, 'C', true);
        $p->setFont('', '');
    }

    public function as_view() {
        $p = new \PrintCore();
        $p->SetTitle($this->_path);
        $p->SetMargins(20, 30);

        $list = array();

        // Calculates totals by type
        foreach ($this->taxes as $tax) {
            if ($tax['is_incoming'] === false) {
                $tax['importo_versato'] *= -1;
            }
            $list['count'] ++;
            $list['total'] += $tax['importo_versato'];
            $list['kinds'][$tax['tipologia']]['name'] = $tax['tipologia'] ? $tax['tipologia'] : " Non definita";
            $list['kinds'][$tax['tipologia']]['count'] ++;
            $list['kinds'][$tax['tipologia']]['total'] += $tax['importo_versato'];
            $list['kinds'][$tax['tipologia']]['type'][$tax['id_tipo_tassa']]['count'] ++;
            $list['kinds'][$tax['tipologia']]['type'][$tax['id_tipo_tassa']]['descr'] = $tax['descrizione'] ? $tax['descrizione'] : "Non definito";
            $list['kinds'][$tax['tipologia']]['type'][$tax['id_tipo_tassa']]['as'] = $tax['anno_scolastico_riferimento'];
            $list['kinds'][$tax['tipologia']]['type'][$tax['id_tipo_tassa']]['importo_base'] = $tax['importo_base'];
            $list['kinds'][$tax['tipologia']]['type'][$tax['id_tipo_tassa']]['total'] += $tax['importo_versato'];
        }

        $first = true;
        foreach ($list['kinds'] as $k) {
            $p->addPage('P');
            if ($first) {
                $this->printHeader($p);
                $first = false;
            }
            $this->printKindHeader($p, $k['name']);

            $oddStyle = false;
            foreach ($k['type'] as $t) {
                // Change style row by row
                if ($oddStyle) {
                    $p->SetFillColor(235, 235, 235);
                    $oddStyle = false;
                } else {
                    $p->SetFillColor(250, 250, 250);
                    $oddStyle = true;
                }
                $this->printType($p, $t);
            }

            $this->printKindTotal($p, $k);
        }

        $this->printTotal($p, $list);

        $create = $p->createPdf();

        if ($create) {
            \Core\PrintSpoolQuery::create()->setStatus($this->_print_id);
        }
    }

    static function getName($data) {
        $dStart = explode("-", $data['data_estratto_conto_start']);
        $dStart = "{$dStart[2]}-{$dStart[1]}-{$dStart[0]}";
        $dEnd = explode("-", $data['data_estratto_conto_end']);
        $dEnd = "{$dEnd[2]}-{$dEnd[1]}-{$dEnd[0]}";

        return "Riepilogo Movimenti per tipo tassa - {$dStart} / {$dEnd}";
    }

}
