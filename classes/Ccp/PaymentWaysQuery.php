<?php

namespace Ccp;

use Ccp\om\BasePaymentWaysQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'payment_ways' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Ccp
 */
class PaymentWaysQuery extends BasePaymentWaysQuery {

    public function read() {
        return PaymentWaysQuery::create()->find()->toArray(null, false, \BasePeer::TYPE_FIELDNAME);
    }

}
