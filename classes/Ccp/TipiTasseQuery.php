<?php

namespace Ccp;

use Ccp\om\BaseTipiTasseQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'tipi_tasse' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Ccp
 */
class TipiTasseQuery extends BaseTipiTasseQuery {

    public function __construct($dbName = 'mc2api', $modelName = null, $modelAlias = null) {
        parent::__construct($dbName, $modelName, $modelAlias);
    }

}
