<?php

namespace Employee;

use Employee\om\BaseTimetable;

/**
 * Skeleton subclass for representing a row from the 'personnel_timetable' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class Timetable extends BaseTimetable {

	/**
	 * Calculates the duration of the whole timetable.
	 *
	 * @return integer Timetable duration, in seconds
	 */
	public function timetableDuration() {
		return ($this->getDateEnd() - $this->getDateStart()) / 60;
	}

	/**
	 * Calculates the duration of the pause of a timetable.
	 *
	 * @return integer Pause duration, in seconds
	 */
	public function pauseDuration() {
		$result = 0;
		if ( $this->getDateEndPause() &&  $this->getDateStartPause()) {
			$result = ((int) $this->getDateEndPause() - (int) $this->getDateStartPause()) / 60;
		}
		return $result;
	}

	/**
	 * Calculates the duration of the work time of a timetable.
	 *
	 * @return integer Work duration, in seconds
	 */
	public function workDuration() {
		return $this->timetableDuration() - $this->pauseDuration();
	}

	/**
	 * Calculates the duration of the two sections of a timetable.
	 *
	 * @return array Sections durations, in seconds
	 */
	public function sectionsDuration() {
		if (!$this->getDateStartPause() || !$this->getDateEndPause()) {
			$section1 = $this->getDateEnd() - $this->getDateStart();
			$section2 = 0;
		} else {
			$section1 = $this->getDateStartPause() - $this->getDateStart();
			$section2 = $this->getDateEnd() - $this->getDateEndPause();
		}
		return array($section1 / 60, $section2 / 60);
	}

	/**
	 * Returns the timetable as a string.
	 *
	 * @return string the timetable in a human readable form
	 */
	public function toString() {
		$pauseStr = " - ";
		if ($this->getDateStartPause() || $this->getDateEndPause()) {
			$pauseStr = " (";
			$pauseStr .=!$this->getDateStartPause() ? "--:--" : date("H:i", $this->getDateStartPause());
			$pauseStr .= " - ";
			$pauseStr .=!$this->getDateEndPause() ? "--:--" : date("H:i", $this->getDateEndPause());
			$pauseStr .= ") ";
		}
		return "[" . date("H:i", $this->getDateStart()) . $pauseStr . date("H:i", $this->getDateEnd()) . "]";
	}

}
