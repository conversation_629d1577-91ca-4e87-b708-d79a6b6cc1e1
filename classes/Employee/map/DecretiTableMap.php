<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'decreti' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class DecretiTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.DecretiTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('decreti');
        $this->setPhpName('Decreti');
        $this->setClassname('Employee\\Decreti');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('decreti_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('absence_kind', 'AbsenceKind', 'VARCHAR', true, 10, null);
        $this->addColumn('employee_kind', 'EmployeeKind', 'VARCHAR', true, 10, null);
        $this->addColumn('employee_role', 'EmployeeRole', 'VARCHAR', true, 10, null);
        $this->addColumn('html', 'Html', 'LONGVARCHAR', true, null, null);
        $this->addColumn('time_back', 'TimeBack', 'VARCHAR', true, 50, null);
        $this->addColumn('prev_abs_kind', 'PrevAbsKind', 'VARCHAR', false, 150, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // DecretiTableMap
