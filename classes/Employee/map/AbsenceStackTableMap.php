<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'absence_stack' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class AbsenceStackTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.AbsenceStackTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('absence_stack');
        $this->setPhpName('AbsenceStack');
        $this->setClassname('Employee\\AbsenceStack');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('absence_stack_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('unit', 'Unit', 'VARCHAR', true, 1, 'h');
        $this->addColumn('denomination', 'Denomination', 'VARCHAR', true, 255, null);
        $this->addColumn('recover', 'Recover', 'BOOLEAN', true, null, false);
        $this->addColumn('reset_type', 'ResetType', 'SMALLINT', true, null, 0);
        $this->addColumn('reset_to_stack_id', 'ResetToStackId', 'BIGINT', false, null, null);
        $this->addColumn('reset_date', 'ResetDate', 'TIMESTAMP', false, null, null);
        $this->addColumn('reset_default_quota', 'ResetDefaultQuota', 'FLOAT', true, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AbsenceKind', 'Employee\\AbsenceKind', RelationMap::ONE_TO_MANY, array('id' => 'absence_stack', ), 'SET NULL', null, 'AbsenceKinds');
        $this->addRelation('PersonnelStacks', 'Employee\\PersonnelStacks', RelationMap::ONE_TO_MANY, array('id' => 'stack_id', ), 'CASCADE', null, 'PersonnelStackss');
        $this->addRelation('StoredStack', 'Employee\\StoredStack', RelationMap::ONE_TO_MANY, array('id' => 'absence_stack', ), 'SET NULL', null, 'StoredStacks');
    } // buildRelations()

} // AbsenceStackTableMap
