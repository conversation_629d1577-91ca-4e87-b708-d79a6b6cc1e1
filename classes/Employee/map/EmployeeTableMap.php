<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'employee' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class EmployeeTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.EmployeeTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('employee');
        $this->setPhpName('Employee');
        $this->setClassname('Employee\\Employee');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('employee_id_seq');
        // columns
        $this->addPrimaryKey('employee_id', 'EmployeeId', 'INTEGER', true, null, null);
        $this->addColumn('name', 'Name', 'VARCHAR', true, 100, null);
        $this->addColumn('surname', 'Surname', 'VARCHAR', true, 50, null);
        $this->addColumn('gender', 'Gender', 'VARCHAR', false, 10, null);
        $this->addColumn('birthdate', 'Birthdate', 'BIGINT', false, null, null);
        $this->addColumn('fiscal_code', 'FiscalCode', 'VARCHAR', false, 30, null);
        $this->addForeignKey('residence_id', 'ResidenceId', 'INTEGER', 'contact', 'contact_id', false, null, null);
        $this->addForeignKey('address_id', 'AddressId', 'INTEGER', 'contact', 'contact_id', false, null, null);
        $this->addColumn('part_spesa', 'PartSpesa', 'VARCHAR', false, 16, null);
        $this->addColumn('bank', 'Bank', 'INTEGER', false, null, 0);
        $this->addColumn('liq_office', 'LiqOffice', 'VARCHAR', false, 4, null);
        $this->addColumn('inps', 'Inps', 'VARCHAR', false, 17, null);
        $this->addColumn('insur_qual', 'InsurQual', 'CHAR', false, 1, null);
        $this->addColumn('fore', 'Fore', 'BOOLEAN', false, null, null);
        $this->addColumn('asl', 'Asl', 'VARCHAR', false, 10, null);
        $this->addColumn('adm_code', 'AdmCode', 'CHAR', false, 16, null);
        $this->addColumn('way_pay', 'WayPay', 'CHAR', false, 2, null);
        $this->addColumn('liquid_group', 'LiquidGroup', 'VARCHAR', false, 4, null);
        $this->addColumn('contr_code', 'ContrCode', 'VARCHAR', false, 2, null);
        $this->addColumn('contr_type', 'ContrType', 'VARCHAR', false, 5, null);
        $this->addColumn('contr_cat', 'ContrCat', 'SMALLINT', false, null, null);
        $this->addColumn('ssp_frm_pmnt', 'SspFrmPmnt', 'SMALLINT', false, null, null);
        $this->addColumn('personal_data', 'PersonalData', 'SMALLINT', false, null, 0);
        $this->addColumn('susp', 'Susp', 'BOOLEAN', false, null, null);
        $this->addColumn('payment_group', 'PaymentGroup', 'INTEGER', false, null, null);
        $this->addColumn('priv_ret_type', 'PrivRetType', 'VARCHAR', false, 5, null);
        $this->addColumn('social_position', 'SocialPosition', 'SMALLINT', false, null, null);
        $this->addColumn('active', 'Active', 'BOOLEAN', false, null, true);
        $this->addColumn('statal_code', 'StatalCode', 'VARCHAR', false, 16, null);
        $this->addColumn('fiscal_city_code', 'FiscalCityCode', 'VARCHAR', false, 16, null);
        $this->addColumn('birthplace', 'Birthplace', 'LONGVARCHAR', false, null, null);
        $this->addColumn('income', 'Income', 'BIGINT', false, null, 0);
        $this->addColumn('state_birth', 'StateBirth', 'VARCHAR', false, 5, null);
        $this->addColumn('citizenship', 'Citizenship', 'VARCHAR', false, 5, null);
        $this->addColumn('id_sissi', 'IdSissi', 'VARCHAR', false, 5, null);
        $this->addColumn('dom_first_prev_year', 'DomFirstPrevYear', 'INTEGER', false, null, null);
        $this->addColumn('dom_last_prev_year', 'DomLastPrevYear', 'INTEGER', false, null, null);
        $this->addColumn('dom_first_curr_year', 'DomFirstCurrYear', 'INTEGER', true, null, null);
        $this->addColumn('qualification', 'Qualification', 'LONGVARCHAR', false, null, null);
        $this->addColumn('liquid_office_id', 'LiquidOfficeId', 'INTEGER', true, null, 0);
        $this->addColumn('badge_number', 'BadgeNumber', 'BIGINT', false, null, null);
        $this->addColumn('tolerance_in', 'ToleranceIn', 'INTEGER', true, null, 0);
        $this->addColumn('tolerance_out', 'ToleranceOut', 'INTEGER', true, null, 0);
        $this->addColumn('flexibility', 'Flexibility', 'INTEGER', true, null, 0);
        $this->addColumn('generic_tolerance', 'GenericTolerance', 'INTEGER', true, null, 0);
        $this->addColumn('negative_round', 'NegativeRound', 'INTEGER', true, null, 0);
        $this->addColumn('recover_hours', 'RecoverHours', 'INTEGER', true, null, 100);
        $this->addColumn('max_extraordinary_in', 'MaxExtraordinaryIn', 'INTEGER', true, null, 999);
        $this->addColumn('max_extraordinary_out', 'MaxExtraordinaryOut', 'INTEGER', true, null, 999);
        $this->addColumn('min_extraordinary_in', 'MinExtraordinaryIn', 'INTEGER', true, null, 0);
        $this->addColumn('min_extraordinary_out', 'MinExtraordinaryOut', 'INTEGER', true, null, 0);
        $this->addColumn('step_out', 'StepOut', 'INTEGER', true, null, 0);
        $this->addColumn('step_in', 'StepIn', 'INTEGER', true, null, 0);
        $this->addColumn('max_break', 'MaxBreak', 'INTEGER', true, null, 0);
        $this->addColumn('max_cont_work', 'MaxContWork', 'INTEGER', true, null, 720);
        $this->addColumn('simplified_ata_settings', 'SimplifiedAtaSettings', 'BOOLEAN', true, null, false);
        $this->addColumn('tolerance_in_und', 'ToleranceInUnd', 'INTEGER', true, null, 0);
        $this->addColumn('tolerance_out_und', 'ToleranceOutUnd', 'INTEGER', true, null, 0);
        $this->addColumn('max_undefined_in', 'MaxUndefinedIn', 'INTEGER', true, null, 999);
        $this->addColumn('max_undefined_out', 'MaxUndefinedOut', 'INTEGER', true, null, 999);
        $this->addColumn('min_undefined_in', 'MinUndefinedIn', 'INTEGER', true, null, 0);
        $this->addColumn('min_undefined_out', 'MinUndefinedOut', 'INTEGER', true, null, 0);
        $this->addColumn('step_out_und', 'StepOutUnd', 'INTEGER', true, null, 0);
        $this->addColumn('step_in_und', 'StepInUnd', 'INTEGER', true, null, 0);
        $this->addColumn('undefined_parameter_active', 'UndefinedParameterActive', 'BOOLEAN', true, null, false);
        $this->addColumn('min_extraordinary_total', 'MinExtraordinaryTotal', 'INTEGER', true, null, 0);
        $this->addColumn('max_extraordinary_total', 'MaxExtraordinaryTotal', 'INTEGER', true, null, 999);
        $this->addColumn('min_undefined_total', 'MinUndefinedTotal', 'INTEGER', true, null, 0);
        $this->addColumn('max_undefined_total', 'MaxUndefinedTotal', 'INTEGER', true, null, 999);
        $this->addColumn('step_total_undefined', 'StepTotalUndefined', 'INTEGER', true, null, 0);
        $this->addColumn('step_total_extraordinary', 'StepTotalExtraordinary', 'INTEGER', true, null, 0);
        $this->addColumn('lunch_duration', 'LunchDuration', 'INTEGER', true, null, 0);
        $this->addColumn('lunch_deductible', 'LunchDeductible', 'BOOLEAN', true, null, false);
        $this->addColumn('service_deductible', 'ServiceDeductible', 'BOOLEAN', true, null, false);
        $this->addColumn('min_undefined_lunch', 'MinUndefinedLunch', 'INTEGER', true, null, 0);
        $this->addColumn('min_extraordinary_lunch', 'MinExtraordinaryLunch', 'INTEGER', true, null, 0);
        $this->addColumn('max_undefined_lunch', 'MaxUndefinedLunch', 'INTEGER', true, null, 0);
        $this->addColumn('max_extraordinary_lunch', 'MaxExtraordinaryLunch', 'INTEGER', true, null, 0);
        $this->addColumn('step_lunch_undefined', 'StepLunchUndefined', 'INTEGER', true, null, 0);
        $this->addColumn('step_lunch_extraordinary', 'StepLunchExtraordinary', 'INTEGER', true, null, 0);
        $this->addColumn('break_after_max_work', 'BreakAfterMaxWork', 'INTEGER', true, null, 0);
        $this->addColumn('unit_recover_hours', 'UnitRecoverHours', 'VARCHAR', true, 3, '%');
        $this->addColumn('max_work', 'MaxWork', 'INTEGER', true, null, 999);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('ResidenceKey', 'Core\\Contact', RelationMap::MANY_TO_ONE, array('residence_id' => 'contact_id', ), 'CASCADE', null);
        $this->addRelation('AddressKey', 'Core\\Contact', RelationMap::MANY_TO_ONE, array('address_id' => 'contact_id', ), 'CASCADE', null);
        $this->addRelation('Institute', 'Core\\Institute', RelationMap::ONE_TO_MANY, array('employee_id' => 'dir_emp_id', ), 'SET NULL', null, 'Institutes');
        $this->addRelation('Absences', 'Employee\\Absences', RelationMap::ONE_TO_MANY, array('employee_id' => 'employee_id', ), 'CASCADE', null, 'Absencess');
        $this->addRelation('Timetable', 'Employee\\Timetable', RelationMap::ONE_TO_MANY, array('employee_id' => 'employee_id', ), 'CASCADE', null, 'Timetables');
        $this->addRelation('Presence', 'Employee\\Presence', RelationMap::ONE_TO_MANY, array('employee_id' => 'employee_id', ), 'CASCADE', null, 'Presences');
        $this->addRelation('PersonnelStacks', 'Employee\\PersonnelStacks', RelationMap::ONE_TO_MANY, array('employee_id' => 'employee_id', ), 'CASCADE', null, 'PersonnelStackss');
        $this->addRelation('StoredMonth', 'Employee\\StoredMonth', RelationMap::ONE_TO_MANY, array('employee_id' => 'employee_id', ), 'CASCADE', null, 'StoredMonths');
        $this->addRelation('StoredDay', 'Employee\\StoredDay', RelationMap::ONE_TO_MANY, array('employee_id' => 'employee_id', ), 'CASCADE', null, 'StoredDays');
    } // buildRelations()

} // EmployeeTableMap
