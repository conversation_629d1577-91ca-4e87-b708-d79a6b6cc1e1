<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'storage_personnel_presences' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class StoredMonthTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.StoredMonthTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('storage_personnel_presences');
        $this->setPhpName('StoredMonth');
        $this->setClassname('Employee\\StoredMonth');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('storage_personnel_presences_storage_personnel_presences_id_seq');
        // columns
        $this->addPrimaryKey('storage_personnel_presences_id', 'StoredMonthId', 'INTEGER', true, null, null);
        $this->addForeignKey('employee_id', 'EmployeeId', 'INTEGER', 'employee', 'employee_id', true, null, 0);
        $this->addColumn('date_start', 'DateStart', 'BIGINT', true, null, 0);
        $this->addColumn('date_end', 'DateEnd', 'BIGINT', true, null, 0);
        $this->addColumn('ext_start_o', 'ExtStartOriginal', 'INTEGER', true, null, 0);
        $this->addColumn('ext_end_o', 'ExtEndOriginal', 'INTEGER', true, null, 0);
        $this->addColumn('ext_start', 'ExtStart', 'INTEGER', true, null, 0);
        $this->addColumn('ext_end', 'ExtEnd', 'INTEGER', true, null, 0);
        $this->addColumn('note', 'Note', 'LONGVARCHAR', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('StoredMonthEmployee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('employee_id' => 'employee_id', ), 'CASCADE', null);
        $this->addRelation('StoredStack', 'Employee\\StoredStack', RelationMap::ONE_TO_MANY, array('storage_personnel_presences_id' => 'storage_personnel_presences', ), 'CASCADE', null, 'StoredStacks');
    } // buildRelations()

} // StoredMonthTableMap
