<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'absences' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class AbsencesTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.AbsencesTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('absences');
        $this->setPhpName('Absences');
        $this->setClassname('Employee\\Absences');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('absence_id_seq');
        // columns
        $this->addPrimaryKey('absence_id', 'AbsenceId', 'BIGINT', true, null, null);
        $this->addColumn('start_date', 'StartDate', 'BIGINT', false, null, null);
        $this->addColumn('end_date', 'EndDate', 'BIGINT', false, null, null);
        $this->addForeignKey('ab_kind', 'AbKind', 'VARCHAR', 'absence_kind', 'code', false, 10, null);
        $this->addColumn('total_days', 'TotalDays', 'INTEGER', false, null, null);
        $this->addForeignKey('employee_id', 'EmployeeId', 'INTEGER', 'employee', 'employee_id', false, null, null);
        $this->addColumn('date_of_req', 'DateOfReq', 'BIGINT', false, null, null);
        $this->addColumn('protocol_id', 'ProtocolId', 'BIGINT', false, null, null);
        $this->addColumn('type_of_abs', 'TypeOfAbs', 'SMALLINT', false, null, null);
        $this->addColumn('decreto', 'Decreto', 'INTEGER', false, null, null);
        $this->addColumn('note', 'Note', 'LONGVARCHAR', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AbsencesAbsenceKind', 'Employee\\AbsenceKind', RelationMap::MANY_TO_ONE, array('ab_kind' => 'code', ), 'SET NULL', null);
        $this->addRelation('AbsenceEmployee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('employee_id' => 'employee_id', ), 'CASCADE', null);
    } // buildRelations()

} // AbsencesTableMap
