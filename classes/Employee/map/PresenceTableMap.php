<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'personnel_presences' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class PresenceTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.PresenceTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('personnel_presences');
        $this->setPhpName('Presence');
        $this->setClassname('Employee\\Presence');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('personnel_presences_personnel_presence_id_seq');
        // columns
        $this->addPrimaryKey('personnel_presence_id', 'PersonnelPresenceId', 'INTEGER', true, null, null);
        $this->addForeignKey('employee_id', 'EmployeeId', 'BIGINT', 'employee', 'employee_id', true, null, -1);
        $this->addColumn('project_id', 'ProjectId', 'BIGINT', false, null, null);
        $this->addForeignKey('project_edit_id', 'ProjectIdEdit', 'BIGINT', 'bdg_activities', 'activ_id', false, null, null);
        $this->addColumn('date', 'Date', 'BIGINT', false, null, null);
        $this->addColumn('date_edit', 'DateEdit', 'BIGINT', false, null, null);
        $this->addColumn('type', 'Type', 'INTEGER', true, null, 1);
        $this->addColumn('type_edit', 'TypeEdit', 'INTEGER', true, null, 1);
        $this->addColumn('original_inout', 'OriginalInOut', 'INTEGER', true, null, -1);
        $this->addColumn('original_inout_edit', 'OriginalInOutEdit', 'INTEGER', true, null, 1);
        $this->addColumn('description', 'Description', 'VARCHAR', true, null, '');
        $this->addColumn('hour_type_id', 'HourTypeId', 'BIGINT', true, null, -1);
        $this->addColumn('hour_type_edit_id', 'HourTypeIdEdit', 'BIGINT', true, null, -1);
        $this->addColumn('insertion_mode', 'InsertionMode', 'VARCHAR', true, 1, 'T');
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('PresenceEmployee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('employee_id' => 'employee_id', ), 'CASCADE', null);
        $this->addRelation('PresenceProject', 'Budget\\Activity', RelationMap::MANY_TO_ONE, array('project_edit_id' => 'activ_id', ), 'SET NULL', null);
    } // buildRelations()

} // PresenceTableMap
