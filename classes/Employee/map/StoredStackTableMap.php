<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'storage_personnel_stack' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class StoredStackTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.StoredStackTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('storage_personnel_stack');
        $this->setPhpName('StoredStack');
        $this->setClassname('Employee\\StoredStack');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('storage_personnel_stack_id_seq');
        // columns
        $this->addPrimaryKey('id', 'StoredStackId', 'INTEGER', true, null, null);
        $this->addForeignKey('storage_personnel_presences', 'StoredMonth', 'BIGINT', 'storage_personnel_presences', 'storage_personnel_presences_id', false, null, null);
        $this->addForeignKey('absence_stack', 'AbsenceStack', 'BIGINT', 'absence_stack', 'id', false, null, null);
        $this->addColumn('stack_denomination', 'StackDenomination', 'VARCHAR', true, 255, null);
        $this->addColumn('value_start_o', 'ValueStartOriginal', 'DOUBLE', true, null, 0);
        $this->addColumn('value_end_o', 'ValueEndOriginal', 'DOUBLE', true, null, 0);
        $this->addColumn('value_start', 'ValueStart', 'DOUBLE', true, null, 0);
        $this->addColumn('value_end', 'ValueEnd', 'DOUBLE', true, null, 0);
        $this->addColumn('unit', 'Unit', 'VARCHAR', true, 1, 'h');
        $this->addColumn('recover', 'Recover', 'BOOLEAN', true, null, false);
        $this->addColumn('reset_type_applied', 'ResetTypeApplied', 'SMALLINT', true, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('StoredStackAbsenceStack', 'Employee\\AbsenceStack', RelationMap::MANY_TO_ONE, array('absence_stack' => 'id', ), 'SET NULL', null);
        $this->addRelation('StoredStackStoredMonth', 'Employee\\StoredMonth', RelationMap::MANY_TO_ONE, array('storage_personnel_presences' => 'storage_personnel_presences_id', ), 'CASCADE', null);
    } // buildRelations()

} // StoredStackTableMap
