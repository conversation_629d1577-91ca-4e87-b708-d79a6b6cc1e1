<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'extraordinary_stored' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class StoredDayTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.StoredDayTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('extraordinary_stored');
        $this->setPhpName('StoredDay');
        $this->setClassname('Employee\\StoredDay');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('extraordinary_stored_extraordinary_stored_id_seq');
        // columns
        $this->addPrimaryKey('extraordinary_stored_id', 'StoredDayId', 'INTEGER', true, null, null);
        $this->addForeignKey('employee_id', 'EmployeeId', 'BIGINT', 'employee', 'employee_id', true, null, null);
        $this->addColumn('date', 'Date', 'BIGINT', true, null, null);
        $this->addColumn('extraordinary', 'Extraordinary', 'INTEGER', true, null, 0);
        $this->addColumn('authorized', 'Authorized', 'INTEGER', true, null, 0);
        $this->addColumn('note', 'Note', 'LONGVARCHAR', true, null, '');
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('StoredDayEmployee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('employee_id' => 'employee_id', ), 'CASCADE', null);
    } // buildRelations()

} // StoredDayTableMap
