<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'absence_kind' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class AbsenceKindTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.AbsenceKindTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('absence_kind');
        $this->setPhpName('AbsenceKind');
        $this->setClassname('Employee\\AbsenceKind');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(false);
        // columns
        $this->addPrimaryKey('code', 'Code', 'VARCHAR', true, 10, null);
        $this->addColumn('description', 'Description', 'VARCHAR', true, 50, null);
        $this->addForeignKey('absence_stack', 'AbsenceStack', 'BIGINT', 'absence_stack', 'id', false, null, null);
        $this->addColumn('date_start', 'DateStart', 'TIMESTAMP', false, null, null);
        $this->addColumn('date_end', 'DateEnd', 'TIMESTAMP', false, null, null);
        $this->addColumn('calc_festivities', 'CalcFestivities', 'BOOLEAN', true, null, false);
        $this->addColumn('calc_ferials', 'CalcFerials', 'BOOLEAN', true, null, false);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('AbsenceKindAbsenceStack', 'Employee\\AbsenceStack', RelationMap::MANY_TO_ONE, array('absence_stack' => 'id', ), 'SET NULL', null);
        $this->addRelation('Absences', 'Employee\\Absences', RelationMap::ONE_TO_MANY, array('code' => 'ab_kind', ), 'SET NULL', null, 'Absencess');
    } // buildRelations()

} // AbsenceKindTableMap
