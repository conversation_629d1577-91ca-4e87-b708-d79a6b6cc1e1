<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'personnel_stacks' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class PersonnelStacksTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.PersonnelStacksTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('personnel_stacks');
        $this->setPhpName('PersonnelStacks');
        $this->setClassname('Employee\\PersonnelStacks');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('personnel_stacks_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addForeignKey('employee_id', 'EmployeeId', 'BIGINT', 'employee', 'employee_id', true, null, -1);
        $this->addForeignKey('stack_id', 'StackId', 'BIGINT', 'absence_stack', 'id', true, null, -1);
        $this->addColumn('reset_quota', 'ResetQuota', 'FLOAT', true, null, 0);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('PersonnelStacksEmployee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('employee_id' => 'employee_id', ), 'CASCADE', null);
        $this->addRelation('PersonnelStacksAbsenceStack', 'Employee\\AbsenceStack', RelationMap::MANY_TO_ONE, array('stack_id' => 'id', ), 'CASCADE', null);
    } // buildRelations()

} // PersonnelStacksTableMap
