<?php

namespace Employee\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'personnel_timetable' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Employee.map
 */
class TimetableTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Employee.map.TimetableTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('personnel_timetable');
        $this->setPhpName('Timetable');
        $this->setClassname('Employee\\Timetable');
        $this->setPackage('Employee');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('personnel_timetable_personnel_timetable_id_seq');
        // columns
        $this->addPrimaryKey('personnel_timetable_id', 'PersonnelTimetableId', 'INTEGER', true, null, null);
        $this->addForeignKey('employee_id', 'EmployeeId', 'BIGINT', 'employee', 'employee_id', true, null, null);
        $this->addColumn('date_start', 'DateStart', 'BIGINT', true, null, null);
        $this->addColumn('date_end', 'DateEnd', 'BIGINT', true, null, null);
        $this->addColumn('date_start_pause', 'DateStartPause', 'BIGINT', false, null, null);
        $this->addColumn('date_end_pause', 'DateEndPause', 'BIGINT', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('TimetableEmployee', 'Employee\\Employee', RelationMap::MANY_TO_ONE, array('employee_id' => 'employee_id', ), 'CASCADE', null);
    } // buildRelations()

} // TimetableTableMap
