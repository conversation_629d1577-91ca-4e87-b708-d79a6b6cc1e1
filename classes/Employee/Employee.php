<?php

namespace Employee;

use Employee\om\BaseEmployee;

/**
 * Skeleton subclass for representing a row from the 'employee' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class Employee extends BaseEmployee {

	/**
	 * Tells you if employee is a teacher or not
	 * @return boolen
	 */
	public function isTeacher() {
		if ($this->getLiquidGroup() == '0001') {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Tells you if employee is an ATA or not
	 * @return boolen
	 */
	public function isATA() {
		if ($this->getLiquidGroup() == '0002') {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Tells you if employee is a long term personnel
	 * @return boolen
	 */
	public function isLongRole() {
		if ($this->getPaymentGroup() == 14) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 *
	 * @return str/null depends of existing qualification or not
	 */
	public function getQualificationDisplay() {
		$con = \Propel::getConnection();
		$sql = "SELECT description FROM payments_class WHERE class = :class";
		$stmt = $con->prepare($sql);
		$stmt->execute(array(':class' => $this->getQualification()));
		$data = $stmt->fetchAll();
		return $data[0]['description'] ? $data[0]['description'] : null;
	}

	/**
	 *
	 * @return str/null depends of existing city or not
	 */
	public function getBirthplaceDisplay() {
		$con = \Propel::getConnection();
		$sql = "SELECT description FROM cities WHERE city_id = :city_id";
		$stmt = $con->prepare($sql);
		$stmt->execute(array(':city_id' => $this->getBirthplace()));
		$data = $stmt->fetchAll();
		return $data[0]['description'] ? $data[0]['description'] : null;
	}

}
