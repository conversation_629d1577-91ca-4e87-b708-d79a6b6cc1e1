<?php

namespace Employee;

use Employee\om\BaseStoredDayQuery;


/**
 * Skeleton subclass for performing query and update operations on the 'extraordinary_stored' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class StoredDayQuery extends BaseStoredDayQuery
{
}
