<?php

namespace Employee;

use Employee\om\BaseDecreti;

/**
 * Skeleton subclass for representing a row from the 'decreti' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class Decreti extends BaseDecreti {

	/**
	 * Get previous absences based on passed one
	 * @param \Employee\Employee $employee object
	 * @param \Employee\Absences $absence
	 * @return \Employee\Absences array
	 */
	private function getPrevAbsences($employee, $absence) {
		$to = $absence->getStartDate();
		switch ($this->getTimeBack()) {
			case 'corrente anno':
				$from = strtotime("1 January " . date("Y", $absence->getStartDate()));
				break;
			case 'corrente mese':
				$from = strtotime("1 " . date("F", $absence->getStartDate()) . " " . date("Y", $absence->getStartDate()));
				break;
			case 'ultimo triennio':
				$from = strtotime(date("d", $absence->getStartDate()) . " " . date("F", $absence->getStartDate()) . " " . date("Y", $absence->getStartDate()) . ' -3 years');
				break;
			default:
				$from = 0;
				$to = time();
				break;
		}

		$prevAbs = AbsencesQuery::create()
				->filterByStartDate($to, '<')
				->filterByEndDate($from, '>')
				->filterByAbsenceId($absence->getPrimaryKey(), '!=')
				->filterByEmployeeId($employee->getEmployeeId())
				->where(AbsencesPeer::AB_KIND . " IN ('" . str_replace(",", "','", $this->getPrevAbsKind()) . "')")
				->find();
		return $prevAbs;
	}

	/**
	 * Replace keys present in tempalte with relative data, and set into "html" field
	 * @param \Employee\Employee $employee object
	 * @param \Employee\Absences $absence object
	 * @return \Employee\Decreti
	 */
	public function replaceHtml($employee, $absence) {
		// Get original html template
		$html = $this->getHtml();

		$institute = \Core\InstituteQuery::create()->filterByDef(true)
				->joinWith('Contact', \Criteria::LEFT_JOIN)
				->joinWith('Employee', \Criteria::LEFT_JOIN)
				->joinWith('Contact.CityKey', \Criteria::LEFT_JOIN)
				->joinWith('CityKey.RegionKey', \Criteria::LEFT_JOIN)
				->withColumn('CityKey.description', 'city')
				->withColumn('Contact.address', 'address')
				->withColumn('CityKey.province', 'province')
				->withColumn('RegionKey.name', 'region')
				->withColumn('Employee.name', 'dir_name')
				->withColumn('Employee.surname', 'dir_surname')
				->select(
						array(
							'name',
							'city',
							'region',
							'fiscal_code',
							'address',
							'province'
				))
				->findOne();

		$absPrev = $this->getPrevAbsences($employee, $absence);
		$str_abs_prev = '';
		foreach ($absPrev as $abs) {
			if ($abs->getPrimaryKey() == $absence->getPrimaryKey()) {
				continue;
			}
			$str_abs_prev .= 'dal ' . date("d/m/Y", $abs->getStartDate()) . ' al ' . date("d/m/Y", $abs->getEndDate()) .
					' gg. ' . $abs->getTotalDays() . ' per ' . $abs->getAbKind() . '<br />';
		}

		// Build keys values array
		$replaceArray = array(
			'{{ surname }}'				 => $employee->getSurname(),
			'{{ name }}'				 => $employee->getName(),
			'{{ protocol_number }}'		 => $absence->getProtocolNumber(),
			'{{ qualification }}'		 => $employee->getQualificationDisplay(),
			'{{ birthplace_city }}'		 => $employee->getBirthplaceDisplay(),
			'{{ birthdate }}'			 => date("d/m/Y", $employee->getBirthdate()),
			'{{ fiscal_code }}'			 => $employee->getFiscalCode(),
			'{{ prev_absences }}'		 => $str_abs_prev,
			'{{ this_tot_days }}'		 => $absence->getTotalDays(),
			'{{ request_date }}'		 => date("d/m/Y", $absence->getDateOfReq()),
			'{{ this_start_date }}'		 => date("d/m/Y", $absence->getStartDate()),
			'{{ this_end_date }}'		 => date("d/m/Y", $absence->getEndDate()),
			'{{ institute_city }}'		 => $institute['city'],
			'{{ ds }}'					 => $institute['dir_surname'] . ' ' . $institute['dir_name'],
			'{{ institute_name }}'		 => $institute['name'],
			'{{ institute_region }}'	 => strtoupper($institute['region']),
			'{{ institute_cf }}'		 => $institute['fiscal_code'],
			'{{ institute_address }}'	 => $institute['address'],
			'{{ institute_province }}'	 => $institute['province'],
			'{{ today }}'				 => date("d/m/Y")
		);

		// Make replace from keys to values and set on html field
		$this->setHtml(
				str_replace(array_keys($replaceArray), array_values($replaceArray), $html)
		);

		return $this;
	}

}
