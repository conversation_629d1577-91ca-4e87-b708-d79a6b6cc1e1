<?php

namespace Employee;

use Employee\om\BaseAbsences;

/**
 * Skeleton subclass for representing a row from the 'absences' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class Absences extends BaseAbsences {

	/**
	 *
	 * @return integer number of protocol if exists, null otherwise
	 */
	public function getProtocolNumber() {
		$con = \Propel::getConnection();
		$sql = "SELECT protocol_number FROM protocol_protocol WHERE id = :protocolId";
		$stmt = $con->prepare($sql);
		$stmt->execute(array(':protocolId' => $this->getProtocolId()));
		$data = $stmt->fetchAll();
		return $data[0]['prot_num'] ? $data[0]['prot_num'] : null;
	}

	/**
	 * Calculates the absence duration.
	 *
	 * If the absence has a daily stack, it calculates how many days the absence spans on, duration in seconds is not calculated;
	 * if the absence has a hourly stack, it calculates how long the duration is in seconds for a day and how many days the absence spans on;
	 * if the absence hasn't a stack, it calculates how long the duration is by making the raw difference between start date and end date, days-span is not calculated.
	 *
	 * @return array the absence duration, the values are in seconds
	 */
	public function getDuration() {
		$absKind = $this->getAbsencesAbsenceKind();
		$absStack = $absKind->getAbsenceKindAbsenceStack();
		if ($absStack != null) {
			$start = strtotime(date("d-m-Y", $this->getStartDate()));
			$end = strtotime(date("d-m-Y", $this->getEndDate()) . " +1 day");
			if ($absStack->isDaily()) {
				$array['time'] = 0;
				$array['days'] = 0;
				for ($day = $start; $day < $end; $day = $day + 86400) {
					if ($this->isCountableOnDay($day)) {
						$array['days'] ++;
					}
				}
			} else {
				if ($this->isCountableOnDay($start)) {
					$end_sameday = strtotime(date("d-m-Y", $this->start_date) . " +" . date("H", $this->end_date) . "hours +" . date("i", $this->end_date) . " minutes");
					if ($end_sameday < $this->start_date) {
						$end_sameday = strtotime("+1 day", $end_sameday);
						$end = strtotime(date("d-m-Y", $end) . " -1 day");
					}
					$array = array(
						"time"	 => ($end_sameday - $this->start_date) / 60,
						"days"	 => ($end - $start) / 86400
					);
				} else {
					$array = array(
						"time"	 => 0,
						"days"	 => 0
					);
				}
			}
		} else {
			$array = array(
				"time"	 => ($this->end_date - $this->start_date) / 60,
				"days"	 => 0
			);
		}
		return $array;
	}

	/**
	 * Use duration function and take days
	 * @return integer
	 */
	public function getTotalDays() {
		$duration = $this->getDuration();
		return $duration['days'];
	}

	/**
	 * Checks if this absence is suitable for counting on the specified day.
	 *
	 * @param type $day the day to check the absence on
	 * @return boolean true if this absence is countable on this day, false otherwise
	 */
	public function isCountableOnDay($day) {
		$day = strtotime(date("d-m-Y", $day));
		$start = strtotime(date("d-m-Y", $this->getStartDate()));
		$end = strtotime(date("d-m-Y", $this->getEndDate()) . " +1 day");
		// Checks if the day is inside the absence period
		if ($day >= $start && $day < $end) {
			$absKind = $this->getAbsencesAbsenceKind();
			$cal = new \Calendar();
			// Checks if the day is a festivity
			if ($cal->isHoliday($day)) {
				if ($absKind->getCalcFestivities()) {
					return true;
				}
			} else {
				$timetable = TimetableQuery::create()
						->filterByEmployeeId($this->getEmployeeId())
						->filterByDateEnd(array('min' => $day, 'max' => ($day + 86399)))
						->findOne();
				if ($timetable != null) {
					return true;
				} else {
					if ($absKind->getCalcFerials()) {
						return true;
					}
				}
			}
		}
		return false;
	}

}
