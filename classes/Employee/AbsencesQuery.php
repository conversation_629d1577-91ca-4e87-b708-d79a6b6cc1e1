<?php

namespace Employee;

use Employee\om\BaseAbsencesQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'absences' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class AbsencesQuery extends BaseAbsencesQuery {

	/**
	 * Save or update and absence
	 * @param type $data arr of absence information
	 * @return boolean if saved or not
	 */
	public function write($data = null) {
		if ($data == null) {
			return false;
		}

		$absence_id = null;
		if (isset($data['absence_id'])) {
			$absence_id = (int) $data['absence_id'] > 0 ? (int) $data['absence_id'] : null;
		}
		unset($data['absence_id']);
		if ($absence_id == null) {// save..
			$absence = new Absences();
		} else { // ..or update
			$absence = AbsencesQuery::create()->findPk($absence_id);
			if ($data['unit'] == ABS_STACK_UNIT_HOURLY) {
				$absence->delete();
				$absence = new Absences();
			}
		}
		if (!isset($data['end_time'])) {
			$data['end_time'] = '00:00';
		}
		if (!isset($data['start_time'])) {
			$data['start_time'] = '00:00';
		}

		//$data['end_date'] = strtotime(isset($data['end_date']) ? $data['end_date'] . ' ' . $data['end_time'] : $data['start_date'] . ' ' . $data['end_time']);
		$data['end_date'] = strtotime($data['end_date'] . ' ' . $data['end_time']);
		$data['start_date'] = strtotime($data['start_date'] . ' ' . $data['start_time']);
		$data['date_of_req'] = strtotime($data['date_of_req']);

		unset($data['unit']);

		$absence->fromArray($data, \BasePeer::TYPE_FIELDNAME);
		$this->new = $absence->isNew();
		return $absence->save() > 0 ? true : false;
	}

}
