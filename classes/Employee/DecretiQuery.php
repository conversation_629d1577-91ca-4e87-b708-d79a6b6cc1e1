<?php

namespace Employee;

use Employee\om\BaseDecretiQuery;

/**
 * Skeleton subclass for performing query and update operations on the 'decreti' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class DecretiQuery extends BaseDecretiQuery {

	/**
	 * Gets Decreto object by Absences obj
	 * @param Absences $absence
	 * @return Decreti obj, null otherwise
	 */
	public function getDecreto($absence = null, $replaceStr = true) {
		if ($absence == null) {
			return null;
		}

		// Get linked data from absence
		$employee = $absence->getAbsenceEmployee();

		$decretoQ = new DecretiQuery();
		// Absence kind filtering
		$decretoQ->filterByAbsenceKind($absence->getAbKind());

		// Employee kind filtering
		$kind = $employee->isTeacher() ? 'teacher' : 'ata';
		$decretoQ->filterByEmployeeKind($kind);

		// Employee role filtering
		$role = $employee->isLongRole() ? 'long' : 'short';
		$decretoQ->filterByEmployeeRole($role);

		$decreto = $decretoQ->findOne();

		if ($replaceStr === true && $decreto != null) {
			$decreto->replaceHtml($employee, $absence);
		}

		return $decreto;
	}

}
