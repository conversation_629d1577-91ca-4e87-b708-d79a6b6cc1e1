<?php

namespace Employee;

use Employee\om\BasePresence;

/**
 * Skeleton subclass for representing a row from the 'personnel_presences' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class Presence extends BasePresence {

    /**
     * Check if the presence is normal.
     * @return boolean true if the presence is normal, false otherwise.
     */
    public function isNormal() {
        if ($this->getTypeEdit() == PRESENCE_NORMAL)
            return true;
        return false;
    }

    /**
     * Check if the presence is for lunch.
     * @return boolean true if the presence is for lunch, false otherwise.
     */
    public function isLunch() {
        if ($this->getTypeEdit() == PRESENCE_LUNCH)
            return true;
        return false;
    }

    /**
     * Check if the presence is for service.
     * @return boolean true if the presence is for service, false otherwise.
     */
    public function isService() {
        if ($this->getTypeEdit() == PRESENCE_SERVICE)
            return true;
        return false;
    }

    /**
     * Check if the presence is an entrance.
     * @return boolean true if the presence is an entrance, false otherwise.
     */
    public function isEntrance() {
        if ($this->getOriginalInOutEdit() == PRESENCE_ENTRANCE)
            return true;
        return false;
    }

    /**
     * Check if the presence is an exit.
     * @return boolean true if the presence is an exit, false otherwise.
     */
    public function isExit() {
        if ($this->getOriginalInOutEdit() == PRESENCE_EXIT)
            return true;
        return false;
    }

    /**
     * Check if the presence is Auto or Manual.
     * @return boolean true if the presence is an Manual, false otherwise.
     */
    public function isManual() {
        if ($this->getInsertionMode() == "T")
            return false;
        return true;
    }

    /**
     * Rounds the seconds of the presence. If $cut is True it just strips the
     * seconds without any roundings; if $cut is False it performs the rounding.
     *
     * @param boolean $cut True to cut exceeding seconds, False to perform a rounding
     * @param boolean $save True to update the db, False to skip saving.
     */
    public function roundSeconds($cut = true, $save = false) {
        if ($cut) {
            $this->setDateEdit(strtotime(date("d-m-Y H:i", $this->getDateEdit())));
        } else {
            $seconds = date("s", $this->getDateEdit());
            if ($seconds <= 30) {
                $this->setDateEdit($this->getDateEdit() - $seconds);
            } else {
                $this->setDateEdit($this->getDateEdit() - $seconds + 60);
            }
        }
        if ($save === true) {
            $this->save();
        }
    }

    /**
     * Returns the presence as a string.
     *
     * @return string the presence in a human readable form
     */
    public function toString($complete = false, $type = false, $kind = false, $mode = false, $project = false, $originals = false, $notes = false) {
        $this->roundSeconds();

        $presenceStr = "";
        $options = array();

        if ($complete) {
            $presenceStr = date("d-m-Y H:i", $this->getDateEdit());
        } else {
            $presenceStr = date("H:i", $this->getDateEdit());
        }

        if ($type) {
            $options['type'] = $this->isEntrance() ? "E" : "U";
        }
        if ($kind) {
            if ($this->isLunch()) {
                $options['kind'] = "p";
            } else if ($this->isService()) {
                $options['kind'] = "s";
            } else {
                // $options['kind'] = " ";
            }
        }
        if ($mode) {
            $options['mode'] = $this->getInsertionMode();
        }
        if ($project) {
            $options['project'] = "";
        }
        if ($originals) {
            $options['originals'] = "";
        }
        if ($notes) {
            if ($this->getDescription()) {
                $options['notes'] = $this->getDescription();
            }
        }

        if (count($options) > 0) {
            $options = "(" . implode(" - ", $options) . ")";
        }

        return implode(" ", array($presenceStr, $options));
    }

}
