<?php

namespace Employee;

use Employee\om\BasePresenceQuery;


/**
 * Skeleton subclass for performing query and update operations on the 'personnel_presences' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class PresenceQuery extends BasePresenceQuery
{

    public $errors = array();
    public $message = '';

    public function read($filters) {
        $presences = new PresenceQuery();
        if (isset($filters['employee_id'])) {
            $presences->filterByEmployeeId($filters['employee_id']);
        }
        if (isset($filters['personnel_presence_id'])) {
            $presences->filterByPrimaryKey($filters['personnel_presence_id']);
        }

        return $presences->find()->toArray(null, false, \BasePeer::TYPE_FIELDNAME);
    }

    public function write($data) {
        if (!isset($data['employee_id'])) {
            $this->errors['employee_id'] = _('Employee is required');
        }
        if (!isset($data['date_edit'])) {
            $this->errors['date_edit'] = _('Date is required');
        }
        if (!isset($data['date_hour'])) {
            $this->errors['date_hour'] = _('Hour is required');
        }

        if (count($this->errors) > 0) {
            return false;
        }

        $id = isset($data['personnel_presence_id']) ? (int) $data['personnel_presence_id'] : null;
        if($id > 0){
            $presence = PresenceQuery::create()->findPk($id);
            if($presence === null){
                $this->message = _('The presence does not exist');
                return false;
            }
        } else {
            $presence = new Presence;
        }

        $data['date_edit'] = strtotime($data['date_edit'].' '.$data['date_hour']);

        if($id === null){
            $data['date'] = $data['date_edit'];
            if(isset($data['type_edit'])){
                $data['type'] = $data['type_edit'];
            }
            if(isset($data['original_inout_edit'])){
                $data['original_inout'] = $data['original_inout_edit'];
            }
            if(isset($data['hour_type_edit_id'])){
                $data['hour_type_id'] = $data['hour_type_edit_id'];
            }
            if(isset($data['project_edit_id'])){
                $data['project_id'] = $data['project_edit_id'];
            }
        } else {
            if($presence->getDate() === 0){
                $data['date'] = $data['date_edit'];
            }
        }

        $presence->fromArray($data, \BasePeer::TYPE_FIELDNAME);

        $res = $presence->save();
        if ($res) {
            return $presence->toArray(\BasePeer::TYPE_FIELDNAME);
        }
        return false;
    }

    public function destroy($filter) {
        $presence = null;
        if (isset($filter['personnel_presence_id'])) {
            $presence = PresenceQuery::create()->findPk((int) $filter['personnel_presence_id']);
        }

        if (!$presence) {
            $this->message = _('Presence does not exist');
            return false;
        }
        $presence->delete();
        return true;
    }

}
