<?php

namespace Employee;

use Employee\om\BaseAbsenceStack;

/**
 * Skeleton subclass for representing a row from the 'absence_stack' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Employee
 */
class AbsenceStack extends BaseAbsenceStack {

	/**
	 * Checks if the absence stack unit is a daily or by time.
	 *
	 * @return boolean True if is daily, False otherwise
	 */
	public function isDaily() {
		if ($this->getUnit() == ABS_STACK_UNIT_DAILY)
			return true;
		return false;
	}

}
