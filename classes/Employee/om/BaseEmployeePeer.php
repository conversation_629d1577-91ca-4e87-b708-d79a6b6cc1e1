<?php

namespace Employee\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Core\ContactPeer;
use Core\InstitutePeer;
use Employee\AbsencesPeer;
use Employee\Employee;
use Employee\EmployeePeer;
use Employee\PersonnelStacksPeer;
use Employee\PresencePeer;
use Employee\StoredDayPeer;
use Employee\StoredMonthPeer;
use Employee\TimetablePeer;
use Employee\map\EmployeeTableMap;

/**
 * Base static class for performing query and update operations on the 'employee' table.
 *
 *
 *
 * @package propel.generator.Employee.om
 */
abstract class BaseEmployeePeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'employee';

    /** the related Propel class for this table */
    const OM_CLASS = 'Employee\\Employee';

    /** the related TableMap class for this table */
    const TM_CLASS = 'EmployeeTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 83;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 83;

    /** the column name for the employee_id field */
    const EMPLOYEE_ID = 'employee.employee_id';

    /** the column name for the name field */
    const NAME = 'employee.name';

    /** the column name for the surname field */
    const SURNAME = 'employee.surname';

    /** the column name for the gender field */
    const GENDER = 'employee.gender';

    /** the column name for the birthdate field */
    const BIRTHDATE = 'employee.birthdate';

    /** the column name for the fiscal_code field */
    const FISCAL_CODE = 'employee.fiscal_code';

    /** the column name for the residence_id field */
    const RESIDENCE_ID = 'employee.residence_id';

    /** the column name for the address_id field */
    const ADDRESS_ID = 'employee.address_id';

    /** the column name for the part_spesa field */
    const PART_SPESA = 'employee.part_spesa';

    /** the column name for the bank field */
    const BANK = 'employee.bank';

    /** the column name for the liq_office field */
    const LIQ_OFFICE = 'employee.liq_office';

    /** the column name for the inps field */
    const INPS = 'employee.inps';

    /** the column name for the insur_qual field */
    const INSUR_QUAL = 'employee.insur_qual';

    /** the column name for the fore field */
    const FORE = 'employee.fore';

    /** the column name for the asl field */
    const ASL = 'employee.asl';

    /** the column name for the adm_code field */
    const ADM_CODE = 'employee.adm_code';

    /** the column name for the way_pay field */
    const WAY_PAY = 'employee.way_pay';

    /** the column name for the liquid_group field */
    const LIQUID_GROUP = 'employee.liquid_group';

    /** the column name for the contr_code field */
    const CONTR_CODE = 'employee.contr_code';

    /** the column name for the contr_type field */
    const CONTR_TYPE = 'employee.contr_type';

    /** the column name for the contr_cat field */
    const CONTR_CAT = 'employee.contr_cat';

    /** the column name for the ssp_frm_pmnt field */
    const SSP_FRM_PMNT = 'employee.ssp_frm_pmnt';

    /** the column name for the personal_data field */
    const PERSONAL_DATA = 'employee.personal_data';

    /** the column name for the susp field */
    const SUSP = 'employee.susp';

    /** the column name for the payment_group field */
    const PAYMENT_GROUP = 'employee.payment_group';

    /** the column name for the priv_ret_type field */
    const PRIV_RET_TYPE = 'employee.priv_ret_type';

    /** the column name for the social_position field */
    const SOCIAL_POSITION = 'employee.social_position';

    /** the column name for the active field */
    const ACTIVE = 'employee.active';

    /** the column name for the statal_code field */
    const STATAL_CODE = 'employee.statal_code';

    /** the column name for the fiscal_city_code field */
    const FISCAL_CITY_CODE = 'employee.fiscal_city_code';

    /** the column name for the birthplace field */
    const BIRTHPLACE = 'employee.birthplace';

    /** the column name for the income field */
    const INCOME = 'employee.income';

    /** the column name for the state_birth field */
    const STATE_BIRTH = 'employee.state_birth';

    /** the column name for the citizenship field */
    const CITIZENSHIP = 'employee.citizenship';

    /** the column name for the id_sissi field */
    const ID_SISSI = 'employee.id_sissi';

    /** the column name for the dom_first_prev_year field */
    const DOM_FIRST_PREV_YEAR = 'employee.dom_first_prev_year';

    /** the column name for the dom_last_prev_year field */
    const DOM_LAST_PREV_YEAR = 'employee.dom_last_prev_year';

    /** the column name for the dom_first_curr_year field */
    const DOM_FIRST_CURR_YEAR = 'employee.dom_first_curr_year';

    /** the column name for the qualification field */
    const QUALIFICATION = 'employee.qualification';

    /** the column name for the liquid_office_id field */
    const LIQUID_OFFICE_ID = 'employee.liquid_office_id';

    /** the column name for the badge_number field */
    const BADGE_NUMBER = 'employee.badge_number';

    /** the column name for the tolerance_in field */
    const TOLERANCE_IN = 'employee.tolerance_in';

    /** the column name for the tolerance_out field */
    const TOLERANCE_OUT = 'employee.tolerance_out';

    /** the column name for the flexibility field */
    const FLEXIBILITY = 'employee.flexibility';

    /** the column name for the generic_tolerance field */
    const GENERIC_TOLERANCE = 'employee.generic_tolerance';

    /** the column name for the negative_round field */
    const NEGATIVE_ROUND = 'employee.negative_round';

    /** the column name for the recover_hours field */
    const RECOVER_HOURS = 'employee.recover_hours';

    /** the column name for the max_extraordinary_in field */
    const MAX_EXTRAORDINARY_IN = 'employee.max_extraordinary_in';

    /** the column name for the max_extraordinary_out field */
    const MAX_EXTRAORDINARY_OUT = 'employee.max_extraordinary_out';

    /** the column name for the min_extraordinary_in field */
    const MIN_EXTRAORDINARY_IN = 'employee.min_extraordinary_in';

    /** the column name for the min_extraordinary_out field */
    const MIN_EXTRAORDINARY_OUT = 'employee.min_extraordinary_out';

    /** the column name for the step_out field */
    const STEP_OUT = 'employee.step_out';

    /** the column name for the step_in field */
    const STEP_IN = 'employee.step_in';

    /** the column name for the max_break field */
    const MAX_BREAK = 'employee.max_break';

    /** the column name for the max_cont_work field */
    const MAX_CONT_WORK = 'employee.max_cont_work';

    /** the column name for the simplified_ata_settings field */
    const SIMPLIFIED_ATA_SETTINGS = 'employee.simplified_ata_settings';

    /** the column name for the tolerance_in_und field */
    const TOLERANCE_IN_UND = 'employee.tolerance_in_und';

    /** the column name for the tolerance_out_und field */
    const TOLERANCE_OUT_UND = 'employee.tolerance_out_und';

    /** the column name for the max_undefined_in field */
    const MAX_UNDEFINED_IN = 'employee.max_undefined_in';

    /** the column name for the max_undefined_out field */
    const MAX_UNDEFINED_OUT = 'employee.max_undefined_out';

    /** the column name for the min_undefined_in field */
    const MIN_UNDEFINED_IN = 'employee.min_undefined_in';

    /** the column name for the min_undefined_out field */
    const MIN_UNDEFINED_OUT = 'employee.min_undefined_out';

    /** the column name for the step_out_und field */
    const STEP_OUT_UND = 'employee.step_out_und';

    /** the column name for the step_in_und field */
    const STEP_IN_UND = 'employee.step_in_und';

    /** the column name for the undefined_parameter_active field */
    const UNDEFINED_PARAMETER_ACTIVE = 'employee.undefined_parameter_active';

    /** the column name for the min_extraordinary_total field */
    const MIN_EXTRAORDINARY_TOTAL = 'employee.min_extraordinary_total';

    /** the column name for the max_extraordinary_total field */
    const MAX_EXTRAORDINARY_TOTAL = 'employee.max_extraordinary_total';

    /** the column name for the min_undefined_total field */
    const MIN_UNDEFINED_TOTAL = 'employee.min_undefined_total';

    /** the column name for the max_undefined_total field */
    const MAX_UNDEFINED_TOTAL = 'employee.max_undefined_total';

    /** the column name for the step_total_undefined field */
    const STEP_TOTAL_UNDEFINED = 'employee.step_total_undefined';

    /** the column name for the step_total_extraordinary field */
    const STEP_TOTAL_EXTRAORDINARY = 'employee.step_total_extraordinary';

    /** the column name for the lunch_duration field */
    const LUNCH_DURATION = 'employee.lunch_duration';

    /** the column name for the lunch_deductible field */
    const LUNCH_DEDUCTIBLE = 'employee.lunch_deductible';

    /** the column name for the service_deductible field */
    const SERVICE_DEDUCTIBLE = 'employee.service_deductible';

    /** the column name for the min_undefined_lunch field */
    const MIN_UNDEFINED_LUNCH = 'employee.min_undefined_lunch';

    /** the column name for the min_extraordinary_lunch field */
    const MIN_EXTRAORDINARY_LUNCH = 'employee.min_extraordinary_lunch';

    /** the column name for the max_undefined_lunch field */
    const MAX_UNDEFINED_LUNCH = 'employee.max_undefined_lunch';

    /** the column name for the max_extraordinary_lunch field */
    const MAX_EXTRAORDINARY_LUNCH = 'employee.max_extraordinary_lunch';

    /** the column name for the step_lunch_undefined field */
    const STEP_LUNCH_UNDEFINED = 'employee.step_lunch_undefined';

    /** the column name for the step_lunch_extraordinary field */
    const STEP_LUNCH_EXTRAORDINARY = 'employee.step_lunch_extraordinary';

    /** the column name for the break_after_max_work field */
    const BREAK_AFTER_MAX_WORK = 'employee.break_after_max_work';

    /** the column name for the unit_recover_hours field */
    const UNIT_RECOVER_HOURS = 'employee.unit_recover_hours';

    /** the column name for the max_work field */
    const MAX_WORK = 'employee.max_work';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of Employee objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array Employee[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. EmployeePeer::$fieldNames[EmployeePeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('EmployeeId', 'Name', 'Surname', 'Gender', 'Birthdate', 'FiscalCode', 'ResidenceId', 'AddressId', 'PartSpesa', 'Bank', 'LiqOffice', 'Inps', 'InsurQual', 'Fore', 'Asl', 'AdmCode', 'WayPay', 'LiquidGroup', 'ContrCode', 'ContrType', 'ContrCat', 'SspFrmPmnt', 'PersonalData', 'Susp', 'PaymentGroup', 'PrivRetType', 'SocialPosition', 'Active', 'StatalCode', 'FiscalCityCode', 'Birthplace', 'Income', 'StateBirth', 'Citizenship', 'IdSissi', 'DomFirstPrevYear', 'DomLastPrevYear', 'DomFirstCurrYear', 'Qualification', 'LiquidOfficeId', 'BadgeNumber', 'ToleranceIn', 'ToleranceOut', 'Flexibility', 'GenericTolerance', 'NegativeRound', 'RecoverHours', 'MaxExtraordinaryIn', 'MaxExtraordinaryOut', 'MinExtraordinaryIn', 'MinExtraordinaryOut', 'StepOut', 'StepIn', 'MaxBreak', 'MaxContWork', 'SimplifiedAtaSettings', 'ToleranceInUnd', 'ToleranceOutUnd', 'MaxUndefinedIn', 'MaxUndefinedOut', 'MinUndefinedIn', 'MinUndefinedOut', 'StepOutUnd', 'StepInUnd', 'UndefinedParameterActive', 'MinExtraordinaryTotal', 'MaxExtraordinaryTotal', 'MinUndefinedTotal', 'MaxUndefinedTotal', 'StepTotalUndefined', 'StepTotalExtraordinary', 'LunchDuration', 'LunchDeductible', 'ServiceDeductible', 'MinUndefinedLunch', 'MinExtraordinaryLunch', 'MaxUndefinedLunch', 'MaxExtraordinaryLunch', 'StepLunchUndefined', 'StepLunchExtraordinary', 'BreakAfterMaxWork', 'UnitRecoverHours', 'MaxWork', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('employeeId', 'name', 'surname', 'gender', 'birthdate', 'fiscalCode', 'residenceId', 'addressId', 'partSpesa', 'bank', 'liqOffice', 'inps', 'insurQual', 'fore', 'asl', 'admCode', 'wayPay', 'liquidGroup', 'contrCode', 'contrType', 'contrCat', 'sspFrmPmnt', 'personalData', 'susp', 'paymentGroup', 'privRetType', 'socialPosition', 'active', 'statalCode', 'fiscalCityCode', 'birthplace', 'income', 'stateBirth', 'citizenship', 'idSissi', 'domFirstPrevYear', 'domLastPrevYear', 'domFirstCurrYear', 'qualification', 'liquidOfficeId', 'badgeNumber', 'toleranceIn', 'toleranceOut', 'flexibility', 'genericTolerance', 'negativeRound', 'recoverHours', 'maxExtraordinaryIn', 'maxExtraordinaryOut', 'minExtraordinaryIn', 'minExtraordinaryOut', 'stepOut', 'stepIn', 'maxBreak', 'maxContWork', 'simplifiedAtaSettings', 'toleranceInUnd', 'toleranceOutUnd', 'maxUndefinedIn', 'maxUndefinedOut', 'minUndefinedIn', 'minUndefinedOut', 'stepOutUnd', 'stepInUnd', 'undefinedParameterActive', 'minExtraordinaryTotal', 'maxExtraordinaryTotal', 'minUndefinedTotal', 'maxUndefinedTotal', 'stepTotalUndefined', 'stepTotalExtraordinary', 'lunchDuration', 'lunchDeductible', 'serviceDeductible', 'minUndefinedLunch', 'minExtraordinaryLunch', 'maxUndefinedLunch', 'maxExtraordinaryLunch', 'stepLunchUndefined', 'stepLunchExtraordinary', 'breakAfterMaxWork', 'unitRecoverHours', 'maxWork', ),
        BasePeer::TYPE_COLNAME => array (EmployeePeer::EMPLOYEE_ID, EmployeePeer::NAME, EmployeePeer::SURNAME, EmployeePeer::GENDER, EmployeePeer::BIRTHDATE, EmployeePeer::FISCAL_CODE, EmployeePeer::RESIDENCE_ID, EmployeePeer::ADDRESS_ID, EmployeePeer::PART_SPESA, EmployeePeer::BANK, EmployeePeer::LIQ_OFFICE, EmployeePeer::INPS, EmployeePeer::INSUR_QUAL, EmployeePeer::FORE, EmployeePeer::ASL, EmployeePeer::ADM_CODE, EmployeePeer::WAY_PAY, EmployeePeer::LIQUID_GROUP, EmployeePeer::CONTR_CODE, EmployeePeer::CONTR_TYPE, EmployeePeer::CONTR_CAT, EmployeePeer::SSP_FRM_PMNT, EmployeePeer::PERSONAL_DATA, EmployeePeer::SUSP, EmployeePeer::PAYMENT_GROUP, EmployeePeer::PRIV_RET_TYPE, EmployeePeer::SOCIAL_POSITION, EmployeePeer::ACTIVE, EmployeePeer::STATAL_CODE, EmployeePeer::FISCAL_CITY_CODE, EmployeePeer::BIRTHPLACE, EmployeePeer::INCOME, EmployeePeer::STATE_BIRTH, EmployeePeer::CITIZENSHIP, EmployeePeer::ID_SISSI, EmployeePeer::DOM_FIRST_PREV_YEAR, EmployeePeer::DOM_LAST_PREV_YEAR, EmployeePeer::DOM_FIRST_CURR_YEAR, EmployeePeer::QUALIFICATION, EmployeePeer::LIQUID_OFFICE_ID, EmployeePeer::BADGE_NUMBER, EmployeePeer::TOLERANCE_IN, EmployeePeer::TOLERANCE_OUT, EmployeePeer::FLEXIBILITY, EmployeePeer::GENERIC_TOLERANCE, EmployeePeer::NEGATIVE_ROUND, EmployeePeer::RECOVER_HOURS, EmployeePeer::MAX_EXTRAORDINARY_IN, EmployeePeer::MAX_EXTRAORDINARY_OUT, EmployeePeer::MIN_EXTRAORDINARY_IN, EmployeePeer::MIN_EXTRAORDINARY_OUT, EmployeePeer::STEP_OUT, EmployeePeer::STEP_IN, EmployeePeer::MAX_BREAK, EmployeePeer::MAX_CONT_WORK, EmployeePeer::SIMPLIFIED_ATA_SETTINGS, EmployeePeer::TOLERANCE_IN_UND, EmployeePeer::TOLERANCE_OUT_UND, EmployeePeer::MAX_UNDEFINED_IN, EmployeePeer::MAX_UNDEFINED_OUT, EmployeePeer::MIN_UNDEFINED_IN, EmployeePeer::MIN_UNDEFINED_OUT, EmployeePeer::STEP_OUT_UND, EmployeePeer::STEP_IN_UND, EmployeePeer::UNDEFINED_PARAMETER_ACTIVE, EmployeePeer::MIN_EXTRAORDINARY_TOTAL, EmployeePeer::MAX_EXTRAORDINARY_TOTAL, EmployeePeer::MIN_UNDEFINED_TOTAL, EmployeePeer::MAX_UNDEFINED_TOTAL, EmployeePeer::STEP_TOTAL_UNDEFINED, EmployeePeer::STEP_TOTAL_EXTRAORDINARY, EmployeePeer::LUNCH_DURATION, EmployeePeer::LUNCH_DEDUCTIBLE, EmployeePeer::SERVICE_DEDUCTIBLE, EmployeePeer::MIN_UNDEFINED_LUNCH, EmployeePeer::MIN_EXTRAORDINARY_LUNCH, EmployeePeer::MAX_UNDEFINED_LUNCH, EmployeePeer::MAX_EXTRAORDINARY_LUNCH, EmployeePeer::STEP_LUNCH_UNDEFINED, EmployeePeer::STEP_LUNCH_EXTRAORDINARY, EmployeePeer::BREAK_AFTER_MAX_WORK, EmployeePeer::UNIT_RECOVER_HOURS, EmployeePeer::MAX_WORK, ),
        BasePeer::TYPE_RAW_COLNAME => array ('EMPLOYEE_ID', 'NAME', 'SURNAME', 'GENDER', 'BIRTHDATE', 'FISCAL_CODE', 'RESIDENCE_ID', 'ADDRESS_ID', 'PART_SPESA', 'BANK', 'LIQ_OFFICE', 'INPS', 'INSUR_QUAL', 'FORE', 'ASL', 'ADM_CODE', 'WAY_PAY', 'LIQUID_GROUP', 'CONTR_CODE', 'CONTR_TYPE', 'CONTR_CAT', 'SSP_FRM_PMNT', 'PERSONAL_DATA', 'SUSP', 'PAYMENT_GROUP', 'PRIV_RET_TYPE', 'SOCIAL_POSITION', 'ACTIVE', 'STATAL_CODE', 'FISCAL_CITY_CODE', 'BIRTHPLACE', 'INCOME', 'STATE_BIRTH', 'CITIZENSHIP', 'ID_SISSI', 'DOM_FIRST_PREV_YEAR', 'DOM_LAST_PREV_YEAR', 'DOM_FIRST_CURR_YEAR', 'QUALIFICATION', 'LIQUID_OFFICE_ID', 'BADGE_NUMBER', 'TOLERANCE_IN', 'TOLERANCE_OUT', 'FLEXIBILITY', 'GENERIC_TOLERANCE', 'NEGATIVE_ROUND', 'RECOVER_HOURS', 'MAX_EXTRAORDINARY_IN', 'MAX_EXTRAORDINARY_OUT', 'MIN_EXTRAORDINARY_IN', 'MIN_EXTRAORDINARY_OUT', 'STEP_OUT', 'STEP_IN', 'MAX_BREAK', 'MAX_CONT_WORK', 'SIMPLIFIED_ATA_SETTINGS', 'TOLERANCE_IN_UND', 'TOLERANCE_OUT_UND', 'MAX_UNDEFINED_IN', 'MAX_UNDEFINED_OUT', 'MIN_UNDEFINED_IN', 'MIN_UNDEFINED_OUT', 'STEP_OUT_UND', 'STEP_IN_UND', 'UNDEFINED_PARAMETER_ACTIVE', 'MIN_EXTRAORDINARY_TOTAL', 'MAX_EXTRAORDINARY_TOTAL', 'MIN_UNDEFINED_TOTAL', 'MAX_UNDEFINED_TOTAL', 'STEP_TOTAL_UNDEFINED', 'STEP_TOTAL_EXTRAORDINARY', 'LUNCH_DURATION', 'LUNCH_DEDUCTIBLE', 'SERVICE_DEDUCTIBLE', 'MIN_UNDEFINED_LUNCH', 'MIN_EXTRAORDINARY_LUNCH', 'MAX_UNDEFINED_LUNCH', 'MAX_EXTRAORDINARY_LUNCH', 'STEP_LUNCH_UNDEFINED', 'STEP_LUNCH_EXTRAORDINARY', 'BREAK_AFTER_MAX_WORK', 'UNIT_RECOVER_HOURS', 'MAX_WORK', ),
        BasePeer::TYPE_FIELDNAME => array ('employee_id', 'name', 'surname', 'gender', 'birthdate', 'fiscal_code', 'residence_id', 'address_id', 'part_spesa', 'bank', 'liq_office', 'inps', 'insur_qual', 'fore', 'asl', 'adm_code', 'way_pay', 'liquid_group', 'contr_code', 'contr_type', 'contr_cat', 'ssp_frm_pmnt', 'personal_data', 'susp', 'payment_group', 'priv_ret_type', 'social_position', 'active', 'statal_code', 'fiscal_city_code', 'birthplace', 'income', 'state_birth', 'citizenship', 'id_sissi', 'dom_first_prev_year', 'dom_last_prev_year', 'dom_first_curr_year', 'qualification', 'liquid_office_id', 'badge_number', 'tolerance_in', 'tolerance_out', 'flexibility', 'generic_tolerance', 'negative_round', 'recover_hours', 'max_extraordinary_in', 'max_extraordinary_out', 'min_extraordinary_in', 'min_extraordinary_out', 'step_out', 'step_in', 'max_break', 'max_cont_work', 'simplified_ata_settings', 'tolerance_in_und', 'tolerance_out_und', 'max_undefined_in', 'max_undefined_out', 'min_undefined_in', 'min_undefined_out', 'step_out_und', 'step_in_und', 'undefined_parameter_active', 'min_extraordinary_total', 'max_extraordinary_total', 'min_undefined_total', 'max_undefined_total', 'step_total_undefined', 'step_total_extraordinary', 'lunch_duration', 'lunch_deductible', 'service_deductible', 'min_undefined_lunch', 'min_extraordinary_lunch', 'max_undefined_lunch', 'max_extraordinary_lunch', 'step_lunch_undefined', 'step_lunch_extraordinary', 'break_after_max_work', 'unit_recover_hours', 'max_work', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. EmployeePeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('EmployeeId' => 0, 'Name' => 1, 'Surname' => 2, 'Gender' => 3, 'Birthdate' => 4, 'FiscalCode' => 5, 'ResidenceId' => 6, 'AddressId' => 7, 'PartSpesa' => 8, 'Bank' => 9, 'LiqOffice' => 10, 'Inps' => 11, 'InsurQual' => 12, 'Fore' => 13, 'Asl' => 14, 'AdmCode' => 15, 'WayPay' => 16, 'LiquidGroup' => 17, 'ContrCode' => 18, 'ContrType' => 19, 'ContrCat' => 20, 'SspFrmPmnt' => 21, 'PersonalData' => 22, 'Susp' => 23, 'PaymentGroup' => 24, 'PrivRetType' => 25, 'SocialPosition' => 26, 'Active' => 27, 'StatalCode' => 28, 'FiscalCityCode' => 29, 'Birthplace' => 30, 'Income' => 31, 'StateBirth' => 32, 'Citizenship' => 33, 'IdSissi' => 34, 'DomFirstPrevYear' => 35, 'DomLastPrevYear' => 36, 'DomFirstCurrYear' => 37, 'Qualification' => 38, 'LiquidOfficeId' => 39, 'BadgeNumber' => 40, 'ToleranceIn' => 41, 'ToleranceOut' => 42, 'Flexibility' => 43, 'GenericTolerance' => 44, 'NegativeRound' => 45, 'RecoverHours' => 46, 'MaxExtraordinaryIn' => 47, 'MaxExtraordinaryOut' => 48, 'MinExtraordinaryIn' => 49, 'MinExtraordinaryOut' => 50, 'StepOut' => 51, 'StepIn' => 52, 'MaxBreak' => 53, 'MaxContWork' => 54, 'SimplifiedAtaSettings' => 55, 'ToleranceInUnd' => 56, 'ToleranceOutUnd' => 57, 'MaxUndefinedIn' => 58, 'MaxUndefinedOut' => 59, 'MinUndefinedIn' => 60, 'MinUndefinedOut' => 61, 'StepOutUnd' => 62, 'StepInUnd' => 63, 'UndefinedParameterActive' => 64, 'MinExtraordinaryTotal' => 65, 'MaxExtraordinaryTotal' => 66, 'MinUndefinedTotal' => 67, 'MaxUndefinedTotal' => 68, 'StepTotalUndefined' => 69, 'StepTotalExtraordinary' => 70, 'LunchDuration' => 71, 'LunchDeductible' => 72, 'ServiceDeductible' => 73, 'MinUndefinedLunch' => 74, 'MinExtraordinaryLunch' => 75, 'MaxUndefinedLunch' => 76, 'MaxExtraordinaryLunch' => 77, 'StepLunchUndefined' => 78, 'StepLunchExtraordinary' => 79, 'BreakAfterMaxWork' => 80, 'UnitRecoverHours' => 81, 'MaxWork' => 82, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('employeeId' => 0, 'name' => 1, 'surname' => 2, 'gender' => 3, 'birthdate' => 4, 'fiscalCode' => 5, 'residenceId' => 6, 'addressId' => 7, 'partSpesa' => 8, 'bank' => 9, 'liqOffice' => 10, 'inps' => 11, 'insurQual' => 12, 'fore' => 13, 'asl' => 14, 'admCode' => 15, 'wayPay' => 16, 'liquidGroup' => 17, 'contrCode' => 18, 'contrType' => 19, 'contrCat' => 20, 'sspFrmPmnt' => 21, 'personalData' => 22, 'susp' => 23, 'paymentGroup' => 24, 'privRetType' => 25, 'socialPosition' => 26, 'active' => 27, 'statalCode' => 28, 'fiscalCityCode' => 29, 'birthplace' => 30, 'income' => 31, 'stateBirth' => 32, 'citizenship' => 33, 'idSissi' => 34, 'domFirstPrevYear' => 35, 'domLastPrevYear' => 36, 'domFirstCurrYear' => 37, 'qualification' => 38, 'liquidOfficeId' => 39, 'badgeNumber' => 40, 'toleranceIn' => 41, 'toleranceOut' => 42, 'flexibility' => 43, 'genericTolerance' => 44, 'negativeRound' => 45, 'recoverHours' => 46, 'maxExtraordinaryIn' => 47, 'maxExtraordinaryOut' => 48, 'minExtraordinaryIn' => 49, 'minExtraordinaryOut' => 50, 'stepOut' => 51, 'stepIn' => 52, 'maxBreak' => 53, 'maxContWork' => 54, 'simplifiedAtaSettings' => 55, 'toleranceInUnd' => 56, 'toleranceOutUnd' => 57, 'maxUndefinedIn' => 58, 'maxUndefinedOut' => 59, 'minUndefinedIn' => 60, 'minUndefinedOut' => 61, 'stepOutUnd' => 62, 'stepInUnd' => 63, 'undefinedParameterActive' => 64, 'minExtraordinaryTotal' => 65, 'maxExtraordinaryTotal' => 66, 'minUndefinedTotal' => 67, 'maxUndefinedTotal' => 68, 'stepTotalUndefined' => 69, 'stepTotalExtraordinary' => 70, 'lunchDuration' => 71, 'lunchDeductible' => 72, 'serviceDeductible' => 73, 'minUndefinedLunch' => 74, 'minExtraordinaryLunch' => 75, 'maxUndefinedLunch' => 76, 'maxExtraordinaryLunch' => 77, 'stepLunchUndefined' => 78, 'stepLunchExtraordinary' => 79, 'breakAfterMaxWork' => 80, 'unitRecoverHours' => 81, 'maxWork' => 82, ),
        BasePeer::TYPE_COLNAME => array (EmployeePeer::EMPLOYEE_ID => 0, EmployeePeer::NAME => 1, EmployeePeer::SURNAME => 2, EmployeePeer::GENDER => 3, EmployeePeer::BIRTHDATE => 4, EmployeePeer::FISCAL_CODE => 5, EmployeePeer::RESIDENCE_ID => 6, EmployeePeer::ADDRESS_ID => 7, EmployeePeer::PART_SPESA => 8, EmployeePeer::BANK => 9, EmployeePeer::LIQ_OFFICE => 10, EmployeePeer::INPS => 11, EmployeePeer::INSUR_QUAL => 12, EmployeePeer::FORE => 13, EmployeePeer::ASL => 14, EmployeePeer::ADM_CODE => 15, EmployeePeer::WAY_PAY => 16, EmployeePeer::LIQUID_GROUP => 17, EmployeePeer::CONTR_CODE => 18, EmployeePeer::CONTR_TYPE => 19, EmployeePeer::CONTR_CAT => 20, EmployeePeer::SSP_FRM_PMNT => 21, EmployeePeer::PERSONAL_DATA => 22, EmployeePeer::SUSP => 23, EmployeePeer::PAYMENT_GROUP => 24, EmployeePeer::PRIV_RET_TYPE => 25, EmployeePeer::SOCIAL_POSITION => 26, EmployeePeer::ACTIVE => 27, EmployeePeer::STATAL_CODE => 28, EmployeePeer::FISCAL_CITY_CODE => 29, EmployeePeer::BIRTHPLACE => 30, EmployeePeer::INCOME => 31, EmployeePeer::STATE_BIRTH => 32, EmployeePeer::CITIZENSHIP => 33, EmployeePeer::ID_SISSI => 34, EmployeePeer::DOM_FIRST_PREV_YEAR => 35, EmployeePeer::DOM_LAST_PREV_YEAR => 36, EmployeePeer::DOM_FIRST_CURR_YEAR => 37, EmployeePeer::QUALIFICATION => 38, EmployeePeer::LIQUID_OFFICE_ID => 39, EmployeePeer::BADGE_NUMBER => 40, EmployeePeer::TOLERANCE_IN => 41, EmployeePeer::TOLERANCE_OUT => 42, EmployeePeer::FLEXIBILITY => 43, EmployeePeer::GENERIC_TOLERANCE => 44, EmployeePeer::NEGATIVE_ROUND => 45, EmployeePeer::RECOVER_HOURS => 46, EmployeePeer::MAX_EXTRAORDINARY_IN => 47, EmployeePeer::MAX_EXTRAORDINARY_OUT => 48, EmployeePeer::MIN_EXTRAORDINARY_IN => 49, EmployeePeer::MIN_EXTRAORDINARY_OUT => 50, EmployeePeer::STEP_OUT => 51, EmployeePeer::STEP_IN => 52, EmployeePeer::MAX_BREAK => 53, EmployeePeer::MAX_CONT_WORK => 54, EmployeePeer::SIMPLIFIED_ATA_SETTINGS => 55, EmployeePeer::TOLERANCE_IN_UND => 56, EmployeePeer::TOLERANCE_OUT_UND => 57, EmployeePeer::MAX_UNDEFINED_IN => 58, EmployeePeer::MAX_UNDEFINED_OUT => 59, EmployeePeer::MIN_UNDEFINED_IN => 60, EmployeePeer::MIN_UNDEFINED_OUT => 61, EmployeePeer::STEP_OUT_UND => 62, EmployeePeer::STEP_IN_UND => 63, EmployeePeer::UNDEFINED_PARAMETER_ACTIVE => 64, EmployeePeer::MIN_EXTRAORDINARY_TOTAL => 65, EmployeePeer::MAX_EXTRAORDINARY_TOTAL => 66, EmployeePeer::MIN_UNDEFINED_TOTAL => 67, EmployeePeer::MAX_UNDEFINED_TOTAL => 68, EmployeePeer::STEP_TOTAL_UNDEFINED => 69, EmployeePeer::STEP_TOTAL_EXTRAORDINARY => 70, EmployeePeer::LUNCH_DURATION => 71, EmployeePeer::LUNCH_DEDUCTIBLE => 72, EmployeePeer::SERVICE_DEDUCTIBLE => 73, EmployeePeer::MIN_UNDEFINED_LUNCH => 74, EmployeePeer::MIN_EXTRAORDINARY_LUNCH => 75, EmployeePeer::MAX_UNDEFINED_LUNCH => 76, EmployeePeer::MAX_EXTRAORDINARY_LUNCH => 77, EmployeePeer::STEP_LUNCH_UNDEFINED => 78, EmployeePeer::STEP_LUNCH_EXTRAORDINARY => 79, EmployeePeer::BREAK_AFTER_MAX_WORK => 80, EmployeePeer::UNIT_RECOVER_HOURS => 81, EmployeePeer::MAX_WORK => 82, ),
        BasePeer::TYPE_RAW_COLNAME => array ('EMPLOYEE_ID' => 0, 'NAME' => 1, 'SURNAME' => 2, 'GENDER' => 3, 'BIRTHDATE' => 4, 'FISCAL_CODE' => 5, 'RESIDENCE_ID' => 6, 'ADDRESS_ID' => 7, 'PART_SPESA' => 8, 'BANK' => 9, 'LIQ_OFFICE' => 10, 'INPS' => 11, 'INSUR_QUAL' => 12, 'FORE' => 13, 'ASL' => 14, 'ADM_CODE' => 15, 'WAY_PAY' => 16, 'LIQUID_GROUP' => 17, 'CONTR_CODE' => 18, 'CONTR_TYPE' => 19, 'CONTR_CAT' => 20, 'SSP_FRM_PMNT' => 21, 'PERSONAL_DATA' => 22, 'SUSP' => 23, 'PAYMENT_GROUP' => 24, 'PRIV_RET_TYPE' => 25, 'SOCIAL_POSITION' => 26, 'ACTIVE' => 27, 'STATAL_CODE' => 28, 'FISCAL_CITY_CODE' => 29, 'BIRTHPLACE' => 30, 'INCOME' => 31, 'STATE_BIRTH' => 32, 'CITIZENSHIP' => 33, 'ID_SISSI' => 34, 'DOM_FIRST_PREV_YEAR' => 35, 'DOM_LAST_PREV_YEAR' => 36, 'DOM_FIRST_CURR_YEAR' => 37, 'QUALIFICATION' => 38, 'LIQUID_OFFICE_ID' => 39, 'BADGE_NUMBER' => 40, 'TOLERANCE_IN' => 41, 'TOLERANCE_OUT' => 42, 'FLEXIBILITY' => 43, 'GENERIC_TOLERANCE' => 44, 'NEGATIVE_ROUND' => 45, 'RECOVER_HOURS' => 46, 'MAX_EXTRAORDINARY_IN' => 47, 'MAX_EXTRAORDINARY_OUT' => 48, 'MIN_EXTRAORDINARY_IN' => 49, 'MIN_EXTRAORDINARY_OUT' => 50, 'STEP_OUT' => 51, 'STEP_IN' => 52, 'MAX_BREAK' => 53, 'MAX_CONT_WORK' => 54, 'SIMPLIFIED_ATA_SETTINGS' => 55, 'TOLERANCE_IN_UND' => 56, 'TOLERANCE_OUT_UND' => 57, 'MAX_UNDEFINED_IN' => 58, 'MAX_UNDEFINED_OUT' => 59, 'MIN_UNDEFINED_IN' => 60, 'MIN_UNDEFINED_OUT' => 61, 'STEP_OUT_UND' => 62, 'STEP_IN_UND' => 63, 'UNDEFINED_PARAMETER_ACTIVE' => 64, 'MIN_EXTRAORDINARY_TOTAL' => 65, 'MAX_EXTRAORDINARY_TOTAL' => 66, 'MIN_UNDEFINED_TOTAL' => 67, 'MAX_UNDEFINED_TOTAL' => 68, 'STEP_TOTAL_UNDEFINED' => 69, 'STEP_TOTAL_EXTRAORDINARY' => 70, 'LUNCH_DURATION' => 71, 'LUNCH_DEDUCTIBLE' => 72, 'SERVICE_DEDUCTIBLE' => 73, 'MIN_UNDEFINED_LUNCH' => 74, 'MIN_EXTRAORDINARY_LUNCH' => 75, 'MAX_UNDEFINED_LUNCH' => 76, 'MAX_EXTRAORDINARY_LUNCH' => 77, 'STEP_LUNCH_UNDEFINED' => 78, 'STEP_LUNCH_EXTRAORDINARY' => 79, 'BREAK_AFTER_MAX_WORK' => 80, 'UNIT_RECOVER_HOURS' => 81, 'MAX_WORK' => 82, ),
        BasePeer::TYPE_FIELDNAME => array ('employee_id' => 0, 'name' => 1, 'surname' => 2, 'gender' => 3, 'birthdate' => 4, 'fiscal_code' => 5, 'residence_id' => 6, 'address_id' => 7, 'part_spesa' => 8, 'bank' => 9, 'liq_office' => 10, 'inps' => 11, 'insur_qual' => 12, 'fore' => 13, 'asl' => 14, 'adm_code' => 15, 'way_pay' => 16, 'liquid_group' => 17, 'contr_code' => 18, 'contr_type' => 19, 'contr_cat' => 20, 'ssp_frm_pmnt' => 21, 'personal_data' => 22, 'susp' => 23, 'payment_group' => 24, 'priv_ret_type' => 25, 'social_position' => 26, 'active' => 27, 'statal_code' => 28, 'fiscal_city_code' => 29, 'birthplace' => 30, 'income' => 31, 'state_birth' => 32, 'citizenship' => 33, 'id_sissi' => 34, 'dom_first_prev_year' => 35, 'dom_last_prev_year' => 36, 'dom_first_curr_year' => 37, 'qualification' => 38, 'liquid_office_id' => 39, 'badge_number' => 40, 'tolerance_in' => 41, 'tolerance_out' => 42, 'flexibility' => 43, 'generic_tolerance' => 44, 'negative_round' => 45, 'recover_hours' => 46, 'max_extraordinary_in' => 47, 'max_extraordinary_out' => 48, 'min_extraordinary_in' => 49, 'min_extraordinary_out' => 50, 'step_out' => 51, 'step_in' => 52, 'max_break' => 53, 'max_cont_work' => 54, 'simplified_ata_settings' => 55, 'tolerance_in_und' => 56, 'tolerance_out_und' => 57, 'max_undefined_in' => 58, 'max_undefined_out' => 59, 'min_undefined_in' => 60, 'min_undefined_out' => 61, 'step_out_und' => 62, 'step_in_und' => 63, 'undefined_parameter_active' => 64, 'min_extraordinary_total' => 65, 'max_extraordinary_total' => 66, 'min_undefined_total' => 67, 'max_undefined_total' => 68, 'step_total_undefined' => 69, 'step_total_extraordinary' => 70, 'lunch_duration' => 71, 'lunch_deductible' => 72, 'service_deductible' => 73, 'min_undefined_lunch' => 74, 'min_extraordinary_lunch' => 75, 'max_undefined_lunch' => 76, 'max_extraordinary_lunch' => 77, 'step_lunch_undefined' => 78, 'step_lunch_extraordinary' => 79, 'break_after_max_work' => 80, 'unit_recover_hours' => 81, 'max_work' => 82, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = EmployeePeer::getFieldNames($toType);
        $key = isset(EmployeePeer::$fieldKeys[$fromType][$name]) ? EmployeePeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(EmployeePeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, EmployeePeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return EmployeePeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. EmployeePeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(EmployeePeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(EmployeePeer::EMPLOYEE_ID);
            $criteria->addSelectColumn(EmployeePeer::NAME);
            $criteria->addSelectColumn(EmployeePeer::SURNAME);
            $criteria->addSelectColumn(EmployeePeer::GENDER);
            $criteria->addSelectColumn(EmployeePeer::BIRTHDATE);
            $criteria->addSelectColumn(EmployeePeer::FISCAL_CODE);
            $criteria->addSelectColumn(EmployeePeer::RESIDENCE_ID);
            $criteria->addSelectColumn(EmployeePeer::ADDRESS_ID);
            $criteria->addSelectColumn(EmployeePeer::PART_SPESA);
            $criteria->addSelectColumn(EmployeePeer::BANK);
            $criteria->addSelectColumn(EmployeePeer::LIQ_OFFICE);
            $criteria->addSelectColumn(EmployeePeer::INPS);
            $criteria->addSelectColumn(EmployeePeer::INSUR_QUAL);
            $criteria->addSelectColumn(EmployeePeer::FORE);
            $criteria->addSelectColumn(EmployeePeer::ASL);
            $criteria->addSelectColumn(EmployeePeer::ADM_CODE);
            $criteria->addSelectColumn(EmployeePeer::WAY_PAY);
            $criteria->addSelectColumn(EmployeePeer::LIQUID_GROUP);
            $criteria->addSelectColumn(EmployeePeer::CONTR_CODE);
            $criteria->addSelectColumn(EmployeePeer::CONTR_TYPE);
            $criteria->addSelectColumn(EmployeePeer::CONTR_CAT);
            $criteria->addSelectColumn(EmployeePeer::SSP_FRM_PMNT);
            $criteria->addSelectColumn(EmployeePeer::PERSONAL_DATA);
            $criteria->addSelectColumn(EmployeePeer::SUSP);
            $criteria->addSelectColumn(EmployeePeer::PAYMENT_GROUP);
            $criteria->addSelectColumn(EmployeePeer::PRIV_RET_TYPE);
            $criteria->addSelectColumn(EmployeePeer::SOCIAL_POSITION);
            $criteria->addSelectColumn(EmployeePeer::ACTIVE);
            $criteria->addSelectColumn(EmployeePeer::STATAL_CODE);
            $criteria->addSelectColumn(EmployeePeer::FISCAL_CITY_CODE);
            $criteria->addSelectColumn(EmployeePeer::BIRTHPLACE);
            $criteria->addSelectColumn(EmployeePeer::INCOME);
            $criteria->addSelectColumn(EmployeePeer::STATE_BIRTH);
            $criteria->addSelectColumn(EmployeePeer::CITIZENSHIP);
            $criteria->addSelectColumn(EmployeePeer::ID_SISSI);
            $criteria->addSelectColumn(EmployeePeer::DOM_FIRST_PREV_YEAR);
            $criteria->addSelectColumn(EmployeePeer::DOM_LAST_PREV_YEAR);
            $criteria->addSelectColumn(EmployeePeer::DOM_FIRST_CURR_YEAR);
            $criteria->addSelectColumn(EmployeePeer::QUALIFICATION);
            $criteria->addSelectColumn(EmployeePeer::LIQUID_OFFICE_ID);
            $criteria->addSelectColumn(EmployeePeer::BADGE_NUMBER);
            $criteria->addSelectColumn(EmployeePeer::TOLERANCE_IN);
            $criteria->addSelectColumn(EmployeePeer::TOLERANCE_OUT);
            $criteria->addSelectColumn(EmployeePeer::FLEXIBILITY);
            $criteria->addSelectColumn(EmployeePeer::GENERIC_TOLERANCE);
            $criteria->addSelectColumn(EmployeePeer::NEGATIVE_ROUND);
            $criteria->addSelectColumn(EmployeePeer::RECOVER_HOURS);
            $criteria->addSelectColumn(EmployeePeer::MAX_EXTRAORDINARY_IN);
            $criteria->addSelectColumn(EmployeePeer::MAX_EXTRAORDINARY_OUT);
            $criteria->addSelectColumn(EmployeePeer::MIN_EXTRAORDINARY_IN);
            $criteria->addSelectColumn(EmployeePeer::MIN_EXTRAORDINARY_OUT);
            $criteria->addSelectColumn(EmployeePeer::STEP_OUT);
            $criteria->addSelectColumn(EmployeePeer::STEP_IN);
            $criteria->addSelectColumn(EmployeePeer::MAX_BREAK);
            $criteria->addSelectColumn(EmployeePeer::MAX_CONT_WORK);
            $criteria->addSelectColumn(EmployeePeer::SIMPLIFIED_ATA_SETTINGS);
            $criteria->addSelectColumn(EmployeePeer::TOLERANCE_IN_UND);
            $criteria->addSelectColumn(EmployeePeer::TOLERANCE_OUT_UND);
            $criteria->addSelectColumn(EmployeePeer::MAX_UNDEFINED_IN);
            $criteria->addSelectColumn(EmployeePeer::MAX_UNDEFINED_OUT);
            $criteria->addSelectColumn(EmployeePeer::MIN_UNDEFINED_IN);
            $criteria->addSelectColumn(EmployeePeer::MIN_UNDEFINED_OUT);
            $criteria->addSelectColumn(EmployeePeer::STEP_OUT_UND);
            $criteria->addSelectColumn(EmployeePeer::STEP_IN_UND);
            $criteria->addSelectColumn(EmployeePeer::UNDEFINED_PARAMETER_ACTIVE);
            $criteria->addSelectColumn(EmployeePeer::MIN_EXTRAORDINARY_TOTAL);
            $criteria->addSelectColumn(EmployeePeer::MAX_EXTRAORDINARY_TOTAL);
            $criteria->addSelectColumn(EmployeePeer::MIN_UNDEFINED_TOTAL);
            $criteria->addSelectColumn(EmployeePeer::MAX_UNDEFINED_TOTAL);
            $criteria->addSelectColumn(EmployeePeer::STEP_TOTAL_UNDEFINED);
            $criteria->addSelectColumn(EmployeePeer::STEP_TOTAL_EXTRAORDINARY);
            $criteria->addSelectColumn(EmployeePeer::LUNCH_DURATION);
            $criteria->addSelectColumn(EmployeePeer::LUNCH_DEDUCTIBLE);
            $criteria->addSelectColumn(EmployeePeer::SERVICE_DEDUCTIBLE);
            $criteria->addSelectColumn(EmployeePeer::MIN_UNDEFINED_LUNCH);
            $criteria->addSelectColumn(EmployeePeer::MIN_EXTRAORDINARY_LUNCH);
            $criteria->addSelectColumn(EmployeePeer::MAX_UNDEFINED_LUNCH);
            $criteria->addSelectColumn(EmployeePeer::MAX_EXTRAORDINARY_LUNCH);
            $criteria->addSelectColumn(EmployeePeer::STEP_LUNCH_UNDEFINED);
            $criteria->addSelectColumn(EmployeePeer::STEP_LUNCH_EXTRAORDINARY);
            $criteria->addSelectColumn(EmployeePeer::BREAK_AFTER_MAX_WORK);
            $criteria->addSelectColumn(EmployeePeer::UNIT_RECOVER_HOURS);
            $criteria->addSelectColumn(EmployeePeer::MAX_WORK);
        } else {
            $criteria->addSelectColumn($alias . '.employee_id');
            $criteria->addSelectColumn($alias . '.name');
            $criteria->addSelectColumn($alias . '.surname');
            $criteria->addSelectColumn($alias . '.gender');
            $criteria->addSelectColumn($alias . '.birthdate');
            $criteria->addSelectColumn($alias . '.fiscal_code');
            $criteria->addSelectColumn($alias . '.residence_id');
            $criteria->addSelectColumn($alias . '.address_id');
            $criteria->addSelectColumn($alias . '.part_spesa');
            $criteria->addSelectColumn($alias . '.bank');
            $criteria->addSelectColumn($alias . '.liq_office');
            $criteria->addSelectColumn($alias . '.inps');
            $criteria->addSelectColumn($alias . '.insur_qual');
            $criteria->addSelectColumn($alias . '.fore');
            $criteria->addSelectColumn($alias . '.asl');
            $criteria->addSelectColumn($alias . '.adm_code');
            $criteria->addSelectColumn($alias . '.way_pay');
            $criteria->addSelectColumn($alias . '.liquid_group');
            $criteria->addSelectColumn($alias . '.contr_code');
            $criteria->addSelectColumn($alias . '.contr_type');
            $criteria->addSelectColumn($alias . '.contr_cat');
            $criteria->addSelectColumn($alias . '.ssp_frm_pmnt');
            $criteria->addSelectColumn($alias . '.personal_data');
            $criteria->addSelectColumn($alias . '.susp');
            $criteria->addSelectColumn($alias . '.payment_group');
            $criteria->addSelectColumn($alias . '.priv_ret_type');
            $criteria->addSelectColumn($alias . '.social_position');
            $criteria->addSelectColumn($alias . '.active');
            $criteria->addSelectColumn($alias . '.statal_code');
            $criteria->addSelectColumn($alias . '.fiscal_city_code');
            $criteria->addSelectColumn($alias . '.birthplace');
            $criteria->addSelectColumn($alias . '.income');
            $criteria->addSelectColumn($alias . '.state_birth');
            $criteria->addSelectColumn($alias . '.citizenship');
            $criteria->addSelectColumn($alias . '.id_sissi');
            $criteria->addSelectColumn($alias . '.dom_first_prev_year');
            $criteria->addSelectColumn($alias . '.dom_last_prev_year');
            $criteria->addSelectColumn($alias . '.dom_first_curr_year');
            $criteria->addSelectColumn($alias . '.qualification');
            $criteria->addSelectColumn($alias . '.liquid_office_id');
            $criteria->addSelectColumn($alias . '.badge_number');
            $criteria->addSelectColumn($alias . '.tolerance_in');
            $criteria->addSelectColumn($alias . '.tolerance_out');
            $criteria->addSelectColumn($alias . '.flexibility');
            $criteria->addSelectColumn($alias . '.generic_tolerance');
            $criteria->addSelectColumn($alias . '.negative_round');
            $criteria->addSelectColumn($alias . '.recover_hours');
            $criteria->addSelectColumn($alias . '.max_extraordinary_in');
            $criteria->addSelectColumn($alias . '.max_extraordinary_out');
            $criteria->addSelectColumn($alias . '.min_extraordinary_in');
            $criteria->addSelectColumn($alias . '.min_extraordinary_out');
            $criteria->addSelectColumn($alias . '.step_out');
            $criteria->addSelectColumn($alias . '.step_in');
            $criteria->addSelectColumn($alias . '.max_break');
            $criteria->addSelectColumn($alias . '.max_cont_work');
            $criteria->addSelectColumn($alias . '.simplified_ata_settings');
            $criteria->addSelectColumn($alias . '.tolerance_in_und');
            $criteria->addSelectColumn($alias . '.tolerance_out_und');
            $criteria->addSelectColumn($alias . '.max_undefined_in');
            $criteria->addSelectColumn($alias . '.max_undefined_out');
            $criteria->addSelectColumn($alias . '.min_undefined_in');
            $criteria->addSelectColumn($alias . '.min_undefined_out');
            $criteria->addSelectColumn($alias . '.step_out_und');
            $criteria->addSelectColumn($alias . '.step_in_und');
            $criteria->addSelectColumn($alias . '.undefined_parameter_active');
            $criteria->addSelectColumn($alias . '.min_extraordinary_total');
            $criteria->addSelectColumn($alias . '.max_extraordinary_total');
            $criteria->addSelectColumn($alias . '.min_undefined_total');
            $criteria->addSelectColumn($alias . '.max_undefined_total');
            $criteria->addSelectColumn($alias . '.step_total_undefined');
            $criteria->addSelectColumn($alias . '.step_total_extraordinary');
            $criteria->addSelectColumn($alias . '.lunch_duration');
            $criteria->addSelectColumn($alias . '.lunch_deductible');
            $criteria->addSelectColumn($alias . '.service_deductible');
            $criteria->addSelectColumn($alias . '.min_undefined_lunch');
            $criteria->addSelectColumn($alias . '.min_extraordinary_lunch');
            $criteria->addSelectColumn($alias . '.max_undefined_lunch');
            $criteria->addSelectColumn($alias . '.max_extraordinary_lunch');
            $criteria->addSelectColumn($alias . '.step_lunch_undefined');
            $criteria->addSelectColumn($alias . '.step_lunch_extraordinary');
            $criteria->addSelectColumn($alias . '.break_after_max_work');
            $criteria->addSelectColumn($alias . '.unit_recover_hours');
            $criteria->addSelectColumn($alias . '.max_work');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            EmployeePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(EmployeePeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return Employee
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = EmployeePeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return EmployeePeer::populateObjects(EmployeePeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            EmployeePeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param Employee $obj A Employee object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getEmployeeId();
            } // if key === null
            EmployeePeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A Employee object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof Employee) {
                $key = (string) $value->getEmployeeId();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or Employee object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(EmployeePeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return Employee Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(EmployeePeer::$instances[$key])) {
                return EmployeePeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (EmployeePeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        EmployeePeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to employee
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
        // Invalidate objects in InstitutePeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        InstitutePeer::clearInstancePool();
        // Invalidate objects in AbsencesPeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        AbsencesPeer::clearInstancePool();
        // Invalidate objects in TimetablePeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        TimetablePeer::clearInstancePool();
        // Invalidate objects in PresencePeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        PresencePeer::clearInstancePool();
        // Invalidate objects in PersonnelStacksPeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        PersonnelStacksPeer::clearInstancePool();
        // Invalidate objects in StoredMonthPeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        StoredMonthPeer::clearInstancePool();
        // Invalidate objects in StoredDayPeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        StoredDayPeer::clearInstancePool();
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = EmployeePeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = EmployeePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = EmployeePeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                EmployeePeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (Employee object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = EmployeePeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + EmployeePeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = EmployeePeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            EmployeePeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }


    /**
     * Returns the number of rows matching criteria, joining the related ResidenceKey table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinResidenceKey(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            EmployeePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(EmployeePeer::RESIDENCE_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related AddressKey table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAddressKey(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            EmployeePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(EmployeePeer::ADDRESS_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Employee objects pre-filled with their Contact objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Employee objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinResidenceKey(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(EmployeePeer::DATABASE_NAME);
        }

        EmployeePeer::addSelectColumns($criteria);
        $startcol = EmployeePeer::NUM_HYDRATE_COLUMNS;
        ContactPeer::addSelectColumns($criteria);

        $criteria->addJoin(EmployeePeer::RESIDENCE_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = EmployeePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = EmployeePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = EmployeePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                EmployeePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = ContactPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = ContactPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    ContactPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Employee) to $obj2 (Contact)
                $obj2->addEmployeeRelatedByResidenceId($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Employee objects pre-filled with their Contact objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Employee objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAddressKey(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(EmployeePeer::DATABASE_NAME);
        }

        EmployeePeer::addSelectColumns($criteria);
        $startcol = EmployeePeer::NUM_HYDRATE_COLUMNS;
        ContactPeer::addSelectColumns($criteria);

        $criteria->addJoin(EmployeePeer::ADDRESS_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = EmployeePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = EmployeePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = EmployeePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                EmployeePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = ContactPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = ContactPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    ContactPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Employee) to $obj2 (Contact)
                $obj2->addEmployeeRelatedByAddressId($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining all related tables
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAll(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            EmployeePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(EmployeePeer::RESIDENCE_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $criteria->addJoin(EmployeePeer::ADDRESS_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }

    /**
     * Selects a collection of Employee objects pre-filled with all related objects.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Employee objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAll(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(EmployeePeer::DATABASE_NAME);
        }

        EmployeePeer::addSelectColumns($criteria);
        $startcol2 = EmployeePeer::NUM_HYDRATE_COLUMNS;

        ContactPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + ContactPeer::NUM_HYDRATE_COLUMNS;

        ContactPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + ContactPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(EmployeePeer::RESIDENCE_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $criteria->addJoin(EmployeePeer::ADDRESS_ID, ContactPeer::CONTACT_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = EmployeePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = EmployeePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = EmployeePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                EmployeePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            // Add objects for joined Contact rows

            $key2 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol2);
            if ($key2 !== null) {
                $obj2 = ContactPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = ContactPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    ContactPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 loaded

                // Add the $obj1 (Employee) to the collection in $obj2 (Contact)
                $obj2->addEmployeeRelatedByResidenceId($obj1);
            } // if joined row not null

            // Add objects for joined Contact rows

            $key3 = ContactPeer::getPrimaryKeyHashFromRow($row, $startcol3);
            if ($key3 !== null) {
                $obj3 = ContactPeer::getInstanceFromPool($key3);
                if (!$obj3) {

                    $cls = ContactPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    ContactPeer::addInstanceToPool($obj3, $key3);
                } // if obj3 loaded

                // Add the $obj1 (Employee) to the collection in $obj3 (Contact)
                $obj3->addEmployeeRelatedByAddressId($obj1);
            } // if joined row not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining the related ResidenceKey table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptResidenceKey(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            EmployeePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related AddressKey table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptAddressKey(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            EmployeePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Employee objects pre-filled with all related objects except ResidenceKey.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Employee objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptResidenceKey(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(EmployeePeer::DATABASE_NAME);
        }

        EmployeePeer::addSelectColumns($criteria);
        $startcol2 = EmployeePeer::NUM_HYDRATE_COLUMNS;


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = EmployeePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = EmployeePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = EmployeePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                EmployeePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Employee objects pre-filled with all related objects except AddressKey.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Employee objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptAddressKey(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(EmployeePeer::DATABASE_NAME);
        }

        EmployeePeer::addSelectColumns($criteria);
        $startcol2 = EmployeePeer::NUM_HYDRATE_COLUMNS;


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = EmployeePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = EmployeePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = EmployeePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                EmployeePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(EmployeePeer::DATABASE_NAME)->getTable(EmployeePeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseEmployeePeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseEmployeePeer::TABLE_NAME)) {
        $dbMap->addTableObject(new EmployeeTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return EmployeePeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a Employee or Criteria object.
     *
     * @param      mixed $values Criteria or Employee object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from Employee object
        }

        if ($criteria->containsKey(EmployeePeer::EMPLOYEE_ID) && $criteria->keyContainsValue(EmployeePeer::EMPLOYEE_ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.EmployeePeer::EMPLOYEE_ID.')');
        }


        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a Employee or Criteria object.
     *
     * @param      mixed $values Criteria or Employee object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(EmployeePeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(EmployeePeer::EMPLOYEE_ID);
            $value = $criteria->remove(EmployeePeer::EMPLOYEE_ID);
            if ($value) {
                $selectCriteria->add(EmployeePeer::EMPLOYEE_ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(EmployeePeer::TABLE_NAME);
            }

        } else { // $values is Employee object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the employee table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(EmployeePeer::TABLE_NAME, $con, EmployeePeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            EmployeePeer::clearInstancePool();
            EmployeePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a Employee or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or Employee object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            EmployeePeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof Employee) { // it's a model object
            // invalidate the cache for this single object
            EmployeePeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(EmployeePeer::DATABASE_NAME);
            $criteria->add(EmployeePeer::EMPLOYEE_ID, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                EmployeePeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(EmployeePeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            EmployeePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given Employee object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param Employee $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(EmployeePeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(EmployeePeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(EmployeePeer::DATABASE_NAME, EmployeePeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return Employee
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = EmployeePeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(EmployeePeer::DATABASE_NAME);
        $criteria->add(EmployeePeer::EMPLOYEE_ID, $pk);

        $v = EmployeePeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return Employee[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(EmployeePeer::DATABASE_NAME);
            $criteria->add(EmployeePeer::EMPLOYEE_ID, $pks, Criteria::IN);
            $objs = EmployeePeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseEmployeePeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseEmployeePeer::buildTableMap();

