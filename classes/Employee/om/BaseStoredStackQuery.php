<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceStack;
use Employee\StoredMonth;
use Employee\StoredStack;
use Employee\StoredStackPeer;
use Employee\StoredStackQuery;

/**
 * Base class that represents a query for the 'storage_personnel_stack' table.
 *
 *
 *
 * @method StoredStackQuery orderByStoredStackId($order = Criteria::ASC) Order by the id column
 * @method StoredStackQuery orderByStoredMonth($order = Criteria::ASC) Order by the storage_personnel_presences column
 * @method StoredStackQuery orderByAbsenceStack($order = Criteria::ASC) Order by the absence_stack column
 * @method StoredStackQuery orderByStackDenomination($order = Criteria::ASC) Order by the stack_denomination column
 * @method StoredStackQuery orderByValueStartOriginal($order = Criteria::ASC) Order by the value_start_o column
 * @method StoredStackQuery orderByValueEndOriginal($order = Criteria::ASC) Order by the value_end_o column
 * @method StoredStackQuery orderByValueStart($order = Criteria::ASC) Order by the value_start column
 * @method StoredStackQuery orderByValueEnd($order = Criteria::ASC) Order by the value_end column
 * @method StoredStackQuery orderByUnit($order = Criteria::ASC) Order by the unit column
 * @method StoredStackQuery orderByRecover($order = Criteria::ASC) Order by the recover column
 * @method StoredStackQuery orderByResetTypeApplied($order = Criteria::ASC) Order by the reset_type_applied column
 *
 * @method StoredStackQuery groupByStoredStackId() Group by the id column
 * @method StoredStackQuery groupByStoredMonth() Group by the storage_personnel_presences column
 * @method StoredStackQuery groupByAbsenceStack() Group by the absence_stack column
 * @method StoredStackQuery groupByStackDenomination() Group by the stack_denomination column
 * @method StoredStackQuery groupByValueStartOriginal() Group by the value_start_o column
 * @method StoredStackQuery groupByValueEndOriginal() Group by the value_end_o column
 * @method StoredStackQuery groupByValueStart() Group by the value_start column
 * @method StoredStackQuery groupByValueEnd() Group by the value_end column
 * @method StoredStackQuery groupByUnit() Group by the unit column
 * @method StoredStackQuery groupByRecover() Group by the recover column
 * @method StoredStackQuery groupByResetTypeApplied() Group by the reset_type_applied column
 *
 * @method StoredStackQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method StoredStackQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method StoredStackQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method StoredStackQuery leftJoinStoredStackAbsenceStack($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredStackAbsenceStack relation
 * @method StoredStackQuery rightJoinStoredStackAbsenceStack($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredStackAbsenceStack relation
 * @method StoredStackQuery innerJoinStoredStackAbsenceStack($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredStackAbsenceStack relation
 *
 * @method StoredStackQuery leftJoinStoredStackStoredMonth($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredStackStoredMonth relation
 * @method StoredStackQuery rightJoinStoredStackStoredMonth($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredStackStoredMonth relation
 * @method StoredStackQuery innerJoinStoredStackStoredMonth($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredStackStoredMonth relation
 *
 * @method StoredStack findOne(PropelPDO $con = null) Return the first StoredStack matching the query
 * @method StoredStack findOneOrCreate(PropelPDO $con = null) Return the first StoredStack matching the query, or a new StoredStack object populated from the query conditions when no match is found
 *
 * @method StoredStack findOneByStoredMonth(string $storage_personnel_presences) Return the first StoredStack filtered by the storage_personnel_presences column
 * @method StoredStack findOneByAbsenceStack(string $absence_stack) Return the first StoredStack filtered by the absence_stack column
 * @method StoredStack findOneByStackDenomination(string $stack_denomination) Return the first StoredStack filtered by the stack_denomination column
 * @method StoredStack findOneByValueStartOriginal(double $value_start_o) Return the first StoredStack filtered by the value_start_o column
 * @method StoredStack findOneByValueEndOriginal(double $value_end_o) Return the first StoredStack filtered by the value_end_o column
 * @method StoredStack findOneByValueStart(double $value_start) Return the first StoredStack filtered by the value_start column
 * @method StoredStack findOneByValueEnd(double $value_end) Return the first StoredStack filtered by the value_end column
 * @method StoredStack findOneByUnit(string $unit) Return the first StoredStack filtered by the unit column
 * @method StoredStack findOneByRecover(boolean $recover) Return the first StoredStack filtered by the recover column
 * @method StoredStack findOneByResetTypeApplied(int $reset_type_applied) Return the first StoredStack filtered by the reset_type_applied column
 *
 * @method array findByStoredStackId(int $id) Return StoredStack objects filtered by the id column
 * @method array findByStoredMonth(string $storage_personnel_presences) Return StoredStack objects filtered by the storage_personnel_presences column
 * @method array findByAbsenceStack(string $absence_stack) Return StoredStack objects filtered by the absence_stack column
 * @method array findByStackDenomination(string $stack_denomination) Return StoredStack objects filtered by the stack_denomination column
 * @method array findByValueStartOriginal(double $value_start_o) Return StoredStack objects filtered by the value_start_o column
 * @method array findByValueEndOriginal(double $value_end_o) Return StoredStack objects filtered by the value_end_o column
 * @method array findByValueStart(double $value_start) Return StoredStack objects filtered by the value_start column
 * @method array findByValueEnd(double $value_end) Return StoredStack objects filtered by the value_end column
 * @method array findByUnit(string $unit) Return StoredStack objects filtered by the unit column
 * @method array findByRecover(boolean $recover) Return StoredStack objects filtered by the recover column
 * @method array findByResetTypeApplied(int $reset_type_applied) Return StoredStack objects filtered by the reset_type_applied column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseStoredStackQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseStoredStackQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\StoredStack';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new StoredStackQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   StoredStackQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return StoredStackQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof StoredStackQuery) {
            return $criteria;
        }
        $query = new StoredStackQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   StoredStack|StoredStack[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = StoredStackPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 StoredStack A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByStoredStackId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 StoredStack A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "storage_personnel_presences", "absence_stack", "stack_denomination", "value_start_o", "value_end_o", "value_start", "value_end", "unit", "recover", "reset_type_applied" FROM "storage_personnel_stack" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new StoredStack();
            $obj->hydrate($row);
            StoredStackPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return StoredStack|StoredStack[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|StoredStack[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(StoredStackPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(StoredStackPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterByStoredStackId(1234); // WHERE id = 1234
     * $query->filterByStoredStackId(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterByStoredStackId(array('min' => 12)); // WHERE id >= 12
     * $query->filterByStoredStackId(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $storedStackId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByStoredStackId($storedStackId = null, $comparison = null)
    {
        if (is_array($storedStackId)) {
            $useMinMax = false;
            if (isset($storedStackId['min'])) {
                $this->addUsingAlias(StoredStackPeer::ID, $storedStackId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($storedStackId['max'])) {
                $this->addUsingAlias(StoredStackPeer::ID, $storedStackId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::ID, $storedStackId, $comparison);
    }

    /**
     * Filter the query on the storage_personnel_presences column
     *
     * Example usage:
     * <code>
     * $query->filterByStoredMonth(1234); // WHERE storage_personnel_presences = 1234
     * $query->filterByStoredMonth(array(12, 34)); // WHERE storage_personnel_presences IN (12, 34)
     * $query->filterByStoredMonth(array('min' => 12)); // WHERE storage_personnel_presences >= 12
     * $query->filterByStoredMonth(array('max' => 12)); // WHERE storage_personnel_presences <= 12
     * </code>
     *
     * @see       filterByStoredStackStoredMonth()
     *
     * @param     mixed $storedMonth The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByStoredMonth($storedMonth = null, $comparison = null)
    {
        if (is_array($storedMonth)) {
            $useMinMax = false;
            if (isset($storedMonth['min'])) {
                $this->addUsingAlias(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, $storedMonth['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($storedMonth['max'])) {
                $this->addUsingAlias(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, $storedMonth['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, $storedMonth, $comparison);
    }

    /**
     * Filter the query on the absence_stack column
     *
     * Example usage:
     * <code>
     * $query->filterByAbsenceStack(1234); // WHERE absence_stack = 1234
     * $query->filterByAbsenceStack(array(12, 34)); // WHERE absence_stack IN (12, 34)
     * $query->filterByAbsenceStack(array('min' => 12)); // WHERE absence_stack >= 12
     * $query->filterByAbsenceStack(array('max' => 12)); // WHERE absence_stack <= 12
     * </code>
     *
     * @see       filterByStoredStackAbsenceStack()
     *
     * @param     mixed $absenceStack The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByAbsenceStack($absenceStack = null, $comparison = null)
    {
        if (is_array($absenceStack)) {
            $useMinMax = false;
            if (isset($absenceStack['min'])) {
                $this->addUsingAlias(StoredStackPeer::ABSENCE_STACK, $absenceStack['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($absenceStack['max'])) {
                $this->addUsingAlias(StoredStackPeer::ABSENCE_STACK, $absenceStack['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::ABSENCE_STACK, $absenceStack, $comparison);
    }

    /**
     * Filter the query on the stack_denomination column
     *
     * Example usage:
     * <code>
     * $query->filterByStackDenomination('fooValue');   // WHERE stack_denomination = 'fooValue'
     * $query->filterByStackDenomination('%fooValue%'); // WHERE stack_denomination LIKE '%fooValue%'
     * </code>
     *
     * @param     string $stackDenomination The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByStackDenomination($stackDenomination = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($stackDenomination)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $stackDenomination)) {
                $stackDenomination = str_replace('*', '%', $stackDenomination);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::STACK_DENOMINATION, $stackDenomination, $comparison);
    }

    /**
     * Filter the query on the value_start_o column
     *
     * Example usage:
     * <code>
     * $query->filterByValueStartOriginal(1234); // WHERE value_start_o = 1234
     * $query->filterByValueStartOriginal(array(12, 34)); // WHERE value_start_o IN (12, 34)
     * $query->filterByValueStartOriginal(array('min' => 12)); // WHERE value_start_o >= 12
     * $query->filterByValueStartOriginal(array('max' => 12)); // WHERE value_start_o <= 12
     * </code>
     *
     * @param     mixed $valueStartOriginal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByValueStartOriginal($valueStartOriginal = null, $comparison = null)
    {
        if (is_array($valueStartOriginal)) {
            $useMinMax = false;
            if (isset($valueStartOriginal['min'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_START_O, $valueStartOriginal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($valueStartOriginal['max'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_START_O, $valueStartOriginal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::VALUE_START_O, $valueStartOriginal, $comparison);
    }

    /**
     * Filter the query on the value_end_o column
     *
     * Example usage:
     * <code>
     * $query->filterByValueEndOriginal(1234); // WHERE value_end_o = 1234
     * $query->filterByValueEndOriginal(array(12, 34)); // WHERE value_end_o IN (12, 34)
     * $query->filterByValueEndOriginal(array('min' => 12)); // WHERE value_end_o >= 12
     * $query->filterByValueEndOriginal(array('max' => 12)); // WHERE value_end_o <= 12
     * </code>
     *
     * @param     mixed $valueEndOriginal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByValueEndOriginal($valueEndOriginal = null, $comparison = null)
    {
        if (is_array($valueEndOriginal)) {
            $useMinMax = false;
            if (isset($valueEndOriginal['min'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_END_O, $valueEndOriginal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($valueEndOriginal['max'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_END_O, $valueEndOriginal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::VALUE_END_O, $valueEndOriginal, $comparison);
    }

    /**
     * Filter the query on the value_start column
     *
     * Example usage:
     * <code>
     * $query->filterByValueStart(1234); // WHERE value_start = 1234
     * $query->filterByValueStart(array(12, 34)); // WHERE value_start IN (12, 34)
     * $query->filterByValueStart(array('min' => 12)); // WHERE value_start >= 12
     * $query->filterByValueStart(array('max' => 12)); // WHERE value_start <= 12
     * </code>
     *
     * @param     mixed $valueStart The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByValueStart($valueStart = null, $comparison = null)
    {
        if (is_array($valueStart)) {
            $useMinMax = false;
            if (isset($valueStart['min'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_START, $valueStart['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($valueStart['max'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_START, $valueStart['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::VALUE_START, $valueStart, $comparison);
    }

    /**
     * Filter the query on the value_end column
     *
     * Example usage:
     * <code>
     * $query->filterByValueEnd(1234); // WHERE value_end = 1234
     * $query->filterByValueEnd(array(12, 34)); // WHERE value_end IN (12, 34)
     * $query->filterByValueEnd(array('min' => 12)); // WHERE value_end >= 12
     * $query->filterByValueEnd(array('max' => 12)); // WHERE value_end <= 12
     * </code>
     *
     * @param     mixed $valueEnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByValueEnd($valueEnd = null, $comparison = null)
    {
        if (is_array($valueEnd)) {
            $useMinMax = false;
            if (isset($valueEnd['min'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_END, $valueEnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($valueEnd['max'])) {
                $this->addUsingAlias(StoredStackPeer::VALUE_END, $valueEnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::VALUE_END, $valueEnd, $comparison);
    }

    /**
     * Filter the query on the unit column
     *
     * Example usage:
     * <code>
     * $query->filterByUnit('fooValue');   // WHERE unit = 'fooValue'
     * $query->filterByUnit('%fooValue%'); // WHERE unit LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unit The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByUnit($unit = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unit)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unit)) {
                $unit = str_replace('*', '%', $unit);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::UNIT, $unit, $comparison);
    }

    /**
     * Filter the query on the recover column
     *
     * Example usage:
     * <code>
     * $query->filterByRecover(true); // WHERE recover = true
     * $query->filterByRecover('yes'); // WHERE recover = true
     * </code>
     *
     * @param     boolean|string $recover The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByRecover($recover = null, $comparison = null)
    {
        if (is_string($recover)) {
            $recover = in_array(strtolower($recover), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(StoredStackPeer::RECOVER, $recover, $comparison);
    }

    /**
     * Filter the query on the reset_type_applied column
     *
     * Example usage:
     * <code>
     * $query->filterByResetTypeApplied(1234); // WHERE reset_type_applied = 1234
     * $query->filterByResetTypeApplied(array(12, 34)); // WHERE reset_type_applied IN (12, 34)
     * $query->filterByResetTypeApplied(array('min' => 12)); // WHERE reset_type_applied >= 12
     * $query->filterByResetTypeApplied(array('max' => 12)); // WHERE reset_type_applied <= 12
     * </code>
     *
     * @param     mixed $resetTypeApplied The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function filterByResetTypeApplied($resetTypeApplied = null, $comparison = null)
    {
        if (is_array($resetTypeApplied)) {
            $useMinMax = false;
            if (isset($resetTypeApplied['min'])) {
                $this->addUsingAlias(StoredStackPeer::RESET_TYPE_APPLIED, $resetTypeApplied['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($resetTypeApplied['max'])) {
                $this->addUsingAlias(StoredStackPeer::RESET_TYPE_APPLIED, $resetTypeApplied['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredStackPeer::RESET_TYPE_APPLIED, $resetTypeApplied, $comparison);
    }

    /**
     * Filter the query by a related AbsenceStack object
     *
     * @param   AbsenceStack|PropelObjectCollection $absenceStack The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 StoredStackQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredStackAbsenceStack($absenceStack, $comparison = null)
    {
        if ($absenceStack instanceof AbsenceStack) {
            return $this
                ->addUsingAlias(StoredStackPeer::ABSENCE_STACK, $absenceStack->getId(), $comparison);
        } elseif ($absenceStack instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(StoredStackPeer::ABSENCE_STACK, $absenceStack->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByStoredStackAbsenceStack() only accepts arguments of type AbsenceStack or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredStackAbsenceStack relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function joinStoredStackAbsenceStack($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredStackAbsenceStack');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredStackAbsenceStack');
        }

        return $this;
    }

    /**
     * Use the StoredStackAbsenceStack relation AbsenceStack object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsenceStackQuery A secondary query class using the current class as primary query
     */
    public function useStoredStackAbsenceStackQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinStoredStackAbsenceStack($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredStackAbsenceStack', '\Employee\AbsenceStackQuery');
    }

    /**
     * Filter the query by a related StoredMonth object
     *
     * @param   StoredMonth|PropelObjectCollection $storedMonth The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 StoredStackQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredStackStoredMonth($storedMonth, $comparison = null)
    {
        if ($storedMonth instanceof StoredMonth) {
            return $this
                ->addUsingAlias(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, $storedMonth->getStoredMonthId(), $comparison);
        } elseif ($storedMonth instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, $storedMonth->toKeyValue('PrimaryKey', 'StoredMonthId'), $comparison);
        } else {
            throw new PropelException('filterByStoredStackStoredMonth() only accepts arguments of type StoredMonth or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredStackStoredMonth relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function joinStoredStackStoredMonth($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredStackStoredMonth');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredStackStoredMonth');
        }

        return $this;
    }

    /**
     * Use the StoredStackStoredMonth relation StoredMonth object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\StoredMonthQuery A secondary query class using the current class as primary query
     */
    public function useStoredStackStoredMonthQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinStoredStackStoredMonth($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredStackStoredMonth', '\Employee\StoredMonthQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   StoredStack $storedStack Object to remove from the list of results
     *
     * @return StoredStackQuery The current query, for fluid interface
     */
    public function prune($storedStack = null)
    {
        if ($storedStack) {
            $this->addUsingAlias(StoredStackPeer::ID, $storedStack->getStoredStackId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
