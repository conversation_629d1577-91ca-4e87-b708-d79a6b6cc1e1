<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\Employee;
use Employee\Timetable;
use Employee\TimetablePeer;
use Employee\TimetableQuery;

/**
 * Base class that represents a query for the 'personnel_timetable' table.
 *
 *
 *
 * @method TimetableQuery orderByPersonnelTimetableId($order = Criteria::ASC) Order by the personnel_timetable_id column
 * @method TimetableQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method TimetableQuery orderByDateStart($order = Criteria::ASC) Order by the date_start column
 * @method TimetableQuery orderByDateEnd($order = Criteria::ASC) Order by the date_end column
 * @method TimetableQuery orderByDateStartPause($order = Criteria::ASC) Order by the date_start_pause column
 * @method TimetableQuery orderByDateEndPause($order = Criteria::ASC) Order by the date_end_pause column
 *
 * @method TimetableQuery groupByPersonnelTimetableId() Group by the personnel_timetable_id column
 * @method TimetableQuery groupByEmployeeId() Group by the employee_id column
 * @method TimetableQuery groupByDateStart() Group by the date_start column
 * @method TimetableQuery groupByDateEnd() Group by the date_end column
 * @method TimetableQuery groupByDateStartPause() Group by the date_start_pause column
 * @method TimetableQuery groupByDateEndPause() Group by the date_end_pause column
 *
 * @method TimetableQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method TimetableQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method TimetableQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method TimetableQuery leftJoinTimetableEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the TimetableEmployee relation
 * @method TimetableQuery rightJoinTimetableEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the TimetableEmployee relation
 * @method TimetableQuery innerJoinTimetableEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the TimetableEmployee relation
 *
 * @method Timetable findOne(PropelPDO $con = null) Return the first Timetable matching the query
 * @method Timetable findOneOrCreate(PropelPDO $con = null) Return the first Timetable matching the query, or a new Timetable object populated from the query conditions when no match is found
 *
 * @method Timetable findOneByEmployeeId(string $employee_id) Return the first Timetable filtered by the employee_id column
 * @method Timetable findOneByDateStart(string $date_start) Return the first Timetable filtered by the date_start column
 * @method Timetable findOneByDateEnd(string $date_end) Return the first Timetable filtered by the date_end column
 * @method Timetable findOneByDateStartPause(string $date_start_pause) Return the first Timetable filtered by the date_start_pause column
 * @method Timetable findOneByDateEndPause(string $date_end_pause) Return the first Timetable filtered by the date_end_pause column
 *
 * @method array findByPersonnelTimetableId(int $personnel_timetable_id) Return Timetable objects filtered by the personnel_timetable_id column
 * @method array findByEmployeeId(string $employee_id) Return Timetable objects filtered by the employee_id column
 * @method array findByDateStart(string $date_start) Return Timetable objects filtered by the date_start column
 * @method array findByDateEnd(string $date_end) Return Timetable objects filtered by the date_end column
 * @method array findByDateStartPause(string $date_start_pause) Return Timetable objects filtered by the date_start_pause column
 * @method array findByDateEndPause(string $date_end_pause) Return Timetable objects filtered by the date_end_pause column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseTimetableQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseTimetableQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\Timetable';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new TimetableQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   TimetableQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return TimetableQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof TimetableQuery) {
            return $criteria;
        }
        $query = new TimetableQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Timetable|Timetable[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = TimetablePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(TimetablePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Timetable A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByPersonnelTimetableId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Timetable A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "personnel_timetable_id", "employee_id", "date_start", "date_end", "date_start_pause", "date_end_pause" FROM "personnel_timetable" WHERE "personnel_timetable_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Timetable();
            $obj->hydrate($row);
            TimetablePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Timetable|Timetable[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Timetable[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(TimetablePeer::PERSONNEL_TIMETABLE_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(TimetablePeer::PERSONNEL_TIMETABLE_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the personnel_timetable_id column
     *
     * Example usage:
     * <code>
     * $query->filterByPersonnelTimetableId(1234); // WHERE personnel_timetable_id = 1234
     * $query->filterByPersonnelTimetableId(array(12, 34)); // WHERE personnel_timetable_id IN (12, 34)
     * $query->filterByPersonnelTimetableId(array('min' => 12)); // WHERE personnel_timetable_id >= 12
     * $query->filterByPersonnelTimetableId(array('max' => 12)); // WHERE personnel_timetable_id <= 12
     * </code>
     *
     * @param     mixed $personnelTimetableId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByPersonnelTimetableId($personnelTimetableId = null, $comparison = null)
    {
        if (is_array($personnelTimetableId)) {
            $useMinMax = false;
            if (isset($personnelTimetableId['min'])) {
                $this->addUsingAlias(TimetablePeer::PERSONNEL_TIMETABLE_ID, $personnelTimetableId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($personnelTimetableId['max'])) {
                $this->addUsingAlias(TimetablePeer::PERSONNEL_TIMETABLE_ID, $personnelTimetableId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TimetablePeer::PERSONNEL_TIMETABLE_ID, $personnelTimetableId, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @see       filterByTimetableEmployee()
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(TimetablePeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(TimetablePeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TimetablePeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the date_start column
     *
     * Example usage:
     * <code>
     * $query->filterByDateStart(1234); // WHERE date_start = 1234
     * $query->filterByDateStart(array(12, 34)); // WHERE date_start IN (12, 34)
     * $query->filterByDateStart(array('min' => 12)); // WHERE date_start >= 12
     * $query->filterByDateStart(array('max' => 12)); // WHERE date_start <= 12
     * </code>
     *
     * @param     mixed $dateStart The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByDateStart($dateStart = null, $comparison = null)
    {
        if (is_array($dateStart)) {
            $useMinMax = false;
            if (isset($dateStart['min'])) {
                $this->addUsingAlias(TimetablePeer::DATE_START, $dateStart['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateStart['max'])) {
                $this->addUsingAlias(TimetablePeer::DATE_START, $dateStart['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TimetablePeer::DATE_START, $dateStart, $comparison);
    }

    /**
     * Filter the query on the date_end column
     *
     * Example usage:
     * <code>
     * $query->filterByDateEnd(1234); // WHERE date_end = 1234
     * $query->filterByDateEnd(array(12, 34)); // WHERE date_end IN (12, 34)
     * $query->filterByDateEnd(array('min' => 12)); // WHERE date_end >= 12
     * $query->filterByDateEnd(array('max' => 12)); // WHERE date_end <= 12
     * </code>
     *
     * @param     mixed $dateEnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByDateEnd($dateEnd = null, $comparison = null)
    {
        if (is_array($dateEnd)) {
            $useMinMax = false;
            if (isset($dateEnd['min'])) {
                $this->addUsingAlias(TimetablePeer::DATE_END, $dateEnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateEnd['max'])) {
                $this->addUsingAlias(TimetablePeer::DATE_END, $dateEnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TimetablePeer::DATE_END, $dateEnd, $comparison);
    }

    /**
     * Filter the query on the date_start_pause column
     *
     * Example usage:
     * <code>
     * $query->filterByDateStartPause(1234); // WHERE date_start_pause = 1234
     * $query->filterByDateStartPause(array(12, 34)); // WHERE date_start_pause IN (12, 34)
     * $query->filterByDateStartPause(array('min' => 12)); // WHERE date_start_pause >= 12
     * $query->filterByDateStartPause(array('max' => 12)); // WHERE date_start_pause <= 12
     * </code>
     *
     * @param     mixed $dateStartPause The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByDateStartPause($dateStartPause = null, $comparison = null)
    {
        if (is_array($dateStartPause)) {
            $useMinMax = false;
            if (isset($dateStartPause['min'])) {
                $this->addUsingAlias(TimetablePeer::DATE_START_PAUSE, $dateStartPause['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateStartPause['max'])) {
                $this->addUsingAlias(TimetablePeer::DATE_START_PAUSE, $dateStartPause['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TimetablePeer::DATE_START_PAUSE, $dateStartPause, $comparison);
    }

    /**
     * Filter the query on the date_end_pause column
     *
     * Example usage:
     * <code>
     * $query->filterByDateEndPause(1234); // WHERE date_end_pause = 1234
     * $query->filterByDateEndPause(array(12, 34)); // WHERE date_end_pause IN (12, 34)
     * $query->filterByDateEndPause(array('min' => 12)); // WHERE date_end_pause >= 12
     * $query->filterByDateEndPause(array('max' => 12)); // WHERE date_end_pause <= 12
     * </code>
     *
     * @param     mixed $dateEndPause The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function filterByDateEndPause($dateEndPause = null, $comparison = null)
    {
        if (is_array($dateEndPause)) {
            $useMinMax = false;
            if (isset($dateEndPause['min'])) {
                $this->addUsingAlias(TimetablePeer::DATE_END_PAUSE, $dateEndPause['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateEndPause['max'])) {
                $this->addUsingAlias(TimetablePeer::DATE_END_PAUSE, $dateEndPause['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TimetablePeer::DATE_END_PAUSE, $dateEndPause, $comparison);
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TimetableQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTimetableEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(TimetablePeer::EMPLOYEE_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(TimetablePeer::EMPLOYEE_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByTimetableEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the TimetableEmployee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function joinTimetableEmployee($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('TimetableEmployee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'TimetableEmployee');
        }

        return $this;
    }

    /**
     * Use the TimetableEmployee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useTimetableEmployeeQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinTimetableEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'TimetableEmployee', '\Employee\EmployeeQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Timetable $timetable Object to remove from the list of results
     *
     * @return TimetableQuery The current query, for fluid interface
     */
    public function prune($timetable = null)
    {
        if ($timetable) {
            $this->addUsingAlias(TimetablePeer::PERSONNEL_TIMETABLE_ID, $timetable->getPersonnelTimetableId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
