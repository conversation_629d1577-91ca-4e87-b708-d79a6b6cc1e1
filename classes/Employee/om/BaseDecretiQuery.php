<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \PDO;
use \Propel;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\Decreti;
use Employee\DecretiPeer;
use Employee\DecretiQuery;

/**
 * Base class that represents a query for the 'decreti' table.
 *
 *
 *
 * @method DecretiQuery orderById($order = Criteria::ASC) Order by the id column
 * @method DecretiQuery orderByAbsenceKind($order = Criteria::ASC) Order by the absence_kind column
 * @method DecretiQuery orderByEmployeeKind($order = Criteria::ASC) Order by the employee_kind column
 * @method DecretiQuery orderByEmployeeRole($order = Criteria::ASC) Order by the employee_role column
 * @method DecretiQuery orderByHtml($order = Criteria::ASC) Order by the html column
 * @method DecretiQuery orderByTimeBack($order = Criteria::ASC) Order by the time_back column
 * @method DecretiQuery orderByPrevAbsKind($order = Criteria::ASC) Order by the prev_abs_kind column
 *
 * @method DecretiQuery groupById() Group by the id column
 * @method DecretiQuery groupByAbsenceKind() Group by the absence_kind column
 * @method DecretiQuery groupByEmployeeKind() Group by the employee_kind column
 * @method DecretiQuery groupByEmployeeRole() Group by the employee_role column
 * @method DecretiQuery groupByHtml() Group by the html column
 * @method DecretiQuery groupByTimeBack() Group by the time_back column
 * @method DecretiQuery groupByPrevAbsKind() Group by the prev_abs_kind column
 *
 * @method DecretiQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method DecretiQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method DecretiQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method Decreti findOne(PropelPDO $con = null) Return the first Decreti matching the query
 * @method Decreti findOneOrCreate(PropelPDO $con = null) Return the first Decreti matching the query, or a new Decreti object populated from the query conditions when no match is found
 *
 * @method Decreti findOneByAbsenceKind(string $absence_kind) Return the first Decreti filtered by the absence_kind column
 * @method Decreti findOneByEmployeeKind(string $employee_kind) Return the first Decreti filtered by the employee_kind column
 * @method Decreti findOneByEmployeeRole(string $employee_role) Return the first Decreti filtered by the employee_role column
 * @method Decreti findOneByHtml(string $html) Return the first Decreti filtered by the html column
 * @method Decreti findOneByTimeBack(string $time_back) Return the first Decreti filtered by the time_back column
 * @method Decreti findOneByPrevAbsKind(string $prev_abs_kind) Return the first Decreti filtered by the prev_abs_kind column
 *
 * @method array findById(int $id) Return Decreti objects filtered by the id column
 * @method array findByAbsenceKind(string $absence_kind) Return Decreti objects filtered by the absence_kind column
 * @method array findByEmployeeKind(string $employee_kind) Return Decreti objects filtered by the employee_kind column
 * @method array findByEmployeeRole(string $employee_role) Return Decreti objects filtered by the employee_role column
 * @method array findByHtml(string $html) Return Decreti objects filtered by the html column
 * @method array findByTimeBack(string $time_back) Return Decreti objects filtered by the time_back column
 * @method array findByPrevAbsKind(string $prev_abs_kind) Return Decreti objects filtered by the prev_abs_kind column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseDecretiQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseDecretiQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\Decreti';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new DecretiQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   DecretiQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return DecretiQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof DecretiQuery) {
            return $criteria;
        }
        $query = new DecretiQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Decreti|Decreti[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = DecretiPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(DecretiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Decreti A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Decreti A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "absence_kind", "employee_kind", "employee_role", "html", "time_back", "prev_abs_kind" FROM "decreti" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Decreti();
            $obj->hydrate($row);
            DecretiPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Decreti|Decreti[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Decreti[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(DecretiPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(DecretiPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(DecretiPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(DecretiPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(DecretiPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the absence_kind column
     *
     * Example usage:
     * <code>
     * $query->filterByAbsenceKind('fooValue');   // WHERE absence_kind = 'fooValue'
     * $query->filterByAbsenceKind('%fooValue%'); // WHERE absence_kind LIKE '%fooValue%'
     * </code>
     *
     * @param     string $absenceKind The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByAbsenceKind($absenceKind = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($absenceKind)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $absenceKind)) {
                $absenceKind = str_replace('*', '%', $absenceKind);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(DecretiPeer::ABSENCE_KIND, $absenceKind, $comparison);
    }

    /**
     * Filter the query on the employee_kind column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeKind('fooValue');   // WHERE employee_kind = 'fooValue'
     * $query->filterByEmployeeKind('%fooValue%'); // WHERE employee_kind LIKE '%fooValue%'
     * </code>
     *
     * @param     string $employeeKind The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByEmployeeKind($employeeKind = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($employeeKind)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $employeeKind)) {
                $employeeKind = str_replace('*', '%', $employeeKind);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(DecretiPeer::EMPLOYEE_KIND, $employeeKind, $comparison);
    }

    /**
     * Filter the query on the employee_role column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeRole('fooValue');   // WHERE employee_role = 'fooValue'
     * $query->filterByEmployeeRole('%fooValue%'); // WHERE employee_role LIKE '%fooValue%'
     * </code>
     *
     * @param     string $employeeRole The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByEmployeeRole($employeeRole = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($employeeRole)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $employeeRole)) {
                $employeeRole = str_replace('*', '%', $employeeRole);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(DecretiPeer::EMPLOYEE_ROLE, $employeeRole, $comparison);
    }

    /**
     * Filter the query on the html column
     *
     * Example usage:
     * <code>
     * $query->filterByHtml('fooValue');   // WHERE html = 'fooValue'
     * $query->filterByHtml('%fooValue%'); // WHERE html LIKE '%fooValue%'
     * </code>
     *
     * @param     string $html The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByHtml($html = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($html)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $html)) {
                $html = str_replace('*', '%', $html);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(DecretiPeer::HTML, $html, $comparison);
    }

    /**
     * Filter the query on the time_back column
     *
     * Example usage:
     * <code>
     * $query->filterByTimeBack('fooValue');   // WHERE time_back = 'fooValue'
     * $query->filterByTimeBack('%fooValue%'); // WHERE time_back LIKE '%fooValue%'
     * </code>
     *
     * @param     string $timeBack The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByTimeBack($timeBack = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($timeBack)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $timeBack)) {
                $timeBack = str_replace('*', '%', $timeBack);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(DecretiPeer::TIME_BACK, $timeBack, $comparison);
    }

    /**
     * Filter the query on the prev_abs_kind column
     *
     * Example usage:
     * <code>
     * $query->filterByPrevAbsKind('fooValue');   // WHERE prev_abs_kind = 'fooValue'
     * $query->filterByPrevAbsKind('%fooValue%'); // WHERE prev_abs_kind LIKE '%fooValue%'
     * </code>
     *
     * @param     string $prevAbsKind The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function filterByPrevAbsKind($prevAbsKind = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($prevAbsKind)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $prevAbsKind)) {
                $prevAbsKind = str_replace('*', '%', $prevAbsKind);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(DecretiPeer::PREV_ABS_KIND, $prevAbsKind, $comparison);
    }

    /**
     * Exclude object from result
     *
     * @param   Decreti $decreti Object to remove from the list of results
     *
     * @return DecretiQuery The current query, for fluid interface
     */
    public function prune($decreti = null)
    {
        if ($decreti) {
            $this->addUsingAlias(DecretiPeer::ID, $decreti->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
