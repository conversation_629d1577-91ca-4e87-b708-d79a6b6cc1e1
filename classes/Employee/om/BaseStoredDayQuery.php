<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\Employee;
use Employee\StoredDay;
use Employee\StoredDayPeer;
use Employee\StoredDayQuery;

/**
 * Base class that represents a query for the 'extraordinary_stored' table.
 *
 *
 *
 * @method StoredDayQuery orderByStoredDayId($order = Criteria::ASC) Order by the extraordinary_stored_id column
 * @method StoredDayQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method StoredDayQuery orderByDate($order = Criteria::ASC) Order by the date column
 * @method StoredDayQuery orderByExtraordinary($order = Criteria::ASC) Order by the extraordinary column
 * @method StoredDayQuery orderByAuthorized($order = Criteria::ASC) Order by the authorized column
 * @method StoredDayQuery orderByNote($order = Criteria::ASC) Order by the note column
 *
 * @method StoredDayQuery groupByStoredDayId() Group by the extraordinary_stored_id column
 * @method StoredDayQuery groupByEmployeeId() Group by the employee_id column
 * @method StoredDayQuery groupByDate() Group by the date column
 * @method StoredDayQuery groupByExtraordinary() Group by the extraordinary column
 * @method StoredDayQuery groupByAuthorized() Group by the authorized column
 * @method StoredDayQuery groupByNote() Group by the note column
 *
 * @method StoredDayQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method StoredDayQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method StoredDayQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method StoredDayQuery leftJoinStoredDayEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredDayEmployee relation
 * @method StoredDayQuery rightJoinStoredDayEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredDayEmployee relation
 * @method StoredDayQuery innerJoinStoredDayEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredDayEmployee relation
 *
 * @method StoredDay findOne(PropelPDO $con = null) Return the first StoredDay matching the query
 * @method StoredDay findOneOrCreate(PropelPDO $con = null) Return the first StoredDay matching the query, or a new StoredDay object populated from the query conditions when no match is found
 *
 * @method StoredDay findOneByEmployeeId(string $employee_id) Return the first StoredDay filtered by the employee_id column
 * @method StoredDay findOneByDate(string $date) Return the first StoredDay filtered by the date column
 * @method StoredDay findOneByExtraordinary(int $extraordinary) Return the first StoredDay filtered by the extraordinary column
 * @method StoredDay findOneByAuthorized(int $authorized) Return the first StoredDay filtered by the authorized column
 * @method StoredDay findOneByNote(string $note) Return the first StoredDay filtered by the note column
 *
 * @method array findByStoredDayId(int $extraordinary_stored_id) Return StoredDay objects filtered by the extraordinary_stored_id column
 * @method array findByEmployeeId(string $employee_id) Return StoredDay objects filtered by the employee_id column
 * @method array findByDate(string $date) Return StoredDay objects filtered by the date column
 * @method array findByExtraordinary(int $extraordinary) Return StoredDay objects filtered by the extraordinary column
 * @method array findByAuthorized(int $authorized) Return StoredDay objects filtered by the authorized column
 * @method array findByNote(string $note) Return StoredDay objects filtered by the note column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseStoredDayQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseStoredDayQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\StoredDay';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new StoredDayQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   StoredDayQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return StoredDayQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof StoredDayQuery) {
            return $criteria;
        }
        $query = new StoredDayQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   StoredDay|StoredDay[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = StoredDayPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(StoredDayPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 StoredDay A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByStoredDayId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 StoredDay A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "extraordinary_stored_id", "employee_id", "date", "extraordinary", "authorized", "note" FROM "extraordinary_stored" WHERE "extraordinary_stored_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new StoredDay();
            $obj->hydrate($row);
            StoredDayPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return StoredDay|StoredDay[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|StoredDay[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY_STORED_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY_STORED_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the extraordinary_stored_id column
     *
     * Example usage:
     * <code>
     * $query->filterByStoredDayId(1234); // WHERE extraordinary_stored_id = 1234
     * $query->filterByStoredDayId(array(12, 34)); // WHERE extraordinary_stored_id IN (12, 34)
     * $query->filterByStoredDayId(array('min' => 12)); // WHERE extraordinary_stored_id >= 12
     * $query->filterByStoredDayId(array('max' => 12)); // WHERE extraordinary_stored_id <= 12
     * </code>
     *
     * @param     mixed $storedDayId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByStoredDayId($storedDayId = null, $comparison = null)
    {
        if (is_array($storedDayId)) {
            $useMinMax = false;
            if (isset($storedDayId['min'])) {
                $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY_STORED_ID, $storedDayId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($storedDayId['max'])) {
                $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY_STORED_ID, $storedDayId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY_STORED_ID, $storedDayId, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @see       filterByStoredDayEmployee()
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(StoredDayPeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(StoredDayPeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredDayPeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the date column
     *
     * Example usage:
     * <code>
     * $query->filterByDate(1234); // WHERE date = 1234
     * $query->filterByDate(array(12, 34)); // WHERE date IN (12, 34)
     * $query->filterByDate(array('min' => 12)); // WHERE date >= 12
     * $query->filterByDate(array('max' => 12)); // WHERE date <= 12
     * </code>
     *
     * @param     mixed $date The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByDate($date = null, $comparison = null)
    {
        if (is_array($date)) {
            $useMinMax = false;
            if (isset($date['min'])) {
                $this->addUsingAlias(StoredDayPeer::DATE, $date['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($date['max'])) {
                $this->addUsingAlias(StoredDayPeer::DATE, $date['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredDayPeer::DATE, $date, $comparison);
    }

    /**
     * Filter the query on the extraordinary column
     *
     * Example usage:
     * <code>
     * $query->filterByExtraordinary(1234); // WHERE extraordinary = 1234
     * $query->filterByExtraordinary(array(12, 34)); // WHERE extraordinary IN (12, 34)
     * $query->filterByExtraordinary(array('min' => 12)); // WHERE extraordinary >= 12
     * $query->filterByExtraordinary(array('max' => 12)); // WHERE extraordinary <= 12
     * </code>
     *
     * @param     mixed $extraordinary The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByExtraordinary($extraordinary = null, $comparison = null)
    {
        if (is_array($extraordinary)) {
            $useMinMax = false;
            if (isset($extraordinary['min'])) {
                $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY, $extraordinary['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($extraordinary['max'])) {
                $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY, $extraordinary['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY, $extraordinary, $comparison);
    }

    /**
     * Filter the query on the authorized column
     *
     * Example usage:
     * <code>
     * $query->filterByAuthorized(1234); // WHERE authorized = 1234
     * $query->filterByAuthorized(array(12, 34)); // WHERE authorized IN (12, 34)
     * $query->filterByAuthorized(array('min' => 12)); // WHERE authorized >= 12
     * $query->filterByAuthorized(array('max' => 12)); // WHERE authorized <= 12
     * </code>
     *
     * @param     mixed $authorized The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByAuthorized($authorized = null, $comparison = null)
    {
        if (is_array($authorized)) {
            $useMinMax = false;
            if (isset($authorized['min'])) {
                $this->addUsingAlias(StoredDayPeer::AUTHORIZED, $authorized['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($authorized['max'])) {
                $this->addUsingAlias(StoredDayPeer::AUTHORIZED, $authorized['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredDayPeer::AUTHORIZED, $authorized, $comparison);
    }

    /**
     * Filter the query on the note column
     *
     * Example usage:
     * <code>
     * $query->filterByNote('fooValue');   // WHERE note = 'fooValue'
     * $query->filterByNote('%fooValue%'); // WHERE note LIKE '%fooValue%'
     * </code>
     *
     * @param     string $note The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function filterByNote($note = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($note)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $note)) {
                $note = str_replace('*', '%', $note);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StoredDayPeer::NOTE, $note, $comparison);
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 StoredDayQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredDayEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(StoredDayPeer::EMPLOYEE_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(StoredDayPeer::EMPLOYEE_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByStoredDayEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredDayEmployee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function joinStoredDayEmployee($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredDayEmployee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredDayEmployee');
        }

        return $this;
    }

    /**
     * Use the StoredDayEmployee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useStoredDayEmployeeQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinStoredDayEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredDayEmployee', '\Employee\EmployeeQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   StoredDay $storedDay Object to remove from the list of results
     *
     * @return StoredDayQuery The current query, for fluid interface
     */
    public function prune($storedDay = null)
    {
        if ($storedDay) {
            $this->addUsingAlias(StoredDayPeer::EXTRAORDINARY_STORED_ID, $storedDay->getStoredDayId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
