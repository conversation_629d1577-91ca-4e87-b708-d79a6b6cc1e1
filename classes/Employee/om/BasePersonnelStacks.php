<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Employee\AbsenceStack;
use Employee\AbsenceStackQuery;
use Employee\Employee;
use Employee\EmployeeQuery;
use Employee\PersonnelStacks;
use Employee\PersonnelStacksPeer;
use Employee\PersonnelStacksQuery;

/**
 * Base class that represents a row from the 'personnel_stacks' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BasePersonnelStacks extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\PersonnelStacksPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        PersonnelStacksPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id field.
     * @var        int
     */
    protected $id;

    /**
     * The value for the employee_id field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $employee_id;

    /**
     * The value for the stack_id field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $stack_id;

    /**
     * The value for the reset_quota field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $reset_quota;

    /**
     * @var        Employee
     */
    protected $aPersonnelStacksEmployee;

    /**
     * @var        AbsenceStack
     */
    protected $aPersonnelStacksAbsenceStack;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->employee_id = '-1';
        $this->stack_id = '-1';
        $this->reset_quota = 0;
    }

    /**
     * Initializes internal state of BasePersonnelStacks object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id] column value.
     *
     * @return int
     */
    public function getId()
    {

        return $this->id;
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return string
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [stack_id] column value.
     *
     * @return string
     */
    public function getStackId()
    {

        return $this->stack_id;
    }

    /**
     * Get the [reset_quota] column value.
     *
     * @return double
     */
    public function getResetQuota()
    {

        return $this->reset_quota;
    }

    /**
     * Set the value of [id] column.
     *
     * @param  int $v new value
     * @return PersonnelStacks The current object (for fluent API support)
     */
    public function setId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id !== $v) {
            $this->id = $v;
            $this->modifiedColumns[] = PersonnelStacksPeer::ID;
        }


        return $this;
    } // setId()

    /**
     * Set the value of [employee_id] column.
     *
     * @param  string $v new value
     * @return PersonnelStacks The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = PersonnelStacksPeer::EMPLOYEE_ID;
        }

        if ($this->aPersonnelStacksEmployee !== null && $this->aPersonnelStacksEmployee->getEmployeeId() !== $v) {
            $this->aPersonnelStacksEmployee = null;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [stack_id] column.
     *
     * @param  string $v new value
     * @return PersonnelStacks The current object (for fluent API support)
     */
    public function setStackId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stack_id !== $v) {
            $this->stack_id = $v;
            $this->modifiedColumns[] = PersonnelStacksPeer::STACK_ID;
        }

        if ($this->aPersonnelStacksAbsenceStack !== null && $this->aPersonnelStacksAbsenceStack->getId() !== $v) {
            $this->aPersonnelStacksAbsenceStack = null;
        }


        return $this;
    } // setStackId()

    /**
     * Set the value of [reset_quota] column.
     *
     * @param  double $v new value
     * @return PersonnelStacks The current object (for fluent API support)
     */
    public function setResetQuota($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->reset_quota !== $v) {
            $this->reset_quota = $v;
            $this->modifiedColumns[] = PersonnelStacksPeer::RESET_QUOTA;
        }


        return $this;
    } // setResetQuota()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->employee_id !== '-1') {
                return false;
            }

            if ($this->stack_id !== '-1') {
                return false;
            }

            if ($this->reset_quota !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->employee_id = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->stack_id = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->reset_quota = ($row[$startcol + 3] !== null) ? (double) $row[$startcol + 3] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 4; // 4 = PersonnelStacksPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating PersonnelStacks object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aPersonnelStacksEmployee !== null && $this->employee_id !== $this->aPersonnelStacksEmployee->getEmployeeId()) {
            $this->aPersonnelStacksEmployee = null;
        }
        if ($this->aPersonnelStacksAbsenceStack !== null && $this->stack_id !== $this->aPersonnelStacksAbsenceStack->getId()) {
            $this->aPersonnelStacksAbsenceStack = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(PersonnelStacksPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = PersonnelStacksPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aPersonnelStacksEmployee = null;
            $this->aPersonnelStacksAbsenceStack = null;
        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(PersonnelStacksPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = PersonnelStacksQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(PersonnelStacksPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                PersonnelStacksPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aPersonnelStacksEmployee !== null) {
                if ($this->aPersonnelStacksEmployee->isModified() || $this->aPersonnelStacksEmployee->isNew()) {
                    $affectedRows += $this->aPersonnelStacksEmployee->save($con);
                }
                $this->setPersonnelStacksEmployee($this->aPersonnelStacksEmployee);
            }

            if ($this->aPersonnelStacksAbsenceStack !== null) {
                if ($this->aPersonnelStacksAbsenceStack->isModified() || $this->aPersonnelStacksAbsenceStack->isNew()) {
                    $affectedRows += $this->aPersonnelStacksAbsenceStack->save($con);
                }
                $this->setPersonnelStacksAbsenceStack($this->aPersonnelStacksAbsenceStack);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = PersonnelStacksPeer::ID;
        if (null !== $this->id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . PersonnelStacksPeer::ID . ')');
        }
        if (null === $this->id) {
            try {
                $stmt = $con->query("SELECT nextval('personnel_stacks_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(PersonnelStacksPeer::ID)) {
            $modifiedColumns[':p' . $index++]  = '"id"';
        }
        if ($this->isColumnModified(PersonnelStacksPeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(PersonnelStacksPeer::STACK_ID)) {
            $modifiedColumns[':p' . $index++]  = '"stack_id"';
        }
        if ($this->isColumnModified(PersonnelStacksPeer::RESET_QUOTA)) {
            $modifiedColumns[':p' . $index++]  = '"reset_quota"';
        }

        $sql = sprintf(
            'INSERT INTO "personnel_stacks" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id"':
                        $stmt->bindValue($identifier, $this->id, PDO::PARAM_INT);
                        break;
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_STR);
                        break;
                    case '"stack_id"':
                        $stmt->bindValue($identifier, $this->stack_id, PDO::PARAM_STR);
                        break;
                    case '"reset_quota"':
                        $stmt->bindValue($identifier, $this->reset_quota, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aPersonnelStacksEmployee !== null) {
                if (!$this->aPersonnelStacksEmployee->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aPersonnelStacksEmployee->getValidationFailures());
                }
            }

            if ($this->aPersonnelStacksAbsenceStack !== null) {
                if (!$this->aPersonnelStacksAbsenceStack->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aPersonnelStacksAbsenceStack->getValidationFailures());
                }
            }


            if (($retval = PersonnelStacksPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = PersonnelStacksPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getId();
                break;
            case 1:
                return $this->getEmployeeId();
                break;
            case 2:
                return $this->getStackId();
                break;
            case 3:
                return $this->getResetQuota();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['PersonnelStacks'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['PersonnelStacks'][$this->getPrimaryKey()] = true;
        $keys = PersonnelStacksPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getId(),
            $keys[1] => $this->getEmployeeId(),
            $keys[2] => $this->getStackId(),
            $keys[3] => $this->getResetQuota(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aPersonnelStacksEmployee) {
                $result['PersonnelStacksEmployee'] = $this->aPersonnelStacksEmployee->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aPersonnelStacksAbsenceStack) {
                $result['PersonnelStacksAbsenceStack'] = $this->aPersonnelStacksAbsenceStack->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = PersonnelStacksPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setId($value);
                break;
            case 1:
                $this->setEmployeeId($value);
                break;
            case 2:
                $this->setStackId($value);
                break;
            case 3:
                $this->setResetQuota($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = PersonnelStacksPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setEmployeeId($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setStackId($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setResetQuota($arr[$keys[3]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(PersonnelStacksPeer::DATABASE_NAME);

        if ($this->isColumnModified(PersonnelStacksPeer::ID)) $criteria->add(PersonnelStacksPeer::ID, $this->id);
        if ($this->isColumnModified(PersonnelStacksPeer::EMPLOYEE_ID)) $criteria->add(PersonnelStacksPeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(PersonnelStacksPeer::STACK_ID)) $criteria->add(PersonnelStacksPeer::STACK_ID, $this->stack_id);
        if ($this->isColumnModified(PersonnelStacksPeer::RESET_QUOTA)) $criteria->add(PersonnelStacksPeer::RESET_QUOTA, $this->reset_quota);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(PersonnelStacksPeer::DATABASE_NAME);
        $criteria->add(PersonnelStacksPeer::ID, $this->id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getId();
    }

    /**
     * Generic method to set the primary key (id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of PersonnelStacks (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setEmployeeId($this->getEmployeeId());
        $copyObj->setStackId($this->getStackId());
        $copyObj->setResetQuota($this->getResetQuota());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return PersonnelStacks Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return PersonnelStacksPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new PersonnelStacksPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Employee object.
     *
     * @param                  Employee $v
     * @return PersonnelStacks The current object (for fluent API support)
     * @throws PropelException
     */
    public function setPersonnelStacksEmployee(Employee $v = null)
    {
        if ($v === null) {
            $this->setEmployeeId('-1');
        } else {
            $this->setEmployeeId($v->getEmployeeId());
        }

        $this->aPersonnelStacksEmployee = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Employee object, it will not be re-added.
        if ($v !== null) {
            $v->addPersonnelStacks($this);
        }


        return $this;
    }


    /**
     * Get the associated Employee object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Employee The associated Employee object.
     * @throws PropelException
     */
    public function getPersonnelStacksEmployee(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aPersonnelStacksEmployee === null && (($this->employee_id !== "" && $this->employee_id !== null)) && $doQuery) {
            $this->aPersonnelStacksEmployee = EmployeeQuery::create()->findPk($this->employee_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aPersonnelStacksEmployee->addPersonnelStackss($this);
             */
        }

        return $this->aPersonnelStacksEmployee;
    }

    /**
     * Declares an association between this object and a AbsenceStack object.
     *
     * @param                  AbsenceStack $v
     * @return PersonnelStacks The current object (for fluent API support)
     * @throws PropelException
     */
    public function setPersonnelStacksAbsenceStack(AbsenceStack $v = null)
    {
        if ($v === null) {
            $this->setStackId('-1');
        } else {
            $this->setStackId($v->getId());
        }

        $this->aPersonnelStacksAbsenceStack = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the AbsenceStack object, it will not be re-added.
        if ($v !== null) {
            $v->addPersonnelStacks($this);
        }


        return $this;
    }


    /**
     * Get the associated AbsenceStack object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return AbsenceStack The associated AbsenceStack object.
     * @throws PropelException
     */
    public function getPersonnelStacksAbsenceStack(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aPersonnelStacksAbsenceStack === null && (($this->stack_id !== "" && $this->stack_id !== null)) && $doQuery) {
            $this->aPersonnelStacksAbsenceStack = AbsenceStackQuery::create()->findPk($this->stack_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aPersonnelStacksAbsenceStack->addPersonnelStackss($this);
             */
        }

        return $this->aPersonnelStacksAbsenceStack;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id = null;
        $this->employee_id = null;
        $this->stack_id = null;
        $this->reset_quota = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->aPersonnelStacksEmployee instanceof Persistent) {
              $this->aPersonnelStacksEmployee->clearAllReferences($deep);
            }
            if ($this->aPersonnelStacksAbsenceStack instanceof Persistent) {
              $this->aPersonnelStacksAbsenceStack->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        $this->aPersonnelStacksEmployee = null;
        $this->aPersonnelStacksAbsenceStack = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(PersonnelStacksPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
