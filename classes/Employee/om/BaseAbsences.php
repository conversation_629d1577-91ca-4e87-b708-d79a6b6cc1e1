<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Employee\AbsenceKind;
use Employee\AbsenceKindQuery;
use Employee\Absences;
use Employee\AbsencesPeer;
use Employee\AbsencesQuery;
use Employee\Employee;
use Employee\EmployeeQuery;

/**
 * Base class that represents a row from the 'absences' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseAbsences extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\AbsencesPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        AbsencesPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the absence_id field.
     * @var        string
     */
    protected $absence_id;

    /**
     * The value for the start_date field.
     * @var        string
     */
    protected $start_date;

    /**
     * The value for the end_date field.
     * @var        string
     */
    protected $end_date;

    /**
     * The value for the ab_kind field.
     * @var        string
     */
    protected $ab_kind;

    /**
     * The value for the total_days field.
     * @var        int
     */
    protected $total_days;

    /**
     * The value for the employee_id field.
     * @var        int
     */
    protected $employee_id;

    /**
     * The value for the date_of_req field.
     * @var        string
     */
    protected $date_of_req;

    /**
     * The value for the protocol_id field.
     * @var        string
     */
    protected $protocol_id;

    /**
     * The value for the type_of_abs field.
     * @var        int
     */
    protected $type_of_abs;

    /**
     * The value for the decreto field.
     * @var        int
     */
    protected $decreto;

    /**
     * The value for the note field.
     * @var        string
     */
    protected $note;

    /**
     * @var        AbsenceKind
     */
    protected $aAbsencesAbsenceKind;

    /**
     * @var        Employee
     */
    protected $aAbsenceEmployee;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Get the [absence_id] column value.
     *
     * @return string
     */
    public function getAbsenceId()
    {

        return $this->absence_id;
    }

    /**
     * Get the [start_date] column value.
     *
     * @return string
     */
    public function getStartDate()
    {

        return $this->start_date;
    }

    /**
     * Get the [end_date] column value.
     *
     * @return string
     */
    public function getEndDate()
    {

        return $this->end_date;
    }

    /**
     * Get the [ab_kind] column value.
     *
     * @return string
     */
    public function getAbKind()
    {

        return $this->ab_kind;
    }

    /**
     * Get the [total_days] column value.
     *
     * @return int
     */
    public function getTotalDays()
    {

        return $this->total_days;
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return int
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [date_of_req] column value.
     *
     * @return string
     */
    public function getDateOfReq()
    {

        return $this->date_of_req;
    }

    /**
     * Get the [protocol_id] column value.
     *
     * @return string
     */
    public function getProtocolId()
    {

        return $this->protocol_id;
    }

    /**
     * Get the [type_of_abs] column value.
     *
     * @return int
     */
    public function getTypeOfAbs()
    {

        return $this->type_of_abs;
    }

    /**
     * Get the [decreto] column value.
     *
     * @return int
     */
    public function getDecreto()
    {

        return $this->decreto;
    }

    /**
     * Get the [note] column value.
     *
     * @return string
     */
    public function getNote()
    {

        return $this->note;
    }

    /**
     * Set the value of [absence_id] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setAbsenceId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->absence_id !== $v) {
            $this->absence_id = $v;
            $this->modifiedColumns[] = AbsencesPeer::ABSENCE_ID;
        }


        return $this;
    } // setAbsenceId()

    /**
     * Set the value of [start_date] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setStartDate($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->start_date !== $v) {
            $this->start_date = $v;
            $this->modifiedColumns[] = AbsencesPeer::START_DATE;
        }


        return $this;
    } // setStartDate()

    /**
     * Set the value of [end_date] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setEndDate($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->end_date !== $v) {
            $this->end_date = $v;
            $this->modifiedColumns[] = AbsencesPeer::END_DATE;
        }


        return $this;
    } // setEndDate()

    /**
     * Set the value of [ab_kind] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setAbKind($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->ab_kind !== $v) {
            $this->ab_kind = $v;
            $this->modifiedColumns[] = AbsencesPeer::AB_KIND;
        }

        if ($this->aAbsencesAbsenceKind !== null && $this->aAbsencesAbsenceKind->getCode() !== $v) {
            $this->aAbsencesAbsenceKind = null;
        }


        return $this;
    } // setAbKind()

    /**
     * Set the value of [total_days] column.
     *
     * @param  int $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setTotalDays($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->total_days !== $v) {
            $this->total_days = $v;
            $this->modifiedColumns[] = AbsencesPeer::TOTAL_DAYS;
        }


        return $this;
    } // setTotalDays()

    /**
     * Set the value of [employee_id] column.
     *
     * @param  int $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = AbsencesPeer::EMPLOYEE_ID;
        }

        if ($this->aAbsenceEmployee !== null && $this->aAbsenceEmployee->getEmployeeId() !== $v) {
            $this->aAbsenceEmployee = null;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [date_of_req] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setDateOfReq($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->date_of_req !== $v) {
            $this->date_of_req = $v;
            $this->modifiedColumns[] = AbsencesPeer::DATE_OF_REQ;
        }


        return $this;
    } // setDateOfReq()

    /**
     * Set the value of [protocol_id] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setProtocolId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->protocol_id !== $v) {
            $this->protocol_id = $v;
            $this->modifiedColumns[] = AbsencesPeer::PROTOCOL_ID;
        }


        return $this;
    } // setProtocolId()

    /**
     * Set the value of [type_of_abs] column.
     *
     * @param  int $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setTypeOfAbs($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->type_of_abs !== $v) {
            $this->type_of_abs = $v;
            $this->modifiedColumns[] = AbsencesPeer::TYPE_OF_ABS;
        }


        return $this;
    } // setTypeOfAbs()

    /**
     * Set the value of [decreto] column.
     *
     * @param  int $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setDecreto($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->decreto !== $v) {
            $this->decreto = $v;
            $this->modifiedColumns[] = AbsencesPeer::DECRETO;
        }


        return $this;
    } // setDecreto()

    /**
     * Set the value of [note] column.
     *
     * @param  string $v new value
     * @return Absences The current object (for fluent API support)
     */
    public function setNote($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->note !== $v) {
            $this->note = $v;
            $this->modifiedColumns[] = AbsencesPeer::NOTE;
        }


        return $this;
    } // setNote()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->absence_id = ($row[$startcol + 0] !== null) ? (string) $row[$startcol + 0] : null;
            $this->start_date = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->end_date = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->ab_kind = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->total_days = ($row[$startcol + 4] !== null) ? (int) $row[$startcol + 4] : null;
            $this->employee_id = ($row[$startcol + 5] !== null) ? (int) $row[$startcol + 5] : null;
            $this->date_of_req = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->protocol_id = ($row[$startcol + 7] !== null) ? (string) $row[$startcol + 7] : null;
            $this->type_of_abs = ($row[$startcol + 8] !== null) ? (int) $row[$startcol + 8] : null;
            $this->decreto = ($row[$startcol + 9] !== null) ? (int) $row[$startcol + 9] : null;
            $this->note = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 11; // 11 = AbsencesPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Absences object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aAbsencesAbsenceKind !== null && $this->ab_kind !== $this->aAbsencesAbsenceKind->getCode()) {
            $this->aAbsencesAbsenceKind = null;
        }
        if ($this->aAbsenceEmployee !== null && $this->employee_id !== $this->aAbsenceEmployee->getEmployeeId()) {
            $this->aAbsenceEmployee = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsencesPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = AbsencesPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aAbsencesAbsenceKind = null;
            $this->aAbsenceEmployee = null;
        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsencesPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = AbsencesQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsencesPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                AbsencesPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aAbsencesAbsenceKind !== null) {
                if ($this->aAbsencesAbsenceKind->isModified() || $this->aAbsencesAbsenceKind->isNew()) {
                    $affectedRows += $this->aAbsencesAbsenceKind->save($con);
                }
                $this->setAbsencesAbsenceKind($this->aAbsencesAbsenceKind);
            }

            if ($this->aAbsenceEmployee !== null) {
                if ($this->aAbsenceEmployee->isModified() || $this->aAbsenceEmployee->isNew()) {
                    $affectedRows += $this->aAbsenceEmployee->save($con);
                }
                $this->setAbsenceEmployee($this->aAbsenceEmployee);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = AbsencesPeer::ABSENCE_ID;
        if (null !== $this->absence_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . AbsencesPeer::ABSENCE_ID . ')');
        }
        if (null === $this->absence_id) {
            try {
                $stmt = $con->query("SELECT nextval('absence_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->absence_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(AbsencesPeer::ABSENCE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"absence_id"';
        }
        if ($this->isColumnModified(AbsencesPeer::START_DATE)) {
            $modifiedColumns[':p' . $index++]  = '"start_date"';
        }
        if ($this->isColumnModified(AbsencesPeer::END_DATE)) {
            $modifiedColumns[':p' . $index++]  = '"end_date"';
        }
        if ($this->isColumnModified(AbsencesPeer::AB_KIND)) {
            $modifiedColumns[':p' . $index++]  = '"ab_kind"';
        }
        if ($this->isColumnModified(AbsencesPeer::TOTAL_DAYS)) {
            $modifiedColumns[':p' . $index++]  = '"total_days"';
        }
        if ($this->isColumnModified(AbsencesPeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(AbsencesPeer::DATE_OF_REQ)) {
            $modifiedColumns[':p' . $index++]  = '"date_of_req"';
        }
        if ($this->isColumnModified(AbsencesPeer::PROTOCOL_ID)) {
            $modifiedColumns[':p' . $index++]  = '"protocol_id"';
        }
        if ($this->isColumnModified(AbsencesPeer::TYPE_OF_ABS)) {
            $modifiedColumns[':p' . $index++]  = '"type_of_abs"';
        }
        if ($this->isColumnModified(AbsencesPeer::DECRETO)) {
            $modifiedColumns[':p' . $index++]  = '"decreto"';
        }
        if ($this->isColumnModified(AbsencesPeer::NOTE)) {
            $modifiedColumns[':p' . $index++]  = '"note"';
        }

        $sql = sprintf(
            'INSERT INTO "absences" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"absence_id"':
                        $stmt->bindValue($identifier, $this->absence_id, PDO::PARAM_STR);
                        break;
                    case '"start_date"':
                        $stmt->bindValue($identifier, $this->start_date, PDO::PARAM_STR);
                        break;
                    case '"end_date"':
                        $stmt->bindValue($identifier, $this->end_date, PDO::PARAM_STR);
                        break;
                    case '"ab_kind"':
                        $stmt->bindValue($identifier, $this->ab_kind, PDO::PARAM_STR);
                        break;
                    case '"total_days"':
                        $stmt->bindValue($identifier, $this->total_days, PDO::PARAM_INT);
                        break;
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_INT);
                        break;
                    case '"date_of_req"':
                        $stmt->bindValue($identifier, $this->date_of_req, PDO::PARAM_STR);
                        break;
                    case '"protocol_id"':
                        $stmt->bindValue($identifier, $this->protocol_id, PDO::PARAM_STR);
                        break;
                    case '"type_of_abs"':
                        $stmt->bindValue($identifier, $this->type_of_abs, PDO::PARAM_INT);
                        break;
                    case '"decreto"':
                        $stmt->bindValue($identifier, $this->decreto, PDO::PARAM_INT);
                        break;
                    case '"note"':
                        $stmt->bindValue($identifier, $this->note, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aAbsencesAbsenceKind !== null) {
                if (!$this->aAbsencesAbsenceKind->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aAbsencesAbsenceKind->getValidationFailures());
                }
            }

            if ($this->aAbsenceEmployee !== null) {
                if (!$this->aAbsenceEmployee->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aAbsenceEmployee->getValidationFailures());
                }
            }


            if (($retval = AbsencesPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AbsencesPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getAbsenceId();
                break;
            case 1:
                return $this->getStartDate();
                break;
            case 2:
                return $this->getEndDate();
                break;
            case 3:
                return $this->getAbKind();
                break;
            case 4:
                return $this->getTotalDays();
                break;
            case 5:
                return $this->getEmployeeId();
                break;
            case 6:
                return $this->getDateOfReq();
                break;
            case 7:
                return $this->getProtocolId();
                break;
            case 8:
                return $this->getTypeOfAbs();
                break;
            case 9:
                return $this->getDecreto();
                break;
            case 10:
                return $this->getNote();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Absences'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Absences'][$this->getPrimaryKey()] = true;
        $keys = AbsencesPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getAbsenceId(),
            $keys[1] => $this->getStartDate(),
            $keys[2] => $this->getEndDate(),
            $keys[3] => $this->getAbKind(),
            $keys[4] => $this->getTotalDays(),
            $keys[5] => $this->getEmployeeId(),
            $keys[6] => $this->getDateOfReq(),
            $keys[7] => $this->getProtocolId(),
            $keys[8] => $this->getTypeOfAbs(),
            $keys[9] => $this->getDecreto(),
            $keys[10] => $this->getNote(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aAbsencesAbsenceKind) {
                $result['AbsencesAbsenceKind'] = $this->aAbsencesAbsenceKind->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aAbsenceEmployee) {
                $result['AbsenceEmployee'] = $this->aAbsenceEmployee->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AbsencesPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setAbsenceId($value);
                break;
            case 1:
                $this->setStartDate($value);
                break;
            case 2:
                $this->setEndDate($value);
                break;
            case 3:
                $this->setAbKind($value);
                break;
            case 4:
                $this->setTotalDays($value);
                break;
            case 5:
                $this->setEmployeeId($value);
                break;
            case 6:
                $this->setDateOfReq($value);
                break;
            case 7:
                $this->setProtocolId($value);
                break;
            case 8:
                $this->setTypeOfAbs($value);
                break;
            case 9:
                $this->setDecreto($value);
                break;
            case 10:
                $this->setNote($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = AbsencesPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setAbsenceId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setStartDate($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setEndDate($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setAbKind($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setTotalDays($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setEmployeeId($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setDateOfReq($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setProtocolId($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setTypeOfAbs($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setDecreto($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setNote($arr[$keys[10]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(AbsencesPeer::DATABASE_NAME);

        if ($this->isColumnModified(AbsencesPeer::ABSENCE_ID)) $criteria->add(AbsencesPeer::ABSENCE_ID, $this->absence_id);
        if ($this->isColumnModified(AbsencesPeer::START_DATE)) $criteria->add(AbsencesPeer::START_DATE, $this->start_date);
        if ($this->isColumnModified(AbsencesPeer::END_DATE)) $criteria->add(AbsencesPeer::END_DATE, $this->end_date);
        if ($this->isColumnModified(AbsencesPeer::AB_KIND)) $criteria->add(AbsencesPeer::AB_KIND, $this->ab_kind);
        if ($this->isColumnModified(AbsencesPeer::TOTAL_DAYS)) $criteria->add(AbsencesPeer::TOTAL_DAYS, $this->total_days);
        if ($this->isColumnModified(AbsencesPeer::EMPLOYEE_ID)) $criteria->add(AbsencesPeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(AbsencesPeer::DATE_OF_REQ)) $criteria->add(AbsencesPeer::DATE_OF_REQ, $this->date_of_req);
        if ($this->isColumnModified(AbsencesPeer::PROTOCOL_ID)) $criteria->add(AbsencesPeer::PROTOCOL_ID, $this->protocol_id);
        if ($this->isColumnModified(AbsencesPeer::TYPE_OF_ABS)) $criteria->add(AbsencesPeer::TYPE_OF_ABS, $this->type_of_abs);
        if ($this->isColumnModified(AbsencesPeer::DECRETO)) $criteria->add(AbsencesPeer::DECRETO, $this->decreto);
        if ($this->isColumnModified(AbsencesPeer::NOTE)) $criteria->add(AbsencesPeer::NOTE, $this->note);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(AbsencesPeer::DATABASE_NAME);
        $criteria->add(AbsencesPeer::ABSENCE_ID, $this->absence_id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return string
     */
    public function getPrimaryKey()
    {
        return $this->getAbsenceId();
    }

    /**
     * Generic method to set the primary key (absence_id column).
     *
     * @param  string $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setAbsenceId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getAbsenceId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Absences (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setStartDate($this->getStartDate());
        $copyObj->setEndDate($this->getEndDate());
        $copyObj->setAbKind($this->getAbKind());
        $copyObj->setTotalDays($this->getTotalDays());
        $copyObj->setEmployeeId($this->getEmployeeId());
        $copyObj->setDateOfReq($this->getDateOfReq());
        $copyObj->setProtocolId($this->getProtocolId());
        $copyObj->setTypeOfAbs($this->getTypeOfAbs());
        $copyObj->setDecreto($this->getDecreto());
        $copyObj->setNote($this->getNote());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setAbsenceId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Absences Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return AbsencesPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new AbsencesPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a AbsenceKind object.
     *
     * @param                  AbsenceKind $v
     * @return Absences The current object (for fluent API support)
     * @throws PropelException
     */
    public function setAbsencesAbsenceKind(AbsenceKind $v = null)
    {
        if ($v === null) {
            $this->setAbKind(NULL);
        } else {
            $this->setAbKind($v->getCode());
        }

        $this->aAbsencesAbsenceKind = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the AbsenceKind object, it will not be re-added.
        if ($v !== null) {
            $v->addAbsences($this);
        }


        return $this;
    }


    /**
     * Get the associated AbsenceKind object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return AbsenceKind The associated AbsenceKind object.
     * @throws PropelException
     */
    public function getAbsencesAbsenceKind(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aAbsencesAbsenceKind === null && (($this->ab_kind !== "" && $this->ab_kind !== null)) && $doQuery) {
            $this->aAbsencesAbsenceKind = AbsenceKindQuery::create()->findPk($this->ab_kind, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aAbsencesAbsenceKind->addAbsencess($this);
             */
        }

        return $this->aAbsencesAbsenceKind;
    }

    /**
     * Declares an association between this object and a Employee object.
     *
     * @param                  Employee $v
     * @return Absences The current object (for fluent API support)
     * @throws PropelException
     */
    public function setAbsenceEmployee(Employee $v = null)
    {
        if ($v === null) {
            $this->setEmployeeId(NULL);
        } else {
            $this->setEmployeeId($v->getEmployeeId());
        }

        $this->aAbsenceEmployee = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Employee object, it will not be re-added.
        if ($v !== null) {
            $v->addAbsences($this);
        }


        return $this;
    }


    /**
     * Get the associated Employee object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Employee The associated Employee object.
     * @throws PropelException
     */
    public function getAbsenceEmployee(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aAbsenceEmployee === null && ($this->employee_id !== null) && $doQuery) {
            $this->aAbsenceEmployee = EmployeeQuery::create()->findPk($this->employee_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aAbsenceEmployee->addAbsencess($this);
             */
        }

        return $this->aAbsenceEmployee;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->absence_id = null;
        $this->start_date = null;
        $this->end_date = null;
        $this->ab_kind = null;
        $this->total_days = null;
        $this->employee_id = null;
        $this->date_of_req = null;
        $this->protocol_id = null;
        $this->type_of_abs = null;
        $this->decreto = null;
        $this->note = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->aAbsencesAbsenceKind instanceof Persistent) {
              $this->aAbsencesAbsenceKind->clearAllReferences($deep);
            }
            if ($this->aAbsenceEmployee instanceof Persistent) {
              $this->aAbsenceEmployee->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        $this->aAbsencesAbsenceKind = null;
        $this->aAbsenceEmployee = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(AbsencesPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
