<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Budget\Activity;
use Employee\Employee;
use Employee\Presence;
use Employee\PresencePeer;
use Employee\PresenceQuery;

/**
 * Base class that represents a query for the 'personnel_presences' table.
 *
 *
 *
 * @method PresenceQuery orderByPersonnelPresenceId($order = Criteria::ASC) Order by the personnel_presence_id column
 * @method PresenceQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method PresenceQuery orderByProjectId($order = Criteria::ASC) Order by the project_id column
 * @method PresenceQuery orderByProjectIdEdit($order = Criteria::ASC) Order by the project_edit_id column
 * @method PresenceQuery orderByDate($order = Criteria::ASC) Order by the date column
 * @method PresenceQuery orderByDateEdit($order = Criteria::ASC) Order by the date_edit column
 * @method PresenceQuery orderByType($order = Criteria::ASC) Order by the type column
 * @method PresenceQuery orderByTypeEdit($order = Criteria::ASC) Order by the type_edit column
 * @method PresenceQuery orderByOriginalInOut($order = Criteria::ASC) Order by the original_inout column
 * @method PresenceQuery orderByOriginalInOutEdit($order = Criteria::ASC) Order by the original_inout_edit column
 * @method PresenceQuery orderByDescription($order = Criteria::ASC) Order by the description column
 * @method PresenceQuery orderByHourTypeId($order = Criteria::ASC) Order by the hour_type_id column
 * @method PresenceQuery orderByHourTypeIdEdit($order = Criteria::ASC) Order by the hour_type_edit_id column
 * @method PresenceQuery orderByInsertionMode($order = Criteria::ASC) Order by the insertion_mode column
 *
 * @method PresenceQuery groupByPersonnelPresenceId() Group by the personnel_presence_id column
 * @method PresenceQuery groupByEmployeeId() Group by the employee_id column
 * @method PresenceQuery groupByProjectId() Group by the project_id column
 * @method PresenceQuery groupByProjectIdEdit() Group by the project_edit_id column
 * @method PresenceQuery groupByDate() Group by the date column
 * @method PresenceQuery groupByDateEdit() Group by the date_edit column
 * @method PresenceQuery groupByType() Group by the type column
 * @method PresenceQuery groupByTypeEdit() Group by the type_edit column
 * @method PresenceQuery groupByOriginalInOut() Group by the original_inout column
 * @method PresenceQuery groupByOriginalInOutEdit() Group by the original_inout_edit column
 * @method PresenceQuery groupByDescription() Group by the description column
 * @method PresenceQuery groupByHourTypeId() Group by the hour_type_id column
 * @method PresenceQuery groupByHourTypeIdEdit() Group by the hour_type_edit_id column
 * @method PresenceQuery groupByInsertionMode() Group by the insertion_mode column
 *
 * @method PresenceQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method PresenceQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method PresenceQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method PresenceQuery leftJoinPresenceEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the PresenceEmployee relation
 * @method PresenceQuery rightJoinPresenceEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the PresenceEmployee relation
 * @method PresenceQuery innerJoinPresenceEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the PresenceEmployee relation
 *
 * @method PresenceQuery leftJoinPresenceProject($relationAlias = null) Adds a LEFT JOIN clause to the query using the PresenceProject relation
 * @method PresenceQuery rightJoinPresenceProject($relationAlias = null) Adds a RIGHT JOIN clause to the query using the PresenceProject relation
 * @method PresenceQuery innerJoinPresenceProject($relationAlias = null) Adds a INNER JOIN clause to the query using the PresenceProject relation
 *
 * @method Presence findOne(PropelPDO $con = null) Return the first Presence matching the query
 * @method Presence findOneOrCreate(PropelPDO $con = null) Return the first Presence matching the query, or a new Presence object populated from the query conditions when no match is found
 *
 * @method Presence findOneByEmployeeId(string $employee_id) Return the first Presence filtered by the employee_id column
 * @method Presence findOneByProjectId(string $project_id) Return the first Presence filtered by the project_id column
 * @method Presence findOneByProjectIdEdit(string $project_edit_id) Return the first Presence filtered by the project_edit_id column
 * @method Presence findOneByDate(string $date) Return the first Presence filtered by the date column
 * @method Presence findOneByDateEdit(string $date_edit) Return the first Presence filtered by the date_edit column
 * @method Presence findOneByType(int $type) Return the first Presence filtered by the type column
 * @method Presence findOneByTypeEdit(int $type_edit) Return the first Presence filtered by the type_edit column
 * @method Presence findOneByOriginalInOut(int $original_inout) Return the first Presence filtered by the original_inout column
 * @method Presence findOneByOriginalInOutEdit(int $original_inout_edit) Return the first Presence filtered by the original_inout_edit column
 * @method Presence findOneByDescription(string $description) Return the first Presence filtered by the description column
 * @method Presence findOneByHourTypeId(string $hour_type_id) Return the first Presence filtered by the hour_type_id column
 * @method Presence findOneByHourTypeIdEdit(string $hour_type_edit_id) Return the first Presence filtered by the hour_type_edit_id column
 * @method Presence findOneByInsertionMode(string $insertion_mode) Return the first Presence filtered by the insertion_mode column
 *
 * @method array findByPersonnelPresenceId(int $personnel_presence_id) Return Presence objects filtered by the personnel_presence_id column
 * @method array findByEmployeeId(string $employee_id) Return Presence objects filtered by the employee_id column
 * @method array findByProjectId(string $project_id) Return Presence objects filtered by the project_id column
 * @method array findByProjectIdEdit(string $project_edit_id) Return Presence objects filtered by the project_edit_id column
 * @method array findByDate(string $date) Return Presence objects filtered by the date column
 * @method array findByDateEdit(string $date_edit) Return Presence objects filtered by the date_edit column
 * @method array findByType(int $type) Return Presence objects filtered by the type column
 * @method array findByTypeEdit(int $type_edit) Return Presence objects filtered by the type_edit column
 * @method array findByOriginalInOut(int $original_inout) Return Presence objects filtered by the original_inout column
 * @method array findByOriginalInOutEdit(int $original_inout_edit) Return Presence objects filtered by the original_inout_edit column
 * @method array findByDescription(string $description) Return Presence objects filtered by the description column
 * @method array findByHourTypeId(string $hour_type_id) Return Presence objects filtered by the hour_type_id column
 * @method array findByHourTypeIdEdit(string $hour_type_edit_id) Return Presence objects filtered by the hour_type_edit_id column
 * @method array findByInsertionMode(string $insertion_mode) Return Presence objects filtered by the insertion_mode column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BasePresenceQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BasePresenceQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\Presence';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new PresenceQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   PresenceQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return PresenceQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof PresenceQuery) {
            return $criteria;
        }
        $query = new PresenceQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Presence|Presence[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = PresencePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Presence A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByPersonnelPresenceId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Presence A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "personnel_presence_id", "employee_id", "project_id", "project_edit_id", "date", "date_edit", "type", "type_edit", "original_inout", "original_inout_edit", "description", "hour_type_id", "hour_type_edit_id", "insertion_mode" FROM "personnel_presences" WHERE "personnel_presence_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Presence();
            $obj->hydrate($row);
            PresencePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Presence|Presence[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Presence[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(PresencePeer::PERSONNEL_PRESENCE_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(PresencePeer::PERSONNEL_PRESENCE_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the personnel_presence_id column
     *
     * Example usage:
     * <code>
     * $query->filterByPersonnelPresenceId(1234); // WHERE personnel_presence_id = 1234
     * $query->filterByPersonnelPresenceId(array(12, 34)); // WHERE personnel_presence_id IN (12, 34)
     * $query->filterByPersonnelPresenceId(array('min' => 12)); // WHERE personnel_presence_id >= 12
     * $query->filterByPersonnelPresenceId(array('max' => 12)); // WHERE personnel_presence_id <= 12
     * </code>
     *
     * @param     mixed $personnelPresenceId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByPersonnelPresenceId($personnelPresenceId = null, $comparison = null)
    {
        if (is_array($personnelPresenceId)) {
            $useMinMax = false;
            if (isset($personnelPresenceId['min'])) {
                $this->addUsingAlias(PresencePeer::PERSONNEL_PRESENCE_ID, $personnelPresenceId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($personnelPresenceId['max'])) {
                $this->addUsingAlias(PresencePeer::PERSONNEL_PRESENCE_ID, $personnelPresenceId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::PERSONNEL_PRESENCE_ID, $personnelPresenceId, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @see       filterByPresenceEmployee()
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(PresencePeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(PresencePeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the project_id column
     *
     * Example usage:
     * <code>
     * $query->filterByProjectId(1234); // WHERE project_id = 1234
     * $query->filterByProjectId(array(12, 34)); // WHERE project_id IN (12, 34)
     * $query->filterByProjectId(array('min' => 12)); // WHERE project_id >= 12
     * $query->filterByProjectId(array('max' => 12)); // WHERE project_id <= 12
     * </code>
     *
     * @param     mixed $projectId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByProjectId($projectId = null, $comparison = null)
    {
        if (is_array($projectId)) {
            $useMinMax = false;
            if (isset($projectId['min'])) {
                $this->addUsingAlias(PresencePeer::PROJECT_ID, $projectId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($projectId['max'])) {
                $this->addUsingAlias(PresencePeer::PROJECT_ID, $projectId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::PROJECT_ID, $projectId, $comparison);
    }

    /**
     * Filter the query on the project_edit_id column
     *
     * Example usage:
     * <code>
     * $query->filterByProjectIdEdit(1234); // WHERE project_edit_id = 1234
     * $query->filterByProjectIdEdit(array(12, 34)); // WHERE project_edit_id IN (12, 34)
     * $query->filterByProjectIdEdit(array('min' => 12)); // WHERE project_edit_id >= 12
     * $query->filterByProjectIdEdit(array('max' => 12)); // WHERE project_edit_id <= 12
     * </code>
     *
     * @see       filterByPresenceProject()
     *
     * @param     mixed $projectIdEdit The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByProjectIdEdit($projectIdEdit = null, $comparison = null)
    {
        if (is_array($projectIdEdit)) {
            $useMinMax = false;
            if (isset($projectIdEdit['min'])) {
                $this->addUsingAlias(PresencePeer::PROJECT_EDIT_ID, $projectIdEdit['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($projectIdEdit['max'])) {
                $this->addUsingAlias(PresencePeer::PROJECT_EDIT_ID, $projectIdEdit['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::PROJECT_EDIT_ID, $projectIdEdit, $comparison);
    }

    /**
     * Filter the query on the date column
     *
     * Example usage:
     * <code>
     * $query->filterByDate(1234); // WHERE date = 1234
     * $query->filterByDate(array(12, 34)); // WHERE date IN (12, 34)
     * $query->filterByDate(array('min' => 12)); // WHERE date >= 12
     * $query->filterByDate(array('max' => 12)); // WHERE date <= 12
     * </code>
     *
     * @param     mixed $date The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByDate($date = null, $comparison = null)
    {
        if (is_array($date)) {
            $useMinMax = false;
            if (isset($date['min'])) {
                $this->addUsingAlias(PresencePeer::DATE, $date['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($date['max'])) {
                $this->addUsingAlias(PresencePeer::DATE, $date['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::DATE, $date, $comparison);
    }

    /**
     * Filter the query on the date_edit column
     *
     * Example usage:
     * <code>
     * $query->filterByDateEdit(1234); // WHERE date_edit = 1234
     * $query->filterByDateEdit(array(12, 34)); // WHERE date_edit IN (12, 34)
     * $query->filterByDateEdit(array('min' => 12)); // WHERE date_edit >= 12
     * $query->filterByDateEdit(array('max' => 12)); // WHERE date_edit <= 12
     * </code>
     *
     * @param     mixed $dateEdit The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByDateEdit($dateEdit = null, $comparison = null)
    {
        if (is_array($dateEdit)) {
            $useMinMax = false;
            if (isset($dateEdit['min'])) {
                $this->addUsingAlias(PresencePeer::DATE_EDIT, $dateEdit['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateEdit['max'])) {
                $this->addUsingAlias(PresencePeer::DATE_EDIT, $dateEdit['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::DATE_EDIT, $dateEdit, $comparison);
    }

    /**
     * Filter the query on the type column
     *
     * Example usage:
     * <code>
     * $query->filterByType(1234); // WHERE type = 1234
     * $query->filterByType(array(12, 34)); // WHERE type IN (12, 34)
     * $query->filterByType(array('min' => 12)); // WHERE type >= 12
     * $query->filterByType(array('max' => 12)); // WHERE type <= 12
     * </code>
     *
     * @param     mixed $type The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByType($type = null, $comparison = null)
    {
        if (is_array($type)) {
            $useMinMax = false;
            if (isset($type['min'])) {
                $this->addUsingAlias(PresencePeer::TYPE, $type['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($type['max'])) {
                $this->addUsingAlias(PresencePeer::TYPE, $type['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::TYPE, $type, $comparison);
    }

    /**
     * Filter the query on the type_edit column
     *
     * Example usage:
     * <code>
     * $query->filterByTypeEdit(1234); // WHERE type_edit = 1234
     * $query->filterByTypeEdit(array(12, 34)); // WHERE type_edit IN (12, 34)
     * $query->filterByTypeEdit(array('min' => 12)); // WHERE type_edit >= 12
     * $query->filterByTypeEdit(array('max' => 12)); // WHERE type_edit <= 12
     * </code>
     *
     * @param     mixed $typeEdit The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByTypeEdit($typeEdit = null, $comparison = null)
    {
        if (is_array($typeEdit)) {
            $useMinMax = false;
            if (isset($typeEdit['min'])) {
                $this->addUsingAlias(PresencePeer::TYPE_EDIT, $typeEdit['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($typeEdit['max'])) {
                $this->addUsingAlias(PresencePeer::TYPE_EDIT, $typeEdit['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::TYPE_EDIT, $typeEdit, $comparison);
    }

    /**
     * Filter the query on the original_inout column
     *
     * Example usage:
     * <code>
     * $query->filterByOriginalInOut(1234); // WHERE original_inout = 1234
     * $query->filterByOriginalInOut(array(12, 34)); // WHERE original_inout IN (12, 34)
     * $query->filterByOriginalInOut(array('min' => 12)); // WHERE original_inout >= 12
     * $query->filterByOriginalInOut(array('max' => 12)); // WHERE original_inout <= 12
     * </code>
     *
     * @param     mixed $originalInOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByOriginalInOut($originalInOut = null, $comparison = null)
    {
        if (is_array($originalInOut)) {
            $useMinMax = false;
            if (isset($originalInOut['min'])) {
                $this->addUsingAlias(PresencePeer::ORIGINAL_INOUT, $originalInOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($originalInOut['max'])) {
                $this->addUsingAlias(PresencePeer::ORIGINAL_INOUT, $originalInOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::ORIGINAL_INOUT, $originalInOut, $comparison);
    }

    /**
     * Filter the query on the original_inout_edit column
     *
     * Example usage:
     * <code>
     * $query->filterByOriginalInOutEdit(1234); // WHERE original_inout_edit = 1234
     * $query->filterByOriginalInOutEdit(array(12, 34)); // WHERE original_inout_edit IN (12, 34)
     * $query->filterByOriginalInOutEdit(array('min' => 12)); // WHERE original_inout_edit >= 12
     * $query->filterByOriginalInOutEdit(array('max' => 12)); // WHERE original_inout_edit <= 12
     * </code>
     *
     * @param     mixed $originalInOutEdit The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByOriginalInOutEdit($originalInOutEdit = null, $comparison = null)
    {
        if (is_array($originalInOutEdit)) {
            $useMinMax = false;
            if (isset($originalInOutEdit['min'])) {
                $this->addUsingAlias(PresencePeer::ORIGINAL_INOUT_EDIT, $originalInOutEdit['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($originalInOutEdit['max'])) {
                $this->addUsingAlias(PresencePeer::ORIGINAL_INOUT_EDIT, $originalInOutEdit['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::ORIGINAL_INOUT_EDIT, $originalInOutEdit, $comparison);
    }

    /**
     * Filter the query on the description column
     *
     * Example usage:
     * <code>
     * $query->filterByDescription('fooValue');   // WHERE description = 'fooValue'
     * $query->filterByDescription('%fooValue%'); // WHERE description LIKE '%fooValue%'
     * </code>
     *
     * @param     string $description The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByDescription($description = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($description)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $description)) {
                $description = str_replace('*', '%', $description);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(PresencePeer::DESCRIPTION, $description, $comparison);
    }

    /**
     * Filter the query on the hour_type_id column
     *
     * Example usage:
     * <code>
     * $query->filterByHourTypeId(1234); // WHERE hour_type_id = 1234
     * $query->filterByHourTypeId(array(12, 34)); // WHERE hour_type_id IN (12, 34)
     * $query->filterByHourTypeId(array('min' => 12)); // WHERE hour_type_id >= 12
     * $query->filterByHourTypeId(array('max' => 12)); // WHERE hour_type_id <= 12
     * </code>
     *
     * @param     mixed $hourTypeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByHourTypeId($hourTypeId = null, $comparison = null)
    {
        if (is_array($hourTypeId)) {
            $useMinMax = false;
            if (isset($hourTypeId['min'])) {
                $this->addUsingAlias(PresencePeer::HOUR_TYPE_ID, $hourTypeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($hourTypeId['max'])) {
                $this->addUsingAlias(PresencePeer::HOUR_TYPE_ID, $hourTypeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::HOUR_TYPE_ID, $hourTypeId, $comparison);
    }

    /**
     * Filter the query on the hour_type_edit_id column
     *
     * Example usage:
     * <code>
     * $query->filterByHourTypeIdEdit(1234); // WHERE hour_type_edit_id = 1234
     * $query->filterByHourTypeIdEdit(array(12, 34)); // WHERE hour_type_edit_id IN (12, 34)
     * $query->filterByHourTypeIdEdit(array('min' => 12)); // WHERE hour_type_edit_id >= 12
     * $query->filterByHourTypeIdEdit(array('max' => 12)); // WHERE hour_type_edit_id <= 12
     * </code>
     *
     * @param     mixed $hourTypeIdEdit The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByHourTypeIdEdit($hourTypeIdEdit = null, $comparison = null)
    {
        if (is_array($hourTypeIdEdit)) {
            $useMinMax = false;
            if (isset($hourTypeIdEdit['min'])) {
                $this->addUsingAlias(PresencePeer::HOUR_TYPE_EDIT_ID, $hourTypeIdEdit['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($hourTypeIdEdit['max'])) {
                $this->addUsingAlias(PresencePeer::HOUR_TYPE_EDIT_ID, $hourTypeIdEdit['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PresencePeer::HOUR_TYPE_EDIT_ID, $hourTypeIdEdit, $comparison);
    }

    /**
     * Filter the query on the insertion_mode column
     *
     * Example usage:
     * <code>
     * $query->filterByInsertionMode('fooValue');   // WHERE insertion_mode = 'fooValue'
     * $query->filterByInsertionMode('%fooValue%'); // WHERE insertion_mode LIKE '%fooValue%'
     * </code>
     *
     * @param     string $insertionMode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function filterByInsertionMode($insertionMode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($insertionMode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $insertionMode)) {
                $insertionMode = str_replace('*', '%', $insertionMode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(PresencePeer::INSERTION_MODE, $insertionMode, $comparison);
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 PresenceQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPresenceEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(PresencePeer::EMPLOYEE_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(PresencePeer::EMPLOYEE_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByPresenceEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the PresenceEmployee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function joinPresenceEmployee($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('PresenceEmployee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'PresenceEmployee');
        }

        return $this;
    }

    /**
     * Use the PresenceEmployee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function usePresenceEmployeeQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinPresenceEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'PresenceEmployee', '\Employee\EmployeeQuery');
    }

    /**
     * Filter the query by a related Activity object
     *
     * @param   Activity|PropelObjectCollection $activity The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 PresenceQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPresenceProject($activity, $comparison = null)
    {
        if ($activity instanceof Activity) {
            return $this
                ->addUsingAlias(PresencePeer::PROJECT_EDIT_ID, $activity->getActivityId(), $comparison);
        } elseif ($activity instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(PresencePeer::PROJECT_EDIT_ID, $activity->toKeyValue('PrimaryKey', 'ActivityId'), $comparison);
        } else {
            throw new PropelException('filterByPresenceProject() only accepts arguments of type Activity or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the PresenceProject relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function joinPresenceProject($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('PresenceProject');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'PresenceProject');
        }

        return $this;
    }

    /**
     * Use the PresenceProject relation Activity object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Budget\ActivityQuery A secondary query class using the current class as primary query
     */
    public function usePresenceProjectQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinPresenceProject($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'PresenceProject', '\Budget\ActivityQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Presence $presence Object to remove from the list of results
     *
     * @return PresenceQuery The current query, for fluid interface
     */
    public function prune($presence = null)
    {
        if ($presence) {
            $this->addUsingAlias(PresencePeer::PERSONNEL_PRESENCE_ID, $presence->getPersonnelPresenceId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
