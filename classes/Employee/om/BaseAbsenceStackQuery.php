<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceKind;
use Employee\AbsenceStack;
use Employee\AbsenceStackPeer;
use Employee\AbsenceStackQuery;
use Employee\PersonnelStacks;
use Employee\StoredStack;

/**
 * Base class that represents a query for the 'absence_stack' table.
 *
 *
 *
 * @method AbsenceStackQuery orderById($order = Criteria::ASC) Order by the id column
 * @method AbsenceStackQuery orderByUnit($order = Criteria::ASC) Order by the unit column
 * @method AbsenceStackQuery orderByDenomination($order = Criteria::ASC) Order by the denomination column
 * @method AbsenceStackQuery orderByRecover($order = Criteria::ASC) Order by the recover column
 * @method AbsenceStackQuery orderByResetType($order = Criteria::ASC) Order by the reset_type column
 * @method AbsenceStackQuery orderByResetToStackId($order = Criteria::ASC) Order by the reset_to_stack_id column
 * @method AbsenceStackQuery orderByResetDate($order = Criteria::ASC) Order by the reset_date column
 * @method AbsenceStackQuery orderByResetDefaultQuota($order = Criteria::ASC) Order by the reset_default_quota column
 *
 * @method AbsenceStackQuery groupById() Group by the id column
 * @method AbsenceStackQuery groupByUnit() Group by the unit column
 * @method AbsenceStackQuery groupByDenomination() Group by the denomination column
 * @method AbsenceStackQuery groupByRecover() Group by the recover column
 * @method AbsenceStackQuery groupByResetType() Group by the reset_type column
 * @method AbsenceStackQuery groupByResetToStackId() Group by the reset_to_stack_id column
 * @method AbsenceStackQuery groupByResetDate() Group by the reset_date column
 * @method AbsenceStackQuery groupByResetDefaultQuota() Group by the reset_default_quota column
 *
 * @method AbsenceStackQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AbsenceStackQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AbsenceStackQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AbsenceStackQuery leftJoinAbsenceKind($relationAlias = null) Adds a LEFT JOIN clause to the query using the AbsenceKind relation
 * @method AbsenceStackQuery rightJoinAbsenceKind($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AbsenceKind relation
 * @method AbsenceStackQuery innerJoinAbsenceKind($relationAlias = null) Adds a INNER JOIN clause to the query using the AbsenceKind relation
 *
 * @method AbsenceStackQuery leftJoinPersonnelStacks($relationAlias = null) Adds a LEFT JOIN clause to the query using the PersonnelStacks relation
 * @method AbsenceStackQuery rightJoinPersonnelStacks($relationAlias = null) Adds a RIGHT JOIN clause to the query using the PersonnelStacks relation
 * @method AbsenceStackQuery innerJoinPersonnelStacks($relationAlias = null) Adds a INNER JOIN clause to the query using the PersonnelStacks relation
 *
 * @method AbsenceStackQuery leftJoinStoredStack($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredStack relation
 * @method AbsenceStackQuery rightJoinStoredStack($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredStack relation
 * @method AbsenceStackQuery innerJoinStoredStack($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredStack relation
 *
 * @method AbsenceStack findOne(PropelPDO $con = null) Return the first AbsenceStack matching the query
 * @method AbsenceStack findOneOrCreate(PropelPDO $con = null) Return the first AbsenceStack matching the query, or a new AbsenceStack object populated from the query conditions when no match is found
 *
 * @method AbsenceStack findOneByUnit(string $unit) Return the first AbsenceStack filtered by the unit column
 * @method AbsenceStack findOneByDenomination(string $denomination) Return the first AbsenceStack filtered by the denomination column
 * @method AbsenceStack findOneByRecover(boolean $recover) Return the first AbsenceStack filtered by the recover column
 * @method AbsenceStack findOneByResetType(int $reset_type) Return the first AbsenceStack filtered by the reset_type column
 * @method AbsenceStack findOneByResetToStackId(string $reset_to_stack_id) Return the first AbsenceStack filtered by the reset_to_stack_id column
 * @method AbsenceStack findOneByResetDate(string $reset_date) Return the first AbsenceStack filtered by the reset_date column
 * @method AbsenceStack findOneByResetDefaultQuota(double $reset_default_quota) Return the first AbsenceStack filtered by the reset_default_quota column
 *
 * @method array findById(int $id) Return AbsenceStack objects filtered by the id column
 * @method array findByUnit(string $unit) Return AbsenceStack objects filtered by the unit column
 * @method array findByDenomination(string $denomination) Return AbsenceStack objects filtered by the denomination column
 * @method array findByRecover(boolean $recover) Return AbsenceStack objects filtered by the recover column
 * @method array findByResetType(int $reset_type) Return AbsenceStack objects filtered by the reset_type column
 * @method array findByResetToStackId(string $reset_to_stack_id) Return AbsenceStack objects filtered by the reset_to_stack_id column
 * @method array findByResetDate(string $reset_date) Return AbsenceStack objects filtered by the reset_date column
 * @method array findByResetDefaultQuota(double $reset_default_quota) Return AbsenceStack objects filtered by the reset_default_quota column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseAbsenceStackQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAbsenceStackQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\AbsenceStack';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AbsenceStackQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AbsenceStackQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AbsenceStackQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AbsenceStackQuery) {
            return $criteria;
        }
        $query = new AbsenceStackQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   AbsenceStack|AbsenceStack[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AbsenceStackPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AbsenceStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AbsenceStack A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AbsenceStack A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "unit", "denomination", "recover", "reset_type", "reset_to_stack_id", "reset_date", "reset_default_quota" FROM "absence_stack" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new AbsenceStack();
            $obj->hydrate($row);
            AbsenceStackPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return AbsenceStack|AbsenceStack[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|AbsenceStack[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AbsenceStackPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AbsenceStackPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(AbsenceStackPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(AbsenceStackPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the unit column
     *
     * Example usage:
     * <code>
     * $query->filterByUnit('fooValue');   // WHERE unit = 'fooValue'
     * $query->filterByUnit('%fooValue%'); // WHERE unit LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unit The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByUnit($unit = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unit)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unit)) {
                $unit = str_replace('*', '%', $unit);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::UNIT, $unit, $comparison);
    }

    /**
     * Filter the query on the denomination column
     *
     * Example usage:
     * <code>
     * $query->filterByDenomination('fooValue');   // WHERE denomination = 'fooValue'
     * $query->filterByDenomination('%fooValue%'); // WHERE denomination LIKE '%fooValue%'
     * </code>
     *
     * @param     string $denomination The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByDenomination($denomination = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($denomination)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $denomination)) {
                $denomination = str_replace('*', '%', $denomination);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::DENOMINATION, $denomination, $comparison);
    }

    /**
     * Filter the query on the recover column
     *
     * Example usage:
     * <code>
     * $query->filterByRecover(true); // WHERE recover = true
     * $query->filterByRecover('yes'); // WHERE recover = true
     * </code>
     *
     * @param     boolean|string $recover The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByRecover($recover = null, $comparison = null)
    {
        if (is_string($recover)) {
            $recover = in_array(strtolower($recover), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(AbsenceStackPeer::RECOVER, $recover, $comparison);
    }

    /**
     * Filter the query on the reset_type column
     *
     * Example usage:
     * <code>
     * $query->filterByResetType(1234); // WHERE reset_type = 1234
     * $query->filterByResetType(array(12, 34)); // WHERE reset_type IN (12, 34)
     * $query->filterByResetType(array('min' => 12)); // WHERE reset_type >= 12
     * $query->filterByResetType(array('max' => 12)); // WHERE reset_type <= 12
     * </code>
     *
     * @param     mixed $resetType The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByResetType($resetType = null, $comparison = null)
    {
        if (is_array($resetType)) {
            $useMinMax = false;
            if (isset($resetType['min'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_TYPE, $resetType['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($resetType['max'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_TYPE, $resetType['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::RESET_TYPE, $resetType, $comparison);
    }

    /**
     * Filter the query on the reset_to_stack_id column
     *
     * Example usage:
     * <code>
     * $query->filterByResetToStackId(1234); // WHERE reset_to_stack_id = 1234
     * $query->filterByResetToStackId(array(12, 34)); // WHERE reset_to_stack_id IN (12, 34)
     * $query->filterByResetToStackId(array('min' => 12)); // WHERE reset_to_stack_id >= 12
     * $query->filterByResetToStackId(array('max' => 12)); // WHERE reset_to_stack_id <= 12
     * </code>
     *
     * @param     mixed $resetToStackId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByResetToStackId($resetToStackId = null, $comparison = null)
    {
        if (is_array($resetToStackId)) {
            $useMinMax = false;
            if (isset($resetToStackId['min'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_TO_STACK_ID, $resetToStackId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($resetToStackId['max'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_TO_STACK_ID, $resetToStackId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::RESET_TO_STACK_ID, $resetToStackId, $comparison);
    }

    /**
     * Filter the query on the reset_date column
     *
     * Example usage:
     * <code>
     * $query->filterByResetDate('2011-03-14'); // WHERE reset_date = '2011-03-14'
     * $query->filterByResetDate('now'); // WHERE reset_date = '2011-03-14'
     * $query->filterByResetDate(array('max' => 'yesterday')); // WHERE reset_date < '2011-03-13'
     * </code>
     *
     * @param     mixed $resetDate The value to use as filter.
     *              Values can be integers (unix timestamps), DateTime objects, or strings.
     *              Empty strings are treated as NULL.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByResetDate($resetDate = null, $comparison = null)
    {
        if (is_array($resetDate)) {
            $useMinMax = false;
            if (isset($resetDate['min'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_DATE, $resetDate['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($resetDate['max'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_DATE, $resetDate['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::RESET_DATE, $resetDate, $comparison);
    }

    /**
     * Filter the query on the reset_default_quota column
     *
     * Example usage:
     * <code>
     * $query->filterByResetDefaultQuota(1234); // WHERE reset_default_quota = 1234
     * $query->filterByResetDefaultQuota(array(12, 34)); // WHERE reset_default_quota IN (12, 34)
     * $query->filterByResetDefaultQuota(array('min' => 12)); // WHERE reset_default_quota >= 12
     * $query->filterByResetDefaultQuota(array('max' => 12)); // WHERE reset_default_quota <= 12
     * </code>
     *
     * @param     mixed $resetDefaultQuota The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function filterByResetDefaultQuota($resetDefaultQuota = null, $comparison = null)
    {
        if (is_array($resetDefaultQuota)) {
            $useMinMax = false;
            if (isset($resetDefaultQuota['min'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_DEFAULT_QUOTA, $resetDefaultQuota['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($resetDefaultQuota['max'])) {
                $this->addUsingAlias(AbsenceStackPeer::RESET_DEFAULT_QUOTA, $resetDefaultQuota['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceStackPeer::RESET_DEFAULT_QUOTA, $resetDefaultQuota, $comparison);
    }

    /**
     * Filter the query by a related AbsenceKind object
     *
     * @param   AbsenceKind|PropelObjectCollection $absenceKind  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsenceStackQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAbsenceKind($absenceKind, $comparison = null)
    {
        if ($absenceKind instanceof AbsenceKind) {
            return $this
                ->addUsingAlias(AbsenceStackPeer::ID, $absenceKind->getAbsenceStack(), $comparison);
        } elseif ($absenceKind instanceof PropelObjectCollection) {
            return $this
                ->useAbsenceKindQuery()
                ->filterByPrimaryKeys($absenceKind->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAbsenceKind() only accepts arguments of type AbsenceKind or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AbsenceKind relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function joinAbsenceKind($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AbsenceKind');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AbsenceKind');
        }

        return $this;
    }

    /**
     * Use the AbsenceKind relation AbsenceKind object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsenceKindQuery A secondary query class using the current class as primary query
     */
    public function useAbsenceKindQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAbsenceKind($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AbsenceKind', '\Employee\AbsenceKindQuery');
    }

    /**
     * Filter the query by a related PersonnelStacks object
     *
     * @param   PersonnelStacks|PropelObjectCollection $personnelStacks  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsenceStackQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPersonnelStacks($personnelStacks, $comparison = null)
    {
        if ($personnelStacks instanceof PersonnelStacks) {
            return $this
                ->addUsingAlias(AbsenceStackPeer::ID, $personnelStacks->getStackId(), $comparison);
        } elseif ($personnelStacks instanceof PropelObjectCollection) {
            return $this
                ->usePersonnelStacksQuery()
                ->filterByPrimaryKeys($personnelStacks->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByPersonnelStacks() only accepts arguments of type PersonnelStacks or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the PersonnelStacks relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function joinPersonnelStacks($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('PersonnelStacks');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'PersonnelStacks');
        }

        return $this;
    }

    /**
     * Use the PersonnelStacks relation PersonnelStacks object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\PersonnelStacksQuery A secondary query class using the current class as primary query
     */
    public function usePersonnelStacksQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinPersonnelStacks($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'PersonnelStacks', '\Employee\PersonnelStacksQuery');
    }

    /**
     * Filter the query by a related StoredStack object
     *
     * @param   StoredStack|PropelObjectCollection $storedStack  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsenceStackQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredStack($storedStack, $comparison = null)
    {
        if ($storedStack instanceof StoredStack) {
            return $this
                ->addUsingAlias(AbsenceStackPeer::ID, $storedStack->getAbsenceStack(), $comparison);
        } elseif ($storedStack instanceof PropelObjectCollection) {
            return $this
                ->useStoredStackQuery()
                ->filterByPrimaryKeys($storedStack->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByStoredStack() only accepts arguments of type StoredStack or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredStack relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function joinStoredStack($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredStack');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredStack');
        }

        return $this;
    }

    /**
     * Use the StoredStack relation StoredStack object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\StoredStackQuery A secondary query class using the current class as primary query
     */
    public function useStoredStackQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinStoredStack($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredStack', '\Employee\StoredStackQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   AbsenceStack $absenceStack Object to remove from the list of results
     *
     * @return AbsenceStackQuery The current query, for fluid interface
     */
    public function prune($absenceStack = null)
    {
        if ($absenceStack) {
            $this->addUsingAlias(AbsenceStackPeer::ID, $absenceStack->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
