<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Employee\AbsenceStack;
use Employee\AbsenceStackQuery;
use Employee\StoredMonth;
use Employee\StoredMonthQuery;
use Employee\StoredStack;
use Employee\StoredStackPeer;
use Employee\StoredStackQuery;

/**
 * Base class that represents a row from the 'storage_personnel_stack' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseStoredStack extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\StoredStackPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        StoredStackPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id field.
     * @var        int
     */
    protected $id;

    /**
     * The value for the storage_personnel_presences field.
     * @var        string
     */
    protected $storage_personnel_presences;

    /**
     * The value for the absence_stack field.
     * @var        string
     */
    protected $absence_stack;

    /**
     * The value for the stack_denomination field.
     * @var        string
     */
    protected $stack_denomination;

    /**
     * The value for the value_start_o field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $value_start_o;

    /**
     * The value for the value_end_o field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $value_end_o;

    /**
     * The value for the value_start field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $value_start;

    /**
     * The value for the value_end field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $value_end;

    /**
     * The value for the unit field.
     * Note: this column has a database default value of: 'h'
     * @var        string
     */
    protected $unit;

    /**
     * The value for the recover field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $recover;

    /**
     * The value for the reset_type_applied field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $reset_type_applied;

    /**
     * @var        AbsenceStack
     */
    protected $aStoredStackAbsenceStack;

    /**
     * @var        StoredMonth
     */
    protected $aStoredStackStoredMonth;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->value_start_o = 0;
        $this->value_end_o = 0;
        $this->value_start = 0;
        $this->value_end = 0;
        $this->unit = 'h';
        $this->recover = false;
        $this->reset_type_applied = 0;
    }

    /**
     * Initializes internal state of BaseStoredStack object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id] column value.
     *
     * @return int
     */
    public function getStoredStackId()
    {

        return $this->id;
    }

    /**
     * Get the [storage_personnel_presences] column value.
     *
     * @return string
     */
    public function getStoredMonth()
    {

        return $this->storage_personnel_presences;
    }

    /**
     * Get the [absence_stack] column value.
     *
     * @return string
     */
    public function getAbsenceStack()
    {

        return $this->absence_stack;
    }

    /**
     * Get the [stack_denomination] column value.
     *
     * @return string
     */
    public function getStackDenomination()
    {

        return $this->stack_denomination;
    }

    /**
     * Get the [value_start_o] column value.
     *
     * @return double
     */
    public function getValueStartOriginal()
    {

        return $this->value_start_o;
    }

    /**
     * Get the [value_end_o] column value.
     *
     * @return double
     */
    public function getValueEndOriginal()
    {

        return $this->value_end_o;
    }

    /**
     * Get the [value_start] column value.
     *
     * @return double
     */
    public function getValueStart()
    {

        return $this->value_start;
    }

    /**
     * Get the [value_end] column value.
     *
     * @return double
     */
    public function getValueEnd()
    {

        return $this->value_end;
    }

    /**
     * Get the [unit] column value.
     *
     * @return string
     */
    public function getUnit()
    {

        return $this->unit;
    }

    /**
     * Get the [recover] column value.
     *
     * @return boolean
     */
    public function getRecover()
    {

        return $this->recover;
    }

    /**
     * Get the [reset_type_applied] column value.
     *
     * @return int
     */
    public function getResetTypeApplied()
    {

        return $this->reset_type_applied;
    }

    /**
     * Set the value of [id] column.
     *
     * @param  int $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setStoredStackId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id !== $v) {
            $this->id = $v;
            $this->modifiedColumns[] = StoredStackPeer::ID;
        }


        return $this;
    } // setStoredStackId()

    /**
     * Set the value of [storage_personnel_presences] column.
     *
     * @param  string $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setStoredMonth($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->storage_personnel_presences !== $v) {
            $this->storage_personnel_presences = $v;
            $this->modifiedColumns[] = StoredStackPeer::STORAGE_PERSONNEL_PRESENCES;
        }

        if ($this->aStoredStackStoredMonth !== null && $this->aStoredStackStoredMonth->getStoredMonthId() !== $v) {
            $this->aStoredStackStoredMonth = null;
        }


        return $this;
    } // setStoredMonth()

    /**
     * Set the value of [absence_stack] column.
     *
     * @param  string $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setAbsenceStack($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->absence_stack !== $v) {
            $this->absence_stack = $v;
            $this->modifiedColumns[] = StoredStackPeer::ABSENCE_STACK;
        }

        if ($this->aStoredStackAbsenceStack !== null && $this->aStoredStackAbsenceStack->getId() !== $v) {
            $this->aStoredStackAbsenceStack = null;
        }


        return $this;
    } // setAbsenceStack()

    /**
     * Set the value of [stack_denomination] column.
     *
     * @param  string $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setStackDenomination($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->stack_denomination !== $v) {
            $this->stack_denomination = $v;
            $this->modifiedColumns[] = StoredStackPeer::STACK_DENOMINATION;
        }


        return $this;
    } // setStackDenomination()

    /**
     * Set the value of [value_start_o] column.
     *
     * @param  double $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setValueStartOriginal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->value_start_o !== $v) {
            $this->value_start_o = $v;
            $this->modifiedColumns[] = StoredStackPeer::VALUE_START_O;
        }


        return $this;
    } // setValueStartOriginal()

    /**
     * Set the value of [value_end_o] column.
     *
     * @param  double $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setValueEndOriginal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->value_end_o !== $v) {
            $this->value_end_o = $v;
            $this->modifiedColumns[] = StoredStackPeer::VALUE_END_O;
        }


        return $this;
    } // setValueEndOriginal()

    /**
     * Set the value of [value_start] column.
     *
     * @param  double $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setValueStart($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->value_start !== $v) {
            $this->value_start = $v;
            $this->modifiedColumns[] = StoredStackPeer::VALUE_START;
        }


        return $this;
    } // setValueStart()

    /**
     * Set the value of [value_end] column.
     *
     * @param  double $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setValueEnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->value_end !== $v) {
            $this->value_end = $v;
            $this->modifiedColumns[] = StoredStackPeer::VALUE_END;
        }


        return $this;
    } // setValueEnd()

    /**
     * Set the value of [unit] column.
     *
     * @param  string $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setUnit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unit !== $v) {
            $this->unit = $v;
            $this->modifiedColumns[] = StoredStackPeer::UNIT;
        }


        return $this;
    } // setUnit()

    /**
     * Sets the value of the [recover] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setRecover($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->recover !== $v) {
            $this->recover = $v;
            $this->modifiedColumns[] = StoredStackPeer::RECOVER;
        }


        return $this;
    } // setRecover()

    /**
     * Set the value of [reset_type_applied] column.
     *
     * @param  int $v new value
     * @return StoredStack The current object (for fluent API support)
     */
    public function setResetTypeApplied($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->reset_type_applied !== $v) {
            $this->reset_type_applied = $v;
            $this->modifiedColumns[] = StoredStackPeer::RESET_TYPE_APPLIED;
        }


        return $this;
    } // setResetTypeApplied()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->value_start_o !== 0) {
                return false;
            }

            if ($this->value_end_o !== 0) {
                return false;
            }

            if ($this->value_start !== 0) {
                return false;
            }

            if ($this->value_end !== 0) {
                return false;
            }

            if ($this->unit !== 'h') {
                return false;
            }

            if ($this->recover !== false) {
                return false;
            }

            if ($this->reset_type_applied !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->storage_personnel_presences = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->absence_stack = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->stack_denomination = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->value_start_o = ($row[$startcol + 4] !== null) ? (double) $row[$startcol + 4] : null;
            $this->value_end_o = ($row[$startcol + 5] !== null) ? (double) $row[$startcol + 5] : null;
            $this->value_start = ($row[$startcol + 6] !== null) ? (double) $row[$startcol + 6] : null;
            $this->value_end = ($row[$startcol + 7] !== null) ? (double) $row[$startcol + 7] : null;
            $this->unit = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->recover = ($row[$startcol + 9] !== null) ? (boolean) $row[$startcol + 9] : null;
            $this->reset_type_applied = ($row[$startcol + 10] !== null) ? (int) $row[$startcol + 10] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 11; // 11 = StoredStackPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating StoredStack object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aStoredStackStoredMonth !== null && $this->storage_personnel_presences !== $this->aStoredStackStoredMonth->getStoredMonthId()) {
            $this->aStoredStackStoredMonth = null;
        }
        if ($this->aStoredStackAbsenceStack !== null && $this->absence_stack !== $this->aStoredStackAbsenceStack->getId()) {
            $this->aStoredStackAbsenceStack = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = StoredStackPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aStoredStackAbsenceStack = null;
            $this->aStoredStackStoredMonth = null;
        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = StoredStackQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                StoredStackPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aStoredStackAbsenceStack !== null) {
                if ($this->aStoredStackAbsenceStack->isModified() || $this->aStoredStackAbsenceStack->isNew()) {
                    $affectedRows += $this->aStoredStackAbsenceStack->save($con);
                }
                $this->setStoredStackAbsenceStack($this->aStoredStackAbsenceStack);
            }

            if ($this->aStoredStackStoredMonth !== null) {
                if ($this->aStoredStackStoredMonth->isModified() || $this->aStoredStackStoredMonth->isNew()) {
                    $affectedRows += $this->aStoredStackStoredMonth->save($con);
                }
                $this->setStoredStackStoredMonth($this->aStoredStackStoredMonth);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = StoredStackPeer::ID;
        if (null !== $this->id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . StoredStackPeer::ID . ')');
        }
        if (null === $this->id) {
            try {
                $stmt = $con->query("SELECT nextval('storage_personnel_stack_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(StoredStackPeer::ID)) {
            $modifiedColumns[':p' . $index++]  = '"id"';
        }
        if ($this->isColumnModified(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES)) {
            $modifiedColumns[':p' . $index++]  = '"storage_personnel_presences"';
        }
        if ($this->isColumnModified(StoredStackPeer::ABSENCE_STACK)) {
            $modifiedColumns[':p' . $index++]  = '"absence_stack"';
        }
        if ($this->isColumnModified(StoredStackPeer::STACK_DENOMINATION)) {
            $modifiedColumns[':p' . $index++]  = '"stack_denomination"';
        }
        if ($this->isColumnModified(StoredStackPeer::VALUE_START_O)) {
            $modifiedColumns[':p' . $index++]  = '"value_start_o"';
        }
        if ($this->isColumnModified(StoredStackPeer::VALUE_END_O)) {
            $modifiedColumns[':p' . $index++]  = '"value_end_o"';
        }
        if ($this->isColumnModified(StoredStackPeer::VALUE_START)) {
            $modifiedColumns[':p' . $index++]  = '"value_start"';
        }
        if ($this->isColumnModified(StoredStackPeer::VALUE_END)) {
            $modifiedColumns[':p' . $index++]  = '"value_end"';
        }
        if ($this->isColumnModified(StoredStackPeer::UNIT)) {
            $modifiedColumns[':p' . $index++]  = '"unit"';
        }
        if ($this->isColumnModified(StoredStackPeer::RECOVER)) {
            $modifiedColumns[':p' . $index++]  = '"recover"';
        }
        if ($this->isColumnModified(StoredStackPeer::RESET_TYPE_APPLIED)) {
            $modifiedColumns[':p' . $index++]  = '"reset_type_applied"';
        }

        $sql = sprintf(
            'INSERT INTO "storage_personnel_stack" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id"':
                        $stmt->bindValue($identifier, $this->id, PDO::PARAM_INT);
                        break;
                    case '"storage_personnel_presences"':
                        $stmt->bindValue($identifier, $this->storage_personnel_presences, PDO::PARAM_STR);
                        break;
                    case '"absence_stack"':
                        $stmt->bindValue($identifier, $this->absence_stack, PDO::PARAM_STR);
                        break;
                    case '"stack_denomination"':
                        $stmt->bindValue($identifier, $this->stack_denomination, PDO::PARAM_STR);
                        break;
                    case '"value_start_o"':
                        $stmt->bindValue($identifier, $this->value_start_o, PDO::PARAM_STR);
                        break;
                    case '"value_end_o"':
                        $stmt->bindValue($identifier, $this->value_end_o, PDO::PARAM_STR);
                        break;
                    case '"value_start"':
                        $stmt->bindValue($identifier, $this->value_start, PDO::PARAM_STR);
                        break;
                    case '"value_end"':
                        $stmt->bindValue($identifier, $this->value_end, PDO::PARAM_STR);
                        break;
                    case '"unit"':
                        $stmt->bindValue($identifier, $this->unit, PDO::PARAM_STR);
                        break;
                    case '"recover"':
                        $stmt->bindValue($identifier, $this->recover, PDO::PARAM_BOOL);
                        break;
                    case '"reset_type_applied"':
                        $stmt->bindValue($identifier, $this->reset_type_applied, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aStoredStackAbsenceStack !== null) {
                if (!$this->aStoredStackAbsenceStack->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aStoredStackAbsenceStack->getValidationFailures());
                }
            }

            if ($this->aStoredStackStoredMonth !== null) {
                if (!$this->aStoredStackStoredMonth->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aStoredStackStoredMonth->getValidationFailures());
                }
            }


            if (($retval = StoredStackPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = StoredStackPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getStoredStackId();
                break;
            case 1:
                return $this->getStoredMonth();
                break;
            case 2:
                return $this->getAbsenceStack();
                break;
            case 3:
                return $this->getStackDenomination();
                break;
            case 4:
                return $this->getValueStartOriginal();
                break;
            case 5:
                return $this->getValueEndOriginal();
                break;
            case 6:
                return $this->getValueStart();
                break;
            case 7:
                return $this->getValueEnd();
                break;
            case 8:
                return $this->getUnit();
                break;
            case 9:
                return $this->getRecover();
                break;
            case 10:
                return $this->getResetTypeApplied();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['StoredStack'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['StoredStack'][$this->getPrimaryKey()] = true;
        $keys = StoredStackPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getStoredStackId(),
            $keys[1] => $this->getStoredMonth(),
            $keys[2] => $this->getAbsenceStack(),
            $keys[3] => $this->getStackDenomination(),
            $keys[4] => $this->getValueStartOriginal(),
            $keys[5] => $this->getValueEndOriginal(),
            $keys[6] => $this->getValueStart(),
            $keys[7] => $this->getValueEnd(),
            $keys[8] => $this->getUnit(),
            $keys[9] => $this->getRecover(),
            $keys[10] => $this->getResetTypeApplied(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aStoredStackAbsenceStack) {
                $result['StoredStackAbsenceStack'] = $this->aStoredStackAbsenceStack->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aStoredStackStoredMonth) {
                $result['StoredStackStoredMonth'] = $this->aStoredStackStoredMonth->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = StoredStackPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setStoredStackId($value);
                break;
            case 1:
                $this->setStoredMonth($value);
                break;
            case 2:
                $this->setAbsenceStack($value);
                break;
            case 3:
                $this->setStackDenomination($value);
                break;
            case 4:
                $this->setValueStartOriginal($value);
                break;
            case 5:
                $this->setValueEndOriginal($value);
                break;
            case 6:
                $this->setValueStart($value);
                break;
            case 7:
                $this->setValueEnd($value);
                break;
            case 8:
                $this->setUnit($value);
                break;
            case 9:
                $this->setRecover($value);
                break;
            case 10:
                $this->setResetTypeApplied($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = StoredStackPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setStoredStackId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setStoredMonth($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setAbsenceStack($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setStackDenomination($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setValueStartOriginal($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setValueEndOriginal($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setValueStart($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setValueEnd($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setUnit($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setRecover($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setResetTypeApplied($arr[$keys[10]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(StoredStackPeer::DATABASE_NAME);

        if ($this->isColumnModified(StoredStackPeer::ID)) $criteria->add(StoredStackPeer::ID, $this->id);
        if ($this->isColumnModified(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES)) $criteria->add(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, $this->storage_personnel_presences);
        if ($this->isColumnModified(StoredStackPeer::ABSENCE_STACK)) $criteria->add(StoredStackPeer::ABSENCE_STACK, $this->absence_stack);
        if ($this->isColumnModified(StoredStackPeer::STACK_DENOMINATION)) $criteria->add(StoredStackPeer::STACK_DENOMINATION, $this->stack_denomination);
        if ($this->isColumnModified(StoredStackPeer::VALUE_START_O)) $criteria->add(StoredStackPeer::VALUE_START_O, $this->value_start_o);
        if ($this->isColumnModified(StoredStackPeer::VALUE_END_O)) $criteria->add(StoredStackPeer::VALUE_END_O, $this->value_end_o);
        if ($this->isColumnModified(StoredStackPeer::VALUE_START)) $criteria->add(StoredStackPeer::VALUE_START, $this->value_start);
        if ($this->isColumnModified(StoredStackPeer::VALUE_END)) $criteria->add(StoredStackPeer::VALUE_END, $this->value_end);
        if ($this->isColumnModified(StoredStackPeer::UNIT)) $criteria->add(StoredStackPeer::UNIT, $this->unit);
        if ($this->isColumnModified(StoredStackPeer::RECOVER)) $criteria->add(StoredStackPeer::RECOVER, $this->recover);
        if ($this->isColumnModified(StoredStackPeer::RESET_TYPE_APPLIED)) $criteria->add(StoredStackPeer::RESET_TYPE_APPLIED, $this->reset_type_applied);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(StoredStackPeer::DATABASE_NAME);
        $criteria->add(StoredStackPeer::ID, $this->id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getStoredStackId();
    }

    /**
     * Generic method to set the primary key (id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setStoredStackId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getStoredStackId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of StoredStack (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setStoredMonth($this->getStoredMonth());
        $copyObj->setAbsenceStack($this->getAbsenceStack());
        $copyObj->setStackDenomination($this->getStackDenomination());
        $copyObj->setValueStartOriginal($this->getValueStartOriginal());
        $copyObj->setValueEndOriginal($this->getValueEndOriginal());
        $copyObj->setValueStart($this->getValueStart());
        $copyObj->setValueEnd($this->getValueEnd());
        $copyObj->setUnit($this->getUnit());
        $copyObj->setRecover($this->getRecover());
        $copyObj->setResetTypeApplied($this->getResetTypeApplied());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setStoredStackId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return StoredStack Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return StoredStackPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new StoredStackPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a AbsenceStack object.
     *
     * @param                  AbsenceStack $v
     * @return StoredStack The current object (for fluent API support)
     * @throws PropelException
     */
    public function setStoredStackAbsenceStack(AbsenceStack $v = null)
    {
        if ($v === null) {
            $this->setAbsenceStack(NULL);
        } else {
            $this->setAbsenceStack($v->getId());
        }

        $this->aStoredStackAbsenceStack = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the AbsenceStack object, it will not be re-added.
        if ($v !== null) {
            $v->addStoredStack($this);
        }


        return $this;
    }


    /**
     * Get the associated AbsenceStack object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return AbsenceStack The associated AbsenceStack object.
     * @throws PropelException
     */
    public function getStoredStackAbsenceStack(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aStoredStackAbsenceStack === null && (($this->absence_stack !== "" && $this->absence_stack !== null)) && $doQuery) {
            $this->aStoredStackAbsenceStack = AbsenceStackQuery::create()->findPk($this->absence_stack, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aStoredStackAbsenceStack->addStoredStacks($this);
             */
        }

        return $this->aStoredStackAbsenceStack;
    }

    /**
     * Declares an association between this object and a StoredMonth object.
     *
     * @param                  StoredMonth $v
     * @return StoredStack The current object (for fluent API support)
     * @throws PropelException
     */
    public function setStoredStackStoredMonth(StoredMonth $v = null)
    {
        if ($v === null) {
            $this->setStoredMonth(NULL);
        } else {
            $this->setStoredMonth($v->getStoredMonthId());
        }

        $this->aStoredStackStoredMonth = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the StoredMonth object, it will not be re-added.
        if ($v !== null) {
            $v->addStoredStack($this);
        }


        return $this;
    }


    /**
     * Get the associated StoredMonth object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return StoredMonth The associated StoredMonth object.
     * @throws PropelException
     */
    public function getStoredStackStoredMonth(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aStoredStackStoredMonth === null && (($this->storage_personnel_presences !== "" && $this->storage_personnel_presences !== null)) && $doQuery) {
            $this->aStoredStackStoredMonth = StoredMonthQuery::create()->findPk($this->storage_personnel_presences, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aStoredStackStoredMonth->addStoredStacks($this);
             */
        }

        return $this->aStoredStackStoredMonth;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id = null;
        $this->storage_personnel_presences = null;
        $this->absence_stack = null;
        $this->stack_denomination = null;
        $this->value_start_o = null;
        $this->value_end_o = null;
        $this->value_start = null;
        $this->value_end = null;
        $this->unit = null;
        $this->recover = null;
        $this->reset_type_applied = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->aStoredStackAbsenceStack instanceof Persistent) {
              $this->aStoredStackAbsenceStack->clearAllReferences($deep);
            }
            if ($this->aStoredStackStoredMonth instanceof Persistent) {
              $this->aStoredStackStoredMonth->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        $this->aStoredStackAbsenceStack = null;
        $this->aStoredStackStoredMonth = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(StoredStackPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
