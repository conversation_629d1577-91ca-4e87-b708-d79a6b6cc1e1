<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\Employee;
use Employee\EmployeeQuery;
use Employee\StoredMonth;
use Employee\StoredMonthPeer;
use Employee\StoredMonthQuery;
use Employee\StoredStack;
use Employee\StoredStackQuery;

/**
 * Base class that represents a row from the 'storage_personnel_presences' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseStoredMonth extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\StoredMonthPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        StoredMonthPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the storage_personnel_presences_id field.
     * @var        int
     */
    protected $storage_personnel_presences_id;

    /**
     * The value for the employee_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $employee_id;

    /**
     * The value for the date_start field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $date_start;

    /**
     * The value for the date_end field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $date_end;

    /**
     * The value for the ext_start_o field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $ext_start_o;

    /**
     * The value for the ext_end_o field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $ext_end_o;

    /**
     * The value for the ext_start field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $ext_start;

    /**
     * The value for the ext_end field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $ext_end;

    /**
     * The value for the note field.
     * @var        string
     */
    protected $note;

    /**
     * @var        Employee
     */
    protected $aStoredMonthEmployee;

    /**
     * @var        PropelObjectCollection|StoredStack[] Collection to store aggregation of StoredStack objects.
     */
    protected $collStoredStacks;
    protected $collStoredStacksPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $storedStacksScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->employee_id = 0;
        $this->date_start = '0';
        $this->date_end = '0';
        $this->ext_start_o = 0;
        $this->ext_end_o = 0;
        $this->ext_start = 0;
        $this->ext_end = 0;
    }

    /**
     * Initializes internal state of BaseStoredMonth object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [storage_personnel_presences_id] column value.
     *
     * @return int
     */
    public function getStoredMonthId()
    {

        return $this->storage_personnel_presences_id;
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return int
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [date_start] column value.
     *
     * @return string
     */
    public function getDateStart()
    {

        return $this->date_start;
    }

    /**
     * Get the [date_end] column value.
     *
     * @return string
     */
    public function getDateEnd()
    {

        return $this->date_end;
    }

    /**
     * Get the [ext_start_o] column value.
     *
     * @return int
     */
    public function getExtStartOriginal()
    {

        return $this->ext_start_o;
    }

    /**
     * Get the [ext_end_o] column value.
     *
     * @return int
     */
    public function getExtEndOriginal()
    {

        return $this->ext_end_o;
    }

    /**
     * Get the [ext_start] column value.
     *
     * @return int
     */
    public function getExtStart()
    {

        return $this->ext_start;
    }

    /**
     * Get the [ext_end] column value.
     *
     * @return int
     */
    public function getExtEnd()
    {

        return $this->ext_end;
    }

    /**
     * Get the [note] column value.
     *
     * @return string
     */
    public function getNote()
    {

        return $this->note;
    }

    /**
     * Set the value of [storage_personnel_presences_id] column.
     *
     * @param  int $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setStoredMonthId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->storage_personnel_presences_id !== $v) {
            $this->storage_personnel_presences_id = $v;
            $this->modifiedColumns[] = StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID;
        }


        return $this;
    } // setStoredMonthId()

    /**
     * Set the value of [employee_id] column.
     *
     * @param  int $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = StoredMonthPeer::EMPLOYEE_ID;
        }

        if ($this->aStoredMonthEmployee !== null && $this->aStoredMonthEmployee->getEmployeeId() !== $v) {
            $this->aStoredMonthEmployee = null;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [date_start] column.
     *
     * @param  string $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setDateStart($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->date_start !== $v) {
            $this->date_start = $v;
            $this->modifiedColumns[] = StoredMonthPeer::DATE_START;
        }


        return $this;
    } // setDateStart()

    /**
     * Set the value of [date_end] column.
     *
     * @param  string $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setDateEnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->date_end !== $v) {
            $this->date_end = $v;
            $this->modifiedColumns[] = StoredMonthPeer::DATE_END;
        }


        return $this;
    } // setDateEnd()

    /**
     * Set the value of [ext_start_o] column.
     *
     * @param  int $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setExtStartOriginal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->ext_start_o !== $v) {
            $this->ext_start_o = $v;
            $this->modifiedColumns[] = StoredMonthPeer::EXT_START_O;
        }


        return $this;
    } // setExtStartOriginal()

    /**
     * Set the value of [ext_end_o] column.
     *
     * @param  int $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setExtEndOriginal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->ext_end_o !== $v) {
            $this->ext_end_o = $v;
            $this->modifiedColumns[] = StoredMonthPeer::EXT_END_O;
        }


        return $this;
    } // setExtEndOriginal()

    /**
     * Set the value of [ext_start] column.
     *
     * @param  int $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setExtStart($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->ext_start !== $v) {
            $this->ext_start = $v;
            $this->modifiedColumns[] = StoredMonthPeer::EXT_START;
        }


        return $this;
    } // setExtStart()

    /**
     * Set the value of [ext_end] column.
     *
     * @param  int $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setExtEnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->ext_end !== $v) {
            $this->ext_end = $v;
            $this->modifiedColumns[] = StoredMonthPeer::EXT_END;
        }


        return $this;
    } // setExtEnd()

    /**
     * Set the value of [note] column.
     *
     * @param  string $v new value
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setNote($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->note !== $v) {
            $this->note = $v;
            $this->modifiedColumns[] = StoredMonthPeer::NOTE;
        }


        return $this;
    } // setNote()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->employee_id !== 0) {
                return false;
            }

            if ($this->date_start !== '0') {
                return false;
            }

            if ($this->date_end !== '0') {
                return false;
            }

            if ($this->ext_start_o !== 0) {
                return false;
            }

            if ($this->ext_end_o !== 0) {
                return false;
            }

            if ($this->ext_start !== 0) {
                return false;
            }

            if ($this->ext_end !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->storage_personnel_presences_id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->employee_id = ($row[$startcol + 1] !== null) ? (int) $row[$startcol + 1] : null;
            $this->date_start = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->date_end = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->ext_start_o = ($row[$startcol + 4] !== null) ? (int) $row[$startcol + 4] : null;
            $this->ext_end_o = ($row[$startcol + 5] !== null) ? (int) $row[$startcol + 5] : null;
            $this->ext_start = ($row[$startcol + 6] !== null) ? (int) $row[$startcol + 6] : null;
            $this->ext_end = ($row[$startcol + 7] !== null) ? (int) $row[$startcol + 7] : null;
            $this->note = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 9; // 9 = StoredMonthPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating StoredMonth object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aStoredMonthEmployee !== null && $this->employee_id !== $this->aStoredMonthEmployee->getEmployeeId()) {
            $this->aStoredMonthEmployee = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = StoredMonthPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aStoredMonthEmployee = null;
            $this->collStoredStacks = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = StoredMonthQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                StoredMonthPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aStoredMonthEmployee !== null) {
                if ($this->aStoredMonthEmployee->isModified() || $this->aStoredMonthEmployee->isNew()) {
                    $affectedRows += $this->aStoredMonthEmployee->save($con);
                }
                $this->setStoredMonthEmployee($this->aStoredMonthEmployee);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->storedStacksScheduledForDeletion !== null) {
                if (!$this->storedStacksScheduledForDeletion->isEmpty()) {
                    StoredStackQuery::create()
                        ->filterByPrimaryKeys($this->storedStacksScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->storedStacksScheduledForDeletion = null;
                }
            }

            if ($this->collStoredStacks !== null) {
                foreach ($this->collStoredStacks as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID;
        if (null !== $this->storage_personnel_presences_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID . ')');
        }
        if (null === $this->storage_personnel_presences_id) {
            try {
                $stmt = $con->query("SELECT nextval('storage_personnel_presences_storage_personnel_presences_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->storage_personnel_presences_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID)) {
            $modifiedColumns[':p' . $index++]  = '"storage_personnel_presences_id"';
        }
        if ($this->isColumnModified(StoredMonthPeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(StoredMonthPeer::DATE_START)) {
            $modifiedColumns[':p' . $index++]  = '"date_start"';
        }
        if ($this->isColumnModified(StoredMonthPeer::DATE_END)) {
            $modifiedColumns[':p' . $index++]  = '"date_end"';
        }
        if ($this->isColumnModified(StoredMonthPeer::EXT_START_O)) {
            $modifiedColumns[':p' . $index++]  = '"ext_start_o"';
        }
        if ($this->isColumnModified(StoredMonthPeer::EXT_END_O)) {
            $modifiedColumns[':p' . $index++]  = '"ext_end_o"';
        }
        if ($this->isColumnModified(StoredMonthPeer::EXT_START)) {
            $modifiedColumns[':p' . $index++]  = '"ext_start"';
        }
        if ($this->isColumnModified(StoredMonthPeer::EXT_END)) {
            $modifiedColumns[':p' . $index++]  = '"ext_end"';
        }
        if ($this->isColumnModified(StoredMonthPeer::NOTE)) {
            $modifiedColumns[':p' . $index++]  = '"note"';
        }

        $sql = sprintf(
            'INSERT INTO "storage_personnel_presences" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"storage_personnel_presences_id"':
                        $stmt->bindValue($identifier, $this->storage_personnel_presences_id, PDO::PARAM_INT);
                        break;
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_INT);
                        break;
                    case '"date_start"':
                        $stmt->bindValue($identifier, $this->date_start, PDO::PARAM_STR);
                        break;
                    case '"date_end"':
                        $stmt->bindValue($identifier, $this->date_end, PDO::PARAM_STR);
                        break;
                    case '"ext_start_o"':
                        $stmt->bindValue($identifier, $this->ext_start_o, PDO::PARAM_INT);
                        break;
                    case '"ext_end_o"':
                        $stmt->bindValue($identifier, $this->ext_end_o, PDO::PARAM_INT);
                        break;
                    case '"ext_start"':
                        $stmt->bindValue($identifier, $this->ext_start, PDO::PARAM_INT);
                        break;
                    case '"ext_end"':
                        $stmt->bindValue($identifier, $this->ext_end, PDO::PARAM_INT);
                        break;
                    case '"note"':
                        $stmt->bindValue($identifier, $this->note, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aStoredMonthEmployee !== null) {
                if (!$this->aStoredMonthEmployee->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aStoredMonthEmployee->getValidationFailures());
                }
            }


            if (($retval = StoredMonthPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collStoredStacks !== null) {
                    foreach ($this->collStoredStacks as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = StoredMonthPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getStoredMonthId();
                break;
            case 1:
                return $this->getEmployeeId();
                break;
            case 2:
                return $this->getDateStart();
                break;
            case 3:
                return $this->getDateEnd();
                break;
            case 4:
                return $this->getExtStartOriginal();
                break;
            case 5:
                return $this->getExtEndOriginal();
                break;
            case 6:
                return $this->getExtStart();
                break;
            case 7:
                return $this->getExtEnd();
                break;
            case 8:
                return $this->getNote();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['StoredMonth'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['StoredMonth'][$this->getPrimaryKey()] = true;
        $keys = StoredMonthPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getStoredMonthId(),
            $keys[1] => $this->getEmployeeId(),
            $keys[2] => $this->getDateStart(),
            $keys[3] => $this->getDateEnd(),
            $keys[4] => $this->getExtStartOriginal(),
            $keys[5] => $this->getExtEndOriginal(),
            $keys[6] => $this->getExtStart(),
            $keys[7] => $this->getExtEnd(),
            $keys[8] => $this->getNote(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aStoredMonthEmployee) {
                $result['StoredMonthEmployee'] = $this->aStoredMonthEmployee->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collStoredStacks) {
                $result['StoredStacks'] = $this->collStoredStacks->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = StoredMonthPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setStoredMonthId($value);
                break;
            case 1:
                $this->setEmployeeId($value);
                break;
            case 2:
                $this->setDateStart($value);
                break;
            case 3:
                $this->setDateEnd($value);
                break;
            case 4:
                $this->setExtStartOriginal($value);
                break;
            case 5:
                $this->setExtEndOriginal($value);
                break;
            case 6:
                $this->setExtStart($value);
                break;
            case 7:
                $this->setExtEnd($value);
                break;
            case 8:
                $this->setNote($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = StoredMonthPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setStoredMonthId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setEmployeeId($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setDateStart($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setDateEnd($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setExtStartOriginal($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setExtEndOriginal($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setExtStart($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setExtEnd($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setNote($arr[$keys[8]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(StoredMonthPeer::DATABASE_NAME);

        if ($this->isColumnModified(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID)) $criteria->add(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $this->storage_personnel_presences_id);
        if ($this->isColumnModified(StoredMonthPeer::EMPLOYEE_ID)) $criteria->add(StoredMonthPeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(StoredMonthPeer::DATE_START)) $criteria->add(StoredMonthPeer::DATE_START, $this->date_start);
        if ($this->isColumnModified(StoredMonthPeer::DATE_END)) $criteria->add(StoredMonthPeer::DATE_END, $this->date_end);
        if ($this->isColumnModified(StoredMonthPeer::EXT_START_O)) $criteria->add(StoredMonthPeer::EXT_START_O, $this->ext_start_o);
        if ($this->isColumnModified(StoredMonthPeer::EXT_END_O)) $criteria->add(StoredMonthPeer::EXT_END_O, $this->ext_end_o);
        if ($this->isColumnModified(StoredMonthPeer::EXT_START)) $criteria->add(StoredMonthPeer::EXT_START, $this->ext_start);
        if ($this->isColumnModified(StoredMonthPeer::EXT_END)) $criteria->add(StoredMonthPeer::EXT_END, $this->ext_end);
        if ($this->isColumnModified(StoredMonthPeer::NOTE)) $criteria->add(StoredMonthPeer::NOTE, $this->note);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(StoredMonthPeer::DATABASE_NAME);
        $criteria->add(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $this->storage_personnel_presences_id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getStoredMonthId();
    }

    /**
     * Generic method to set the primary key (storage_personnel_presences_id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setStoredMonthId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getStoredMonthId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of StoredMonth (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setEmployeeId($this->getEmployeeId());
        $copyObj->setDateStart($this->getDateStart());
        $copyObj->setDateEnd($this->getDateEnd());
        $copyObj->setExtStartOriginal($this->getExtStartOriginal());
        $copyObj->setExtEndOriginal($this->getExtEndOriginal());
        $copyObj->setExtStart($this->getExtStart());
        $copyObj->setExtEnd($this->getExtEnd());
        $copyObj->setNote($this->getNote());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getStoredStacks() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addStoredStack($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setStoredMonthId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return StoredMonth Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return StoredMonthPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new StoredMonthPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Employee object.
     *
     * @param                  Employee $v
     * @return StoredMonth The current object (for fluent API support)
     * @throws PropelException
     */
    public function setStoredMonthEmployee(Employee $v = null)
    {
        if ($v === null) {
            $this->setEmployeeId(0);
        } else {
            $this->setEmployeeId($v->getEmployeeId());
        }

        $this->aStoredMonthEmployee = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Employee object, it will not be re-added.
        if ($v !== null) {
            $v->addStoredMonth($this);
        }


        return $this;
    }


    /**
     * Get the associated Employee object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Employee The associated Employee object.
     * @throws PropelException
     */
    public function getStoredMonthEmployee(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aStoredMonthEmployee === null && ($this->employee_id !== null) && $doQuery) {
            $this->aStoredMonthEmployee = EmployeeQuery::create()->findPk($this->employee_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aStoredMonthEmployee->addStoredMonths($this);
             */
        }

        return $this->aStoredMonthEmployee;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('StoredStack' == $relationName) {
            $this->initStoredStacks();
        }
    }

    /**
     * Clears out the collStoredStacks collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return StoredMonth The current object (for fluent API support)
     * @see        addStoredStacks()
     */
    public function clearStoredStacks()
    {
        $this->collStoredStacks = null; // important to set this to null since that means it is uninitialized
        $this->collStoredStacksPartial = null;

        return $this;
    }

    /**
     * reset is the collStoredStacks collection loaded partially
     *
     * @return void
     */
    public function resetPartialStoredStacks($v = true)
    {
        $this->collStoredStacksPartial = $v;
    }

    /**
     * Initializes the collStoredStacks collection.
     *
     * By default this just sets the collStoredStacks collection to an empty array (like clearcollStoredStacks());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initStoredStacks($overrideExisting = true)
    {
        if (null !== $this->collStoredStacks && !$overrideExisting) {
            return;
        }
        $this->collStoredStacks = new PropelObjectCollection();
        $this->collStoredStacks->setModel('StoredStack');
    }

    /**
     * Gets an array of StoredStack objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this StoredMonth is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|StoredStack[] List of StoredStack objects
     * @throws PropelException
     */
    public function getStoredStacks($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collStoredStacksPartial && !$this->isNew();
        if (null === $this->collStoredStacks || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collStoredStacks) {
                // return empty collection
                $this->initStoredStacks();
            } else {
                $collStoredStacks = StoredStackQuery::create(null, $criteria)
                    ->filterByStoredStackStoredMonth($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collStoredStacksPartial && count($collStoredStacks)) {
                      $this->initStoredStacks(false);

                      foreach ($collStoredStacks as $obj) {
                        if (false == $this->collStoredStacks->contains($obj)) {
                          $this->collStoredStacks->append($obj);
                        }
                      }

                      $this->collStoredStacksPartial = true;
                    }

                    $collStoredStacks->getInternalIterator()->rewind();

                    return $collStoredStacks;
                }

                if ($partial && $this->collStoredStacks) {
                    foreach ($this->collStoredStacks as $obj) {
                        if ($obj->isNew()) {
                            $collStoredStacks[] = $obj;
                        }
                    }
                }

                $this->collStoredStacks = $collStoredStacks;
                $this->collStoredStacksPartial = false;
            }
        }

        return $this->collStoredStacks;
    }

    /**
     * Sets a collection of StoredStack objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $storedStacks A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return StoredMonth The current object (for fluent API support)
     */
    public function setStoredStacks(PropelCollection $storedStacks, PropelPDO $con = null)
    {
        $storedStacksToDelete = $this->getStoredStacks(new Criteria(), $con)->diff($storedStacks);


        $this->storedStacksScheduledForDeletion = $storedStacksToDelete;

        foreach ($storedStacksToDelete as $storedStackRemoved) {
            $storedStackRemoved->setStoredStackStoredMonth(null);
        }

        $this->collStoredStacks = null;
        foreach ($storedStacks as $storedStack) {
            $this->addStoredStack($storedStack);
        }

        $this->collStoredStacks = $storedStacks;
        $this->collStoredStacksPartial = false;

        return $this;
    }

    /**
     * Returns the number of related StoredStack objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related StoredStack objects.
     * @throws PropelException
     */
    public function countStoredStacks(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collStoredStacksPartial && !$this->isNew();
        if (null === $this->collStoredStacks || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collStoredStacks) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getStoredStacks());
            }
            $query = StoredStackQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByStoredStackStoredMonth($this)
                ->count($con);
        }

        return count($this->collStoredStacks);
    }

    /**
     * Method called to associate a StoredStack object to this object
     * through the StoredStack foreign key attribute.
     *
     * @param    StoredStack $l StoredStack
     * @return StoredMonth The current object (for fluent API support)
     */
    public function addStoredStack(StoredStack $l)
    {
        if ($this->collStoredStacks === null) {
            $this->initStoredStacks();
            $this->collStoredStacksPartial = true;
        }

        if (!in_array($l, $this->collStoredStacks->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddStoredStack($l);

            if ($this->storedStacksScheduledForDeletion and $this->storedStacksScheduledForDeletion->contains($l)) {
                $this->storedStacksScheduledForDeletion->remove($this->storedStacksScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	StoredStack $storedStack The storedStack object to add.
     */
    protected function doAddStoredStack($storedStack)
    {
        $this->collStoredStacks[]= $storedStack;
        $storedStack->setStoredStackStoredMonth($this);
    }

    /**
     * @param	StoredStack $storedStack The storedStack object to remove.
     * @return StoredMonth The current object (for fluent API support)
     */
    public function removeStoredStack($storedStack)
    {
        if ($this->getStoredStacks()->contains($storedStack)) {
            $this->collStoredStacks->remove($this->collStoredStacks->search($storedStack));
            if (null === $this->storedStacksScheduledForDeletion) {
                $this->storedStacksScheduledForDeletion = clone $this->collStoredStacks;
                $this->storedStacksScheduledForDeletion->clear();
            }
            $this->storedStacksScheduledForDeletion[]= $storedStack;
            $storedStack->setStoredStackStoredMonth(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this StoredMonth is new, it will return
     * an empty collection; or if this StoredMonth has previously
     * been saved, it will retrieve related StoredStacks from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in StoredMonth.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|StoredStack[] List of StoredStack objects
     */
    public function getStoredStacksJoinStoredStackAbsenceStack($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = StoredStackQuery::create(null, $criteria);
        $query->joinWith('StoredStackAbsenceStack', $join_behavior);

        return $this->getStoredStacks($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->storage_personnel_presences_id = null;
        $this->employee_id = null;
        $this->date_start = null;
        $this->date_end = null;
        $this->ext_start_o = null;
        $this->ext_end_o = null;
        $this->ext_start = null;
        $this->ext_end = null;
        $this->note = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collStoredStacks) {
                foreach ($this->collStoredStacks as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aStoredMonthEmployee instanceof Persistent) {
              $this->aStoredMonthEmployee->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collStoredStacks instanceof PropelCollection) {
            $this->collStoredStacks->clearIterator();
        }
        $this->collStoredStacks = null;
        $this->aStoredMonthEmployee = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(StoredMonthPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
