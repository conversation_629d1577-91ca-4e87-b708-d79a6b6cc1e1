<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Contact;
use Core\Institute;
use Employee\Absences;
use Employee\Employee;
use Employee\EmployeePeer;
use Employee\EmployeeQuery;
use Employee\PersonnelStacks;
use Employee\Presence;
use Employee\StoredDay;
use Employee\StoredMonth;
use Employee\Timetable;

/**
 * Base class that represents a query for the 'employee' table.
 *
 *
 *
 * @method EmployeeQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method EmployeeQuery orderByName($order = Criteria::ASC) Order by the name column
 * @method EmployeeQuery orderBySurname($order = Criteria::ASC) Order by the surname column
 * @method EmployeeQuery orderByGender($order = Criteria::ASC) Order by the gender column
 * @method EmployeeQuery orderByBirthdate($order = Criteria::ASC) Order by the birthdate column
 * @method EmployeeQuery orderByFiscalCode($order = Criteria::ASC) Order by the fiscal_code column
 * @method EmployeeQuery orderByResidenceId($order = Criteria::ASC) Order by the residence_id column
 * @method EmployeeQuery orderByAddressId($order = Criteria::ASC) Order by the address_id column
 * @method EmployeeQuery orderByPartSpesa($order = Criteria::ASC) Order by the part_spesa column
 * @method EmployeeQuery orderByBank($order = Criteria::ASC) Order by the bank column
 * @method EmployeeQuery orderByLiqOffice($order = Criteria::ASC) Order by the liq_office column
 * @method EmployeeQuery orderByInps($order = Criteria::ASC) Order by the inps column
 * @method EmployeeQuery orderByInsurQual($order = Criteria::ASC) Order by the insur_qual column
 * @method EmployeeQuery orderByFore($order = Criteria::ASC) Order by the fore column
 * @method EmployeeQuery orderByAsl($order = Criteria::ASC) Order by the asl column
 * @method EmployeeQuery orderByAdmCode($order = Criteria::ASC) Order by the adm_code column
 * @method EmployeeQuery orderByWayPay($order = Criteria::ASC) Order by the way_pay column
 * @method EmployeeQuery orderByLiquidGroup($order = Criteria::ASC) Order by the liquid_group column
 * @method EmployeeQuery orderByContrCode($order = Criteria::ASC) Order by the contr_code column
 * @method EmployeeQuery orderByContrType($order = Criteria::ASC) Order by the contr_type column
 * @method EmployeeQuery orderByContrCat($order = Criteria::ASC) Order by the contr_cat column
 * @method EmployeeQuery orderBySspFrmPmnt($order = Criteria::ASC) Order by the ssp_frm_pmnt column
 * @method EmployeeQuery orderByPersonalData($order = Criteria::ASC) Order by the personal_data column
 * @method EmployeeQuery orderBySusp($order = Criteria::ASC) Order by the susp column
 * @method EmployeeQuery orderByPaymentGroup($order = Criteria::ASC) Order by the payment_group column
 * @method EmployeeQuery orderByPrivRetType($order = Criteria::ASC) Order by the priv_ret_type column
 * @method EmployeeQuery orderBySocialPosition($order = Criteria::ASC) Order by the social_position column
 * @method EmployeeQuery orderByActive($order = Criteria::ASC) Order by the active column
 * @method EmployeeQuery orderByStatalCode($order = Criteria::ASC) Order by the statal_code column
 * @method EmployeeQuery orderByFiscalCityCode($order = Criteria::ASC) Order by the fiscal_city_code column
 * @method EmployeeQuery orderByBirthplace($order = Criteria::ASC) Order by the birthplace column
 * @method EmployeeQuery orderByIncome($order = Criteria::ASC) Order by the income column
 * @method EmployeeQuery orderByStateBirth($order = Criteria::ASC) Order by the state_birth column
 * @method EmployeeQuery orderByCitizenship($order = Criteria::ASC) Order by the citizenship column
 * @method EmployeeQuery orderByIdSissi($order = Criteria::ASC) Order by the id_sissi column
 * @method EmployeeQuery orderByDomFirstPrevYear($order = Criteria::ASC) Order by the dom_first_prev_year column
 * @method EmployeeQuery orderByDomLastPrevYear($order = Criteria::ASC) Order by the dom_last_prev_year column
 * @method EmployeeQuery orderByDomFirstCurrYear($order = Criteria::ASC) Order by the dom_first_curr_year column
 * @method EmployeeQuery orderByQualification($order = Criteria::ASC) Order by the qualification column
 * @method EmployeeQuery orderByLiquidOfficeId($order = Criteria::ASC) Order by the liquid_office_id column
 * @method EmployeeQuery orderByBadgeNumber($order = Criteria::ASC) Order by the badge_number column
 * @method EmployeeQuery orderByToleranceIn($order = Criteria::ASC) Order by the tolerance_in column
 * @method EmployeeQuery orderByToleranceOut($order = Criteria::ASC) Order by the tolerance_out column
 * @method EmployeeQuery orderByFlexibility($order = Criteria::ASC) Order by the flexibility column
 * @method EmployeeQuery orderByGenericTolerance($order = Criteria::ASC) Order by the generic_tolerance column
 * @method EmployeeQuery orderByNegativeRound($order = Criteria::ASC) Order by the negative_round column
 * @method EmployeeQuery orderByRecoverHours($order = Criteria::ASC) Order by the recover_hours column
 * @method EmployeeQuery orderByMaxExtraordinaryIn($order = Criteria::ASC) Order by the max_extraordinary_in column
 * @method EmployeeQuery orderByMaxExtraordinaryOut($order = Criteria::ASC) Order by the max_extraordinary_out column
 * @method EmployeeQuery orderByMinExtraordinaryIn($order = Criteria::ASC) Order by the min_extraordinary_in column
 * @method EmployeeQuery orderByMinExtraordinaryOut($order = Criteria::ASC) Order by the min_extraordinary_out column
 * @method EmployeeQuery orderByStepOut($order = Criteria::ASC) Order by the step_out column
 * @method EmployeeQuery orderByStepIn($order = Criteria::ASC) Order by the step_in column
 * @method EmployeeQuery orderByMaxBreak($order = Criteria::ASC) Order by the max_break column
 * @method EmployeeQuery orderByMaxContWork($order = Criteria::ASC) Order by the max_cont_work column
 * @method EmployeeQuery orderBySimplifiedAtaSettings($order = Criteria::ASC) Order by the simplified_ata_settings column
 * @method EmployeeQuery orderByToleranceInUnd($order = Criteria::ASC) Order by the tolerance_in_und column
 * @method EmployeeQuery orderByToleranceOutUnd($order = Criteria::ASC) Order by the tolerance_out_und column
 * @method EmployeeQuery orderByMaxUndefinedIn($order = Criteria::ASC) Order by the max_undefined_in column
 * @method EmployeeQuery orderByMaxUndefinedOut($order = Criteria::ASC) Order by the max_undefined_out column
 * @method EmployeeQuery orderByMinUndefinedIn($order = Criteria::ASC) Order by the min_undefined_in column
 * @method EmployeeQuery orderByMinUndefinedOut($order = Criteria::ASC) Order by the min_undefined_out column
 * @method EmployeeQuery orderByStepOutUnd($order = Criteria::ASC) Order by the step_out_und column
 * @method EmployeeQuery orderByStepInUnd($order = Criteria::ASC) Order by the step_in_und column
 * @method EmployeeQuery orderByUndefinedParameterActive($order = Criteria::ASC) Order by the undefined_parameter_active column
 * @method EmployeeQuery orderByMinExtraordinaryTotal($order = Criteria::ASC) Order by the min_extraordinary_total column
 * @method EmployeeQuery orderByMaxExtraordinaryTotal($order = Criteria::ASC) Order by the max_extraordinary_total column
 * @method EmployeeQuery orderByMinUndefinedTotal($order = Criteria::ASC) Order by the min_undefined_total column
 * @method EmployeeQuery orderByMaxUndefinedTotal($order = Criteria::ASC) Order by the max_undefined_total column
 * @method EmployeeQuery orderByStepTotalUndefined($order = Criteria::ASC) Order by the step_total_undefined column
 * @method EmployeeQuery orderByStepTotalExtraordinary($order = Criteria::ASC) Order by the step_total_extraordinary column
 * @method EmployeeQuery orderByLunchDuration($order = Criteria::ASC) Order by the lunch_duration column
 * @method EmployeeQuery orderByLunchDeductible($order = Criteria::ASC) Order by the lunch_deductible column
 * @method EmployeeQuery orderByServiceDeductible($order = Criteria::ASC) Order by the service_deductible column
 * @method EmployeeQuery orderByMinUndefinedLunch($order = Criteria::ASC) Order by the min_undefined_lunch column
 * @method EmployeeQuery orderByMinExtraordinaryLunch($order = Criteria::ASC) Order by the min_extraordinary_lunch column
 * @method EmployeeQuery orderByMaxUndefinedLunch($order = Criteria::ASC) Order by the max_undefined_lunch column
 * @method EmployeeQuery orderByMaxExtraordinaryLunch($order = Criteria::ASC) Order by the max_extraordinary_lunch column
 * @method EmployeeQuery orderByStepLunchUndefined($order = Criteria::ASC) Order by the step_lunch_undefined column
 * @method EmployeeQuery orderByStepLunchExtraordinary($order = Criteria::ASC) Order by the step_lunch_extraordinary column
 * @method EmployeeQuery orderByBreakAfterMaxWork($order = Criteria::ASC) Order by the break_after_max_work column
 * @method EmployeeQuery orderByUnitRecoverHours($order = Criteria::ASC) Order by the unit_recover_hours column
 * @method EmployeeQuery orderByMaxWork($order = Criteria::ASC) Order by the max_work column
 *
 * @method EmployeeQuery groupByEmployeeId() Group by the employee_id column
 * @method EmployeeQuery groupByName() Group by the name column
 * @method EmployeeQuery groupBySurname() Group by the surname column
 * @method EmployeeQuery groupByGender() Group by the gender column
 * @method EmployeeQuery groupByBirthdate() Group by the birthdate column
 * @method EmployeeQuery groupByFiscalCode() Group by the fiscal_code column
 * @method EmployeeQuery groupByResidenceId() Group by the residence_id column
 * @method EmployeeQuery groupByAddressId() Group by the address_id column
 * @method EmployeeQuery groupByPartSpesa() Group by the part_spesa column
 * @method EmployeeQuery groupByBank() Group by the bank column
 * @method EmployeeQuery groupByLiqOffice() Group by the liq_office column
 * @method EmployeeQuery groupByInps() Group by the inps column
 * @method EmployeeQuery groupByInsurQual() Group by the insur_qual column
 * @method EmployeeQuery groupByFore() Group by the fore column
 * @method EmployeeQuery groupByAsl() Group by the asl column
 * @method EmployeeQuery groupByAdmCode() Group by the adm_code column
 * @method EmployeeQuery groupByWayPay() Group by the way_pay column
 * @method EmployeeQuery groupByLiquidGroup() Group by the liquid_group column
 * @method EmployeeQuery groupByContrCode() Group by the contr_code column
 * @method EmployeeQuery groupByContrType() Group by the contr_type column
 * @method EmployeeQuery groupByContrCat() Group by the contr_cat column
 * @method EmployeeQuery groupBySspFrmPmnt() Group by the ssp_frm_pmnt column
 * @method EmployeeQuery groupByPersonalData() Group by the personal_data column
 * @method EmployeeQuery groupBySusp() Group by the susp column
 * @method EmployeeQuery groupByPaymentGroup() Group by the payment_group column
 * @method EmployeeQuery groupByPrivRetType() Group by the priv_ret_type column
 * @method EmployeeQuery groupBySocialPosition() Group by the social_position column
 * @method EmployeeQuery groupByActive() Group by the active column
 * @method EmployeeQuery groupByStatalCode() Group by the statal_code column
 * @method EmployeeQuery groupByFiscalCityCode() Group by the fiscal_city_code column
 * @method EmployeeQuery groupByBirthplace() Group by the birthplace column
 * @method EmployeeQuery groupByIncome() Group by the income column
 * @method EmployeeQuery groupByStateBirth() Group by the state_birth column
 * @method EmployeeQuery groupByCitizenship() Group by the citizenship column
 * @method EmployeeQuery groupByIdSissi() Group by the id_sissi column
 * @method EmployeeQuery groupByDomFirstPrevYear() Group by the dom_first_prev_year column
 * @method EmployeeQuery groupByDomLastPrevYear() Group by the dom_last_prev_year column
 * @method EmployeeQuery groupByDomFirstCurrYear() Group by the dom_first_curr_year column
 * @method EmployeeQuery groupByQualification() Group by the qualification column
 * @method EmployeeQuery groupByLiquidOfficeId() Group by the liquid_office_id column
 * @method EmployeeQuery groupByBadgeNumber() Group by the badge_number column
 * @method EmployeeQuery groupByToleranceIn() Group by the tolerance_in column
 * @method EmployeeQuery groupByToleranceOut() Group by the tolerance_out column
 * @method EmployeeQuery groupByFlexibility() Group by the flexibility column
 * @method EmployeeQuery groupByGenericTolerance() Group by the generic_tolerance column
 * @method EmployeeQuery groupByNegativeRound() Group by the negative_round column
 * @method EmployeeQuery groupByRecoverHours() Group by the recover_hours column
 * @method EmployeeQuery groupByMaxExtraordinaryIn() Group by the max_extraordinary_in column
 * @method EmployeeQuery groupByMaxExtraordinaryOut() Group by the max_extraordinary_out column
 * @method EmployeeQuery groupByMinExtraordinaryIn() Group by the min_extraordinary_in column
 * @method EmployeeQuery groupByMinExtraordinaryOut() Group by the min_extraordinary_out column
 * @method EmployeeQuery groupByStepOut() Group by the step_out column
 * @method EmployeeQuery groupByStepIn() Group by the step_in column
 * @method EmployeeQuery groupByMaxBreak() Group by the max_break column
 * @method EmployeeQuery groupByMaxContWork() Group by the max_cont_work column
 * @method EmployeeQuery groupBySimplifiedAtaSettings() Group by the simplified_ata_settings column
 * @method EmployeeQuery groupByToleranceInUnd() Group by the tolerance_in_und column
 * @method EmployeeQuery groupByToleranceOutUnd() Group by the tolerance_out_und column
 * @method EmployeeQuery groupByMaxUndefinedIn() Group by the max_undefined_in column
 * @method EmployeeQuery groupByMaxUndefinedOut() Group by the max_undefined_out column
 * @method EmployeeQuery groupByMinUndefinedIn() Group by the min_undefined_in column
 * @method EmployeeQuery groupByMinUndefinedOut() Group by the min_undefined_out column
 * @method EmployeeQuery groupByStepOutUnd() Group by the step_out_und column
 * @method EmployeeQuery groupByStepInUnd() Group by the step_in_und column
 * @method EmployeeQuery groupByUndefinedParameterActive() Group by the undefined_parameter_active column
 * @method EmployeeQuery groupByMinExtraordinaryTotal() Group by the min_extraordinary_total column
 * @method EmployeeQuery groupByMaxExtraordinaryTotal() Group by the max_extraordinary_total column
 * @method EmployeeQuery groupByMinUndefinedTotal() Group by the min_undefined_total column
 * @method EmployeeQuery groupByMaxUndefinedTotal() Group by the max_undefined_total column
 * @method EmployeeQuery groupByStepTotalUndefined() Group by the step_total_undefined column
 * @method EmployeeQuery groupByStepTotalExtraordinary() Group by the step_total_extraordinary column
 * @method EmployeeQuery groupByLunchDuration() Group by the lunch_duration column
 * @method EmployeeQuery groupByLunchDeductible() Group by the lunch_deductible column
 * @method EmployeeQuery groupByServiceDeductible() Group by the service_deductible column
 * @method EmployeeQuery groupByMinUndefinedLunch() Group by the min_undefined_lunch column
 * @method EmployeeQuery groupByMinExtraordinaryLunch() Group by the min_extraordinary_lunch column
 * @method EmployeeQuery groupByMaxUndefinedLunch() Group by the max_undefined_lunch column
 * @method EmployeeQuery groupByMaxExtraordinaryLunch() Group by the max_extraordinary_lunch column
 * @method EmployeeQuery groupByStepLunchUndefined() Group by the step_lunch_undefined column
 * @method EmployeeQuery groupByStepLunchExtraordinary() Group by the step_lunch_extraordinary column
 * @method EmployeeQuery groupByBreakAfterMaxWork() Group by the break_after_max_work column
 * @method EmployeeQuery groupByUnitRecoverHours() Group by the unit_recover_hours column
 * @method EmployeeQuery groupByMaxWork() Group by the max_work column
 *
 * @method EmployeeQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method EmployeeQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method EmployeeQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method EmployeeQuery leftJoinResidenceKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the ResidenceKey relation
 * @method EmployeeQuery rightJoinResidenceKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the ResidenceKey relation
 * @method EmployeeQuery innerJoinResidenceKey($relationAlias = null) Adds a INNER JOIN clause to the query using the ResidenceKey relation
 *
 * @method EmployeeQuery leftJoinAddressKey($relationAlias = null) Adds a LEFT JOIN clause to the query using the AddressKey relation
 * @method EmployeeQuery rightJoinAddressKey($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AddressKey relation
 * @method EmployeeQuery innerJoinAddressKey($relationAlias = null) Adds a INNER JOIN clause to the query using the AddressKey relation
 *
 * @method EmployeeQuery leftJoinInstitute($relationAlias = null) Adds a LEFT JOIN clause to the query using the Institute relation
 * @method EmployeeQuery rightJoinInstitute($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Institute relation
 * @method EmployeeQuery innerJoinInstitute($relationAlias = null) Adds a INNER JOIN clause to the query using the Institute relation
 *
 * @method EmployeeQuery leftJoinAbsences($relationAlias = null) Adds a LEFT JOIN clause to the query using the Absences relation
 * @method EmployeeQuery rightJoinAbsences($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Absences relation
 * @method EmployeeQuery innerJoinAbsences($relationAlias = null) Adds a INNER JOIN clause to the query using the Absences relation
 *
 * @method EmployeeQuery leftJoinTimetable($relationAlias = null) Adds a LEFT JOIN clause to the query using the Timetable relation
 * @method EmployeeQuery rightJoinTimetable($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Timetable relation
 * @method EmployeeQuery innerJoinTimetable($relationAlias = null) Adds a INNER JOIN clause to the query using the Timetable relation
 *
 * @method EmployeeQuery leftJoinPresence($relationAlias = null) Adds a LEFT JOIN clause to the query using the Presence relation
 * @method EmployeeQuery rightJoinPresence($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Presence relation
 * @method EmployeeQuery innerJoinPresence($relationAlias = null) Adds a INNER JOIN clause to the query using the Presence relation
 *
 * @method EmployeeQuery leftJoinPersonnelStacks($relationAlias = null) Adds a LEFT JOIN clause to the query using the PersonnelStacks relation
 * @method EmployeeQuery rightJoinPersonnelStacks($relationAlias = null) Adds a RIGHT JOIN clause to the query using the PersonnelStacks relation
 * @method EmployeeQuery innerJoinPersonnelStacks($relationAlias = null) Adds a INNER JOIN clause to the query using the PersonnelStacks relation
 *
 * @method EmployeeQuery leftJoinStoredMonth($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredMonth relation
 * @method EmployeeQuery rightJoinStoredMonth($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredMonth relation
 * @method EmployeeQuery innerJoinStoredMonth($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredMonth relation
 *
 * @method EmployeeQuery leftJoinStoredDay($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredDay relation
 * @method EmployeeQuery rightJoinStoredDay($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredDay relation
 * @method EmployeeQuery innerJoinStoredDay($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredDay relation
 *
 * @method Employee findOne(PropelPDO $con = null) Return the first Employee matching the query
 * @method Employee findOneOrCreate(PropelPDO $con = null) Return the first Employee matching the query, or a new Employee object populated from the query conditions when no match is found
 *
 * @method Employee findOneByName(string $name) Return the first Employee filtered by the name column
 * @method Employee findOneBySurname(string $surname) Return the first Employee filtered by the surname column
 * @method Employee findOneByGender(string $gender) Return the first Employee filtered by the gender column
 * @method Employee findOneByBirthdate(string $birthdate) Return the first Employee filtered by the birthdate column
 * @method Employee findOneByFiscalCode(string $fiscal_code) Return the first Employee filtered by the fiscal_code column
 * @method Employee findOneByResidenceId(int $residence_id) Return the first Employee filtered by the residence_id column
 * @method Employee findOneByAddressId(int $address_id) Return the first Employee filtered by the address_id column
 * @method Employee findOneByPartSpesa(string $part_spesa) Return the first Employee filtered by the part_spesa column
 * @method Employee findOneByBank(int $bank) Return the first Employee filtered by the bank column
 * @method Employee findOneByLiqOffice(string $liq_office) Return the first Employee filtered by the liq_office column
 * @method Employee findOneByInps(string $inps) Return the first Employee filtered by the inps column
 * @method Employee findOneByInsurQual(string $insur_qual) Return the first Employee filtered by the insur_qual column
 * @method Employee findOneByFore(boolean $fore) Return the first Employee filtered by the fore column
 * @method Employee findOneByAsl(string $asl) Return the first Employee filtered by the asl column
 * @method Employee findOneByAdmCode(string $adm_code) Return the first Employee filtered by the adm_code column
 * @method Employee findOneByWayPay(string $way_pay) Return the first Employee filtered by the way_pay column
 * @method Employee findOneByLiquidGroup(string $liquid_group) Return the first Employee filtered by the liquid_group column
 * @method Employee findOneByContrCode(string $contr_code) Return the first Employee filtered by the contr_code column
 * @method Employee findOneByContrType(string $contr_type) Return the first Employee filtered by the contr_type column
 * @method Employee findOneByContrCat(int $contr_cat) Return the first Employee filtered by the contr_cat column
 * @method Employee findOneBySspFrmPmnt(int $ssp_frm_pmnt) Return the first Employee filtered by the ssp_frm_pmnt column
 * @method Employee findOneByPersonalData(int $personal_data) Return the first Employee filtered by the personal_data column
 * @method Employee findOneBySusp(boolean $susp) Return the first Employee filtered by the susp column
 * @method Employee findOneByPaymentGroup(int $payment_group) Return the first Employee filtered by the payment_group column
 * @method Employee findOneByPrivRetType(string $priv_ret_type) Return the first Employee filtered by the priv_ret_type column
 * @method Employee findOneBySocialPosition(int $social_position) Return the first Employee filtered by the social_position column
 * @method Employee findOneByActive(boolean $active) Return the first Employee filtered by the active column
 * @method Employee findOneByStatalCode(string $statal_code) Return the first Employee filtered by the statal_code column
 * @method Employee findOneByFiscalCityCode(string $fiscal_city_code) Return the first Employee filtered by the fiscal_city_code column
 * @method Employee findOneByBirthplace(string $birthplace) Return the first Employee filtered by the birthplace column
 * @method Employee findOneByIncome(string $income) Return the first Employee filtered by the income column
 * @method Employee findOneByStateBirth(string $state_birth) Return the first Employee filtered by the state_birth column
 * @method Employee findOneByCitizenship(string $citizenship) Return the first Employee filtered by the citizenship column
 * @method Employee findOneByIdSissi(string $id_sissi) Return the first Employee filtered by the id_sissi column
 * @method Employee findOneByDomFirstPrevYear(int $dom_first_prev_year) Return the first Employee filtered by the dom_first_prev_year column
 * @method Employee findOneByDomLastPrevYear(int $dom_last_prev_year) Return the first Employee filtered by the dom_last_prev_year column
 * @method Employee findOneByDomFirstCurrYear(int $dom_first_curr_year) Return the first Employee filtered by the dom_first_curr_year column
 * @method Employee findOneByQualification(string $qualification) Return the first Employee filtered by the qualification column
 * @method Employee findOneByLiquidOfficeId(int $liquid_office_id) Return the first Employee filtered by the liquid_office_id column
 * @method Employee findOneByBadgeNumber(string $badge_number) Return the first Employee filtered by the badge_number column
 * @method Employee findOneByToleranceIn(int $tolerance_in) Return the first Employee filtered by the tolerance_in column
 * @method Employee findOneByToleranceOut(int $tolerance_out) Return the first Employee filtered by the tolerance_out column
 * @method Employee findOneByFlexibility(int $flexibility) Return the first Employee filtered by the flexibility column
 * @method Employee findOneByGenericTolerance(int $generic_tolerance) Return the first Employee filtered by the generic_tolerance column
 * @method Employee findOneByNegativeRound(int $negative_round) Return the first Employee filtered by the negative_round column
 * @method Employee findOneByRecoverHours(int $recover_hours) Return the first Employee filtered by the recover_hours column
 * @method Employee findOneByMaxExtraordinaryIn(int $max_extraordinary_in) Return the first Employee filtered by the max_extraordinary_in column
 * @method Employee findOneByMaxExtraordinaryOut(int $max_extraordinary_out) Return the first Employee filtered by the max_extraordinary_out column
 * @method Employee findOneByMinExtraordinaryIn(int $min_extraordinary_in) Return the first Employee filtered by the min_extraordinary_in column
 * @method Employee findOneByMinExtraordinaryOut(int $min_extraordinary_out) Return the first Employee filtered by the min_extraordinary_out column
 * @method Employee findOneByStepOut(int $step_out) Return the first Employee filtered by the step_out column
 * @method Employee findOneByStepIn(int $step_in) Return the first Employee filtered by the step_in column
 * @method Employee findOneByMaxBreak(int $max_break) Return the first Employee filtered by the max_break column
 * @method Employee findOneByMaxContWork(int $max_cont_work) Return the first Employee filtered by the max_cont_work column
 * @method Employee findOneBySimplifiedAtaSettings(boolean $simplified_ata_settings) Return the first Employee filtered by the simplified_ata_settings column
 * @method Employee findOneByToleranceInUnd(int $tolerance_in_und) Return the first Employee filtered by the tolerance_in_und column
 * @method Employee findOneByToleranceOutUnd(int $tolerance_out_und) Return the first Employee filtered by the tolerance_out_und column
 * @method Employee findOneByMaxUndefinedIn(int $max_undefined_in) Return the first Employee filtered by the max_undefined_in column
 * @method Employee findOneByMaxUndefinedOut(int $max_undefined_out) Return the first Employee filtered by the max_undefined_out column
 * @method Employee findOneByMinUndefinedIn(int $min_undefined_in) Return the first Employee filtered by the min_undefined_in column
 * @method Employee findOneByMinUndefinedOut(int $min_undefined_out) Return the first Employee filtered by the min_undefined_out column
 * @method Employee findOneByStepOutUnd(int $step_out_und) Return the first Employee filtered by the step_out_und column
 * @method Employee findOneByStepInUnd(int $step_in_und) Return the first Employee filtered by the step_in_und column
 * @method Employee findOneByUndefinedParameterActive(boolean $undefined_parameter_active) Return the first Employee filtered by the undefined_parameter_active column
 * @method Employee findOneByMinExtraordinaryTotal(int $min_extraordinary_total) Return the first Employee filtered by the min_extraordinary_total column
 * @method Employee findOneByMaxExtraordinaryTotal(int $max_extraordinary_total) Return the first Employee filtered by the max_extraordinary_total column
 * @method Employee findOneByMinUndefinedTotal(int $min_undefined_total) Return the first Employee filtered by the min_undefined_total column
 * @method Employee findOneByMaxUndefinedTotal(int $max_undefined_total) Return the first Employee filtered by the max_undefined_total column
 * @method Employee findOneByStepTotalUndefined(int $step_total_undefined) Return the first Employee filtered by the step_total_undefined column
 * @method Employee findOneByStepTotalExtraordinary(int $step_total_extraordinary) Return the first Employee filtered by the step_total_extraordinary column
 * @method Employee findOneByLunchDuration(int $lunch_duration) Return the first Employee filtered by the lunch_duration column
 * @method Employee findOneByLunchDeductible(boolean $lunch_deductible) Return the first Employee filtered by the lunch_deductible column
 * @method Employee findOneByServiceDeductible(boolean $service_deductible) Return the first Employee filtered by the service_deductible column
 * @method Employee findOneByMinUndefinedLunch(int $min_undefined_lunch) Return the first Employee filtered by the min_undefined_lunch column
 * @method Employee findOneByMinExtraordinaryLunch(int $min_extraordinary_lunch) Return the first Employee filtered by the min_extraordinary_lunch column
 * @method Employee findOneByMaxUndefinedLunch(int $max_undefined_lunch) Return the first Employee filtered by the max_undefined_lunch column
 * @method Employee findOneByMaxExtraordinaryLunch(int $max_extraordinary_lunch) Return the first Employee filtered by the max_extraordinary_lunch column
 * @method Employee findOneByStepLunchUndefined(int $step_lunch_undefined) Return the first Employee filtered by the step_lunch_undefined column
 * @method Employee findOneByStepLunchExtraordinary(int $step_lunch_extraordinary) Return the first Employee filtered by the step_lunch_extraordinary column
 * @method Employee findOneByBreakAfterMaxWork(int $break_after_max_work) Return the first Employee filtered by the break_after_max_work column
 * @method Employee findOneByUnitRecoverHours(string $unit_recover_hours) Return the first Employee filtered by the unit_recover_hours column
 * @method Employee findOneByMaxWork(int $max_work) Return the first Employee filtered by the max_work column
 *
 * @method array findByEmployeeId(int $employee_id) Return Employee objects filtered by the employee_id column
 * @method array findByName(string $name) Return Employee objects filtered by the name column
 * @method array findBySurname(string $surname) Return Employee objects filtered by the surname column
 * @method array findByGender(string $gender) Return Employee objects filtered by the gender column
 * @method array findByBirthdate(string $birthdate) Return Employee objects filtered by the birthdate column
 * @method array findByFiscalCode(string $fiscal_code) Return Employee objects filtered by the fiscal_code column
 * @method array findByResidenceId(int $residence_id) Return Employee objects filtered by the residence_id column
 * @method array findByAddressId(int $address_id) Return Employee objects filtered by the address_id column
 * @method array findByPartSpesa(string $part_spesa) Return Employee objects filtered by the part_spesa column
 * @method array findByBank(int $bank) Return Employee objects filtered by the bank column
 * @method array findByLiqOffice(string $liq_office) Return Employee objects filtered by the liq_office column
 * @method array findByInps(string $inps) Return Employee objects filtered by the inps column
 * @method array findByInsurQual(string $insur_qual) Return Employee objects filtered by the insur_qual column
 * @method array findByFore(boolean $fore) Return Employee objects filtered by the fore column
 * @method array findByAsl(string $asl) Return Employee objects filtered by the asl column
 * @method array findByAdmCode(string $adm_code) Return Employee objects filtered by the adm_code column
 * @method array findByWayPay(string $way_pay) Return Employee objects filtered by the way_pay column
 * @method array findByLiquidGroup(string $liquid_group) Return Employee objects filtered by the liquid_group column
 * @method array findByContrCode(string $contr_code) Return Employee objects filtered by the contr_code column
 * @method array findByContrType(string $contr_type) Return Employee objects filtered by the contr_type column
 * @method array findByContrCat(int $contr_cat) Return Employee objects filtered by the contr_cat column
 * @method array findBySspFrmPmnt(int $ssp_frm_pmnt) Return Employee objects filtered by the ssp_frm_pmnt column
 * @method array findByPersonalData(int $personal_data) Return Employee objects filtered by the personal_data column
 * @method array findBySusp(boolean $susp) Return Employee objects filtered by the susp column
 * @method array findByPaymentGroup(int $payment_group) Return Employee objects filtered by the payment_group column
 * @method array findByPrivRetType(string $priv_ret_type) Return Employee objects filtered by the priv_ret_type column
 * @method array findBySocialPosition(int $social_position) Return Employee objects filtered by the social_position column
 * @method array findByActive(boolean $active) Return Employee objects filtered by the active column
 * @method array findByStatalCode(string $statal_code) Return Employee objects filtered by the statal_code column
 * @method array findByFiscalCityCode(string $fiscal_city_code) Return Employee objects filtered by the fiscal_city_code column
 * @method array findByBirthplace(string $birthplace) Return Employee objects filtered by the birthplace column
 * @method array findByIncome(string $income) Return Employee objects filtered by the income column
 * @method array findByStateBirth(string $state_birth) Return Employee objects filtered by the state_birth column
 * @method array findByCitizenship(string $citizenship) Return Employee objects filtered by the citizenship column
 * @method array findByIdSissi(string $id_sissi) Return Employee objects filtered by the id_sissi column
 * @method array findByDomFirstPrevYear(int $dom_first_prev_year) Return Employee objects filtered by the dom_first_prev_year column
 * @method array findByDomLastPrevYear(int $dom_last_prev_year) Return Employee objects filtered by the dom_last_prev_year column
 * @method array findByDomFirstCurrYear(int $dom_first_curr_year) Return Employee objects filtered by the dom_first_curr_year column
 * @method array findByQualification(string $qualification) Return Employee objects filtered by the qualification column
 * @method array findByLiquidOfficeId(int $liquid_office_id) Return Employee objects filtered by the liquid_office_id column
 * @method array findByBadgeNumber(string $badge_number) Return Employee objects filtered by the badge_number column
 * @method array findByToleranceIn(int $tolerance_in) Return Employee objects filtered by the tolerance_in column
 * @method array findByToleranceOut(int $tolerance_out) Return Employee objects filtered by the tolerance_out column
 * @method array findByFlexibility(int $flexibility) Return Employee objects filtered by the flexibility column
 * @method array findByGenericTolerance(int $generic_tolerance) Return Employee objects filtered by the generic_tolerance column
 * @method array findByNegativeRound(int $negative_round) Return Employee objects filtered by the negative_round column
 * @method array findByRecoverHours(int $recover_hours) Return Employee objects filtered by the recover_hours column
 * @method array findByMaxExtraordinaryIn(int $max_extraordinary_in) Return Employee objects filtered by the max_extraordinary_in column
 * @method array findByMaxExtraordinaryOut(int $max_extraordinary_out) Return Employee objects filtered by the max_extraordinary_out column
 * @method array findByMinExtraordinaryIn(int $min_extraordinary_in) Return Employee objects filtered by the min_extraordinary_in column
 * @method array findByMinExtraordinaryOut(int $min_extraordinary_out) Return Employee objects filtered by the min_extraordinary_out column
 * @method array findByStepOut(int $step_out) Return Employee objects filtered by the step_out column
 * @method array findByStepIn(int $step_in) Return Employee objects filtered by the step_in column
 * @method array findByMaxBreak(int $max_break) Return Employee objects filtered by the max_break column
 * @method array findByMaxContWork(int $max_cont_work) Return Employee objects filtered by the max_cont_work column
 * @method array findBySimplifiedAtaSettings(boolean $simplified_ata_settings) Return Employee objects filtered by the simplified_ata_settings column
 * @method array findByToleranceInUnd(int $tolerance_in_und) Return Employee objects filtered by the tolerance_in_und column
 * @method array findByToleranceOutUnd(int $tolerance_out_und) Return Employee objects filtered by the tolerance_out_und column
 * @method array findByMaxUndefinedIn(int $max_undefined_in) Return Employee objects filtered by the max_undefined_in column
 * @method array findByMaxUndefinedOut(int $max_undefined_out) Return Employee objects filtered by the max_undefined_out column
 * @method array findByMinUndefinedIn(int $min_undefined_in) Return Employee objects filtered by the min_undefined_in column
 * @method array findByMinUndefinedOut(int $min_undefined_out) Return Employee objects filtered by the min_undefined_out column
 * @method array findByStepOutUnd(int $step_out_und) Return Employee objects filtered by the step_out_und column
 * @method array findByStepInUnd(int $step_in_und) Return Employee objects filtered by the step_in_und column
 * @method array findByUndefinedParameterActive(boolean $undefined_parameter_active) Return Employee objects filtered by the undefined_parameter_active column
 * @method array findByMinExtraordinaryTotal(int $min_extraordinary_total) Return Employee objects filtered by the min_extraordinary_total column
 * @method array findByMaxExtraordinaryTotal(int $max_extraordinary_total) Return Employee objects filtered by the max_extraordinary_total column
 * @method array findByMinUndefinedTotal(int $min_undefined_total) Return Employee objects filtered by the min_undefined_total column
 * @method array findByMaxUndefinedTotal(int $max_undefined_total) Return Employee objects filtered by the max_undefined_total column
 * @method array findByStepTotalUndefined(int $step_total_undefined) Return Employee objects filtered by the step_total_undefined column
 * @method array findByStepTotalExtraordinary(int $step_total_extraordinary) Return Employee objects filtered by the step_total_extraordinary column
 * @method array findByLunchDuration(int $lunch_duration) Return Employee objects filtered by the lunch_duration column
 * @method array findByLunchDeductible(boolean $lunch_deductible) Return Employee objects filtered by the lunch_deductible column
 * @method array findByServiceDeductible(boolean $service_deductible) Return Employee objects filtered by the service_deductible column
 * @method array findByMinUndefinedLunch(int $min_undefined_lunch) Return Employee objects filtered by the min_undefined_lunch column
 * @method array findByMinExtraordinaryLunch(int $min_extraordinary_lunch) Return Employee objects filtered by the min_extraordinary_lunch column
 * @method array findByMaxUndefinedLunch(int $max_undefined_lunch) Return Employee objects filtered by the max_undefined_lunch column
 * @method array findByMaxExtraordinaryLunch(int $max_extraordinary_lunch) Return Employee objects filtered by the max_extraordinary_lunch column
 * @method array findByStepLunchUndefined(int $step_lunch_undefined) Return Employee objects filtered by the step_lunch_undefined column
 * @method array findByStepLunchExtraordinary(int $step_lunch_extraordinary) Return Employee objects filtered by the step_lunch_extraordinary column
 * @method array findByBreakAfterMaxWork(int $break_after_max_work) Return Employee objects filtered by the break_after_max_work column
 * @method array findByUnitRecoverHours(string $unit_recover_hours) Return Employee objects filtered by the unit_recover_hours column
 * @method array findByMaxWork(int $max_work) Return Employee objects filtered by the max_work column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseEmployeeQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseEmployeeQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\Employee';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new EmployeeQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   EmployeeQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return EmployeeQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof EmployeeQuery) {
            return $criteria;
        }
        $query = new EmployeeQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Employee|Employee[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = EmployeePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Employee A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByEmployeeId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Employee A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "employee_id", "name", "surname", "gender", "birthdate", "fiscal_code", "residence_id", "address_id", "part_spesa", "bank", "liq_office", "inps", "insur_qual", "fore", "asl", "adm_code", "way_pay", "liquid_group", "contr_code", "contr_type", "contr_cat", "ssp_frm_pmnt", "personal_data", "susp", "payment_group", "priv_ret_type", "social_position", "active", "statal_code", "fiscal_city_code", "birthplace", "income", "state_birth", "citizenship", "id_sissi", "dom_first_prev_year", "dom_last_prev_year", "dom_first_curr_year", "qualification", "liquid_office_id", "badge_number", "tolerance_in", "tolerance_out", "flexibility", "generic_tolerance", "negative_round", "recover_hours", "max_extraordinary_in", "max_extraordinary_out", "min_extraordinary_in", "min_extraordinary_out", "step_out", "step_in", "max_break", "max_cont_work", "simplified_ata_settings", "tolerance_in_und", "tolerance_out_und", "max_undefined_in", "max_undefined_out", "min_undefined_in", "min_undefined_out", "step_out_und", "step_in_und", "undefined_parameter_active", "min_extraordinary_total", "max_extraordinary_total", "min_undefined_total", "max_undefined_total", "step_total_undefined", "step_total_extraordinary", "lunch_duration", "lunch_deductible", "service_deductible", "min_undefined_lunch", "min_extraordinary_lunch", "max_undefined_lunch", "max_extraordinary_lunch", "step_lunch_undefined", "step_lunch_extraordinary", "break_after_max_work", "unit_recover_hours", "max_work" FROM "employee" WHERE "employee_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Employee();
            $obj->hydrate($row);
            EmployeePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Employee|Employee[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Employee[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the name column
     *
     * Example usage:
     * <code>
     * $query->filterByName('fooValue');   // WHERE name = 'fooValue'
     * $query->filterByName('%fooValue%'); // WHERE name LIKE '%fooValue%'
     * </code>
     *
     * @param     string $name The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByName($name = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($name)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $name)) {
                $name = str_replace('*', '%', $name);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::NAME, $name, $comparison);
    }

    /**
     * Filter the query on the surname column
     *
     * Example usage:
     * <code>
     * $query->filterBySurname('fooValue');   // WHERE surname = 'fooValue'
     * $query->filterBySurname('%fooValue%'); // WHERE surname LIKE '%fooValue%'
     * </code>
     *
     * @param     string $surname The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterBySurname($surname = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($surname)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $surname)) {
                $surname = str_replace('*', '%', $surname);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::SURNAME, $surname, $comparison);
    }

    /**
     * Filter the query on the gender column
     *
     * Example usage:
     * <code>
     * $query->filterByGender('fooValue');   // WHERE gender = 'fooValue'
     * $query->filterByGender('%fooValue%'); // WHERE gender LIKE '%fooValue%'
     * </code>
     *
     * @param     string $gender The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByGender($gender = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($gender)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $gender)) {
                $gender = str_replace('*', '%', $gender);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::GENDER, $gender, $comparison);
    }

    /**
     * Filter the query on the birthdate column
     *
     * Example usage:
     * <code>
     * $query->filterByBirthdate(1234); // WHERE birthdate = 1234
     * $query->filterByBirthdate(array(12, 34)); // WHERE birthdate IN (12, 34)
     * $query->filterByBirthdate(array('min' => 12)); // WHERE birthdate >= 12
     * $query->filterByBirthdate(array('max' => 12)); // WHERE birthdate <= 12
     * </code>
     *
     * @param     mixed $birthdate The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByBirthdate($birthdate = null, $comparison = null)
    {
        if (is_array($birthdate)) {
            $useMinMax = false;
            if (isset($birthdate['min'])) {
                $this->addUsingAlias(EmployeePeer::BIRTHDATE, $birthdate['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($birthdate['max'])) {
                $this->addUsingAlias(EmployeePeer::BIRTHDATE, $birthdate['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::BIRTHDATE, $birthdate, $comparison);
    }

    /**
     * Filter the query on the fiscal_code column
     *
     * Example usage:
     * <code>
     * $query->filterByFiscalCode('fooValue');   // WHERE fiscal_code = 'fooValue'
     * $query->filterByFiscalCode('%fooValue%'); // WHERE fiscal_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $fiscalCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByFiscalCode($fiscalCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($fiscalCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $fiscalCode)) {
                $fiscalCode = str_replace('*', '%', $fiscalCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::FISCAL_CODE, $fiscalCode, $comparison);
    }

    /**
     * Filter the query on the residence_id column
     *
     * Example usage:
     * <code>
     * $query->filterByResidenceId(1234); // WHERE residence_id = 1234
     * $query->filterByResidenceId(array(12, 34)); // WHERE residence_id IN (12, 34)
     * $query->filterByResidenceId(array('min' => 12)); // WHERE residence_id >= 12
     * $query->filterByResidenceId(array('max' => 12)); // WHERE residence_id <= 12
     * </code>
     *
     * @see       filterByResidenceKey()
     *
     * @param     mixed $residenceId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByResidenceId($residenceId = null, $comparison = null)
    {
        if (is_array($residenceId)) {
            $useMinMax = false;
            if (isset($residenceId['min'])) {
                $this->addUsingAlias(EmployeePeer::RESIDENCE_ID, $residenceId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($residenceId['max'])) {
                $this->addUsingAlias(EmployeePeer::RESIDENCE_ID, $residenceId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::RESIDENCE_ID, $residenceId, $comparison);
    }

    /**
     * Filter the query on the address_id column
     *
     * Example usage:
     * <code>
     * $query->filterByAddressId(1234); // WHERE address_id = 1234
     * $query->filterByAddressId(array(12, 34)); // WHERE address_id IN (12, 34)
     * $query->filterByAddressId(array('min' => 12)); // WHERE address_id >= 12
     * $query->filterByAddressId(array('max' => 12)); // WHERE address_id <= 12
     * </code>
     *
     * @see       filterByAddressKey()
     *
     * @param     mixed $addressId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByAddressId($addressId = null, $comparison = null)
    {
        if (is_array($addressId)) {
            $useMinMax = false;
            if (isset($addressId['min'])) {
                $this->addUsingAlias(EmployeePeer::ADDRESS_ID, $addressId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($addressId['max'])) {
                $this->addUsingAlias(EmployeePeer::ADDRESS_ID, $addressId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::ADDRESS_ID, $addressId, $comparison);
    }

    /**
     * Filter the query on the part_spesa column
     *
     * Example usage:
     * <code>
     * $query->filterByPartSpesa('fooValue');   // WHERE part_spesa = 'fooValue'
     * $query->filterByPartSpesa('%fooValue%'); // WHERE part_spesa LIKE '%fooValue%'
     * </code>
     *
     * @param     string $partSpesa The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByPartSpesa($partSpesa = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($partSpesa)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $partSpesa)) {
                $partSpesa = str_replace('*', '%', $partSpesa);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::PART_SPESA, $partSpesa, $comparison);
    }

    /**
     * Filter the query on the bank column
     *
     * Example usage:
     * <code>
     * $query->filterByBank(1234); // WHERE bank = 1234
     * $query->filterByBank(array(12, 34)); // WHERE bank IN (12, 34)
     * $query->filterByBank(array('min' => 12)); // WHERE bank >= 12
     * $query->filterByBank(array('max' => 12)); // WHERE bank <= 12
     * </code>
     *
     * @param     mixed $bank The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByBank($bank = null, $comparison = null)
    {
        if (is_array($bank)) {
            $useMinMax = false;
            if (isset($bank['min'])) {
                $this->addUsingAlias(EmployeePeer::BANK, $bank['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($bank['max'])) {
                $this->addUsingAlias(EmployeePeer::BANK, $bank['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::BANK, $bank, $comparison);
    }

    /**
     * Filter the query on the liq_office column
     *
     * Example usage:
     * <code>
     * $query->filterByLiqOffice('fooValue');   // WHERE liq_office = 'fooValue'
     * $query->filterByLiqOffice('%fooValue%'); // WHERE liq_office LIKE '%fooValue%'
     * </code>
     *
     * @param     string $liqOffice The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByLiqOffice($liqOffice = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($liqOffice)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $liqOffice)) {
                $liqOffice = str_replace('*', '%', $liqOffice);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::LIQ_OFFICE, $liqOffice, $comparison);
    }

    /**
     * Filter the query on the inps column
     *
     * Example usage:
     * <code>
     * $query->filterByInps('fooValue');   // WHERE inps = 'fooValue'
     * $query->filterByInps('%fooValue%'); // WHERE inps LIKE '%fooValue%'
     * </code>
     *
     * @param     string $inps The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByInps($inps = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($inps)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $inps)) {
                $inps = str_replace('*', '%', $inps);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::INPS, $inps, $comparison);
    }

    /**
     * Filter the query on the insur_qual column
     *
     * Example usage:
     * <code>
     * $query->filterByInsurQual('fooValue');   // WHERE insur_qual = 'fooValue'
     * $query->filterByInsurQual('%fooValue%'); // WHERE insur_qual LIKE '%fooValue%'
     * </code>
     *
     * @param     string $insurQual The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByInsurQual($insurQual = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($insurQual)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $insurQual)) {
                $insurQual = str_replace('*', '%', $insurQual);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::INSUR_QUAL, $insurQual, $comparison);
    }

    /**
     * Filter the query on the fore column
     *
     * Example usage:
     * <code>
     * $query->filterByFore(true); // WHERE fore = true
     * $query->filterByFore('yes'); // WHERE fore = true
     * </code>
     *
     * @param     boolean|string $fore The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByFore($fore = null, $comparison = null)
    {
        if (is_string($fore)) {
            $fore = in_array(strtolower($fore), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::FORE, $fore, $comparison);
    }

    /**
     * Filter the query on the asl column
     *
     * Example usage:
     * <code>
     * $query->filterByAsl('fooValue');   // WHERE asl = 'fooValue'
     * $query->filterByAsl('%fooValue%'); // WHERE asl LIKE '%fooValue%'
     * </code>
     *
     * @param     string $asl The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByAsl($asl = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($asl)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $asl)) {
                $asl = str_replace('*', '%', $asl);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::ASL, $asl, $comparison);
    }

    /**
     * Filter the query on the adm_code column
     *
     * Example usage:
     * <code>
     * $query->filterByAdmCode('fooValue');   // WHERE adm_code = 'fooValue'
     * $query->filterByAdmCode('%fooValue%'); // WHERE adm_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $admCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByAdmCode($admCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($admCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $admCode)) {
                $admCode = str_replace('*', '%', $admCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::ADM_CODE, $admCode, $comparison);
    }

    /**
     * Filter the query on the way_pay column
     *
     * Example usage:
     * <code>
     * $query->filterByWayPay('fooValue');   // WHERE way_pay = 'fooValue'
     * $query->filterByWayPay('%fooValue%'); // WHERE way_pay LIKE '%fooValue%'
     * </code>
     *
     * @param     string $wayPay The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByWayPay($wayPay = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($wayPay)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $wayPay)) {
                $wayPay = str_replace('*', '%', $wayPay);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::WAY_PAY, $wayPay, $comparison);
    }

    /**
     * Filter the query on the liquid_group column
     *
     * Example usage:
     * <code>
     * $query->filterByLiquidGroup('fooValue');   // WHERE liquid_group = 'fooValue'
     * $query->filterByLiquidGroup('%fooValue%'); // WHERE liquid_group LIKE '%fooValue%'
     * </code>
     *
     * @param     string $liquidGroup The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByLiquidGroup($liquidGroup = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($liquidGroup)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $liquidGroup)) {
                $liquidGroup = str_replace('*', '%', $liquidGroup);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::LIQUID_GROUP, $liquidGroup, $comparison);
    }

    /**
     * Filter the query on the contr_code column
     *
     * Example usage:
     * <code>
     * $query->filterByContrCode('fooValue');   // WHERE contr_code = 'fooValue'
     * $query->filterByContrCode('%fooValue%'); // WHERE contr_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $contrCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByContrCode($contrCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($contrCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $contrCode)) {
                $contrCode = str_replace('*', '%', $contrCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::CONTR_CODE, $contrCode, $comparison);
    }

    /**
     * Filter the query on the contr_type column
     *
     * Example usage:
     * <code>
     * $query->filterByContrType('fooValue');   // WHERE contr_type = 'fooValue'
     * $query->filterByContrType('%fooValue%'); // WHERE contr_type LIKE '%fooValue%'
     * </code>
     *
     * @param     string $contrType The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByContrType($contrType = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($contrType)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $contrType)) {
                $contrType = str_replace('*', '%', $contrType);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::CONTR_TYPE, $contrType, $comparison);
    }

    /**
     * Filter the query on the contr_cat column
     *
     * Example usage:
     * <code>
     * $query->filterByContrCat(1234); // WHERE contr_cat = 1234
     * $query->filterByContrCat(array(12, 34)); // WHERE contr_cat IN (12, 34)
     * $query->filterByContrCat(array('min' => 12)); // WHERE contr_cat >= 12
     * $query->filterByContrCat(array('max' => 12)); // WHERE contr_cat <= 12
     * </code>
     *
     * @param     mixed $contrCat The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByContrCat($contrCat = null, $comparison = null)
    {
        if (is_array($contrCat)) {
            $useMinMax = false;
            if (isset($contrCat['min'])) {
                $this->addUsingAlias(EmployeePeer::CONTR_CAT, $contrCat['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($contrCat['max'])) {
                $this->addUsingAlias(EmployeePeer::CONTR_CAT, $contrCat['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::CONTR_CAT, $contrCat, $comparison);
    }

    /**
     * Filter the query on the ssp_frm_pmnt column
     *
     * Example usage:
     * <code>
     * $query->filterBySspFrmPmnt(1234); // WHERE ssp_frm_pmnt = 1234
     * $query->filterBySspFrmPmnt(array(12, 34)); // WHERE ssp_frm_pmnt IN (12, 34)
     * $query->filterBySspFrmPmnt(array('min' => 12)); // WHERE ssp_frm_pmnt >= 12
     * $query->filterBySspFrmPmnt(array('max' => 12)); // WHERE ssp_frm_pmnt <= 12
     * </code>
     *
     * @param     mixed $sspFrmPmnt The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterBySspFrmPmnt($sspFrmPmnt = null, $comparison = null)
    {
        if (is_array($sspFrmPmnt)) {
            $useMinMax = false;
            if (isset($sspFrmPmnt['min'])) {
                $this->addUsingAlias(EmployeePeer::SSP_FRM_PMNT, $sspFrmPmnt['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($sspFrmPmnt['max'])) {
                $this->addUsingAlias(EmployeePeer::SSP_FRM_PMNT, $sspFrmPmnt['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::SSP_FRM_PMNT, $sspFrmPmnt, $comparison);
    }

    /**
     * Filter the query on the personal_data column
     *
     * Example usage:
     * <code>
     * $query->filterByPersonalData(1234); // WHERE personal_data = 1234
     * $query->filterByPersonalData(array(12, 34)); // WHERE personal_data IN (12, 34)
     * $query->filterByPersonalData(array('min' => 12)); // WHERE personal_data >= 12
     * $query->filterByPersonalData(array('max' => 12)); // WHERE personal_data <= 12
     * </code>
     *
     * @param     mixed $personalData The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByPersonalData($personalData = null, $comparison = null)
    {
        if (is_array($personalData)) {
            $useMinMax = false;
            if (isset($personalData['min'])) {
                $this->addUsingAlias(EmployeePeer::PERSONAL_DATA, $personalData['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($personalData['max'])) {
                $this->addUsingAlias(EmployeePeer::PERSONAL_DATA, $personalData['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::PERSONAL_DATA, $personalData, $comparison);
    }

    /**
     * Filter the query on the susp column
     *
     * Example usage:
     * <code>
     * $query->filterBySusp(true); // WHERE susp = true
     * $query->filterBySusp('yes'); // WHERE susp = true
     * </code>
     *
     * @param     boolean|string $susp The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterBySusp($susp = null, $comparison = null)
    {
        if (is_string($susp)) {
            $susp = in_array(strtolower($susp), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::SUSP, $susp, $comparison);
    }

    /**
     * Filter the query on the payment_group column
     *
     * Example usage:
     * <code>
     * $query->filterByPaymentGroup(1234); // WHERE payment_group = 1234
     * $query->filterByPaymentGroup(array(12, 34)); // WHERE payment_group IN (12, 34)
     * $query->filterByPaymentGroup(array('min' => 12)); // WHERE payment_group >= 12
     * $query->filterByPaymentGroup(array('max' => 12)); // WHERE payment_group <= 12
     * </code>
     *
     * @param     mixed $paymentGroup The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByPaymentGroup($paymentGroup = null, $comparison = null)
    {
        if (is_array($paymentGroup)) {
            $useMinMax = false;
            if (isset($paymentGroup['min'])) {
                $this->addUsingAlias(EmployeePeer::PAYMENT_GROUP, $paymentGroup['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($paymentGroup['max'])) {
                $this->addUsingAlias(EmployeePeer::PAYMENT_GROUP, $paymentGroup['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::PAYMENT_GROUP, $paymentGroup, $comparison);
    }

    /**
     * Filter the query on the priv_ret_type column
     *
     * Example usage:
     * <code>
     * $query->filterByPrivRetType('fooValue');   // WHERE priv_ret_type = 'fooValue'
     * $query->filterByPrivRetType('%fooValue%'); // WHERE priv_ret_type LIKE '%fooValue%'
     * </code>
     *
     * @param     string $privRetType The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByPrivRetType($privRetType = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($privRetType)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $privRetType)) {
                $privRetType = str_replace('*', '%', $privRetType);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::PRIV_RET_TYPE, $privRetType, $comparison);
    }

    /**
     * Filter the query on the social_position column
     *
     * Example usage:
     * <code>
     * $query->filterBySocialPosition(1234); // WHERE social_position = 1234
     * $query->filterBySocialPosition(array(12, 34)); // WHERE social_position IN (12, 34)
     * $query->filterBySocialPosition(array('min' => 12)); // WHERE social_position >= 12
     * $query->filterBySocialPosition(array('max' => 12)); // WHERE social_position <= 12
     * </code>
     *
     * @param     mixed $socialPosition The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterBySocialPosition($socialPosition = null, $comparison = null)
    {
        if (is_array($socialPosition)) {
            $useMinMax = false;
            if (isset($socialPosition['min'])) {
                $this->addUsingAlias(EmployeePeer::SOCIAL_POSITION, $socialPosition['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($socialPosition['max'])) {
                $this->addUsingAlias(EmployeePeer::SOCIAL_POSITION, $socialPosition['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::SOCIAL_POSITION, $socialPosition, $comparison);
    }

    /**
     * Filter the query on the active column
     *
     * Example usage:
     * <code>
     * $query->filterByActive(true); // WHERE active = true
     * $query->filterByActive('yes'); // WHERE active = true
     * </code>
     *
     * @param     boolean|string $active The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByActive($active = null, $comparison = null)
    {
        if (is_string($active)) {
            $active = in_array(strtolower($active), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::ACTIVE, $active, $comparison);
    }

    /**
     * Filter the query on the statal_code column
     *
     * Example usage:
     * <code>
     * $query->filterByStatalCode('fooValue');   // WHERE statal_code = 'fooValue'
     * $query->filterByStatalCode('%fooValue%'); // WHERE statal_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $statalCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStatalCode($statalCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($statalCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $statalCode)) {
                $statalCode = str_replace('*', '%', $statalCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STATAL_CODE, $statalCode, $comparison);
    }

    /**
     * Filter the query on the fiscal_city_code column
     *
     * Example usage:
     * <code>
     * $query->filterByFiscalCityCode('fooValue');   // WHERE fiscal_city_code = 'fooValue'
     * $query->filterByFiscalCityCode('%fooValue%'); // WHERE fiscal_city_code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $fiscalCityCode The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByFiscalCityCode($fiscalCityCode = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($fiscalCityCode)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $fiscalCityCode)) {
                $fiscalCityCode = str_replace('*', '%', $fiscalCityCode);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::FISCAL_CITY_CODE, $fiscalCityCode, $comparison);
    }

    /**
     * Filter the query on the birthplace column
     *
     * Example usage:
     * <code>
     * $query->filterByBirthplace('fooValue');   // WHERE birthplace = 'fooValue'
     * $query->filterByBirthplace('%fooValue%'); // WHERE birthplace LIKE '%fooValue%'
     * </code>
     *
     * @param     string $birthplace The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByBirthplace($birthplace = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($birthplace)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $birthplace)) {
                $birthplace = str_replace('*', '%', $birthplace);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::BIRTHPLACE, $birthplace, $comparison);
    }

    /**
     * Filter the query on the income column
     *
     * Example usage:
     * <code>
     * $query->filterByIncome(1234); // WHERE income = 1234
     * $query->filterByIncome(array(12, 34)); // WHERE income IN (12, 34)
     * $query->filterByIncome(array('min' => 12)); // WHERE income >= 12
     * $query->filterByIncome(array('max' => 12)); // WHERE income <= 12
     * </code>
     *
     * @param     mixed $income The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByIncome($income = null, $comparison = null)
    {
        if (is_array($income)) {
            $useMinMax = false;
            if (isset($income['min'])) {
                $this->addUsingAlias(EmployeePeer::INCOME, $income['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($income['max'])) {
                $this->addUsingAlias(EmployeePeer::INCOME, $income['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::INCOME, $income, $comparison);
    }

    /**
     * Filter the query on the state_birth column
     *
     * Example usage:
     * <code>
     * $query->filterByStateBirth('fooValue');   // WHERE state_birth = 'fooValue'
     * $query->filterByStateBirth('%fooValue%'); // WHERE state_birth LIKE '%fooValue%'
     * </code>
     *
     * @param     string $stateBirth The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStateBirth($stateBirth = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($stateBirth)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $stateBirth)) {
                $stateBirth = str_replace('*', '%', $stateBirth);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STATE_BIRTH, $stateBirth, $comparison);
    }

    /**
     * Filter the query on the citizenship column
     *
     * Example usage:
     * <code>
     * $query->filterByCitizenship('fooValue');   // WHERE citizenship = 'fooValue'
     * $query->filterByCitizenship('%fooValue%'); // WHERE citizenship LIKE '%fooValue%'
     * </code>
     *
     * @param     string $citizenship The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByCitizenship($citizenship = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($citizenship)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $citizenship)) {
                $citizenship = str_replace('*', '%', $citizenship);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::CITIZENSHIP, $citizenship, $comparison);
    }

    /**
     * Filter the query on the id_sissi column
     *
     * Example usage:
     * <code>
     * $query->filterByIdSissi('fooValue');   // WHERE id_sissi = 'fooValue'
     * $query->filterByIdSissi('%fooValue%'); // WHERE id_sissi LIKE '%fooValue%'
     * </code>
     *
     * @param     string $idSissi The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByIdSissi($idSissi = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($idSissi)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $idSissi)) {
                $idSissi = str_replace('*', '%', $idSissi);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::ID_SISSI, $idSissi, $comparison);
    }

    /**
     * Filter the query on the dom_first_prev_year column
     *
     * Example usage:
     * <code>
     * $query->filterByDomFirstPrevYear(1234); // WHERE dom_first_prev_year = 1234
     * $query->filterByDomFirstPrevYear(array(12, 34)); // WHERE dom_first_prev_year IN (12, 34)
     * $query->filterByDomFirstPrevYear(array('min' => 12)); // WHERE dom_first_prev_year >= 12
     * $query->filterByDomFirstPrevYear(array('max' => 12)); // WHERE dom_first_prev_year <= 12
     * </code>
     *
     * @param     mixed $domFirstPrevYear The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByDomFirstPrevYear($domFirstPrevYear = null, $comparison = null)
    {
        if (is_array($domFirstPrevYear)) {
            $useMinMax = false;
            if (isset($domFirstPrevYear['min'])) {
                $this->addUsingAlias(EmployeePeer::DOM_FIRST_PREV_YEAR, $domFirstPrevYear['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($domFirstPrevYear['max'])) {
                $this->addUsingAlias(EmployeePeer::DOM_FIRST_PREV_YEAR, $domFirstPrevYear['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::DOM_FIRST_PREV_YEAR, $domFirstPrevYear, $comparison);
    }

    /**
     * Filter the query on the dom_last_prev_year column
     *
     * Example usage:
     * <code>
     * $query->filterByDomLastPrevYear(1234); // WHERE dom_last_prev_year = 1234
     * $query->filterByDomLastPrevYear(array(12, 34)); // WHERE dom_last_prev_year IN (12, 34)
     * $query->filterByDomLastPrevYear(array('min' => 12)); // WHERE dom_last_prev_year >= 12
     * $query->filterByDomLastPrevYear(array('max' => 12)); // WHERE dom_last_prev_year <= 12
     * </code>
     *
     * @param     mixed $domLastPrevYear The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByDomLastPrevYear($domLastPrevYear = null, $comparison = null)
    {
        if (is_array($domLastPrevYear)) {
            $useMinMax = false;
            if (isset($domLastPrevYear['min'])) {
                $this->addUsingAlias(EmployeePeer::DOM_LAST_PREV_YEAR, $domLastPrevYear['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($domLastPrevYear['max'])) {
                $this->addUsingAlias(EmployeePeer::DOM_LAST_PREV_YEAR, $domLastPrevYear['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::DOM_LAST_PREV_YEAR, $domLastPrevYear, $comparison);
    }

    /**
     * Filter the query on the dom_first_curr_year column
     *
     * Example usage:
     * <code>
     * $query->filterByDomFirstCurrYear(1234); // WHERE dom_first_curr_year = 1234
     * $query->filterByDomFirstCurrYear(array(12, 34)); // WHERE dom_first_curr_year IN (12, 34)
     * $query->filterByDomFirstCurrYear(array('min' => 12)); // WHERE dom_first_curr_year >= 12
     * $query->filterByDomFirstCurrYear(array('max' => 12)); // WHERE dom_first_curr_year <= 12
     * </code>
     *
     * @param     mixed $domFirstCurrYear The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByDomFirstCurrYear($domFirstCurrYear = null, $comparison = null)
    {
        if (is_array($domFirstCurrYear)) {
            $useMinMax = false;
            if (isset($domFirstCurrYear['min'])) {
                $this->addUsingAlias(EmployeePeer::DOM_FIRST_CURR_YEAR, $domFirstCurrYear['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($domFirstCurrYear['max'])) {
                $this->addUsingAlias(EmployeePeer::DOM_FIRST_CURR_YEAR, $domFirstCurrYear['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::DOM_FIRST_CURR_YEAR, $domFirstCurrYear, $comparison);
    }

    /**
     * Filter the query on the qualification column
     *
     * Example usage:
     * <code>
     * $query->filterByQualification('fooValue');   // WHERE qualification = 'fooValue'
     * $query->filterByQualification('%fooValue%'); // WHERE qualification LIKE '%fooValue%'
     * </code>
     *
     * @param     string $qualification The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByQualification($qualification = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($qualification)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $qualification)) {
                $qualification = str_replace('*', '%', $qualification);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::QUALIFICATION, $qualification, $comparison);
    }

    /**
     * Filter the query on the liquid_office_id column
     *
     * Example usage:
     * <code>
     * $query->filterByLiquidOfficeId(1234); // WHERE liquid_office_id = 1234
     * $query->filterByLiquidOfficeId(array(12, 34)); // WHERE liquid_office_id IN (12, 34)
     * $query->filterByLiquidOfficeId(array('min' => 12)); // WHERE liquid_office_id >= 12
     * $query->filterByLiquidOfficeId(array('max' => 12)); // WHERE liquid_office_id <= 12
     * </code>
     *
     * @param     mixed $liquidOfficeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByLiquidOfficeId($liquidOfficeId = null, $comparison = null)
    {
        if (is_array($liquidOfficeId)) {
            $useMinMax = false;
            if (isset($liquidOfficeId['min'])) {
                $this->addUsingAlias(EmployeePeer::LIQUID_OFFICE_ID, $liquidOfficeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($liquidOfficeId['max'])) {
                $this->addUsingAlias(EmployeePeer::LIQUID_OFFICE_ID, $liquidOfficeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::LIQUID_OFFICE_ID, $liquidOfficeId, $comparison);
    }

    /**
     * Filter the query on the badge_number column
     *
     * Example usage:
     * <code>
     * $query->filterByBadgeNumber(1234); // WHERE badge_number = 1234
     * $query->filterByBadgeNumber(array(12, 34)); // WHERE badge_number IN (12, 34)
     * $query->filterByBadgeNumber(array('min' => 12)); // WHERE badge_number >= 12
     * $query->filterByBadgeNumber(array('max' => 12)); // WHERE badge_number <= 12
     * </code>
     *
     * @param     mixed $badgeNumber The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByBadgeNumber($badgeNumber = null, $comparison = null)
    {
        if (is_array($badgeNumber)) {
            $useMinMax = false;
            if (isset($badgeNumber['min'])) {
                $this->addUsingAlias(EmployeePeer::BADGE_NUMBER, $badgeNumber['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($badgeNumber['max'])) {
                $this->addUsingAlias(EmployeePeer::BADGE_NUMBER, $badgeNumber['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::BADGE_NUMBER, $badgeNumber, $comparison);
    }

    /**
     * Filter the query on the tolerance_in column
     *
     * Example usage:
     * <code>
     * $query->filterByToleranceIn(1234); // WHERE tolerance_in = 1234
     * $query->filterByToleranceIn(array(12, 34)); // WHERE tolerance_in IN (12, 34)
     * $query->filterByToleranceIn(array('min' => 12)); // WHERE tolerance_in >= 12
     * $query->filterByToleranceIn(array('max' => 12)); // WHERE tolerance_in <= 12
     * </code>
     *
     * @param     mixed $toleranceIn The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByToleranceIn($toleranceIn = null, $comparison = null)
    {
        if (is_array($toleranceIn)) {
            $useMinMax = false;
            if (isset($toleranceIn['min'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_IN, $toleranceIn['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($toleranceIn['max'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_IN, $toleranceIn['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::TOLERANCE_IN, $toleranceIn, $comparison);
    }

    /**
     * Filter the query on the tolerance_out column
     *
     * Example usage:
     * <code>
     * $query->filterByToleranceOut(1234); // WHERE tolerance_out = 1234
     * $query->filterByToleranceOut(array(12, 34)); // WHERE tolerance_out IN (12, 34)
     * $query->filterByToleranceOut(array('min' => 12)); // WHERE tolerance_out >= 12
     * $query->filterByToleranceOut(array('max' => 12)); // WHERE tolerance_out <= 12
     * </code>
     *
     * @param     mixed $toleranceOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByToleranceOut($toleranceOut = null, $comparison = null)
    {
        if (is_array($toleranceOut)) {
            $useMinMax = false;
            if (isset($toleranceOut['min'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_OUT, $toleranceOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($toleranceOut['max'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_OUT, $toleranceOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::TOLERANCE_OUT, $toleranceOut, $comparison);
    }

    /**
     * Filter the query on the flexibility column
     *
     * Example usage:
     * <code>
     * $query->filterByFlexibility(1234); // WHERE flexibility = 1234
     * $query->filterByFlexibility(array(12, 34)); // WHERE flexibility IN (12, 34)
     * $query->filterByFlexibility(array('min' => 12)); // WHERE flexibility >= 12
     * $query->filterByFlexibility(array('max' => 12)); // WHERE flexibility <= 12
     * </code>
     *
     * @param     mixed $flexibility The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByFlexibility($flexibility = null, $comparison = null)
    {
        if (is_array($flexibility)) {
            $useMinMax = false;
            if (isset($flexibility['min'])) {
                $this->addUsingAlias(EmployeePeer::FLEXIBILITY, $flexibility['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($flexibility['max'])) {
                $this->addUsingAlias(EmployeePeer::FLEXIBILITY, $flexibility['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::FLEXIBILITY, $flexibility, $comparison);
    }

    /**
     * Filter the query on the generic_tolerance column
     *
     * Example usage:
     * <code>
     * $query->filterByGenericTolerance(1234); // WHERE generic_tolerance = 1234
     * $query->filterByGenericTolerance(array(12, 34)); // WHERE generic_tolerance IN (12, 34)
     * $query->filterByGenericTolerance(array('min' => 12)); // WHERE generic_tolerance >= 12
     * $query->filterByGenericTolerance(array('max' => 12)); // WHERE generic_tolerance <= 12
     * </code>
     *
     * @param     mixed $genericTolerance The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByGenericTolerance($genericTolerance = null, $comparison = null)
    {
        if (is_array($genericTolerance)) {
            $useMinMax = false;
            if (isset($genericTolerance['min'])) {
                $this->addUsingAlias(EmployeePeer::GENERIC_TOLERANCE, $genericTolerance['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($genericTolerance['max'])) {
                $this->addUsingAlias(EmployeePeer::GENERIC_TOLERANCE, $genericTolerance['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::GENERIC_TOLERANCE, $genericTolerance, $comparison);
    }

    /**
     * Filter the query on the negative_round column
     *
     * Example usage:
     * <code>
     * $query->filterByNegativeRound(1234); // WHERE negative_round = 1234
     * $query->filterByNegativeRound(array(12, 34)); // WHERE negative_round IN (12, 34)
     * $query->filterByNegativeRound(array('min' => 12)); // WHERE negative_round >= 12
     * $query->filterByNegativeRound(array('max' => 12)); // WHERE negative_round <= 12
     * </code>
     *
     * @param     mixed $negativeRound The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByNegativeRound($negativeRound = null, $comparison = null)
    {
        if (is_array($negativeRound)) {
            $useMinMax = false;
            if (isset($negativeRound['min'])) {
                $this->addUsingAlias(EmployeePeer::NEGATIVE_ROUND, $negativeRound['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($negativeRound['max'])) {
                $this->addUsingAlias(EmployeePeer::NEGATIVE_ROUND, $negativeRound['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::NEGATIVE_ROUND, $negativeRound, $comparison);
    }

    /**
     * Filter the query on the recover_hours column
     *
     * Example usage:
     * <code>
     * $query->filterByRecoverHours(1234); // WHERE recover_hours = 1234
     * $query->filterByRecoverHours(array(12, 34)); // WHERE recover_hours IN (12, 34)
     * $query->filterByRecoverHours(array('min' => 12)); // WHERE recover_hours >= 12
     * $query->filterByRecoverHours(array('max' => 12)); // WHERE recover_hours <= 12
     * </code>
     *
     * @param     mixed $recoverHours The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByRecoverHours($recoverHours = null, $comparison = null)
    {
        if (is_array($recoverHours)) {
            $useMinMax = false;
            if (isset($recoverHours['min'])) {
                $this->addUsingAlias(EmployeePeer::RECOVER_HOURS, $recoverHours['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($recoverHours['max'])) {
                $this->addUsingAlias(EmployeePeer::RECOVER_HOURS, $recoverHours['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::RECOVER_HOURS, $recoverHours, $comparison);
    }

    /**
     * Filter the query on the max_extraordinary_in column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxExtraordinaryIn(1234); // WHERE max_extraordinary_in = 1234
     * $query->filterByMaxExtraordinaryIn(array(12, 34)); // WHERE max_extraordinary_in IN (12, 34)
     * $query->filterByMaxExtraordinaryIn(array('min' => 12)); // WHERE max_extraordinary_in >= 12
     * $query->filterByMaxExtraordinaryIn(array('max' => 12)); // WHERE max_extraordinary_in <= 12
     * </code>
     *
     * @param     mixed $maxExtraordinaryIn The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxExtraordinaryIn($maxExtraordinaryIn = null, $comparison = null)
    {
        if (is_array($maxExtraordinaryIn)) {
            $useMinMax = false;
            if (isset($maxExtraordinaryIn['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_IN, $maxExtraordinaryIn['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxExtraordinaryIn['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_IN, $maxExtraordinaryIn['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_IN, $maxExtraordinaryIn, $comparison);
    }

    /**
     * Filter the query on the max_extraordinary_out column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxExtraordinaryOut(1234); // WHERE max_extraordinary_out = 1234
     * $query->filterByMaxExtraordinaryOut(array(12, 34)); // WHERE max_extraordinary_out IN (12, 34)
     * $query->filterByMaxExtraordinaryOut(array('min' => 12)); // WHERE max_extraordinary_out >= 12
     * $query->filterByMaxExtraordinaryOut(array('max' => 12)); // WHERE max_extraordinary_out <= 12
     * </code>
     *
     * @param     mixed $maxExtraordinaryOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxExtraordinaryOut($maxExtraordinaryOut = null, $comparison = null)
    {
        if (is_array($maxExtraordinaryOut)) {
            $useMinMax = false;
            if (isset($maxExtraordinaryOut['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_OUT, $maxExtraordinaryOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxExtraordinaryOut['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_OUT, $maxExtraordinaryOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_OUT, $maxExtraordinaryOut, $comparison);
    }

    /**
     * Filter the query on the min_extraordinary_in column
     *
     * Example usage:
     * <code>
     * $query->filterByMinExtraordinaryIn(1234); // WHERE min_extraordinary_in = 1234
     * $query->filterByMinExtraordinaryIn(array(12, 34)); // WHERE min_extraordinary_in IN (12, 34)
     * $query->filterByMinExtraordinaryIn(array('min' => 12)); // WHERE min_extraordinary_in >= 12
     * $query->filterByMinExtraordinaryIn(array('max' => 12)); // WHERE min_extraordinary_in <= 12
     * </code>
     *
     * @param     mixed $minExtraordinaryIn The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinExtraordinaryIn($minExtraordinaryIn = null, $comparison = null)
    {
        if (is_array($minExtraordinaryIn)) {
            $useMinMax = false;
            if (isset($minExtraordinaryIn['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_IN, $minExtraordinaryIn['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minExtraordinaryIn['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_IN, $minExtraordinaryIn['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_IN, $minExtraordinaryIn, $comparison);
    }

    /**
     * Filter the query on the min_extraordinary_out column
     *
     * Example usage:
     * <code>
     * $query->filterByMinExtraordinaryOut(1234); // WHERE min_extraordinary_out = 1234
     * $query->filterByMinExtraordinaryOut(array(12, 34)); // WHERE min_extraordinary_out IN (12, 34)
     * $query->filterByMinExtraordinaryOut(array('min' => 12)); // WHERE min_extraordinary_out >= 12
     * $query->filterByMinExtraordinaryOut(array('max' => 12)); // WHERE min_extraordinary_out <= 12
     * </code>
     *
     * @param     mixed $minExtraordinaryOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinExtraordinaryOut($minExtraordinaryOut = null, $comparison = null)
    {
        if (is_array($minExtraordinaryOut)) {
            $useMinMax = false;
            if (isset($minExtraordinaryOut['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_OUT, $minExtraordinaryOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minExtraordinaryOut['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_OUT, $minExtraordinaryOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_OUT, $minExtraordinaryOut, $comparison);
    }

    /**
     * Filter the query on the step_out column
     *
     * Example usage:
     * <code>
     * $query->filterByStepOut(1234); // WHERE step_out = 1234
     * $query->filterByStepOut(array(12, 34)); // WHERE step_out IN (12, 34)
     * $query->filterByStepOut(array('min' => 12)); // WHERE step_out >= 12
     * $query->filterByStepOut(array('max' => 12)); // WHERE step_out <= 12
     * </code>
     *
     * @param     mixed $stepOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepOut($stepOut = null, $comparison = null)
    {
        if (is_array($stepOut)) {
            $useMinMax = false;
            if (isset($stepOut['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_OUT, $stepOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepOut['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_OUT, $stepOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_OUT, $stepOut, $comparison);
    }

    /**
     * Filter the query on the step_in column
     *
     * Example usage:
     * <code>
     * $query->filterByStepIn(1234); // WHERE step_in = 1234
     * $query->filterByStepIn(array(12, 34)); // WHERE step_in IN (12, 34)
     * $query->filterByStepIn(array('min' => 12)); // WHERE step_in >= 12
     * $query->filterByStepIn(array('max' => 12)); // WHERE step_in <= 12
     * </code>
     *
     * @param     mixed $stepIn The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepIn($stepIn = null, $comparison = null)
    {
        if (is_array($stepIn)) {
            $useMinMax = false;
            if (isset($stepIn['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_IN, $stepIn['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepIn['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_IN, $stepIn['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_IN, $stepIn, $comparison);
    }

    /**
     * Filter the query on the max_break column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxBreak(1234); // WHERE max_break = 1234
     * $query->filterByMaxBreak(array(12, 34)); // WHERE max_break IN (12, 34)
     * $query->filterByMaxBreak(array('min' => 12)); // WHERE max_break >= 12
     * $query->filterByMaxBreak(array('max' => 12)); // WHERE max_break <= 12
     * </code>
     *
     * @param     mixed $maxBreak The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxBreak($maxBreak = null, $comparison = null)
    {
        if (is_array($maxBreak)) {
            $useMinMax = false;
            if (isset($maxBreak['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_BREAK, $maxBreak['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxBreak['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_BREAK, $maxBreak['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_BREAK, $maxBreak, $comparison);
    }

    /**
     * Filter the query on the max_cont_work column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxContWork(1234); // WHERE max_cont_work = 1234
     * $query->filterByMaxContWork(array(12, 34)); // WHERE max_cont_work IN (12, 34)
     * $query->filterByMaxContWork(array('min' => 12)); // WHERE max_cont_work >= 12
     * $query->filterByMaxContWork(array('max' => 12)); // WHERE max_cont_work <= 12
     * </code>
     *
     * @param     mixed $maxContWork The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxContWork($maxContWork = null, $comparison = null)
    {
        if (is_array($maxContWork)) {
            $useMinMax = false;
            if (isset($maxContWork['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_CONT_WORK, $maxContWork['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxContWork['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_CONT_WORK, $maxContWork['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_CONT_WORK, $maxContWork, $comparison);
    }

    /**
     * Filter the query on the simplified_ata_settings column
     *
     * Example usage:
     * <code>
     * $query->filterBySimplifiedAtaSettings(true); // WHERE simplified_ata_settings = true
     * $query->filterBySimplifiedAtaSettings('yes'); // WHERE simplified_ata_settings = true
     * </code>
     *
     * @param     boolean|string $simplifiedAtaSettings The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterBySimplifiedAtaSettings($simplifiedAtaSettings = null, $comparison = null)
    {
        if (is_string($simplifiedAtaSettings)) {
            $simplifiedAtaSettings = in_array(strtolower($simplifiedAtaSettings), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::SIMPLIFIED_ATA_SETTINGS, $simplifiedAtaSettings, $comparison);
    }

    /**
     * Filter the query on the tolerance_in_und column
     *
     * Example usage:
     * <code>
     * $query->filterByToleranceInUnd(1234); // WHERE tolerance_in_und = 1234
     * $query->filterByToleranceInUnd(array(12, 34)); // WHERE tolerance_in_und IN (12, 34)
     * $query->filterByToleranceInUnd(array('min' => 12)); // WHERE tolerance_in_und >= 12
     * $query->filterByToleranceInUnd(array('max' => 12)); // WHERE tolerance_in_und <= 12
     * </code>
     *
     * @param     mixed $toleranceInUnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByToleranceInUnd($toleranceInUnd = null, $comparison = null)
    {
        if (is_array($toleranceInUnd)) {
            $useMinMax = false;
            if (isset($toleranceInUnd['min'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_IN_UND, $toleranceInUnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($toleranceInUnd['max'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_IN_UND, $toleranceInUnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::TOLERANCE_IN_UND, $toleranceInUnd, $comparison);
    }

    /**
     * Filter the query on the tolerance_out_und column
     *
     * Example usage:
     * <code>
     * $query->filterByToleranceOutUnd(1234); // WHERE tolerance_out_und = 1234
     * $query->filterByToleranceOutUnd(array(12, 34)); // WHERE tolerance_out_und IN (12, 34)
     * $query->filterByToleranceOutUnd(array('min' => 12)); // WHERE tolerance_out_und >= 12
     * $query->filterByToleranceOutUnd(array('max' => 12)); // WHERE tolerance_out_und <= 12
     * </code>
     *
     * @param     mixed $toleranceOutUnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByToleranceOutUnd($toleranceOutUnd = null, $comparison = null)
    {
        if (is_array($toleranceOutUnd)) {
            $useMinMax = false;
            if (isset($toleranceOutUnd['min'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_OUT_UND, $toleranceOutUnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($toleranceOutUnd['max'])) {
                $this->addUsingAlias(EmployeePeer::TOLERANCE_OUT_UND, $toleranceOutUnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::TOLERANCE_OUT_UND, $toleranceOutUnd, $comparison);
    }

    /**
     * Filter the query on the max_undefined_in column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxUndefinedIn(1234); // WHERE max_undefined_in = 1234
     * $query->filterByMaxUndefinedIn(array(12, 34)); // WHERE max_undefined_in IN (12, 34)
     * $query->filterByMaxUndefinedIn(array('min' => 12)); // WHERE max_undefined_in >= 12
     * $query->filterByMaxUndefinedIn(array('max' => 12)); // WHERE max_undefined_in <= 12
     * </code>
     *
     * @param     mixed $maxUndefinedIn The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxUndefinedIn($maxUndefinedIn = null, $comparison = null)
    {
        if (is_array($maxUndefinedIn)) {
            $useMinMax = false;
            if (isset($maxUndefinedIn['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_IN, $maxUndefinedIn['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxUndefinedIn['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_IN, $maxUndefinedIn['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_IN, $maxUndefinedIn, $comparison);
    }

    /**
     * Filter the query on the max_undefined_out column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxUndefinedOut(1234); // WHERE max_undefined_out = 1234
     * $query->filterByMaxUndefinedOut(array(12, 34)); // WHERE max_undefined_out IN (12, 34)
     * $query->filterByMaxUndefinedOut(array('min' => 12)); // WHERE max_undefined_out >= 12
     * $query->filterByMaxUndefinedOut(array('max' => 12)); // WHERE max_undefined_out <= 12
     * </code>
     *
     * @param     mixed $maxUndefinedOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxUndefinedOut($maxUndefinedOut = null, $comparison = null)
    {
        if (is_array($maxUndefinedOut)) {
            $useMinMax = false;
            if (isset($maxUndefinedOut['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_OUT, $maxUndefinedOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxUndefinedOut['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_OUT, $maxUndefinedOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_OUT, $maxUndefinedOut, $comparison);
    }

    /**
     * Filter the query on the min_undefined_in column
     *
     * Example usage:
     * <code>
     * $query->filterByMinUndefinedIn(1234); // WHERE min_undefined_in = 1234
     * $query->filterByMinUndefinedIn(array(12, 34)); // WHERE min_undefined_in IN (12, 34)
     * $query->filterByMinUndefinedIn(array('min' => 12)); // WHERE min_undefined_in >= 12
     * $query->filterByMinUndefinedIn(array('max' => 12)); // WHERE min_undefined_in <= 12
     * </code>
     *
     * @param     mixed $minUndefinedIn The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinUndefinedIn($minUndefinedIn = null, $comparison = null)
    {
        if (is_array($minUndefinedIn)) {
            $useMinMax = false;
            if (isset($minUndefinedIn['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_IN, $minUndefinedIn['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minUndefinedIn['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_IN, $minUndefinedIn['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_IN, $minUndefinedIn, $comparison);
    }

    /**
     * Filter the query on the min_undefined_out column
     *
     * Example usage:
     * <code>
     * $query->filterByMinUndefinedOut(1234); // WHERE min_undefined_out = 1234
     * $query->filterByMinUndefinedOut(array(12, 34)); // WHERE min_undefined_out IN (12, 34)
     * $query->filterByMinUndefinedOut(array('min' => 12)); // WHERE min_undefined_out >= 12
     * $query->filterByMinUndefinedOut(array('max' => 12)); // WHERE min_undefined_out <= 12
     * </code>
     *
     * @param     mixed $minUndefinedOut The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinUndefinedOut($minUndefinedOut = null, $comparison = null)
    {
        if (is_array($minUndefinedOut)) {
            $useMinMax = false;
            if (isset($minUndefinedOut['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_OUT, $minUndefinedOut['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minUndefinedOut['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_OUT, $minUndefinedOut['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_OUT, $minUndefinedOut, $comparison);
    }

    /**
     * Filter the query on the step_out_und column
     *
     * Example usage:
     * <code>
     * $query->filterByStepOutUnd(1234); // WHERE step_out_und = 1234
     * $query->filterByStepOutUnd(array(12, 34)); // WHERE step_out_und IN (12, 34)
     * $query->filterByStepOutUnd(array('min' => 12)); // WHERE step_out_und >= 12
     * $query->filterByStepOutUnd(array('max' => 12)); // WHERE step_out_und <= 12
     * </code>
     *
     * @param     mixed $stepOutUnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepOutUnd($stepOutUnd = null, $comparison = null)
    {
        if (is_array($stepOutUnd)) {
            $useMinMax = false;
            if (isset($stepOutUnd['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_OUT_UND, $stepOutUnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepOutUnd['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_OUT_UND, $stepOutUnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_OUT_UND, $stepOutUnd, $comparison);
    }

    /**
     * Filter the query on the step_in_und column
     *
     * Example usage:
     * <code>
     * $query->filterByStepInUnd(1234); // WHERE step_in_und = 1234
     * $query->filterByStepInUnd(array(12, 34)); // WHERE step_in_und IN (12, 34)
     * $query->filterByStepInUnd(array('min' => 12)); // WHERE step_in_und >= 12
     * $query->filterByStepInUnd(array('max' => 12)); // WHERE step_in_und <= 12
     * </code>
     *
     * @param     mixed $stepInUnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepInUnd($stepInUnd = null, $comparison = null)
    {
        if (is_array($stepInUnd)) {
            $useMinMax = false;
            if (isset($stepInUnd['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_IN_UND, $stepInUnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepInUnd['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_IN_UND, $stepInUnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_IN_UND, $stepInUnd, $comparison);
    }

    /**
     * Filter the query on the undefined_parameter_active column
     *
     * Example usage:
     * <code>
     * $query->filterByUndefinedParameterActive(true); // WHERE undefined_parameter_active = true
     * $query->filterByUndefinedParameterActive('yes'); // WHERE undefined_parameter_active = true
     * </code>
     *
     * @param     boolean|string $undefinedParameterActive The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByUndefinedParameterActive($undefinedParameterActive = null, $comparison = null)
    {
        if (is_string($undefinedParameterActive)) {
            $undefinedParameterActive = in_array(strtolower($undefinedParameterActive), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::UNDEFINED_PARAMETER_ACTIVE, $undefinedParameterActive, $comparison);
    }

    /**
     * Filter the query on the min_extraordinary_total column
     *
     * Example usage:
     * <code>
     * $query->filterByMinExtraordinaryTotal(1234); // WHERE min_extraordinary_total = 1234
     * $query->filterByMinExtraordinaryTotal(array(12, 34)); // WHERE min_extraordinary_total IN (12, 34)
     * $query->filterByMinExtraordinaryTotal(array('min' => 12)); // WHERE min_extraordinary_total >= 12
     * $query->filterByMinExtraordinaryTotal(array('max' => 12)); // WHERE min_extraordinary_total <= 12
     * </code>
     *
     * @param     mixed $minExtraordinaryTotal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinExtraordinaryTotal($minExtraordinaryTotal = null, $comparison = null)
    {
        if (is_array($minExtraordinaryTotal)) {
            $useMinMax = false;
            if (isset($minExtraordinaryTotal['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_TOTAL, $minExtraordinaryTotal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minExtraordinaryTotal['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_TOTAL, $minExtraordinaryTotal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_TOTAL, $minExtraordinaryTotal, $comparison);
    }

    /**
     * Filter the query on the max_extraordinary_total column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxExtraordinaryTotal(1234); // WHERE max_extraordinary_total = 1234
     * $query->filterByMaxExtraordinaryTotal(array(12, 34)); // WHERE max_extraordinary_total IN (12, 34)
     * $query->filterByMaxExtraordinaryTotal(array('min' => 12)); // WHERE max_extraordinary_total >= 12
     * $query->filterByMaxExtraordinaryTotal(array('max' => 12)); // WHERE max_extraordinary_total <= 12
     * </code>
     *
     * @param     mixed $maxExtraordinaryTotal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxExtraordinaryTotal($maxExtraordinaryTotal = null, $comparison = null)
    {
        if (is_array($maxExtraordinaryTotal)) {
            $useMinMax = false;
            if (isset($maxExtraordinaryTotal['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_TOTAL, $maxExtraordinaryTotal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxExtraordinaryTotal['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_TOTAL, $maxExtraordinaryTotal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_TOTAL, $maxExtraordinaryTotal, $comparison);
    }

    /**
     * Filter the query on the min_undefined_total column
     *
     * Example usage:
     * <code>
     * $query->filterByMinUndefinedTotal(1234); // WHERE min_undefined_total = 1234
     * $query->filterByMinUndefinedTotal(array(12, 34)); // WHERE min_undefined_total IN (12, 34)
     * $query->filterByMinUndefinedTotal(array('min' => 12)); // WHERE min_undefined_total >= 12
     * $query->filterByMinUndefinedTotal(array('max' => 12)); // WHERE min_undefined_total <= 12
     * </code>
     *
     * @param     mixed $minUndefinedTotal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinUndefinedTotal($minUndefinedTotal = null, $comparison = null)
    {
        if (is_array($minUndefinedTotal)) {
            $useMinMax = false;
            if (isset($minUndefinedTotal['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_TOTAL, $minUndefinedTotal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minUndefinedTotal['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_TOTAL, $minUndefinedTotal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_TOTAL, $minUndefinedTotal, $comparison);
    }

    /**
     * Filter the query on the max_undefined_total column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxUndefinedTotal(1234); // WHERE max_undefined_total = 1234
     * $query->filterByMaxUndefinedTotal(array(12, 34)); // WHERE max_undefined_total IN (12, 34)
     * $query->filterByMaxUndefinedTotal(array('min' => 12)); // WHERE max_undefined_total >= 12
     * $query->filterByMaxUndefinedTotal(array('max' => 12)); // WHERE max_undefined_total <= 12
     * </code>
     *
     * @param     mixed $maxUndefinedTotal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxUndefinedTotal($maxUndefinedTotal = null, $comparison = null)
    {
        if (is_array($maxUndefinedTotal)) {
            $useMinMax = false;
            if (isset($maxUndefinedTotal['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_TOTAL, $maxUndefinedTotal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxUndefinedTotal['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_TOTAL, $maxUndefinedTotal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_TOTAL, $maxUndefinedTotal, $comparison);
    }

    /**
     * Filter the query on the step_total_undefined column
     *
     * Example usage:
     * <code>
     * $query->filterByStepTotalUndefined(1234); // WHERE step_total_undefined = 1234
     * $query->filterByStepTotalUndefined(array(12, 34)); // WHERE step_total_undefined IN (12, 34)
     * $query->filterByStepTotalUndefined(array('min' => 12)); // WHERE step_total_undefined >= 12
     * $query->filterByStepTotalUndefined(array('max' => 12)); // WHERE step_total_undefined <= 12
     * </code>
     *
     * @param     mixed $stepTotalUndefined The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepTotalUndefined($stepTotalUndefined = null, $comparison = null)
    {
        if (is_array($stepTotalUndefined)) {
            $useMinMax = false;
            if (isset($stepTotalUndefined['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_TOTAL_UNDEFINED, $stepTotalUndefined['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepTotalUndefined['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_TOTAL_UNDEFINED, $stepTotalUndefined['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_TOTAL_UNDEFINED, $stepTotalUndefined, $comparison);
    }

    /**
     * Filter the query on the step_total_extraordinary column
     *
     * Example usage:
     * <code>
     * $query->filterByStepTotalExtraordinary(1234); // WHERE step_total_extraordinary = 1234
     * $query->filterByStepTotalExtraordinary(array(12, 34)); // WHERE step_total_extraordinary IN (12, 34)
     * $query->filterByStepTotalExtraordinary(array('min' => 12)); // WHERE step_total_extraordinary >= 12
     * $query->filterByStepTotalExtraordinary(array('max' => 12)); // WHERE step_total_extraordinary <= 12
     * </code>
     *
     * @param     mixed $stepTotalExtraordinary The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepTotalExtraordinary($stepTotalExtraordinary = null, $comparison = null)
    {
        if (is_array($stepTotalExtraordinary)) {
            $useMinMax = false;
            if (isset($stepTotalExtraordinary['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_TOTAL_EXTRAORDINARY, $stepTotalExtraordinary['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepTotalExtraordinary['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_TOTAL_EXTRAORDINARY, $stepTotalExtraordinary['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_TOTAL_EXTRAORDINARY, $stepTotalExtraordinary, $comparison);
    }

    /**
     * Filter the query on the lunch_duration column
     *
     * Example usage:
     * <code>
     * $query->filterByLunchDuration(1234); // WHERE lunch_duration = 1234
     * $query->filterByLunchDuration(array(12, 34)); // WHERE lunch_duration IN (12, 34)
     * $query->filterByLunchDuration(array('min' => 12)); // WHERE lunch_duration >= 12
     * $query->filterByLunchDuration(array('max' => 12)); // WHERE lunch_duration <= 12
     * </code>
     *
     * @param     mixed $lunchDuration The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByLunchDuration($lunchDuration = null, $comparison = null)
    {
        if (is_array($lunchDuration)) {
            $useMinMax = false;
            if (isset($lunchDuration['min'])) {
                $this->addUsingAlias(EmployeePeer::LUNCH_DURATION, $lunchDuration['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($lunchDuration['max'])) {
                $this->addUsingAlias(EmployeePeer::LUNCH_DURATION, $lunchDuration['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::LUNCH_DURATION, $lunchDuration, $comparison);
    }

    /**
     * Filter the query on the lunch_deductible column
     *
     * Example usage:
     * <code>
     * $query->filterByLunchDeductible(true); // WHERE lunch_deductible = true
     * $query->filterByLunchDeductible('yes'); // WHERE lunch_deductible = true
     * </code>
     *
     * @param     boolean|string $lunchDeductible The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByLunchDeductible($lunchDeductible = null, $comparison = null)
    {
        if (is_string($lunchDeductible)) {
            $lunchDeductible = in_array(strtolower($lunchDeductible), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::LUNCH_DEDUCTIBLE, $lunchDeductible, $comparison);
    }

    /**
     * Filter the query on the service_deductible column
     *
     * Example usage:
     * <code>
     * $query->filterByServiceDeductible(true); // WHERE service_deductible = true
     * $query->filterByServiceDeductible('yes'); // WHERE service_deductible = true
     * </code>
     *
     * @param     boolean|string $serviceDeductible The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByServiceDeductible($serviceDeductible = null, $comparison = null)
    {
        if (is_string($serviceDeductible)) {
            $serviceDeductible = in_array(strtolower($serviceDeductible), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(EmployeePeer::SERVICE_DEDUCTIBLE, $serviceDeductible, $comparison);
    }

    /**
     * Filter the query on the min_undefined_lunch column
     *
     * Example usage:
     * <code>
     * $query->filterByMinUndefinedLunch(1234); // WHERE min_undefined_lunch = 1234
     * $query->filterByMinUndefinedLunch(array(12, 34)); // WHERE min_undefined_lunch IN (12, 34)
     * $query->filterByMinUndefinedLunch(array('min' => 12)); // WHERE min_undefined_lunch >= 12
     * $query->filterByMinUndefinedLunch(array('max' => 12)); // WHERE min_undefined_lunch <= 12
     * </code>
     *
     * @param     mixed $minUndefinedLunch The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinUndefinedLunch($minUndefinedLunch = null, $comparison = null)
    {
        if (is_array($minUndefinedLunch)) {
            $useMinMax = false;
            if (isset($minUndefinedLunch['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_LUNCH, $minUndefinedLunch['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minUndefinedLunch['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_LUNCH, $minUndefinedLunch['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_UNDEFINED_LUNCH, $minUndefinedLunch, $comparison);
    }

    /**
     * Filter the query on the min_extraordinary_lunch column
     *
     * Example usage:
     * <code>
     * $query->filterByMinExtraordinaryLunch(1234); // WHERE min_extraordinary_lunch = 1234
     * $query->filterByMinExtraordinaryLunch(array(12, 34)); // WHERE min_extraordinary_lunch IN (12, 34)
     * $query->filterByMinExtraordinaryLunch(array('min' => 12)); // WHERE min_extraordinary_lunch >= 12
     * $query->filterByMinExtraordinaryLunch(array('max' => 12)); // WHERE min_extraordinary_lunch <= 12
     * </code>
     *
     * @param     mixed $minExtraordinaryLunch The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMinExtraordinaryLunch($minExtraordinaryLunch = null, $comparison = null)
    {
        if (is_array($minExtraordinaryLunch)) {
            $useMinMax = false;
            if (isset($minExtraordinaryLunch['min'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_LUNCH, $minExtraordinaryLunch['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($minExtraordinaryLunch['max'])) {
                $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_LUNCH, $minExtraordinaryLunch['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MIN_EXTRAORDINARY_LUNCH, $minExtraordinaryLunch, $comparison);
    }

    /**
     * Filter the query on the max_undefined_lunch column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxUndefinedLunch(1234); // WHERE max_undefined_lunch = 1234
     * $query->filterByMaxUndefinedLunch(array(12, 34)); // WHERE max_undefined_lunch IN (12, 34)
     * $query->filterByMaxUndefinedLunch(array('min' => 12)); // WHERE max_undefined_lunch >= 12
     * $query->filterByMaxUndefinedLunch(array('max' => 12)); // WHERE max_undefined_lunch <= 12
     * </code>
     *
     * @param     mixed $maxUndefinedLunch The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxUndefinedLunch($maxUndefinedLunch = null, $comparison = null)
    {
        if (is_array($maxUndefinedLunch)) {
            $useMinMax = false;
            if (isset($maxUndefinedLunch['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_LUNCH, $maxUndefinedLunch['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxUndefinedLunch['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_LUNCH, $maxUndefinedLunch['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_UNDEFINED_LUNCH, $maxUndefinedLunch, $comparison);
    }

    /**
     * Filter the query on the max_extraordinary_lunch column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxExtraordinaryLunch(1234); // WHERE max_extraordinary_lunch = 1234
     * $query->filterByMaxExtraordinaryLunch(array(12, 34)); // WHERE max_extraordinary_lunch IN (12, 34)
     * $query->filterByMaxExtraordinaryLunch(array('min' => 12)); // WHERE max_extraordinary_lunch >= 12
     * $query->filterByMaxExtraordinaryLunch(array('max' => 12)); // WHERE max_extraordinary_lunch <= 12
     * </code>
     *
     * @param     mixed $maxExtraordinaryLunch The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxExtraordinaryLunch($maxExtraordinaryLunch = null, $comparison = null)
    {
        if (is_array($maxExtraordinaryLunch)) {
            $useMinMax = false;
            if (isset($maxExtraordinaryLunch['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_LUNCH, $maxExtraordinaryLunch['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxExtraordinaryLunch['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_LUNCH, $maxExtraordinaryLunch['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_EXTRAORDINARY_LUNCH, $maxExtraordinaryLunch, $comparison);
    }

    /**
     * Filter the query on the step_lunch_undefined column
     *
     * Example usage:
     * <code>
     * $query->filterByStepLunchUndefined(1234); // WHERE step_lunch_undefined = 1234
     * $query->filterByStepLunchUndefined(array(12, 34)); // WHERE step_lunch_undefined IN (12, 34)
     * $query->filterByStepLunchUndefined(array('min' => 12)); // WHERE step_lunch_undefined >= 12
     * $query->filterByStepLunchUndefined(array('max' => 12)); // WHERE step_lunch_undefined <= 12
     * </code>
     *
     * @param     mixed $stepLunchUndefined The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepLunchUndefined($stepLunchUndefined = null, $comparison = null)
    {
        if (is_array($stepLunchUndefined)) {
            $useMinMax = false;
            if (isset($stepLunchUndefined['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_LUNCH_UNDEFINED, $stepLunchUndefined['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepLunchUndefined['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_LUNCH_UNDEFINED, $stepLunchUndefined['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_LUNCH_UNDEFINED, $stepLunchUndefined, $comparison);
    }

    /**
     * Filter the query on the step_lunch_extraordinary column
     *
     * Example usage:
     * <code>
     * $query->filterByStepLunchExtraordinary(1234); // WHERE step_lunch_extraordinary = 1234
     * $query->filterByStepLunchExtraordinary(array(12, 34)); // WHERE step_lunch_extraordinary IN (12, 34)
     * $query->filterByStepLunchExtraordinary(array('min' => 12)); // WHERE step_lunch_extraordinary >= 12
     * $query->filterByStepLunchExtraordinary(array('max' => 12)); // WHERE step_lunch_extraordinary <= 12
     * </code>
     *
     * @param     mixed $stepLunchExtraordinary The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByStepLunchExtraordinary($stepLunchExtraordinary = null, $comparison = null)
    {
        if (is_array($stepLunchExtraordinary)) {
            $useMinMax = false;
            if (isset($stepLunchExtraordinary['min'])) {
                $this->addUsingAlias(EmployeePeer::STEP_LUNCH_EXTRAORDINARY, $stepLunchExtraordinary['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stepLunchExtraordinary['max'])) {
                $this->addUsingAlias(EmployeePeer::STEP_LUNCH_EXTRAORDINARY, $stepLunchExtraordinary['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::STEP_LUNCH_EXTRAORDINARY, $stepLunchExtraordinary, $comparison);
    }

    /**
     * Filter the query on the break_after_max_work column
     *
     * Example usage:
     * <code>
     * $query->filterByBreakAfterMaxWork(1234); // WHERE break_after_max_work = 1234
     * $query->filterByBreakAfterMaxWork(array(12, 34)); // WHERE break_after_max_work IN (12, 34)
     * $query->filterByBreakAfterMaxWork(array('min' => 12)); // WHERE break_after_max_work >= 12
     * $query->filterByBreakAfterMaxWork(array('max' => 12)); // WHERE break_after_max_work <= 12
     * </code>
     *
     * @param     mixed $breakAfterMaxWork The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByBreakAfterMaxWork($breakAfterMaxWork = null, $comparison = null)
    {
        if (is_array($breakAfterMaxWork)) {
            $useMinMax = false;
            if (isset($breakAfterMaxWork['min'])) {
                $this->addUsingAlias(EmployeePeer::BREAK_AFTER_MAX_WORK, $breakAfterMaxWork['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($breakAfterMaxWork['max'])) {
                $this->addUsingAlias(EmployeePeer::BREAK_AFTER_MAX_WORK, $breakAfterMaxWork['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::BREAK_AFTER_MAX_WORK, $breakAfterMaxWork, $comparison);
    }

    /**
     * Filter the query on the unit_recover_hours column
     *
     * Example usage:
     * <code>
     * $query->filterByUnitRecoverHours('fooValue');   // WHERE unit_recover_hours = 'fooValue'
     * $query->filterByUnitRecoverHours('%fooValue%'); // WHERE unit_recover_hours LIKE '%fooValue%'
     * </code>
     *
     * @param     string $unitRecoverHours The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByUnitRecoverHours($unitRecoverHours = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($unitRecoverHours)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $unitRecoverHours)) {
                $unitRecoverHours = str_replace('*', '%', $unitRecoverHours);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(EmployeePeer::UNIT_RECOVER_HOURS, $unitRecoverHours, $comparison);
    }

    /**
     * Filter the query on the max_work column
     *
     * Example usage:
     * <code>
     * $query->filterByMaxWork(1234); // WHERE max_work = 1234
     * $query->filterByMaxWork(array(12, 34)); // WHERE max_work IN (12, 34)
     * $query->filterByMaxWork(array('min' => 12)); // WHERE max_work >= 12
     * $query->filterByMaxWork(array('max' => 12)); // WHERE max_work <= 12
     * </code>
     *
     * @param     mixed $maxWork The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function filterByMaxWork($maxWork = null, $comparison = null)
    {
        if (is_array($maxWork)) {
            $useMinMax = false;
            if (isset($maxWork['min'])) {
                $this->addUsingAlias(EmployeePeer::MAX_WORK, $maxWork['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($maxWork['max'])) {
                $this->addUsingAlias(EmployeePeer::MAX_WORK, $maxWork['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(EmployeePeer::MAX_WORK, $maxWork, $comparison);
    }

    /**
     * Filter the query by a related Contact object
     *
     * @param   Contact|PropelObjectCollection $contact The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByResidenceKey($contact, $comparison = null)
    {
        if ($contact instanceof Contact) {
            return $this
                ->addUsingAlias(EmployeePeer::RESIDENCE_ID, $contact->getContactId(), $comparison);
        } elseif ($contact instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(EmployeePeer::RESIDENCE_ID, $contact->toKeyValue('PrimaryKey', 'ContactId'), $comparison);
        } else {
            throw new PropelException('filterByResidenceKey() only accepts arguments of type Contact or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the ResidenceKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinResidenceKey($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('ResidenceKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'ResidenceKey');
        }

        return $this;
    }

    /**
     * Use the ResidenceKey relation Contact object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\ContactQuery A secondary query class using the current class as primary query
     */
    public function useResidenceKeyQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinResidenceKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'ResidenceKey', '\Core\ContactQuery');
    }

    /**
     * Filter the query by a related Contact object
     *
     * @param   Contact|PropelObjectCollection $contact The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAddressKey($contact, $comparison = null)
    {
        if ($contact instanceof Contact) {
            return $this
                ->addUsingAlias(EmployeePeer::ADDRESS_ID, $contact->getContactId(), $comparison);
        } elseif ($contact instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(EmployeePeer::ADDRESS_ID, $contact->toKeyValue('PrimaryKey', 'ContactId'), $comparison);
        } else {
            throw new PropelException('filterByAddressKey() only accepts arguments of type Contact or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AddressKey relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinAddressKey($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AddressKey');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AddressKey');
        }

        return $this;
    }

    /**
     * Use the AddressKey relation Contact object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\ContactQuery A secondary query class using the current class as primary query
     */
    public function useAddressKeyQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAddressKey($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AddressKey', '\Core\ContactQuery');
    }

    /**
     * Filter the query by a related Institute object
     *
     * @param   Institute|PropelObjectCollection $institute  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByInstitute($institute, $comparison = null)
    {
        if ($institute instanceof Institute) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $institute->getDirEmpId(), $comparison);
        } elseif ($institute instanceof PropelObjectCollection) {
            return $this
                ->useInstituteQuery()
                ->filterByPrimaryKeys($institute->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByInstitute() only accepts arguments of type Institute or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Institute relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinInstitute($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Institute');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Institute');
        }

        return $this;
    }

    /**
     * Use the Institute relation Institute object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Core\InstituteQuery A secondary query class using the current class as primary query
     */
    public function useInstituteQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinInstitute($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Institute', '\Core\InstituteQuery');
    }

    /**
     * Filter the query by a related Absences object
     *
     * @param   Absences|PropelObjectCollection $absences  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAbsences($absences, $comparison = null)
    {
        if ($absences instanceof Absences) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $absences->getEmployeeId(), $comparison);
        } elseif ($absences instanceof PropelObjectCollection) {
            return $this
                ->useAbsencesQuery()
                ->filterByPrimaryKeys($absences->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAbsences() only accepts arguments of type Absences or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Absences relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinAbsences($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Absences');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Absences');
        }

        return $this;
    }

    /**
     * Use the Absences relation Absences object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsencesQuery A secondary query class using the current class as primary query
     */
    public function useAbsencesQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAbsences($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Absences', '\Employee\AbsencesQuery');
    }

    /**
     * Filter the query by a related Timetable object
     *
     * @param   Timetable|PropelObjectCollection $timetable  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTimetable($timetable, $comparison = null)
    {
        if ($timetable instanceof Timetable) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $timetable->getEmployeeId(), $comparison);
        } elseif ($timetable instanceof PropelObjectCollection) {
            return $this
                ->useTimetableQuery()
                ->filterByPrimaryKeys($timetable->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByTimetable() only accepts arguments of type Timetable or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Timetable relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinTimetable($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Timetable');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Timetable');
        }

        return $this;
    }

    /**
     * Use the Timetable relation Timetable object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\TimetableQuery A secondary query class using the current class as primary query
     */
    public function useTimetableQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinTimetable($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Timetable', '\Employee\TimetableQuery');
    }

    /**
     * Filter the query by a related Presence object
     *
     * @param   Presence|PropelObjectCollection $presence  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPresence($presence, $comparison = null)
    {
        if ($presence instanceof Presence) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $presence->getEmployeeId(), $comparison);
        } elseif ($presence instanceof PropelObjectCollection) {
            return $this
                ->usePresenceQuery()
                ->filterByPrimaryKeys($presence->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByPresence() only accepts arguments of type Presence or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Presence relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinPresence($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Presence');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Presence');
        }

        return $this;
    }

    /**
     * Use the Presence relation Presence object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\PresenceQuery A secondary query class using the current class as primary query
     */
    public function usePresenceQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinPresence($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Presence', '\Employee\PresenceQuery');
    }

    /**
     * Filter the query by a related PersonnelStacks object
     *
     * @param   PersonnelStacks|PropelObjectCollection $personnelStacks  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPersonnelStacks($personnelStacks, $comparison = null)
    {
        if ($personnelStacks instanceof PersonnelStacks) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $personnelStacks->getEmployeeId(), $comparison);
        } elseif ($personnelStacks instanceof PropelObjectCollection) {
            return $this
                ->usePersonnelStacksQuery()
                ->filterByPrimaryKeys($personnelStacks->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByPersonnelStacks() only accepts arguments of type PersonnelStacks or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the PersonnelStacks relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinPersonnelStacks($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('PersonnelStacks');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'PersonnelStacks');
        }

        return $this;
    }

    /**
     * Use the PersonnelStacks relation PersonnelStacks object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\PersonnelStacksQuery A secondary query class using the current class as primary query
     */
    public function usePersonnelStacksQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinPersonnelStacks($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'PersonnelStacks', '\Employee\PersonnelStacksQuery');
    }

    /**
     * Filter the query by a related StoredMonth object
     *
     * @param   StoredMonth|PropelObjectCollection $storedMonth  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredMonth($storedMonth, $comparison = null)
    {
        if ($storedMonth instanceof StoredMonth) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $storedMonth->getEmployeeId(), $comparison);
        } elseif ($storedMonth instanceof PropelObjectCollection) {
            return $this
                ->useStoredMonthQuery()
                ->filterByPrimaryKeys($storedMonth->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByStoredMonth() only accepts arguments of type StoredMonth or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredMonth relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinStoredMonth($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredMonth');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredMonth');
        }

        return $this;
    }

    /**
     * Use the StoredMonth relation StoredMonth object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\StoredMonthQuery A secondary query class using the current class as primary query
     */
    public function useStoredMonthQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinStoredMonth($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredMonth', '\Employee\StoredMonthQuery');
    }

    /**
     * Filter the query by a related StoredDay object
     *
     * @param   StoredDay|PropelObjectCollection $storedDay  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 EmployeeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredDay($storedDay, $comparison = null)
    {
        if ($storedDay instanceof StoredDay) {
            return $this
                ->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $storedDay->getEmployeeId(), $comparison);
        } elseif ($storedDay instanceof PropelObjectCollection) {
            return $this
                ->useStoredDayQuery()
                ->filterByPrimaryKeys($storedDay->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByStoredDay() only accepts arguments of type StoredDay or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredDay relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function joinStoredDay($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredDay');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredDay');
        }

        return $this;
    }

    /**
     * Use the StoredDay relation StoredDay object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\StoredDayQuery A secondary query class using the current class as primary query
     */
    public function useStoredDayQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinStoredDay($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredDay', '\Employee\StoredDayQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Employee $employee Object to remove from the list of results
     *
     * @return EmployeeQuery The current query, for fluid interface
     */
    public function prune($employee = null)
    {
        if ($employee) {
            $this->addUsingAlias(EmployeePeer::EMPLOYEE_ID, $employee->getEmployeeId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
