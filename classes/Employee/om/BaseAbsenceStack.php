<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \DateTime;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelDateTime;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceKind;
use Employee\AbsenceKindQuery;
use Employee\AbsenceStack;
use Employee\AbsenceStackPeer;
use Employee\AbsenceStackQuery;
use Employee\PersonnelStacks;
use Employee\PersonnelStacksQuery;
use Employee\StoredStack;
use Employee\StoredStackQuery;

/**
 * Base class that represents a row from the 'absence_stack' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseAbsenceStack extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\AbsenceStackPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        AbsenceStackPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id field.
     * @var        int
     */
    protected $id;

    /**
     * The value for the unit field.
     * Note: this column has a database default value of: 'h'
     * @var        string
     */
    protected $unit;

    /**
     * The value for the denomination field.
     * @var        string
     */
    protected $denomination;

    /**
     * The value for the recover field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $recover;

    /**
     * The value for the reset_type field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $reset_type;

    /**
     * The value for the reset_to_stack_id field.
     * @var        string
     */
    protected $reset_to_stack_id;

    /**
     * The value for the reset_date field.
     * @var        string
     */
    protected $reset_date;

    /**
     * The value for the reset_default_quota field.
     * Note: this column has a database default value of: 0
     * @var        double
     */
    protected $reset_default_quota;

    /**
     * @var        PropelObjectCollection|AbsenceKind[] Collection to store aggregation of AbsenceKind objects.
     */
    protected $collAbsenceKinds;
    protected $collAbsenceKindsPartial;

    /**
     * @var        PropelObjectCollection|PersonnelStacks[] Collection to store aggregation of PersonnelStacks objects.
     */
    protected $collPersonnelStackss;
    protected $collPersonnelStackssPartial;

    /**
     * @var        PropelObjectCollection|StoredStack[] Collection to store aggregation of StoredStack objects.
     */
    protected $collStoredStacks;
    protected $collStoredStacksPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $absenceKindsScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $personnelStackssScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $storedStacksScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->unit = 'h';
        $this->recover = false;
        $this->reset_type = 0;
        $this->reset_default_quota = 0;
    }

    /**
     * Initializes internal state of BaseAbsenceStack object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [id] column value.
     *
     * @return int
     */
    public function getId()
    {

        return $this->id;
    }

    /**
     * Get the [unit] column value.
     *
     * @return string
     */
    public function getUnit()
    {

        return $this->unit;
    }

    /**
     * Get the [denomination] column value.
     *
     * @return string
     */
    public function getDenomination()
    {

        return $this->denomination;
    }

    /**
     * Get the [recover] column value.
     *
     * @return boolean
     */
    public function getRecover()
    {

        return $this->recover;
    }

    /**
     * Get the [reset_type] column value.
     *
     * @return int
     */
    public function getResetType()
    {

        return $this->reset_type;
    }

    /**
     * Get the [reset_to_stack_id] column value.
     *
     * @return string
     */
    public function getResetToStackId()
    {

        return $this->reset_to_stack_id;
    }

    /**
     * Get the [optionally formatted] temporal [reset_date] column value.
     *
     *
     * @param string $format The date/time format string (either date()-style or strftime()-style).
     *				 If format is null, then the raw DateTime object will be returned.
     * @return mixed Formatted date/time value as string or DateTime object (if format is null), null if column is null
     * @throws PropelException - if unable to parse/validate the date/time value.
     */
    public function getResetDate($format = 'Y-m-d H:i:s')
    {
        if ($this->reset_date === null) {
            return null;
        }


        try {
            $dt = new DateTime($this->reset_date);
        } catch (Exception $x) {
            throw new PropelException("Internally stored date/time/timestamp value could not be converted to DateTime: " . var_export($this->reset_date, true), $x);
        }

        if ($format === null) {
            // Because propel.useDateTimeClass is true, we return a DateTime object.
            return $dt;
        }

        if (strpos($format, '%') !== false) {
            return strftime($format, $dt->format('U'));
        }

        return $dt->format($format);

    }

    /**
     * Get the [reset_default_quota] column value.
     *
     * @return double
     */
    public function getResetDefaultQuota()
    {

        return $this->reset_default_quota;
    }

    /**
     * Set the value of [id] column.
     *
     * @param  int $v new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id !== $v) {
            $this->id = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::ID;
        }


        return $this;
    } // setId()

    /**
     * Set the value of [unit] column.
     *
     * @param  string $v new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setUnit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unit !== $v) {
            $this->unit = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::UNIT;
        }


        return $this;
    } // setUnit()

    /**
     * Set the value of [denomination] column.
     *
     * @param  string $v new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setDenomination($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->denomination !== $v) {
            $this->denomination = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::DENOMINATION;
        }


        return $this;
    } // setDenomination()

    /**
     * Sets the value of the [recover] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setRecover($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->recover !== $v) {
            $this->recover = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::RECOVER;
        }


        return $this;
    } // setRecover()

    /**
     * Set the value of [reset_type] column.
     *
     * @param  int $v new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setResetType($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->reset_type !== $v) {
            $this->reset_type = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::RESET_TYPE;
        }


        return $this;
    } // setResetType()

    /**
     * Set the value of [reset_to_stack_id] column.
     *
     * @param  string $v new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setResetToStackId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->reset_to_stack_id !== $v) {
            $this->reset_to_stack_id = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::RESET_TO_STACK_ID;
        }


        return $this;
    } // setResetToStackId()

    /**
     * Sets the value of [reset_date] column to a normalized version of the date/time value specified.
     *
     * @param mixed $v string, integer (timestamp), or DateTime value.
     *               Empty strings are treated as null.
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setResetDate($v)
    {
        $dt = PropelDateTime::newInstance($v, null, 'DateTime');
        if ($this->reset_date !== null || $dt !== null) {
            $currentDateAsString = ($this->reset_date !== null && $tmpDt = new DateTime($this->reset_date)) ? $tmpDt->format('Y-m-d H:i:s') : null;
            $newDateAsString = $dt ? $dt->format('Y-m-d H:i:s') : null;
            if ($currentDateAsString !== $newDateAsString) {
                $this->reset_date = $newDateAsString;
                $this->modifiedColumns[] = AbsenceStackPeer::RESET_DATE;
            }
        } // if either are not null


        return $this;
    } // setResetDate()

    /**
     * Set the value of [reset_default_quota] column.
     *
     * @param  double $v new value
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setResetDefaultQuota($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (double) $v;
        }

        if ($this->reset_default_quota !== $v) {
            $this->reset_default_quota = $v;
            $this->modifiedColumns[] = AbsenceStackPeer::RESET_DEFAULT_QUOTA;
        }


        return $this;
    } // setResetDefaultQuota()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->unit !== 'h') {
                return false;
            }

            if ($this->recover !== false) {
                return false;
            }

            if ($this->reset_type !== 0) {
                return false;
            }

            if ($this->reset_default_quota !== 0) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->unit = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->denomination = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->recover = ($row[$startcol + 3] !== null) ? (boolean) $row[$startcol + 3] : null;
            $this->reset_type = ($row[$startcol + 4] !== null) ? (int) $row[$startcol + 4] : null;
            $this->reset_to_stack_id = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->reset_date = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->reset_default_quota = ($row[$startcol + 7] !== null) ? (double) $row[$startcol + 7] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 8; // 8 = AbsenceStackPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating AbsenceStack object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsenceStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = AbsenceStackPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->collAbsenceKinds = null;

            $this->collPersonnelStackss = null;

            $this->collStoredStacks = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsenceStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = AbsenceStackQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsenceStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                AbsenceStackPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->absenceKindsScheduledForDeletion !== null) {
                if (!$this->absenceKindsScheduledForDeletion->isEmpty()) {
                    foreach ($this->absenceKindsScheduledForDeletion as $absenceKind) {
                        // need to save related object because we set the relation to null
                        $absenceKind->save($con);
                    }
                    $this->absenceKindsScheduledForDeletion = null;
                }
            }

            if ($this->collAbsenceKinds !== null) {
                foreach ($this->collAbsenceKinds as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->personnelStackssScheduledForDeletion !== null) {
                if (!$this->personnelStackssScheduledForDeletion->isEmpty()) {
                    PersonnelStacksQuery::create()
                        ->filterByPrimaryKeys($this->personnelStackssScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->personnelStackssScheduledForDeletion = null;
                }
            }

            if ($this->collPersonnelStackss !== null) {
                foreach ($this->collPersonnelStackss as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->storedStacksScheduledForDeletion !== null) {
                if (!$this->storedStacksScheduledForDeletion->isEmpty()) {
                    foreach ($this->storedStacksScheduledForDeletion as $storedStack) {
                        // need to save related object because we set the relation to null
                        $storedStack->save($con);
                    }
                    $this->storedStacksScheduledForDeletion = null;
                }
            }

            if ($this->collStoredStacks !== null) {
                foreach ($this->collStoredStacks as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = AbsenceStackPeer::ID;
        if (null !== $this->id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . AbsenceStackPeer::ID . ')');
        }
        if (null === $this->id) {
            try {
                $stmt = $con->query("SELECT nextval('absence_stack_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(AbsenceStackPeer::ID)) {
            $modifiedColumns[':p' . $index++]  = '"id"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::UNIT)) {
            $modifiedColumns[':p' . $index++]  = '"unit"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::DENOMINATION)) {
            $modifiedColumns[':p' . $index++]  = '"denomination"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::RECOVER)) {
            $modifiedColumns[':p' . $index++]  = '"recover"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::RESET_TYPE)) {
            $modifiedColumns[':p' . $index++]  = '"reset_type"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::RESET_TO_STACK_ID)) {
            $modifiedColumns[':p' . $index++]  = '"reset_to_stack_id"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::RESET_DATE)) {
            $modifiedColumns[':p' . $index++]  = '"reset_date"';
        }
        if ($this->isColumnModified(AbsenceStackPeer::RESET_DEFAULT_QUOTA)) {
            $modifiedColumns[':p' . $index++]  = '"reset_default_quota"';
        }

        $sql = sprintf(
            'INSERT INTO "absence_stack" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id"':
                        $stmt->bindValue($identifier, $this->id, PDO::PARAM_INT);
                        break;
                    case '"unit"':
                        $stmt->bindValue($identifier, $this->unit, PDO::PARAM_STR);
                        break;
                    case '"denomination"':
                        $stmt->bindValue($identifier, $this->denomination, PDO::PARAM_STR);
                        break;
                    case '"recover"':
                        $stmt->bindValue($identifier, $this->recover, PDO::PARAM_BOOL);
                        break;
                    case '"reset_type"':
                        $stmt->bindValue($identifier, $this->reset_type, PDO::PARAM_INT);
                        break;
                    case '"reset_to_stack_id"':
                        $stmt->bindValue($identifier, $this->reset_to_stack_id, PDO::PARAM_STR);
                        break;
                    case '"reset_date"':
                        $stmt->bindValue($identifier, $this->reset_date, PDO::PARAM_STR);
                        break;
                    case '"reset_default_quota"':
                        $stmt->bindValue($identifier, $this->reset_default_quota, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = AbsenceStackPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collAbsenceKinds !== null) {
                    foreach ($this->collAbsenceKinds as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collPersonnelStackss !== null) {
                    foreach ($this->collPersonnelStackss as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collStoredStacks !== null) {
                    foreach ($this->collStoredStacks as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AbsenceStackPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getId();
                break;
            case 1:
                return $this->getUnit();
                break;
            case 2:
                return $this->getDenomination();
                break;
            case 3:
                return $this->getRecover();
                break;
            case 4:
                return $this->getResetType();
                break;
            case 5:
                return $this->getResetToStackId();
                break;
            case 6:
                return $this->getResetDate();
                break;
            case 7:
                return $this->getResetDefaultQuota();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['AbsenceStack'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['AbsenceStack'][$this->getPrimaryKey()] = true;
        $keys = AbsenceStackPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getId(),
            $keys[1] => $this->getUnit(),
            $keys[2] => $this->getDenomination(),
            $keys[3] => $this->getRecover(),
            $keys[4] => $this->getResetType(),
            $keys[5] => $this->getResetToStackId(),
            $keys[6] => $this->getResetDate(),
            $keys[7] => $this->getResetDefaultQuota(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->collAbsenceKinds) {
                $result['AbsenceKinds'] = $this->collAbsenceKinds->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collPersonnelStackss) {
                $result['PersonnelStackss'] = $this->collPersonnelStackss->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collStoredStacks) {
                $result['StoredStacks'] = $this->collStoredStacks->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AbsenceStackPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setId($value);
                break;
            case 1:
                $this->setUnit($value);
                break;
            case 2:
                $this->setDenomination($value);
                break;
            case 3:
                $this->setRecover($value);
                break;
            case 4:
                $this->setResetType($value);
                break;
            case 5:
                $this->setResetToStackId($value);
                break;
            case 6:
                $this->setResetDate($value);
                break;
            case 7:
                $this->setResetDefaultQuota($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = AbsenceStackPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setUnit($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setDenomination($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setRecover($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setResetType($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setResetToStackId($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setResetDate($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setResetDefaultQuota($arr[$keys[7]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(AbsenceStackPeer::DATABASE_NAME);

        if ($this->isColumnModified(AbsenceStackPeer::ID)) $criteria->add(AbsenceStackPeer::ID, $this->id);
        if ($this->isColumnModified(AbsenceStackPeer::UNIT)) $criteria->add(AbsenceStackPeer::UNIT, $this->unit);
        if ($this->isColumnModified(AbsenceStackPeer::DENOMINATION)) $criteria->add(AbsenceStackPeer::DENOMINATION, $this->denomination);
        if ($this->isColumnModified(AbsenceStackPeer::RECOVER)) $criteria->add(AbsenceStackPeer::RECOVER, $this->recover);
        if ($this->isColumnModified(AbsenceStackPeer::RESET_TYPE)) $criteria->add(AbsenceStackPeer::RESET_TYPE, $this->reset_type);
        if ($this->isColumnModified(AbsenceStackPeer::RESET_TO_STACK_ID)) $criteria->add(AbsenceStackPeer::RESET_TO_STACK_ID, $this->reset_to_stack_id);
        if ($this->isColumnModified(AbsenceStackPeer::RESET_DATE)) $criteria->add(AbsenceStackPeer::RESET_DATE, $this->reset_date);
        if ($this->isColumnModified(AbsenceStackPeer::RESET_DEFAULT_QUOTA)) $criteria->add(AbsenceStackPeer::RESET_DEFAULT_QUOTA, $this->reset_default_quota);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(AbsenceStackPeer::DATABASE_NAME);
        $criteria->add(AbsenceStackPeer::ID, $this->id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getId();
    }

    /**
     * Generic method to set the primary key (id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of AbsenceStack (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setUnit($this->getUnit());
        $copyObj->setDenomination($this->getDenomination());
        $copyObj->setRecover($this->getRecover());
        $copyObj->setResetType($this->getResetType());
        $copyObj->setResetToStackId($this->getResetToStackId());
        $copyObj->setResetDate($this->getResetDate());
        $copyObj->setResetDefaultQuota($this->getResetDefaultQuota());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getAbsenceKinds() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAbsenceKind($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getPersonnelStackss() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addPersonnelStacks($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getStoredStacks() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addStoredStack($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return AbsenceStack Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return AbsenceStackPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new AbsenceStackPeer();
        }

        return self::$peer;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('AbsenceKind' == $relationName) {
            $this->initAbsenceKinds();
        }
        if ('PersonnelStacks' == $relationName) {
            $this->initPersonnelStackss();
        }
        if ('StoredStack' == $relationName) {
            $this->initStoredStacks();
        }
    }

    /**
     * Clears out the collAbsenceKinds collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AbsenceStack The current object (for fluent API support)
     * @see        addAbsenceKinds()
     */
    public function clearAbsenceKinds()
    {
        $this->collAbsenceKinds = null; // important to set this to null since that means it is uninitialized
        $this->collAbsenceKindsPartial = null;

        return $this;
    }

    /**
     * reset is the collAbsenceKinds collection loaded partially
     *
     * @return void
     */
    public function resetPartialAbsenceKinds($v = true)
    {
        $this->collAbsenceKindsPartial = $v;
    }

    /**
     * Initializes the collAbsenceKinds collection.
     *
     * By default this just sets the collAbsenceKinds collection to an empty array (like clearcollAbsenceKinds());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAbsenceKinds($overrideExisting = true)
    {
        if (null !== $this->collAbsenceKinds && !$overrideExisting) {
            return;
        }
        $this->collAbsenceKinds = new PropelObjectCollection();
        $this->collAbsenceKinds->setModel('AbsenceKind');
    }

    /**
     * Gets an array of AbsenceKind objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AbsenceStack is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|AbsenceKind[] List of AbsenceKind objects
     * @throws PropelException
     */
    public function getAbsenceKinds($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAbsenceKindsPartial && !$this->isNew();
        if (null === $this->collAbsenceKinds || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAbsenceKinds) {
                // return empty collection
                $this->initAbsenceKinds();
            } else {
                $collAbsenceKinds = AbsenceKindQuery::create(null, $criteria)
                    ->filterByAbsenceKindAbsenceStack($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAbsenceKindsPartial && count($collAbsenceKinds)) {
                      $this->initAbsenceKinds(false);

                      foreach ($collAbsenceKinds as $obj) {
                        if (false == $this->collAbsenceKinds->contains($obj)) {
                          $this->collAbsenceKinds->append($obj);
                        }
                      }

                      $this->collAbsenceKindsPartial = true;
                    }

                    $collAbsenceKinds->getInternalIterator()->rewind();

                    return $collAbsenceKinds;
                }

                if ($partial && $this->collAbsenceKinds) {
                    foreach ($this->collAbsenceKinds as $obj) {
                        if ($obj->isNew()) {
                            $collAbsenceKinds[] = $obj;
                        }
                    }
                }

                $this->collAbsenceKinds = $collAbsenceKinds;
                $this->collAbsenceKindsPartial = false;
            }
        }

        return $this->collAbsenceKinds;
    }

    /**
     * Sets a collection of AbsenceKind objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $absenceKinds A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setAbsenceKinds(PropelCollection $absenceKinds, PropelPDO $con = null)
    {
        $absenceKindsToDelete = $this->getAbsenceKinds(new Criteria(), $con)->diff($absenceKinds);


        $this->absenceKindsScheduledForDeletion = $absenceKindsToDelete;

        foreach ($absenceKindsToDelete as $absenceKindRemoved) {
            $absenceKindRemoved->setAbsenceKindAbsenceStack(null);
        }

        $this->collAbsenceKinds = null;
        foreach ($absenceKinds as $absenceKind) {
            $this->addAbsenceKind($absenceKind);
        }

        $this->collAbsenceKinds = $absenceKinds;
        $this->collAbsenceKindsPartial = false;

        return $this;
    }

    /**
     * Returns the number of related AbsenceKind objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related AbsenceKind objects.
     * @throws PropelException
     */
    public function countAbsenceKinds(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAbsenceKindsPartial && !$this->isNew();
        if (null === $this->collAbsenceKinds || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAbsenceKinds) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAbsenceKinds());
            }
            $query = AbsenceKindQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAbsenceKindAbsenceStack($this)
                ->count($con);
        }

        return count($this->collAbsenceKinds);
    }

    /**
     * Method called to associate a AbsenceKind object to this object
     * through the AbsenceKind foreign key attribute.
     *
     * @param    AbsenceKind $l AbsenceKind
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function addAbsenceKind(AbsenceKind $l)
    {
        if ($this->collAbsenceKinds === null) {
            $this->initAbsenceKinds();
            $this->collAbsenceKindsPartial = true;
        }

        if (!in_array($l, $this->collAbsenceKinds->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAbsenceKind($l);

            if ($this->absenceKindsScheduledForDeletion and $this->absenceKindsScheduledForDeletion->contains($l)) {
                $this->absenceKindsScheduledForDeletion->remove($this->absenceKindsScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	AbsenceKind $absenceKind The absenceKind object to add.
     */
    protected function doAddAbsenceKind($absenceKind)
    {
        $this->collAbsenceKinds[]= $absenceKind;
        $absenceKind->setAbsenceKindAbsenceStack($this);
    }

    /**
     * @param	AbsenceKind $absenceKind The absenceKind object to remove.
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function removeAbsenceKind($absenceKind)
    {
        if ($this->getAbsenceKinds()->contains($absenceKind)) {
            $this->collAbsenceKinds->remove($this->collAbsenceKinds->search($absenceKind));
            if (null === $this->absenceKindsScheduledForDeletion) {
                $this->absenceKindsScheduledForDeletion = clone $this->collAbsenceKinds;
                $this->absenceKindsScheduledForDeletion->clear();
            }
            $this->absenceKindsScheduledForDeletion[]= $absenceKind;
            $absenceKind->setAbsenceKindAbsenceStack(null);
        }

        return $this;
    }

    /**
     * Clears out the collPersonnelStackss collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AbsenceStack The current object (for fluent API support)
     * @see        addPersonnelStackss()
     */
    public function clearPersonnelStackss()
    {
        $this->collPersonnelStackss = null; // important to set this to null since that means it is uninitialized
        $this->collPersonnelStackssPartial = null;

        return $this;
    }

    /**
     * reset is the collPersonnelStackss collection loaded partially
     *
     * @return void
     */
    public function resetPartialPersonnelStackss($v = true)
    {
        $this->collPersonnelStackssPartial = $v;
    }

    /**
     * Initializes the collPersonnelStackss collection.
     *
     * By default this just sets the collPersonnelStackss collection to an empty array (like clearcollPersonnelStackss());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initPersonnelStackss($overrideExisting = true)
    {
        if (null !== $this->collPersonnelStackss && !$overrideExisting) {
            return;
        }
        $this->collPersonnelStackss = new PropelObjectCollection();
        $this->collPersonnelStackss->setModel('PersonnelStacks');
    }

    /**
     * Gets an array of PersonnelStacks objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AbsenceStack is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|PersonnelStacks[] List of PersonnelStacks objects
     * @throws PropelException
     */
    public function getPersonnelStackss($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collPersonnelStackssPartial && !$this->isNew();
        if (null === $this->collPersonnelStackss || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collPersonnelStackss) {
                // return empty collection
                $this->initPersonnelStackss();
            } else {
                $collPersonnelStackss = PersonnelStacksQuery::create(null, $criteria)
                    ->filterByPersonnelStacksAbsenceStack($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collPersonnelStackssPartial && count($collPersonnelStackss)) {
                      $this->initPersonnelStackss(false);

                      foreach ($collPersonnelStackss as $obj) {
                        if (false == $this->collPersonnelStackss->contains($obj)) {
                          $this->collPersonnelStackss->append($obj);
                        }
                      }

                      $this->collPersonnelStackssPartial = true;
                    }

                    $collPersonnelStackss->getInternalIterator()->rewind();

                    return $collPersonnelStackss;
                }

                if ($partial && $this->collPersonnelStackss) {
                    foreach ($this->collPersonnelStackss as $obj) {
                        if ($obj->isNew()) {
                            $collPersonnelStackss[] = $obj;
                        }
                    }
                }

                $this->collPersonnelStackss = $collPersonnelStackss;
                $this->collPersonnelStackssPartial = false;
            }
        }

        return $this->collPersonnelStackss;
    }

    /**
     * Sets a collection of PersonnelStacks objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $personnelStackss A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setPersonnelStackss(PropelCollection $personnelStackss, PropelPDO $con = null)
    {
        $personnelStackssToDelete = $this->getPersonnelStackss(new Criteria(), $con)->diff($personnelStackss);


        $this->personnelStackssScheduledForDeletion = $personnelStackssToDelete;

        foreach ($personnelStackssToDelete as $personnelStacksRemoved) {
            $personnelStacksRemoved->setPersonnelStacksAbsenceStack(null);
        }

        $this->collPersonnelStackss = null;
        foreach ($personnelStackss as $personnelStacks) {
            $this->addPersonnelStacks($personnelStacks);
        }

        $this->collPersonnelStackss = $personnelStackss;
        $this->collPersonnelStackssPartial = false;

        return $this;
    }

    /**
     * Returns the number of related PersonnelStacks objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related PersonnelStacks objects.
     * @throws PropelException
     */
    public function countPersonnelStackss(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collPersonnelStackssPartial && !$this->isNew();
        if (null === $this->collPersonnelStackss || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collPersonnelStackss) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getPersonnelStackss());
            }
            $query = PersonnelStacksQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByPersonnelStacksAbsenceStack($this)
                ->count($con);
        }

        return count($this->collPersonnelStackss);
    }

    /**
     * Method called to associate a PersonnelStacks object to this object
     * through the PersonnelStacks foreign key attribute.
     *
     * @param    PersonnelStacks $l PersonnelStacks
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function addPersonnelStacks(PersonnelStacks $l)
    {
        if ($this->collPersonnelStackss === null) {
            $this->initPersonnelStackss();
            $this->collPersonnelStackssPartial = true;
        }

        if (!in_array($l, $this->collPersonnelStackss->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddPersonnelStacks($l);

            if ($this->personnelStackssScheduledForDeletion and $this->personnelStackssScheduledForDeletion->contains($l)) {
                $this->personnelStackssScheduledForDeletion->remove($this->personnelStackssScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	PersonnelStacks $personnelStacks The personnelStacks object to add.
     */
    protected function doAddPersonnelStacks($personnelStacks)
    {
        $this->collPersonnelStackss[]= $personnelStacks;
        $personnelStacks->setPersonnelStacksAbsenceStack($this);
    }

    /**
     * @param	PersonnelStacks $personnelStacks The personnelStacks object to remove.
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function removePersonnelStacks($personnelStacks)
    {
        if ($this->getPersonnelStackss()->contains($personnelStacks)) {
            $this->collPersonnelStackss->remove($this->collPersonnelStackss->search($personnelStacks));
            if (null === $this->personnelStackssScheduledForDeletion) {
                $this->personnelStackssScheduledForDeletion = clone $this->collPersonnelStackss;
                $this->personnelStackssScheduledForDeletion->clear();
            }
            $this->personnelStackssScheduledForDeletion[]= clone $personnelStacks;
            $personnelStacks->setPersonnelStacksAbsenceStack(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this AbsenceStack is new, it will return
     * an empty collection; or if this AbsenceStack has previously
     * been saved, it will retrieve related PersonnelStackss from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in AbsenceStack.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|PersonnelStacks[] List of PersonnelStacks objects
     */
    public function getPersonnelStackssJoinPersonnelStacksEmployee($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = PersonnelStacksQuery::create(null, $criteria);
        $query->joinWith('PersonnelStacksEmployee', $join_behavior);

        return $this->getPersonnelStackss($query, $con);
    }

    /**
     * Clears out the collStoredStacks collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AbsenceStack The current object (for fluent API support)
     * @see        addStoredStacks()
     */
    public function clearStoredStacks()
    {
        $this->collStoredStacks = null; // important to set this to null since that means it is uninitialized
        $this->collStoredStacksPartial = null;

        return $this;
    }

    /**
     * reset is the collStoredStacks collection loaded partially
     *
     * @return void
     */
    public function resetPartialStoredStacks($v = true)
    {
        $this->collStoredStacksPartial = $v;
    }

    /**
     * Initializes the collStoredStacks collection.
     *
     * By default this just sets the collStoredStacks collection to an empty array (like clearcollStoredStacks());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initStoredStacks($overrideExisting = true)
    {
        if (null !== $this->collStoredStacks && !$overrideExisting) {
            return;
        }
        $this->collStoredStacks = new PropelObjectCollection();
        $this->collStoredStacks->setModel('StoredStack');
    }

    /**
     * Gets an array of StoredStack objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AbsenceStack is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|StoredStack[] List of StoredStack objects
     * @throws PropelException
     */
    public function getStoredStacks($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collStoredStacksPartial && !$this->isNew();
        if (null === $this->collStoredStacks || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collStoredStacks) {
                // return empty collection
                $this->initStoredStacks();
            } else {
                $collStoredStacks = StoredStackQuery::create(null, $criteria)
                    ->filterByStoredStackAbsenceStack($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collStoredStacksPartial && count($collStoredStacks)) {
                      $this->initStoredStacks(false);

                      foreach ($collStoredStacks as $obj) {
                        if (false == $this->collStoredStacks->contains($obj)) {
                          $this->collStoredStacks->append($obj);
                        }
                      }

                      $this->collStoredStacksPartial = true;
                    }

                    $collStoredStacks->getInternalIterator()->rewind();

                    return $collStoredStacks;
                }

                if ($partial && $this->collStoredStacks) {
                    foreach ($this->collStoredStacks as $obj) {
                        if ($obj->isNew()) {
                            $collStoredStacks[] = $obj;
                        }
                    }
                }

                $this->collStoredStacks = $collStoredStacks;
                $this->collStoredStacksPartial = false;
            }
        }

        return $this->collStoredStacks;
    }

    /**
     * Sets a collection of StoredStack objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $storedStacks A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function setStoredStacks(PropelCollection $storedStacks, PropelPDO $con = null)
    {
        $storedStacksToDelete = $this->getStoredStacks(new Criteria(), $con)->diff($storedStacks);


        $this->storedStacksScheduledForDeletion = $storedStacksToDelete;

        foreach ($storedStacksToDelete as $storedStackRemoved) {
            $storedStackRemoved->setStoredStackAbsenceStack(null);
        }

        $this->collStoredStacks = null;
        foreach ($storedStacks as $storedStack) {
            $this->addStoredStack($storedStack);
        }

        $this->collStoredStacks = $storedStacks;
        $this->collStoredStacksPartial = false;

        return $this;
    }

    /**
     * Returns the number of related StoredStack objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related StoredStack objects.
     * @throws PropelException
     */
    public function countStoredStacks(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collStoredStacksPartial && !$this->isNew();
        if (null === $this->collStoredStacks || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collStoredStacks) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getStoredStacks());
            }
            $query = StoredStackQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByStoredStackAbsenceStack($this)
                ->count($con);
        }

        return count($this->collStoredStacks);
    }

    /**
     * Method called to associate a StoredStack object to this object
     * through the StoredStack foreign key attribute.
     *
     * @param    StoredStack $l StoredStack
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function addStoredStack(StoredStack $l)
    {
        if ($this->collStoredStacks === null) {
            $this->initStoredStacks();
            $this->collStoredStacksPartial = true;
        }

        if (!in_array($l, $this->collStoredStacks->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddStoredStack($l);

            if ($this->storedStacksScheduledForDeletion and $this->storedStacksScheduledForDeletion->contains($l)) {
                $this->storedStacksScheduledForDeletion->remove($this->storedStacksScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	StoredStack $storedStack The storedStack object to add.
     */
    protected function doAddStoredStack($storedStack)
    {
        $this->collStoredStacks[]= $storedStack;
        $storedStack->setStoredStackAbsenceStack($this);
    }

    /**
     * @param	StoredStack $storedStack The storedStack object to remove.
     * @return AbsenceStack The current object (for fluent API support)
     */
    public function removeStoredStack($storedStack)
    {
        if ($this->getStoredStacks()->contains($storedStack)) {
            $this->collStoredStacks->remove($this->collStoredStacks->search($storedStack));
            if (null === $this->storedStacksScheduledForDeletion) {
                $this->storedStacksScheduledForDeletion = clone $this->collStoredStacks;
                $this->storedStacksScheduledForDeletion->clear();
            }
            $this->storedStacksScheduledForDeletion[]= $storedStack;
            $storedStack->setStoredStackAbsenceStack(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this AbsenceStack is new, it will return
     * an empty collection; or if this AbsenceStack has previously
     * been saved, it will retrieve related StoredStacks from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in AbsenceStack.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|StoredStack[] List of StoredStack objects
     */
    public function getStoredStacksJoinStoredStackStoredMonth($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = StoredStackQuery::create(null, $criteria);
        $query->joinWith('StoredStackStoredMonth', $join_behavior);

        return $this->getStoredStacks($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id = null;
        $this->unit = null;
        $this->denomination = null;
        $this->recover = null;
        $this->reset_type = null;
        $this->reset_to_stack_id = null;
        $this->reset_date = null;
        $this->reset_default_quota = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collAbsenceKinds) {
                foreach ($this->collAbsenceKinds as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collPersonnelStackss) {
                foreach ($this->collPersonnelStackss as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collStoredStacks) {
                foreach ($this->collStoredStacks as $o) {
                    $o->clearAllReferences($deep);
                }
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collAbsenceKinds instanceof PropelCollection) {
            $this->collAbsenceKinds->clearIterator();
        }
        $this->collAbsenceKinds = null;
        if ($this->collPersonnelStackss instanceof PropelCollection) {
            $this->collPersonnelStackss->clearIterator();
        }
        $this->collPersonnelStackss = null;
        if ($this->collStoredStacks instanceof PropelCollection) {
            $this->collStoredStacks->clearIterator();
        }
        $this->collStoredStacks = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(AbsenceStackPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
