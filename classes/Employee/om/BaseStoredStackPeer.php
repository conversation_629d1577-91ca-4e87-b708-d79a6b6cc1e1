<?php

namespace Employee\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Employee\AbsenceStackPeer;
use Employee\StoredMonthPeer;
use Employee\StoredStack;
use Employee\StoredStackPeer;
use Employee\map\StoredStackTableMap;

/**
 * Base static class for performing query and update operations on the 'storage_personnel_stack' table.
 *
 *
 *
 * @package propel.generator.Employee.om
 */
abstract class BaseStoredStackPeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'storage_personnel_stack';

    /** the related Propel class for this table */
    const OM_CLASS = 'Employee\\StoredStack';

    /** the related TableMap class for this table */
    const TM_CLASS = 'StoredStackTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 11;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 11;

    /** the column name for the id field */
    const ID = 'storage_personnel_stack.id';

    /** the column name for the storage_personnel_presences field */
    const STORAGE_PERSONNEL_PRESENCES = 'storage_personnel_stack.storage_personnel_presences';

    /** the column name for the absence_stack field */
    const ABSENCE_STACK = 'storage_personnel_stack.absence_stack';

    /** the column name for the stack_denomination field */
    const STACK_DENOMINATION = 'storage_personnel_stack.stack_denomination';

    /** the column name for the value_start_o field */
    const VALUE_START_O = 'storage_personnel_stack.value_start_o';

    /** the column name for the value_end_o field */
    const VALUE_END_O = 'storage_personnel_stack.value_end_o';

    /** the column name for the value_start field */
    const VALUE_START = 'storage_personnel_stack.value_start';

    /** the column name for the value_end field */
    const VALUE_END = 'storage_personnel_stack.value_end';

    /** the column name for the unit field */
    const UNIT = 'storage_personnel_stack.unit';

    /** the column name for the recover field */
    const RECOVER = 'storage_personnel_stack.recover';

    /** the column name for the reset_type_applied field */
    const RESET_TYPE_APPLIED = 'storage_personnel_stack.reset_type_applied';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of StoredStack objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array StoredStack[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. StoredStackPeer::$fieldNames[StoredStackPeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('StoredStackId', 'StoredMonth', 'AbsenceStack', 'StackDenomination', 'ValueStartOriginal', 'ValueEndOriginal', 'ValueStart', 'ValueEnd', 'Unit', 'Recover', 'ResetTypeApplied', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('storedStackId', 'storedMonth', 'absenceStack', 'stackDenomination', 'valueStartOriginal', 'valueEndOriginal', 'valueStart', 'valueEnd', 'unit', 'recover', 'resetTypeApplied', ),
        BasePeer::TYPE_COLNAME => array (StoredStackPeer::ID, StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredStackPeer::ABSENCE_STACK, StoredStackPeer::STACK_DENOMINATION, StoredStackPeer::VALUE_START_O, StoredStackPeer::VALUE_END_O, StoredStackPeer::VALUE_START, StoredStackPeer::VALUE_END, StoredStackPeer::UNIT, StoredStackPeer::RECOVER, StoredStackPeer::RESET_TYPE_APPLIED, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID', 'STORAGE_PERSONNEL_PRESENCES', 'ABSENCE_STACK', 'STACK_DENOMINATION', 'VALUE_START_O', 'VALUE_END_O', 'VALUE_START', 'VALUE_END', 'UNIT', 'RECOVER', 'RESET_TYPE_APPLIED', ),
        BasePeer::TYPE_FIELDNAME => array ('id', 'storage_personnel_presences', 'absence_stack', 'stack_denomination', 'value_start_o', 'value_end_o', 'value_start', 'value_end', 'unit', 'recover', 'reset_type_applied', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. StoredStackPeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('StoredStackId' => 0, 'StoredMonth' => 1, 'AbsenceStack' => 2, 'StackDenomination' => 3, 'ValueStartOriginal' => 4, 'ValueEndOriginal' => 5, 'ValueStart' => 6, 'ValueEnd' => 7, 'Unit' => 8, 'Recover' => 9, 'ResetTypeApplied' => 10, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('storedStackId' => 0, 'storedMonth' => 1, 'absenceStack' => 2, 'stackDenomination' => 3, 'valueStartOriginal' => 4, 'valueEndOriginal' => 5, 'valueStart' => 6, 'valueEnd' => 7, 'unit' => 8, 'recover' => 9, 'resetTypeApplied' => 10, ),
        BasePeer::TYPE_COLNAME => array (StoredStackPeer::ID => 0, StoredStackPeer::STORAGE_PERSONNEL_PRESENCES => 1, StoredStackPeer::ABSENCE_STACK => 2, StoredStackPeer::STACK_DENOMINATION => 3, StoredStackPeer::VALUE_START_O => 4, StoredStackPeer::VALUE_END_O => 5, StoredStackPeer::VALUE_START => 6, StoredStackPeer::VALUE_END => 7, StoredStackPeer::UNIT => 8, StoredStackPeer::RECOVER => 9, StoredStackPeer::RESET_TYPE_APPLIED => 10, ),
        BasePeer::TYPE_RAW_COLNAME => array ('ID' => 0, 'STORAGE_PERSONNEL_PRESENCES' => 1, 'ABSENCE_STACK' => 2, 'STACK_DENOMINATION' => 3, 'VALUE_START_O' => 4, 'VALUE_END_O' => 5, 'VALUE_START' => 6, 'VALUE_END' => 7, 'UNIT' => 8, 'RECOVER' => 9, 'RESET_TYPE_APPLIED' => 10, ),
        BasePeer::TYPE_FIELDNAME => array ('id' => 0, 'storage_personnel_presences' => 1, 'absence_stack' => 2, 'stack_denomination' => 3, 'value_start_o' => 4, 'value_end_o' => 5, 'value_start' => 6, 'value_end' => 7, 'unit' => 8, 'recover' => 9, 'reset_type_applied' => 10, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = StoredStackPeer::getFieldNames($toType);
        $key = isset(StoredStackPeer::$fieldKeys[$fromType][$name]) ? StoredStackPeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(StoredStackPeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, StoredStackPeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return StoredStackPeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. StoredStackPeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(StoredStackPeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(StoredStackPeer::ID);
            $criteria->addSelectColumn(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES);
            $criteria->addSelectColumn(StoredStackPeer::ABSENCE_STACK);
            $criteria->addSelectColumn(StoredStackPeer::STACK_DENOMINATION);
            $criteria->addSelectColumn(StoredStackPeer::VALUE_START_O);
            $criteria->addSelectColumn(StoredStackPeer::VALUE_END_O);
            $criteria->addSelectColumn(StoredStackPeer::VALUE_START);
            $criteria->addSelectColumn(StoredStackPeer::VALUE_END);
            $criteria->addSelectColumn(StoredStackPeer::UNIT);
            $criteria->addSelectColumn(StoredStackPeer::RECOVER);
            $criteria->addSelectColumn(StoredStackPeer::RESET_TYPE_APPLIED);
        } else {
            $criteria->addSelectColumn($alias . '.id');
            $criteria->addSelectColumn($alias . '.storage_personnel_presences');
            $criteria->addSelectColumn($alias . '.absence_stack');
            $criteria->addSelectColumn($alias . '.stack_denomination');
            $criteria->addSelectColumn($alias . '.value_start_o');
            $criteria->addSelectColumn($alias . '.value_end_o');
            $criteria->addSelectColumn($alias . '.value_start');
            $criteria->addSelectColumn($alias . '.value_end');
            $criteria->addSelectColumn($alias . '.unit');
            $criteria->addSelectColumn($alias . '.recover');
            $criteria->addSelectColumn($alias . '.reset_type_applied');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredStackPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return StoredStack
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = StoredStackPeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return StoredStackPeer::populateObjects(StoredStackPeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            StoredStackPeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param StoredStack $obj A StoredStack object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getStoredStackId();
            } // if key === null
            StoredStackPeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A StoredStack object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof StoredStack) {
                $key = (string) $value->getStoredStackId();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or StoredStack object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(StoredStackPeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return StoredStack Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(StoredStackPeer::$instances[$key])) {
                return StoredStackPeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (StoredStackPeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        StoredStackPeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to storage_personnel_stack
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = StoredStackPeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = StoredStackPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = StoredStackPeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                StoredStackPeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (StoredStack object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = StoredStackPeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = StoredStackPeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + StoredStackPeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = StoredStackPeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            StoredStackPeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }


    /**
     * Returns the number of rows matching criteria, joining the related StoredStackAbsenceStack table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinStoredStackAbsenceStack(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredStackPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredStackPeer::ABSENCE_STACK, AbsenceStackPeer::ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related StoredStackStoredMonth table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinStoredStackStoredMonth(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredStackPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of StoredStack objects pre-filled with their AbsenceStack objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredStack objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinStoredStackAbsenceStack(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredStackPeer::DATABASE_NAME);
        }

        StoredStackPeer::addSelectColumns($criteria);
        $startcol = StoredStackPeer::NUM_HYDRATE_COLUMNS;
        AbsenceStackPeer::addSelectColumns($criteria);

        $criteria->addJoin(StoredStackPeer::ABSENCE_STACK, AbsenceStackPeer::ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredStackPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredStackPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = StoredStackPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredStackPeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = AbsenceStackPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = AbsenceStackPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = AbsenceStackPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    AbsenceStackPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (StoredStack) to $obj2 (AbsenceStack)
                $obj2->addStoredStack($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of StoredStack objects pre-filled with their StoredMonth objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredStack objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinStoredStackStoredMonth(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredStackPeer::DATABASE_NAME);
        }

        StoredStackPeer::addSelectColumns($criteria);
        $startcol = StoredStackPeer::NUM_HYDRATE_COLUMNS;
        StoredMonthPeer::addSelectColumns($criteria);

        $criteria->addJoin(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredStackPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredStackPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = StoredStackPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredStackPeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = StoredMonthPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = StoredMonthPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = StoredMonthPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    StoredMonthPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (StoredStack) to $obj2 (StoredMonth)
                $obj2->addStoredStack($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining all related tables
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAll(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredStackPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredStackPeer::ABSENCE_STACK, AbsenceStackPeer::ID, $join_behavior);

        $criteria->addJoin(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }

    /**
     * Selects a collection of StoredStack objects pre-filled with all related objects.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredStack objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAll(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredStackPeer::DATABASE_NAME);
        }

        StoredStackPeer::addSelectColumns($criteria);
        $startcol2 = StoredStackPeer::NUM_HYDRATE_COLUMNS;

        AbsenceStackPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + AbsenceStackPeer::NUM_HYDRATE_COLUMNS;

        StoredMonthPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + StoredMonthPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(StoredStackPeer::ABSENCE_STACK, AbsenceStackPeer::ID, $join_behavior);

        $criteria->addJoin(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredStackPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredStackPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = StoredStackPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredStackPeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            // Add objects for joined AbsenceStack rows

            $key2 = AbsenceStackPeer::getPrimaryKeyHashFromRow($row, $startcol2);
            if ($key2 !== null) {
                $obj2 = AbsenceStackPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = AbsenceStackPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    AbsenceStackPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 loaded

                // Add the $obj1 (StoredStack) to the collection in $obj2 (AbsenceStack)
                $obj2->addStoredStack($obj1);
            } // if joined row not null

            // Add objects for joined StoredMonth rows

            $key3 = StoredMonthPeer::getPrimaryKeyHashFromRow($row, $startcol3);
            if ($key3 !== null) {
                $obj3 = StoredMonthPeer::getInstanceFromPool($key3);
                if (!$obj3) {

                    $cls = StoredMonthPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    StoredMonthPeer::addInstanceToPool($obj3, $key3);
                } // if obj3 loaded

                // Add the $obj1 (StoredStack) to the collection in $obj3 (StoredMonth)
                $obj3->addStoredStack($obj1);
            } // if joined row not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining the related StoredStackAbsenceStack table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptStoredStackAbsenceStack(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredStackPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related StoredStackStoredMonth table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptStoredStackStoredMonth(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredStackPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredStackPeer::ABSENCE_STACK, AbsenceStackPeer::ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of StoredStack objects pre-filled with all related objects except StoredStackAbsenceStack.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredStack objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptStoredStackAbsenceStack(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredStackPeer::DATABASE_NAME);
        }

        StoredStackPeer::addSelectColumns($criteria);
        $startcol2 = StoredStackPeer::NUM_HYDRATE_COLUMNS;

        StoredMonthPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + StoredMonthPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(StoredStackPeer::STORAGE_PERSONNEL_PRESENCES, StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredStackPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredStackPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = StoredStackPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredStackPeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined StoredMonth rows

                $key2 = StoredMonthPeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = StoredMonthPeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = StoredMonthPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    StoredMonthPeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (StoredStack) to the collection in $obj2 (StoredMonth)
                $obj2->addStoredStack($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of StoredStack objects pre-filled with all related objects except StoredStackStoredMonth.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredStack objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptStoredStackStoredMonth(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredStackPeer::DATABASE_NAME);
        }

        StoredStackPeer::addSelectColumns($criteria);
        $startcol2 = StoredStackPeer::NUM_HYDRATE_COLUMNS;

        AbsenceStackPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + AbsenceStackPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(StoredStackPeer::ABSENCE_STACK, AbsenceStackPeer::ID, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredStackPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredStackPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = StoredStackPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredStackPeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined AbsenceStack rows

                $key2 = AbsenceStackPeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = AbsenceStackPeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = AbsenceStackPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    AbsenceStackPeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (StoredStack) to the collection in $obj2 (AbsenceStack)
                $obj2->addStoredStack($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(StoredStackPeer::DATABASE_NAME)->getTable(StoredStackPeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseStoredStackPeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseStoredStackPeer::TABLE_NAME)) {
        $dbMap->addTableObject(new StoredStackTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return StoredStackPeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a StoredStack or Criteria object.
     *
     * @param      mixed $values Criteria or StoredStack object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from StoredStack object
        }

        if ($criteria->containsKey(StoredStackPeer::ID) && $criteria->keyContainsValue(StoredStackPeer::ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.StoredStackPeer::ID.')');
        }


        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a StoredStack or Criteria object.
     *
     * @param      mixed $values Criteria or StoredStack object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(StoredStackPeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(StoredStackPeer::ID);
            $value = $criteria->remove(StoredStackPeer::ID);
            if ($value) {
                $selectCriteria->add(StoredStackPeer::ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(StoredStackPeer::TABLE_NAME);
            }

        } else { // $values is StoredStack object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the storage_personnel_stack table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(StoredStackPeer::TABLE_NAME, $con, StoredStackPeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            StoredStackPeer::clearInstancePool();
            StoredStackPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a StoredStack or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or StoredStack object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            StoredStackPeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof StoredStack) { // it's a model object
            // invalidate the cache for this single object
            StoredStackPeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(StoredStackPeer::DATABASE_NAME);
            $criteria->add(StoredStackPeer::ID, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                StoredStackPeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(StoredStackPeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            StoredStackPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given StoredStack object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param StoredStack $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(StoredStackPeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(StoredStackPeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(StoredStackPeer::DATABASE_NAME, StoredStackPeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return StoredStack
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = StoredStackPeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(StoredStackPeer::DATABASE_NAME);
        $criteria->add(StoredStackPeer::ID, $pk);

        $v = StoredStackPeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return StoredStack[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredStackPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(StoredStackPeer::DATABASE_NAME);
            $criteria->add(StoredStackPeer::ID, $pks, Criteria::IN);
            $objs = StoredStackPeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseStoredStackPeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseStoredStackPeer::buildTableMap();

