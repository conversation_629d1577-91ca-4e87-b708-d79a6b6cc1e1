<?php

namespace Employee\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Employee\EmployeePeer;
use Employee\StoredMonth;
use Employee\StoredMonthPeer;
use Employee\StoredStackPeer;
use Employee\map\StoredMonthTableMap;

/**
 * Base static class for performing query and update operations on the 'storage_personnel_presences' table.
 *
 *
 *
 * @package propel.generator.Employee.om
 */
abstract class BaseStoredMonthPeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'storage_personnel_presences';

    /** the related Propel class for this table */
    const OM_CLASS = 'Employee\\StoredMonth';

    /** the related TableMap class for this table */
    const TM_CLASS = 'StoredMonthTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 9;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 9;

    /** the column name for the storage_personnel_presences_id field */
    const STORAGE_PERSONNEL_PRESENCES_ID = 'storage_personnel_presences.storage_personnel_presences_id';

    /** the column name for the employee_id field */
    const EMPLOYEE_ID = 'storage_personnel_presences.employee_id';

    /** the column name for the date_start field */
    const DATE_START = 'storage_personnel_presences.date_start';

    /** the column name for the date_end field */
    const DATE_END = 'storage_personnel_presences.date_end';

    /** the column name for the ext_start_o field */
    const EXT_START_O = 'storage_personnel_presences.ext_start_o';

    /** the column name for the ext_end_o field */
    const EXT_END_O = 'storage_personnel_presences.ext_end_o';

    /** the column name for the ext_start field */
    const EXT_START = 'storage_personnel_presences.ext_start';

    /** the column name for the ext_end field */
    const EXT_END = 'storage_personnel_presences.ext_end';

    /** the column name for the note field */
    const NOTE = 'storage_personnel_presences.note';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of StoredMonth objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array StoredMonth[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. StoredMonthPeer::$fieldNames[StoredMonthPeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('StoredMonthId', 'EmployeeId', 'DateStart', 'DateEnd', 'ExtStartOriginal', 'ExtEndOriginal', 'ExtStart', 'ExtEnd', 'Note', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('storedMonthId', 'employeeId', 'dateStart', 'dateEnd', 'extStartOriginal', 'extEndOriginal', 'extStart', 'extEnd', 'note', ),
        BasePeer::TYPE_COLNAME => array (StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, StoredMonthPeer::EMPLOYEE_ID, StoredMonthPeer::DATE_START, StoredMonthPeer::DATE_END, StoredMonthPeer::EXT_START_O, StoredMonthPeer::EXT_END_O, StoredMonthPeer::EXT_START, StoredMonthPeer::EXT_END, StoredMonthPeer::NOTE, ),
        BasePeer::TYPE_RAW_COLNAME => array ('STORAGE_PERSONNEL_PRESENCES_ID', 'EMPLOYEE_ID', 'DATE_START', 'DATE_END', 'EXT_START_O', 'EXT_END_O', 'EXT_START', 'EXT_END', 'NOTE', ),
        BasePeer::TYPE_FIELDNAME => array ('storage_personnel_presences_id', 'employee_id', 'date_start', 'date_end', 'ext_start_o', 'ext_end_o', 'ext_start', 'ext_end', 'note', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. StoredMonthPeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('StoredMonthId' => 0, 'EmployeeId' => 1, 'DateStart' => 2, 'DateEnd' => 3, 'ExtStartOriginal' => 4, 'ExtEndOriginal' => 5, 'ExtStart' => 6, 'ExtEnd' => 7, 'Note' => 8, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('storedMonthId' => 0, 'employeeId' => 1, 'dateStart' => 2, 'dateEnd' => 3, 'extStartOriginal' => 4, 'extEndOriginal' => 5, 'extStart' => 6, 'extEnd' => 7, 'note' => 8, ),
        BasePeer::TYPE_COLNAME => array (StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID => 0, StoredMonthPeer::EMPLOYEE_ID => 1, StoredMonthPeer::DATE_START => 2, StoredMonthPeer::DATE_END => 3, StoredMonthPeer::EXT_START_O => 4, StoredMonthPeer::EXT_END_O => 5, StoredMonthPeer::EXT_START => 6, StoredMonthPeer::EXT_END => 7, StoredMonthPeer::NOTE => 8, ),
        BasePeer::TYPE_RAW_COLNAME => array ('STORAGE_PERSONNEL_PRESENCES_ID' => 0, 'EMPLOYEE_ID' => 1, 'DATE_START' => 2, 'DATE_END' => 3, 'EXT_START_O' => 4, 'EXT_END_O' => 5, 'EXT_START' => 6, 'EXT_END' => 7, 'NOTE' => 8, ),
        BasePeer::TYPE_FIELDNAME => array ('storage_personnel_presences_id' => 0, 'employee_id' => 1, 'date_start' => 2, 'date_end' => 3, 'ext_start_o' => 4, 'ext_end_o' => 5, 'ext_start' => 6, 'ext_end' => 7, 'note' => 8, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = StoredMonthPeer::getFieldNames($toType);
        $key = isset(StoredMonthPeer::$fieldKeys[$fromType][$name]) ? StoredMonthPeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(StoredMonthPeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, StoredMonthPeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return StoredMonthPeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. StoredMonthPeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(StoredMonthPeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID);
            $criteria->addSelectColumn(StoredMonthPeer::EMPLOYEE_ID);
            $criteria->addSelectColumn(StoredMonthPeer::DATE_START);
            $criteria->addSelectColumn(StoredMonthPeer::DATE_END);
            $criteria->addSelectColumn(StoredMonthPeer::EXT_START_O);
            $criteria->addSelectColumn(StoredMonthPeer::EXT_END_O);
            $criteria->addSelectColumn(StoredMonthPeer::EXT_START);
            $criteria->addSelectColumn(StoredMonthPeer::EXT_END);
            $criteria->addSelectColumn(StoredMonthPeer::NOTE);
        } else {
            $criteria->addSelectColumn($alias . '.storage_personnel_presences_id');
            $criteria->addSelectColumn($alias . '.employee_id');
            $criteria->addSelectColumn($alias . '.date_start');
            $criteria->addSelectColumn($alias . '.date_end');
            $criteria->addSelectColumn($alias . '.ext_start_o');
            $criteria->addSelectColumn($alias . '.ext_end_o');
            $criteria->addSelectColumn($alias . '.ext_start');
            $criteria->addSelectColumn($alias . '.ext_end');
            $criteria->addSelectColumn($alias . '.note');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredMonthPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredMonthPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return StoredMonth
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = StoredMonthPeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return StoredMonthPeer::populateObjects(StoredMonthPeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            StoredMonthPeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param StoredMonth $obj A StoredMonth object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getStoredMonthId();
            } // if key === null
            StoredMonthPeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A StoredMonth object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof StoredMonth) {
                $key = (string) $value->getStoredMonthId();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or StoredMonth object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(StoredMonthPeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return StoredMonth Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(StoredMonthPeer::$instances[$key])) {
                return StoredMonthPeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (StoredMonthPeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        StoredMonthPeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to storage_personnel_presences
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
        // Invalidate objects in StoredStackPeer instance pool,
        // since one or more of them may be deleted by ON DELETE CASCADE/SETNULL rule.
        StoredStackPeer::clearInstancePool();
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = StoredMonthPeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = StoredMonthPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = StoredMonthPeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                StoredMonthPeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (StoredMonth object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = StoredMonthPeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = StoredMonthPeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + StoredMonthPeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = StoredMonthPeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            StoredMonthPeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }


    /**
     * Returns the number of rows matching criteria, joining the related StoredMonthEmployee table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinStoredMonthEmployee(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredMonthPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredMonthPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredMonthPeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of StoredMonth objects pre-filled with their Employee objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredMonth objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinStoredMonthEmployee(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);
        }

        StoredMonthPeer::addSelectColumns($criteria);
        $startcol = StoredMonthPeer::NUM_HYDRATE_COLUMNS;
        EmployeePeer::addSelectColumns($criteria);

        $criteria->addJoin(StoredMonthPeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredMonthPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredMonthPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = StoredMonthPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredMonthPeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = EmployeePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (StoredMonth) to $obj2 (Employee)
                $obj2->addStoredMonth($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining all related tables
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAll(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(StoredMonthPeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            StoredMonthPeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(StoredMonthPeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }

    /**
     * Selects a collection of StoredMonth objects pre-filled with all related objects.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of StoredMonth objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAll(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);
        }

        StoredMonthPeer::addSelectColumns($criteria);
        $startcol2 = StoredMonthPeer::NUM_HYDRATE_COLUMNS;

        EmployeePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + EmployeePeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(StoredMonthPeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = StoredMonthPeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = StoredMonthPeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = StoredMonthPeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                StoredMonthPeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            // Add objects for joined Employee rows

            $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol2);
            if ($key2 !== null) {
                $obj2 = EmployeePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 loaded

                // Add the $obj1 (StoredMonth) to the collection in $obj2 (Employee)
                $obj2->addStoredMonth($obj1);
            } // if joined row not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(StoredMonthPeer::DATABASE_NAME)->getTable(StoredMonthPeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BaseStoredMonthPeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BaseStoredMonthPeer::TABLE_NAME)) {
        $dbMap->addTableObject(new StoredMonthTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return StoredMonthPeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a StoredMonth or Criteria object.
     *
     * @param      mixed $values Criteria or StoredMonth object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from StoredMonth object
        }

        if ($criteria->containsKey(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID) && $criteria->keyContainsValue(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID.')');
        }


        // Set the correct dbName
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a StoredMonth or Criteria object.
     *
     * @param      mixed $values Criteria or StoredMonth object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(StoredMonthPeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID);
            $value = $criteria->remove(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID);
            if ($value) {
                $selectCriteria->add(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(StoredMonthPeer::TABLE_NAME);
            }

        } else { // $values is StoredMonth object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the storage_personnel_presences table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(StoredMonthPeer::TABLE_NAME, $con, StoredMonthPeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            StoredMonthPeer::clearInstancePool();
            StoredMonthPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a StoredMonth or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or StoredMonth object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            StoredMonthPeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof StoredMonth) { // it's a model object
            // invalidate the cache for this single object
            StoredMonthPeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(StoredMonthPeer::DATABASE_NAME);
            $criteria->add(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                StoredMonthPeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(StoredMonthPeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            StoredMonthPeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given StoredMonth object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param StoredMonth $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(StoredMonthPeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(StoredMonthPeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(StoredMonthPeer::DATABASE_NAME, StoredMonthPeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return StoredMonth
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = StoredMonthPeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(StoredMonthPeer::DATABASE_NAME);
        $criteria->add(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $pk);

        $v = StoredMonthPeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return StoredMonth[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(StoredMonthPeer::DATABASE_NAME);
            $criteria->add(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $pks, Criteria::IN);
            $objs = StoredMonthPeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BaseStoredMonthPeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BaseStoredMonthPeer::buildTableMap();

