<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceStack;
use Employee\Employee;
use Employee\PersonnelStacks;
use Employee\PersonnelStacksPeer;
use Employee\PersonnelStacksQuery;

/**
 * Base class that represents a query for the 'personnel_stacks' table.
 *
 *
 *
 * @method PersonnelStacksQuery orderById($order = Criteria::ASC) Order by the id column
 * @method PersonnelStacksQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method PersonnelStacksQuery orderByStackId($order = Criteria::ASC) Order by the stack_id column
 * @method PersonnelStacksQuery orderByResetQuota($order = Criteria::ASC) Order by the reset_quota column
 *
 * @method PersonnelStacksQuery groupById() Group by the id column
 * @method PersonnelStacksQuery groupByEmployeeId() Group by the employee_id column
 * @method PersonnelStacksQuery groupByStackId() Group by the stack_id column
 * @method PersonnelStacksQuery groupByResetQuota() Group by the reset_quota column
 *
 * @method PersonnelStacksQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method PersonnelStacksQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method PersonnelStacksQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method PersonnelStacksQuery leftJoinPersonnelStacksEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the PersonnelStacksEmployee relation
 * @method PersonnelStacksQuery rightJoinPersonnelStacksEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the PersonnelStacksEmployee relation
 * @method PersonnelStacksQuery innerJoinPersonnelStacksEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the PersonnelStacksEmployee relation
 *
 * @method PersonnelStacksQuery leftJoinPersonnelStacksAbsenceStack($relationAlias = null) Adds a LEFT JOIN clause to the query using the PersonnelStacksAbsenceStack relation
 * @method PersonnelStacksQuery rightJoinPersonnelStacksAbsenceStack($relationAlias = null) Adds a RIGHT JOIN clause to the query using the PersonnelStacksAbsenceStack relation
 * @method PersonnelStacksQuery innerJoinPersonnelStacksAbsenceStack($relationAlias = null) Adds a INNER JOIN clause to the query using the PersonnelStacksAbsenceStack relation
 *
 * @method PersonnelStacks findOne(PropelPDO $con = null) Return the first PersonnelStacks matching the query
 * @method PersonnelStacks findOneOrCreate(PropelPDO $con = null) Return the first PersonnelStacks matching the query, or a new PersonnelStacks object populated from the query conditions when no match is found
 *
 * @method PersonnelStacks findOneByEmployeeId(string $employee_id) Return the first PersonnelStacks filtered by the employee_id column
 * @method PersonnelStacks findOneByStackId(string $stack_id) Return the first PersonnelStacks filtered by the stack_id column
 * @method PersonnelStacks findOneByResetQuota(double $reset_quota) Return the first PersonnelStacks filtered by the reset_quota column
 *
 * @method array findById(int $id) Return PersonnelStacks objects filtered by the id column
 * @method array findByEmployeeId(string $employee_id) Return PersonnelStacks objects filtered by the employee_id column
 * @method array findByStackId(string $stack_id) Return PersonnelStacks objects filtered by the stack_id column
 * @method array findByResetQuota(double $reset_quota) Return PersonnelStacks objects filtered by the reset_quota column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BasePersonnelStacksQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BasePersonnelStacksQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\PersonnelStacks';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new PersonnelStacksQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   PersonnelStacksQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return PersonnelStacksQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof PersonnelStacksQuery) {
            return $criteria;
        }
        $query = new PersonnelStacksQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   PersonnelStacks|PersonnelStacks[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = PersonnelStacksPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(PersonnelStacksPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 PersonnelStacks A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 PersonnelStacks A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "employee_id", "stack_id", "reset_quota" FROM "personnel_stacks" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new PersonnelStacks();
            $obj->hydrate($row);
            PersonnelStacksPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return PersonnelStacks|PersonnelStacks[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|PersonnelStacks[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(PersonnelStacksPeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(PersonnelStacksPeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(PersonnelStacksPeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(PersonnelStacksPeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PersonnelStacksPeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @see       filterByPersonnelStacksEmployee()
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(PersonnelStacksPeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(PersonnelStacksPeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PersonnelStacksPeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the stack_id column
     *
     * Example usage:
     * <code>
     * $query->filterByStackId(1234); // WHERE stack_id = 1234
     * $query->filterByStackId(array(12, 34)); // WHERE stack_id IN (12, 34)
     * $query->filterByStackId(array('min' => 12)); // WHERE stack_id >= 12
     * $query->filterByStackId(array('max' => 12)); // WHERE stack_id <= 12
     * </code>
     *
     * @see       filterByPersonnelStacksAbsenceStack()
     *
     * @param     mixed $stackId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function filterByStackId($stackId = null, $comparison = null)
    {
        if (is_array($stackId)) {
            $useMinMax = false;
            if (isset($stackId['min'])) {
                $this->addUsingAlias(PersonnelStacksPeer::STACK_ID, $stackId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($stackId['max'])) {
                $this->addUsingAlias(PersonnelStacksPeer::STACK_ID, $stackId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PersonnelStacksPeer::STACK_ID, $stackId, $comparison);
    }

    /**
     * Filter the query on the reset_quota column
     *
     * Example usage:
     * <code>
     * $query->filterByResetQuota(1234); // WHERE reset_quota = 1234
     * $query->filterByResetQuota(array(12, 34)); // WHERE reset_quota IN (12, 34)
     * $query->filterByResetQuota(array('min' => 12)); // WHERE reset_quota >= 12
     * $query->filterByResetQuota(array('max' => 12)); // WHERE reset_quota <= 12
     * </code>
     *
     * @param     mixed $resetQuota The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function filterByResetQuota($resetQuota = null, $comparison = null)
    {
        if (is_array($resetQuota)) {
            $useMinMax = false;
            if (isset($resetQuota['min'])) {
                $this->addUsingAlias(PersonnelStacksPeer::RESET_QUOTA, $resetQuota['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($resetQuota['max'])) {
                $this->addUsingAlias(PersonnelStacksPeer::RESET_QUOTA, $resetQuota['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(PersonnelStacksPeer::RESET_QUOTA, $resetQuota, $comparison);
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 PersonnelStacksQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPersonnelStacksEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(PersonnelStacksPeer::EMPLOYEE_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(PersonnelStacksPeer::EMPLOYEE_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByPersonnelStacksEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the PersonnelStacksEmployee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function joinPersonnelStacksEmployee($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('PersonnelStacksEmployee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'PersonnelStacksEmployee');
        }

        return $this;
    }

    /**
     * Use the PersonnelStacksEmployee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function usePersonnelStacksEmployeeQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinPersonnelStacksEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'PersonnelStacksEmployee', '\Employee\EmployeeQuery');
    }

    /**
     * Filter the query by a related AbsenceStack object
     *
     * @param   AbsenceStack|PropelObjectCollection $absenceStack The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 PersonnelStacksQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByPersonnelStacksAbsenceStack($absenceStack, $comparison = null)
    {
        if ($absenceStack instanceof AbsenceStack) {
            return $this
                ->addUsingAlias(PersonnelStacksPeer::STACK_ID, $absenceStack->getId(), $comparison);
        } elseif ($absenceStack instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(PersonnelStacksPeer::STACK_ID, $absenceStack->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByPersonnelStacksAbsenceStack() only accepts arguments of type AbsenceStack or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the PersonnelStacksAbsenceStack relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function joinPersonnelStacksAbsenceStack($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('PersonnelStacksAbsenceStack');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'PersonnelStacksAbsenceStack');
        }

        return $this;
    }

    /**
     * Use the PersonnelStacksAbsenceStack relation AbsenceStack object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsenceStackQuery A secondary query class using the current class as primary query
     */
    public function usePersonnelStacksAbsenceStackQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinPersonnelStacksAbsenceStack($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'PersonnelStacksAbsenceStack', '\Employee\AbsenceStackQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   PersonnelStacks $personnelStacks Object to remove from the list of results
     *
     * @return PersonnelStacksQuery The current query, for fluid interface
     */
    public function prune($personnelStacks = null)
    {
        if ($personnelStacks) {
            $this->addUsingAlias(PersonnelStacksPeer::ID, $personnelStacks->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
