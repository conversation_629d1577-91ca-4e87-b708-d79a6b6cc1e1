<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \DateTime;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelDateTime;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceKind;
use Employee\AbsenceKindPeer;
use Employee\AbsenceKindQuery;
use Employee\AbsenceStack;
use Employee\AbsenceStackQuery;
use Employee\Absences;
use Employee\AbsencesQuery;

/**
 * Base class that represents a row from the 'absence_kind' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseAbsenceKind extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\AbsenceKindPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        AbsenceKindPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the code field.
     * @var        string
     */
    protected $code;

    /**
     * The value for the description field.
     * @var        string
     */
    protected $description;

    /**
     * The value for the absence_stack field.
     * @var        string
     */
    protected $absence_stack;

    /**
     * The value for the date_start field.
     * @var        string
     */
    protected $date_start;

    /**
     * The value for the date_end field.
     * @var        string
     */
    protected $date_end;

    /**
     * The value for the calc_festivities field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $calc_festivities;

    /**
     * The value for the calc_ferials field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $calc_ferials;

    /**
     * @var        AbsenceStack
     */
    protected $aAbsenceKindAbsenceStack;

    /**
     * @var        PropelObjectCollection|Absences[] Collection to store aggregation of Absences objects.
     */
    protected $collAbsencess;
    protected $collAbsencessPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $absencessScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->calc_festivities = false;
        $this->calc_ferials = false;
    }

    /**
     * Initializes internal state of BaseAbsenceKind object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [code] column value.
     *
     * @return string
     */
    public function getCode()
    {

        return $this->code;
    }

    /**
     * Get the [description] column value.
     *
     * @return string
     */
    public function getDescription()
    {

        return $this->description;
    }

    /**
     * Get the [absence_stack] column value.
     *
     * @return string
     */
    public function getAbsenceStack()
    {

        return $this->absence_stack;
    }

    /**
     * Get the [optionally formatted] temporal [date_start] column value.
     *
     *
     * @param string $format The date/time format string (either date()-style or strftime()-style).
     *				 If format is null, then the raw DateTime object will be returned.
     * @return mixed Formatted date/time value as string or DateTime object (if format is null), null if column is null
     * @throws PropelException - if unable to parse/validate the date/time value.
     */
    public function getDateStart($format = 'Y-m-d H:i:s')
    {
        if ($this->date_start === null) {
            return null;
        }


        try {
            $dt = new DateTime($this->date_start);
        } catch (Exception $x) {
            throw new PropelException("Internally stored date/time/timestamp value could not be converted to DateTime: " . var_export($this->date_start, true), $x);
        }

        if ($format === null) {
            // Because propel.useDateTimeClass is true, we return a DateTime object.
            return $dt;
        }

        if (strpos($format, '%') !== false) {
            return strftime($format, $dt->format('U'));
        }

        return $dt->format($format);

    }

    /**
     * Get the [optionally formatted] temporal [date_end] column value.
     *
     *
     * @param string $format The date/time format string (either date()-style or strftime()-style).
     *				 If format is null, then the raw DateTime object will be returned.
     * @return mixed Formatted date/time value as string or DateTime object (if format is null), null if column is null
     * @throws PropelException - if unable to parse/validate the date/time value.
     */
    public function getDateEnd($format = 'Y-m-d H:i:s')
    {
        if ($this->date_end === null) {
            return null;
        }


        try {
            $dt = new DateTime($this->date_end);
        } catch (Exception $x) {
            throw new PropelException("Internally stored date/time/timestamp value could not be converted to DateTime: " . var_export($this->date_end, true), $x);
        }

        if ($format === null) {
            // Because propel.useDateTimeClass is true, we return a DateTime object.
            return $dt;
        }

        if (strpos($format, '%') !== false) {
            return strftime($format, $dt->format('U'));
        }

        return $dt->format($format);

    }

    /**
     * Get the [calc_festivities] column value.
     *
     * @return boolean
     */
    public function getCalcFestivities()
    {

        return $this->calc_festivities;
    }

    /**
     * Get the [calc_ferials] column value.
     *
     * @return boolean
     */
    public function getCalcFerials()
    {

        return $this->calc_ferials;
    }

    /**
     * Set the value of [code] column.
     *
     * @param  string $v new value
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->code !== $v) {
            $this->code = $v;
            $this->modifiedColumns[] = AbsenceKindPeer::CODE;
        }


        return $this;
    } // setCode()

    /**
     * Set the value of [description] column.
     *
     * @param  string $v new value
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setDescription($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->description !== $v) {
            $this->description = $v;
            $this->modifiedColumns[] = AbsenceKindPeer::DESCRIPTION;
        }


        return $this;
    } // setDescription()

    /**
     * Set the value of [absence_stack] column.
     *
     * @param  string $v new value
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setAbsenceStack($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->absence_stack !== $v) {
            $this->absence_stack = $v;
            $this->modifiedColumns[] = AbsenceKindPeer::ABSENCE_STACK;
        }

        if ($this->aAbsenceKindAbsenceStack !== null && $this->aAbsenceKindAbsenceStack->getId() !== $v) {
            $this->aAbsenceKindAbsenceStack = null;
        }


        return $this;
    } // setAbsenceStack()

    /**
     * Sets the value of [date_start] column to a normalized version of the date/time value specified.
     *
     * @param mixed $v string, integer (timestamp), or DateTime value.
     *               Empty strings are treated as null.
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setDateStart($v)
    {
        $dt = PropelDateTime::newInstance($v, null, 'DateTime');
        if ($this->date_start !== null || $dt !== null) {
            $currentDateAsString = ($this->date_start !== null && $tmpDt = new DateTime($this->date_start)) ? $tmpDt->format('Y-m-d H:i:s') : null;
            $newDateAsString = $dt ? $dt->format('Y-m-d H:i:s') : null;
            if ($currentDateAsString !== $newDateAsString) {
                $this->date_start = $newDateAsString;
                $this->modifiedColumns[] = AbsenceKindPeer::DATE_START;
            }
        } // if either are not null


        return $this;
    } // setDateStart()

    /**
     * Sets the value of [date_end] column to a normalized version of the date/time value specified.
     *
     * @param mixed $v string, integer (timestamp), or DateTime value.
     *               Empty strings are treated as null.
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setDateEnd($v)
    {
        $dt = PropelDateTime::newInstance($v, null, 'DateTime');
        if ($this->date_end !== null || $dt !== null) {
            $currentDateAsString = ($this->date_end !== null && $tmpDt = new DateTime($this->date_end)) ? $tmpDt->format('Y-m-d H:i:s') : null;
            $newDateAsString = $dt ? $dt->format('Y-m-d H:i:s') : null;
            if ($currentDateAsString !== $newDateAsString) {
                $this->date_end = $newDateAsString;
                $this->modifiedColumns[] = AbsenceKindPeer::DATE_END;
            }
        } // if either are not null


        return $this;
    } // setDateEnd()

    /**
     * Sets the value of the [calc_festivities] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setCalcFestivities($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->calc_festivities !== $v) {
            $this->calc_festivities = $v;
            $this->modifiedColumns[] = AbsenceKindPeer::CALC_FESTIVITIES;
        }


        return $this;
    } // setCalcFestivities()

    /**
     * Sets the value of the [calc_ferials] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setCalcFerials($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->calc_ferials !== $v) {
            $this->calc_ferials = $v;
            $this->modifiedColumns[] = AbsenceKindPeer::CALC_FERIALS;
        }


        return $this;
    } // setCalcFerials()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->calc_festivities !== false) {
                return false;
            }

            if ($this->calc_ferials !== false) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->code = ($row[$startcol + 0] !== null) ? (string) $row[$startcol + 0] : null;
            $this->description = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->absence_stack = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->date_start = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->date_end = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->calc_festivities = ($row[$startcol + 5] !== null) ? (boolean) $row[$startcol + 5] : null;
            $this->calc_ferials = ($row[$startcol + 6] !== null) ? (boolean) $row[$startcol + 6] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 7; // 7 = AbsenceKindPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating AbsenceKind object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aAbsenceKindAbsenceStack !== null && $this->absence_stack !== $this->aAbsenceKindAbsenceStack->getId()) {
            $this->aAbsenceKindAbsenceStack = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsenceKindPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = AbsenceKindPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aAbsenceKindAbsenceStack = null;
            $this->collAbsencess = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsenceKindPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = AbsenceKindQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(AbsenceKindPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                AbsenceKindPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aAbsenceKindAbsenceStack !== null) {
                if ($this->aAbsenceKindAbsenceStack->isModified() || $this->aAbsenceKindAbsenceStack->isNew()) {
                    $affectedRows += $this->aAbsenceKindAbsenceStack->save($con);
                }
                $this->setAbsenceKindAbsenceStack($this->aAbsenceKindAbsenceStack);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->absencessScheduledForDeletion !== null) {
                if (!$this->absencessScheduledForDeletion->isEmpty()) {
                    foreach ($this->absencessScheduledForDeletion as $absences) {
                        // need to save related object because we set the relation to null
                        $absences->save($con);
                    }
                    $this->absencessScheduledForDeletion = null;
                }
            }

            if ($this->collAbsencess !== null) {
                foreach ($this->collAbsencess as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(AbsenceKindPeer::CODE)) {
            $modifiedColumns[':p' . $index++]  = '"code"';
        }
        if ($this->isColumnModified(AbsenceKindPeer::DESCRIPTION)) {
            $modifiedColumns[':p' . $index++]  = '"description"';
        }
        if ($this->isColumnModified(AbsenceKindPeer::ABSENCE_STACK)) {
            $modifiedColumns[':p' . $index++]  = '"absence_stack"';
        }
        if ($this->isColumnModified(AbsenceKindPeer::DATE_START)) {
            $modifiedColumns[':p' . $index++]  = '"date_start"';
        }
        if ($this->isColumnModified(AbsenceKindPeer::DATE_END)) {
            $modifiedColumns[':p' . $index++]  = '"date_end"';
        }
        if ($this->isColumnModified(AbsenceKindPeer::CALC_FESTIVITIES)) {
            $modifiedColumns[':p' . $index++]  = '"calc_festivities"';
        }
        if ($this->isColumnModified(AbsenceKindPeer::CALC_FERIALS)) {
            $modifiedColumns[':p' . $index++]  = '"calc_ferials"';
        }

        $sql = sprintf(
            'INSERT INTO "absence_kind" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"code"':
                        $stmt->bindValue($identifier, $this->code, PDO::PARAM_STR);
                        break;
                    case '"description"':
                        $stmt->bindValue($identifier, $this->description, PDO::PARAM_STR);
                        break;
                    case '"absence_stack"':
                        $stmt->bindValue($identifier, $this->absence_stack, PDO::PARAM_STR);
                        break;
                    case '"date_start"':
                        $stmt->bindValue($identifier, $this->date_start, PDO::PARAM_STR);
                        break;
                    case '"date_end"':
                        $stmt->bindValue($identifier, $this->date_end, PDO::PARAM_STR);
                        break;
                    case '"calc_festivities"':
                        $stmt->bindValue($identifier, $this->calc_festivities, PDO::PARAM_BOOL);
                        break;
                    case '"calc_ferials"':
                        $stmt->bindValue($identifier, $this->calc_ferials, PDO::PARAM_BOOL);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aAbsenceKindAbsenceStack !== null) {
                if (!$this->aAbsenceKindAbsenceStack->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aAbsenceKindAbsenceStack->getValidationFailures());
                }
            }


            if (($retval = AbsenceKindPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collAbsencess !== null) {
                    foreach ($this->collAbsencess as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AbsenceKindPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getCode();
                break;
            case 1:
                return $this->getDescription();
                break;
            case 2:
                return $this->getAbsenceStack();
                break;
            case 3:
                return $this->getDateStart();
                break;
            case 4:
                return $this->getDateEnd();
                break;
            case 5:
                return $this->getCalcFestivities();
                break;
            case 6:
                return $this->getCalcFerials();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['AbsenceKind'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['AbsenceKind'][$this->getPrimaryKey()] = true;
        $keys = AbsenceKindPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getCode(),
            $keys[1] => $this->getDescription(),
            $keys[2] => $this->getAbsenceStack(),
            $keys[3] => $this->getDateStart(),
            $keys[4] => $this->getDateEnd(),
            $keys[5] => $this->getCalcFestivities(),
            $keys[6] => $this->getCalcFerials(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aAbsenceKindAbsenceStack) {
                $result['AbsenceKindAbsenceStack'] = $this->aAbsenceKindAbsenceStack->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collAbsencess) {
                $result['Absencess'] = $this->collAbsencess->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = AbsenceKindPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setCode($value);
                break;
            case 1:
                $this->setDescription($value);
                break;
            case 2:
                $this->setAbsenceStack($value);
                break;
            case 3:
                $this->setDateStart($value);
                break;
            case 4:
                $this->setDateEnd($value);
                break;
            case 5:
                $this->setCalcFestivities($value);
                break;
            case 6:
                $this->setCalcFerials($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = AbsenceKindPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setCode($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setDescription($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setAbsenceStack($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setDateStart($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setDateEnd($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setCalcFestivities($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setCalcFerials($arr[$keys[6]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(AbsenceKindPeer::DATABASE_NAME);

        if ($this->isColumnModified(AbsenceKindPeer::CODE)) $criteria->add(AbsenceKindPeer::CODE, $this->code);
        if ($this->isColumnModified(AbsenceKindPeer::DESCRIPTION)) $criteria->add(AbsenceKindPeer::DESCRIPTION, $this->description);
        if ($this->isColumnModified(AbsenceKindPeer::ABSENCE_STACK)) $criteria->add(AbsenceKindPeer::ABSENCE_STACK, $this->absence_stack);
        if ($this->isColumnModified(AbsenceKindPeer::DATE_START)) $criteria->add(AbsenceKindPeer::DATE_START, $this->date_start);
        if ($this->isColumnModified(AbsenceKindPeer::DATE_END)) $criteria->add(AbsenceKindPeer::DATE_END, $this->date_end);
        if ($this->isColumnModified(AbsenceKindPeer::CALC_FESTIVITIES)) $criteria->add(AbsenceKindPeer::CALC_FESTIVITIES, $this->calc_festivities);
        if ($this->isColumnModified(AbsenceKindPeer::CALC_FERIALS)) $criteria->add(AbsenceKindPeer::CALC_FERIALS, $this->calc_ferials);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(AbsenceKindPeer::DATABASE_NAME);
        $criteria->add(AbsenceKindPeer::CODE, $this->code);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return string
     */
    public function getPrimaryKey()
    {
        return $this->getCode();
    }

    /**
     * Generic method to set the primary key (code column).
     *
     * @param  string $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setCode($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getCode();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of AbsenceKind (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setDescription($this->getDescription());
        $copyObj->setAbsenceStack($this->getAbsenceStack());
        $copyObj->setDateStart($this->getDateStart());
        $copyObj->setDateEnd($this->getDateEnd());
        $copyObj->setCalcFestivities($this->getCalcFestivities());
        $copyObj->setCalcFerials($this->getCalcFerials());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getAbsencess() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAbsences($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setCode(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return AbsenceKind Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return AbsenceKindPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new AbsenceKindPeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a AbsenceStack object.
     *
     * @param                  AbsenceStack $v
     * @return AbsenceKind The current object (for fluent API support)
     * @throws PropelException
     */
    public function setAbsenceKindAbsenceStack(AbsenceStack $v = null)
    {
        if ($v === null) {
            $this->setAbsenceStack(NULL);
        } else {
            $this->setAbsenceStack($v->getId());
        }

        $this->aAbsenceKindAbsenceStack = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the AbsenceStack object, it will not be re-added.
        if ($v !== null) {
            $v->addAbsenceKind($this);
        }


        return $this;
    }


    /**
     * Get the associated AbsenceStack object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return AbsenceStack The associated AbsenceStack object.
     * @throws PropelException
     */
    public function getAbsenceKindAbsenceStack(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aAbsenceKindAbsenceStack === null && (($this->absence_stack !== "" && $this->absence_stack !== null)) && $doQuery) {
            $this->aAbsenceKindAbsenceStack = AbsenceStackQuery::create()->findPk($this->absence_stack, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aAbsenceKindAbsenceStack->addAbsenceKinds($this);
             */
        }

        return $this->aAbsenceKindAbsenceStack;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Absences' == $relationName) {
            $this->initAbsencess();
        }
    }

    /**
     * Clears out the collAbsencess collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return AbsenceKind The current object (for fluent API support)
     * @see        addAbsencess()
     */
    public function clearAbsencess()
    {
        $this->collAbsencess = null; // important to set this to null since that means it is uninitialized
        $this->collAbsencessPartial = null;

        return $this;
    }

    /**
     * reset is the collAbsencess collection loaded partially
     *
     * @return void
     */
    public function resetPartialAbsencess($v = true)
    {
        $this->collAbsencessPartial = $v;
    }

    /**
     * Initializes the collAbsencess collection.
     *
     * By default this just sets the collAbsencess collection to an empty array (like clearcollAbsencess());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAbsencess($overrideExisting = true)
    {
        if (null !== $this->collAbsencess && !$overrideExisting) {
            return;
        }
        $this->collAbsencess = new PropelObjectCollection();
        $this->collAbsencess->setModel('Absences');
    }

    /**
     * Gets an array of Absences objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this AbsenceKind is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Absences[] List of Absences objects
     * @throws PropelException
     */
    public function getAbsencess($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAbsencessPartial && !$this->isNew();
        if (null === $this->collAbsencess || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAbsencess) {
                // return empty collection
                $this->initAbsencess();
            } else {
                $collAbsencess = AbsencesQuery::create(null, $criteria)
                    ->filterByAbsencesAbsenceKind($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAbsencessPartial && count($collAbsencess)) {
                      $this->initAbsencess(false);

                      foreach ($collAbsencess as $obj) {
                        if (false == $this->collAbsencess->contains($obj)) {
                          $this->collAbsencess->append($obj);
                        }
                      }

                      $this->collAbsencessPartial = true;
                    }

                    $collAbsencess->getInternalIterator()->rewind();

                    return $collAbsencess;
                }

                if ($partial && $this->collAbsencess) {
                    foreach ($this->collAbsencess as $obj) {
                        if ($obj->isNew()) {
                            $collAbsencess[] = $obj;
                        }
                    }
                }

                $this->collAbsencess = $collAbsencess;
                $this->collAbsencessPartial = false;
            }
        }

        return $this->collAbsencess;
    }

    /**
     * Sets a collection of Absences objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $absencess A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function setAbsencess(PropelCollection $absencess, PropelPDO $con = null)
    {
        $absencessToDelete = $this->getAbsencess(new Criteria(), $con)->diff($absencess);


        $this->absencessScheduledForDeletion = $absencessToDelete;

        foreach ($absencessToDelete as $absencesRemoved) {
            $absencesRemoved->setAbsencesAbsenceKind(null);
        }

        $this->collAbsencess = null;
        foreach ($absencess as $absences) {
            $this->addAbsences($absences);
        }

        $this->collAbsencess = $absencess;
        $this->collAbsencessPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Absences objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Absences objects.
     * @throws PropelException
     */
    public function countAbsencess(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAbsencessPartial && !$this->isNew();
        if (null === $this->collAbsencess || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAbsencess) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAbsencess());
            }
            $query = AbsencesQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAbsencesAbsenceKind($this)
                ->count($con);
        }

        return count($this->collAbsencess);
    }

    /**
     * Method called to associate a Absences object to this object
     * through the Absences foreign key attribute.
     *
     * @param    Absences $l Absences
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function addAbsences(Absences $l)
    {
        if ($this->collAbsencess === null) {
            $this->initAbsencess();
            $this->collAbsencessPartial = true;
        }

        if (!in_array($l, $this->collAbsencess->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAbsences($l);

            if ($this->absencessScheduledForDeletion and $this->absencessScheduledForDeletion->contains($l)) {
                $this->absencessScheduledForDeletion->remove($this->absencessScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Absences $absences The absences object to add.
     */
    protected function doAddAbsences($absences)
    {
        $this->collAbsencess[]= $absences;
        $absences->setAbsencesAbsenceKind($this);
    }

    /**
     * @param	Absences $absences The absences object to remove.
     * @return AbsenceKind The current object (for fluent API support)
     */
    public function removeAbsences($absences)
    {
        if ($this->getAbsencess()->contains($absences)) {
            $this->collAbsencess->remove($this->collAbsencess->search($absences));
            if (null === $this->absencessScheduledForDeletion) {
                $this->absencessScheduledForDeletion = clone $this->collAbsencess;
                $this->absencessScheduledForDeletion->clear();
            }
            $this->absencessScheduledForDeletion[]= $absences;
            $absences->setAbsencesAbsenceKind(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this AbsenceKind is new, it will return
     * an empty collection; or if this AbsenceKind has previously
     * been saved, it will retrieve related Absencess from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in AbsenceKind.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Absences[] List of Absences objects
     */
    public function getAbsencessJoinAbsenceEmployee($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = AbsencesQuery::create(null, $criteria);
        $query->joinWith('AbsenceEmployee', $join_behavior);

        return $this->getAbsencess($query, $con);
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->code = null;
        $this->description = null;
        $this->absence_stack = null;
        $this->date_start = null;
        $this->date_end = null;
        $this->calc_festivities = null;
        $this->calc_ferials = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collAbsencess) {
                foreach ($this->collAbsencess as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aAbsenceKindAbsenceStack instanceof Persistent) {
              $this->aAbsenceKindAbsenceStack->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collAbsencess instanceof PropelCollection) {
            $this->collAbsencess->clearIterator();
        }
        $this->collAbsencess = null;
        $this->aAbsenceKindAbsenceStack = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(AbsenceKindPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
