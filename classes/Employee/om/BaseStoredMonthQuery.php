<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\Employee;
use Employee\StoredMonth;
use Employee\StoredMonthPeer;
use Employee\StoredMonthQuery;
use Employee\StoredStack;

/**
 * Base class that represents a query for the 'storage_personnel_presences' table.
 *
 *
 *
 * @method StoredMonthQuery orderByStoredMonthId($order = Criteria::ASC) Order by the storage_personnel_presences_id column
 * @method StoredMonthQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method StoredMonthQuery orderByDateStart($order = Criteria::ASC) Order by the date_start column
 * @method StoredMonthQuery orderByDateEnd($order = Criteria::ASC) Order by the date_end column
 * @method StoredMonthQuery orderByExtStartOriginal($order = Criteria::ASC) Order by the ext_start_o column
 * @method StoredMonthQuery orderByExtEndOriginal($order = Criteria::ASC) Order by the ext_end_o column
 * @method StoredMonthQuery orderByExtStart($order = Criteria::ASC) Order by the ext_start column
 * @method StoredMonthQuery orderByExtEnd($order = Criteria::ASC) Order by the ext_end column
 * @method StoredMonthQuery orderByNote($order = Criteria::ASC) Order by the note column
 *
 * @method StoredMonthQuery groupByStoredMonthId() Group by the storage_personnel_presences_id column
 * @method StoredMonthQuery groupByEmployeeId() Group by the employee_id column
 * @method StoredMonthQuery groupByDateStart() Group by the date_start column
 * @method StoredMonthQuery groupByDateEnd() Group by the date_end column
 * @method StoredMonthQuery groupByExtStartOriginal() Group by the ext_start_o column
 * @method StoredMonthQuery groupByExtEndOriginal() Group by the ext_end_o column
 * @method StoredMonthQuery groupByExtStart() Group by the ext_start column
 * @method StoredMonthQuery groupByExtEnd() Group by the ext_end column
 * @method StoredMonthQuery groupByNote() Group by the note column
 *
 * @method StoredMonthQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method StoredMonthQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method StoredMonthQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method StoredMonthQuery leftJoinStoredMonthEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredMonthEmployee relation
 * @method StoredMonthQuery rightJoinStoredMonthEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredMonthEmployee relation
 * @method StoredMonthQuery innerJoinStoredMonthEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredMonthEmployee relation
 *
 * @method StoredMonthQuery leftJoinStoredStack($relationAlias = null) Adds a LEFT JOIN clause to the query using the StoredStack relation
 * @method StoredMonthQuery rightJoinStoredStack($relationAlias = null) Adds a RIGHT JOIN clause to the query using the StoredStack relation
 * @method StoredMonthQuery innerJoinStoredStack($relationAlias = null) Adds a INNER JOIN clause to the query using the StoredStack relation
 *
 * @method StoredMonth findOne(PropelPDO $con = null) Return the first StoredMonth matching the query
 * @method StoredMonth findOneOrCreate(PropelPDO $con = null) Return the first StoredMonth matching the query, or a new StoredMonth object populated from the query conditions when no match is found
 *
 * @method StoredMonth findOneByEmployeeId(int $employee_id) Return the first StoredMonth filtered by the employee_id column
 * @method StoredMonth findOneByDateStart(string $date_start) Return the first StoredMonth filtered by the date_start column
 * @method StoredMonth findOneByDateEnd(string $date_end) Return the first StoredMonth filtered by the date_end column
 * @method StoredMonth findOneByExtStartOriginal(int $ext_start_o) Return the first StoredMonth filtered by the ext_start_o column
 * @method StoredMonth findOneByExtEndOriginal(int $ext_end_o) Return the first StoredMonth filtered by the ext_end_o column
 * @method StoredMonth findOneByExtStart(int $ext_start) Return the first StoredMonth filtered by the ext_start column
 * @method StoredMonth findOneByExtEnd(int $ext_end) Return the first StoredMonth filtered by the ext_end column
 * @method StoredMonth findOneByNote(string $note) Return the first StoredMonth filtered by the note column
 *
 * @method array findByStoredMonthId(int $storage_personnel_presences_id) Return StoredMonth objects filtered by the storage_personnel_presences_id column
 * @method array findByEmployeeId(int $employee_id) Return StoredMonth objects filtered by the employee_id column
 * @method array findByDateStart(string $date_start) Return StoredMonth objects filtered by the date_start column
 * @method array findByDateEnd(string $date_end) Return StoredMonth objects filtered by the date_end column
 * @method array findByExtStartOriginal(int $ext_start_o) Return StoredMonth objects filtered by the ext_start_o column
 * @method array findByExtEndOriginal(int $ext_end_o) Return StoredMonth objects filtered by the ext_end_o column
 * @method array findByExtStart(int $ext_start) Return StoredMonth objects filtered by the ext_start column
 * @method array findByExtEnd(int $ext_end) Return StoredMonth objects filtered by the ext_end column
 * @method array findByNote(string $note) Return StoredMonth objects filtered by the note column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseStoredMonthQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseStoredMonthQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\StoredMonth';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new StoredMonthQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   StoredMonthQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return StoredMonthQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof StoredMonthQuery) {
            return $criteria;
        }
        $query = new StoredMonthQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   StoredMonth|StoredMonth[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = StoredMonthPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(StoredMonthPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 StoredMonth A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByStoredMonthId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 StoredMonth A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "storage_personnel_presences_id", "employee_id", "date_start", "date_end", "ext_start_o", "ext_end_o", "ext_start", "ext_end", "note" FROM "storage_personnel_presences" WHERE "storage_personnel_presences_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new StoredMonth();
            $obj->hydrate($row);
            StoredMonthPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return StoredMonth|StoredMonth[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|StoredMonth[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the storage_personnel_presences_id column
     *
     * Example usage:
     * <code>
     * $query->filterByStoredMonthId(1234); // WHERE storage_personnel_presences_id = 1234
     * $query->filterByStoredMonthId(array(12, 34)); // WHERE storage_personnel_presences_id IN (12, 34)
     * $query->filterByStoredMonthId(array('min' => 12)); // WHERE storage_personnel_presences_id >= 12
     * $query->filterByStoredMonthId(array('max' => 12)); // WHERE storage_personnel_presences_id <= 12
     * </code>
     *
     * @param     mixed $storedMonthId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByStoredMonthId($storedMonthId = null, $comparison = null)
    {
        if (is_array($storedMonthId)) {
            $useMinMax = false;
            if (isset($storedMonthId['min'])) {
                $this->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $storedMonthId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($storedMonthId['max'])) {
                $this->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $storedMonthId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $storedMonthId, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @see       filterByStoredMonthEmployee()
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(StoredMonthPeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(StoredMonthPeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the date_start column
     *
     * Example usage:
     * <code>
     * $query->filterByDateStart(1234); // WHERE date_start = 1234
     * $query->filterByDateStart(array(12, 34)); // WHERE date_start IN (12, 34)
     * $query->filterByDateStart(array('min' => 12)); // WHERE date_start >= 12
     * $query->filterByDateStart(array('max' => 12)); // WHERE date_start <= 12
     * </code>
     *
     * @param     mixed $dateStart The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByDateStart($dateStart = null, $comparison = null)
    {
        if (is_array($dateStart)) {
            $useMinMax = false;
            if (isset($dateStart['min'])) {
                $this->addUsingAlias(StoredMonthPeer::DATE_START, $dateStart['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateStart['max'])) {
                $this->addUsingAlias(StoredMonthPeer::DATE_START, $dateStart['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::DATE_START, $dateStart, $comparison);
    }

    /**
     * Filter the query on the date_end column
     *
     * Example usage:
     * <code>
     * $query->filterByDateEnd(1234); // WHERE date_end = 1234
     * $query->filterByDateEnd(array(12, 34)); // WHERE date_end IN (12, 34)
     * $query->filterByDateEnd(array('min' => 12)); // WHERE date_end >= 12
     * $query->filterByDateEnd(array('max' => 12)); // WHERE date_end <= 12
     * </code>
     *
     * @param     mixed $dateEnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByDateEnd($dateEnd = null, $comparison = null)
    {
        if (is_array($dateEnd)) {
            $useMinMax = false;
            if (isset($dateEnd['min'])) {
                $this->addUsingAlias(StoredMonthPeer::DATE_END, $dateEnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateEnd['max'])) {
                $this->addUsingAlias(StoredMonthPeer::DATE_END, $dateEnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::DATE_END, $dateEnd, $comparison);
    }

    /**
     * Filter the query on the ext_start_o column
     *
     * Example usage:
     * <code>
     * $query->filterByExtStartOriginal(1234); // WHERE ext_start_o = 1234
     * $query->filterByExtStartOriginal(array(12, 34)); // WHERE ext_start_o IN (12, 34)
     * $query->filterByExtStartOriginal(array('min' => 12)); // WHERE ext_start_o >= 12
     * $query->filterByExtStartOriginal(array('max' => 12)); // WHERE ext_start_o <= 12
     * </code>
     *
     * @param     mixed $extStartOriginal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByExtStartOriginal($extStartOriginal = null, $comparison = null)
    {
        if (is_array($extStartOriginal)) {
            $useMinMax = false;
            if (isset($extStartOriginal['min'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_START_O, $extStartOriginal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($extStartOriginal['max'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_START_O, $extStartOriginal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::EXT_START_O, $extStartOriginal, $comparison);
    }

    /**
     * Filter the query on the ext_end_o column
     *
     * Example usage:
     * <code>
     * $query->filterByExtEndOriginal(1234); // WHERE ext_end_o = 1234
     * $query->filterByExtEndOriginal(array(12, 34)); // WHERE ext_end_o IN (12, 34)
     * $query->filterByExtEndOriginal(array('min' => 12)); // WHERE ext_end_o >= 12
     * $query->filterByExtEndOriginal(array('max' => 12)); // WHERE ext_end_o <= 12
     * </code>
     *
     * @param     mixed $extEndOriginal The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByExtEndOriginal($extEndOriginal = null, $comparison = null)
    {
        if (is_array($extEndOriginal)) {
            $useMinMax = false;
            if (isset($extEndOriginal['min'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_END_O, $extEndOriginal['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($extEndOriginal['max'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_END_O, $extEndOriginal['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::EXT_END_O, $extEndOriginal, $comparison);
    }

    /**
     * Filter the query on the ext_start column
     *
     * Example usage:
     * <code>
     * $query->filterByExtStart(1234); // WHERE ext_start = 1234
     * $query->filterByExtStart(array(12, 34)); // WHERE ext_start IN (12, 34)
     * $query->filterByExtStart(array('min' => 12)); // WHERE ext_start >= 12
     * $query->filterByExtStart(array('max' => 12)); // WHERE ext_start <= 12
     * </code>
     *
     * @param     mixed $extStart The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByExtStart($extStart = null, $comparison = null)
    {
        if (is_array($extStart)) {
            $useMinMax = false;
            if (isset($extStart['min'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_START, $extStart['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($extStart['max'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_START, $extStart['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::EXT_START, $extStart, $comparison);
    }

    /**
     * Filter the query on the ext_end column
     *
     * Example usage:
     * <code>
     * $query->filterByExtEnd(1234); // WHERE ext_end = 1234
     * $query->filterByExtEnd(array(12, 34)); // WHERE ext_end IN (12, 34)
     * $query->filterByExtEnd(array('min' => 12)); // WHERE ext_end >= 12
     * $query->filterByExtEnd(array('max' => 12)); // WHERE ext_end <= 12
     * </code>
     *
     * @param     mixed $extEnd The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByExtEnd($extEnd = null, $comparison = null)
    {
        if (is_array($extEnd)) {
            $useMinMax = false;
            if (isset($extEnd['min'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_END, $extEnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($extEnd['max'])) {
                $this->addUsingAlias(StoredMonthPeer::EXT_END, $extEnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::EXT_END, $extEnd, $comparison);
    }

    /**
     * Filter the query on the note column
     *
     * Example usage:
     * <code>
     * $query->filterByNote('fooValue');   // WHERE note = 'fooValue'
     * $query->filterByNote('%fooValue%'); // WHERE note LIKE '%fooValue%'
     * </code>
     *
     * @param     string $note The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function filterByNote($note = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($note)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $note)) {
                $note = str_replace('*', '%', $note);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(StoredMonthPeer::NOTE, $note, $comparison);
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 StoredMonthQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredMonthEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(StoredMonthPeer::EMPLOYEE_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(StoredMonthPeer::EMPLOYEE_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByStoredMonthEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredMonthEmployee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function joinStoredMonthEmployee($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredMonthEmployee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredMonthEmployee');
        }

        return $this;
    }

    /**
     * Use the StoredMonthEmployee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useStoredMonthEmployeeQuery($relationAlias = null, $joinType = Criteria::INNER_JOIN)
    {
        return $this
            ->joinStoredMonthEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredMonthEmployee', '\Employee\EmployeeQuery');
    }

    /**
     * Filter the query by a related StoredStack object
     *
     * @param   StoredStack|PropelObjectCollection $storedStack  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 StoredMonthQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByStoredStack($storedStack, $comparison = null)
    {
        if ($storedStack instanceof StoredStack) {
            return $this
                ->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $storedStack->getStoredMonth(), $comparison);
        } elseif ($storedStack instanceof PropelObjectCollection) {
            return $this
                ->useStoredStackQuery()
                ->filterByPrimaryKeys($storedStack->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByStoredStack() only accepts arguments of type StoredStack or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the StoredStack relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function joinStoredStack($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('StoredStack');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'StoredStack');
        }

        return $this;
    }

    /**
     * Use the StoredStack relation StoredStack object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\StoredStackQuery A secondary query class using the current class as primary query
     */
    public function useStoredStackQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinStoredStack($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'StoredStack', '\Employee\StoredStackQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   StoredMonth $storedMonth Object to remove from the list of results
     *
     * @return StoredMonthQuery The current query, for fluid interface
     */
    public function prune($storedMonth = null)
    {
        if ($storedMonth) {
            $this->addUsingAlias(StoredMonthPeer::STORAGE_PERSONNEL_PRESENCES_ID, $storedMonth->getStoredMonthId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
