<?php

namespace Employee\om;

use \BasePeer;
use \Criteria;
use \PDO;
use \PDOStatement;
use \Propel;
use \PropelException;
use \PropelPDO;
use Budget\ActivityPeer;
use Employee\EmployeePeer;
use Employee\Presence;
use Employee\PresencePeer;
use Employee\map\PresenceTableMap;

/**
 * Base static class for performing query and update operations on the 'personnel_presences' table.
 *
 *
 *
 * @package propel.generator.Employee.om
 */
abstract class BasePresencePeer
{

    /** the default database name for this class */
    const DATABASE_NAME = 'mc2api';

    /** the table name for this class */
    const TABLE_NAME = 'personnel_presences';

    /** the related Propel class for this table */
    const OM_CLASS = 'Employee\\Presence';

    /** the related TableMap class for this table */
    const TM_CLASS = 'PresenceTableMap';

    /** The total number of columns. */
    const NUM_COLUMNS = 14;

    /** The number of lazy-loaded columns. */
    const NUM_LAZY_LOAD_COLUMNS = 0;

    /** The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS) */
    const NUM_HYDRATE_COLUMNS = 14;

    /** the column name for the personnel_presence_id field */
    const PERSONNEL_PRESENCE_ID = 'personnel_presences.personnel_presence_id';

    /** the column name for the employee_id field */
    const EMPLOYEE_ID = 'personnel_presences.employee_id';

    /** the column name for the project_id field */
    const PROJECT_ID = 'personnel_presences.project_id';

    /** the column name for the project_edit_id field */
    const PROJECT_EDIT_ID = 'personnel_presences.project_edit_id';

    /** the column name for the date field */
    const DATE = 'personnel_presences.date';

    /** the column name for the date_edit field */
    const DATE_EDIT = 'personnel_presences.date_edit';

    /** the column name for the type field */
    const TYPE = 'personnel_presences.type';

    /** the column name for the type_edit field */
    const TYPE_EDIT = 'personnel_presences.type_edit';

    /** the column name for the original_inout field */
    const ORIGINAL_INOUT = 'personnel_presences.original_inout';

    /** the column name for the original_inout_edit field */
    const ORIGINAL_INOUT_EDIT = 'personnel_presences.original_inout_edit';

    /** the column name for the description field */
    const DESCRIPTION = 'personnel_presences.description';

    /** the column name for the hour_type_id field */
    const HOUR_TYPE_ID = 'personnel_presences.hour_type_id';

    /** the column name for the hour_type_edit_id field */
    const HOUR_TYPE_EDIT_ID = 'personnel_presences.hour_type_edit_id';

    /** the column name for the insertion_mode field */
    const INSERTION_MODE = 'personnel_presences.insertion_mode';

    /** The default string format for model objects of the related table **/
    const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * An identity map to hold any loaded instances of Presence objects.
     * This must be public so that other peer classes can access this when hydrating from JOIN
     * queries.
     * @var        array Presence[]
     */
    public static $instances = array();


    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. PresencePeer::$fieldNames[PresencePeer::TYPE_PHPNAME][0] = 'Id'
     */
    protected static $fieldNames = array (
        BasePeer::TYPE_PHPNAME => array ('PersonnelPresenceId', 'EmployeeId', 'ProjectId', 'ProjectIdEdit', 'Date', 'DateEdit', 'Type', 'TypeEdit', 'OriginalInOut', 'OriginalInOutEdit', 'Description', 'HourTypeId', 'HourTypeIdEdit', 'InsertionMode', ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('personnelPresenceId', 'employeeId', 'projectId', 'projectIdEdit', 'date', 'dateEdit', 'type', 'typeEdit', 'originalInOut', 'originalInOutEdit', 'description', 'hourTypeId', 'hourTypeIdEdit', 'insertionMode', ),
        BasePeer::TYPE_COLNAME => array (PresencePeer::PERSONNEL_PRESENCE_ID, PresencePeer::EMPLOYEE_ID, PresencePeer::PROJECT_ID, PresencePeer::PROJECT_EDIT_ID, PresencePeer::DATE, PresencePeer::DATE_EDIT, PresencePeer::TYPE, PresencePeer::TYPE_EDIT, PresencePeer::ORIGINAL_INOUT, PresencePeer::ORIGINAL_INOUT_EDIT, PresencePeer::DESCRIPTION, PresencePeer::HOUR_TYPE_ID, PresencePeer::HOUR_TYPE_EDIT_ID, PresencePeer::INSERTION_MODE, ),
        BasePeer::TYPE_RAW_COLNAME => array ('PERSONNEL_PRESENCE_ID', 'EMPLOYEE_ID', 'PROJECT_ID', 'PROJECT_EDIT_ID', 'DATE', 'DATE_EDIT', 'TYPE', 'TYPE_EDIT', 'ORIGINAL_INOUT', 'ORIGINAL_INOUT_EDIT', 'DESCRIPTION', 'HOUR_TYPE_ID', 'HOUR_TYPE_EDIT_ID', 'INSERTION_MODE', ),
        BasePeer::TYPE_FIELDNAME => array ('personnel_presence_id', 'employee_id', 'project_id', 'project_edit_id', 'date', 'date_edit', 'type', 'type_edit', 'original_inout', 'original_inout_edit', 'description', 'hour_type_id', 'hour_type_edit_id', 'insertion_mode', ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, )
    );

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. PresencePeer::$fieldNames[BasePeer::TYPE_PHPNAME]['Id'] = 0
     */
    protected static $fieldKeys = array (
        BasePeer::TYPE_PHPNAME => array ('PersonnelPresenceId' => 0, 'EmployeeId' => 1, 'ProjectId' => 2, 'ProjectIdEdit' => 3, 'Date' => 4, 'DateEdit' => 5, 'Type' => 6, 'TypeEdit' => 7, 'OriginalInOut' => 8, 'OriginalInOutEdit' => 9, 'Description' => 10, 'HourTypeId' => 11, 'HourTypeIdEdit' => 12, 'InsertionMode' => 13, ),
        BasePeer::TYPE_STUDLYPHPNAME => array ('personnelPresenceId' => 0, 'employeeId' => 1, 'projectId' => 2, 'projectIdEdit' => 3, 'date' => 4, 'dateEdit' => 5, 'type' => 6, 'typeEdit' => 7, 'originalInOut' => 8, 'originalInOutEdit' => 9, 'description' => 10, 'hourTypeId' => 11, 'hourTypeIdEdit' => 12, 'insertionMode' => 13, ),
        BasePeer::TYPE_COLNAME => array (PresencePeer::PERSONNEL_PRESENCE_ID => 0, PresencePeer::EMPLOYEE_ID => 1, PresencePeer::PROJECT_ID => 2, PresencePeer::PROJECT_EDIT_ID => 3, PresencePeer::DATE => 4, PresencePeer::DATE_EDIT => 5, PresencePeer::TYPE => 6, PresencePeer::TYPE_EDIT => 7, PresencePeer::ORIGINAL_INOUT => 8, PresencePeer::ORIGINAL_INOUT_EDIT => 9, PresencePeer::DESCRIPTION => 10, PresencePeer::HOUR_TYPE_ID => 11, PresencePeer::HOUR_TYPE_EDIT_ID => 12, PresencePeer::INSERTION_MODE => 13, ),
        BasePeer::TYPE_RAW_COLNAME => array ('PERSONNEL_PRESENCE_ID' => 0, 'EMPLOYEE_ID' => 1, 'PROJECT_ID' => 2, 'PROJECT_EDIT_ID' => 3, 'DATE' => 4, 'DATE_EDIT' => 5, 'TYPE' => 6, 'TYPE_EDIT' => 7, 'ORIGINAL_INOUT' => 8, 'ORIGINAL_INOUT_EDIT' => 9, 'DESCRIPTION' => 10, 'HOUR_TYPE_ID' => 11, 'HOUR_TYPE_EDIT_ID' => 12, 'INSERTION_MODE' => 13, ),
        BasePeer::TYPE_FIELDNAME => array ('personnel_presence_id' => 0, 'employee_id' => 1, 'project_id' => 2, 'project_edit_id' => 3, 'date' => 4, 'date_edit' => 5, 'type' => 6, 'type_edit' => 7, 'original_inout' => 8, 'original_inout_edit' => 9, 'description' => 10, 'hour_type_id' => 11, 'hour_type_edit_id' => 12, 'insertion_mode' => 13, ),
        BasePeer::TYPE_NUM => array (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, )
    );

    /**
     * Translates a fieldname to another type
     *
     * @param      string $name field name
     * @param      string $fromType One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                         BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @param      string $toType   One of the class type constants
     * @return string          translated name of the field.
     * @throws PropelException - if the specified name could not be found in the fieldname mappings.
     */
    public static function translateFieldName($name, $fromType, $toType)
    {
        $toNames = PresencePeer::getFieldNames($toType);
        $key = isset(PresencePeer::$fieldKeys[$fromType][$name]) ? PresencePeer::$fieldKeys[$fromType][$name] : null;
        if ($key === null) {
            throw new PropelException("'$name' could not be found in the field names of type '$fromType'. These are: " . print_r(PresencePeer::$fieldKeys[$fromType], true));
        }

        return $toNames[$key];
    }

    /**
     * Returns an array of field names.
     *
     * @param      string $type The type of fieldnames to return:
     *                      One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                      BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM
     * @return array           A list of field names
     * @throws PropelException - if the type is not valid.
     */
    public static function getFieldNames($type = BasePeer::TYPE_PHPNAME)
    {
        if (!array_key_exists($type, PresencePeer::$fieldNames)) {
            throw new PropelException('Method getFieldNames() expects the parameter $type to be one of the class constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME, BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM. ' . $type . ' was given.');
        }

        return PresencePeer::$fieldNames[$type];
    }

    /**
     * Convenience method which changes table.column to alias.column.
     *
     * Using this method you can maintain SQL abstraction while using column aliases.
     * <code>
     *		$c->addAlias("alias1", TablePeer::TABLE_NAME);
     *		$c->addJoin(TablePeer::alias("alias1", TablePeer::PRIMARY_KEY_COLUMN), TablePeer::PRIMARY_KEY_COLUMN);
     * </code>
     * @param      string $alias The alias for the current table.
     * @param      string $column The column name for current table. (i.e. PresencePeer::COLUMN_NAME).
     * @return string
     */
    public static function alias($alias, $column)
    {
        return str_replace(PresencePeer::TABLE_NAME.'.', $alias.'.', $column);
    }

    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param      Criteria $criteria object containing the columns to add.
     * @param      string   $alias    optional table alias
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function addSelectColumns(Criteria $criteria, $alias = null)
    {
        if (null === $alias) {
            $criteria->addSelectColumn(PresencePeer::PERSONNEL_PRESENCE_ID);
            $criteria->addSelectColumn(PresencePeer::EMPLOYEE_ID);
            $criteria->addSelectColumn(PresencePeer::PROJECT_ID);
            $criteria->addSelectColumn(PresencePeer::PROJECT_EDIT_ID);
            $criteria->addSelectColumn(PresencePeer::DATE);
            $criteria->addSelectColumn(PresencePeer::DATE_EDIT);
            $criteria->addSelectColumn(PresencePeer::TYPE);
            $criteria->addSelectColumn(PresencePeer::TYPE_EDIT);
            $criteria->addSelectColumn(PresencePeer::ORIGINAL_INOUT);
            $criteria->addSelectColumn(PresencePeer::ORIGINAL_INOUT_EDIT);
            $criteria->addSelectColumn(PresencePeer::DESCRIPTION);
            $criteria->addSelectColumn(PresencePeer::HOUR_TYPE_ID);
            $criteria->addSelectColumn(PresencePeer::HOUR_TYPE_EDIT_ID);
            $criteria->addSelectColumn(PresencePeer::INSERTION_MODE);
        } else {
            $criteria->addSelectColumn($alias . '.personnel_presence_id');
            $criteria->addSelectColumn($alias . '.employee_id');
            $criteria->addSelectColumn($alias . '.project_id');
            $criteria->addSelectColumn($alias . '.project_edit_id');
            $criteria->addSelectColumn($alias . '.date');
            $criteria->addSelectColumn($alias . '.date_edit');
            $criteria->addSelectColumn($alias . '.type');
            $criteria->addSelectColumn($alias . '.type_edit');
            $criteria->addSelectColumn($alias . '.original_inout');
            $criteria->addSelectColumn($alias . '.original_inout_edit');
            $criteria->addSelectColumn($alias . '.description');
            $criteria->addSelectColumn($alias . '.hour_type_id');
            $criteria->addSelectColumn($alias . '.hour_type_edit_id');
            $criteria->addSelectColumn($alias . '.insertion_mode');
        }
    }

    /**
     * Returns the number of rows matching criteria.
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @return int Number of matching rows.
     */
    public static function doCount(Criteria $criteria, $distinct = false, PropelPDO $con = null)
    {
        // we may modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(PresencePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            PresencePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count
        $criteria->setDbName(PresencePeer::DATABASE_NAME); // Set the correct dbName

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        // BasePeer returns a PDOStatement
        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }
    /**
     * Selects one object from the DB.
     *
     * @param      Criteria $criteria object used to create the SELECT statement.
     * @param      PropelPDO $con
     * @return Presence
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectOne(Criteria $criteria, PropelPDO $con = null)
    {
        $critcopy = clone $criteria;
        $critcopy->setLimit(1);
        $objects = PresencePeer::doSelect($critcopy, $con);
        if ($objects) {
            return $objects[0];
        }

        return null;
    }
    /**
     * Selects several row from the DB.
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con
     * @return array           Array of selected Objects
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelect(Criteria $criteria, PropelPDO $con = null)
    {
        return PresencePeer::populateObjects(PresencePeer::doSelectStmt($criteria, $con));
    }
    /**
     * Prepares the Criteria object and uses the parent doSelect() method to execute a PDOStatement.
     *
     * Use this method directly if you want to work with an executed statement directly (for example
     * to perform your own object hydration).
     *
     * @param      Criteria $criteria The Criteria object used to build the SELECT statement.
     * @param      PropelPDO $con The connection to use
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return PDOStatement The executed PDOStatement object.
     * @see        BasePeer::doSelect()
     */
    public static function doSelectStmt(Criteria $criteria, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        if (!$criteria->hasSelectClause()) {
            $criteria = clone $criteria;
            PresencePeer::addSelectColumns($criteria);
        }

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        // BasePeer returns a PDOStatement
        return BasePeer::doSelect($criteria, $con);
    }
    /**
     * Adds an object to the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doSelect*()
     * methods in your stub classes -- you may need to explicitly add objects
     * to the cache in order to ensure that the same objects are always returned by doSelect*()
     * and retrieveByPK*() calls.
     *
     * @param Presence $obj A Presence object.
     * @param      string $key (optional) key to use for instance map (for performance boost if key was already calculated externally).
     */
    public static function addInstanceToPool($obj, $key = null)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if ($key === null) {
                $key = (string) $obj->getPersonnelPresenceId();
            } // if key === null
            PresencePeer::$instances[$key] = $obj;
        }
    }

    /**
     * Removes an object from the instance pool.
     *
     * Propel keeps cached copies of objects in an instance pool when they are retrieved
     * from the database.  In some cases -- especially when you override doDelete
     * methods in your stub classes -- you may need to explicitly remove objects
     * from the cache in order to prevent returning objects that no longer exist.
     *
     * @param      mixed $value A Presence object or a primary key value.
     *
     * @return void
     * @throws PropelException - if the value is invalid.
     */
    public static function removeInstanceFromPool($value)
    {
        if (Propel::isInstancePoolingEnabled() && $value !== null) {
            if (is_object($value) && $value instanceof Presence) {
                $key = (string) $value->getPersonnelPresenceId();
            } elseif (is_scalar($value)) {
                // assume we've been passed a primary key
                $key = (string) $value;
            } else {
                $e = new PropelException("Invalid value passed to removeInstanceFromPool().  Expected primary key or Presence object; got " . (is_object($value) ? get_class($value) . ' object.' : var_export($value,true)));
                throw $e;
            }

            unset(PresencePeer::$instances[$key]);
        }
    } // removeInstanceFromPool()

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      string $key The key (@see getPrimaryKeyHash()) for this instance.
     * @return Presence Found object or null if 1) no instance exists for specified key or 2) instance pooling has been disabled.
     * @see        getPrimaryKeyHash()
     */
    public static function getInstanceFromPool($key)
    {
        if (Propel::isInstancePoolingEnabled()) {
            if (isset(PresencePeer::$instances[$key])) {
                return PresencePeer::$instances[$key];
            }
        }

        return null; // just to be explicit
    }

    /**
     * Clear the instance pool.
     *
     * @return void
     */
    public static function clearInstancePool($and_clear_all_references = false)
    {
      if ($and_clear_all_references) {
        foreach (PresencePeer::$instances as $instance) {
          $instance->clearAllReferences(true);
        }
      }
        PresencePeer::$instances = array();
    }

    /**
     * Method to invalidate the instance pool of all tables related to personnel_presences
     * by a foreign key with ON DELETE CASCADE
     */
    public static function clearRelatedInstancePool()
    {
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return string A string version of PK or null if the components of primary key in result array are all null.
     */
    public static function getPrimaryKeyHashFromRow($row, $startcol = 0)
    {
        // If the PK cannot be derived from the row, return null.
        if ($row[$startcol] === null) {
            return null;
        }

        return (string) $row[$startcol];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow($row, $startcol = 0)
    {

        return (int) $row[$startcol];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function populateObjects(PDOStatement $stmt)
    {
        $results = array();

        // set the class once to avoid overhead in the loop
        $cls = PresencePeer::getOMClass();
        // populate the object(s)
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key = PresencePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj = PresencePeer::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                PresencePeer::addInstanceToPool($obj, $key);
            } // if key exists
        }
        $stmt->closeCursor();

        return $results;
    }
    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param      array $row PropelPDO resultset row.
     * @param      int $startcol The 0-based offset for reading from the resultset row.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     * @return array (Presence object, last column rank)
     */
    public static function populateObject($row, $startcol = 0)
    {
        $key = PresencePeer::getPrimaryKeyHashFromRow($row, $startcol);
        if (null !== ($obj = PresencePeer::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $startcol, true); // rehydrate
            $col = $startcol + PresencePeer::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = PresencePeer::OM_CLASS;
            $obj = new $cls();
            $col = $obj->hydrate($row, $startcol);
            PresencePeer::addInstanceToPool($obj, $key);
        }

        return array($obj, $col);
    }


    /**
     * Returns the number of rows matching criteria, joining the related PresenceEmployee table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinPresenceEmployee(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(PresencePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            PresencePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(PresencePeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related PresenceProject table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinPresenceProject(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(PresencePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            PresencePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(PresencePeer::PROJECT_EDIT_ID, ActivityPeer::ACTIV_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Presence objects pre-filled with their Employee objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Presence objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinPresenceEmployee(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(PresencePeer::DATABASE_NAME);
        }

        PresencePeer::addSelectColumns($criteria);
        $startcol = PresencePeer::NUM_HYDRATE_COLUMNS;
        EmployeePeer::addSelectColumns($criteria);

        $criteria->addJoin(PresencePeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = PresencePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = PresencePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = PresencePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                PresencePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = EmployeePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Presence) to $obj2 (Employee)
                $obj2->addPresence($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Presence objects pre-filled with their Activity objects.
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Presence objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinPresenceProject(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(PresencePeer::DATABASE_NAME);
        }

        PresencePeer::addSelectColumns($criteria);
        $startcol = PresencePeer::NUM_HYDRATE_COLUMNS;
        ActivityPeer::addSelectColumns($criteria);

        $criteria->addJoin(PresencePeer::PROJECT_EDIT_ID, ActivityPeer::ACTIV_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = PresencePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = PresencePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {

                $cls = PresencePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                PresencePeer::addInstanceToPool($obj1, $key1);
            } // if $obj1 already loaded

            $key2 = ActivityPeer::getPrimaryKeyHashFromRow($row, $startcol);
            if ($key2 !== null) {
                $obj2 = ActivityPeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = ActivityPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol);
                    ActivityPeer::addInstanceToPool($obj2, $key2);
                } // if obj2 already loaded

                // Add the $obj1 (Presence) to $obj2 (Activity)
                $obj2->addPresence($obj1);

            } // if joined row was not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining all related tables
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAll(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(PresencePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            PresencePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY won't ever affect the count

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(PresencePeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $criteria->addJoin(PresencePeer::PROJECT_EDIT_ID, ActivityPeer::ACTIV_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }

    /**
     * Selects a collection of Presence objects pre-filled with all related objects.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Presence objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAll(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(PresencePeer::DATABASE_NAME);
        }

        PresencePeer::addSelectColumns($criteria);
        $startcol2 = PresencePeer::NUM_HYDRATE_COLUMNS;

        EmployeePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + EmployeePeer::NUM_HYDRATE_COLUMNS;

        ActivityPeer::addSelectColumns($criteria);
        $startcol4 = $startcol3 + ActivityPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(PresencePeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $criteria->addJoin(PresencePeer::PROJECT_EDIT_ID, ActivityPeer::ACTIV_ID, $join_behavior);

        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = PresencePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = PresencePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = PresencePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                PresencePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

            // Add objects for joined Employee rows

            $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol2);
            if ($key2 !== null) {
                $obj2 = EmployeePeer::getInstanceFromPool($key2);
                if (!$obj2) {

                    $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if obj2 loaded

                // Add the $obj1 (Presence) to the collection in $obj2 (Employee)
                $obj2->addPresence($obj1);
            } // if joined row not null

            // Add objects for joined Activity rows

            $key3 = ActivityPeer::getPrimaryKeyHashFromRow($row, $startcol3);
            if ($key3 !== null) {
                $obj3 = ActivityPeer::getInstanceFromPool($key3);
                if (!$obj3) {

                    $cls = ActivityPeer::getOMClass();

                    $obj3 = new $cls();
                    $obj3->hydrate($row, $startcol3);
                    ActivityPeer::addInstanceToPool($obj3, $key3);
                } // if obj3 loaded

                // Add the $obj1 (Presence) to the collection in $obj3 (Activity)
                $obj3->addPresence($obj1);
            } // if joined row not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Returns the number of rows matching criteria, joining the related PresenceEmployee table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptPresenceEmployee(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(PresencePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            PresencePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(PresencePeer::PROJECT_EDIT_ID, ActivityPeer::ACTIV_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Returns the number of rows matching criteria, joining the related PresenceProject table
     *
     * @param      Criteria $criteria
     * @param      boolean $distinct Whether to select only distinct columns; deprecated: use Criteria->setDistinct() instead.
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return int Number of matching rows.
     */
    public static function doCountJoinAllExceptPresenceProject(Criteria $criteria, $distinct = false, PropelPDO $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        // we're going to modify criteria, so copy it first
        $criteria = clone $criteria;

        // We need to set the primary table name, since in the case that there are no WHERE columns
        // it will be impossible for the BasePeer::createSelectSql() method to determine which
        // tables go into the FROM clause.
        $criteria->setPrimaryTableName(PresencePeer::TABLE_NAME);

        if ($distinct && !in_array(Criteria::DISTINCT, $criteria->getSelectModifiers())) {
            $criteria->setDistinct();
        }

        if (!$criteria->hasSelectClause()) {
            PresencePeer::addSelectColumns($criteria);
        }

        $criteria->clearOrderByColumns(); // ORDER BY should not affect count

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria->addJoin(PresencePeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);

        $stmt = BasePeer::doCount($criteria, $con);

        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $count = (int) $row[0];
        } else {
            $count = 0; // no rows returned; we infer that means 0 matches.
        }
        $stmt->closeCursor();

        return $count;
    }


    /**
     * Selects a collection of Presence objects pre-filled with all related objects except PresenceEmployee.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Presence objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptPresenceEmployee(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(PresencePeer::DATABASE_NAME);
        }

        PresencePeer::addSelectColumns($criteria);
        $startcol2 = PresencePeer::NUM_HYDRATE_COLUMNS;

        ActivityPeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + ActivityPeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(PresencePeer::PROJECT_EDIT_ID, ActivityPeer::ACTIV_ID, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = PresencePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = PresencePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = PresencePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                PresencePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined Activity rows

                $key2 = ActivityPeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = ActivityPeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = ActivityPeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    ActivityPeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Presence) to the collection in $obj2 (Activity)
                $obj2->addPresence($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }


    /**
     * Selects a collection of Presence objects pre-filled with all related objects except PresenceProject.
     *
     * @param      Criteria  $criteria
     * @param      PropelPDO $con
     * @param      String    $join_behavior the type of joins to use, defaults to Criteria::LEFT_JOIN
     * @return array           Array of Presence objects.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doSelectJoinAllExceptPresenceProject(Criteria $criteria, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $criteria = clone $criteria;

        // Set the correct dbName if it has not been overridden
        // $criteria->getDbName() will return the same object if not set to another value
        // so == check is okay and faster
        if ($criteria->getDbName() == Propel::getDefaultDB()) {
            $criteria->setDbName(PresencePeer::DATABASE_NAME);
        }

        PresencePeer::addSelectColumns($criteria);
        $startcol2 = PresencePeer::NUM_HYDRATE_COLUMNS;

        EmployeePeer::addSelectColumns($criteria);
        $startcol3 = $startcol2 + EmployeePeer::NUM_HYDRATE_COLUMNS;

        $criteria->addJoin(PresencePeer::EMPLOYEE_ID, EmployeePeer::EMPLOYEE_ID, $join_behavior);


        $stmt = BasePeer::doSelect($criteria, $con);
        $results = array();

        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $key1 = PresencePeer::getPrimaryKeyHashFromRow($row, 0);
            if (null !== ($obj1 = PresencePeer::getInstanceFromPool($key1))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj1->hydrate($row, 0, true); // rehydrate
            } else {
                $cls = PresencePeer::getOMClass();

                $obj1 = new $cls();
                $obj1->hydrate($row);
                PresencePeer::addInstanceToPool($obj1, $key1);
            } // if obj1 already loaded

                // Add objects for joined Employee rows

                $key2 = EmployeePeer::getPrimaryKeyHashFromRow($row, $startcol2);
                if ($key2 !== null) {
                    $obj2 = EmployeePeer::getInstanceFromPool($key2);
                    if (!$obj2) {

                        $cls = EmployeePeer::getOMClass();

                    $obj2 = new $cls();
                    $obj2->hydrate($row, $startcol2);
                    EmployeePeer::addInstanceToPool($obj2, $key2);
                } // if $obj2 already loaded

                // Add the $obj1 (Presence) to the collection in $obj2 (Employee)
                $obj2->addPresence($obj1);

            } // if joined row is not null

            $results[] = $obj1;
        }
        $stmt->closeCursor();

        return $results;
    }

    /**
     * Returns the TableMap related to this peer.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function getTableMap()
    {
        return Propel::getDatabaseMap(PresencePeer::DATABASE_NAME)->getTable(PresencePeer::TABLE_NAME);
    }

    /**
     * Add a TableMap instance to the database for this peer class.
     */
    public static function buildTableMap()
    {
      $dbMap = Propel::getDatabaseMap(BasePresencePeer::DATABASE_NAME);
      if (!$dbMap->hasTable(BasePresencePeer::TABLE_NAME)) {
        $dbMap->addTableObject(new PresenceTableMap());
      }
    }

    /**
     * The class that the Peer will make instances of.
     *
     *
     * @return string ClassName
     */
    public static function getOMClass($row = 0, $colnum = 0)
    {
        return PresencePeer::OM_CLASS;
    }

    /**
     * Performs an INSERT on the database, given a Presence or Criteria object.
     *
     * @param      mixed $values Criteria or Presence object containing data that is used to create the INSERT statement.
     * @param      PropelPDO $con the PropelPDO connection to use
     * @return mixed           The new primary key.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doInsert($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity
        } else {
            $criteria = $values->buildCriteria(); // build Criteria from Presence object
        }

        if ($criteria->containsKey(PresencePeer::PERSONNEL_PRESENCE_ID) && $criteria->keyContainsValue(PresencePeer::PERSONNEL_PRESENCE_ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.PresencePeer::PERSONNEL_PRESENCE_ID.')');
        }


        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        try {
            // use transaction because $criteria could contain info
            // for more than one table (I guess, conceivably)
            $con->beginTransaction();
            $pk = BasePeer::doInsert($criteria, $con);
            $con->commit();
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }

        return $pk;
    }

    /**
     * Performs an UPDATE on the database, given a Presence or Criteria object.
     *
     * @param      mixed $values Criteria or Presence object containing data that is used to create the UPDATE statement.
     * @param      PropelPDO $con The connection to use (specify PropelPDO connection object to exert more control over transactions).
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function doUpdate($values, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $selectCriteria = new Criteria(PresencePeer::DATABASE_NAME);

        if ($values instanceof Criteria) {
            $criteria = clone $values; // rename for clarity

            $comparison = $criteria->getComparison(PresencePeer::PERSONNEL_PRESENCE_ID);
            $value = $criteria->remove(PresencePeer::PERSONNEL_PRESENCE_ID);
            if ($value) {
                $selectCriteria->add(PresencePeer::PERSONNEL_PRESENCE_ID, $value, $comparison);
            } else {
                $selectCriteria->setPrimaryTableName(PresencePeer::TABLE_NAME);
            }

        } else { // $values is Presence object
            $criteria = $values->buildCriteria(); // gets full criteria
            $selectCriteria = $values->buildPkeyCriteria(); // gets criteria w/ primary key(s)
        }

        // set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        return BasePeer::doUpdate($selectCriteria, $criteria, $con);
    }

    /**
     * Deletes all rows from the personnel_presences table.
     *
     * @param      PropelPDO $con the connection to use
     * @return int             The number of affected rows (if supported by underlying database driver).
     * @throws PropelException
     */
    public static function doDeleteAll(PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }
        $affectedRows = 0; // initialize var to track total num of affected rows
        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();
            $affectedRows += BasePeer::doDeleteAll(PresencePeer::TABLE_NAME, $con, PresencePeer::DATABASE_NAME);
            // Because this db requires some delete cascade/set null emulation, we have to
            // clear the cached instance *after* the emulation has happened (since
            // instances get re-added by the select statement contained therein).
            PresencePeer::clearInstancePool();
            PresencePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs a DELETE on the database, given a Presence or Criteria object OR a primary key value.
     *
     * @param      mixed $values Criteria or Presence object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param      PropelPDO $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *				if supported by native driver or if emulated using Propel.
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, PropelPDO $con = null)
     {
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        if ($values instanceof Criteria) {
            // invalidate the cache for all objects of this type, since we have no
            // way of knowing (without running a query) what objects should be invalidated
            // from the cache based on this Criteria.
            PresencePeer::clearInstancePool();
            // rename for clarity
            $criteria = clone $values;
        } elseif ($values instanceof Presence) { // it's a model object
            // invalidate the cache for this single object
            PresencePeer::removeInstanceFromPool($values);
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(PresencePeer::DATABASE_NAME);
            $criteria->add(PresencePeer::PERSONNEL_PRESENCE_ID, (array) $values, Criteria::IN);
            // invalidate the cache for this object(s)
            foreach ((array) $values as $singleval) {
                PresencePeer::removeInstanceFromPool($singleval);
            }
        }

        // Set the correct dbName
        $criteria->setDbName(PresencePeer::DATABASE_NAME);

        $affectedRows = 0; // initialize var to track total num of affected rows

        try {
            // use transaction because $criteria could contain info
            // for more than one table or we could emulating ON DELETE CASCADE, etc.
            $con->beginTransaction();

            $affectedRows += BasePeer::doDelete($criteria, $con);
            PresencePeer::clearRelatedInstancePool();
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Validates all modified columns of given Presence object.
     * If parameter $columns is either a single column name or an array of column names
     * than only those columns are validated.
     *
     * NOTICE: This does not apply to primary or foreign keys for now.
     *
     * @param Presence $obj The object to validate.
     * @param      mixed $cols Column name or array of column names.
     *
     * @return mixed TRUE if all columns are valid or the error message of the first invalid column.
     */
    public static function doValidate($obj, $cols = null)
    {
        $columns = array();

        if ($cols) {
            $dbMap = Propel::getDatabaseMap(PresencePeer::DATABASE_NAME);
            $tableMap = $dbMap->getTable(PresencePeer::TABLE_NAME);

            if (! is_array($cols)) {
                $cols = array($cols);
            }

            foreach ($cols as $colName) {
                if ($tableMap->hasColumn($colName)) {
                    $get = 'get' . $tableMap->getColumn($colName)->getPhpName();
                    $columns[$colName] = $obj->$get();
                }
            }
        } else {

        }

        return BasePeer::doValidate(PresencePeer::DATABASE_NAME, PresencePeer::TABLE_NAME, $columns);
    }

    /**
     * Retrieve a single object by pkey.
     *
     * @param int $pk the primary key.
     * @param      PropelPDO $con the connection to use
     * @return Presence
     */
    public static function retrieveByPK($pk, PropelPDO $con = null)
    {

        if (null !== ($obj = PresencePeer::getInstanceFromPool((string) $pk))) {
            return $obj;
        }

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $criteria = new Criteria(PresencePeer::DATABASE_NAME);
        $criteria->add(PresencePeer::PERSONNEL_PRESENCE_ID, $pk);

        $v = PresencePeer::doSelect($criteria, $con);

        return !empty($v) > 0 ? $v[0] : null;
    }

    /**
     * Retrieve multiple objects by pkey.
     *
     * @param      array $pks List of primary keys
     * @param      PropelPDO $con the connection to use
     * @return Presence[]
     * @throws PropelException Any exceptions caught during processing will be
     *		 rethrown wrapped into a PropelException.
     */
    public static function retrieveByPKs($pks, PropelPDO $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        $objs = null;
        if (empty($pks)) {
            $objs = array();
        } else {
            $criteria = new Criteria(PresencePeer::DATABASE_NAME);
            $criteria->add(PresencePeer::PERSONNEL_PRESENCE_ID, $pks, Criteria::IN);
            $objs = PresencePeer::doSelect($criteria, $con);
        }

        return $objs;
    }

} // BasePresencePeer

// This is the static code needed to register the TableMap for this table with the main Propel class.
//
BasePresencePeer::buildTableMap();

