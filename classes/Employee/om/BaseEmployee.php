<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Core\Contact;
use Core\ContactQuery;
use Core\Institute;
use Core\InstituteQuery;
use Employee\Absences;
use Employee\AbsencesQuery;
use Employee\Employee;
use Employee\EmployeePeer;
use Employee\EmployeeQuery;
use Employee\PersonnelStacks;
use Employee\PersonnelStacksQuery;
use Employee\Presence;
use Employee\PresenceQuery;
use Employee\StoredDay;
use Employee\StoredDayQuery;
use Employee\StoredMonth;
use Employee\StoredMonthQuery;
use Employee\Timetable;
use Employee\TimetableQuery;

/**
 * Base class that represents a row from the 'employee' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseEmployee extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\EmployeePeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        EmployeePeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the employee_id field.
     * @var        int
     */
    protected $employee_id;

    /**
     * The value for the name field.
     * @var        string
     */
    protected $name;

    /**
     * The value for the surname field.
     * @var        string
     */
    protected $surname;

    /**
     * The value for the gender field.
     * @var        string
     */
    protected $gender;

    /**
     * The value for the birthdate field.
     * @var        string
     */
    protected $birthdate;

    /**
     * The value for the fiscal_code field.
     * @var        string
     */
    protected $fiscal_code;

    /**
     * The value for the residence_id field.
     * @var        int
     */
    protected $residence_id;

    /**
     * The value for the address_id field.
     * @var        int
     */
    protected $address_id;

    /**
     * The value for the part_spesa field.
     * @var        string
     */
    protected $part_spesa;

    /**
     * The value for the bank field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $bank;

    /**
     * The value for the liq_office field.
     * @var        string
     */
    protected $liq_office;

    /**
     * The value for the inps field.
     * @var        string
     */
    protected $inps;

    /**
     * The value for the insur_qual field.
     * @var        string
     */
    protected $insur_qual;

    /**
     * The value for the fore field.
     * @var        boolean
     */
    protected $fore;

    /**
     * The value for the asl field.
     * @var        string
     */
    protected $asl;

    /**
     * The value for the adm_code field.
     * @var        string
     */
    protected $adm_code;

    /**
     * The value for the way_pay field.
     * @var        string
     */
    protected $way_pay;

    /**
     * The value for the liquid_group field.
     * @var        string
     */
    protected $liquid_group;

    /**
     * The value for the contr_code field.
     * @var        string
     */
    protected $contr_code;

    /**
     * The value for the contr_type field.
     * @var        string
     */
    protected $contr_type;

    /**
     * The value for the contr_cat field.
     * @var        int
     */
    protected $contr_cat;

    /**
     * The value for the ssp_frm_pmnt field.
     * @var        int
     */
    protected $ssp_frm_pmnt;

    /**
     * The value for the personal_data field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $personal_data;

    /**
     * The value for the susp field.
     * @var        boolean
     */
    protected $susp;

    /**
     * The value for the payment_group field.
     * @var        int
     */
    protected $payment_group;

    /**
     * The value for the priv_ret_type field.
     * @var        string
     */
    protected $priv_ret_type;

    /**
     * The value for the social_position field.
     * @var        int
     */
    protected $social_position;

    /**
     * The value for the active field.
     * Note: this column has a database default value of: true
     * @var        boolean
     */
    protected $active;

    /**
     * The value for the statal_code field.
     * @var        string
     */
    protected $statal_code;

    /**
     * The value for the fiscal_city_code field.
     * @var        string
     */
    protected $fiscal_city_code;

    /**
     * The value for the birthplace field.
     * @var        string
     */
    protected $birthplace;

    /**
     * The value for the income field.
     * Note: this column has a database default value of: '0'
     * @var        string
     */
    protected $income;

    /**
     * The value for the state_birth field.
     * @var        string
     */
    protected $state_birth;

    /**
     * The value for the citizenship field.
     * @var        string
     */
    protected $citizenship;

    /**
     * The value for the id_sissi field.
     * @var        string
     */
    protected $id_sissi;

    /**
     * The value for the dom_first_prev_year field.
     * @var        int
     */
    protected $dom_first_prev_year;

    /**
     * The value for the dom_last_prev_year field.
     * @var        int
     */
    protected $dom_last_prev_year;

    /**
     * The value for the dom_first_curr_year field.
     * @var        int
     */
    protected $dom_first_curr_year;

    /**
     * The value for the qualification field.
     * @var        string
     */
    protected $qualification;

    /**
     * The value for the liquid_office_id field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $liquid_office_id;

    /**
     * The value for the badge_number field.
     * @var        string
     */
    protected $badge_number;

    /**
     * The value for the tolerance_in field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $tolerance_in;

    /**
     * The value for the tolerance_out field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $tolerance_out;

    /**
     * The value for the flexibility field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $flexibility;

    /**
     * The value for the generic_tolerance field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $generic_tolerance;

    /**
     * The value for the negative_round field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $negative_round;

    /**
     * The value for the recover_hours field.
     * Note: this column has a database default value of: 100
     * @var        int
     */
    protected $recover_hours;

    /**
     * The value for the max_extraordinary_in field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_extraordinary_in;

    /**
     * The value for the max_extraordinary_out field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_extraordinary_out;

    /**
     * The value for the min_extraordinary_in field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_extraordinary_in;

    /**
     * The value for the min_extraordinary_out field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_extraordinary_out;

    /**
     * The value for the step_out field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_out;

    /**
     * The value for the step_in field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_in;

    /**
     * The value for the max_break field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $max_break;

    /**
     * The value for the max_cont_work field.
     * Note: this column has a database default value of: 720
     * @var        int
     */
    protected $max_cont_work;

    /**
     * The value for the simplified_ata_settings field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $simplified_ata_settings;

    /**
     * The value for the tolerance_in_und field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $tolerance_in_und;

    /**
     * The value for the tolerance_out_und field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $tolerance_out_und;

    /**
     * The value for the max_undefined_in field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_undefined_in;

    /**
     * The value for the max_undefined_out field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_undefined_out;

    /**
     * The value for the min_undefined_in field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_undefined_in;

    /**
     * The value for the min_undefined_out field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_undefined_out;

    /**
     * The value for the step_out_und field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_out_und;

    /**
     * The value for the step_in_und field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_in_und;

    /**
     * The value for the undefined_parameter_active field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $undefined_parameter_active;

    /**
     * The value for the min_extraordinary_total field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_extraordinary_total;

    /**
     * The value for the max_extraordinary_total field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_extraordinary_total;

    /**
     * The value for the min_undefined_total field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_undefined_total;

    /**
     * The value for the max_undefined_total field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_undefined_total;

    /**
     * The value for the step_total_undefined field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_total_undefined;

    /**
     * The value for the step_total_extraordinary field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_total_extraordinary;

    /**
     * The value for the lunch_duration field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $lunch_duration;

    /**
     * The value for the lunch_deductible field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $lunch_deductible;

    /**
     * The value for the service_deductible field.
     * Note: this column has a database default value of: false
     * @var        boolean
     */
    protected $service_deductible;

    /**
     * The value for the min_undefined_lunch field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_undefined_lunch;

    /**
     * The value for the min_extraordinary_lunch field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $min_extraordinary_lunch;

    /**
     * The value for the max_undefined_lunch field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $max_undefined_lunch;

    /**
     * The value for the max_extraordinary_lunch field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $max_extraordinary_lunch;

    /**
     * The value for the step_lunch_undefined field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_lunch_undefined;

    /**
     * The value for the step_lunch_extraordinary field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $step_lunch_extraordinary;

    /**
     * The value for the break_after_max_work field.
     * Note: this column has a database default value of: 0
     * @var        int
     */
    protected $break_after_max_work;

    /**
     * The value for the unit_recover_hours field.
     * Note: this column has a database default value of: '%'
     * @var        string
     */
    protected $unit_recover_hours;

    /**
     * The value for the max_work field.
     * Note: this column has a database default value of: 999
     * @var        int
     */
    protected $max_work;

    /**
     * @var        Contact
     */
    protected $aResidenceKey;

    /**
     * @var        Contact
     */
    protected $aAddressKey;

    /**
     * @var        PropelObjectCollection|Institute[] Collection to store aggregation of Institute objects.
     */
    protected $collInstitutes;
    protected $collInstitutesPartial;

    /**
     * @var        PropelObjectCollection|Absences[] Collection to store aggregation of Absences objects.
     */
    protected $collAbsencess;
    protected $collAbsencessPartial;

    /**
     * @var        PropelObjectCollection|Timetable[] Collection to store aggregation of Timetable objects.
     */
    protected $collTimetables;
    protected $collTimetablesPartial;

    /**
     * @var        PropelObjectCollection|Presence[] Collection to store aggregation of Presence objects.
     */
    protected $collPresences;
    protected $collPresencesPartial;

    /**
     * @var        PropelObjectCollection|PersonnelStacks[] Collection to store aggregation of PersonnelStacks objects.
     */
    protected $collPersonnelStackss;
    protected $collPersonnelStackssPartial;

    /**
     * @var        PropelObjectCollection|StoredMonth[] Collection to store aggregation of StoredMonth objects.
     */
    protected $collStoredMonths;
    protected $collStoredMonthsPartial;

    /**
     * @var        PropelObjectCollection|StoredDay[] Collection to store aggregation of StoredDay objects.
     */
    protected $collStoredDays;
    protected $collStoredDaysPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $institutesScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $absencessScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $timetablesScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $presencesScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $personnelStackssScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $storedMonthsScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $storedDaysScheduledForDeletion = null;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->bank = 0;
        $this->personal_data = 0;
        $this->active = true;
        $this->income = '0';
        $this->liquid_office_id = 0;
        $this->tolerance_in = 0;
        $this->tolerance_out = 0;
        $this->flexibility = 0;
        $this->generic_tolerance = 0;
        $this->negative_round = 0;
        $this->recover_hours = 100;
        $this->max_extraordinary_in = 999;
        $this->max_extraordinary_out = 999;
        $this->min_extraordinary_in = 0;
        $this->min_extraordinary_out = 0;
        $this->step_out = 0;
        $this->step_in = 0;
        $this->max_break = 0;
        $this->max_cont_work = 720;
        $this->simplified_ata_settings = false;
        $this->tolerance_in_und = 0;
        $this->tolerance_out_und = 0;
        $this->max_undefined_in = 999;
        $this->max_undefined_out = 999;
        $this->min_undefined_in = 0;
        $this->min_undefined_out = 0;
        $this->step_out_und = 0;
        $this->step_in_und = 0;
        $this->undefined_parameter_active = false;
        $this->min_extraordinary_total = 0;
        $this->max_extraordinary_total = 999;
        $this->min_undefined_total = 0;
        $this->max_undefined_total = 999;
        $this->step_total_undefined = 0;
        $this->step_total_extraordinary = 0;
        $this->lunch_duration = 0;
        $this->lunch_deductible = false;
        $this->service_deductible = false;
        $this->min_undefined_lunch = 0;
        $this->min_extraordinary_lunch = 0;
        $this->max_undefined_lunch = 0;
        $this->max_extraordinary_lunch = 0;
        $this->step_lunch_undefined = 0;
        $this->step_lunch_extraordinary = 0;
        $this->break_after_max_work = 0;
        $this->unit_recover_hours = '%';
        $this->max_work = 999;
    }

    /**
     * Initializes internal state of BaseEmployee object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return int
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [name] column value.
     *
     * @return string
     */
    public function getName()
    {

        return $this->name;
    }

    /**
     * Get the [surname] column value.
     *
     * @return string
     */
    public function getSurname()
    {

        return $this->surname;
    }

    /**
     * Get the [gender] column value.
     *
     * @return string
     */
    public function getGender()
    {

        return $this->gender;
    }

    /**
     * Get the [birthdate] column value.
     *
     * @return string
     */
    public function getBirthdate()
    {

        return $this->birthdate;
    }

    /**
     * Get the [fiscal_code] column value.
     *
     * @return string
     */
    public function getFiscalCode()
    {

        return $this->fiscal_code;
    }

    /**
     * Get the [residence_id] column value.
     *
     * @return int
     */
    public function getResidenceId()
    {

        return $this->residence_id;
    }

    /**
     * Get the [address_id] column value.
     *
     * @return int
     */
    public function getAddressId()
    {

        return $this->address_id;
    }

    /**
     * Get the [part_spesa] column value.
     *
     * @return string
     */
    public function getPartSpesa()
    {

        return $this->part_spesa;
    }

    /**
     * Get the [bank] column value.
     *
     * @return int
     */
    public function getBank()
    {

        return $this->bank;
    }

    /**
     * Get the [liq_office] column value.
     *
     * @return string
     */
    public function getLiqOffice()
    {

        return $this->liq_office;
    }

    /**
     * Get the [inps] column value.
     *
     * @return string
     */
    public function getInps()
    {

        return $this->inps;
    }

    /**
     * Get the [insur_qual] column value.
     *
     * @return string
     */
    public function getInsurQual()
    {

        return $this->insur_qual;
    }

    /**
     * Get the [fore] column value.
     *
     * @return boolean
     */
    public function getFore()
    {

        return $this->fore;
    }

    /**
     * Get the [asl] column value.
     *
     * @return string
     */
    public function getAsl()
    {

        return $this->asl;
    }

    /**
     * Get the [adm_code] column value.
     *
     * @return string
     */
    public function getAdmCode()
    {

        return $this->adm_code;
    }

    /**
     * Get the [way_pay] column value.
     *
     * @return string
     */
    public function getWayPay()
    {

        return $this->way_pay;
    }

    /**
     * Get the [liquid_group] column value.
     *
     * @return string
     */
    public function getLiquidGroup()
    {

        return $this->liquid_group;
    }

    /**
     * Get the [contr_code] column value.
     *
     * @return string
     */
    public function getContrCode()
    {

        return $this->contr_code;
    }

    /**
     * Get the [contr_type] column value.
     *
     * @return string
     */
    public function getContrType()
    {

        return $this->contr_type;
    }

    /**
     * Get the [contr_cat] column value.
     *
     * @return int
     */
    public function getContrCat()
    {

        return $this->contr_cat;
    }

    /**
     * Get the [ssp_frm_pmnt] column value.
     *
     * @return int
     */
    public function getSspFrmPmnt()
    {

        return $this->ssp_frm_pmnt;
    }

    /**
     * Get the [personal_data] column value.
     *
     * @return int
     */
    public function getPersonalData()
    {

        return $this->personal_data;
    }

    /**
     * Get the [susp] column value.
     *
     * @return boolean
     */
    public function getSusp()
    {

        return $this->susp;
    }

    /**
     * Get the [payment_group] column value.
     *
     * @return int
     */
    public function getPaymentGroup()
    {

        return $this->payment_group;
    }

    /**
     * Get the [priv_ret_type] column value.
     *
     * @return string
     */
    public function getPrivRetType()
    {

        return $this->priv_ret_type;
    }

    /**
     * Get the [social_position] column value.
     *
     * @return int
     */
    public function getSocialPosition()
    {

        return $this->social_position;
    }

    /**
     * Get the [active] column value.
     *
     * @return boolean
     */
    public function getActive()
    {

        return $this->active;
    }

    /**
     * Get the [statal_code] column value.
     *
     * @return string
     */
    public function getStatalCode()
    {

        return $this->statal_code;
    }

    /**
     * Get the [fiscal_city_code] column value.
     *
     * @return string
     */
    public function getFiscalCityCode()
    {

        return $this->fiscal_city_code;
    }

    /**
     * Get the [birthplace] column value.
     *
     * @return string
     */
    public function getBirthplace()
    {

        return $this->birthplace;
    }

    /**
     * Get the [income] column value.
     *
     * @return string
     */
    public function getIncome()
    {

        return $this->income;
    }

    /**
     * Get the [state_birth] column value.
     *
     * @return string
     */
    public function getStateBirth()
    {

        return $this->state_birth;
    }

    /**
     * Get the [citizenship] column value.
     *
     * @return string
     */
    public function getCitizenship()
    {

        return $this->citizenship;
    }

    /**
     * Get the [id_sissi] column value.
     *
     * @return string
     */
    public function getIdSissi()
    {

        return $this->id_sissi;
    }

    /**
     * Get the [dom_first_prev_year] column value.
     *
     * @return int
     */
    public function getDomFirstPrevYear()
    {

        return $this->dom_first_prev_year;
    }

    /**
     * Get the [dom_last_prev_year] column value.
     *
     * @return int
     */
    public function getDomLastPrevYear()
    {

        return $this->dom_last_prev_year;
    }

    /**
     * Get the [dom_first_curr_year] column value.
     *
     * @return int
     */
    public function getDomFirstCurrYear()
    {

        return $this->dom_first_curr_year;
    }

    /**
     * Get the [qualification] column value.
     *
     * @return string
     */
    public function getQualification()
    {

        return $this->qualification;
    }

    /**
     * Get the [liquid_office_id] column value.
     *
     * @return int
     */
    public function getLiquidOfficeId()
    {

        return $this->liquid_office_id;
    }

    /**
     * Get the [badge_number] column value.
     *
     * @return string
     */
    public function getBadgeNumber()
    {

        return $this->badge_number;
    }

    /**
     * Get the [tolerance_in] column value.
     *
     * @return int
     */
    public function getToleranceIn()
    {

        return $this->tolerance_in;
    }

    /**
     * Get the [tolerance_out] column value.
     *
     * @return int
     */
    public function getToleranceOut()
    {

        return $this->tolerance_out;
    }

    /**
     * Get the [flexibility] column value.
     *
     * @return int
     */
    public function getFlexibility()
    {

        return $this->flexibility;
    }

    /**
     * Get the [generic_tolerance] column value.
     *
     * @return int
     */
    public function getGenericTolerance()
    {

        return $this->generic_tolerance;
    }

    /**
     * Get the [negative_round] column value.
     *
     * @return int
     */
    public function getNegativeRound()
    {

        return $this->negative_round;
    }

    /**
     * Get the [recover_hours] column value.
     *
     * @return int
     */
    public function getRecoverHours()
    {

        return $this->recover_hours;
    }

    /**
     * Get the [max_extraordinary_in] column value.
     *
     * @return int
     */
    public function getMaxExtraordinaryIn()
    {

        return $this->max_extraordinary_in;
    }

    /**
     * Get the [max_extraordinary_out] column value.
     *
     * @return int
     */
    public function getMaxExtraordinaryOut()
    {

        return $this->max_extraordinary_out;
    }

    /**
     * Get the [min_extraordinary_in] column value.
     *
     * @return int
     */
    public function getMinExtraordinaryIn()
    {

        return $this->min_extraordinary_in;
    }

    /**
     * Get the [min_extraordinary_out] column value.
     *
     * @return int
     */
    public function getMinExtraordinaryOut()
    {

        return $this->min_extraordinary_out;
    }

    /**
     * Get the [step_out] column value.
     *
     * @return int
     */
    public function getStepOut()
    {

        return $this->step_out;
    }

    /**
     * Get the [step_in] column value.
     *
     * @return int
     */
    public function getStepIn()
    {

        return $this->step_in;
    }

    /**
     * Get the [max_break] column value.
     *
     * @return int
     */
    public function getMaxBreak()
    {

        return $this->max_break;
    }

    /**
     * Get the [max_cont_work] column value.
     *
     * @return int
     */
    public function getMaxContWork()
    {

        return $this->max_cont_work;
    }

    /**
     * Get the [simplified_ata_settings] column value.
     *
     * @return boolean
     */
    public function getSimplifiedAtaSettings()
    {

        return $this->simplified_ata_settings;
    }

    /**
     * Get the [tolerance_in_und] column value.
     *
     * @return int
     */
    public function getToleranceInUnd()
    {

        return $this->tolerance_in_und;
    }

    /**
     * Get the [tolerance_out_und] column value.
     *
     * @return int
     */
    public function getToleranceOutUnd()
    {

        return $this->tolerance_out_und;
    }

    /**
     * Get the [max_undefined_in] column value.
     *
     * @return int
     */
    public function getMaxUndefinedIn()
    {

        return $this->max_undefined_in;
    }

    /**
     * Get the [max_undefined_out] column value.
     *
     * @return int
     */
    public function getMaxUndefinedOut()
    {

        return $this->max_undefined_out;
    }

    /**
     * Get the [min_undefined_in] column value.
     *
     * @return int
     */
    public function getMinUndefinedIn()
    {

        return $this->min_undefined_in;
    }

    /**
     * Get the [min_undefined_out] column value.
     *
     * @return int
     */
    public function getMinUndefinedOut()
    {

        return $this->min_undefined_out;
    }

    /**
     * Get the [step_out_und] column value.
     *
     * @return int
     */
    public function getStepOutUnd()
    {

        return $this->step_out_und;
    }

    /**
     * Get the [step_in_und] column value.
     *
     * @return int
     */
    public function getStepInUnd()
    {

        return $this->step_in_und;
    }

    /**
     * Get the [undefined_parameter_active] column value.
     *
     * @return boolean
     */
    public function getUndefinedParameterActive()
    {

        return $this->undefined_parameter_active;
    }

    /**
     * Get the [min_extraordinary_total] column value.
     *
     * @return int
     */
    public function getMinExtraordinaryTotal()
    {

        return $this->min_extraordinary_total;
    }

    /**
     * Get the [max_extraordinary_total] column value.
     *
     * @return int
     */
    public function getMaxExtraordinaryTotal()
    {

        return $this->max_extraordinary_total;
    }

    /**
     * Get the [min_undefined_total] column value.
     *
     * @return int
     */
    public function getMinUndefinedTotal()
    {

        return $this->min_undefined_total;
    }

    /**
     * Get the [max_undefined_total] column value.
     *
     * @return int
     */
    public function getMaxUndefinedTotal()
    {

        return $this->max_undefined_total;
    }

    /**
     * Get the [step_total_undefined] column value.
     *
     * @return int
     */
    public function getStepTotalUndefined()
    {

        return $this->step_total_undefined;
    }

    /**
     * Get the [step_total_extraordinary] column value.
     *
     * @return int
     */
    public function getStepTotalExtraordinary()
    {

        return $this->step_total_extraordinary;
    }

    /**
     * Get the [lunch_duration] column value.
     *
     * @return int
     */
    public function getLunchDuration()
    {

        return $this->lunch_duration;
    }

    /**
     * Get the [lunch_deductible] column value.
     *
     * @return boolean
     */
    public function getLunchDeductible()
    {

        return $this->lunch_deductible;
    }

    /**
     * Get the [service_deductible] column value.
     *
     * @return boolean
     */
    public function getServiceDeductible()
    {

        return $this->service_deductible;
    }

    /**
     * Get the [min_undefined_lunch] column value.
     *
     * @return int
     */
    public function getMinUndefinedLunch()
    {

        return $this->min_undefined_lunch;
    }

    /**
     * Get the [min_extraordinary_lunch] column value.
     *
     * @return int
     */
    public function getMinExtraordinaryLunch()
    {

        return $this->min_extraordinary_lunch;
    }

    /**
     * Get the [max_undefined_lunch] column value.
     *
     * @return int
     */
    public function getMaxUndefinedLunch()
    {

        return $this->max_undefined_lunch;
    }

    /**
     * Get the [max_extraordinary_lunch] column value.
     *
     * @return int
     */
    public function getMaxExtraordinaryLunch()
    {

        return $this->max_extraordinary_lunch;
    }

    /**
     * Get the [step_lunch_undefined] column value.
     *
     * @return int
     */
    public function getStepLunchUndefined()
    {

        return $this->step_lunch_undefined;
    }

    /**
     * Get the [step_lunch_extraordinary] column value.
     *
     * @return int
     */
    public function getStepLunchExtraordinary()
    {

        return $this->step_lunch_extraordinary;
    }

    /**
     * Get the [break_after_max_work] column value.
     *
     * @return int
     */
    public function getBreakAfterMaxWork()
    {

        return $this->break_after_max_work;
    }

    /**
     * Get the [unit_recover_hours] column value.
     *
     * @return string
     */
    public function getUnitRecoverHours()
    {

        return $this->unit_recover_hours;
    }

    /**
     * Get the [max_work] column value.
     *
     * @return int
     */
    public function getMaxWork()
    {

        return $this->max_work;
    }

    /**
     * Set the value of [employee_id] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = EmployeePeer::EMPLOYEE_ID;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [name] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setName($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->name !== $v) {
            $this->name = $v;
            $this->modifiedColumns[] = EmployeePeer::NAME;
        }


        return $this;
    } // setName()

    /**
     * Set the value of [surname] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setSurname($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->surname !== $v) {
            $this->surname = $v;
            $this->modifiedColumns[] = EmployeePeer::SURNAME;
        }


        return $this;
    } // setSurname()

    /**
     * Set the value of [gender] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setGender($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->gender !== $v) {
            $this->gender = $v;
            $this->modifiedColumns[] = EmployeePeer::GENDER;
        }


        return $this;
    } // setGender()

    /**
     * Set the value of [birthdate] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setBirthdate($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->birthdate !== $v) {
            $this->birthdate = $v;
            $this->modifiedColumns[] = EmployeePeer::BIRTHDATE;
        }


        return $this;
    } // setBirthdate()

    /**
     * Set the value of [fiscal_code] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setFiscalCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->fiscal_code !== $v) {
            $this->fiscal_code = $v;
            $this->modifiedColumns[] = EmployeePeer::FISCAL_CODE;
        }


        return $this;
    } // setFiscalCode()

    /**
     * Set the value of [residence_id] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setResidenceId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->residence_id !== $v) {
            $this->residence_id = $v;
            $this->modifiedColumns[] = EmployeePeer::RESIDENCE_ID;
        }

        if ($this->aResidenceKey !== null && $this->aResidenceKey->getContactId() !== $v) {
            $this->aResidenceKey = null;
        }


        return $this;
    } // setResidenceId()

    /**
     * Set the value of [address_id] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setAddressId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->address_id !== $v) {
            $this->address_id = $v;
            $this->modifiedColumns[] = EmployeePeer::ADDRESS_ID;
        }

        if ($this->aAddressKey !== null && $this->aAddressKey->getContactId() !== $v) {
            $this->aAddressKey = null;
        }


        return $this;
    } // setAddressId()

    /**
     * Set the value of [part_spesa] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setPartSpesa($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->part_spesa !== $v) {
            $this->part_spesa = $v;
            $this->modifiedColumns[] = EmployeePeer::PART_SPESA;
        }


        return $this;
    } // setPartSpesa()

    /**
     * Set the value of [bank] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setBank($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->bank !== $v) {
            $this->bank = $v;
            $this->modifiedColumns[] = EmployeePeer::BANK;
        }


        return $this;
    } // setBank()

    /**
     * Set the value of [liq_office] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setLiqOffice($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->liq_office !== $v) {
            $this->liq_office = $v;
            $this->modifiedColumns[] = EmployeePeer::LIQ_OFFICE;
        }


        return $this;
    } // setLiqOffice()

    /**
     * Set the value of [inps] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setInps($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->inps !== $v) {
            $this->inps = $v;
            $this->modifiedColumns[] = EmployeePeer::INPS;
        }


        return $this;
    } // setInps()

    /**
     * Set the value of [insur_qual] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setInsurQual($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->insur_qual !== $v) {
            $this->insur_qual = $v;
            $this->modifiedColumns[] = EmployeePeer::INSUR_QUAL;
        }


        return $this;
    } // setInsurQual()

    /**
     * Sets the value of the [fore] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setFore($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->fore !== $v) {
            $this->fore = $v;
            $this->modifiedColumns[] = EmployeePeer::FORE;
        }


        return $this;
    } // setFore()

    /**
     * Set the value of [asl] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setAsl($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->asl !== $v) {
            $this->asl = $v;
            $this->modifiedColumns[] = EmployeePeer::ASL;
        }


        return $this;
    } // setAsl()

    /**
     * Set the value of [adm_code] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setAdmCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->adm_code !== $v) {
            $this->adm_code = $v;
            $this->modifiedColumns[] = EmployeePeer::ADM_CODE;
        }


        return $this;
    } // setAdmCode()

    /**
     * Set the value of [way_pay] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setWayPay($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->way_pay !== $v) {
            $this->way_pay = $v;
            $this->modifiedColumns[] = EmployeePeer::WAY_PAY;
        }


        return $this;
    } // setWayPay()

    /**
     * Set the value of [liquid_group] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setLiquidGroup($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->liquid_group !== $v) {
            $this->liquid_group = $v;
            $this->modifiedColumns[] = EmployeePeer::LIQUID_GROUP;
        }


        return $this;
    } // setLiquidGroup()

    /**
     * Set the value of [contr_code] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setContrCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->contr_code !== $v) {
            $this->contr_code = $v;
            $this->modifiedColumns[] = EmployeePeer::CONTR_CODE;
        }


        return $this;
    } // setContrCode()

    /**
     * Set the value of [contr_type] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setContrType($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->contr_type !== $v) {
            $this->contr_type = $v;
            $this->modifiedColumns[] = EmployeePeer::CONTR_TYPE;
        }


        return $this;
    } // setContrType()

    /**
     * Set the value of [contr_cat] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setContrCat($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->contr_cat !== $v) {
            $this->contr_cat = $v;
            $this->modifiedColumns[] = EmployeePeer::CONTR_CAT;
        }


        return $this;
    } // setContrCat()

    /**
     * Set the value of [ssp_frm_pmnt] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setSspFrmPmnt($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->ssp_frm_pmnt !== $v) {
            $this->ssp_frm_pmnt = $v;
            $this->modifiedColumns[] = EmployeePeer::SSP_FRM_PMNT;
        }


        return $this;
    } // setSspFrmPmnt()

    /**
     * Set the value of [personal_data] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setPersonalData($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->personal_data !== $v) {
            $this->personal_data = $v;
            $this->modifiedColumns[] = EmployeePeer::PERSONAL_DATA;
        }


        return $this;
    } // setPersonalData()

    /**
     * Sets the value of the [susp] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setSusp($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->susp !== $v) {
            $this->susp = $v;
            $this->modifiedColumns[] = EmployeePeer::SUSP;
        }


        return $this;
    } // setSusp()

    /**
     * Set the value of [payment_group] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setPaymentGroup($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->payment_group !== $v) {
            $this->payment_group = $v;
            $this->modifiedColumns[] = EmployeePeer::PAYMENT_GROUP;
        }


        return $this;
    } // setPaymentGroup()

    /**
     * Set the value of [priv_ret_type] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setPrivRetType($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->priv_ret_type !== $v) {
            $this->priv_ret_type = $v;
            $this->modifiedColumns[] = EmployeePeer::PRIV_RET_TYPE;
        }


        return $this;
    } // setPrivRetType()

    /**
     * Set the value of [social_position] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setSocialPosition($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->social_position !== $v) {
            $this->social_position = $v;
            $this->modifiedColumns[] = EmployeePeer::SOCIAL_POSITION;
        }


        return $this;
    } // setSocialPosition()

    /**
     * Sets the value of the [active] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setActive($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->active !== $v) {
            $this->active = $v;
            $this->modifiedColumns[] = EmployeePeer::ACTIVE;
        }


        return $this;
    } // setActive()

    /**
     * Set the value of [statal_code] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStatalCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->statal_code !== $v) {
            $this->statal_code = $v;
            $this->modifiedColumns[] = EmployeePeer::STATAL_CODE;
        }


        return $this;
    } // setStatalCode()

    /**
     * Set the value of [fiscal_city_code] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setFiscalCityCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->fiscal_city_code !== $v) {
            $this->fiscal_city_code = $v;
            $this->modifiedColumns[] = EmployeePeer::FISCAL_CITY_CODE;
        }


        return $this;
    } // setFiscalCityCode()

    /**
     * Set the value of [birthplace] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setBirthplace($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->birthplace !== $v) {
            $this->birthplace = $v;
            $this->modifiedColumns[] = EmployeePeer::BIRTHPLACE;
        }


        return $this;
    } // setBirthplace()

    /**
     * Set the value of [income] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setIncome($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->income !== $v) {
            $this->income = $v;
            $this->modifiedColumns[] = EmployeePeer::INCOME;
        }


        return $this;
    } // setIncome()

    /**
     * Set the value of [state_birth] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStateBirth($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->state_birth !== $v) {
            $this->state_birth = $v;
            $this->modifiedColumns[] = EmployeePeer::STATE_BIRTH;
        }


        return $this;
    } // setStateBirth()

    /**
     * Set the value of [citizenship] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setCitizenship($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->citizenship !== $v) {
            $this->citizenship = $v;
            $this->modifiedColumns[] = EmployeePeer::CITIZENSHIP;
        }


        return $this;
    } // setCitizenship()

    /**
     * Set the value of [id_sissi] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setIdSissi($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->id_sissi !== $v) {
            $this->id_sissi = $v;
            $this->modifiedColumns[] = EmployeePeer::ID_SISSI;
        }


        return $this;
    } // setIdSissi()

    /**
     * Set the value of [dom_first_prev_year] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setDomFirstPrevYear($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->dom_first_prev_year !== $v) {
            $this->dom_first_prev_year = $v;
            $this->modifiedColumns[] = EmployeePeer::DOM_FIRST_PREV_YEAR;
        }


        return $this;
    } // setDomFirstPrevYear()

    /**
     * Set the value of [dom_last_prev_year] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setDomLastPrevYear($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->dom_last_prev_year !== $v) {
            $this->dom_last_prev_year = $v;
            $this->modifiedColumns[] = EmployeePeer::DOM_LAST_PREV_YEAR;
        }


        return $this;
    } // setDomLastPrevYear()

    /**
     * Set the value of [dom_first_curr_year] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setDomFirstCurrYear($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->dom_first_curr_year !== $v) {
            $this->dom_first_curr_year = $v;
            $this->modifiedColumns[] = EmployeePeer::DOM_FIRST_CURR_YEAR;
        }


        return $this;
    } // setDomFirstCurrYear()

    /**
     * Set the value of [qualification] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setQualification($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->qualification !== $v) {
            $this->qualification = $v;
            $this->modifiedColumns[] = EmployeePeer::QUALIFICATION;
        }


        return $this;
    } // setQualification()

    /**
     * Set the value of [liquid_office_id] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setLiquidOfficeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->liquid_office_id !== $v) {
            $this->liquid_office_id = $v;
            $this->modifiedColumns[] = EmployeePeer::LIQUID_OFFICE_ID;
        }


        return $this;
    } // setLiquidOfficeId()

    /**
     * Set the value of [badge_number] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setBadgeNumber($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->badge_number !== $v) {
            $this->badge_number = $v;
            $this->modifiedColumns[] = EmployeePeer::BADGE_NUMBER;
        }


        return $this;
    } // setBadgeNumber()

    /**
     * Set the value of [tolerance_in] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setToleranceIn($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->tolerance_in !== $v) {
            $this->tolerance_in = $v;
            $this->modifiedColumns[] = EmployeePeer::TOLERANCE_IN;
        }


        return $this;
    } // setToleranceIn()

    /**
     * Set the value of [tolerance_out] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setToleranceOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->tolerance_out !== $v) {
            $this->tolerance_out = $v;
            $this->modifiedColumns[] = EmployeePeer::TOLERANCE_OUT;
        }


        return $this;
    } // setToleranceOut()

    /**
     * Set the value of [flexibility] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setFlexibility($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->flexibility !== $v) {
            $this->flexibility = $v;
            $this->modifiedColumns[] = EmployeePeer::FLEXIBILITY;
        }


        return $this;
    } // setFlexibility()

    /**
     * Set the value of [generic_tolerance] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setGenericTolerance($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->generic_tolerance !== $v) {
            $this->generic_tolerance = $v;
            $this->modifiedColumns[] = EmployeePeer::GENERIC_TOLERANCE;
        }


        return $this;
    } // setGenericTolerance()

    /**
     * Set the value of [negative_round] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setNegativeRound($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->negative_round !== $v) {
            $this->negative_round = $v;
            $this->modifiedColumns[] = EmployeePeer::NEGATIVE_ROUND;
        }


        return $this;
    } // setNegativeRound()

    /**
     * Set the value of [recover_hours] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setRecoverHours($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->recover_hours !== $v) {
            $this->recover_hours = $v;
            $this->modifiedColumns[] = EmployeePeer::RECOVER_HOURS;
        }


        return $this;
    } // setRecoverHours()

    /**
     * Set the value of [max_extraordinary_in] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxExtraordinaryIn($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_extraordinary_in !== $v) {
            $this->max_extraordinary_in = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_EXTRAORDINARY_IN;
        }


        return $this;
    } // setMaxExtraordinaryIn()

    /**
     * Set the value of [max_extraordinary_out] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxExtraordinaryOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_extraordinary_out !== $v) {
            $this->max_extraordinary_out = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_EXTRAORDINARY_OUT;
        }


        return $this;
    } // setMaxExtraordinaryOut()

    /**
     * Set the value of [min_extraordinary_in] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinExtraordinaryIn($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_extraordinary_in !== $v) {
            $this->min_extraordinary_in = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_EXTRAORDINARY_IN;
        }


        return $this;
    } // setMinExtraordinaryIn()

    /**
     * Set the value of [min_extraordinary_out] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinExtraordinaryOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_extraordinary_out !== $v) {
            $this->min_extraordinary_out = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_EXTRAORDINARY_OUT;
        }


        return $this;
    } // setMinExtraordinaryOut()

    /**
     * Set the value of [step_out] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_out !== $v) {
            $this->step_out = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_OUT;
        }


        return $this;
    } // setStepOut()

    /**
     * Set the value of [step_in] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepIn($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_in !== $v) {
            $this->step_in = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_IN;
        }


        return $this;
    } // setStepIn()

    /**
     * Set the value of [max_break] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxBreak($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_break !== $v) {
            $this->max_break = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_BREAK;
        }


        return $this;
    } // setMaxBreak()

    /**
     * Set the value of [max_cont_work] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxContWork($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_cont_work !== $v) {
            $this->max_cont_work = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_CONT_WORK;
        }


        return $this;
    } // setMaxContWork()

    /**
     * Sets the value of the [simplified_ata_settings] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setSimplifiedAtaSettings($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->simplified_ata_settings !== $v) {
            $this->simplified_ata_settings = $v;
            $this->modifiedColumns[] = EmployeePeer::SIMPLIFIED_ATA_SETTINGS;
        }


        return $this;
    } // setSimplifiedAtaSettings()

    /**
     * Set the value of [tolerance_in_und] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setToleranceInUnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->tolerance_in_und !== $v) {
            $this->tolerance_in_und = $v;
            $this->modifiedColumns[] = EmployeePeer::TOLERANCE_IN_UND;
        }


        return $this;
    } // setToleranceInUnd()

    /**
     * Set the value of [tolerance_out_und] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setToleranceOutUnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->tolerance_out_und !== $v) {
            $this->tolerance_out_und = $v;
            $this->modifiedColumns[] = EmployeePeer::TOLERANCE_OUT_UND;
        }


        return $this;
    } // setToleranceOutUnd()

    /**
     * Set the value of [max_undefined_in] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxUndefinedIn($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_undefined_in !== $v) {
            $this->max_undefined_in = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_UNDEFINED_IN;
        }


        return $this;
    } // setMaxUndefinedIn()

    /**
     * Set the value of [max_undefined_out] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxUndefinedOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_undefined_out !== $v) {
            $this->max_undefined_out = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_UNDEFINED_OUT;
        }


        return $this;
    } // setMaxUndefinedOut()

    /**
     * Set the value of [min_undefined_in] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinUndefinedIn($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_undefined_in !== $v) {
            $this->min_undefined_in = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_UNDEFINED_IN;
        }


        return $this;
    } // setMinUndefinedIn()

    /**
     * Set the value of [min_undefined_out] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinUndefinedOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_undefined_out !== $v) {
            $this->min_undefined_out = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_UNDEFINED_OUT;
        }


        return $this;
    } // setMinUndefinedOut()

    /**
     * Set the value of [step_out_und] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepOutUnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_out_und !== $v) {
            $this->step_out_und = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_OUT_UND;
        }


        return $this;
    } // setStepOutUnd()

    /**
     * Set the value of [step_in_und] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepInUnd($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_in_und !== $v) {
            $this->step_in_und = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_IN_UND;
        }


        return $this;
    } // setStepInUnd()

    /**
     * Sets the value of the [undefined_parameter_active] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setUndefinedParameterActive($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->undefined_parameter_active !== $v) {
            $this->undefined_parameter_active = $v;
            $this->modifiedColumns[] = EmployeePeer::UNDEFINED_PARAMETER_ACTIVE;
        }


        return $this;
    } // setUndefinedParameterActive()

    /**
     * Set the value of [min_extraordinary_total] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinExtraordinaryTotal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_extraordinary_total !== $v) {
            $this->min_extraordinary_total = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_EXTRAORDINARY_TOTAL;
        }


        return $this;
    } // setMinExtraordinaryTotal()

    /**
     * Set the value of [max_extraordinary_total] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxExtraordinaryTotal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_extraordinary_total !== $v) {
            $this->max_extraordinary_total = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_EXTRAORDINARY_TOTAL;
        }


        return $this;
    } // setMaxExtraordinaryTotal()

    /**
     * Set the value of [min_undefined_total] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinUndefinedTotal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_undefined_total !== $v) {
            $this->min_undefined_total = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_UNDEFINED_TOTAL;
        }


        return $this;
    } // setMinUndefinedTotal()

    /**
     * Set the value of [max_undefined_total] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxUndefinedTotal($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_undefined_total !== $v) {
            $this->max_undefined_total = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_UNDEFINED_TOTAL;
        }


        return $this;
    } // setMaxUndefinedTotal()

    /**
     * Set the value of [step_total_undefined] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepTotalUndefined($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_total_undefined !== $v) {
            $this->step_total_undefined = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_TOTAL_UNDEFINED;
        }


        return $this;
    } // setStepTotalUndefined()

    /**
     * Set the value of [step_total_extraordinary] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepTotalExtraordinary($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_total_extraordinary !== $v) {
            $this->step_total_extraordinary = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_TOTAL_EXTRAORDINARY;
        }


        return $this;
    } // setStepTotalExtraordinary()

    /**
     * Set the value of [lunch_duration] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setLunchDuration($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->lunch_duration !== $v) {
            $this->lunch_duration = $v;
            $this->modifiedColumns[] = EmployeePeer::LUNCH_DURATION;
        }


        return $this;
    } // setLunchDuration()

    /**
     * Sets the value of the [lunch_deductible] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setLunchDeductible($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->lunch_deductible !== $v) {
            $this->lunch_deductible = $v;
            $this->modifiedColumns[] = EmployeePeer::LUNCH_DEDUCTIBLE;
        }


        return $this;
    } // setLunchDeductible()

    /**
     * Sets the value of the [service_deductible] column.
     * Non-boolean arguments are converted using the following rules:
     *   * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *   * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     * Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     *
     * @param boolean|integer|string $v The new value
     * @return Employee The current object (for fluent API support)
     */
    public function setServiceDeductible($v)
    {
        if ($v !== null) {
            if (is_string($v)) {
                $v = in_array(strtolower($v), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
            } else {
                $v = (boolean) $v;
            }
        }

        if ($this->service_deductible !== $v) {
            $this->service_deductible = $v;
            $this->modifiedColumns[] = EmployeePeer::SERVICE_DEDUCTIBLE;
        }


        return $this;
    } // setServiceDeductible()

    /**
     * Set the value of [min_undefined_lunch] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinUndefinedLunch($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_undefined_lunch !== $v) {
            $this->min_undefined_lunch = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_UNDEFINED_LUNCH;
        }


        return $this;
    } // setMinUndefinedLunch()

    /**
     * Set the value of [min_extraordinary_lunch] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMinExtraordinaryLunch($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->min_extraordinary_lunch !== $v) {
            $this->min_extraordinary_lunch = $v;
            $this->modifiedColumns[] = EmployeePeer::MIN_EXTRAORDINARY_LUNCH;
        }


        return $this;
    } // setMinExtraordinaryLunch()

    /**
     * Set the value of [max_undefined_lunch] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxUndefinedLunch($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_undefined_lunch !== $v) {
            $this->max_undefined_lunch = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_UNDEFINED_LUNCH;
        }


        return $this;
    } // setMaxUndefinedLunch()

    /**
     * Set the value of [max_extraordinary_lunch] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxExtraordinaryLunch($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_extraordinary_lunch !== $v) {
            $this->max_extraordinary_lunch = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_EXTRAORDINARY_LUNCH;
        }


        return $this;
    } // setMaxExtraordinaryLunch()

    /**
     * Set the value of [step_lunch_undefined] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepLunchUndefined($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_lunch_undefined !== $v) {
            $this->step_lunch_undefined = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_LUNCH_UNDEFINED;
        }


        return $this;
    } // setStepLunchUndefined()

    /**
     * Set the value of [step_lunch_extraordinary] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setStepLunchExtraordinary($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->step_lunch_extraordinary !== $v) {
            $this->step_lunch_extraordinary = $v;
            $this->modifiedColumns[] = EmployeePeer::STEP_LUNCH_EXTRAORDINARY;
        }


        return $this;
    } // setStepLunchExtraordinary()

    /**
     * Set the value of [break_after_max_work] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setBreakAfterMaxWork($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->break_after_max_work !== $v) {
            $this->break_after_max_work = $v;
            $this->modifiedColumns[] = EmployeePeer::BREAK_AFTER_MAX_WORK;
        }


        return $this;
    } // setBreakAfterMaxWork()

    /**
     * Set the value of [unit_recover_hours] column.
     *
     * @param  string $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setUnitRecoverHours($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->unit_recover_hours !== $v) {
            $this->unit_recover_hours = $v;
            $this->modifiedColumns[] = EmployeePeer::UNIT_RECOVER_HOURS;
        }


        return $this;
    } // setUnitRecoverHours()

    /**
     * Set the value of [max_work] column.
     *
     * @param  int $v new value
     * @return Employee The current object (for fluent API support)
     */
    public function setMaxWork($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->max_work !== $v) {
            $this->max_work = $v;
            $this->modifiedColumns[] = EmployeePeer::MAX_WORK;
        }


        return $this;
    } // setMaxWork()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->bank !== 0) {
                return false;
            }

            if ($this->personal_data !== 0) {
                return false;
            }

            if ($this->active !== true) {
                return false;
            }

            if ($this->income !== '0') {
                return false;
            }

            if ($this->liquid_office_id !== 0) {
                return false;
            }

            if ($this->tolerance_in !== 0) {
                return false;
            }

            if ($this->tolerance_out !== 0) {
                return false;
            }

            if ($this->flexibility !== 0) {
                return false;
            }

            if ($this->generic_tolerance !== 0) {
                return false;
            }

            if ($this->negative_round !== 0) {
                return false;
            }

            if ($this->recover_hours !== 100) {
                return false;
            }

            if ($this->max_extraordinary_in !== 999) {
                return false;
            }

            if ($this->max_extraordinary_out !== 999) {
                return false;
            }

            if ($this->min_extraordinary_in !== 0) {
                return false;
            }

            if ($this->min_extraordinary_out !== 0) {
                return false;
            }

            if ($this->step_out !== 0) {
                return false;
            }

            if ($this->step_in !== 0) {
                return false;
            }

            if ($this->max_break !== 0) {
                return false;
            }

            if ($this->max_cont_work !== 720) {
                return false;
            }

            if ($this->simplified_ata_settings !== false) {
                return false;
            }

            if ($this->tolerance_in_und !== 0) {
                return false;
            }

            if ($this->tolerance_out_und !== 0) {
                return false;
            }

            if ($this->max_undefined_in !== 999) {
                return false;
            }

            if ($this->max_undefined_out !== 999) {
                return false;
            }

            if ($this->min_undefined_in !== 0) {
                return false;
            }

            if ($this->min_undefined_out !== 0) {
                return false;
            }

            if ($this->step_out_und !== 0) {
                return false;
            }

            if ($this->step_in_und !== 0) {
                return false;
            }

            if ($this->undefined_parameter_active !== false) {
                return false;
            }

            if ($this->min_extraordinary_total !== 0) {
                return false;
            }

            if ($this->max_extraordinary_total !== 999) {
                return false;
            }

            if ($this->min_undefined_total !== 0) {
                return false;
            }

            if ($this->max_undefined_total !== 999) {
                return false;
            }

            if ($this->step_total_undefined !== 0) {
                return false;
            }

            if ($this->step_total_extraordinary !== 0) {
                return false;
            }

            if ($this->lunch_duration !== 0) {
                return false;
            }

            if ($this->lunch_deductible !== false) {
                return false;
            }

            if ($this->service_deductible !== false) {
                return false;
            }

            if ($this->min_undefined_lunch !== 0) {
                return false;
            }

            if ($this->min_extraordinary_lunch !== 0) {
                return false;
            }

            if ($this->max_undefined_lunch !== 0) {
                return false;
            }

            if ($this->max_extraordinary_lunch !== 0) {
                return false;
            }

            if ($this->step_lunch_undefined !== 0) {
                return false;
            }

            if ($this->step_lunch_extraordinary !== 0) {
                return false;
            }

            if ($this->break_after_max_work !== 0) {
                return false;
            }

            if ($this->unit_recover_hours !== '%') {
                return false;
            }

            if ($this->max_work !== 999) {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->employee_id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->name = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->surname = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->gender = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->birthdate = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->fiscal_code = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->residence_id = ($row[$startcol + 6] !== null) ? (int) $row[$startcol + 6] : null;
            $this->address_id = ($row[$startcol + 7] !== null) ? (int) $row[$startcol + 7] : null;
            $this->part_spesa = ($row[$startcol + 8] !== null) ? (string) $row[$startcol + 8] : null;
            $this->bank = ($row[$startcol + 9] !== null) ? (int) $row[$startcol + 9] : null;
            $this->liq_office = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->inps = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->insur_qual = ($row[$startcol + 12] !== null) ? (string) $row[$startcol + 12] : null;
            $this->fore = ($row[$startcol + 13] !== null) ? (boolean) $row[$startcol + 13] : null;
            $this->asl = ($row[$startcol + 14] !== null) ? (string) $row[$startcol + 14] : null;
            $this->adm_code = ($row[$startcol + 15] !== null) ? (string) $row[$startcol + 15] : null;
            $this->way_pay = ($row[$startcol + 16] !== null) ? (string) $row[$startcol + 16] : null;
            $this->liquid_group = ($row[$startcol + 17] !== null) ? (string) $row[$startcol + 17] : null;
            $this->contr_code = ($row[$startcol + 18] !== null) ? (string) $row[$startcol + 18] : null;
            $this->contr_type = ($row[$startcol + 19] !== null) ? (string) $row[$startcol + 19] : null;
            $this->contr_cat = ($row[$startcol + 20] !== null) ? (int) $row[$startcol + 20] : null;
            $this->ssp_frm_pmnt = ($row[$startcol + 21] !== null) ? (int) $row[$startcol + 21] : null;
            $this->personal_data = ($row[$startcol + 22] !== null) ? (int) $row[$startcol + 22] : null;
            $this->susp = ($row[$startcol + 23] !== null) ? (boolean) $row[$startcol + 23] : null;
            $this->payment_group = ($row[$startcol + 24] !== null) ? (int) $row[$startcol + 24] : null;
            $this->priv_ret_type = ($row[$startcol + 25] !== null) ? (string) $row[$startcol + 25] : null;
            $this->social_position = ($row[$startcol + 26] !== null) ? (int) $row[$startcol + 26] : null;
            $this->active = ($row[$startcol + 27] !== null) ? (boolean) $row[$startcol + 27] : null;
            $this->statal_code = ($row[$startcol + 28] !== null) ? (string) $row[$startcol + 28] : null;
            $this->fiscal_city_code = ($row[$startcol + 29] !== null) ? (string) $row[$startcol + 29] : null;
            $this->birthplace = ($row[$startcol + 30] !== null) ? (string) $row[$startcol + 30] : null;
            $this->income = ($row[$startcol + 31] !== null) ? (string) $row[$startcol + 31] : null;
            $this->state_birth = ($row[$startcol + 32] !== null) ? (string) $row[$startcol + 32] : null;
            $this->citizenship = ($row[$startcol + 33] !== null) ? (string) $row[$startcol + 33] : null;
            $this->id_sissi = ($row[$startcol + 34] !== null) ? (string) $row[$startcol + 34] : null;
            $this->dom_first_prev_year = ($row[$startcol + 35] !== null) ? (int) $row[$startcol + 35] : null;
            $this->dom_last_prev_year = ($row[$startcol + 36] !== null) ? (int) $row[$startcol + 36] : null;
            $this->dom_first_curr_year = ($row[$startcol + 37] !== null) ? (int) $row[$startcol + 37] : null;
            $this->qualification = ($row[$startcol + 38] !== null) ? (string) $row[$startcol + 38] : null;
            $this->liquid_office_id = ($row[$startcol + 39] !== null) ? (int) $row[$startcol + 39] : null;
            $this->badge_number = ($row[$startcol + 40] !== null) ? (string) $row[$startcol + 40] : null;
            $this->tolerance_in = ($row[$startcol + 41] !== null) ? (int) $row[$startcol + 41] : null;
            $this->tolerance_out = ($row[$startcol + 42] !== null) ? (int) $row[$startcol + 42] : null;
            $this->flexibility = ($row[$startcol + 43] !== null) ? (int) $row[$startcol + 43] : null;
            $this->generic_tolerance = ($row[$startcol + 44] !== null) ? (int) $row[$startcol + 44] : null;
            $this->negative_round = ($row[$startcol + 45] !== null) ? (int) $row[$startcol + 45] : null;
            $this->recover_hours = ($row[$startcol + 46] !== null) ? (int) $row[$startcol + 46] : null;
            $this->max_extraordinary_in = ($row[$startcol + 47] !== null) ? (int) $row[$startcol + 47] : null;
            $this->max_extraordinary_out = ($row[$startcol + 48] !== null) ? (int) $row[$startcol + 48] : null;
            $this->min_extraordinary_in = ($row[$startcol + 49] !== null) ? (int) $row[$startcol + 49] : null;
            $this->min_extraordinary_out = ($row[$startcol + 50] !== null) ? (int) $row[$startcol + 50] : null;
            $this->step_out = ($row[$startcol + 51] !== null) ? (int) $row[$startcol + 51] : null;
            $this->step_in = ($row[$startcol + 52] !== null) ? (int) $row[$startcol + 52] : null;
            $this->max_break = ($row[$startcol + 53] !== null) ? (int) $row[$startcol + 53] : null;
            $this->max_cont_work = ($row[$startcol + 54] !== null) ? (int) $row[$startcol + 54] : null;
            $this->simplified_ata_settings = ($row[$startcol + 55] !== null) ? (boolean) $row[$startcol + 55] : null;
            $this->tolerance_in_und = ($row[$startcol + 56] !== null) ? (int) $row[$startcol + 56] : null;
            $this->tolerance_out_und = ($row[$startcol + 57] !== null) ? (int) $row[$startcol + 57] : null;
            $this->max_undefined_in = ($row[$startcol + 58] !== null) ? (int) $row[$startcol + 58] : null;
            $this->max_undefined_out = ($row[$startcol + 59] !== null) ? (int) $row[$startcol + 59] : null;
            $this->min_undefined_in = ($row[$startcol + 60] !== null) ? (int) $row[$startcol + 60] : null;
            $this->min_undefined_out = ($row[$startcol + 61] !== null) ? (int) $row[$startcol + 61] : null;
            $this->step_out_und = ($row[$startcol + 62] !== null) ? (int) $row[$startcol + 62] : null;
            $this->step_in_und = ($row[$startcol + 63] !== null) ? (int) $row[$startcol + 63] : null;
            $this->undefined_parameter_active = ($row[$startcol + 64] !== null) ? (boolean) $row[$startcol + 64] : null;
            $this->min_extraordinary_total = ($row[$startcol + 65] !== null) ? (int) $row[$startcol + 65] : null;
            $this->max_extraordinary_total = ($row[$startcol + 66] !== null) ? (int) $row[$startcol + 66] : null;
            $this->min_undefined_total = ($row[$startcol + 67] !== null) ? (int) $row[$startcol + 67] : null;
            $this->max_undefined_total = ($row[$startcol + 68] !== null) ? (int) $row[$startcol + 68] : null;
            $this->step_total_undefined = ($row[$startcol + 69] !== null) ? (int) $row[$startcol + 69] : null;
            $this->step_total_extraordinary = ($row[$startcol + 70] !== null) ? (int) $row[$startcol + 70] : null;
            $this->lunch_duration = ($row[$startcol + 71] !== null) ? (int) $row[$startcol + 71] : null;
            $this->lunch_deductible = ($row[$startcol + 72] !== null) ? (boolean) $row[$startcol + 72] : null;
            $this->service_deductible = ($row[$startcol + 73] !== null) ? (boolean) $row[$startcol + 73] : null;
            $this->min_undefined_lunch = ($row[$startcol + 74] !== null) ? (int) $row[$startcol + 74] : null;
            $this->min_extraordinary_lunch = ($row[$startcol + 75] !== null) ? (int) $row[$startcol + 75] : null;
            $this->max_undefined_lunch = ($row[$startcol + 76] !== null) ? (int) $row[$startcol + 76] : null;
            $this->max_extraordinary_lunch = ($row[$startcol + 77] !== null) ? (int) $row[$startcol + 77] : null;
            $this->step_lunch_undefined = ($row[$startcol + 78] !== null) ? (int) $row[$startcol + 78] : null;
            $this->step_lunch_extraordinary = ($row[$startcol + 79] !== null) ? (int) $row[$startcol + 79] : null;
            $this->break_after_max_work = ($row[$startcol + 80] !== null) ? (int) $row[$startcol + 80] : null;
            $this->unit_recover_hours = ($row[$startcol + 81] !== null) ? (string) $row[$startcol + 81] : null;
            $this->max_work = ($row[$startcol + 82] !== null) ? (int) $row[$startcol + 82] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 83; // 83 = EmployeePeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Employee object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aResidenceKey !== null && $this->residence_id !== $this->aResidenceKey->getContactId()) {
            $this->aResidenceKey = null;
        }
        if ($this->aAddressKey !== null && $this->address_id !== $this->aAddressKey->getContactId()) {
            $this->aAddressKey = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = EmployeePeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aResidenceKey = null;
            $this->aAddressKey = null;
            $this->collInstitutes = null;

            $this->collAbsencess = null;

            $this->collTimetables = null;

            $this->collPresences = null;

            $this->collPersonnelStackss = null;

            $this->collStoredMonths = null;

            $this->collStoredDays = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = EmployeeQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(EmployeePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                EmployeePeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aResidenceKey !== null) {
                if ($this->aResidenceKey->isModified() || $this->aResidenceKey->isNew()) {
                    $affectedRows += $this->aResidenceKey->save($con);
                }
                $this->setResidenceKey($this->aResidenceKey);
            }

            if ($this->aAddressKey !== null) {
                if ($this->aAddressKey->isModified() || $this->aAddressKey->isNew()) {
                    $affectedRows += $this->aAddressKey->save($con);
                }
                $this->setAddressKey($this->aAddressKey);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->institutesScheduledForDeletion !== null) {
                if (!$this->institutesScheduledForDeletion->isEmpty()) {
                    InstituteQuery::create()
                        ->filterByPrimaryKeys($this->institutesScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->institutesScheduledForDeletion = null;
                }
            }

            if ($this->collInstitutes !== null) {
                foreach ($this->collInstitutes as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->absencessScheduledForDeletion !== null) {
                if (!$this->absencessScheduledForDeletion->isEmpty()) {
                    AbsencesQuery::create()
                        ->filterByPrimaryKeys($this->absencessScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->absencessScheduledForDeletion = null;
                }
            }

            if ($this->collAbsencess !== null) {
                foreach ($this->collAbsencess as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->timetablesScheduledForDeletion !== null) {
                if (!$this->timetablesScheduledForDeletion->isEmpty()) {
                    TimetableQuery::create()
                        ->filterByPrimaryKeys($this->timetablesScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->timetablesScheduledForDeletion = null;
                }
            }

            if ($this->collTimetables !== null) {
                foreach ($this->collTimetables as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->presencesScheduledForDeletion !== null) {
                if (!$this->presencesScheduledForDeletion->isEmpty()) {
                    PresenceQuery::create()
                        ->filterByPrimaryKeys($this->presencesScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->presencesScheduledForDeletion = null;
                }
            }

            if ($this->collPresences !== null) {
                foreach ($this->collPresences as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->personnelStackssScheduledForDeletion !== null) {
                if (!$this->personnelStackssScheduledForDeletion->isEmpty()) {
                    PersonnelStacksQuery::create()
                        ->filterByPrimaryKeys($this->personnelStackssScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->personnelStackssScheduledForDeletion = null;
                }
            }

            if ($this->collPersonnelStackss !== null) {
                foreach ($this->collPersonnelStackss as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->storedMonthsScheduledForDeletion !== null) {
                if (!$this->storedMonthsScheduledForDeletion->isEmpty()) {
                    StoredMonthQuery::create()
                        ->filterByPrimaryKeys($this->storedMonthsScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->storedMonthsScheduledForDeletion = null;
                }
            }

            if ($this->collStoredMonths !== null) {
                foreach ($this->collStoredMonths as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->storedDaysScheduledForDeletion !== null) {
                if (!$this->storedDaysScheduledForDeletion->isEmpty()) {
                    StoredDayQuery::create()
                        ->filterByPrimaryKeys($this->storedDaysScheduledForDeletion->getPrimaryKeys(false))
                        ->delete($con);
                    $this->storedDaysScheduledForDeletion = null;
                }
            }

            if ($this->collStoredDays !== null) {
                foreach ($this->collStoredDays as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = EmployeePeer::EMPLOYEE_ID;
        if (null !== $this->employee_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . EmployeePeer::EMPLOYEE_ID . ')');
        }
        if (null === $this->employee_id) {
            try {
                $stmt = $con->query("SELECT nextval('employee_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->employee_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(EmployeePeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(EmployeePeer::NAME)) {
            $modifiedColumns[':p' . $index++]  = '"name"';
        }
        if ($this->isColumnModified(EmployeePeer::SURNAME)) {
            $modifiedColumns[':p' . $index++]  = '"surname"';
        }
        if ($this->isColumnModified(EmployeePeer::GENDER)) {
            $modifiedColumns[':p' . $index++]  = '"gender"';
        }
        if ($this->isColumnModified(EmployeePeer::BIRTHDATE)) {
            $modifiedColumns[':p' . $index++]  = '"birthdate"';
        }
        if ($this->isColumnModified(EmployeePeer::FISCAL_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"fiscal_code"';
        }
        if ($this->isColumnModified(EmployeePeer::RESIDENCE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"residence_id"';
        }
        if ($this->isColumnModified(EmployeePeer::ADDRESS_ID)) {
            $modifiedColumns[':p' . $index++]  = '"address_id"';
        }
        if ($this->isColumnModified(EmployeePeer::PART_SPESA)) {
            $modifiedColumns[':p' . $index++]  = '"part_spesa"';
        }
        if ($this->isColumnModified(EmployeePeer::BANK)) {
            $modifiedColumns[':p' . $index++]  = '"bank"';
        }
        if ($this->isColumnModified(EmployeePeer::LIQ_OFFICE)) {
            $modifiedColumns[':p' . $index++]  = '"liq_office"';
        }
        if ($this->isColumnModified(EmployeePeer::INPS)) {
            $modifiedColumns[':p' . $index++]  = '"inps"';
        }
        if ($this->isColumnModified(EmployeePeer::INSUR_QUAL)) {
            $modifiedColumns[':p' . $index++]  = '"insur_qual"';
        }
        if ($this->isColumnModified(EmployeePeer::FORE)) {
            $modifiedColumns[':p' . $index++]  = '"fore"';
        }
        if ($this->isColumnModified(EmployeePeer::ASL)) {
            $modifiedColumns[':p' . $index++]  = '"asl"';
        }
        if ($this->isColumnModified(EmployeePeer::ADM_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"adm_code"';
        }
        if ($this->isColumnModified(EmployeePeer::WAY_PAY)) {
            $modifiedColumns[':p' . $index++]  = '"way_pay"';
        }
        if ($this->isColumnModified(EmployeePeer::LIQUID_GROUP)) {
            $modifiedColumns[':p' . $index++]  = '"liquid_group"';
        }
        if ($this->isColumnModified(EmployeePeer::CONTR_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"contr_code"';
        }
        if ($this->isColumnModified(EmployeePeer::CONTR_TYPE)) {
            $modifiedColumns[':p' . $index++]  = '"contr_type"';
        }
        if ($this->isColumnModified(EmployeePeer::CONTR_CAT)) {
            $modifiedColumns[':p' . $index++]  = '"contr_cat"';
        }
        if ($this->isColumnModified(EmployeePeer::SSP_FRM_PMNT)) {
            $modifiedColumns[':p' . $index++]  = '"ssp_frm_pmnt"';
        }
        if ($this->isColumnModified(EmployeePeer::PERSONAL_DATA)) {
            $modifiedColumns[':p' . $index++]  = '"personal_data"';
        }
        if ($this->isColumnModified(EmployeePeer::SUSP)) {
            $modifiedColumns[':p' . $index++]  = '"susp"';
        }
        if ($this->isColumnModified(EmployeePeer::PAYMENT_GROUP)) {
            $modifiedColumns[':p' . $index++]  = '"payment_group"';
        }
        if ($this->isColumnModified(EmployeePeer::PRIV_RET_TYPE)) {
            $modifiedColumns[':p' . $index++]  = '"priv_ret_type"';
        }
        if ($this->isColumnModified(EmployeePeer::SOCIAL_POSITION)) {
            $modifiedColumns[':p' . $index++]  = '"social_position"';
        }
        if ($this->isColumnModified(EmployeePeer::ACTIVE)) {
            $modifiedColumns[':p' . $index++]  = '"active"';
        }
        if ($this->isColumnModified(EmployeePeer::STATAL_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"statal_code"';
        }
        if ($this->isColumnModified(EmployeePeer::FISCAL_CITY_CODE)) {
            $modifiedColumns[':p' . $index++]  = '"fiscal_city_code"';
        }
        if ($this->isColumnModified(EmployeePeer::BIRTHPLACE)) {
            $modifiedColumns[':p' . $index++]  = '"birthplace"';
        }
        if ($this->isColumnModified(EmployeePeer::INCOME)) {
            $modifiedColumns[':p' . $index++]  = '"income"';
        }
        if ($this->isColumnModified(EmployeePeer::STATE_BIRTH)) {
            $modifiedColumns[':p' . $index++]  = '"state_birth"';
        }
        if ($this->isColumnModified(EmployeePeer::CITIZENSHIP)) {
            $modifiedColumns[':p' . $index++]  = '"citizenship"';
        }
        if ($this->isColumnModified(EmployeePeer::ID_SISSI)) {
            $modifiedColumns[':p' . $index++]  = '"id_sissi"';
        }
        if ($this->isColumnModified(EmployeePeer::DOM_FIRST_PREV_YEAR)) {
            $modifiedColumns[':p' . $index++]  = '"dom_first_prev_year"';
        }
        if ($this->isColumnModified(EmployeePeer::DOM_LAST_PREV_YEAR)) {
            $modifiedColumns[':p' . $index++]  = '"dom_last_prev_year"';
        }
        if ($this->isColumnModified(EmployeePeer::DOM_FIRST_CURR_YEAR)) {
            $modifiedColumns[':p' . $index++]  = '"dom_first_curr_year"';
        }
        if ($this->isColumnModified(EmployeePeer::QUALIFICATION)) {
            $modifiedColumns[':p' . $index++]  = '"qualification"';
        }
        if ($this->isColumnModified(EmployeePeer::LIQUID_OFFICE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"liquid_office_id"';
        }
        if ($this->isColumnModified(EmployeePeer::BADGE_NUMBER)) {
            $modifiedColumns[':p' . $index++]  = '"badge_number"';
        }
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_IN)) {
            $modifiedColumns[':p' . $index++]  = '"tolerance_in"';
        }
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_OUT)) {
            $modifiedColumns[':p' . $index++]  = '"tolerance_out"';
        }
        if ($this->isColumnModified(EmployeePeer::FLEXIBILITY)) {
            $modifiedColumns[':p' . $index++]  = '"flexibility"';
        }
        if ($this->isColumnModified(EmployeePeer::GENERIC_TOLERANCE)) {
            $modifiedColumns[':p' . $index++]  = '"generic_tolerance"';
        }
        if ($this->isColumnModified(EmployeePeer::NEGATIVE_ROUND)) {
            $modifiedColumns[':p' . $index++]  = '"negative_round"';
        }
        if ($this->isColumnModified(EmployeePeer::RECOVER_HOURS)) {
            $modifiedColumns[':p' . $index++]  = '"recover_hours"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_IN)) {
            $modifiedColumns[':p' . $index++]  = '"max_extraordinary_in"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_OUT)) {
            $modifiedColumns[':p' . $index++]  = '"max_extraordinary_out"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_IN)) {
            $modifiedColumns[':p' . $index++]  = '"min_extraordinary_in"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_OUT)) {
            $modifiedColumns[':p' . $index++]  = '"min_extraordinary_out"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_OUT)) {
            $modifiedColumns[':p' . $index++]  = '"step_out"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_IN)) {
            $modifiedColumns[':p' . $index++]  = '"step_in"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_BREAK)) {
            $modifiedColumns[':p' . $index++]  = '"max_break"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_CONT_WORK)) {
            $modifiedColumns[':p' . $index++]  = '"max_cont_work"';
        }
        if ($this->isColumnModified(EmployeePeer::SIMPLIFIED_ATA_SETTINGS)) {
            $modifiedColumns[':p' . $index++]  = '"simplified_ata_settings"';
        }
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_IN_UND)) {
            $modifiedColumns[':p' . $index++]  = '"tolerance_in_und"';
        }
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_OUT_UND)) {
            $modifiedColumns[':p' . $index++]  = '"tolerance_out_und"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_IN)) {
            $modifiedColumns[':p' . $index++]  = '"max_undefined_in"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_OUT)) {
            $modifiedColumns[':p' . $index++]  = '"max_undefined_out"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_IN)) {
            $modifiedColumns[':p' . $index++]  = '"min_undefined_in"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_OUT)) {
            $modifiedColumns[':p' . $index++]  = '"min_undefined_out"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_OUT_UND)) {
            $modifiedColumns[':p' . $index++]  = '"step_out_und"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_IN_UND)) {
            $modifiedColumns[':p' . $index++]  = '"step_in_und"';
        }
        if ($this->isColumnModified(EmployeePeer::UNDEFINED_PARAMETER_ACTIVE)) {
            $modifiedColumns[':p' . $index++]  = '"undefined_parameter_active"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_TOTAL)) {
            $modifiedColumns[':p' . $index++]  = '"min_extraordinary_total"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_TOTAL)) {
            $modifiedColumns[':p' . $index++]  = '"max_extraordinary_total"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_TOTAL)) {
            $modifiedColumns[':p' . $index++]  = '"min_undefined_total"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_TOTAL)) {
            $modifiedColumns[':p' . $index++]  = '"max_undefined_total"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_TOTAL_UNDEFINED)) {
            $modifiedColumns[':p' . $index++]  = '"step_total_undefined"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_TOTAL_EXTRAORDINARY)) {
            $modifiedColumns[':p' . $index++]  = '"step_total_extraordinary"';
        }
        if ($this->isColumnModified(EmployeePeer::LUNCH_DURATION)) {
            $modifiedColumns[':p' . $index++]  = '"lunch_duration"';
        }
        if ($this->isColumnModified(EmployeePeer::LUNCH_DEDUCTIBLE)) {
            $modifiedColumns[':p' . $index++]  = '"lunch_deductible"';
        }
        if ($this->isColumnModified(EmployeePeer::SERVICE_DEDUCTIBLE)) {
            $modifiedColumns[':p' . $index++]  = '"service_deductible"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_LUNCH)) {
            $modifiedColumns[':p' . $index++]  = '"min_undefined_lunch"';
        }
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_LUNCH)) {
            $modifiedColumns[':p' . $index++]  = '"min_extraordinary_lunch"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_LUNCH)) {
            $modifiedColumns[':p' . $index++]  = '"max_undefined_lunch"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_LUNCH)) {
            $modifiedColumns[':p' . $index++]  = '"max_extraordinary_lunch"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_LUNCH_UNDEFINED)) {
            $modifiedColumns[':p' . $index++]  = '"step_lunch_undefined"';
        }
        if ($this->isColumnModified(EmployeePeer::STEP_LUNCH_EXTRAORDINARY)) {
            $modifiedColumns[':p' . $index++]  = '"step_lunch_extraordinary"';
        }
        if ($this->isColumnModified(EmployeePeer::BREAK_AFTER_MAX_WORK)) {
            $modifiedColumns[':p' . $index++]  = '"break_after_max_work"';
        }
        if ($this->isColumnModified(EmployeePeer::UNIT_RECOVER_HOURS)) {
            $modifiedColumns[':p' . $index++]  = '"unit_recover_hours"';
        }
        if ($this->isColumnModified(EmployeePeer::MAX_WORK)) {
            $modifiedColumns[':p' . $index++]  = '"max_work"';
        }

        $sql = sprintf(
            'INSERT INTO "employee" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_INT);
                        break;
                    case '"name"':
                        $stmt->bindValue($identifier, $this->name, PDO::PARAM_STR);
                        break;
                    case '"surname"':
                        $stmt->bindValue($identifier, $this->surname, PDO::PARAM_STR);
                        break;
                    case '"gender"':
                        $stmt->bindValue($identifier, $this->gender, PDO::PARAM_STR);
                        break;
                    case '"birthdate"':
                        $stmt->bindValue($identifier, $this->birthdate, PDO::PARAM_STR);
                        break;
                    case '"fiscal_code"':
                        $stmt->bindValue($identifier, $this->fiscal_code, PDO::PARAM_STR);
                        break;
                    case '"residence_id"':
                        $stmt->bindValue($identifier, $this->residence_id, PDO::PARAM_INT);
                        break;
                    case '"address_id"':
                        $stmt->bindValue($identifier, $this->address_id, PDO::PARAM_INT);
                        break;
                    case '"part_spesa"':
                        $stmt->bindValue($identifier, $this->part_spesa, PDO::PARAM_STR);
                        break;
                    case '"bank"':
                        $stmt->bindValue($identifier, $this->bank, PDO::PARAM_INT);
                        break;
                    case '"liq_office"':
                        $stmt->bindValue($identifier, $this->liq_office, PDO::PARAM_STR);
                        break;
                    case '"inps"':
                        $stmt->bindValue($identifier, $this->inps, PDO::PARAM_STR);
                        break;
                    case '"insur_qual"':
                        $stmt->bindValue($identifier, $this->insur_qual, PDO::PARAM_STR);
                        break;
                    case '"fore"':
                        $stmt->bindValue($identifier, $this->fore, PDO::PARAM_BOOL);
                        break;
                    case '"asl"':
                        $stmt->bindValue($identifier, $this->asl, PDO::PARAM_STR);
                        break;
                    case '"adm_code"':
                        $stmt->bindValue($identifier, $this->adm_code, PDO::PARAM_STR);
                        break;
                    case '"way_pay"':
                        $stmt->bindValue($identifier, $this->way_pay, PDO::PARAM_STR);
                        break;
                    case '"liquid_group"':
                        $stmt->bindValue($identifier, $this->liquid_group, PDO::PARAM_STR);
                        break;
                    case '"contr_code"':
                        $stmt->bindValue($identifier, $this->contr_code, PDO::PARAM_STR);
                        break;
                    case '"contr_type"':
                        $stmt->bindValue($identifier, $this->contr_type, PDO::PARAM_STR);
                        break;
                    case '"contr_cat"':
                        $stmt->bindValue($identifier, $this->contr_cat, PDO::PARAM_INT);
                        break;
                    case '"ssp_frm_pmnt"':
                        $stmt->bindValue($identifier, $this->ssp_frm_pmnt, PDO::PARAM_INT);
                        break;
                    case '"personal_data"':
                        $stmt->bindValue($identifier, $this->personal_data, PDO::PARAM_INT);
                        break;
                    case '"susp"':
                        $stmt->bindValue($identifier, $this->susp, PDO::PARAM_BOOL);
                        break;
                    case '"payment_group"':
                        $stmt->bindValue($identifier, $this->payment_group, PDO::PARAM_INT);
                        break;
                    case '"priv_ret_type"':
                        $stmt->bindValue($identifier, $this->priv_ret_type, PDO::PARAM_STR);
                        break;
                    case '"social_position"':
                        $stmt->bindValue($identifier, $this->social_position, PDO::PARAM_INT);
                        break;
                    case '"active"':
                        $stmt->bindValue($identifier, $this->active, PDO::PARAM_BOOL);
                        break;
                    case '"statal_code"':
                        $stmt->bindValue($identifier, $this->statal_code, PDO::PARAM_STR);
                        break;
                    case '"fiscal_city_code"':
                        $stmt->bindValue($identifier, $this->fiscal_city_code, PDO::PARAM_STR);
                        break;
                    case '"birthplace"':
                        $stmt->bindValue($identifier, $this->birthplace, PDO::PARAM_STR);
                        break;
                    case '"income"':
                        $stmt->bindValue($identifier, $this->income, PDO::PARAM_STR);
                        break;
                    case '"state_birth"':
                        $stmt->bindValue($identifier, $this->state_birth, PDO::PARAM_STR);
                        break;
                    case '"citizenship"':
                        $stmt->bindValue($identifier, $this->citizenship, PDO::PARAM_STR);
                        break;
                    case '"id_sissi"':
                        $stmt->bindValue($identifier, $this->id_sissi, PDO::PARAM_STR);
                        break;
                    case '"dom_first_prev_year"':
                        $stmt->bindValue($identifier, $this->dom_first_prev_year, PDO::PARAM_INT);
                        break;
                    case '"dom_last_prev_year"':
                        $stmt->bindValue($identifier, $this->dom_last_prev_year, PDO::PARAM_INT);
                        break;
                    case '"dom_first_curr_year"':
                        $stmt->bindValue($identifier, $this->dom_first_curr_year, PDO::PARAM_INT);
                        break;
                    case '"qualification"':
                        $stmt->bindValue($identifier, $this->qualification, PDO::PARAM_STR);
                        break;
                    case '"liquid_office_id"':
                        $stmt->bindValue($identifier, $this->liquid_office_id, PDO::PARAM_INT);
                        break;
                    case '"badge_number"':
                        $stmt->bindValue($identifier, $this->badge_number, PDO::PARAM_STR);
                        break;
                    case '"tolerance_in"':
                        $stmt->bindValue($identifier, $this->tolerance_in, PDO::PARAM_INT);
                        break;
                    case '"tolerance_out"':
                        $stmt->bindValue($identifier, $this->tolerance_out, PDO::PARAM_INT);
                        break;
                    case '"flexibility"':
                        $stmt->bindValue($identifier, $this->flexibility, PDO::PARAM_INT);
                        break;
                    case '"generic_tolerance"':
                        $stmt->bindValue($identifier, $this->generic_tolerance, PDO::PARAM_INT);
                        break;
                    case '"negative_round"':
                        $stmt->bindValue($identifier, $this->negative_round, PDO::PARAM_INT);
                        break;
                    case '"recover_hours"':
                        $stmt->bindValue($identifier, $this->recover_hours, PDO::PARAM_INT);
                        break;
                    case '"max_extraordinary_in"':
                        $stmt->bindValue($identifier, $this->max_extraordinary_in, PDO::PARAM_INT);
                        break;
                    case '"max_extraordinary_out"':
                        $stmt->bindValue($identifier, $this->max_extraordinary_out, PDO::PARAM_INT);
                        break;
                    case '"min_extraordinary_in"':
                        $stmt->bindValue($identifier, $this->min_extraordinary_in, PDO::PARAM_INT);
                        break;
                    case '"min_extraordinary_out"':
                        $stmt->bindValue($identifier, $this->min_extraordinary_out, PDO::PARAM_INT);
                        break;
                    case '"step_out"':
                        $stmt->bindValue($identifier, $this->step_out, PDO::PARAM_INT);
                        break;
                    case '"step_in"':
                        $stmt->bindValue($identifier, $this->step_in, PDO::PARAM_INT);
                        break;
                    case '"max_break"':
                        $stmt->bindValue($identifier, $this->max_break, PDO::PARAM_INT);
                        break;
                    case '"max_cont_work"':
                        $stmt->bindValue($identifier, $this->max_cont_work, PDO::PARAM_INT);
                        break;
                    case '"simplified_ata_settings"':
                        $stmt->bindValue($identifier, $this->simplified_ata_settings, PDO::PARAM_BOOL);
                        break;
                    case '"tolerance_in_und"':
                        $stmt->bindValue($identifier, $this->tolerance_in_und, PDO::PARAM_INT);
                        break;
                    case '"tolerance_out_und"':
                        $stmt->bindValue($identifier, $this->tolerance_out_und, PDO::PARAM_INT);
                        break;
                    case '"max_undefined_in"':
                        $stmt->bindValue($identifier, $this->max_undefined_in, PDO::PARAM_INT);
                        break;
                    case '"max_undefined_out"':
                        $stmt->bindValue($identifier, $this->max_undefined_out, PDO::PARAM_INT);
                        break;
                    case '"min_undefined_in"':
                        $stmt->bindValue($identifier, $this->min_undefined_in, PDO::PARAM_INT);
                        break;
                    case '"min_undefined_out"':
                        $stmt->bindValue($identifier, $this->min_undefined_out, PDO::PARAM_INT);
                        break;
                    case '"step_out_und"':
                        $stmt->bindValue($identifier, $this->step_out_und, PDO::PARAM_INT);
                        break;
                    case '"step_in_und"':
                        $stmt->bindValue($identifier, $this->step_in_und, PDO::PARAM_INT);
                        break;
                    case '"undefined_parameter_active"':
                        $stmt->bindValue($identifier, $this->undefined_parameter_active, PDO::PARAM_BOOL);
                        break;
                    case '"min_extraordinary_total"':
                        $stmt->bindValue($identifier, $this->min_extraordinary_total, PDO::PARAM_INT);
                        break;
                    case '"max_extraordinary_total"':
                        $stmt->bindValue($identifier, $this->max_extraordinary_total, PDO::PARAM_INT);
                        break;
                    case '"min_undefined_total"':
                        $stmt->bindValue($identifier, $this->min_undefined_total, PDO::PARAM_INT);
                        break;
                    case '"max_undefined_total"':
                        $stmt->bindValue($identifier, $this->max_undefined_total, PDO::PARAM_INT);
                        break;
                    case '"step_total_undefined"':
                        $stmt->bindValue($identifier, $this->step_total_undefined, PDO::PARAM_INT);
                        break;
                    case '"step_total_extraordinary"':
                        $stmt->bindValue($identifier, $this->step_total_extraordinary, PDO::PARAM_INT);
                        break;
                    case '"lunch_duration"':
                        $stmt->bindValue($identifier, $this->lunch_duration, PDO::PARAM_INT);
                        break;
                    case '"lunch_deductible"':
                        $stmt->bindValue($identifier, $this->lunch_deductible, PDO::PARAM_BOOL);
                        break;
                    case '"service_deductible"':
                        $stmt->bindValue($identifier, $this->service_deductible, PDO::PARAM_BOOL);
                        break;
                    case '"min_undefined_lunch"':
                        $stmt->bindValue($identifier, $this->min_undefined_lunch, PDO::PARAM_INT);
                        break;
                    case '"min_extraordinary_lunch"':
                        $stmt->bindValue($identifier, $this->min_extraordinary_lunch, PDO::PARAM_INT);
                        break;
                    case '"max_undefined_lunch"':
                        $stmt->bindValue($identifier, $this->max_undefined_lunch, PDO::PARAM_INT);
                        break;
                    case '"max_extraordinary_lunch"':
                        $stmt->bindValue($identifier, $this->max_extraordinary_lunch, PDO::PARAM_INT);
                        break;
                    case '"step_lunch_undefined"':
                        $stmt->bindValue($identifier, $this->step_lunch_undefined, PDO::PARAM_INT);
                        break;
                    case '"step_lunch_extraordinary"':
                        $stmt->bindValue($identifier, $this->step_lunch_extraordinary, PDO::PARAM_INT);
                        break;
                    case '"break_after_max_work"':
                        $stmt->bindValue($identifier, $this->break_after_max_work, PDO::PARAM_INT);
                        break;
                    case '"unit_recover_hours"':
                        $stmt->bindValue($identifier, $this->unit_recover_hours, PDO::PARAM_STR);
                        break;
                    case '"max_work"':
                        $stmt->bindValue($identifier, $this->max_work, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aResidenceKey !== null) {
                if (!$this->aResidenceKey->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aResidenceKey->getValidationFailures());
                }
            }

            if ($this->aAddressKey !== null) {
                if (!$this->aAddressKey->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aAddressKey->getValidationFailures());
                }
            }


            if (($retval = EmployeePeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collInstitutes !== null) {
                    foreach ($this->collInstitutes as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collAbsencess !== null) {
                    foreach ($this->collAbsencess as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collTimetables !== null) {
                    foreach ($this->collTimetables as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collPresences !== null) {
                    foreach ($this->collPresences as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collPersonnelStackss !== null) {
                    foreach ($this->collPersonnelStackss as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collStoredMonths !== null) {
                    foreach ($this->collStoredMonths as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collStoredDays !== null) {
                    foreach ($this->collStoredDays as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = EmployeePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getEmployeeId();
                break;
            case 1:
                return $this->getName();
                break;
            case 2:
                return $this->getSurname();
                break;
            case 3:
                return $this->getGender();
                break;
            case 4:
                return $this->getBirthdate();
                break;
            case 5:
                return $this->getFiscalCode();
                break;
            case 6:
                return $this->getResidenceId();
                break;
            case 7:
                return $this->getAddressId();
                break;
            case 8:
                return $this->getPartSpesa();
                break;
            case 9:
                return $this->getBank();
                break;
            case 10:
                return $this->getLiqOffice();
                break;
            case 11:
                return $this->getInps();
                break;
            case 12:
                return $this->getInsurQual();
                break;
            case 13:
                return $this->getFore();
                break;
            case 14:
                return $this->getAsl();
                break;
            case 15:
                return $this->getAdmCode();
                break;
            case 16:
                return $this->getWayPay();
                break;
            case 17:
                return $this->getLiquidGroup();
                break;
            case 18:
                return $this->getContrCode();
                break;
            case 19:
                return $this->getContrType();
                break;
            case 20:
                return $this->getContrCat();
                break;
            case 21:
                return $this->getSspFrmPmnt();
                break;
            case 22:
                return $this->getPersonalData();
                break;
            case 23:
                return $this->getSusp();
                break;
            case 24:
                return $this->getPaymentGroup();
                break;
            case 25:
                return $this->getPrivRetType();
                break;
            case 26:
                return $this->getSocialPosition();
                break;
            case 27:
                return $this->getActive();
                break;
            case 28:
                return $this->getStatalCode();
                break;
            case 29:
                return $this->getFiscalCityCode();
                break;
            case 30:
                return $this->getBirthplace();
                break;
            case 31:
                return $this->getIncome();
                break;
            case 32:
                return $this->getStateBirth();
                break;
            case 33:
                return $this->getCitizenship();
                break;
            case 34:
                return $this->getIdSissi();
                break;
            case 35:
                return $this->getDomFirstPrevYear();
                break;
            case 36:
                return $this->getDomLastPrevYear();
                break;
            case 37:
                return $this->getDomFirstCurrYear();
                break;
            case 38:
                return $this->getQualification();
                break;
            case 39:
                return $this->getLiquidOfficeId();
                break;
            case 40:
                return $this->getBadgeNumber();
                break;
            case 41:
                return $this->getToleranceIn();
                break;
            case 42:
                return $this->getToleranceOut();
                break;
            case 43:
                return $this->getFlexibility();
                break;
            case 44:
                return $this->getGenericTolerance();
                break;
            case 45:
                return $this->getNegativeRound();
                break;
            case 46:
                return $this->getRecoverHours();
                break;
            case 47:
                return $this->getMaxExtraordinaryIn();
                break;
            case 48:
                return $this->getMaxExtraordinaryOut();
                break;
            case 49:
                return $this->getMinExtraordinaryIn();
                break;
            case 50:
                return $this->getMinExtraordinaryOut();
                break;
            case 51:
                return $this->getStepOut();
                break;
            case 52:
                return $this->getStepIn();
                break;
            case 53:
                return $this->getMaxBreak();
                break;
            case 54:
                return $this->getMaxContWork();
                break;
            case 55:
                return $this->getSimplifiedAtaSettings();
                break;
            case 56:
                return $this->getToleranceInUnd();
                break;
            case 57:
                return $this->getToleranceOutUnd();
                break;
            case 58:
                return $this->getMaxUndefinedIn();
                break;
            case 59:
                return $this->getMaxUndefinedOut();
                break;
            case 60:
                return $this->getMinUndefinedIn();
                break;
            case 61:
                return $this->getMinUndefinedOut();
                break;
            case 62:
                return $this->getStepOutUnd();
                break;
            case 63:
                return $this->getStepInUnd();
                break;
            case 64:
                return $this->getUndefinedParameterActive();
                break;
            case 65:
                return $this->getMinExtraordinaryTotal();
                break;
            case 66:
                return $this->getMaxExtraordinaryTotal();
                break;
            case 67:
                return $this->getMinUndefinedTotal();
                break;
            case 68:
                return $this->getMaxUndefinedTotal();
                break;
            case 69:
                return $this->getStepTotalUndefined();
                break;
            case 70:
                return $this->getStepTotalExtraordinary();
                break;
            case 71:
                return $this->getLunchDuration();
                break;
            case 72:
                return $this->getLunchDeductible();
                break;
            case 73:
                return $this->getServiceDeductible();
                break;
            case 74:
                return $this->getMinUndefinedLunch();
                break;
            case 75:
                return $this->getMinExtraordinaryLunch();
                break;
            case 76:
                return $this->getMaxUndefinedLunch();
                break;
            case 77:
                return $this->getMaxExtraordinaryLunch();
                break;
            case 78:
                return $this->getStepLunchUndefined();
                break;
            case 79:
                return $this->getStepLunchExtraordinary();
                break;
            case 80:
                return $this->getBreakAfterMaxWork();
                break;
            case 81:
                return $this->getUnitRecoverHours();
                break;
            case 82:
                return $this->getMaxWork();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Employee'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Employee'][$this->getPrimaryKey()] = true;
        $keys = EmployeePeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getEmployeeId(),
            $keys[1] => $this->getName(),
            $keys[2] => $this->getSurname(),
            $keys[3] => $this->getGender(),
            $keys[4] => $this->getBirthdate(),
            $keys[5] => $this->getFiscalCode(),
            $keys[6] => $this->getResidenceId(),
            $keys[7] => $this->getAddressId(),
            $keys[8] => $this->getPartSpesa(),
            $keys[9] => $this->getBank(),
            $keys[10] => $this->getLiqOffice(),
            $keys[11] => $this->getInps(),
            $keys[12] => $this->getInsurQual(),
            $keys[13] => $this->getFore(),
            $keys[14] => $this->getAsl(),
            $keys[15] => $this->getAdmCode(),
            $keys[16] => $this->getWayPay(),
            $keys[17] => $this->getLiquidGroup(),
            $keys[18] => $this->getContrCode(),
            $keys[19] => $this->getContrType(),
            $keys[20] => $this->getContrCat(),
            $keys[21] => $this->getSspFrmPmnt(),
            $keys[22] => $this->getPersonalData(),
            $keys[23] => $this->getSusp(),
            $keys[24] => $this->getPaymentGroup(),
            $keys[25] => $this->getPrivRetType(),
            $keys[26] => $this->getSocialPosition(),
            $keys[27] => $this->getActive(),
            $keys[28] => $this->getStatalCode(),
            $keys[29] => $this->getFiscalCityCode(),
            $keys[30] => $this->getBirthplace(),
            $keys[31] => $this->getIncome(),
            $keys[32] => $this->getStateBirth(),
            $keys[33] => $this->getCitizenship(),
            $keys[34] => $this->getIdSissi(),
            $keys[35] => $this->getDomFirstPrevYear(),
            $keys[36] => $this->getDomLastPrevYear(),
            $keys[37] => $this->getDomFirstCurrYear(),
            $keys[38] => $this->getQualification(),
            $keys[39] => $this->getLiquidOfficeId(),
            $keys[40] => $this->getBadgeNumber(),
            $keys[41] => $this->getToleranceIn(),
            $keys[42] => $this->getToleranceOut(),
            $keys[43] => $this->getFlexibility(),
            $keys[44] => $this->getGenericTolerance(),
            $keys[45] => $this->getNegativeRound(),
            $keys[46] => $this->getRecoverHours(),
            $keys[47] => $this->getMaxExtraordinaryIn(),
            $keys[48] => $this->getMaxExtraordinaryOut(),
            $keys[49] => $this->getMinExtraordinaryIn(),
            $keys[50] => $this->getMinExtraordinaryOut(),
            $keys[51] => $this->getStepOut(),
            $keys[52] => $this->getStepIn(),
            $keys[53] => $this->getMaxBreak(),
            $keys[54] => $this->getMaxContWork(),
            $keys[55] => $this->getSimplifiedAtaSettings(),
            $keys[56] => $this->getToleranceInUnd(),
            $keys[57] => $this->getToleranceOutUnd(),
            $keys[58] => $this->getMaxUndefinedIn(),
            $keys[59] => $this->getMaxUndefinedOut(),
            $keys[60] => $this->getMinUndefinedIn(),
            $keys[61] => $this->getMinUndefinedOut(),
            $keys[62] => $this->getStepOutUnd(),
            $keys[63] => $this->getStepInUnd(),
            $keys[64] => $this->getUndefinedParameterActive(),
            $keys[65] => $this->getMinExtraordinaryTotal(),
            $keys[66] => $this->getMaxExtraordinaryTotal(),
            $keys[67] => $this->getMinUndefinedTotal(),
            $keys[68] => $this->getMaxUndefinedTotal(),
            $keys[69] => $this->getStepTotalUndefined(),
            $keys[70] => $this->getStepTotalExtraordinary(),
            $keys[71] => $this->getLunchDuration(),
            $keys[72] => $this->getLunchDeductible(),
            $keys[73] => $this->getServiceDeductible(),
            $keys[74] => $this->getMinUndefinedLunch(),
            $keys[75] => $this->getMinExtraordinaryLunch(),
            $keys[76] => $this->getMaxUndefinedLunch(),
            $keys[77] => $this->getMaxExtraordinaryLunch(),
            $keys[78] => $this->getStepLunchUndefined(),
            $keys[79] => $this->getStepLunchExtraordinary(),
            $keys[80] => $this->getBreakAfterMaxWork(),
            $keys[81] => $this->getUnitRecoverHours(),
            $keys[82] => $this->getMaxWork(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aResidenceKey) {
                $result['ResidenceKey'] = $this->aResidenceKey->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aAddressKey) {
                $result['AddressKey'] = $this->aAddressKey->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collInstitutes) {
                $result['Institutes'] = $this->collInstitutes->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collAbsencess) {
                $result['Absencess'] = $this->collAbsencess->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collTimetables) {
                $result['Timetables'] = $this->collTimetables->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collPresences) {
                $result['Presences'] = $this->collPresences->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collPersonnelStackss) {
                $result['PersonnelStackss'] = $this->collPersonnelStackss->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collStoredMonths) {
                $result['StoredMonths'] = $this->collStoredMonths->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collStoredDays) {
                $result['StoredDays'] = $this->collStoredDays->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = EmployeePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setEmployeeId($value);
                break;
            case 1:
                $this->setName($value);
                break;
            case 2:
                $this->setSurname($value);
                break;
            case 3:
                $this->setGender($value);
                break;
            case 4:
                $this->setBirthdate($value);
                break;
            case 5:
                $this->setFiscalCode($value);
                break;
            case 6:
                $this->setResidenceId($value);
                break;
            case 7:
                $this->setAddressId($value);
                break;
            case 8:
                $this->setPartSpesa($value);
                break;
            case 9:
                $this->setBank($value);
                break;
            case 10:
                $this->setLiqOffice($value);
                break;
            case 11:
                $this->setInps($value);
                break;
            case 12:
                $this->setInsurQual($value);
                break;
            case 13:
                $this->setFore($value);
                break;
            case 14:
                $this->setAsl($value);
                break;
            case 15:
                $this->setAdmCode($value);
                break;
            case 16:
                $this->setWayPay($value);
                break;
            case 17:
                $this->setLiquidGroup($value);
                break;
            case 18:
                $this->setContrCode($value);
                break;
            case 19:
                $this->setContrType($value);
                break;
            case 20:
                $this->setContrCat($value);
                break;
            case 21:
                $this->setSspFrmPmnt($value);
                break;
            case 22:
                $this->setPersonalData($value);
                break;
            case 23:
                $this->setSusp($value);
                break;
            case 24:
                $this->setPaymentGroup($value);
                break;
            case 25:
                $this->setPrivRetType($value);
                break;
            case 26:
                $this->setSocialPosition($value);
                break;
            case 27:
                $this->setActive($value);
                break;
            case 28:
                $this->setStatalCode($value);
                break;
            case 29:
                $this->setFiscalCityCode($value);
                break;
            case 30:
                $this->setBirthplace($value);
                break;
            case 31:
                $this->setIncome($value);
                break;
            case 32:
                $this->setStateBirth($value);
                break;
            case 33:
                $this->setCitizenship($value);
                break;
            case 34:
                $this->setIdSissi($value);
                break;
            case 35:
                $this->setDomFirstPrevYear($value);
                break;
            case 36:
                $this->setDomLastPrevYear($value);
                break;
            case 37:
                $this->setDomFirstCurrYear($value);
                break;
            case 38:
                $this->setQualification($value);
                break;
            case 39:
                $this->setLiquidOfficeId($value);
                break;
            case 40:
                $this->setBadgeNumber($value);
                break;
            case 41:
                $this->setToleranceIn($value);
                break;
            case 42:
                $this->setToleranceOut($value);
                break;
            case 43:
                $this->setFlexibility($value);
                break;
            case 44:
                $this->setGenericTolerance($value);
                break;
            case 45:
                $this->setNegativeRound($value);
                break;
            case 46:
                $this->setRecoverHours($value);
                break;
            case 47:
                $this->setMaxExtraordinaryIn($value);
                break;
            case 48:
                $this->setMaxExtraordinaryOut($value);
                break;
            case 49:
                $this->setMinExtraordinaryIn($value);
                break;
            case 50:
                $this->setMinExtraordinaryOut($value);
                break;
            case 51:
                $this->setStepOut($value);
                break;
            case 52:
                $this->setStepIn($value);
                break;
            case 53:
                $this->setMaxBreak($value);
                break;
            case 54:
                $this->setMaxContWork($value);
                break;
            case 55:
                $this->setSimplifiedAtaSettings($value);
                break;
            case 56:
                $this->setToleranceInUnd($value);
                break;
            case 57:
                $this->setToleranceOutUnd($value);
                break;
            case 58:
                $this->setMaxUndefinedIn($value);
                break;
            case 59:
                $this->setMaxUndefinedOut($value);
                break;
            case 60:
                $this->setMinUndefinedIn($value);
                break;
            case 61:
                $this->setMinUndefinedOut($value);
                break;
            case 62:
                $this->setStepOutUnd($value);
                break;
            case 63:
                $this->setStepInUnd($value);
                break;
            case 64:
                $this->setUndefinedParameterActive($value);
                break;
            case 65:
                $this->setMinExtraordinaryTotal($value);
                break;
            case 66:
                $this->setMaxExtraordinaryTotal($value);
                break;
            case 67:
                $this->setMinUndefinedTotal($value);
                break;
            case 68:
                $this->setMaxUndefinedTotal($value);
                break;
            case 69:
                $this->setStepTotalUndefined($value);
                break;
            case 70:
                $this->setStepTotalExtraordinary($value);
                break;
            case 71:
                $this->setLunchDuration($value);
                break;
            case 72:
                $this->setLunchDeductible($value);
                break;
            case 73:
                $this->setServiceDeductible($value);
                break;
            case 74:
                $this->setMinUndefinedLunch($value);
                break;
            case 75:
                $this->setMinExtraordinaryLunch($value);
                break;
            case 76:
                $this->setMaxUndefinedLunch($value);
                break;
            case 77:
                $this->setMaxExtraordinaryLunch($value);
                break;
            case 78:
                $this->setStepLunchUndefined($value);
                break;
            case 79:
                $this->setStepLunchExtraordinary($value);
                break;
            case 80:
                $this->setBreakAfterMaxWork($value);
                break;
            case 81:
                $this->setUnitRecoverHours($value);
                break;
            case 82:
                $this->setMaxWork($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = EmployeePeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setEmployeeId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setName($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setSurname($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setGender($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setBirthdate($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setFiscalCode($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setResidenceId($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setAddressId($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setPartSpesa($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setBank($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setLiqOffice($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setInps($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setInsurQual($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setFore($arr[$keys[13]]);
        if (array_key_exists($keys[14], $arr)) $this->setAsl($arr[$keys[14]]);
        if (array_key_exists($keys[15], $arr)) $this->setAdmCode($arr[$keys[15]]);
        if (array_key_exists($keys[16], $arr)) $this->setWayPay($arr[$keys[16]]);
        if (array_key_exists($keys[17], $arr)) $this->setLiquidGroup($arr[$keys[17]]);
        if (array_key_exists($keys[18], $arr)) $this->setContrCode($arr[$keys[18]]);
        if (array_key_exists($keys[19], $arr)) $this->setContrType($arr[$keys[19]]);
        if (array_key_exists($keys[20], $arr)) $this->setContrCat($arr[$keys[20]]);
        if (array_key_exists($keys[21], $arr)) $this->setSspFrmPmnt($arr[$keys[21]]);
        if (array_key_exists($keys[22], $arr)) $this->setPersonalData($arr[$keys[22]]);
        if (array_key_exists($keys[23], $arr)) $this->setSusp($arr[$keys[23]]);
        if (array_key_exists($keys[24], $arr)) $this->setPaymentGroup($arr[$keys[24]]);
        if (array_key_exists($keys[25], $arr)) $this->setPrivRetType($arr[$keys[25]]);
        if (array_key_exists($keys[26], $arr)) $this->setSocialPosition($arr[$keys[26]]);
        if (array_key_exists($keys[27], $arr)) $this->setActive($arr[$keys[27]]);
        if (array_key_exists($keys[28], $arr)) $this->setStatalCode($arr[$keys[28]]);
        if (array_key_exists($keys[29], $arr)) $this->setFiscalCityCode($arr[$keys[29]]);
        if (array_key_exists($keys[30], $arr)) $this->setBirthplace($arr[$keys[30]]);
        if (array_key_exists($keys[31], $arr)) $this->setIncome($arr[$keys[31]]);
        if (array_key_exists($keys[32], $arr)) $this->setStateBirth($arr[$keys[32]]);
        if (array_key_exists($keys[33], $arr)) $this->setCitizenship($arr[$keys[33]]);
        if (array_key_exists($keys[34], $arr)) $this->setIdSissi($arr[$keys[34]]);
        if (array_key_exists($keys[35], $arr)) $this->setDomFirstPrevYear($arr[$keys[35]]);
        if (array_key_exists($keys[36], $arr)) $this->setDomLastPrevYear($arr[$keys[36]]);
        if (array_key_exists($keys[37], $arr)) $this->setDomFirstCurrYear($arr[$keys[37]]);
        if (array_key_exists($keys[38], $arr)) $this->setQualification($arr[$keys[38]]);
        if (array_key_exists($keys[39], $arr)) $this->setLiquidOfficeId($arr[$keys[39]]);
        if (array_key_exists($keys[40], $arr)) $this->setBadgeNumber($arr[$keys[40]]);
        if (array_key_exists($keys[41], $arr)) $this->setToleranceIn($arr[$keys[41]]);
        if (array_key_exists($keys[42], $arr)) $this->setToleranceOut($arr[$keys[42]]);
        if (array_key_exists($keys[43], $arr)) $this->setFlexibility($arr[$keys[43]]);
        if (array_key_exists($keys[44], $arr)) $this->setGenericTolerance($arr[$keys[44]]);
        if (array_key_exists($keys[45], $arr)) $this->setNegativeRound($arr[$keys[45]]);
        if (array_key_exists($keys[46], $arr)) $this->setRecoverHours($arr[$keys[46]]);
        if (array_key_exists($keys[47], $arr)) $this->setMaxExtraordinaryIn($arr[$keys[47]]);
        if (array_key_exists($keys[48], $arr)) $this->setMaxExtraordinaryOut($arr[$keys[48]]);
        if (array_key_exists($keys[49], $arr)) $this->setMinExtraordinaryIn($arr[$keys[49]]);
        if (array_key_exists($keys[50], $arr)) $this->setMinExtraordinaryOut($arr[$keys[50]]);
        if (array_key_exists($keys[51], $arr)) $this->setStepOut($arr[$keys[51]]);
        if (array_key_exists($keys[52], $arr)) $this->setStepIn($arr[$keys[52]]);
        if (array_key_exists($keys[53], $arr)) $this->setMaxBreak($arr[$keys[53]]);
        if (array_key_exists($keys[54], $arr)) $this->setMaxContWork($arr[$keys[54]]);
        if (array_key_exists($keys[55], $arr)) $this->setSimplifiedAtaSettings($arr[$keys[55]]);
        if (array_key_exists($keys[56], $arr)) $this->setToleranceInUnd($arr[$keys[56]]);
        if (array_key_exists($keys[57], $arr)) $this->setToleranceOutUnd($arr[$keys[57]]);
        if (array_key_exists($keys[58], $arr)) $this->setMaxUndefinedIn($arr[$keys[58]]);
        if (array_key_exists($keys[59], $arr)) $this->setMaxUndefinedOut($arr[$keys[59]]);
        if (array_key_exists($keys[60], $arr)) $this->setMinUndefinedIn($arr[$keys[60]]);
        if (array_key_exists($keys[61], $arr)) $this->setMinUndefinedOut($arr[$keys[61]]);
        if (array_key_exists($keys[62], $arr)) $this->setStepOutUnd($arr[$keys[62]]);
        if (array_key_exists($keys[63], $arr)) $this->setStepInUnd($arr[$keys[63]]);
        if (array_key_exists($keys[64], $arr)) $this->setUndefinedParameterActive($arr[$keys[64]]);
        if (array_key_exists($keys[65], $arr)) $this->setMinExtraordinaryTotal($arr[$keys[65]]);
        if (array_key_exists($keys[66], $arr)) $this->setMaxExtraordinaryTotal($arr[$keys[66]]);
        if (array_key_exists($keys[67], $arr)) $this->setMinUndefinedTotal($arr[$keys[67]]);
        if (array_key_exists($keys[68], $arr)) $this->setMaxUndefinedTotal($arr[$keys[68]]);
        if (array_key_exists($keys[69], $arr)) $this->setStepTotalUndefined($arr[$keys[69]]);
        if (array_key_exists($keys[70], $arr)) $this->setStepTotalExtraordinary($arr[$keys[70]]);
        if (array_key_exists($keys[71], $arr)) $this->setLunchDuration($arr[$keys[71]]);
        if (array_key_exists($keys[72], $arr)) $this->setLunchDeductible($arr[$keys[72]]);
        if (array_key_exists($keys[73], $arr)) $this->setServiceDeductible($arr[$keys[73]]);
        if (array_key_exists($keys[74], $arr)) $this->setMinUndefinedLunch($arr[$keys[74]]);
        if (array_key_exists($keys[75], $arr)) $this->setMinExtraordinaryLunch($arr[$keys[75]]);
        if (array_key_exists($keys[76], $arr)) $this->setMaxUndefinedLunch($arr[$keys[76]]);
        if (array_key_exists($keys[77], $arr)) $this->setMaxExtraordinaryLunch($arr[$keys[77]]);
        if (array_key_exists($keys[78], $arr)) $this->setStepLunchUndefined($arr[$keys[78]]);
        if (array_key_exists($keys[79], $arr)) $this->setStepLunchExtraordinary($arr[$keys[79]]);
        if (array_key_exists($keys[80], $arr)) $this->setBreakAfterMaxWork($arr[$keys[80]]);
        if (array_key_exists($keys[81], $arr)) $this->setUnitRecoverHours($arr[$keys[81]]);
        if (array_key_exists($keys[82], $arr)) $this->setMaxWork($arr[$keys[82]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(EmployeePeer::DATABASE_NAME);

        if ($this->isColumnModified(EmployeePeer::EMPLOYEE_ID)) $criteria->add(EmployeePeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(EmployeePeer::NAME)) $criteria->add(EmployeePeer::NAME, $this->name);
        if ($this->isColumnModified(EmployeePeer::SURNAME)) $criteria->add(EmployeePeer::SURNAME, $this->surname);
        if ($this->isColumnModified(EmployeePeer::GENDER)) $criteria->add(EmployeePeer::GENDER, $this->gender);
        if ($this->isColumnModified(EmployeePeer::BIRTHDATE)) $criteria->add(EmployeePeer::BIRTHDATE, $this->birthdate);
        if ($this->isColumnModified(EmployeePeer::FISCAL_CODE)) $criteria->add(EmployeePeer::FISCAL_CODE, $this->fiscal_code);
        if ($this->isColumnModified(EmployeePeer::RESIDENCE_ID)) $criteria->add(EmployeePeer::RESIDENCE_ID, $this->residence_id);
        if ($this->isColumnModified(EmployeePeer::ADDRESS_ID)) $criteria->add(EmployeePeer::ADDRESS_ID, $this->address_id);
        if ($this->isColumnModified(EmployeePeer::PART_SPESA)) $criteria->add(EmployeePeer::PART_SPESA, $this->part_spesa);
        if ($this->isColumnModified(EmployeePeer::BANK)) $criteria->add(EmployeePeer::BANK, $this->bank);
        if ($this->isColumnModified(EmployeePeer::LIQ_OFFICE)) $criteria->add(EmployeePeer::LIQ_OFFICE, $this->liq_office);
        if ($this->isColumnModified(EmployeePeer::INPS)) $criteria->add(EmployeePeer::INPS, $this->inps);
        if ($this->isColumnModified(EmployeePeer::INSUR_QUAL)) $criteria->add(EmployeePeer::INSUR_QUAL, $this->insur_qual);
        if ($this->isColumnModified(EmployeePeer::FORE)) $criteria->add(EmployeePeer::FORE, $this->fore);
        if ($this->isColumnModified(EmployeePeer::ASL)) $criteria->add(EmployeePeer::ASL, $this->asl);
        if ($this->isColumnModified(EmployeePeer::ADM_CODE)) $criteria->add(EmployeePeer::ADM_CODE, $this->adm_code);
        if ($this->isColumnModified(EmployeePeer::WAY_PAY)) $criteria->add(EmployeePeer::WAY_PAY, $this->way_pay);
        if ($this->isColumnModified(EmployeePeer::LIQUID_GROUP)) $criteria->add(EmployeePeer::LIQUID_GROUP, $this->liquid_group);
        if ($this->isColumnModified(EmployeePeer::CONTR_CODE)) $criteria->add(EmployeePeer::CONTR_CODE, $this->contr_code);
        if ($this->isColumnModified(EmployeePeer::CONTR_TYPE)) $criteria->add(EmployeePeer::CONTR_TYPE, $this->contr_type);
        if ($this->isColumnModified(EmployeePeer::CONTR_CAT)) $criteria->add(EmployeePeer::CONTR_CAT, $this->contr_cat);
        if ($this->isColumnModified(EmployeePeer::SSP_FRM_PMNT)) $criteria->add(EmployeePeer::SSP_FRM_PMNT, $this->ssp_frm_pmnt);
        if ($this->isColumnModified(EmployeePeer::PERSONAL_DATA)) $criteria->add(EmployeePeer::PERSONAL_DATA, $this->personal_data);
        if ($this->isColumnModified(EmployeePeer::SUSP)) $criteria->add(EmployeePeer::SUSP, $this->susp);
        if ($this->isColumnModified(EmployeePeer::PAYMENT_GROUP)) $criteria->add(EmployeePeer::PAYMENT_GROUP, $this->payment_group);
        if ($this->isColumnModified(EmployeePeer::PRIV_RET_TYPE)) $criteria->add(EmployeePeer::PRIV_RET_TYPE, $this->priv_ret_type);
        if ($this->isColumnModified(EmployeePeer::SOCIAL_POSITION)) $criteria->add(EmployeePeer::SOCIAL_POSITION, $this->social_position);
        if ($this->isColumnModified(EmployeePeer::ACTIVE)) $criteria->add(EmployeePeer::ACTIVE, $this->active);
        if ($this->isColumnModified(EmployeePeer::STATAL_CODE)) $criteria->add(EmployeePeer::STATAL_CODE, $this->statal_code);
        if ($this->isColumnModified(EmployeePeer::FISCAL_CITY_CODE)) $criteria->add(EmployeePeer::FISCAL_CITY_CODE, $this->fiscal_city_code);
        if ($this->isColumnModified(EmployeePeer::BIRTHPLACE)) $criteria->add(EmployeePeer::BIRTHPLACE, $this->birthplace);
        if ($this->isColumnModified(EmployeePeer::INCOME)) $criteria->add(EmployeePeer::INCOME, $this->income);
        if ($this->isColumnModified(EmployeePeer::STATE_BIRTH)) $criteria->add(EmployeePeer::STATE_BIRTH, $this->state_birth);
        if ($this->isColumnModified(EmployeePeer::CITIZENSHIP)) $criteria->add(EmployeePeer::CITIZENSHIP, $this->citizenship);
        if ($this->isColumnModified(EmployeePeer::ID_SISSI)) $criteria->add(EmployeePeer::ID_SISSI, $this->id_sissi);
        if ($this->isColumnModified(EmployeePeer::DOM_FIRST_PREV_YEAR)) $criteria->add(EmployeePeer::DOM_FIRST_PREV_YEAR, $this->dom_first_prev_year);
        if ($this->isColumnModified(EmployeePeer::DOM_LAST_PREV_YEAR)) $criteria->add(EmployeePeer::DOM_LAST_PREV_YEAR, $this->dom_last_prev_year);
        if ($this->isColumnModified(EmployeePeer::DOM_FIRST_CURR_YEAR)) $criteria->add(EmployeePeer::DOM_FIRST_CURR_YEAR, $this->dom_first_curr_year);
        if ($this->isColumnModified(EmployeePeer::QUALIFICATION)) $criteria->add(EmployeePeer::QUALIFICATION, $this->qualification);
        if ($this->isColumnModified(EmployeePeer::LIQUID_OFFICE_ID)) $criteria->add(EmployeePeer::LIQUID_OFFICE_ID, $this->liquid_office_id);
        if ($this->isColumnModified(EmployeePeer::BADGE_NUMBER)) $criteria->add(EmployeePeer::BADGE_NUMBER, $this->badge_number);
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_IN)) $criteria->add(EmployeePeer::TOLERANCE_IN, $this->tolerance_in);
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_OUT)) $criteria->add(EmployeePeer::TOLERANCE_OUT, $this->tolerance_out);
        if ($this->isColumnModified(EmployeePeer::FLEXIBILITY)) $criteria->add(EmployeePeer::FLEXIBILITY, $this->flexibility);
        if ($this->isColumnModified(EmployeePeer::GENERIC_TOLERANCE)) $criteria->add(EmployeePeer::GENERIC_TOLERANCE, $this->generic_tolerance);
        if ($this->isColumnModified(EmployeePeer::NEGATIVE_ROUND)) $criteria->add(EmployeePeer::NEGATIVE_ROUND, $this->negative_round);
        if ($this->isColumnModified(EmployeePeer::RECOVER_HOURS)) $criteria->add(EmployeePeer::RECOVER_HOURS, $this->recover_hours);
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_IN)) $criteria->add(EmployeePeer::MAX_EXTRAORDINARY_IN, $this->max_extraordinary_in);
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_OUT)) $criteria->add(EmployeePeer::MAX_EXTRAORDINARY_OUT, $this->max_extraordinary_out);
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_IN)) $criteria->add(EmployeePeer::MIN_EXTRAORDINARY_IN, $this->min_extraordinary_in);
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_OUT)) $criteria->add(EmployeePeer::MIN_EXTRAORDINARY_OUT, $this->min_extraordinary_out);
        if ($this->isColumnModified(EmployeePeer::STEP_OUT)) $criteria->add(EmployeePeer::STEP_OUT, $this->step_out);
        if ($this->isColumnModified(EmployeePeer::STEP_IN)) $criteria->add(EmployeePeer::STEP_IN, $this->step_in);
        if ($this->isColumnModified(EmployeePeer::MAX_BREAK)) $criteria->add(EmployeePeer::MAX_BREAK, $this->max_break);
        if ($this->isColumnModified(EmployeePeer::MAX_CONT_WORK)) $criteria->add(EmployeePeer::MAX_CONT_WORK, $this->max_cont_work);
        if ($this->isColumnModified(EmployeePeer::SIMPLIFIED_ATA_SETTINGS)) $criteria->add(EmployeePeer::SIMPLIFIED_ATA_SETTINGS, $this->simplified_ata_settings);
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_IN_UND)) $criteria->add(EmployeePeer::TOLERANCE_IN_UND, $this->tolerance_in_und);
        if ($this->isColumnModified(EmployeePeer::TOLERANCE_OUT_UND)) $criteria->add(EmployeePeer::TOLERANCE_OUT_UND, $this->tolerance_out_und);
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_IN)) $criteria->add(EmployeePeer::MAX_UNDEFINED_IN, $this->max_undefined_in);
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_OUT)) $criteria->add(EmployeePeer::MAX_UNDEFINED_OUT, $this->max_undefined_out);
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_IN)) $criteria->add(EmployeePeer::MIN_UNDEFINED_IN, $this->min_undefined_in);
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_OUT)) $criteria->add(EmployeePeer::MIN_UNDEFINED_OUT, $this->min_undefined_out);
        if ($this->isColumnModified(EmployeePeer::STEP_OUT_UND)) $criteria->add(EmployeePeer::STEP_OUT_UND, $this->step_out_und);
        if ($this->isColumnModified(EmployeePeer::STEP_IN_UND)) $criteria->add(EmployeePeer::STEP_IN_UND, $this->step_in_und);
        if ($this->isColumnModified(EmployeePeer::UNDEFINED_PARAMETER_ACTIVE)) $criteria->add(EmployeePeer::UNDEFINED_PARAMETER_ACTIVE, $this->undefined_parameter_active);
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_TOTAL)) $criteria->add(EmployeePeer::MIN_EXTRAORDINARY_TOTAL, $this->min_extraordinary_total);
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_TOTAL)) $criteria->add(EmployeePeer::MAX_EXTRAORDINARY_TOTAL, $this->max_extraordinary_total);
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_TOTAL)) $criteria->add(EmployeePeer::MIN_UNDEFINED_TOTAL, $this->min_undefined_total);
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_TOTAL)) $criteria->add(EmployeePeer::MAX_UNDEFINED_TOTAL, $this->max_undefined_total);
        if ($this->isColumnModified(EmployeePeer::STEP_TOTAL_UNDEFINED)) $criteria->add(EmployeePeer::STEP_TOTAL_UNDEFINED, $this->step_total_undefined);
        if ($this->isColumnModified(EmployeePeer::STEP_TOTAL_EXTRAORDINARY)) $criteria->add(EmployeePeer::STEP_TOTAL_EXTRAORDINARY, $this->step_total_extraordinary);
        if ($this->isColumnModified(EmployeePeer::LUNCH_DURATION)) $criteria->add(EmployeePeer::LUNCH_DURATION, $this->lunch_duration);
        if ($this->isColumnModified(EmployeePeer::LUNCH_DEDUCTIBLE)) $criteria->add(EmployeePeer::LUNCH_DEDUCTIBLE, $this->lunch_deductible);
        if ($this->isColumnModified(EmployeePeer::SERVICE_DEDUCTIBLE)) $criteria->add(EmployeePeer::SERVICE_DEDUCTIBLE, $this->service_deductible);
        if ($this->isColumnModified(EmployeePeer::MIN_UNDEFINED_LUNCH)) $criteria->add(EmployeePeer::MIN_UNDEFINED_LUNCH, $this->min_undefined_lunch);
        if ($this->isColumnModified(EmployeePeer::MIN_EXTRAORDINARY_LUNCH)) $criteria->add(EmployeePeer::MIN_EXTRAORDINARY_LUNCH, $this->min_extraordinary_lunch);
        if ($this->isColumnModified(EmployeePeer::MAX_UNDEFINED_LUNCH)) $criteria->add(EmployeePeer::MAX_UNDEFINED_LUNCH, $this->max_undefined_lunch);
        if ($this->isColumnModified(EmployeePeer::MAX_EXTRAORDINARY_LUNCH)) $criteria->add(EmployeePeer::MAX_EXTRAORDINARY_LUNCH, $this->max_extraordinary_lunch);
        if ($this->isColumnModified(EmployeePeer::STEP_LUNCH_UNDEFINED)) $criteria->add(EmployeePeer::STEP_LUNCH_UNDEFINED, $this->step_lunch_undefined);
        if ($this->isColumnModified(EmployeePeer::STEP_LUNCH_EXTRAORDINARY)) $criteria->add(EmployeePeer::STEP_LUNCH_EXTRAORDINARY, $this->step_lunch_extraordinary);
        if ($this->isColumnModified(EmployeePeer::BREAK_AFTER_MAX_WORK)) $criteria->add(EmployeePeer::BREAK_AFTER_MAX_WORK, $this->break_after_max_work);
        if ($this->isColumnModified(EmployeePeer::UNIT_RECOVER_HOURS)) $criteria->add(EmployeePeer::UNIT_RECOVER_HOURS, $this->unit_recover_hours);
        if ($this->isColumnModified(EmployeePeer::MAX_WORK)) $criteria->add(EmployeePeer::MAX_WORK, $this->max_work);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(EmployeePeer::DATABASE_NAME);
        $criteria->add(EmployeePeer::EMPLOYEE_ID, $this->employee_id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getEmployeeId();
    }

    /**
     * Generic method to set the primary key (employee_id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setEmployeeId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getEmployeeId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Employee (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setName($this->getName());
        $copyObj->setSurname($this->getSurname());
        $copyObj->setGender($this->getGender());
        $copyObj->setBirthdate($this->getBirthdate());
        $copyObj->setFiscalCode($this->getFiscalCode());
        $copyObj->setResidenceId($this->getResidenceId());
        $copyObj->setAddressId($this->getAddressId());
        $copyObj->setPartSpesa($this->getPartSpesa());
        $copyObj->setBank($this->getBank());
        $copyObj->setLiqOffice($this->getLiqOffice());
        $copyObj->setInps($this->getInps());
        $copyObj->setInsurQual($this->getInsurQual());
        $copyObj->setFore($this->getFore());
        $copyObj->setAsl($this->getAsl());
        $copyObj->setAdmCode($this->getAdmCode());
        $copyObj->setWayPay($this->getWayPay());
        $copyObj->setLiquidGroup($this->getLiquidGroup());
        $copyObj->setContrCode($this->getContrCode());
        $copyObj->setContrType($this->getContrType());
        $copyObj->setContrCat($this->getContrCat());
        $copyObj->setSspFrmPmnt($this->getSspFrmPmnt());
        $copyObj->setPersonalData($this->getPersonalData());
        $copyObj->setSusp($this->getSusp());
        $copyObj->setPaymentGroup($this->getPaymentGroup());
        $copyObj->setPrivRetType($this->getPrivRetType());
        $copyObj->setSocialPosition($this->getSocialPosition());
        $copyObj->setActive($this->getActive());
        $copyObj->setStatalCode($this->getStatalCode());
        $copyObj->setFiscalCityCode($this->getFiscalCityCode());
        $copyObj->setBirthplace($this->getBirthplace());
        $copyObj->setIncome($this->getIncome());
        $copyObj->setStateBirth($this->getStateBirth());
        $copyObj->setCitizenship($this->getCitizenship());
        $copyObj->setIdSissi($this->getIdSissi());
        $copyObj->setDomFirstPrevYear($this->getDomFirstPrevYear());
        $copyObj->setDomLastPrevYear($this->getDomLastPrevYear());
        $copyObj->setDomFirstCurrYear($this->getDomFirstCurrYear());
        $copyObj->setQualification($this->getQualification());
        $copyObj->setLiquidOfficeId($this->getLiquidOfficeId());
        $copyObj->setBadgeNumber($this->getBadgeNumber());
        $copyObj->setToleranceIn($this->getToleranceIn());
        $copyObj->setToleranceOut($this->getToleranceOut());
        $copyObj->setFlexibility($this->getFlexibility());
        $copyObj->setGenericTolerance($this->getGenericTolerance());
        $copyObj->setNegativeRound($this->getNegativeRound());
        $copyObj->setRecoverHours($this->getRecoverHours());
        $copyObj->setMaxExtraordinaryIn($this->getMaxExtraordinaryIn());
        $copyObj->setMaxExtraordinaryOut($this->getMaxExtraordinaryOut());
        $copyObj->setMinExtraordinaryIn($this->getMinExtraordinaryIn());
        $copyObj->setMinExtraordinaryOut($this->getMinExtraordinaryOut());
        $copyObj->setStepOut($this->getStepOut());
        $copyObj->setStepIn($this->getStepIn());
        $copyObj->setMaxBreak($this->getMaxBreak());
        $copyObj->setMaxContWork($this->getMaxContWork());
        $copyObj->setSimplifiedAtaSettings($this->getSimplifiedAtaSettings());
        $copyObj->setToleranceInUnd($this->getToleranceInUnd());
        $copyObj->setToleranceOutUnd($this->getToleranceOutUnd());
        $copyObj->setMaxUndefinedIn($this->getMaxUndefinedIn());
        $copyObj->setMaxUndefinedOut($this->getMaxUndefinedOut());
        $copyObj->setMinUndefinedIn($this->getMinUndefinedIn());
        $copyObj->setMinUndefinedOut($this->getMinUndefinedOut());
        $copyObj->setStepOutUnd($this->getStepOutUnd());
        $copyObj->setStepInUnd($this->getStepInUnd());
        $copyObj->setUndefinedParameterActive($this->getUndefinedParameterActive());
        $copyObj->setMinExtraordinaryTotal($this->getMinExtraordinaryTotal());
        $copyObj->setMaxExtraordinaryTotal($this->getMaxExtraordinaryTotal());
        $copyObj->setMinUndefinedTotal($this->getMinUndefinedTotal());
        $copyObj->setMaxUndefinedTotal($this->getMaxUndefinedTotal());
        $copyObj->setStepTotalUndefined($this->getStepTotalUndefined());
        $copyObj->setStepTotalExtraordinary($this->getStepTotalExtraordinary());
        $copyObj->setLunchDuration($this->getLunchDuration());
        $copyObj->setLunchDeductible($this->getLunchDeductible());
        $copyObj->setServiceDeductible($this->getServiceDeductible());
        $copyObj->setMinUndefinedLunch($this->getMinUndefinedLunch());
        $copyObj->setMinExtraordinaryLunch($this->getMinExtraordinaryLunch());
        $copyObj->setMaxUndefinedLunch($this->getMaxUndefinedLunch());
        $copyObj->setMaxExtraordinaryLunch($this->getMaxExtraordinaryLunch());
        $copyObj->setStepLunchUndefined($this->getStepLunchUndefined());
        $copyObj->setStepLunchExtraordinary($this->getStepLunchExtraordinary());
        $copyObj->setBreakAfterMaxWork($this->getBreakAfterMaxWork());
        $copyObj->setUnitRecoverHours($this->getUnitRecoverHours());
        $copyObj->setMaxWork($this->getMaxWork());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getInstitutes() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addInstitute($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getAbsencess() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addAbsences($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getTimetables() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addTimetable($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getPresences() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addPresence($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getPersonnelStackss() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addPersonnelStacks($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getStoredMonths() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addStoredMonth($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getStoredDays() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addStoredDay($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setEmployeeId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Employee Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return EmployeePeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new EmployeePeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Contact object.
     *
     * @param                  Contact $v
     * @return Employee The current object (for fluent API support)
     * @throws PropelException
     */
    public function setResidenceKey(Contact $v = null)
    {
        if ($v === null) {
            $this->setResidenceId(NULL);
        } else {
            $this->setResidenceId($v->getContactId());
        }

        $this->aResidenceKey = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Contact object, it will not be re-added.
        if ($v !== null) {
            $v->addEmployeeRelatedByResidenceId($this);
        }


        return $this;
    }


    /**
     * Get the associated Contact object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Contact The associated Contact object.
     * @throws PropelException
     */
    public function getResidenceKey(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aResidenceKey === null && ($this->residence_id !== null) && $doQuery) {
            $this->aResidenceKey = ContactQuery::create()->findPk($this->residence_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aResidenceKey->addEmployeesRelatedByResidenceId($this);
             */
        }

        return $this->aResidenceKey;
    }

    /**
     * Declares an association between this object and a Contact object.
     *
     * @param                  Contact $v
     * @return Employee The current object (for fluent API support)
     * @throws PropelException
     */
    public function setAddressKey(Contact $v = null)
    {
        if ($v === null) {
            $this->setAddressId(NULL);
        } else {
            $this->setAddressId($v->getContactId());
        }

        $this->aAddressKey = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Contact object, it will not be re-added.
        if ($v !== null) {
            $v->addEmployeeRelatedByAddressId($this);
        }


        return $this;
    }


    /**
     * Get the associated Contact object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Contact The associated Contact object.
     * @throws PropelException
     */
    public function getAddressKey(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aAddressKey === null && ($this->address_id !== null) && $doQuery) {
            $this->aAddressKey = ContactQuery::create()->findPk($this->address_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aAddressKey->addEmployeesRelatedByAddressId($this);
             */
        }

        return $this->aAddressKey;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Institute' == $relationName) {
            $this->initInstitutes();
        }
        if ('Absences' == $relationName) {
            $this->initAbsencess();
        }
        if ('Timetable' == $relationName) {
            $this->initTimetables();
        }
        if ('Presence' == $relationName) {
            $this->initPresences();
        }
        if ('PersonnelStacks' == $relationName) {
            $this->initPersonnelStackss();
        }
        if ('StoredMonth' == $relationName) {
            $this->initStoredMonths();
        }
        if ('StoredDay' == $relationName) {
            $this->initStoredDays();
        }
    }

    /**
     * Clears out the collInstitutes collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addInstitutes()
     */
    public function clearInstitutes()
    {
        $this->collInstitutes = null; // important to set this to null since that means it is uninitialized
        $this->collInstitutesPartial = null;

        return $this;
    }

    /**
     * reset is the collInstitutes collection loaded partially
     *
     * @return void
     */
    public function resetPartialInstitutes($v = true)
    {
        $this->collInstitutesPartial = $v;
    }

    /**
     * Initializes the collInstitutes collection.
     *
     * By default this just sets the collInstitutes collection to an empty array (like clearcollInstitutes());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initInstitutes($overrideExisting = true)
    {
        if (null !== $this->collInstitutes && !$overrideExisting) {
            return;
        }
        $this->collInstitutes = new PropelObjectCollection();
        $this->collInstitutes->setModel('Institute');
    }

    /**
     * Gets an array of Institute objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Institute[] List of Institute objects
     * @throws PropelException
     */
    public function getInstitutes($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collInstitutesPartial && !$this->isNew();
        if (null === $this->collInstitutes || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collInstitutes) {
                // return empty collection
                $this->initInstitutes();
            } else {
                $collInstitutes = InstituteQuery::create(null, $criteria)
                    ->filterByEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collInstitutesPartial && count($collInstitutes)) {
                      $this->initInstitutes(false);

                      foreach ($collInstitutes as $obj) {
                        if (false == $this->collInstitutes->contains($obj)) {
                          $this->collInstitutes->append($obj);
                        }
                      }

                      $this->collInstitutesPartial = true;
                    }

                    $collInstitutes->getInternalIterator()->rewind();

                    return $collInstitutes;
                }

                if ($partial && $this->collInstitutes) {
                    foreach ($this->collInstitutes as $obj) {
                        if ($obj->isNew()) {
                            $collInstitutes[] = $obj;
                        }
                    }
                }

                $this->collInstitutes = $collInstitutes;
                $this->collInstitutesPartial = false;
            }
        }

        return $this->collInstitutes;
    }

    /**
     * Sets a collection of Institute objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $institutes A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setInstitutes(PropelCollection $institutes, PropelPDO $con = null)
    {
        $institutesToDelete = $this->getInstitutes(new Criteria(), $con)->diff($institutes);


        $this->institutesScheduledForDeletion = $institutesToDelete;

        foreach ($institutesToDelete as $instituteRemoved) {
            $instituteRemoved->setEmployee(null);
        }

        $this->collInstitutes = null;
        foreach ($institutes as $institute) {
            $this->addInstitute($institute);
        }

        $this->collInstitutes = $institutes;
        $this->collInstitutesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Institute objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Institute objects.
     * @throws PropelException
     */
    public function countInstitutes(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collInstitutesPartial && !$this->isNew();
        if (null === $this->collInstitutes || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collInstitutes) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getInstitutes());
            }
            $query = InstituteQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByEmployee($this)
                ->count($con);
        }

        return count($this->collInstitutes);
    }

    /**
     * Method called to associate a Institute object to this object
     * through the Institute foreign key attribute.
     *
     * @param    Institute $l Institute
     * @return Employee The current object (for fluent API support)
     */
    public function addInstitute(Institute $l)
    {
        if ($this->collInstitutes === null) {
            $this->initInstitutes();
            $this->collInstitutesPartial = true;
        }

        if (!in_array($l, $this->collInstitutes->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddInstitute($l);

            if ($this->institutesScheduledForDeletion and $this->institutesScheduledForDeletion->contains($l)) {
                $this->institutesScheduledForDeletion->remove($this->institutesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Institute $institute The institute object to add.
     */
    protected function doAddInstitute($institute)
    {
        $this->collInstitutes[]= $institute;
        $institute->setEmployee($this);
    }

    /**
     * @param	Institute $institute The institute object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removeInstitute($institute)
    {
        if ($this->getInstitutes()->contains($institute)) {
            $this->collInstitutes->remove($this->collInstitutes->search($institute));
            if (null === $this->institutesScheduledForDeletion) {
                $this->institutesScheduledForDeletion = clone $this->collInstitutes;
                $this->institutesScheduledForDeletion->clear();
            }
            $this->institutesScheduledForDeletion[]= clone $institute;
            $institute->setEmployee(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Employee is new, it will return
     * an empty collection; or if this Employee has previously
     * been saved, it will retrieve related Institutes from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Employee.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Institute[] List of Institute objects
     */
    public function getInstitutesJoinContact($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = InstituteQuery::create(null, $criteria);
        $query->joinWith('Contact', $join_behavior);

        return $this->getInstitutes($query, $con);
    }

    /**
     * Clears out the collAbsencess collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addAbsencess()
     */
    public function clearAbsencess()
    {
        $this->collAbsencess = null; // important to set this to null since that means it is uninitialized
        $this->collAbsencessPartial = null;

        return $this;
    }

    /**
     * reset is the collAbsencess collection loaded partially
     *
     * @return void
     */
    public function resetPartialAbsencess($v = true)
    {
        $this->collAbsencessPartial = $v;
    }

    /**
     * Initializes the collAbsencess collection.
     *
     * By default this just sets the collAbsencess collection to an empty array (like clearcollAbsencess());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initAbsencess($overrideExisting = true)
    {
        if (null !== $this->collAbsencess && !$overrideExisting) {
            return;
        }
        $this->collAbsencess = new PropelObjectCollection();
        $this->collAbsencess->setModel('Absences');
    }

    /**
     * Gets an array of Absences objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Absences[] List of Absences objects
     * @throws PropelException
     */
    public function getAbsencess($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collAbsencessPartial && !$this->isNew();
        if (null === $this->collAbsencess || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collAbsencess) {
                // return empty collection
                $this->initAbsencess();
            } else {
                $collAbsencess = AbsencesQuery::create(null, $criteria)
                    ->filterByAbsenceEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collAbsencessPartial && count($collAbsencess)) {
                      $this->initAbsencess(false);

                      foreach ($collAbsencess as $obj) {
                        if (false == $this->collAbsencess->contains($obj)) {
                          $this->collAbsencess->append($obj);
                        }
                      }

                      $this->collAbsencessPartial = true;
                    }

                    $collAbsencess->getInternalIterator()->rewind();

                    return $collAbsencess;
                }

                if ($partial && $this->collAbsencess) {
                    foreach ($this->collAbsencess as $obj) {
                        if ($obj->isNew()) {
                            $collAbsencess[] = $obj;
                        }
                    }
                }

                $this->collAbsencess = $collAbsencess;
                $this->collAbsencessPartial = false;
            }
        }

        return $this->collAbsencess;
    }

    /**
     * Sets a collection of Absences objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $absencess A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setAbsencess(PropelCollection $absencess, PropelPDO $con = null)
    {
        $absencessToDelete = $this->getAbsencess(new Criteria(), $con)->diff($absencess);


        $this->absencessScheduledForDeletion = $absencessToDelete;

        foreach ($absencessToDelete as $absencesRemoved) {
            $absencesRemoved->setAbsenceEmployee(null);
        }

        $this->collAbsencess = null;
        foreach ($absencess as $absences) {
            $this->addAbsences($absences);
        }

        $this->collAbsencess = $absencess;
        $this->collAbsencessPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Absences objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Absences objects.
     * @throws PropelException
     */
    public function countAbsencess(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collAbsencessPartial && !$this->isNew();
        if (null === $this->collAbsencess || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collAbsencess) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getAbsencess());
            }
            $query = AbsencesQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByAbsenceEmployee($this)
                ->count($con);
        }

        return count($this->collAbsencess);
    }

    /**
     * Method called to associate a Absences object to this object
     * through the Absences foreign key attribute.
     *
     * @param    Absences $l Absences
     * @return Employee The current object (for fluent API support)
     */
    public function addAbsences(Absences $l)
    {
        if ($this->collAbsencess === null) {
            $this->initAbsencess();
            $this->collAbsencessPartial = true;
        }

        if (!in_array($l, $this->collAbsencess->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddAbsences($l);

            if ($this->absencessScheduledForDeletion and $this->absencessScheduledForDeletion->contains($l)) {
                $this->absencessScheduledForDeletion->remove($this->absencessScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Absences $absences The absences object to add.
     */
    protected function doAddAbsences($absences)
    {
        $this->collAbsencess[]= $absences;
        $absences->setAbsenceEmployee($this);
    }

    /**
     * @param	Absences $absences The absences object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removeAbsences($absences)
    {
        if ($this->getAbsencess()->contains($absences)) {
            $this->collAbsencess->remove($this->collAbsencess->search($absences));
            if (null === $this->absencessScheduledForDeletion) {
                $this->absencessScheduledForDeletion = clone $this->collAbsencess;
                $this->absencessScheduledForDeletion->clear();
            }
            $this->absencessScheduledForDeletion[]= $absences;
            $absences->setAbsenceEmployee(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Employee is new, it will return
     * an empty collection; or if this Employee has previously
     * been saved, it will retrieve related Absencess from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Employee.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Absences[] List of Absences objects
     */
    public function getAbsencessJoinAbsencesAbsenceKind($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = AbsencesQuery::create(null, $criteria);
        $query->joinWith('AbsencesAbsenceKind', $join_behavior);

        return $this->getAbsencess($query, $con);
    }

    /**
     * Clears out the collTimetables collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addTimetables()
     */
    public function clearTimetables()
    {
        $this->collTimetables = null; // important to set this to null since that means it is uninitialized
        $this->collTimetablesPartial = null;

        return $this;
    }

    /**
     * reset is the collTimetables collection loaded partially
     *
     * @return void
     */
    public function resetPartialTimetables($v = true)
    {
        $this->collTimetablesPartial = $v;
    }

    /**
     * Initializes the collTimetables collection.
     *
     * By default this just sets the collTimetables collection to an empty array (like clearcollTimetables());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initTimetables($overrideExisting = true)
    {
        if (null !== $this->collTimetables && !$overrideExisting) {
            return;
        }
        $this->collTimetables = new PropelObjectCollection();
        $this->collTimetables->setModel('Timetable');
    }

    /**
     * Gets an array of Timetable objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Timetable[] List of Timetable objects
     * @throws PropelException
     */
    public function getTimetables($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collTimetablesPartial && !$this->isNew();
        if (null === $this->collTimetables || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collTimetables) {
                // return empty collection
                $this->initTimetables();
            } else {
                $collTimetables = TimetableQuery::create(null, $criteria)
                    ->filterByTimetableEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collTimetablesPartial && count($collTimetables)) {
                      $this->initTimetables(false);

                      foreach ($collTimetables as $obj) {
                        if (false == $this->collTimetables->contains($obj)) {
                          $this->collTimetables->append($obj);
                        }
                      }

                      $this->collTimetablesPartial = true;
                    }

                    $collTimetables->getInternalIterator()->rewind();

                    return $collTimetables;
                }

                if ($partial && $this->collTimetables) {
                    foreach ($this->collTimetables as $obj) {
                        if ($obj->isNew()) {
                            $collTimetables[] = $obj;
                        }
                    }
                }

                $this->collTimetables = $collTimetables;
                $this->collTimetablesPartial = false;
            }
        }

        return $this->collTimetables;
    }

    /**
     * Sets a collection of Timetable objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $timetables A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setTimetables(PropelCollection $timetables, PropelPDO $con = null)
    {
        $timetablesToDelete = $this->getTimetables(new Criteria(), $con)->diff($timetables);


        $this->timetablesScheduledForDeletion = $timetablesToDelete;

        foreach ($timetablesToDelete as $timetableRemoved) {
            $timetableRemoved->setTimetableEmployee(null);
        }

        $this->collTimetables = null;
        foreach ($timetables as $timetable) {
            $this->addTimetable($timetable);
        }

        $this->collTimetables = $timetables;
        $this->collTimetablesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Timetable objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Timetable objects.
     * @throws PropelException
     */
    public function countTimetables(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collTimetablesPartial && !$this->isNew();
        if (null === $this->collTimetables || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collTimetables) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getTimetables());
            }
            $query = TimetableQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByTimetableEmployee($this)
                ->count($con);
        }

        return count($this->collTimetables);
    }

    /**
     * Method called to associate a Timetable object to this object
     * through the Timetable foreign key attribute.
     *
     * @param    Timetable $l Timetable
     * @return Employee The current object (for fluent API support)
     */
    public function addTimetable(Timetable $l)
    {
        if ($this->collTimetables === null) {
            $this->initTimetables();
            $this->collTimetablesPartial = true;
        }

        if (!in_array($l, $this->collTimetables->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddTimetable($l);

            if ($this->timetablesScheduledForDeletion and $this->timetablesScheduledForDeletion->contains($l)) {
                $this->timetablesScheduledForDeletion->remove($this->timetablesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Timetable $timetable The timetable object to add.
     */
    protected function doAddTimetable($timetable)
    {
        $this->collTimetables[]= $timetable;
        $timetable->setTimetableEmployee($this);
    }

    /**
     * @param	Timetable $timetable The timetable object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removeTimetable($timetable)
    {
        if ($this->getTimetables()->contains($timetable)) {
            $this->collTimetables->remove($this->collTimetables->search($timetable));
            if (null === $this->timetablesScheduledForDeletion) {
                $this->timetablesScheduledForDeletion = clone $this->collTimetables;
                $this->timetablesScheduledForDeletion->clear();
            }
            $this->timetablesScheduledForDeletion[]= clone $timetable;
            $timetable->setTimetableEmployee(null);
        }

        return $this;
    }

    /**
     * Clears out the collPresences collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addPresences()
     */
    public function clearPresences()
    {
        $this->collPresences = null; // important to set this to null since that means it is uninitialized
        $this->collPresencesPartial = null;

        return $this;
    }

    /**
     * reset is the collPresences collection loaded partially
     *
     * @return void
     */
    public function resetPartialPresences($v = true)
    {
        $this->collPresencesPartial = $v;
    }

    /**
     * Initializes the collPresences collection.
     *
     * By default this just sets the collPresences collection to an empty array (like clearcollPresences());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initPresences($overrideExisting = true)
    {
        if (null !== $this->collPresences && !$overrideExisting) {
            return;
        }
        $this->collPresences = new PropelObjectCollection();
        $this->collPresences->setModel('Presence');
    }

    /**
     * Gets an array of Presence objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Presence[] List of Presence objects
     * @throws PropelException
     */
    public function getPresences($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collPresencesPartial && !$this->isNew();
        if (null === $this->collPresences || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collPresences) {
                // return empty collection
                $this->initPresences();
            } else {
                $collPresences = PresenceQuery::create(null, $criteria)
                    ->filterByPresenceEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collPresencesPartial && count($collPresences)) {
                      $this->initPresences(false);

                      foreach ($collPresences as $obj) {
                        if (false == $this->collPresences->contains($obj)) {
                          $this->collPresences->append($obj);
                        }
                      }

                      $this->collPresencesPartial = true;
                    }

                    $collPresences->getInternalIterator()->rewind();

                    return $collPresences;
                }

                if ($partial && $this->collPresences) {
                    foreach ($this->collPresences as $obj) {
                        if ($obj->isNew()) {
                            $collPresences[] = $obj;
                        }
                    }
                }

                $this->collPresences = $collPresences;
                $this->collPresencesPartial = false;
            }
        }

        return $this->collPresences;
    }

    /**
     * Sets a collection of Presence objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $presences A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setPresences(PropelCollection $presences, PropelPDO $con = null)
    {
        $presencesToDelete = $this->getPresences(new Criteria(), $con)->diff($presences);


        $this->presencesScheduledForDeletion = $presencesToDelete;

        foreach ($presencesToDelete as $presenceRemoved) {
            $presenceRemoved->setPresenceEmployee(null);
        }

        $this->collPresences = null;
        foreach ($presences as $presence) {
            $this->addPresence($presence);
        }

        $this->collPresences = $presences;
        $this->collPresencesPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Presence objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Presence objects.
     * @throws PropelException
     */
    public function countPresences(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collPresencesPartial && !$this->isNew();
        if (null === $this->collPresences || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collPresences) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getPresences());
            }
            $query = PresenceQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByPresenceEmployee($this)
                ->count($con);
        }

        return count($this->collPresences);
    }

    /**
     * Method called to associate a Presence object to this object
     * through the Presence foreign key attribute.
     *
     * @param    Presence $l Presence
     * @return Employee The current object (for fluent API support)
     */
    public function addPresence(Presence $l)
    {
        if ($this->collPresences === null) {
            $this->initPresences();
            $this->collPresencesPartial = true;
        }

        if (!in_array($l, $this->collPresences->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddPresence($l);

            if ($this->presencesScheduledForDeletion and $this->presencesScheduledForDeletion->contains($l)) {
                $this->presencesScheduledForDeletion->remove($this->presencesScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Presence $presence The presence object to add.
     */
    protected function doAddPresence($presence)
    {
        $this->collPresences[]= $presence;
        $presence->setPresenceEmployee($this);
    }

    /**
     * @param	Presence $presence The presence object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removePresence($presence)
    {
        if ($this->getPresences()->contains($presence)) {
            $this->collPresences->remove($this->collPresences->search($presence));
            if (null === $this->presencesScheduledForDeletion) {
                $this->presencesScheduledForDeletion = clone $this->collPresences;
                $this->presencesScheduledForDeletion->clear();
            }
            $this->presencesScheduledForDeletion[]= clone $presence;
            $presence->setPresenceEmployee(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Employee is new, it will return
     * an empty collection; or if this Employee has previously
     * been saved, it will retrieve related Presences from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Employee.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|Presence[] List of Presence objects
     */
    public function getPresencesJoinPresenceProject($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = PresenceQuery::create(null, $criteria);
        $query->joinWith('PresenceProject', $join_behavior);

        return $this->getPresences($query, $con);
    }

    /**
     * Clears out the collPersonnelStackss collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addPersonnelStackss()
     */
    public function clearPersonnelStackss()
    {
        $this->collPersonnelStackss = null; // important to set this to null since that means it is uninitialized
        $this->collPersonnelStackssPartial = null;

        return $this;
    }

    /**
     * reset is the collPersonnelStackss collection loaded partially
     *
     * @return void
     */
    public function resetPartialPersonnelStackss($v = true)
    {
        $this->collPersonnelStackssPartial = $v;
    }

    /**
     * Initializes the collPersonnelStackss collection.
     *
     * By default this just sets the collPersonnelStackss collection to an empty array (like clearcollPersonnelStackss());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initPersonnelStackss($overrideExisting = true)
    {
        if (null !== $this->collPersonnelStackss && !$overrideExisting) {
            return;
        }
        $this->collPersonnelStackss = new PropelObjectCollection();
        $this->collPersonnelStackss->setModel('PersonnelStacks');
    }

    /**
     * Gets an array of PersonnelStacks objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|PersonnelStacks[] List of PersonnelStacks objects
     * @throws PropelException
     */
    public function getPersonnelStackss($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collPersonnelStackssPartial && !$this->isNew();
        if (null === $this->collPersonnelStackss || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collPersonnelStackss) {
                // return empty collection
                $this->initPersonnelStackss();
            } else {
                $collPersonnelStackss = PersonnelStacksQuery::create(null, $criteria)
                    ->filterByPersonnelStacksEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collPersonnelStackssPartial && count($collPersonnelStackss)) {
                      $this->initPersonnelStackss(false);

                      foreach ($collPersonnelStackss as $obj) {
                        if (false == $this->collPersonnelStackss->contains($obj)) {
                          $this->collPersonnelStackss->append($obj);
                        }
                      }

                      $this->collPersonnelStackssPartial = true;
                    }

                    $collPersonnelStackss->getInternalIterator()->rewind();

                    return $collPersonnelStackss;
                }

                if ($partial && $this->collPersonnelStackss) {
                    foreach ($this->collPersonnelStackss as $obj) {
                        if ($obj->isNew()) {
                            $collPersonnelStackss[] = $obj;
                        }
                    }
                }

                $this->collPersonnelStackss = $collPersonnelStackss;
                $this->collPersonnelStackssPartial = false;
            }
        }

        return $this->collPersonnelStackss;
    }

    /**
     * Sets a collection of PersonnelStacks objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $personnelStackss A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setPersonnelStackss(PropelCollection $personnelStackss, PropelPDO $con = null)
    {
        $personnelStackssToDelete = $this->getPersonnelStackss(new Criteria(), $con)->diff($personnelStackss);


        $this->personnelStackssScheduledForDeletion = $personnelStackssToDelete;

        foreach ($personnelStackssToDelete as $personnelStacksRemoved) {
            $personnelStacksRemoved->setPersonnelStacksEmployee(null);
        }

        $this->collPersonnelStackss = null;
        foreach ($personnelStackss as $personnelStacks) {
            $this->addPersonnelStacks($personnelStacks);
        }

        $this->collPersonnelStackss = $personnelStackss;
        $this->collPersonnelStackssPartial = false;

        return $this;
    }

    /**
     * Returns the number of related PersonnelStacks objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related PersonnelStacks objects.
     * @throws PropelException
     */
    public function countPersonnelStackss(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collPersonnelStackssPartial && !$this->isNew();
        if (null === $this->collPersonnelStackss || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collPersonnelStackss) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getPersonnelStackss());
            }
            $query = PersonnelStacksQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByPersonnelStacksEmployee($this)
                ->count($con);
        }

        return count($this->collPersonnelStackss);
    }

    /**
     * Method called to associate a PersonnelStacks object to this object
     * through the PersonnelStacks foreign key attribute.
     *
     * @param    PersonnelStacks $l PersonnelStacks
     * @return Employee The current object (for fluent API support)
     */
    public function addPersonnelStacks(PersonnelStacks $l)
    {
        if ($this->collPersonnelStackss === null) {
            $this->initPersonnelStackss();
            $this->collPersonnelStackssPartial = true;
        }

        if (!in_array($l, $this->collPersonnelStackss->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddPersonnelStacks($l);

            if ($this->personnelStackssScheduledForDeletion and $this->personnelStackssScheduledForDeletion->contains($l)) {
                $this->personnelStackssScheduledForDeletion->remove($this->personnelStackssScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	PersonnelStacks $personnelStacks The personnelStacks object to add.
     */
    protected function doAddPersonnelStacks($personnelStacks)
    {
        $this->collPersonnelStackss[]= $personnelStacks;
        $personnelStacks->setPersonnelStacksEmployee($this);
    }

    /**
     * @param	PersonnelStacks $personnelStacks The personnelStacks object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removePersonnelStacks($personnelStacks)
    {
        if ($this->getPersonnelStackss()->contains($personnelStacks)) {
            $this->collPersonnelStackss->remove($this->collPersonnelStackss->search($personnelStacks));
            if (null === $this->personnelStackssScheduledForDeletion) {
                $this->personnelStackssScheduledForDeletion = clone $this->collPersonnelStackss;
                $this->personnelStackssScheduledForDeletion->clear();
            }
            $this->personnelStackssScheduledForDeletion[]= clone $personnelStacks;
            $personnelStacks->setPersonnelStacksEmployee(null);
        }

        return $this;
    }


    /**
     * If this collection has already been initialized with
     * an identical criteria, it returns the collection.
     * Otherwise if this Employee is new, it will return
     * an empty collection; or if this Employee has previously
     * been saved, it will retrieve related PersonnelStackss from storage.
     *
     * This method is protected by default in order to keep the public
     * api reasonable.  You can provide public methods for those you
     * actually need in Employee.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @param string $join_behavior optional join type to use (defaults to Criteria::LEFT_JOIN)
     * @return PropelObjectCollection|PersonnelStacks[] List of PersonnelStacks objects
     */
    public function getPersonnelStackssJoinPersonnelStacksAbsenceStack($criteria = null, $con = null, $join_behavior = Criteria::LEFT_JOIN)
    {
        $query = PersonnelStacksQuery::create(null, $criteria);
        $query->joinWith('PersonnelStacksAbsenceStack', $join_behavior);

        return $this->getPersonnelStackss($query, $con);
    }

    /**
     * Clears out the collStoredMonths collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addStoredMonths()
     */
    public function clearStoredMonths()
    {
        $this->collStoredMonths = null; // important to set this to null since that means it is uninitialized
        $this->collStoredMonthsPartial = null;

        return $this;
    }

    /**
     * reset is the collStoredMonths collection loaded partially
     *
     * @return void
     */
    public function resetPartialStoredMonths($v = true)
    {
        $this->collStoredMonthsPartial = $v;
    }

    /**
     * Initializes the collStoredMonths collection.
     *
     * By default this just sets the collStoredMonths collection to an empty array (like clearcollStoredMonths());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initStoredMonths($overrideExisting = true)
    {
        if (null !== $this->collStoredMonths && !$overrideExisting) {
            return;
        }
        $this->collStoredMonths = new PropelObjectCollection();
        $this->collStoredMonths->setModel('StoredMonth');
    }

    /**
     * Gets an array of StoredMonth objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|StoredMonth[] List of StoredMonth objects
     * @throws PropelException
     */
    public function getStoredMonths($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collStoredMonthsPartial && !$this->isNew();
        if (null === $this->collStoredMonths || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collStoredMonths) {
                // return empty collection
                $this->initStoredMonths();
            } else {
                $collStoredMonths = StoredMonthQuery::create(null, $criteria)
                    ->filterByStoredMonthEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collStoredMonthsPartial && count($collStoredMonths)) {
                      $this->initStoredMonths(false);

                      foreach ($collStoredMonths as $obj) {
                        if (false == $this->collStoredMonths->contains($obj)) {
                          $this->collStoredMonths->append($obj);
                        }
                      }

                      $this->collStoredMonthsPartial = true;
                    }

                    $collStoredMonths->getInternalIterator()->rewind();

                    return $collStoredMonths;
                }

                if ($partial && $this->collStoredMonths) {
                    foreach ($this->collStoredMonths as $obj) {
                        if ($obj->isNew()) {
                            $collStoredMonths[] = $obj;
                        }
                    }
                }

                $this->collStoredMonths = $collStoredMonths;
                $this->collStoredMonthsPartial = false;
            }
        }

        return $this->collStoredMonths;
    }

    /**
     * Sets a collection of StoredMonth objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $storedMonths A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setStoredMonths(PropelCollection $storedMonths, PropelPDO $con = null)
    {
        $storedMonthsToDelete = $this->getStoredMonths(new Criteria(), $con)->diff($storedMonths);


        $this->storedMonthsScheduledForDeletion = $storedMonthsToDelete;

        foreach ($storedMonthsToDelete as $storedMonthRemoved) {
            $storedMonthRemoved->setStoredMonthEmployee(null);
        }

        $this->collStoredMonths = null;
        foreach ($storedMonths as $storedMonth) {
            $this->addStoredMonth($storedMonth);
        }

        $this->collStoredMonths = $storedMonths;
        $this->collStoredMonthsPartial = false;

        return $this;
    }

    /**
     * Returns the number of related StoredMonth objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related StoredMonth objects.
     * @throws PropelException
     */
    public function countStoredMonths(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collStoredMonthsPartial && !$this->isNew();
        if (null === $this->collStoredMonths || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collStoredMonths) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getStoredMonths());
            }
            $query = StoredMonthQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByStoredMonthEmployee($this)
                ->count($con);
        }

        return count($this->collStoredMonths);
    }

    /**
     * Method called to associate a StoredMonth object to this object
     * through the StoredMonth foreign key attribute.
     *
     * @param    StoredMonth $l StoredMonth
     * @return Employee The current object (for fluent API support)
     */
    public function addStoredMonth(StoredMonth $l)
    {
        if ($this->collStoredMonths === null) {
            $this->initStoredMonths();
            $this->collStoredMonthsPartial = true;
        }

        if (!in_array($l, $this->collStoredMonths->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddStoredMonth($l);

            if ($this->storedMonthsScheduledForDeletion and $this->storedMonthsScheduledForDeletion->contains($l)) {
                $this->storedMonthsScheduledForDeletion->remove($this->storedMonthsScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	StoredMonth $storedMonth The storedMonth object to add.
     */
    protected function doAddStoredMonth($storedMonth)
    {
        $this->collStoredMonths[]= $storedMonth;
        $storedMonth->setStoredMonthEmployee($this);
    }

    /**
     * @param	StoredMonth $storedMonth The storedMonth object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removeStoredMonth($storedMonth)
    {
        if ($this->getStoredMonths()->contains($storedMonth)) {
            $this->collStoredMonths->remove($this->collStoredMonths->search($storedMonth));
            if (null === $this->storedMonthsScheduledForDeletion) {
                $this->storedMonthsScheduledForDeletion = clone $this->collStoredMonths;
                $this->storedMonthsScheduledForDeletion->clear();
            }
            $this->storedMonthsScheduledForDeletion[]= clone $storedMonth;
            $storedMonth->setStoredMonthEmployee(null);
        }

        return $this;
    }

    /**
     * Clears out the collStoredDays collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Employee The current object (for fluent API support)
     * @see        addStoredDays()
     */
    public function clearStoredDays()
    {
        $this->collStoredDays = null; // important to set this to null since that means it is uninitialized
        $this->collStoredDaysPartial = null;

        return $this;
    }

    /**
     * reset is the collStoredDays collection loaded partially
     *
     * @return void
     */
    public function resetPartialStoredDays($v = true)
    {
        $this->collStoredDaysPartial = $v;
    }

    /**
     * Initializes the collStoredDays collection.
     *
     * By default this just sets the collStoredDays collection to an empty array (like clearcollStoredDays());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initStoredDays($overrideExisting = true)
    {
        if (null !== $this->collStoredDays && !$overrideExisting) {
            return;
        }
        $this->collStoredDays = new PropelObjectCollection();
        $this->collStoredDays->setModel('StoredDay');
    }

    /**
     * Gets an array of StoredDay objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Employee is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|StoredDay[] List of StoredDay objects
     * @throws PropelException
     */
    public function getStoredDays($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collStoredDaysPartial && !$this->isNew();
        if (null === $this->collStoredDays || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collStoredDays) {
                // return empty collection
                $this->initStoredDays();
            } else {
                $collStoredDays = StoredDayQuery::create(null, $criteria)
                    ->filterByStoredDayEmployee($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collStoredDaysPartial && count($collStoredDays)) {
                      $this->initStoredDays(false);

                      foreach ($collStoredDays as $obj) {
                        if (false == $this->collStoredDays->contains($obj)) {
                          $this->collStoredDays->append($obj);
                        }
                      }

                      $this->collStoredDaysPartial = true;
                    }

                    $collStoredDays->getInternalIterator()->rewind();

                    return $collStoredDays;
                }

                if ($partial && $this->collStoredDays) {
                    foreach ($this->collStoredDays as $obj) {
                        if ($obj->isNew()) {
                            $collStoredDays[] = $obj;
                        }
                    }
                }

                $this->collStoredDays = $collStoredDays;
                $this->collStoredDaysPartial = false;
            }
        }

        return $this->collStoredDays;
    }

    /**
     * Sets a collection of StoredDay objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $storedDays A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Employee The current object (for fluent API support)
     */
    public function setStoredDays(PropelCollection $storedDays, PropelPDO $con = null)
    {
        $storedDaysToDelete = $this->getStoredDays(new Criteria(), $con)->diff($storedDays);


        $this->storedDaysScheduledForDeletion = $storedDaysToDelete;

        foreach ($storedDaysToDelete as $storedDayRemoved) {
            $storedDayRemoved->setStoredDayEmployee(null);
        }

        $this->collStoredDays = null;
        foreach ($storedDays as $storedDay) {
            $this->addStoredDay($storedDay);
        }

        $this->collStoredDays = $storedDays;
        $this->collStoredDaysPartial = false;

        return $this;
    }

    /**
     * Returns the number of related StoredDay objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related StoredDay objects.
     * @throws PropelException
     */
    public function countStoredDays(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collStoredDaysPartial && !$this->isNew();
        if (null === $this->collStoredDays || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collStoredDays) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getStoredDays());
            }
            $query = StoredDayQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByStoredDayEmployee($this)
                ->count($con);
        }

        return count($this->collStoredDays);
    }

    /**
     * Method called to associate a StoredDay object to this object
     * through the StoredDay foreign key attribute.
     *
     * @param    StoredDay $l StoredDay
     * @return Employee The current object (for fluent API support)
     */
    public function addStoredDay(StoredDay $l)
    {
        if ($this->collStoredDays === null) {
            $this->initStoredDays();
            $this->collStoredDaysPartial = true;
        }

        if (!in_array($l, $this->collStoredDays->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddStoredDay($l);

            if ($this->storedDaysScheduledForDeletion and $this->storedDaysScheduledForDeletion->contains($l)) {
                $this->storedDaysScheduledForDeletion->remove($this->storedDaysScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	StoredDay $storedDay The storedDay object to add.
     */
    protected function doAddStoredDay($storedDay)
    {
        $this->collStoredDays[]= $storedDay;
        $storedDay->setStoredDayEmployee($this);
    }

    /**
     * @param	StoredDay $storedDay The storedDay object to remove.
     * @return Employee The current object (for fluent API support)
     */
    public function removeStoredDay($storedDay)
    {
        if ($this->getStoredDays()->contains($storedDay)) {
            $this->collStoredDays->remove($this->collStoredDays->search($storedDay));
            if (null === $this->storedDaysScheduledForDeletion) {
                $this->storedDaysScheduledForDeletion = clone $this->collStoredDays;
                $this->storedDaysScheduledForDeletion->clear();
            }
            $this->storedDaysScheduledForDeletion[]= clone $storedDay;
            $storedDay->setStoredDayEmployee(null);
        }

        return $this;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->employee_id = null;
        $this->name = null;
        $this->surname = null;
        $this->gender = null;
        $this->birthdate = null;
        $this->fiscal_code = null;
        $this->residence_id = null;
        $this->address_id = null;
        $this->part_spesa = null;
        $this->bank = null;
        $this->liq_office = null;
        $this->inps = null;
        $this->insur_qual = null;
        $this->fore = null;
        $this->asl = null;
        $this->adm_code = null;
        $this->way_pay = null;
        $this->liquid_group = null;
        $this->contr_code = null;
        $this->contr_type = null;
        $this->contr_cat = null;
        $this->ssp_frm_pmnt = null;
        $this->personal_data = null;
        $this->susp = null;
        $this->payment_group = null;
        $this->priv_ret_type = null;
        $this->social_position = null;
        $this->active = null;
        $this->statal_code = null;
        $this->fiscal_city_code = null;
        $this->birthplace = null;
        $this->income = null;
        $this->state_birth = null;
        $this->citizenship = null;
        $this->id_sissi = null;
        $this->dom_first_prev_year = null;
        $this->dom_last_prev_year = null;
        $this->dom_first_curr_year = null;
        $this->qualification = null;
        $this->liquid_office_id = null;
        $this->badge_number = null;
        $this->tolerance_in = null;
        $this->tolerance_out = null;
        $this->flexibility = null;
        $this->generic_tolerance = null;
        $this->negative_round = null;
        $this->recover_hours = null;
        $this->max_extraordinary_in = null;
        $this->max_extraordinary_out = null;
        $this->min_extraordinary_in = null;
        $this->min_extraordinary_out = null;
        $this->step_out = null;
        $this->step_in = null;
        $this->max_break = null;
        $this->max_cont_work = null;
        $this->simplified_ata_settings = null;
        $this->tolerance_in_und = null;
        $this->tolerance_out_und = null;
        $this->max_undefined_in = null;
        $this->max_undefined_out = null;
        $this->min_undefined_in = null;
        $this->min_undefined_out = null;
        $this->step_out_und = null;
        $this->step_in_und = null;
        $this->undefined_parameter_active = null;
        $this->min_extraordinary_total = null;
        $this->max_extraordinary_total = null;
        $this->min_undefined_total = null;
        $this->max_undefined_total = null;
        $this->step_total_undefined = null;
        $this->step_total_extraordinary = null;
        $this->lunch_duration = null;
        $this->lunch_deductible = null;
        $this->service_deductible = null;
        $this->min_undefined_lunch = null;
        $this->min_extraordinary_lunch = null;
        $this->max_undefined_lunch = null;
        $this->max_extraordinary_lunch = null;
        $this->step_lunch_undefined = null;
        $this->step_lunch_extraordinary = null;
        $this->break_after_max_work = null;
        $this->unit_recover_hours = null;
        $this->max_work = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collInstitutes) {
                foreach ($this->collInstitutes as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collAbsencess) {
                foreach ($this->collAbsencess as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collTimetables) {
                foreach ($this->collTimetables as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collPresences) {
                foreach ($this->collPresences as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collPersonnelStackss) {
                foreach ($this->collPersonnelStackss as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collStoredMonths) {
                foreach ($this->collStoredMonths as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collStoredDays) {
                foreach ($this->collStoredDays as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aResidenceKey instanceof Persistent) {
              $this->aResidenceKey->clearAllReferences($deep);
            }
            if ($this->aAddressKey instanceof Persistent) {
              $this->aAddressKey->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collInstitutes instanceof PropelCollection) {
            $this->collInstitutes->clearIterator();
        }
        $this->collInstitutes = null;
        if ($this->collAbsencess instanceof PropelCollection) {
            $this->collAbsencess->clearIterator();
        }
        $this->collAbsencess = null;
        if ($this->collTimetables instanceof PropelCollection) {
            $this->collTimetables->clearIterator();
        }
        $this->collTimetables = null;
        if ($this->collPresences instanceof PropelCollection) {
            $this->collPresences->clearIterator();
        }
        $this->collPresences = null;
        if ($this->collPersonnelStackss instanceof PropelCollection) {
            $this->collPersonnelStackss->clearIterator();
        }
        $this->collPersonnelStackss = null;
        if ($this->collStoredMonths instanceof PropelCollection) {
            $this->collStoredMonths->clearIterator();
        }
        $this->collStoredMonths = null;
        if ($this->collStoredDays instanceof PropelCollection) {
            $this->collStoredDays->clearIterator();
        }
        $this->collStoredDays = null;
        $this->aResidenceKey = null;
        $this->aAddressKey = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(EmployeePeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
