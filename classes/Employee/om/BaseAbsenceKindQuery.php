<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceKind;
use Employee\AbsenceKindPeer;
use Employee\AbsenceKindQuery;
use Employee\AbsenceStack;
use Employee\Absences;

/**
 * Base class that represents a query for the 'absence_kind' table.
 *
 *
 *
 * @method AbsenceKindQuery orderByCode($order = Criteria::ASC) Order by the code column
 * @method AbsenceKindQuery orderByDescription($order = Criteria::ASC) Order by the description column
 * @method AbsenceKindQuery orderByAbsenceStack($order = Criteria::ASC) Order by the absence_stack column
 * @method AbsenceKindQuery orderByDateStart($order = Criteria::ASC) Order by the date_start column
 * @method AbsenceKindQuery orderByDateEnd($order = Criteria::ASC) Order by the date_end column
 * @method AbsenceKindQuery orderByCalcFestivities($order = Criteria::ASC) Order by the calc_festivities column
 * @method AbsenceKindQuery orderByCalcFerials($order = Criteria::ASC) Order by the calc_ferials column
 *
 * @method AbsenceKindQuery groupByCode() Group by the code column
 * @method AbsenceKindQuery groupByDescription() Group by the description column
 * @method AbsenceKindQuery groupByAbsenceStack() Group by the absence_stack column
 * @method AbsenceKindQuery groupByDateStart() Group by the date_start column
 * @method AbsenceKindQuery groupByDateEnd() Group by the date_end column
 * @method AbsenceKindQuery groupByCalcFestivities() Group by the calc_festivities column
 * @method AbsenceKindQuery groupByCalcFerials() Group by the calc_ferials column
 *
 * @method AbsenceKindQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AbsenceKindQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AbsenceKindQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AbsenceKindQuery leftJoinAbsenceKindAbsenceStack($relationAlias = null) Adds a LEFT JOIN clause to the query using the AbsenceKindAbsenceStack relation
 * @method AbsenceKindQuery rightJoinAbsenceKindAbsenceStack($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AbsenceKindAbsenceStack relation
 * @method AbsenceKindQuery innerJoinAbsenceKindAbsenceStack($relationAlias = null) Adds a INNER JOIN clause to the query using the AbsenceKindAbsenceStack relation
 *
 * @method AbsenceKindQuery leftJoinAbsences($relationAlias = null) Adds a LEFT JOIN clause to the query using the Absences relation
 * @method AbsenceKindQuery rightJoinAbsences($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Absences relation
 * @method AbsenceKindQuery innerJoinAbsences($relationAlias = null) Adds a INNER JOIN clause to the query using the Absences relation
 *
 * @method AbsenceKind findOne(PropelPDO $con = null) Return the first AbsenceKind matching the query
 * @method AbsenceKind findOneOrCreate(PropelPDO $con = null) Return the first AbsenceKind matching the query, or a new AbsenceKind object populated from the query conditions when no match is found
 *
 * @method AbsenceKind findOneByDescription(string $description) Return the first AbsenceKind filtered by the description column
 * @method AbsenceKind findOneByAbsenceStack(string $absence_stack) Return the first AbsenceKind filtered by the absence_stack column
 * @method AbsenceKind findOneByDateStart(string $date_start) Return the first AbsenceKind filtered by the date_start column
 * @method AbsenceKind findOneByDateEnd(string $date_end) Return the first AbsenceKind filtered by the date_end column
 * @method AbsenceKind findOneByCalcFestivities(boolean $calc_festivities) Return the first AbsenceKind filtered by the calc_festivities column
 * @method AbsenceKind findOneByCalcFerials(boolean $calc_ferials) Return the first AbsenceKind filtered by the calc_ferials column
 *
 * @method array findByCode(string $code) Return AbsenceKind objects filtered by the code column
 * @method array findByDescription(string $description) Return AbsenceKind objects filtered by the description column
 * @method array findByAbsenceStack(string $absence_stack) Return AbsenceKind objects filtered by the absence_stack column
 * @method array findByDateStart(string $date_start) Return AbsenceKind objects filtered by the date_start column
 * @method array findByDateEnd(string $date_end) Return AbsenceKind objects filtered by the date_end column
 * @method array findByCalcFestivities(boolean $calc_festivities) Return AbsenceKind objects filtered by the calc_festivities column
 * @method array findByCalcFerials(boolean $calc_ferials) Return AbsenceKind objects filtered by the calc_ferials column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseAbsenceKindQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAbsenceKindQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\AbsenceKind';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AbsenceKindQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AbsenceKindQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AbsenceKindQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AbsenceKindQuery) {
            return $criteria;
        }
        $query = new AbsenceKindQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   AbsenceKind|AbsenceKind[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AbsenceKindPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AbsenceKindPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AbsenceKind A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByCode($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 AbsenceKind A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "code", "description", "absence_stack", "date_start", "date_end", "calc_festivities", "calc_ferials" FROM "absence_kind" WHERE "code" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_STR);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new AbsenceKind();
            $obj->hydrate($row);
            AbsenceKindPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return AbsenceKind|AbsenceKind[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|AbsenceKind[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AbsenceKindPeer::CODE, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AbsenceKindPeer::CODE, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the code column
     *
     * Example usage:
     * <code>
     * $query->filterByCode('fooValue');   // WHERE code = 'fooValue'
     * $query->filterByCode('%fooValue%'); // WHERE code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $code The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByCode($code = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($code)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $code)) {
                $code = str_replace('*', '%', $code);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AbsenceKindPeer::CODE, $code, $comparison);
    }

    /**
     * Filter the query on the description column
     *
     * Example usage:
     * <code>
     * $query->filterByDescription('fooValue');   // WHERE description = 'fooValue'
     * $query->filterByDescription('%fooValue%'); // WHERE description LIKE '%fooValue%'
     * </code>
     *
     * @param     string $description The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByDescription($description = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($description)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $description)) {
                $description = str_replace('*', '%', $description);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AbsenceKindPeer::DESCRIPTION, $description, $comparison);
    }

    /**
     * Filter the query on the absence_stack column
     *
     * Example usage:
     * <code>
     * $query->filterByAbsenceStack(1234); // WHERE absence_stack = 1234
     * $query->filterByAbsenceStack(array(12, 34)); // WHERE absence_stack IN (12, 34)
     * $query->filterByAbsenceStack(array('min' => 12)); // WHERE absence_stack >= 12
     * $query->filterByAbsenceStack(array('max' => 12)); // WHERE absence_stack <= 12
     * </code>
     *
     * @see       filterByAbsenceKindAbsenceStack()
     *
     * @param     mixed $absenceStack The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByAbsenceStack($absenceStack = null, $comparison = null)
    {
        if (is_array($absenceStack)) {
            $useMinMax = false;
            if (isset($absenceStack['min'])) {
                $this->addUsingAlias(AbsenceKindPeer::ABSENCE_STACK, $absenceStack['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($absenceStack['max'])) {
                $this->addUsingAlias(AbsenceKindPeer::ABSENCE_STACK, $absenceStack['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceKindPeer::ABSENCE_STACK, $absenceStack, $comparison);
    }

    /**
     * Filter the query on the date_start column
     *
     * Example usage:
     * <code>
     * $query->filterByDateStart('2011-03-14'); // WHERE date_start = '2011-03-14'
     * $query->filterByDateStart('now'); // WHERE date_start = '2011-03-14'
     * $query->filterByDateStart(array('max' => 'yesterday')); // WHERE date_start < '2011-03-13'
     * </code>
     *
     * @param     mixed $dateStart The value to use as filter.
     *              Values can be integers (unix timestamps), DateTime objects, or strings.
     *              Empty strings are treated as NULL.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByDateStart($dateStart = null, $comparison = null)
    {
        if (is_array($dateStart)) {
            $useMinMax = false;
            if (isset($dateStart['min'])) {
                $this->addUsingAlias(AbsenceKindPeer::DATE_START, $dateStart['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateStart['max'])) {
                $this->addUsingAlias(AbsenceKindPeer::DATE_START, $dateStart['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceKindPeer::DATE_START, $dateStart, $comparison);
    }

    /**
     * Filter the query on the date_end column
     *
     * Example usage:
     * <code>
     * $query->filterByDateEnd('2011-03-14'); // WHERE date_end = '2011-03-14'
     * $query->filterByDateEnd('now'); // WHERE date_end = '2011-03-14'
     * $query->filterByDateEnd(array('max' => 'yesterday')); // WHERE date_end < '2011-03-13'
     * </code>
     *
     * @param     mixed $dateEnd The value to use as filter.
     *              Values can be integers (unix timestamps), DateTime objects, or strings.
     *              Empty strings are treated as NULL.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByDateEnd($dateEnd = null, $comparison = null)
    {
        if (is_array($dateEnd)) {
            $useMinMax = false;
            if (isset($dateEnd['min'])) {
                $this->addUsingAlias(AbsenceKindPeer::DATE_END, $dateEnd['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateEnd['max'])) {
                $this->addUsingAlias(AbsenceKindPeer::DATE_END, $dateEnd['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsenceKindPeer::DATE_END, $dateEnd, $comparison);
    }

    /**
     * Filter the query on the calc_festivities column
     *
     * Example usage:
     * <code>
     * $query->filterByCalcFestivities(true); // WHERE calc_festivities = true
     * $query->filterByCalcFestivities('yes'); // WHERE calc_festivities = true
     * </code>
     *
     * @param     boolean|string $calcFestivities The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByCalcFestivities($calcFestivities = null, $comparison = null)
    {
        if (is_string($calcFestivities)) {
            $calcFestivities = in_array(strtolower($calcFestivities), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(AbsenceKindPeer::CALC_FESTIVITIES, $calcFestivities, $comparison);
    }

    /**
     * Filter the query on the calc_ferials column
     *
     * Example usage:
     * <code>
     * $query->filterByCalcFerials(true); // WHERE calc_ferials = true
     * $query->filterByCalcFerials('yes'); // WHERE calc_ferials = true
     * </code>
     *
     * @param     boolean|string $calcFerials The value to use as filter.
     *              Non-boolean arguments are converted using the following rules:
     *                * 1, '1', 'true',  'on',  and 'yes' are converted to boolean true
     *                * 0, '0', 'false', 'off', and 'no'  are converted to boolean false
     *              Check on string values is case insensitive (so 'FaLsE' is seen as 'false').
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function filterByCalcFerials($calcFerials = null, $comparison = null)
    {
        if (is_string($calcFerials)) {
            $calcFerials = in_array(strtolower($calcFerials), array('false', 'off', '-', 'no', 'n', '0', '')) ? false : true;
        }

        return $this->addUsingAlias(AbsenceKindPeer::CALC_FERIALS, $calcFerials, $comparison);
    }

    /**
     * Filter the query by a related AbsenceStack object
     *
     * @param   AbsenceStack|PropelObjectCollection $absenceStack The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsenceKindQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAbsenceKindAbsenceStack($absenceStack, $comparison = null)
    {
        if ($absenceStack instanceof AbsenceStack) {
            return $this
                ->addUsingAlias(AbsenceKindPeer::ABSENCE_STACK, $absenceStack->getId(), $comparison);
        } elseif ($absenceStack instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AbsenceKindPeer::ABSENCE_STACK, $absenceStack->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByAbsenceKindAbsenceStack() only accepts arguments of type AbsenceStack or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AbsenceKindAbsenceStack relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function joinAbsenceKindAbsenceStack($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AbsenceKindAbsenceStack');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AbsenceKindAbsenceStack');
        }

        return $this;
    }

    /**
     * Use the AbsenceKindAbsenceStack relation AbsenceStack object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsenceStackQuery A secondary query class using the current class as primary query
     */
    public function useAbsenceKindAbsenceStackQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAbsenceKindAbsenceStack($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AbsenceKindAbsenceStack', '\Employee\AbsenceStackQuery');
    }

    /**
     * Filter the query by a related Absences object
     *
     * @param   Absences|PropelObjectCollection $absences  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsenceKindQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAbsences($absences, $comparison = null)
    {
        if ($absences instanceof Absences) {
            return $this
                ->addUsingAlias(AbsenceKindPeer::CODE, $absences->getAbKind(), $comparison);
        } elseif ($absences instanceof PropelObjectCollection) {
            return $this
                ->useAbsencesQuery()
                ->filterByPrimaryKeys($absences->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByAbsences() only accepts arguments of type Absences or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Absences relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function joinAbsences($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Absences');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Absences');
        }

        return $this;
    }

    /**
     * Use the Absences relation Absences object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsencesQuery A secondary query class using the current class as primary query
     */
    public function useAbsencesQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAbsences($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Absences', '\Employee\AbsencesQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   AbsenceKind $absenceKind Object to remove from the list of results
     *
     * @return AbsenceKindQuery The current query, for fluid interface
     */
    public function prune($absenceKind = null)
    {
        if ($absenceKind) {
            $this->addUsingAlias(AbsenceKindPeer::CODE, $absenceKind->getCode(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
