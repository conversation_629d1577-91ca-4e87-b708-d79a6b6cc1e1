<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Employee\Decreti;
use Employee\DecretiPeer;
use Employee\DecretiQuery;

/**
 * Base class that represents a row from the 'decreti' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseDecreti extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\DecretiPeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        DecretiPeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id field.
     * @var        int
     */
    protected $id;

    /**
     * The value for the absence_kind field.
     * @var        string
     */
    protected $absence_kind;

    /**
     * The value for the employee_kind field.
     * @var        string
     */
    protected $employee_kind;

    /**
     * The value for the employee_role field.
     * @var        string
     */
    protected $employee_role;

    /**
     * The value for the html field.
     * @var        string
     */
    protected $html;

    /**
     * The value for the time_back field.
     * @var        string
     */
    protected $time_back;

    /**
     * The value for the prev_abs_kind field.
     * @var        string
     */
    protected $prev_abs_kind;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Get the [id] column value.
     *
     * @return int
     */
    public function getId()
    {

        return $this->id;
    }

    /**
     * Get the [absence_kind] column value.
     *
     * @return string
     */
    public function getAbsenceKind()
    {

        return $this->absence_kind;
    }

    /**
     * Get the [employee_kind] column value.
     *
     * @return string
     */
    public function getEmployeeKind()
    {

        return $this->employee_kind;
    }

    /**
     * Get the [employee_role] column value.
     *
     * @return string
     */
    public function getEmployeeRole()
    {

        return $this->employee_role;
    }

    /**
     * Get the [html] column value.
     *
     * @return string
     */
    public function getHtml()
    {

        return $this->html;
    }

    /**
     * Get the [time_back] column value.
     *
     * @return string
     */
    public function getTimeBack()
    {

        return $this->time_back;
    }

    /**
     * Get the [prev_abs_kind] column value.
     *
     * @return string
     */
    public function getPrevAbsKind()
    {

        return $this->prev_abs_kind;
    }

    /**
     * Set the value of [id] column.
     *
     * @param  int $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id !== $v) {
            $this->id = $v;
            $this->modifiedColumns[] = DecretiPeer::ID;
        }


        return $this;
    } // setId()

    /**
     * Set the value of [absence_kind] column.
     *
     * @param  string $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setAbsenceKind($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->absence_kind !== $v) {
            $this->absence_kind = $v;
            $this->modifiedColumns[] = DecretiPeer::ABSENCE_KIND;
        }


        return $this;
    } // setAbsenceKind()

    /**
     * Set the value of [employee_kind] column.
     *
     * @param  string $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setEmployeeKind($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->employee_kind !== $v) {
            $this->employee_kind = $v;
            $this->modifiedColumns[] = DecretiPeer::EMPLOYEE_KIND;
        }


        return $this;
    } // setEmployeeKind()

    /**
     * Set the value of [employee_role] column.
     *
     * @param  string $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setEmployeeRole($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->employee_role !== $v) {
            $this->employee_role = $v;
            $this->modifiedColumns[] = DecretiPeer::EMPLOYEE_ROLE;
        }


        return $this;
    } // setEmployeeRole()

    /**
     * Set the value of [html] column.
     *
     * @param  string $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setHtml($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->html !== $v) {
            $this->html = $v;
            $this->modifiedColumns[] = DecretiPeer::HTML;
        }


        return $this;
    } // setHtml()

    /**
     * Set the value of [time_back] column.
     *
     * @param  string $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setTimeBack($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->time_back !== $v) {
            $this->time_back = $v;
            $this->modifiedColumns[] = DecretiPeer::TIME_BACK;
        }


        return $this;
    } // setTimeBack()

    /**
     * Set the value of [prev_abs_kind] column.
     *
     * @param  string $v new value
     * @return Decreti The current object (for fluent API support)
     */
    public function setPrevAbsKind($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->prev_abs_kind !== $v) {
            $this->prev_abs_kind = $v;
            $this->modifiedColumns[] = DecretiPeer::PREV_ABS_KIND;
        }


        return $this;
    } // setPrevAbsKind()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->absence_kind = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->employee_kind = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->employee_role = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->html = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->time_back = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->prev_abs_kind = ($row[$startcol + 6] !== null) ? (string) $row[$startcol + 6] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 7; // 7 = DecretiPeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Decreti object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(DecretiPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = DecretiPeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(DecretiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = DecretiQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(DecretiPeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                DecretiPeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = DecretiPeer::ID;
        if (null !== $this->id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . DecretiPeer::ID . ')');
        }
        if (null === $this->id) {
            try {
                $stmt = $con->query("SELECT nextval('decreti_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(DecretiPeer::ID)) {
            $modifiedColumns[':p' . $index++]  = '"id"';
        }
        if ($this->isColumnModified(DecretiPeer::ABSENCE_KIND)) {
            $modifiedColumns[':p' . $index++]  = '"absence_kind"';
        }
        if ($this->isColumnModified(DecretiPeer::EMPLOYEE_KIND)) {
            $modifiedColumns[':p' . $index++]  = '"employee_kind"';
        }
        if ($this->isColumnModified(DecretiPeer::EMPLOYEE_ROLE)) {
            $modifiedColumns[':p' . $index++]  = '"employee_role"';
        }
        if ($this->isColumnModified(DecretiPeer::HTML)) {
            $modifiedColumns[':p' . $index++]  = '"html"';
        }
        if ($this->isColumnModified(DecretiPeer::TIME_BACK)) {
            $modifiedColumns[':p' . $index++]  = '"time_back"';
        }
        if ($this->isColumnModified(DecretiPeer::PREV_ABS_KIND)) {
            $modifiedColumns[':p' . $index++]  = '"prev_abs_kind"';
        }

        $sql = sprintf(
            'INSERT INTO "decreti" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id"':
                        $stmt->bindValue($identifier, $this->id, PDO::PARAM_INT);
                        break;
                    case '"absence_kind"':
                        $stmt->bindValue($identifier, $this->absence_kind, PDO::PARAM_STR);
                        break;
                    case '"employee_kind"':
                        $stmt->bindValue($identifier, $this->employee_kind, PDO::PARAM_STR);
                        break;
                    case '"employee_role"':
                        $stmt->bindValue($identifier, $this->employee_role, PDO::PARAM_STR);
                        break;
                    case '"html"':
                        $stmt->bindValue($identifier, $this->html, PDO::PARAM_STR);
                        break;
                    case '"time_back"':
                        $stmt->bindValue($identifier, $this->time_back, PDO::PARAM_STR);
                        break;
                    case '"prev_abs_kind"':
                        $stmt->bindValue($identifier, $this->prev_abs_kind, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            if (($retval = DecretiPeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = DecretiPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getId();
                break;
            case 1:
                return $this->getAbsenceKind();
                break;
            case 2:
                return $this->getEmployeeKind();
                break;
            case 3:
                return $this->getEmployeeRole();
                break;
            case 4:
                return $this->getHtml();
                break;
            case 5:
                return $this->getTimeBack();
                break;
            case 6:
                return $this->getPrevAbsKind();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array())
    {
        if (isset($alreadyDumpedObjects['Decreti'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Decreti'][$this->getPrimaryKey()] = true;
        $keys = DecretiPeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getId(),
            $keys[1] => $this->getAbsenceKind(),
            $keys[2] => $this->getEmployeeKind(),
            $keys[3] => $this->getEmployeeRole(),
            $keys[4] => $this->getHtml(),
            $keys[5] => $this->getTimeBack(),
            $keys[6] => $this->getPrevAbsKind(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }


        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = DecretiPeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setId($value);
                break;
            case 1:
                $this->setAbsenceKind($value);
                break;
            case 2:
                $this->setEmployeeKind($value);
                break;
            case 3:
                $this->setEmployeeRole($value);
                break;
            case 4:
                $this->setHtml($value);
                break;
            case 5:
                $this->setTimeBack($value);
                break;
            case 6:
                $this->setPrevAbsKind($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = DecretiPeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setAbsenceKind($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setEmployeeKind($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setEmployeeRole($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setHtml($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setTimeBack($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setPrevAbsKind($arr[$keys[6]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(DecretiPeer::DATABASE_NAME);

        if ($this->isColumnModified(DecretiPeer::ID)) $criteria->add(DecretiPeer::ID, $this->id);
        if ($this->isColumnModified(DecretiPeer::ABSENCE_KIND)) $criteria->add(DecretiPeer::ABSENCE_KIND, $this->absence_kind);
        if ($this->isColumnModified(DecretiPeer::EMPLOYEE_KIND)) $criteria->add(DecretiPeer::EMPLOYEE_KIND, $this->employee_kind);
        if ($this->isColumnModified(DecretiPeer::EMPLOYEE_ROLE)) $criteria->add(DecretiPeer::EMPLOYEE_ROLE, $this->employee_role);
        if ($this->isColumnModified(DecretiPeer::HTML)) $criteria->add(DecretiPeer::HTML, $this->html);
        if ($this->isColumnModified(DecretiPeer::TIME_BACK)) $criteria->add(DecretiPeer::TIME_BACK, $this->time_back);
        if ($this->isColumnModified(DecretiPeer::PREV_ABS_KIND)) $criteria->add(DecretiPeer::PREV_ABS_KIND, $this->prev_abs_kind);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(DecretiPeer::DATABASE_NAME);
        $criteria->add(DecretiPeer::ID, $this->id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getId();
    }

    /**
     * Generic method to set the primary key (id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Decreti (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setAbsenceKind($this->getAbsenceKind());
        $copyObj->setEmployeeKind($this->getEmployeeKind());
        $copyObj->setEmployeeRole($this->getEmployeeRole());
        $copyObj->setHtml($this->getHtml());
        $copyObj->setTimeBack($this->getTimeBack());
        $copyObj->setPrevAbsKind($this->getPrevAbsKind());
        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Decreti Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return DecretiPeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new DecretiPeer();
        }

        return self::$peer;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id = null;
        $this->absence_kind = null;
        $this->employee_kind = null;
        $this->employee_role = null;
        $this->html = null;
        $this->time_back = null;
        $this->prev_abs_kind = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(DecretiPeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
