<?php

namespace Employee\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelException;
use \PropelPDO;
use Budget\Activity;
use Budget\ActivityQuery;
use Employee\Employee;
use Employee\EmployeeQuery;
use Employee\Presence;
use Employee\PresencePeer;
use Employee\PresenceQuery;

/**
 * Base class that represents a row from the 'personnel_presences' table.
 *
 *
 *
 * @package    propel.generator.Employee.om
 */
abstract class BasePresence extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Employee\\PresencePeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        PresencePeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the personnel_presence_id field.
     * @var        int
     */
    protected $personnel_presence_id;

    /**
     * The value for the employee_id field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $employee_id;

    /**
     * The value for the project_id field.
     * @var        string
     */
    protected $project_id;

    /**
     * The value for the project_edit_id field.
     * @var        string
     */
    protected $project_edit_id;

    /**
     * The value for the date field.
     * @var        string
     */
    protected $date;

    /**
     * The value for the date_edit field.
     * @var        string
     */
    protected $date_edit;

    /**
     * The value for the type field.
     * Note: this column has a database default value of: 1
     * @var        int
     */
    protected $type;

    /**
     * The value for the type_edit field.
     * Note: this column has a database default value of: 1
     * @var        int
     */
    protected $type_edit;

    /**
     * The value for the original_inout field.
     * Note: this column has a database default value of: -1
     * @var        int
     */
    protected $original_inout;

    /**
     * The value for the original_inout_edit field.
     * Note: this column has a database default value of: 1
     * @var        int
     */
    protected $original_inout_edit;

    /**
     * The value for the description field.
     * Note: this column has a database default value of: ''
     * @var        string
     */
    protected $description;

    /**
     * The value for the hour_type_id field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $hour_type_id;

    /**
     * The value for the hour_type_edit_id field.
     * Note: this column has a database default value of: '-1'
     * @var        string
     */
    protected $hour_type_edit_id;

    /**
     * The value for the insertion_mode field.
     * Note: this column has a database default value of: 'T'
     * @var        string
     */
    protected $insertion_mode;

    /**
     * @var        Employee
     */
    protected $aPresenceEmployee;

    /**
     * @var        Activity
     */
    protected $aPresenceProject;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * Applies default values to this object.
     * This method should be called from the object's constructor (or
     * equivalent initialization method).
     * @see        __construct()
     */
    public function applyDefaultValues()
    {
        $this->employee_id = '-1';
        $this->type = 1;
        $this->type_edit = 1;
        $this->original_inout = -1;
        $this->original_inout_edit = 1;
        $this->description = '';
        $this->hour_type_id = '-1';
        $this->hour_type_edit_id = '-1';
        $this->insertion_mode = 'T';
    }

    /**
     * Initializes internal state of BasePresence object.
     * @see        applyDefaults()
     */
    public function __construct()
    {
        parent::__construct();
        $this->applyDefaultValues();
    }

    /**
     * Get the [personnel_presence_id] column value.
     *
     * @return int
     */
    public function getPersonnelPresenceId()
    {

        return $this->personnel_presence_id;
    }

    /**
     * Get the [employee_id] column value.
     *
     * @return string
     */
    public function getEmployeeId()
    {

        return $this->employee_id;
    }

    /**
     * Get the [project_id] column value.
     *
     * @return string
     */
    public function getProjectId()
    {

        return $this->project_id;
    }

    /**
     * Get the [project_edit_id] column value.
     *
     * @return string
     */
    public function getProjectIdEdit()
    {

        return $this->project_edit_id;
    }

    /**
     * Get the [date] column value.
     *
     * @return string
     */
    public function getDate()
    {

        return $this->date;
    }

    /**
     * Get the [date_edit] column value.
     *
     * @return string
     */
    public function getDateEdit()
    {

        return $this->date_edit;
    }

    /**
     * Get the [type] column value.
     *
     * @return int
     */
    public function getType()
    {

        return $this->type;
    }

    /**
     * Get the [type_edit] column value.
     *
     * @return int
     */
    public function getTypeEdit()
    {

        return $this->type_edit;
    }

    /**
     * Get the [original_inout] column value.
     *
     * @return int
     */
    public function getOriginalInOut()
    {

        return $this->original_inout;
    }

    /**
     * Get the [original_inout_edit] column value.
     *
     * @return int
     */
    public function getOriginalInOutEdit()
    {

        return $this->original_inout_edit;
    }

    /**
     * Get the [description] column value.
     *
     * @return string
     */
    public function getDescription()
    {

        return $this->description;
    }

    /**
     * Get the [hour_type_id] column value.
     *
     * @return string
     */
    public function getHourTypeId()
    {

        return $this->hour_type_id;
    }

    /**
     * Get the [hour_type_edit_id] column value.
     *
     * @return string
     */
    public function getHourTypeIdEdit()
    {

        return $this->hour_type_edit_id;
    }

    /**
     * Get the [insertion_mode] column value.
     *
     * @return string
     */
    public function getInsertionMode()
    {

        return $this->insertion_mode;
    }

    /**
     * Set the value of [personnel_presence_id] column.
     *
     * @param  int $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setPersonnelPresenceId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->personnel_presence_id !== $v) {
            $this->personnel_presence_id = $v;
            $this->modifiedColumns[] = PresencePeer::PERSONNEL_PRESENCE_ID;
        }


        return $this;
    } // setPersonnelPresenceId()

    /**
     * Set the value of [employee_id] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setEmployeeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->employee_id !== $v) {
            $this->employee_id = $v;
            $this->modifiedColumns[] = PresencePeer::EMPLOYEE_ID;
        }

        if ($this->aPresenceEmployee !== null && $this->aPresenceEmployee->getEmployeeId() !== $v) {
            $this->aPresenceEmployee = null;
        }


        return $this;
    } // setEmployeeId()

    /**
     * Set the value of [project_id] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setProjectId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->project_id !== $v) {
            $this->project_id = $v;
            $this->modifiedColumns[] = PresencePeer::PROJECT_ID;
        }


        return $this;
    } // setProjectId()

    /**
     * Set the value of [project_edit_id] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setProjectIdEdit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->project_edit_id !== $v) {
            $this->project_edit_id = $v;
            $this->modifiedColumns[] = PresencePeer::PROJECT_EDIT_ID;
        }

        if ($this->aPresenceProject !== null && $this->aPresenceProject->getActivityId() !== $v) {
            $this->aPresenceProject = null;
        }


        return $this;
    } // setProjectIdEdit()

    /**
     * Set the value of [date] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setDate($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->date !== $v) {
            $this->date = $v;
            $this->modifiedColumns[] = PresencePeer::DATE;
        }


        return $this;
    } // setDate()

    /**
     * Set the value of [date_edit] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setDateEdit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->date_edit !== $v) {
            $this->date_edit = $v;
            $this->modifiedColumns[] = PresencePeer::DATE_EDIT;
        }


        return $this;
    } // setDateEdit()

    /**
     * Set the value of [type] column.
     *
     * @param  int $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setType($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->type !== $v) {
            $this->type = $v;
            $this->modifiedColumns[] = PresencePeer::TYPE;
        }


        return $this;
    } // setType()

    /**
     * Set the value of [type_edit] column.
     *
     * @param  int $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setTypeEdit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->type_edit !== $v) {
            $this->type_edit = $v;
            $this->modifiedColumns[] = PresencePeer::TYPE_EDIT;
        }


        return $this;
    } // setTypeEdit()

    /**
     * Set the value of [original_inout] column.
     *
     * @param  int $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setOriginalInOut($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->original_inout !== $v) {
            $this->original_inout = $v;
            $this->modifiedColumns[] = PresencePeer::ORIGINAL_INOUT;
        }


        return $this;
    } // setOriginalInOut()

    /**
     * Set the value of [original_inout_edit] column.
     *
     * @param  int $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setOriginalInOutEdit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->original_inout_edit !== $v) {
            $this->original_inout_edit = $v;
            $this->modifiedColumns[] = PresencePeer::ORIGINAL_INOUT_EDIT;
        }


        return $this;
    } // setOriginalInOutEdit()

    /**
     * Set the value of [description] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setDescription($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->description !== $v) {
            $this->description = $v;
            $this->modifiedColumns[] = PresencePeer::DESCRIPTION;
        }


        return $this;
    } // setDescription()

    /**
     * Set the value of [hour_type_id] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setHourTypeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->hour_type_id !== $v) {
            $this->hour_type_id = $v;
            $this->modifiedColumns[] = PresencePeer::HOUR_TYPE_ID;
        }


        return $this;
    } // setHourTypeId()

    /**
     * Set the value of [hour_type_edit_id] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setHourTypeIdEdit($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->hour_type_edit_id !== $v) {
            $this->hour_type_edit_id = $v;
            $this->modifiedColumns[] = PresencePeer::HOUR_TYPE_EDIT_ID;
        }


        return $this;
    } // setHourTypeIdEdit()

    /**
     * Set the value of [insertion_mode] column.
     *
     * @param  string $v new value
     * @return Presence The current object (for fluent API support)
     */
    public function setInsertionMode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->insertion_mode !== $v) {
            $this->insertion_mode = $v;
            $this->modifiedColumns[] = PresencePeer::INSERTION_MODE;
        }


        return $this;
    } // setInsertionMode()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
            if ($this->employee_id !== '-1') {
                return false;
            }

            if ($this->type !== 1) {
                return false;
            }

            if ($this->type_edit !== 1) {
                return false;
            }

            if ($this->original_inout !== -1) {
                return false;
            }

            if ($this->original_inout_edit !== 1) {
                return false;
            }

            if ($this->description !== '') {
                return false;
            }

            if ($this->hour_type_id !== '-1') {
                return false;
            }

            if ($this->hour_type_edit_id !== '-1') {
                return false;
            }

            if ($this->insertion_mode !== 'T') {
                return false;
            }

        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->personnel_presence_id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->employee_id = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->project_id = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->project_edit_id = ($row[$startcol + 3] !== null) ? (string) $row[$startcol + 3] : null;
            $this->date = ($row[$startcol + 4] !== null) ? (string) $row[$startcol + 4] : null;
            $this->date_edit = ($row[$startcol + 5] !== null) ? (string) $row[$startcol + 5] : null;
            $this->type = ($row[$startcol + 6] !== null) ? (int) $row[$startcol + 6] : null;
            $this->type_edit = ($row[$startcol + 7] !== null) ? (int) $row[$startcol + 7] : null;
            $this->original_inout = ($row[$startcol + 8] !== null) ? (int) $row[$startcol + 8] : null;
            $this->original_inout_edit = ($row[$startcol + 9] !== null) ? (int) $row[$startcol + 9] : null;
            $this->description = ($row[$startcol + 10] !== null) ? (string) $row[$startcol + 10] : null;
            $this->hour_type_id = ($row[$startcol + 11] !== null) ? (string) $row[$startcol + 11] : null;
            $this->hour_type_edit_id = ($row[$startcol + 12] !== null) ? (string) $row[$startcol + 12] : null;
            $this->insertion_mode = ($row[$startcol + 13] !== null) ? (string) $row[$startcol + 13] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 14; // 14 = PresencePeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Presence object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aPresenceEmployee !== null && $this->employee_id !== $this->aPresenceEmployee->getEmployeeId()) {
            $this->aPresenceEmployee = null;
        }
        if ($this->aPresenceProject !== null && $this->project_edit_id !== $this->aPresenceProject->getActivityId()) {
            $this->aPresenceProject = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = PresencePeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aPresenceEmployee = null;
            $this->aPresenceProject = null;
        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = PresenceQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(PresencePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                PresencePeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aPresenceEmployee !== null) {
                if ($this->aPresenceEmployee->isModified() || $this->aPresenceEmployee->isNew()) {
                    $affectedRows += $this->aPresenceEmployee->save($con);
                }
                $this->setPresenceEmployee($this->aPresenceEmployee);
            }

            if ($this->aPresenceProject !== null) {
                if ($this->aPresenceProject->isModified() || $this->aPresenceProject->isNew()) {
                    $affectedRows += $this->aPresenceProject->save($con);
                }
                $this->setPresenceProject($this->aPresenceProject);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = PresencePeer::PERSONNEL_PRESENCE_ID;
        if (null !== $this->personnel_presence_id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . PresencePeer::PERSONNEL_PRESENCE_ID . ')');
        }
        if (null === $this->personnel_presence_id) {
            try {
                $stmt = $con->query("SELECT nextval('personnel_presences_personnel_presence_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->personnel_presence_id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(PresencePeer::PERSONNEL_PRESENCE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"personnel_presence_id"';
        }
        if ($this->isColumnModified(PresencePeer::EMPLOYEE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"employee_id"';
        }
        if ($this->isColumnModified(PresencePeer::PROJECT_ID)) {
            $modifiedColumns[':p' . $index++]  = '"project_id"';
        }
        if ($this->isColumnModified(PresencePeer::PROJECT_EDIT_ID)) {
            $modifiedColumns[':p' . $index++]  = '"project_edit_id"';
        }
        if ($this->isColumnModified(PresencePeer::DATE)) {
            $modifiedColumns[':p' . $index++]  = '"date"';
        }
        if ($this->isColumnModified(PresencePeer::DATE_EDIT)) {
            $modifiedColumns[':p' . $index++]  = '"date_edit"';
        }
        if ($this->isColumnModified(PresencePeer::TYPE)) {
            $modifiedColumns[':p' . $index++]  = '"type"';
        }
        if ($this->isColumnModified(PresencePeer::TYPE_EDIT)) {
            $modifiedColumns[':p' . $index++]  = '"type_edit"';
        }
        if ($this->isColumnModified(PresencePeer::ORIGINAL_INOUT)) {
            $modifiedColumns[':p' . $index++]  = '"original_inout"';
        }
        if ($this->isColumnModified(PresencePeer::ORIGINAL_INOUT_EDIT)) {
            $modifiedColumns[':p' . $index++]  = '"original_inout_edit"';
        }
        if ($this->isColumnModified(PresencePeer::DESCRIPTION)) {
            $modifiedColumns[':p' . $index++]  = '"description"';
        }
        if ($this->isColumnModified(PresencePeer::HOUR_TYPE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"hour_type_id"';
        }
        if ($this->isColumnModified(PresencePeer::HOUR_TYPE_EDIT_ID)) {
            $modifiedColumns[':p' . $index++]  = '"hour_type_edit_id"';
        }
        if ($this->isColumnModified(PresencePeer::INSERTION_MODE)) {
            $modifiedColumns[':p' . $index++]  = '"insertion_mode"';
        }

        $sql = sprintf(
            'INSERT INTO "personnel_presences" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"personnel_presence_id"':
                        $stmt->bindValue($identifier, $this->personnel_presence_id, PDO::PARAM_INT);
                        break;
                    case '"employee_id"':
                        $stmt->bindValue($identifier, $this->employee_id, PDO::PARAM_STR);
                        break;
                    case '"project_id"':
                        $stmt->bindValue($identifier, $this->project_id, PDO::PARAM_STR);
                        break;
                    case '"project_edit_id"':
                        $stmt->bindValue($identifier, $this->project_edit_id, PDO::PARAM_STR);
                        break;
                    case '"date"':
                        $stmt->bindValue($identifier, $this->date, PDO::PARAM_STR);
                        break;
                    case '"date_edit"':
                        $stmt->bindValue($identifier, $this->date_edit, PDO::PARAM_STR);
                        break;
                    case '"type"':
                        $stmt->bindValue($identifier, $this->type, PDO::PARAM_INT);
                        break;
                    case '"type_edit"':
                        $stmt->bindValue($identifier, $this->type_edit, PDO::PARAM_INT);
                        break;
                    case '"original_inout"':
                        $stmt->bindValue($identifier, $this->original_inout, PDO::PARAM_INT);
                        break;
                    case '"original_inout_edit"':
                        $stmt->bindValue($identifier, $this->original_inout_edit, PDO::PARAM_INT);
                        break;
                    case '"description"':
                        $stmt->bindValue($identifier, $this->description, PDO::PARAM_STR);
                        break;
                    case '"hour_type_id"':
                        $stmt->bindValue($identifier, $this->hour_type_id, PDO::PARAM_STR);
                        break;
                    case '"hour_type_edit_id"':
                        $stmt->bindValue($identifier, $this->hour_type_edit_id, PDO::PARAM_STR);
                        break;
                    case '"insertion_mode"':
                        $stmt->bindValue($identifier, $this->insertion_mode, PDO::PARAM_STR);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aPresenceEmployee !== null) {
                if (!$this->aPresenceEmployee->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aPresenceEmployee->getValidationFailures());
                }
            }

            if ($this->aPresenceProject !== null) {
                if (!$this->aPresenceProject->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aPresenceProject->getValidationFailures());
                }
            }


            if (($retval = PresencePeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }



            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = PresencePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getPersonnelPresenceId();
                break;
            case 1:
                return $this->getEmployeeId();
                break;
            case 2:
                return $this->getProjectId();
                break;
            case 3:
                return $this->getProjectIdEdit();
                break;
            case 4:
                return $this->getDate();
                break;
            case 5:
                return $this->getDateEdit();
                break;
            case 6:
                return $this->getType();
                break;
            case 7:
                return $this->getTypeEdit();
                break;
            case 8:
                return $this->getOriginalInOut();
                break;
            case 9:
                return $this->getOriginalInOutEdit();
                break;
            case 10:
                return $this->getDescription();
                break;
            case 11:
                return $this->getHourTypeId();
                break;
            case 12:
                return $this->getHourTypeIdEdit();
                break;
            case 13:
                return $this->getInsertionMode();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Presence'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Presence'][$this->getPrimaryKey()] = true;
        $keys = PresencePeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getPersonnelPresenceId(),
            $keys[1] => $this->getEmployeeId(),
            $keys[2] => $this->getProjectId(),
            $keys[3] => $this->getProjectIdEdit(),
            $keys[4] => $this->getDate(),
            $keys[5] => $this->getDateEdit(),
            $keys[6] => $this->getType(),
            $keys[7] => $this->getTypeEdit(),
            $keys[8] => $this->getOriginalInOut(),
            $keys[9] => $this->getOriginalInOutEdit(),
            $keys[10] => $this->getDescription(),
            $keys[11] => $this->getHourTypeId(),
            $keys[12] => $this->getHourTypeIdEdit(),
            $keys[13] => $this->getInsertionMode(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aPresenceEmployee) {
                $result['PresenceEmployee'] = $this->aPresenceEmployee->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->aPresenceProject) {
                $result['PresenceProject'] = $this->aPresenceProject->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = PresencePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setPersonnelPresenceId($value);
                break;
            case 1:
                $this->setEmployeeId($value);
                break;
            case 2:
                $this->setProjectId($value);
                break;
            case 3:
                $this->setProjectIdEdit($value);
                break;
            case 4:
                $this->setDate($value);
                break;
            case 5:
                $this->setDateEdit($value);
                break;
            case 6:
                $this->setType($value);
                break;
            case 7:
                $this->setTypeEdit($value);
                break;
            case 8:
                $this->setOriginalInOut($value);
                break;
            case 9:
                $this->setOriginalInOutEdit($value);
                break;
            case 10:
                $this->setDescription($value);
                break;
            case 11:
                $this->setHourTypeId($value);
                break;
            case 12:
                $this->setHourTypeIdEdit($value);
                break;
            case 13:
                $this->setInsertionMode($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = PresencePeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setPersonnelPresenceId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setEmployeeId($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setProjectId($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setProjectIdEdit($arr[$keys[3]]);
        if (array_key_exists($keys[4], $arr)) $this->setDate($arr[$keys[4]]);
        if (array_key_exists($keys[5], $arr)) $this->setDateEdit($arr[$keys[5]]);
        if (array_key_exists($keys[6], $arr)) $this->setType($arr[$keys[6]]);
        if (array_key_exists($keys[7], $arr)) $this->setTypeEdit($arr[$keys[7]]);
        if (array_key_exists($keys[8], $arr)) $this->setOriginalInOut($arr[$keys[8]]);
        if (array_key_exists($keys[9], $arr)) $this->setOriginalInOutEdit($arr[$keys[9]]);
        if (array_key_exists($keys[10], $arr)) $this->setDescription($arr[$keys[10]]);
        if (array_key_exists($keys[11], $arr)) $this->setHourTypeId($arr[$keys[11]]);
        if (array_key_exists($keys[12], $arr)) $this->setHourTypeIdEdit($arr[$keys[12]]);
        if (array_key_exists($keys[13], $arr)) $this->setInsertionMode($arr[$keys[13]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(PresencePeer::DATABASE_NAME);

        if ($this->isColumnModified(PresencePeer::PERSONNEL_PRESENCE_ID)) $criteria->add(PresencePeer::PERSONNEL_PRESENCE_ID, $this->personnel_presence_id);
        if ($this->isColumnModified(PresencePeer::EMPLOYEE_ID)) $criteria->add(PresencePeer::EMPLOYEE_ID, $this->employee_id);
        if ($this->isColumnModified(PresencePeer::PROJECT_ID)) $criteria->add(PresencePeer::PROJECT_ID, $this->project_id);
        if ($this->isColumnModified(PresencePeer::PROJECT_EDIT_ID)) $criteria->add(PresencePeer::PROJECT_EDIT_ID, $this->project_edit_id);
        if ($this->isColumnModified(PresencePeer::DATE)) $criteria->add(PresencePeer::DATE, $this->date);
        if ($this->isColumnModified(PresencePeer::DATE_EDIT)) $criteria->add(PresencePeer::DATE_EDIT, $this->date_edit);
        if ($this->isColumnModified(PresencePeer::TYPE)) $criteria->add(PresencePeer::TYPE, $this->type);
        if ($this->isColumnModified(PresencePeer::TYPE_EDIT)) $criteria->add(PresencePeer::TYPE_EDIT, $this->type_edit);
        if ($this->isColumnModified(PresencePeer::ORIGINAL_INOUT)) $criteria->add(PresencePeer::ORIGINAL_INOUT, $this->original_inout);
        if ($this->isColumnModified(PresencePeer::ORIGINAL_INOUT_EDIT)) $criteria->add(PresencePeer::ORIGINAL_INOUT_EDIT, $this->original_inout_edit);
        if ($this->isColumnModified(PresencePeer::DESCRIPTION)) $criteria->add(PresencePeer::DESCRIPTION, $this->description);
        if ($this->isColumnModified(PresencePeer::HOUR_TYPE_ID)) $criteria->add(PresencePeer::HOUR_TYPE_ID, $this->hour_type_id);
        if ($this->isColumnModified(PresencePeer::HOUR_TYPE_EDIT_ID)) $criteria->add(PresencePeer::HOUR_TYPE_EDIT_ID, $this->hour_type_edit_id);
        if ($this->isColumnModified(PresencePeer::INSERTION_MODE)) $criteria->add(PresencePeer::INSERTION_MODE, $this->insertion_mode);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(PresencePeer::DATABASE_NAME);
        $criteria->add(PresencePeer::PERSONNEL_PRESENCE_ID, $this->personnel_presence_id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getPersonnelPresenceId();
    }

    /**
     * Generic method to set the primary key (personnel_presence_id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setPersonnelPresenceId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getPersonnelPresenceId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Presence (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setEmployeeId($this->getEmployeeId());
        $copyObj->setProjectId($this->getProjectId());
        $copyObj->setProjectIdEdit($this->getProjectIdEdit());
        $copyObj->setDate($this->getDate());
        $copyObj->setDateEdit($this->getDateEdit());
        $copyObj->setType($this->getType());
        $copyObj->setTypeEdit($this->getTypeEdit());
        $copyObj->setOriginalInOut($this->getOriginalInOut());
        $copyObj->setOriginalInOutEdit($this->getOriginalInOutEdit());
        $copyObj->setDescription($this->getDescription());
        $copyObj->setHourTypeId($this->getHourTypeId());
        $copyObj->setHourTypeIdEdit($this->getHourTypeIdEdit());
        $copyObj->setInsertionMode($this->getInsertionMode());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setPersonnelPresenceId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Presence Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return PresencePeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new PresencePeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Employee object.
     *
     * @param                  Employee $v
     * @return Presence The current object (for fluent API support)
     * @throws PropelException
     */
    public function setPresenceEmployee(Employee $v = null)
    {
        if ($v === null) {
            $this->setEmployeeId('-1');
        } else {
            $this->setEmployeeId($v->getEmployeeId());
        }

        $this->aPresenceEmployee = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Employee object, it will not be re-added.
        if ($v !== null) {
            $v->addPresence($this);
        }


        return $this;
    }


    /**
     * Get the associated Employee object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Employee The associated Employee object.
     * @throws PropelException
     */
    public function getPresenceEmployee(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aPresenceEmployee === null && (($this->employee_id !== "" && $this->employee_id !== null)) && $doQuery) {
            $this->aPresenceEmployee = EmployeeQuery::create()->findPk($this->employee_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aPresenceEmployee->addPresences($this);
             */
        }

        return $this->aPresenceEmployee;
    }

    /**
     * Declares an association between this object and a Activity object.
     *
     * @param                  Activity $v
     * @return Presence The current object (for fluent API support)
     * @throws PropelException
     */
    public function setPresenceProject(Activity $v = null)
    {
        if ($v === null) {
            $this->setProjectIdEdit(NULL);
        } else {
            $this->setProjectIdEdit($v->getActivityId());
        }

        $this->aPresenceProject = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Activity object, it will not be re-added.
        if ($v !== null) {
            $v->addPresence($this);
        }


        return $this;
    }


    /**
     * Get the associated Activity object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Activity The associated Activity object.
     * @throws PropelException
     */
    public function getPresenceProject(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aPresenceProject === null && (($this->project_edit_id !== "" && $this->project_edit_id !== null)) && $doQuery) {
            $this->aPresenceProject = ActivityQuery::create()->findPk($this->project_edit_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aPresenceProject->addPresences($this);
             */
        }

        return $this->aPresenceProject;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->personnel_presence_id = null;
        $this->employee_id = null;
        $this->project_id = null;
        $this->project_edit_id = null;
        $this->date = null;
        $this->date_edit = null;
        $this->type = null;
        $this->type_edit = null;
        $this->original_inout = null;
        $this->original_inout_edit = null;
        $this->description = null;
        $this->hour_type_id = null;
        $this->hour_type_edit_id = null;
        $this->insertion_mode = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->applyDefaultValues();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->aPresenceEmployee instanceof Persistent) {
              $this->aPresenceEmployee->clearAllReferences($deep);
            }
            if ($this->aPresenceProject instanceof Persistent) {
              $this->aPresenceProject->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        $this->aPresenceEmployee = null;
        $this->aPresenceProject = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(PresencePeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
