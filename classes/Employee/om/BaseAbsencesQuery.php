<?php

namespace Employee\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Employee\AbsenceKind;
use Employee\Absences;
use Employee\AbsencesPeer;
use Employee\AbsencesQuery;
use Employee\Employee;

/**
 * Base class that represents a query for the 'absences' table.
 *
 *
 *
 * @method AbsencesQuery orderByAbsenceId($order = Criteria::ASC) Order by the absence_id column
 * @method AbsencesQuery orderByStartDate($order = Criteria::ASC) Order by the start_date column
 * @method AbsencesQuery orderByEndDate($order = Criteria::ASC) Order by the end_date column
 * @method AbsencesQuery orderByAbKind($order = Criteria::ASC) Order by the ab_kind column
 * @method AbsencesQuery orderByTotalDays($order = Criteria::ASC) Order by the total_days column
 * @method AbsencesQuery orderByEmployeeId($order = Criteria::ASC) Order by the employee_id column
 * @method AbsencesQuery orderByDateOfReq($order = Criteria::ASC) Order by the date_of_req column
 * @method AbsencesQuery orderByProtocolId($order = Criteria::ASC) Order by the protocol_id column
 * @method AbsencesQuery orderByTypeOfAbs($order = Criteria::ASC) Order by the type_of_abs column
 * @method AbsencesQuery orderByDecreto($order = Criteria::ASC) Order by the decreto column
 * @method AbsencesQuery orderByNote($order = Criteria::ASC) Order by the note column
 *
 * @method AbsencesQuery groupByAbsenceId() Group by the absence_id column
 * @method AbsencesQuery groupByStartDate() Group by the start_date column
 * @method AbsencesQuery groupByEndDate() Group by the end_date column
 * @method AbsencesQuery groupByAbKind() Group by the ab_kind column
 * @method AbsencesQuery groupByTotalDays() Group by the total_days column
 * @method AbsencesQuery groupByEmployeeId() Group by the employee_id column
 * @method AbsencesQuery groupByDateOfReq() Group by the date_of_req column
 * @method AbsencesQuery groupByProtocolId() Group by the protocol_id column
 * @method AbsencesQuery groupByTypeOfAbs() Group by the type_of_abs column
 * @method AbsencesQuery groupByDecreto() Group by the decreto column
 * @method AbsencesQuery groupByNote() Group by the note column
 *
 * @method AbsencesQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method AbsencesQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method AbsencesQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method AbsencesQuery leftJoinAbsencesAbsenceKind($relationAlias = null) Adds a LEFT JOIN clause to the query using the AbsencesAbsenceKind relation
 * @method AbsencesQuery rightJoinAbsencesAbsenceKind($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AbsencesAbsenceKind relation
 * @method AbsencesQuery innerJoinAbsencesAbsenceKind($relationAlias = null) Adds a INNER JOIN clause to the query using the AbsencesAbsenceKind relation
 *
 * @method AbsencesQuery leftJoinAbsenceEmployee($relationAlias = null) Adds a LEFT JOIN clause to the query using the AbsenceEmployee relation
 * @method AbsencesQuery rightJoinAbsenceEmployee($relationAlias = null) Adds a RIGHT JOIN clause to the query using the AbsenceEmployee relation
 * @method AbsencesQuery innerJoinAbsenceEmployee($relationAlias = null) Adds a INNER JOIN clause to the query using the AbsenceEmployee relation
 *
 * @method Absences findOne(PropelPDO $con = null) Return the first Absences matching the query
 * @method Absences findOneOrCreate(PropelPDO $con = null) Return the first Absences matching the query, or a new Absences object populated from the query conditions when no match is found
 *
 * @method Absences findOneByStartDate(string $start_date) Return the first Absences filtered by the start_date column
 * @method Absences findOneByEndDate(string $end_date) Return the first Absences filtered by the end_date column
 * @method Absences findOneByAbKind(string $ab_kind) Return the first Absences filtered by the ab_kind column
 * @method Absences findOneByTotalDays(int $total_days) Return the first Absences filtered by the total_days column
 * @method Absences findOneByEmployeeId(int $employee_id) Return the first Absences filtered by the employee_id column
 * @method Absences findOneByDateOfReq(string $date_of_req) Return the first Absences filtered by the date_of_req column
 * @method Absences findOneByProtocolId(string $protocol_id) Return the first Absences filtered by the protocol_id column
 * @method Absences findOneByTypeOfAbs(int $type_of_abs) Return the first Absences filtered by the type_of_abs column
 * @method Absences findOneByDecreto(int $decreto) Return the first Absences filtered by the decreto column
 * @method Absences findOneByNote(string $note) Return the first Absences filtered by the note column
 *
 * @method array findByAbsenceId(string $absence_id) Return Absences objects filtered by the absence_id column
 * @method array findByStartDate(string $start_date) Return Absences objects filtered by the start_date column
 * @method array findByEndDate(string $end_date) Return Absences objects filtered by the end_date column
 * @method array findByAbKind(string $ab_kind) Return Absences objects filtered by the ab_kind column
 * @method array findByTotalDays(int $total_days) Return Absences objects filtered by the total_days column
 * @method array findByEmployeeId(int $employee_id) Return Absences objects filtered by the employee_id column
 * @method array findByDateOfReq(string $date_of_req) Return Absences objects filtered by the date_of_req column
 * @method array findByProtocolId(string $protocol_id) Return Absences objects filtered by the protocol_id column
 * @method array findByTypeOfAbs(int $type_of_abs) Return Absences objects filtered by the type_of_abs column
 * @method array findByDecreto(int $decreto) Return Absences objects filtered by the decreto column
 * @method array findByNote(string $note) Return Absences objects filtered by the note column
 *
 * @package    propel.generator.Employee.om
 */
abstract class BaseAbsencesQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseAbsencesQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Employee\\Absences';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new AbsencesQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   AbsencesQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return AbsencesQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof AbsencesQuery) {
            return $criteria;
        }
        $query = new AbsencesQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Absences|Absences[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = AbsencesPeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(AbsencesPeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Absences A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneByAbsenceId($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Absences A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "absence_id", "start_date", "end_date", "ab_kind", "total_days", "employee_id", "date_of_req", "protocol_id", "type_of_abs", "decreto", "note" FROM "absences" WHERE "absence_id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_STR);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Absences();
            $obj->hydrate($row);
            AbsencesPeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Absences|Absences[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Absences[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(AbsencesPeer::ABSENCE_ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(AbsencesPeer::ABSENCE_ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the absence_id column
     *
     * Example usage:
     * <code>
     * $query->filterByAbsenceId(1234); // WHERE absence_id = 1234
     * $query->filterByAbsenceId(array(12, 34)); // WHERE absence_id IN (12, 34)
     * $query->filterByAbsenceId(array('min' => 12)); // WHERE absence_id >= 12
     * $query->filterByAbsenceId(array('max' => 12)); // WHERE absence_id <= 12
     * </code>
     *
     * @param     mixed $absenceId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByAbsenceId($absenceId = null, $comparison = null)
    {
        if (is_array($absenceId)) {
            $useMinMax = false;
            if (isset($absenceId['min'])) {
                $this->addUsingAlias(AbsencesPeer::ABSENCE_ID, $absenceId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($absenceId['max'])) {
                $this->addUsingAlias(AbsencesPeer::ABSENCE_ID, $absenceId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::ABSENCE_ID, $absenceId, $comparison);
    }

    /**
     * Filter the query on the start_date column
     *
     * Example usage:
     * <code>
     * $query->filterByStartDate(1234); // WHERE start_date = 1234
     * $query->filterByStartDate(array(12, 34)); // WHERE start_date IN (12, 34)
     * $query->filterByStartDate(array('min' => 12)); // WHERE start_date >= 12
     * $query->filterByStartDate(array('max' => 12)); // WHERE start_date <= 12
     * </code>
     *
     * @param     mixed $startDate The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByStartDate($startDate = null, $comparison = null)
    {
        if (is_array($startDate)) {
            $useMinMax = false;
            if (isset($startDate['min'])) {
                $this->addUsingAlias(AbsencesPeer::START_DATE, $startDate['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($startDate['max'])) {
                $this->addUsingAlias(AbsencesPeer::START_DATE, $startDate['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::START_DATE, $startDate, $comparison);
    }

    /**
     * Filter the query on the end_date column
     *
     * Example usage:
     * <code>
     * $query->filterByEndDate(1234); // WHERE end_date = 1234
     * $query->filterByEndDate(array(12, 34)); // WHERE end_date IN (12, 34)
     * $query->filterByEndDate(array('min' => 12)); // WHERE end_date >= 12
     * $query->filterByEndDate(array('max' => 12)); // WHERE end_date <= 12
     * </code>
     *
     * @param     mixed $endDate The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByEndDate($endDate = null, $comparison = null)
    {
        if (is_array($endDate)) {
            $useMinMax = false;
            if (isset($endDate['min'])) {
                $this->addUsingAlias(AbsencesPeer::END_DATE, $endDate['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($endDate['max'])) {
                $this->addUsingAlias(AbsencesPeer::END_DATE, $endDate['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::END_DATE, $endDate, $comparison);
    }

    /**
     * Filter the query on the ab_kind column
     *
     * Example usage:
     * <code>
     * $query->filterByAbKind('fooValue');   // WHERE ab_kind = 'fooValue'
     * $query->filterByAbKind('%fooValue%'); // WHERE ab_kind LIKE '%fooValue%'
     * </code>
     *
     * @param     string $abKind The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByAbKind($abKind = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($abKind)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $abKind)) {
                $abKind = str_replace('*', '%', $abKind);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::AB_KIND, $abKind, $comparison);
    }

    /**
     * Filter the query on the total_days column
     *
     * Example usage:
     * <code>
     * $query->filterByTotalDays(1234); // WHERE total_days = 1234
     * $query->filterByTotalDays(array(12, 34)); // WHERE total_days IN (12, 34)
     * $query->filterByTotalDays(array('min' => 12)); // WHERE total_days >= 12
     * $query->filterByTotalDays(array('max' => 12)); // WHERE total_days <= 12
     * </code>
     *
     * @param     mixed $totalDays The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByTotalDays($totalDays = null, $comparison = null)
    {
        if (is_array($totalDays)) {
            $useMinMax = false;
            if (isset($totalDays['min'])) {
                $this->addUsingAlias(AbsencesPeer::TOTAL_DAYS, $totalDays['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($totalDays['max'])) {
                $this->addUsingAlias(AbsencesPeer::TOTAL_DAYS, $totalDays['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::TOTAL_DAYS, $totalDays, $comparison);
    }

    /**
     * Filter the query on the employee_id column
     *
     * Example usage:
     * <code>
     * $query->filterByEmployeeId(1234); // WHERE employee_id = 1234
     * $query->filterByEmployeeId(array(12, 34)); // WHERE employee_id IN (12, 34)
     * $query->filterByEmployeeId(array('min' => 12)); // WHERE employee_id >= 12
     * $query->filterByEmployeeId(array('max' => 12)); // WHERE employee_id <= 12
     * </code>
     *
     * @see       filterByAbsenceEmployee()
     *
     * @param     mixed $employeeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByEmployeeId($employeeId = null, $comparison = null)
    {
        if (is_array($employeeId)) {
            $useMinMax = false;
            if (isset($employeeId['min'])) {
                $this->addUsingAlias(AbsencesPeer::EMPLOYEE_ID, $employeeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($employeeId['max'])) {
                $this->addUsingAlias(AbsencesPeer::EMPLOYEE_ID, $employeeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::EMPLOYEE_ID, $employeeId, $comparison);
    }

    /**
     * Filter the query on the date_of_req column
     *
     * Example usage:
     * <code>
     * $query->filterByDateOfReq(1234); // WHERE date_of_req = 1234
     * $query->filterByDateOfReq(array(12, 34)); // WHERE date_of_req IN (12, 34)
     * $query->filterByDateOfReq(array('min' => 12)); // WHERE date_of_req >= 12
     * $query->filterByDateOfReq(array('max' => 12)); // WHERE date_of_req <= 12
     * </code>
     *
     * @param     mixed $dateOfReq The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByDateOfReq($dateOfReq = null, $comparison = null)
    {
        if (is_array($dateOfReq)) {
            $useMinMax = false;
            if (isset($dateOfReq['min'])) {
                $this->addUsingAlias(AbsencesPeer::DATE_OF_REQ, $dateOfReq['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($dateOfReq['max'])) {
                $this->addUsingAlias(AbsencesPeer::DATE_OF_REQ, $dateOfReq['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::DATE_OF_REQ, $dateOfReq, $comparison);
    }

    /**
     * Filter the query on the protocol_id column
     *
     * Example usage:
     * <code>
     * $query->filterByProtocolId(1234); // WHERE protocol_id = 1234
     * $query->filterByProtocolId(array(12, 34)); // WHERE protocol_id IN (12, 34)
     * $query->filterByProtocolId(array('min' => 12)); // WHERE protocol_id >= 12
     * $query->filterByProtocolId(array('max' => 12)); // WHERE protocol_id <= 12
     * </code>
     *
     * @param     mixed $protocolId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByProtocolId($protocolId = null, $comparison = null)
    {
        if (is_array($protocolId)) {
            $useMinMax = false;
            if (isset($protocolId['min'])) {
                $this->addUsingAlias(AbsencesPeer::PROTOCOL_ID, $protocolId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($protocolId['max'])) {
                $this->addUsingAlias(AbsencesPeer::PROTOCOL_ID, $protocolId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::PROTOCOL_ID, $protocolId, $comparison);
    }

    /**
     * Filter the query on the type_of_abs column
     *
     * Example usage:
     * <code>
     * $query->filterByTypeOfAbs(1234); // WHERE type_of_abs = 1234
     * $query->filterByTypeOfAbs(array(12, 34)); // WHERE type_of_abs IN (12, 34)
     * $query->filterByTypeOfAbs(array('min' => 12)); // WHERE type_of_abs >= 12
     * $query->filterByTypeOfAbs(array('max' => 12)); // WHERE type_of_abs <= 12
     * </code>
     *
     * @param     mixed $typeOfAbs The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByTypeOfAbs($typeOfAbs = null, $comparison = null)
    {
        if (is_array($typeOfAbs)) {
            $useMinMax = false;
            if (isset($typeOfAbs['min'])) {
                $this->addUsingAlias(AbsencesPeer::TYPE_OF_ABS, $typeOfAbs['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($typeOfAbs['max'])) {
                $this->addUsingAlias(AbsencesPeer::TYPE_OF_ABS, $typeOfAbs['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::TYPE_OF_ABS, $typeOfAbs, $comparison);
    }

    /**
     * Filter the query on the decreto column
     *
     * Example usage:
     * <code>
     * $query->filterByDecreto(1234); // WHERE decreto = 1234
     * $query->filterByDecreto(array(12, 34)); // WHERE decreto IN (12, 34)
     * $query->filterByDecreto(array('min' => 12)); // WHERE decreto >= 12
     * $query->filterByDecreto(array('max' => 12)); // WHERE decreto <= 12
     * </code>
     *
     * @param     mixed $decreto The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByDecreto($decreto = null, $comparison = null)
    {
        if (is_array($decreto)) {
            $useMinMax = false;
            if (isset($decreto['min'])) {
                $this->addUsingAlias(AbsencesPeer::DECRETO, $decreto['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($decreto['max'])) {
                $this->addUsingAlias(AbsencesPeer::DECRETO, $decreto['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::DECRETO, $decreto, $comparison);
    }

    /**
     * Filter the query on the note column
     *
     * Example usage:
     * <code>
     * $query->filterByNote('fooValue');   // WHERE note = 'fooValue'
     * $query->filterByNote('%fooValue%'); // WHERE note LIKE '%fooValue%'
     * </code>
     *
     * @param     string $note The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function filterByNote($note = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($note)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $note)) {
                $note = str_replace('*', '%', $note);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(AbsencesPeer::NOTE, $note, $comparison);
    }

    /**
     * Filter the query by a related AbsenceKind object
     *
     * @param   AbsenceKind|PropelObjectCollection $absenceKind The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsencesQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAbsencesAbsenceKind($absenceKind, $comparison = null)
    {
        if ($absenceKind instanceof AbsenceKind) {
            return $this
                ->addUsingAlias(AbsencesPeer::AB_KIND, $absenceKind->getCode(), $comparison);
        } elseif ($absenceKind instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AbsencesPeer::AB_KIND, $absenceKind->toKeyValue('PrimaryKey', 'Code'), $comparison);
        } else {
            throw new PropelException('filterByAbsencesAbsenceKind() only accepts arguments of type AbsenceKind or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AbsencesAbsenceKind relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function joinAbsencesAbsenceKind($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AbsencesAbsenceKind');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AbsencesAbsenceKind');
        }

        return $this;
    }

    /**
     * Use the AbsencesAbsenceKind relation AbsenceKind object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\AbsenceKindQuery A secondary query class using the current class as primary query
     */
    public function useAbsencesAbsenceKindQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAbsencesAbsenceKind($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AbsencesAbsenceKind', '\Employee\AbsenceKindQuery');
    }

    /**
     * Filter the query by a related Employee object
     *
     * @param   Employee|PropelObjectCollection $employee The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 AbsencesQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByAbsenceEmployee($employee, $comparison = null)
    {
        if ($employee instanceof Employee) {
            return $this
                ->addUsingAlias(AbsencesPeer::EMPLOYEE_ID, $employee->getEmployeeId(), $comparison);
        } elseif ($employee instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(AbsencesPeer::EMPLOYEE_ID, $employee->toKeyValue('PrimaryKey', 'EmployeeId'), $comparison);
        } else {
            throw new PropelException('filterByAbsenceEmployee() only accepts arguments of type Employee or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the AbsenceEmployee relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function joinAbsenceEmployee($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('AbsenceEmployee');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'AbsenceEmployee');
        }

        return $this;
    }

    /**
     * Use the AbsenceEmployee relation Employee object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Employee\EmployeeQuery A secondary query class using the current class as primary query
     */
    public function useAbsenceEmployeeQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinAbsenceEmployee($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'AbsenceEmployee', '\Employee\EmployeeQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Absences $absences Object to remove from the list of results
     *
     * @return AbsencesQuery The current query, for fluid interface
     */
    public function prune($absences = null)
    {
        if ($absences) {
            $this->addUsingAlias(AbsencesPeer::ABSENCE_ID, $absences->getAbsenceId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
