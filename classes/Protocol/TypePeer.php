<?php

namespace Protocol;

use Protocol\om\BaseTypePeer;


/**
 * Skeleton subclass for performing query and update operations on the 'protocol_type' table.
 *
 *
 *
 * You should add additional methods to this class to meet the
 * application requirements.  This class will only be generated as
 * long as it does not already exist in the output directory.
 *
 * @package    propel.generator.Protocol
 */
class TypePeer extends BaseTypePeer
{
}
