<?php

namespace Protocol\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'protocol_action' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Protocol.map
 */
class ActionsTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Protocol.map.ActionsTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('protocol_action');
        $this->setPhpName('Actions');
        $this->setClassname('Protocol\\Actions');
        $this->setPackage('Protocol');
        $this->setUseIdGenerator(false);
        // columns
        $this->addPrimaryKey('id', 'Id', 'VARCHAR', true, 100, null);
        $this->addColumn('description', 'Description', 'VARCHAR', true, 100, null);
        $this->addColumn('comment', 'Comment', 'VARCHAR', false, 255, null);
        $this->addColumn('active', 'Active', 'BOOLEAN', true, null, false);
        $this->addForeignKey('type_id', 'TypeId', 'INTEGER', 'protocol_type', 'id', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('Type', 'Protocol\\Type', RelationMap::MANY_TO_ONE, array('type_id' => 'id', ), null, null);
    } // buildRelations()

} // ActionsTableMap
