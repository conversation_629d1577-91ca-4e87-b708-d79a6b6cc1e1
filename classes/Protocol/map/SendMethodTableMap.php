<?php

namespace Protocol\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'protocol_send_method' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Protocol.map
 */
class SendMethodTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Protocol.map.SendMethodTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('protocol_send_method');
        $this->setPhpName('SendMethod');
        $this->setClassname('Protocol\\SendMethod');
        $this->setPackage('Protocol');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('protocol_send_method_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('title', 'Title', 'VARCHAR', true, 100, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
    } // buildRelations()

} // SendMethodTableMap
