<?php

namespace Protocol\map;

use \RelationMap;
use \TableMap;


/**
 * This class defines the structure of the 'protocol_type' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 *
 * @package    propel.generator.Protocol.map
 */
class TypeTableMap extends TableMap
{

    /**
     * The (dot-path) name of this class
     */
    const CLASS_NAME = 'Protocol.map.TypeTableMap';

    /**
     * Initialize the table attributes, columns and validators
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws PropelException
     */
    public function initialize()
    {
        // attributes
        $this->setName('protocol_type');
        $this->setPhpName('Type');
        $this->setClassname('Protocol\\Type');
        $this->setPackage('Protocol');
        $this->setUseIdGenerator(true);
        $this->setPrimaryKeyMethodInfo('protocol_type_id_seq');
        // columns
        $this->addPrimaryKey('id', 'Id', 'INTEGER', true, null, null);
        $this->addColumn('code', 'Code', 'VARCHAR', true, 20, null);
        $this->addColumn('description', 'Description', 'VARCHAR', false, 255, null);
        $this->addForeignKey('parent_type_id', 'ParentTypeId', 'INTEGER', 'protocol_type', 'id', false, null, null);
        // validators
    } // initialize()

    /**
     * Build the RelationMap objects for this table relationships
     */
    public function buildRelations()
    {
        $this->addRelation('TypeRelatedByParentTypeId', 'Protocol\\Type', RelationMap::MANY_TO_ONE, array('parent_type_id' => 'id', ), null, null);
        $this->addRelation('Actions', 'Protocol\\Actions', RelationMap::ONE_TO_MANY, array('id' => 'type_id', ), null, null, 'Actionss');
        $this->addRelation('TypeRelatedById', 'Protocol\\Type', RelationMap::ONE_TO_MANY, array('id' => 'parent_type_id', ), null, null, 'TypesRelatedById');
    } // buildRelations()

} // TypeTableMap
