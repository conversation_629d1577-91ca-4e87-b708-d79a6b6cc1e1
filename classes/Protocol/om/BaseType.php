<?php

namespace Protocol\om;

use \BaseObject;
use \BasePeer;
use \Criteria;
use \Exception;
use \PDO;
use \Persistent;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Protocol\Actions;
use Protocol\ActionsQuery;
use Protocol\Type;
use Protocol\TypePeer;
use Protocol\TypeQuery;

/**
 * Base class that represents a row from the 'protocol_type' table.
 *
 *
 *
 * @package    propel.generator.Protocol.om
 */
abstract class BaseType extends BaseObject implements Persistent
{
    /**
     * Peer class name
     */
    const PEER = 'Protocol\\TypePeer';

    /**
     * The Peer class.
     * Instance provides a convenient way of calling static methods on a class
     * that calling code may not be able to identify.
     * @var        TypePeer
     */
    protected static $peer;

    /**
     * The flag var to prevent infinite loop in deep copy
     * @var       boolean
     */
    protected $startCopy = false;

    /**
     * The value for the id field.
     * @var        int
     */
    protected $id;

    /**
     * The value for the code field.
     * @var        string
     */
    protected $code;

    /**
     * The value for the description field.
     * @var        string
     */
    protected $description;

    /**
     * The value for the parent_type_id field.
     * @var        int
     */
    protected $parent_type_id;

    /**
     * @var        Type
     */
    protected $aTypeRelatedByParentTypeId;

    /**
     * @var        PropelObjectCollection|Actions[] Collection to store aggregation of Actions objects.
     */
    protected $collActionss;
    protected $collActionssPartial;

    /**
     * @var        PropelObjectCollection|Type[] Collection to store aggregation of Type objects.
     */
    protected $collTypesRelatedById;
    protected $collTypesRelatedByIdPartial;

    /**
     * Flag to prevent endless save loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInSave = false;

    /**
     * Flag to prevent endless validation loop, if this object is referenced
     * by another object which falls in this transaction.
     * @var        boolean
     */
    protected $alreadyInValidation = false;

    /**
     * Flag to prevent endless clearAllReferences($deep=true) loop, if this object is referenced
     * @var        boolean
     */
    protected $alreadyInClearAllReferencesDeep = false;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $actionssScheduledForDeletion = null;

    /**
     * An array of objects scheduled for deletion.
     * @var		PropelObjectCollection
     */
    protected $typesRelatedByIdScheduledForDeletion = null;

    /**
     * Get the [id] column value.
     *
     * @return int
     */
    public function getId()
    {

        return $this->id;
    }

    /**
     * Get the [code] column value.
     *
     * @return string
     */
    public function getCode()
    {

        return $this->code;
    }

    /**
     * Get the [description] column value.
     *
     * @return string
     */
    public function getDescription()
    {

        return $this->description;
    }

    /**
     * Get the [parent_type_id] column value.
     *
     * @return int
     */
    public function getParentTypeId()
    {

        return $this->parent_type_id;
    }

    /**
     * Set the value of [id] column.
     *
     * @param  int $v new value
     * @return Type The current object (for fluent API support)
     */
    public function setId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->id !== $v) {
            $this->id = $v;
            $this->modifiedColumns[] = TypePeer::ID;
        }


        return $this;
    } // setId()

    /**
     * Set the value of [code] column.
     *
     * @param  string $v new value
     * @return Type The current object (for fluent API support)
     */
    public function setCode($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->code !== $v) {
            $this->code = $v;
            $this->modifiedColumns[] = TypePeer::CODE;
        }


        return $this;
    } // setCode()

    /**
     * Set the value of [description] column.
     *
     * @param  string $v new value
     * @return Type The current object (for fluent API support)
     */
    public function setDescription($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (string) $v;
        }

        if ($this->description !== $v) {
            $this->description = $v;
            $this->modifiedColumns[] = TypePeer::DESCRIPTION;
        }


        return $this;
    } // setDescription()

    /**
     * Set the value of [parent_type_id] column.
     *
     * @param  int $v new value
     * @return Type The current object (for fluent API support)
     */
    public function setParentTypeId($v)
    {
        if ($v !== null && is_numeric($v)) {
            $v = (int) $v;
        }

        if ($this->parent_type_id !== $v) {
            $this->parent_type_id = $v;
            $this->modifiedColumns[] = TypePeer::PARENT_TYPE_ID;
        }

        if ($this->aTypeRelatedByParentTypeId !== null && $this->aTypeRelatedByParentTypeId->getId() !== $v) {
            $this->aTypeRelatedByParentTypeId = null;
        }


        return $this;
    } // setParentTypeId()

    /**
     * Indicates whether the columns in this object are only set to default values.
     *
     * This method can be used in conjunction with isModified() to indicate whether an object is both
     * modified _and_ has some values set which are non-default.
     *
     * @return boolean Whether the columns in this object are only been set with default values.
     */
    public function hasOnlyDefaultValues()
    {
        // otherwise, everything was equal, so return true
        return true;
    } // hasOnlyDefaultValues()

    /**
     * Hydrates (populates) the object variables with values from the database resultset.
     *
     * An offset (0-based "start column") is specified so that objects can be hydrated
     * with a subset of the columns in the resultset rows.  This is needed, for example,
     * for results of JOIN queries where the resultset row includes columns from two or
     * more tables.
     *
     * @param array $row The row returned by PDOStatement->fetch(PDO::FETCH_NUM)
     * @param int $startcol 0-based offset column which indicates which resultset column to start with.
     * @param boolean $rehydrate Whether this object is being re-hydrated from the database.
     * @return int             next starting column
     * @throws PropelException - Any caught Exception will be rewrapped as a PropelException.
     */
    public function hydrate($row, $startcol = 0, $rehydrate = false)
    {
        try {

            $this->id = ($row[$startcol + 0] !== null) ? (int) $row[$startcol + 0] : null;
            $this->code = ($row[$startcol + 1] !== null) ? (string) $row[$startcol + 1] : null;
            $this->description = ($row[$startcol + 2] !== null) ? (string) $row[$startcol + 2] : null;
            $this->parent_type_id = ($row[$startcol + 3] !== null) ? (int) $row[$startcol + 3] : null;
            $this->resetModified();

            $this->setNew(false);

            if ($rehydrate) {
                $this->ensureConsistency();
            }
            $this->postHydrate($row, $startcol, $rehydrate);

            return $startcol + 4; // 4 = TypePeer::NUM_HYDRATE_COLUMNS.

        } catch (Exception $e) {
            throw new PropelException("Error populating Type object", $e);
        }
    }

    /**
     * Checks and repairs the internal consistency of the object.
     *
     * This method is executed after an already-instantiated object is re-hydrated
     * from the database.  It exists to check any foreign keys to make sure that
     * the objects related to the current object are correct based on foreign key.
     *
     * You can override this method in the stub class, but you should always invoke
     * the base method from the overridden method (i.e. parent::ensureConsistency()),
     * in case your model changes.
     *
     * @throws PropelException
     */
    public function ensureConsistency()
    {

        if ($this->aTypeRelatedByParentTypeId !== null && $this->parent_type_id !== $this->aTypeRelatedByParentTypeId->getId()) {
            $this->aTypeRelatedByParentTypeId = null;
        }
    } // ensureConsistency

    /**
     * Reloads this object from datastore based on primary key and (optionally) resets all associated objects.
     *
     * This will only work if the object has been saved and has a valid primary key set.
     *
     * @param boolean $deep (optional) Whether to also de-associated any related objects.
     * @param PropelPDO $con (optional) The PropelPDO connection to use.
     * @return void
     * @throws PropelException - if this object is deleted, unsaved or doesn't have pk match in db
     */
    public function reload($deep = false, PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("Cannot reload a deleted object.");
        }

        if ($this->isNew()) {
            throw new PropelException("Cannot reload an unsaved object.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TypePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }

        // We don't need to alter the object instance pool; we're just modifying this instance
        // already in the pool.

        $stmt = TypePeer::doSelectStmt($this->buildPkeyCriteria(), $con);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $stmt->closeCursor();
        if (!$row) {
            throw new PropelException('Cannot find matching row in the database to reload object values.');
        }
        $this->hydrate($row, 0, true); // rehydrate

        if ($deep) {  // also de-associate any related objects?

            $this->aTypeRelatedByParentTypeId = null;
            $this->collActionss = null;

            $this->collTypesRelatedById = null;

        } // if (deep)
    }

    /**
     * Removes this object from datastore and sets delete attribute.
     *
     * @param PropelPDO $con
     * @return void
     * @throws PropelException
     * @throws Exception
     * @see        BaseObject::setDeleted()
     * @see        BaseObject::isDeleted()
     */
    public function delete(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("This object has already been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TypePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        try {
            $deleteQuery = TypeQuery::create()
                ->filterByPrimaryKey($this->getPrimaryKey());
            $ret = $this->preDelete($con);
            if ($ret) {
                $deleteQuery->delete($con);
                $this->postDelete($con);
                $con->commit();
                $this->setDeleted(true);
            } else {
                $con->commit();
            }
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Persists this object to the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All modified related objects will also be persisted in the doSave()
     * method.  This method wraps all precipitate database operations in a
     * single transaction.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @throws Exception
     * @see        doSave()
     */
    public function save(PropelPDO $con = null)
    {
        if ($this->isDeleted()) {
            throw new PropelException("You cannot save an object that has been deleted.");
        }

        if ($con === null) {
            $con = Propel::getConnection(TypePeer::DATABASE_NAME, Propel::CONNECTION_WRITE);
        }

        $con->beginTransaction();
        $isInsert = $this->isNew();
        try {
            $ret = $this->preSave($con);
            if ($isInsert) {
                $ret = $ret && $this->preInsert($con);
            } else {
                $ret = $ret && $this->preUpdate($con);
            }
            if ($ret) {
                $affectedRows = $this->doSave($con);
                if ($isInsert) {
                    $this->postInsert($con);
                } else {
                    $this->postUpdate($con);
                }
                $this->postSave($con);
                TypePeer::addInstanceToPool($this);
            } else {
                $affectedRows = 0;
            }
            $con->commit();

            return $affectedRows;
        } catch (Exception $e) {
            $con->rollBack();
            throw $e;
        }
    }

    /**
     * Performs the work of inserting or updating the row in the database.
     *
     * If the object is new, it inserts it; otherwise an update is performed.
     * All related objects are also updated in this method.
     *
     * @param PropelPDO $con
     * @return int             The number of rows affected by this insert/update and any referring fk objects' save() operations.
     * @throws PropelException
     * @see        save()
     */
    protected function doSave(PropelPDO $con)
    {
        $affectedRows = 0; // initialize var to track total num of affected rows
        if (!$this->alreadyInSave) {
            $this->alreadyInSave = true;

            // We call the save method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aTypeRelatedByParentTypeId !== null) {
                if ($this->aTypeRelatedByParentTypeId->isModified() || $this->aTypeRelatedByParentTypeId->isNew()) {
                    $affectedRows += $this->aTypeRelatedByParentTypeId->save($con);
                }
                $this->setTypeRelatedByParentTypeId($this->aTypeRelatedByParentTypeId);
            }

            if ($this->isNew() || $this->isModified()) {
                // persist changes
                if ($this->isNew()) {
                    $this->doInsert($con);
                } else {
                    $this->doUpdate($con);
                }
                $affectedRows += 1;
                $this->resetModified();
            }

            if ($this->actionssScheduledForDeletion !== null) {
                if (!$this->actionssScheduledForDeletion->isEmpty()) {
                    foreach ($this->actionssScheduledForDeletion as $actions) {
                        // need to save related object because we set the relation to null
                        $actions->save($con);
                    }
                    $this->actionssScheduledForDeletion = null;
                }
            }

            if ($this->collActionss !== null) {
                foreach ($this->collActionss as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            if ($this->typesRelatedByIdScheduledForDeletion !== null) {
                if (!$this->typesRelatedByIdScheduledForDeletion->isEmpty()) {
                    foreach ($this->typesRelatedByIdScheduledForDeletion as $typeRelatedById) {
                        // need to save related object because we set the relation to null
                        $typeRelatedById->save($con);
                    }
                    $this->typesRelatedByIdScheduledForDeletion = null;
                }
            }

            if ($this->collTypesRelatedById !== null) {
                foreach ($this->collTypesRelatedById as $referrerFK) {
                    if (!$referrerFK->isDeleted() && ($referrerFK->isNew() || $referrerFK->isModified())) {
                        $affectedRows += $referrerFK->save($con);
                    }
                }
            }

            $this->alreadyInSave = false;

        }

        return $affectedRows;
    } // doSave()

    /**
     * Insert the row in the database.
     *
     * @param PropelPDO $con
     *
     * @throws PropelException
     * @see        doSave()
     */
    protected function doInsert(PropelPDO $con)
    {
        $modifiedColumns = array();
        $index = 0;

        $this->modifiedColumns[] = TypePeer::ID;
        if (null !== $this->id) {
            throw new PropelException('Cannot insert a value for auto-increment primary key (' . TypePeer::ID . ')');
        }
        if (null === $this->id) {
            try {
                $stmt = $con->query("SELECT nextval('protocol_type_id_seq')");
                $row = $stmt->fetch(PDO::FETCH_NUM);
                $this->id = $row[0];
            } catch (Exception $e) {
                throw new PropelException('Unable to get sequence id.', $e);
            }
        }


         // check the columns in natural order for more readable SQL queries
        if ($this->isColumnModified(TypePeer::ID)) {
            $modifiedColumns[':p' . $index++]  = '"id"';
        }
        if ($this->isColumnModified(TypePeer::CODE)) {
            $modifiedColumns[':p' . $index++]  = '"code"';
        }
        if ($this->isColumnModified(TypePeer::DESCRIPTION)) {
            $modifiedColumns[':p' . $index++]  = '"description"';
        }
        if ($this->isColumnModified(TypePeer::PARENT_TYPE_ID)) {
            $modifiedColumns[':p' . $index++]  = '"parent_type_id"';
        }

        $sql = sprintf(
            'INSERT INTO "protocol_type" (%s) VALUES (%s)',
            implode(', ', $modifiedColumns),
            implode(', ', array_keys($modifiedColumns))
        );

        try {
            $stmt = $con->prepare($sql);
            foreach ($modifiedColumns as $identifier => $columnName) {
                switch ($columnName) {
                    case '"id"':
                        $stmt->bindValue($identifier, $this->id, PDO::PARAM_INT);
                        break;
                    case '"code"':
                        $stmt->bindValue($identifier, $this->code, PDO::PARAM_STR);
                        break;
                    case '"description"':
                        $stmt->bindValue($identifier, $this->description, PDO::PARAM_STR);
                        break;
                    case '"parent_type_id"':
                        $stmt->bindValue($identifier, $this->parent_type_id, PDO::PARAM_INT);
                        break;
                }
            }
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute INSERT statement [%s]', $sql), $e);
        }

        $this->setNew(false);
    }

    /**
     * Update the row in the database.
     *
     * @param PropelPDO $con
     *
     * @see        doSave()
     */
    protected function doUpdate(PropelPDO $con)
    {
        $selectCriteria = $this->buildPkeyCriteria();
        $valuesCriteria = $this->buildCriteria();
        BasePeer::doUpdate($selectCriteria, $valuesCriteria, $con);
    }

    /**
     * Array of ValidationFailed objects.
     * @var        array ValidationFailed[]
     */
    protected $validationFailures = array();

    /**
     * Gets any ValidationFailed objects that resulted from last call to validate().
     *
     *
     * @return array ValidationFailed[]
     * @see        validate()
     */
    public function getValidationFailures()
    {
        return $this->validationFailures;
    }

    /**
     * Validates the objects modified field values and all objects related to this table.
     *
     * If $columns is either a column name or an array of column names
     * only those columns are validated.
     *
     * @param mixed $columns Column name or an array of column names.
     * @return boolean Whether all columns pass validation.
     * @see        doValidate()
     * @see        getValidationFailures()
     */
    public function validate($columns = null)
    {
        $res = $this->doValidate($columns);
        if ($res === true) {
            $this->validationFailures = array();

            return true;
        }

        $this->validationFailures = $res;

        return false;
    }

    /**
     * This function performs the validation work for complex object models.
     *
     * In addition to checking the current object, all related objects will
     * also be validated.  If all pass then <code>true</code> is returned; otherwise
     * an aggregated array of ValidationFailed objects will be returned.
     *
     * @param array $columns Array of column names to validate.
     * @return mixed <code>true</code> if all validations pass; array of <code>ValidationFailed</code> objects otherwise.
     */
    protected function doValidate($columns = null)
    {
        if (!$this->alreadyInValidation) {
            $this->alreadyInValidation = true;
            $retval = null;

            $failureMap = array();


            // We call the validate method on the following object(s) if they
            // were passed to this object by their corresponding set
            // method.  This object relates to these object(s) by a
            // foreign key reference.

            if ($this->aTypeRelatedByParentTypeId !== null) {
                if (!$this->aTypeRelatedByParentTypeId->validate($columns)) {
                    $failureMap = array_merge($failureMap, $this->aTypeRelatedByParentTypeId->getValidationFailures());
                }
            }


            if (($retval = TypePeer::doValidate($this, $columns)) !== true) {
                $failureMap = array_merge($failureMap, $retval);
            }


                if ($this->collActionss !== null) {
                    foreach ($this->collActionss as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }

                if ($this->collTypesRelatedById !== null) {
                    foreach ($this->collTypesRelatedById as $referrerFK) {
                        if (!$referrerFK->validate($columns)) {
                            $failureMap = array_merge($failureMap, $referrerFK->getValidationFailures());
                        }
                    }
                }


            $this->alreadyInValidation = false;
        }

        return (!empty($failureMap) ? $failureMap : true);
    }

    /**
     * Retrieves a field from the object by name passed in as a string.
     *
     * @param string $name name
     * @param string $type The type of fieldname the $name is of:
     *               one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *               BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *               Defaults to BasePeer::TYPE_PHPNAME
     * @return mixed Value of field.
     */
    public function getByName($name, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = TypePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);
        $field = $this->getByPosition($pos);

        return $field;
    }

    /**
     * Retrieves a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @return mixed Value of field at $pos
     */
    public function getByPosition($pos)
    {
        switch ($pos) {
            case 0:
                return $this->getId();
                break;
            case 1:
                return $this->getCode();
                break;
            case 2:
                return $this->getDescription();
                break;
            case 3:
                return $this->getParentTypeId();
                break;
            default:
                return null;
                break;
        } // switch()
    }

    /**
     * Exports the object as an array.
     *
     * You can specify the key type of the array by passing one of the class
     * type constants.
     *
     * @param     string  $keyType (optional) One of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     *                    BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                    Defaults to BasePeer::TYPE_PHPNAME.
     * @param     boolean $includeLazyLoadColumns (optional) Whether to include lazy loaded columns. Defaults to true.
     * @param     array $alreadyDumpedObjects List of objects to skip to avoid recursion
     * @param     boolean $includeForeignObjects (optional) Whether to include hydrated related objects. Default to FALSE.
     *
     * @return array an associative array containing the field names (as keys) and field values
     */
    public function toArray($keyType = BasePeer::TYPE_PHPNAME, $includeLazyLoadColumns = true, $alreadyDumpedObjects = array(), $includeForeignObjects = false)
    {
        if (isset($alreadyDumpedObjects['Type'][$this->getPrimaryKey()])) {
            return '*RECURSION*';
        }
        $alreadyDumpedObjects['Type'][$this->getPrimaryKey()] = true;
        $keys = TypePeer::getFieldNames($keyType);
        $result = array(
            $keys[0] => $this->getId(),
            $keys[1] => $this->getCode(),
            $keys[2] => $this->getDescription(),
            $keys[3] => $this->getParentTypeId(),
        );
        $virtualColumns = $this->virtualColumns;
        foreach($virtualColumns as $key => $virtualColumn)
        {
            $result[$key] = $virtualColumn;
        }

        if ($includeForeignObjects) {
            if (null !== $this->aTypeRelatedByParentTypeId) {
                $result['TypeRelatedByParentTypeId'] = $this->aTypeRelatedByParentTypeId->toArray($keyType, $includeLazyLoadColumns,  $alreadyDumpedObjects, true);
            }
            if (null !== $this->collActionss) {
                $result['Actionss'] = $this->collActionss->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
            if (null !== $this->collTypesRelatedById) {
                $result['TypesRelatedById'] = $this->collTypesRelatedById->toArray(null, true, $keyType, $includeLazyLoadColumns, $alreadyDumpedObjects);
            }
        }

        return $result;
    }

    /**
     * Sets a field from the object by name passed in as a string.
     *
     * @param string $name peer name
     * @param mixed $value field value
     * @param string $type The type of fieldname the $name is of:
     *                     one of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME
     *                     BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     *                     Defaults to BasePeer::TYPE_PHPNAME
     * @return void
     */
    public function setByName($name, $value, $type = BasePeer::TYPE_PHPNAME)
    {
        $pos = TypePeer::translateFieldName($name, $type, BasePeer::TYPE_NUM);

        $this->setByPosition($pos, $value);
    }

    /**
     * Sets a field from the object by Position as specified in the xml schema.
     * Zero-based.
     *
     * @param int $pos position in xml schema
     * @param mixed $value field value
     * @return void
     */
    public function setByPosition($pos, $value)
    {
        switch ($pos) {
            case 0:
                $this->setId($value);
                break;
            case 1:
                $this->setCode($value);
                break;
            case 2:
                $this->setDescription($value);
                break;
            case 3:
                $this->setParentTypeId($value);
                break;
        } // switch()
    }

    /**
     * Populates the object using an array.
     *
     * This is particularly useful when populating an object from one of the
     * request arrays (e.g. $_POST).  This method goes through the column
     * names, checking to see whether a matching key exists in populated
     * array. If so the setByName() method is called for that column.
     *
     * You can specify the key type of the array by additionally passing one
     * of the class type constants BasePeer::TYPE_PHPNAME, BasePeer::TYPE_STUDLYPHPNAME,
     * BasePeer::TYPE_COLNAME, BasePeer::TYPE_FIELDNAME, BasePeer::TYPE_NUM.
     * The default key type is the column's BasePeer::TYPE_PHPNAME
     *
     * @param array  $arr     An array to populate the object from.
     * @param string $keyType The type of keys the array uses.
     * @return void
     */
    public function fromArray($arr, $keyType = BasePeer::TYPE_PHPNAME)
    {
        $keys = TypePeer::getFieldNames($keyType);

        if (array_key_exists($keys[0], $arr)) $this->setId($arr[$keys[0]]);
        if (array_key_exists($keys[1], $arr)) $this->setCode($arr[$keys[1]]);
        if (array_key_exists($keys[2], $arr)) $this->setDescription($arr[$keys[2]]);
        if (array_key_exists($keys[3], $arr)) $this->setParentTypeId($arr[$keys[3]]);
    }

    /**
     * Build a Criteria object containing the values of all modified columns in this object.
     *
     * @return Criteria The Criteria object containing all modified values.
     */
    public function buildCriteria()
    {
        $criteria = new Criteria(TypePeer::DATABASE_NAME);

        if ($this->isColumnModified(TypePeer::ID)) $criteria->add(TypePeer::ID, $this->id);
        if ($this->isColumnModified(TypePeer::CODE)) $criteria->add(TypePeer::CODE, $this->code);
        if ($this->isColumnModified(TypePeer::DESCRIPTION)) $criteria->add(TypePeer::DESCRIPTION, $this->description);
        if ($this->isColumnModified(TypePeer::PARENT_TYPE_ID)) $criteria->add(TypePeer::PARENT_TYPE_ID, $this->parent_type_id);

        return $criteria;
    }

    /**
     * Builds a Criteria object containing the primary key for this object.
     *
     * Unlike buildCriteria() this method includes the primary key values regardless
     * of whether or not they have been modified.
     *
     * @return Criteria The Criteria object containing value(s) for primary key(s).
     */
    public function buildPkeyCriteria()
    {
        $criteria = new Criteria(TypePeer::DATABASE_NAME);
        $criteria->add(TypePeer::ID, $this->id);

        return $criteria;
    }

    /**
     * Returns the primary key for this object (row).
     * @return int
     */
    public function getPrimaryKey()
    {
        return $this->getId();
    }

    /**
     * Generic method to set the primary key (id column).
     *
     * @param  int $key Primary key.
     * @return void
     */
    public function setPrimaryKey($key)
    {
        $this->setId($key);
    }

    /**
     * Returns true if the primary key for this object is null.
     * @return boolean
     */
    public function isPrimaryKeyNull()
    {

        return null === $this->getId();
    }

    /**
     * Sets contents of passed object to values from current object.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param object $copyObj An object of Type (or compatible) type.
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @param boolean $makeNew Whether to reset autoincrement PKs and make the object new.
     * @throws PropelException
     */
    public function copyInto($copyObj, $deepCopy = false, $makeNew = true)
    {
        $copyObj->setCode($this->getCode());
        $copyObj->setDescription($this->getDescription());
        $copyObj->setParentTypeId($this->getParentTypeId());

        if ($deepCopy && !$this->startCopy) {
            // important: temporarily setNew(false) because this affects the behavior of
            // the getter/setter methods for fkey referrer objects.
            $copyObj->setNew(false);
            // store object hash to prevent cycle
            $this->startCopy = true;

            foreach ($this->getActionss() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addActions($relObj->copy($deepCopy));
                }
            }

            foreach ($this->getTypesRelatedById() as $relObj) {
                if ($relObj !== $this) {  // ensure that we don't try to copy a reference to ourselves
                    $copyObj->addTypeRelatedById($relObj->copy($deepCopy));
                }
            }

            //unflag object copy
            $this->startCopy = false;
        } // if ($deepCopy)

        if ($makeNew) {
            $copyObj->setNew(true);
            $copyObj->setId(NULL); // this is a auto-increment column, so set to default value
        }
    }

    /**
     * Makes a copy of this object that will be inserted as a new row in table when saved.
     * It creates a new object filling in the simple attributes, but skipping any primary
     * keys that are defined for the table.
     *
     * If desired, this method can also make copies of all associated (fkey referrers)
     * objects.
     *
     * @param boolean $deepCopy Whether to also copy all rows that refer (by fkey) to the current row.
     * @return Type Clone of current object.
     * @throws PropelException
     */
    public function copy($deepCopy = false)
    {
        // we use get_class(), because this might be a subclass
        $clazz = get_class($this);
        $copyObj = new $clazz();
        $this->copyInto($copyObj, $deepCopy);

        return $copyObj;
    }

    /**
     * Returns a peer instance associated with this om.
     *
     * Since Peer classes are not to have any instance attributes, this method returns the
     * same instance for all member of this class. The method could therefore
     * be static, but this would prevent one from overriding the behavior.
     *
     * @return TypePeer
     */
    public function getPeer()
    {
        if (self::$peer === null) {
            self::$peer = new TypePeer();
        }

        return self::$peer;
    }

    /**
     * Declares an association between this object and a Type object.
     *
     * @param                  Type $v
     * @return Type The current object (for fluent API support)
     * @throws PropelException
     */
    public function setTypeRelatedByParentTypeId(Type $v = null)
    {
        if ($v === null) {
            $this->setParentTypeId(NULL);
        } else {
            $this->setParentTypeId($v->getId());
        }

        $this->aTypeRelatedByParentTypeId = $v;

        // Add binding for other direction of this n:n relationship.
        // If this object has already been added to the Type object, it will not be re-added.
        if ($v !== null) {
            $v->addTypeRelatedById($this);
        }


        return $this;
    }


    /**
     * Get the associated Type object
     *
     * @param PropelPDO $con Optional Connection object.
     * @param $doQuery Executes a query to get the object if required
     * @return Type The associated Type object.
     * @throws PropelException
     */
    public function getTypeRelatedByParentTypeId(PropelPDO $con = null, $doQuery = true)
    {
        if ($this->aTypeRelatedByParentTypeId === null && ($this->parent_type_id !== null) && $doQuery) {
            $this->aTypeRelatedByParentTypeId = TypeQuery::create()->findPk($this->parent_type_id, $con);
            /* The following can be used additionally to
                guarantee the related object contains a reference
                to this object.  This level of coupling may, however, be
                undesirable since it could result in an only partially populated collection
                in the referenced object.
                $this->aTypeRelatedByParentTypeId->addTypesRelatedById($this);
             */
        }

        return $this->aTypeRelatedByParentTypeId;
    }


    /**
     * Initializes a collection based on the name of a relation.
     * Avoids crafting an 'init[$relationName]s' method name
     * that wouldn't work when StandardEnglishPluralizer is used.
     *
     * @param string $relationName The name of the relation to initialize
     * @return void
     */
    public function initRelation($relationName)
    {
        if ('Actions' == $relationName) {
            $this->initActionss();
        }
        if ('TypeRelatedById' == $relationName) {
            $this->initTypesRelatedById();
        }
    }

    /**
     * Clears out the collActionss collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Type The current object (for fluent API support)
     * @see        addActionss()
     */
    public function clearActionss()
    {
        $this->collActionss = null; // important to set this to null since that means it is uninitialized
        $this->collActionssPartial = null;

        return $this;
    }

    /**
     * reset is the collActionss collection loaded partially
     *
     * @return void
     */
    public function resetPartialActionss($v = true)
    {
        $this->collActionssPartial = $v;
    }

    /**
     * Initializes the collActionss collection.
     *
     * By default this just sets the collActionss collection to an empty array (like clearcollActionss());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initActionss($overrideExisting = true)
    {
        if (null !== $this->collActionss && !$overrideExisting) {
            return;
        }
        $this->collActionss = new PropelObjectCollection();
        $this->collActionss->setModel('Actions');
    }

    /**
     * Gets an array of Actions objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Type is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Actions[] List of Actions objects
     * @throws PropelException
     */
    public function getActionss($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collActionssPartial && !$this->isNew();
        if (null === $this->collActionss || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collActionss) {
                // return empty collection
                $this->initActionss();
            } else {
                $collActionss = ActionsQuery::create(null, $criteria)
                    ->filterByType($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collActionssPartial && count($collActionss)) {
                      $this->initActionss(false);

                      foreach ($collActionss as $obj) {
                        if (false == $this->collActionss->contains($obj)) {
                          $this->collActionss->append($obj);
                        }
                      }

                      $this->collActionssPartial = true;
                    }

                    $collActionss->getInternalIterator()->rewind();

                    return $collActionss;
                }

                if ($partial && $this->collActionss) {
                    foreach ($this->collActionss as $obj) {
                        if ($obj->isNew()) {
                            $collActionss[] = $obj;
                        }
                    }
                }

                $this->collActionss = $collActionss;
                $this->collActionssPartial = false;
            }
        }

        return $this->collActionss;
    }

    /**
     * Sets a collection of Actions objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $actionss A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Type The current object (for fluent API support)
     */
    public function setActionss(PropelCollection $actionss, PropelPDO $con = null)
    {
        $actionssToDelete = $this->getActionss(new Criteria(), $con)->diff($actionss);


        $this->actionssScheduledForDeletion = $actionssToDelete;

        foreach ($actionssToDelete as $actionsRemoved) {
            $actionsRemoved->setType(null);
        }

        $this->collActionss = null;
        foreach ($actionss as $actions) {
            $this->addActions($actions);
        }

        $this->collActionss = $actionss;
        $this->collActionssPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Actions objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Actions objects.
     * @throws PropelException
     */
    public function countActionss(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collActionssPartial && !$this->isNew();
        if (null === $this->collActionss || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collActionss) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getActionss());
            }
            $query = ActionsQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByType($this)
                ->count($con);
        }

        return count($this->collActionss);
    }

    /**
     * Method called to associate a Actions object to this object
     * through the Actions foreign key attribute.
     *
     * @param    Actions $l Actions
     * @return Type The current object (for fluent API support)
     */
    public function addActions(Actions $l)
    {
        if ($this->collActionss === null) {
            $this->initActionss();
            $this->collActionssPartial = true;
        }

        if (!in_array($l, $this->collActionss->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddActions($l);

            if ($this->actionssScheduledForDeletion and $this->actionssScheduledForDeletion->contains($l)) {
                $this->actionssScheduledForDeletion->remove($this->actionssScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	Actions $actions The actions object to add.
     */
    protected function doAddActions($actions)
    {
        $this->collActionss[]= $actions;
        $actions->setType($this);
    }

    /**
     * @param	Actions $actions The actions object to remove.
     * @return Type The current object (for fluent API support)
     */
    public function removeActions($actions)
    {
        if ($this->getActionss()->contains($actions)) {
            $this->collActionss->remove($this->collActionss->search($actions));
            if (null === $this->actionssScheduledForDeletion) {
                $this->actionssScheduledForDeletion = clone $this->collActionss;
                $this->actionssScheduledForDeletion->clear();
            }
            $this->actionssScheduledForDeletion[]= $actions;
            $actions->setType(null);
        }

        return $this;
    }

    /**
     * Clears out the collTypesRelatedById collection
     *
     * This does not modify the database; however, it will remove any associated objects, causing
     * them to be refetched by subsequent calls to accessor method.
     *
     * @return Type The current object (for fluent API support)
     * @see        addTypesRelatedById()
     */
    public function clearTypesRelatedById()
    {
        $this->collTypesRelatedById = null; // important to set this to null since that means it is uninitialized
        $this->collTypesRelatedByIdPartial = null;

        return $this;
    }

    /**
     * reset is the collTypesRelatedById collection loaded partially
     *
     * @return void
     */
    public function resetPartialTypesRelatedById($v = true)
    {
        $this->collTypesRelatedByIdPartial = $v;
    }

    /**
     * Initializes the collTypesRelatedById collection.
     *
     * By default this just sets the collTypesRelatedById collection to an empty array (like clearcollTypesRelatedById());
     * however, you may wish to override this method in your stub class to provide setting appropriate
     * to your application -- for example, setting the initial array to the values stored in database.
     *
     * @param boolean $overrideExisting If set to true, the method call initializes
     *                                        the collection even if it is not empty
     *
     * @return void
     */
    public function initTypesRelatedById($overrideExisting = true)
    {
        if (null !== $this->collTypesRelatedById && !$overrideExisting) {
            return;
        }
        $this->collTypesRelatedById = new PropelObjectCollection();
        $this->collTypesRelatedById->setModel('Type');
    }

    /**
     * Gets an array of Type objects which contain a foreign key that references this object.
     *
     * If the $criteria is not null, it is used to always fetch the results from the database.
     * Otherwise the results are fetched from the database the first time, then cached.
     * Next time the same method is called without $criteria, the cached collection is returned.
     * If this Type is new, it will return
     * an empty collection or the current collection; the criteria is ignored on a new object.
     *
     * @param Criteria $criteria optional Criteria object to narrow the query
     * @param PropelPDO $con optional connection object
     * @return PropelObjectCollection|Type[] List of Type objects
     * @throws PropelException
     */
    public function getTypesRelatedById($criteria = null, PropelPDO $con = null)
    {
        $partial = $this->collTypesRelatedByIdPartial && !$this->isNew();
        if (null === $this->collTypesRelatedById || null !== $criteria  || $partial) {
            if ($this->isNew() && null === $this->collTypesRelatedById) {
                // return empty collection
                $this->initTypesRelatedById();
            } else {
                $collTypesRelatedById = TypeQuery::create(null, $criteria)
                    ->filterByTypeRelatedByParentTypeId($this)
                    ->find($con);
                if (null !== $criteria) {
                    if (false !== $this->collTypesRelatedByIdPartial && count($collTypesRelatedById)) {
                      $this->initTypesRelatedById(false);

                      foreach ($collTypesRelatedById as $obj) {
                        if (false == $this->collTypesRelatedById->contains($obj)) {
                          $this->collTypesRelatedById->append($obj);
                        }
                      }

                      $this->collTypesRelatedByIdPartial = true;
                    }

                    $collTypesRelatedById->getInternalIterator()->rewind();

                    return $collTypesRelatedById;
                }

                if ($partial && $this->collTypesRelatedById) {
                    foreach ($this->collTypesRelatedById as $obj) {
                        if ($obj->isNew()) {
                            $collTypesRelatedById[] = $obj;
                        }
                    }
                }

                $this->collTypesRelatedById = $collTypesRelatedById;
                $this->collTypesRelatedByIdPartial = false;
            }
        }

        return $this->collTypesRelatedById;
    }

    /**
     * Sets a collection of TypeRelatedById objects related by a one-to-many relationship
     * to the current object.
     * It will also schedule objects for deletion based on a diff between old objects (aka persisted)
     * and new objects from the given Propel collection.
     *
     * @param PropelCollection $typesRelatedById A Propel collection.
     * @param PropelPDO $con Optional connection object
     * @return Type The current object (for fluent API support)
     */
    public function setTypesRelatedById(PropelCollection $typesRelatedById, PropelPDO $con = null)
    {
        $typesRelatedByIdToDelete = $this->getTypesRelatedById(new Criteria(), $con)->diff($typesRelatedById);


        $this->typesRelatedByIdScheduledForDeletion = $typesRelatedByIdToDelete;

        foreach ($typesRelatedByIdToDelete as $typeRelatedByIdRemoved) {
            $typeRelatedByIdRemoved->setTypeRelatedByParentTypeId(null);
        }

        $this->collTypesRelatedById = null;
        foreach ($typesRelatedById as $typeRelatedById) {
            $this->addTypeRelatedById($typeRelatedById);
        }

        $this->collTypesRelatedById = $typesRelatedById;
        $this->collTypesRelatedByIdPartial = false;

        return $this;
    }

    /**
     * Returns the number of related Type objects.
     *
     * @param Criteria $criteria
     * @param boolean $distinct
     * @param PropelPDO $con
     * @return int             Count of related Type objects.
     * @throws PropelException
     */
    public function countTypesRelatedById(Criteria $criteria = null, $distinct = false, PropelPDO $con = null)
    {
        $partial = $this->collTypesRelatedByIdPartial && !$this->isNew();
        if (null === $this->collTypesRelatedById || null !== $criteria || $partial) {
            if ($this->isNew() && null === $this->collTypesRelatedById) {
                return 0;
            }

            if ($partial && !$criteria) {
                return count($this->getTypesRelatedById());
            }
            $query = TypeQuery::create(null, $criteria);
            if ($distinct) {
                $query->distinct();
            }

            return $query
                ->filterByTypeRelatedByParentTypeId($this)
                ->count($con);
        }

        return count($this->collTypesRelatedById);
    }

    /**
     * Method called to associate a Type object to this object
     * through the Type foreign key attribute.
     *
     * @param    Type $l Type
     * @return Type The current object (for fluent API support)
     */
    public function addTypeRelatedById(Type $l)
    {
        if ($this->collTypesRelatedById === null) {
            $this->initTypesRelatedById();
            $this->collTypesRelatedByIdPartial = true;
        }

        if (!in_array($l, $this->collTypesRelatedById->getArrayCopy(), true)) { // only add it if the **same** object is not already associated
            $this->doAddTypeRelatedById($l);

            if ($this->typesRelatedByIdScheduledForDeletion and $this->typesRelatedByIdScheduledForDeletion->contains($l)) {
                $this->typesRelatedByIdScheduledForDeletion->remove($this->typesRelatedByIdScheduledForDeletion->search($l));
            }
        }

        return $this;
    }

    /**
     * @param	TypeRelatedById $typeRelatedById The typeRelatedById object to add.
     */
    protected function doAddTypeRelatedById($typeRelatedById)
    {
        $this->collTypesRelatedById[]= $typeRelatedById;
        $typeRelatedById->setTypeRelatedByParentTypeId($this);
    }

    /**
     * @param	TypeRelatedById $typeRelatedById The typeRelatedById object to remove.
     * @return Type The current object (for fluent API support)
     */
    public function removeTypeRelatedById($typeRelatedById)
    {
        if ($this->getTypesRelatedById()->contains($typeRelatedById)) {
            $this->collTypesRelatedById->remove($this->collTypesRelatedById->search($typeRelatedById));
            if (null === $this->typesRelatedByIdScheduledForDeletion) {
                $this->typesRelatedByIdScheduledForDeletion = clone $this->collTypesRelatedById;
                $this->typesRelatedByIdScheduledForDeletion->clear();
            }
            $this->typesRelatedByIdScheduledForDeletion[]= $typeRelatedById;
            $typeRelatedById->setTypeRelatedByParentTypeId(null);
        }

        return $this;
    }

    /**
     * Clears the current object and sets all attributes to their default values
     */
    public function clear()
    {
        $this->id = null;
        $this->code = null;
        $this->description = null;
        $this->parent_type_id = null;
        $this->alreadyInSave = false;
        $this->alreadyInValidation = false;
        $this->alreadyInClearAllReferencesDeep = false;
        $this->clearAllReferences();
        $this->resetModified();
        $this->setNew(true);
        $this->setDeleted(false);
    }

    /**
     * Resets all references to other model objects or collections of model objects.
     *
     * This method is a user-space workaround for PHP's inability to garbage collect
     * objects with circular references (even in PHP 5.3). This is currently necessary
     * when using Propel in certain daemon or large-volume/high-memory operations.
     *
     * @param boolean $deep Whether to also clear the references on all referrer objects.
     */
    public function clearAllReferences($deep = false)
    {
        if ($deep && !$this->alreadyInClearAllReferencesDeep) {
            $this->alreadyInClearAllReferencesDeep = true;
            if ($this->collActionss) {
                foreach ($this->collActionss as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->collTypesRelatedById) {
                foreach ($this->collTypesRelatedById as $o) {
                    $o->clearAllReferences($deep);
                }
            }
            if ($this->aTypeRelatedByParentTypeId instanceof Persistent) {
              $this->aTypeRelatedByParentTypeId->clearAllReferences($deep);
            }

            $this->alreadyInClearAllReferencesDeep = false;
        } // if ($deep)

        if ($this->collActionss instanceof PropelCollection) {
            $this->collActionss->clearIterator();
        }
        $this->collActionss = null;
        if ($this->collTypesRelatedById instanceof PropelCollection) {
            $this->collTypesRelatedById->clearIterator();
        }
        $this->collTypesRelatedById = null;
        $this->aTypeRelatedByParentTypeId = null;
    }

    /**
     * return the string representation of this object
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->exportTo(TypePeer::DEFAULT_STRING_FORMAT);
    }

    /**
     * return true is the object is in saving state
     *
     * @return boolean
     */
    public function isAlreadyInSave()
    {
        return $this->alreadyInSave;
    }

}
