<?php

namespace Protocol\om;

use \Criteria;
use \Exception;
use \ModelCriteria;
use \ModelJoin;
use \PDO;
use \Propel;
use \PropelCollection;
use \PropelException;
use \PropelObjectCollection;
use \PropelPDO;
use Protocol\Actions;
use Protocol\Type;
use Protocol\TypePeer;
use Protocol\TypeQuery;

/**
 * Base class that represents a query for the 'protocol_type' table.
 *
 *
 *
 * @method TypeQuery orderById($order = Criteria::ASC) Order by the id column
 * @method TypeQuery orderByCode($order = Criteria::ASC) Order by the code column
 * @method TypeQuery orderByDescription($order = Criteria::ASC) Order by the description column
 * @method TypeQuery orderByParentTypeId($order = Criteria::ASC) Order by the parent_type_id column
 *
 * @method TypeQuery groupById() Group by the id column
 * @method TypeQuery groupByCode() Group by the code column
 * @method TypeQuery groupByDescription() Group by the description column
 * @method TypeQuery groupByParentTypeId() Group by the parent_type_id column
 *
 * @method TypeQuery leftJoin($relation) Adds a LEFT JOIN clause to the query
 * @method TypeQuery rightJoin($relation) Adds a RIGHT JOIN clause to the query
 * @method TypeQuery innerJoin($relation) Adds a INNER JOIN clause to the query
 *
 * @method TypeQuery leftJoinTypeRelatedByParentTypeId($relationAlias = null) Adds a LEFT JOIN clause to the query using the TypeRelatedByParentTypeId relation
 * @method TypeQuery rightJoinTypeRelatedByParentTypeId($relationAlias = null) Adds a RIGHT JOIN clause to the query using the TypeRelatedByParentTypeId relation
 * @method TypeQuery innerJoinTypeRelatedByParentTypeId($relationAlias = null) Adds a INNER JOIN clause to the query using the TypeRelatedByParentTypeId relation
 *
 * @method TypeQuery leftJoinActions($relationAlias = null) Adds a LEFT JOIN clause to the query using the Actions relation
 * @method TypeQuery rightJoinActions($relationAlias = null) Adds a RIGHT JOIN clause to the query using the Actions relation
 * @method TypeQuery innerJoinActions($relationAlias = null) Adds a INNER JOIN clause to the query using the Actions relation
 *
 * @method TypeQuery leftJoinTypeRelatedById($relationAlias = null) Adds a LEFT JOIN clause to the query using the TypeRelatedById relation
 * @method TypeQuery rightJoinTypeRelatedById($relationAlias = null) Adds a RIGHT JOIN clause to the query using the TypeRelatedById relation
 * @method TypeQuery innerJoinTypeRelatedById($relationAlias = null) Adds a INNER JOIN clause to the query using the TypeRelatedById relation
 *
 * @method Type findOne(PropelPDO $con = null) Return the first Type matching the query
 * @method Type findOneOrCreate(PropelPDO $con = null) Return the first Type matching the query, or a new Type object populated from the query conditions when no match is found
 *
 * @method Type findOneByCode(string $code) Return the first Type filtered by the code column
 * @method Type findOneByDescription(string $description) Return the first Type filtered by the description column
 * @method Type findOneByParentTypeId(int $parent_type_id) Return the first Type filtered by the parent_type_id column
 *
 * @method array findById(int $id) Return Type objects filtered by the id column
 * @method array findByCode(string $code) Return Type objects filtered by the code column
 * @method array findByDescription(string $description) Return Type objects filtered by the description column
 * @method array findByParentTypeId(int $parent_type_id) Return Type objects filtered by the parent_type_id column
 *
 * @package    propel.generator.Protocol.om
 */
abstract class BaseTypeQuery extends ModelCriteria
{
    /**
     * Initializes internal state of BaseTypeQuery object.
     *
     * @param     string $dbName The dabase name
     * @param     string $modelName The phpName of a model, e.g. 'Book'
     * @param     string $modelAlias The alias for the model in this query, e.g. 'b'
     */
    public function __construct($dbName = null, $modelName = null, $modelAlias = null)
    {
        if (null === $dbName) {
            $dbName = 'mc2api';
        }
        if (null === $modelName) {
            $modelName = 'Protocol\\Type';
        }
        parent::__construct($dbName, $modelName, $modelAlias);
    }

    /**
     * Returns a new TypeQuery object.
     *
     * @param     string $modelAlias The alias of a model in the query
     * @param   TypeQuery|Criteria $criteria Optional Criteria to build the query from
     *
     * @return TypeQuery
     */
    public static function create($modelAlias = null, $criteria = null)
    {
        if ($criteria instanceof TypeQuery) {
            return $criteria;
        }
        $query = new TypeQuery(null, null, $modelAlias);

        if ($criteria instanceof Criteria) {
            $query->mergeWith($criteria);
        }

        return $query;
    }

    /**
     * Find object by primary key.
     * Propel uses the instance pool to skip the database if the object exists.
     * Go fast if the query is untouched.
     *
     * <code>
     * $obj  = $c->findPk(12, $con);
     * </code>
     *
     * @param mixed $key Primary key to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return   Type|Type[]|mixed the result, formatted by the current formatter
     */
    public function findPk($key, $con = null)
    {
        if ($key === null) {
            return null;
        }
        if ((null !== ($obj = TypePeer::getInstanceFromPool((string) $key))) && !$this->formatter) {
            // the object is already in the instance pool
            return $obj;
        }
        if ($con === null) {
            $con = Propel::getConnection(TypePeer::DATABASE_NAME, Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        if ($this->formatter || $this->modelAlias || $this->with || $this->select
         || $this->selectColumns || $this->asColumns || $this->selectModifiers
         || $this->map || $this->having || $this->joins) {
            return $this->findPkComplex($key, $con);
        } else {
            return $this->findPkSimple($key, $con);
        }
    }

    /**
     * Alias of findPk to use instance pooling
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Type A model object, or null if the key is not found
     * @throws PropelException
     */
     public function findOneById($key, $con = null)
     {
        return $this->findPk($key, $con);
     }

    /**
     * Find object by primary key using raw SQL to go fast.
     * Bypass doSelect() and the object formatter by using generated code.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return                 Type A model object, or null if the key is not found
     * @throws PropelException
     */
    protected function findPkSimple($key, $con)
    {
        $sql = 'SELECT "id", "code", "description", "parent_type_id" FROM "protocol_type" WHERE "id" = :p0';
        try {
            $stmt = $con->prepare($sql);
            $stmt->bindValue(':p0', $key, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            Propel::log($e->getMessage(), Propel::LOG_ERR);
            throw new PropelException(sprintf('Unable to execute SELECT statement [%s]', $sql), $e);
        }
        $obj = null;
        if ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $obj = new Type();
            $obj->hydrate($row);
            TypePeer::addInstanceToPool($obj, (string) $key);
        }
        $stmt->closeCursor();

        return $obj;
    }

    /**
     * Find object by primary key.
     *
     * @param     mixed $key Primary key to use for the query
     * @param     PropelPDO $con A connection object
     *
     * @return Type|Type[]|mixed the result, formatted by the current formatter
     */
    protected function findPkComplex($key, $con)
    {
        // As the query uses a PK condition, no limit(1) is necessary.
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKey($key)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->formatOne($stmt);
    }

    /**
     * Find objects by primary key
     * <code>
     * $objs = $c->findPks(array(12, 56, 832), $con);
     * </code>
     * @param     array $keys Primary keys to use for the query
     * @param     PropelPDO $con an optional connection object
     *
     * @return PropelObjectCollection|Type[]|mixed the list of results, formatted by the current formatter
     */
    public function findPks($keys, $con = null)
    {
        if ($con === null) {
            $con = Propel::getConnection($this->getDbName(), Propel::CONNECTION_READ);
        }
        $this->basePreSelect($con);
        $criteria = $this->isKeepQuery() ? clone $this : $this;
        $stmt = $criteria
            ->filterByPrimaryKeys($keys)
            ->doSelect($con);

        return $criteria->getFormatter()->init($criteria)->format($stmt);
    }

    /**
     * Filter the query by primary key
     *
     * @param     mixed $key Primary key to use for the query
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function filterByPrimaryKey($key)
    {

        return $this->addUsingAlias(TypePeer::ID, $key, Criteria::EQUAL);
    }

    /**
     * Filter the query by a list of primary keys
     *
     * @param     array $keys The list of primary key to use for the query
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function filterByPrimaryKeys($keys)
    {

        return $this->addUsingAlias(TypePeer::ID, $keys, Criteria::IN);
    }

    /**
     * Filter the query on the id column
     *
     * Example usage:
     * <code>
     * $query->filterById(1234); // WHERE id = 1234
     * $query->filterById(array(12, 34)); // WHERE id IN (12, 34)
     * $query->filterById(array('min' => 12)); // WHERE id >= 12
     * $query->filterById(array('max' => 12)); // WHERE id <= 12
     * </code>
     *
     * @param     mixed $id The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function filterById($id = null, $comparison = null)
    {
        if (is_array($id)) {
            $useMinMax = false;
            if (isset($id['min'])) {
                $this->addUsingAlias(TypePeer::ID, $id['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($id['max'])) {
                $this->addUsingAlias(TypePeer::ID, $id['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TypePeer::ID, $id, $comparison);
    }

    /**
     * Filter the query on the code column
     *
     * Example usage:
     * <code>
     * $query->filterByCode('fooValue');   // WHERE code = 'fooValue'
     * $query->filterByCode('%fooValue%'); // WHERE code LIKE '%fooValue%'
     * </code>
     *
     * @param     string $code The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function filterByCode($code = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($code)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $code)) {
                $code = str_replace('*', '%', $code);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TypePeer::CODE, $code, $comparison);
    }

    /**
     * Filter the query on the description column
     *
     * Example usage:
     * <code>
     * $query->filterByDescription('fooValue');   // WHERE description = 'fooValue'
     * $query->filterByDescription('%fooValue%'); // WHERE description LIKE '%fooValue%'
     * </code>
     *
     * @param     string $description The value to use as filter.
     *              Accepts wildcards (* and % trigger a LIKE)
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function filterByDescription($description = null, $comparison = null)
    {
        if (null === $comparison) {
            if (is_array($description)) {
                $comparison = Criteria::IN;
            } elseif (preg_match('/[\%\*]/', $description)) {
                $description = str_replace('*', '%', $description);
                $comparison = Criteria::LIKE;
            }
        }

        return $this->addUsingAlias(TypePeer::DESCRIPTION, $description, $comparison);
    }

    /**
     * Filter the query on the parent_type_id column
     *
     * Example usage:
     * <code>
     * $query->filterByParentTypeId(1234); // WHERE parent_type_id = 1234
     * $query->filterByParentTypeId(array(12, 34)); // WHERE parent_type_id IN (12, 34)
     * $query->filterByParentTypeId(array('min' => 12)); // WHERE parent_type_id >= 12
     * $query->filterByParentTypeId(array('max' => 12)); // WHERE parent_type_id <= 12
     * </code>
     *
     * @see       filterByTypeRelatedByParentTypeId()
     *
     * @param     mixed $parentTypeId The value to use as filter.
     *              Use scalar values for equality.
     *              Use array values for in_array() equivalent.
     *              Use associative array('min' => $minValue, 'max' => $maxValue) for intervals.
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function filterByParentTypeId($parentTypeId = null, $comparison = null)
    {
        if (is_array($parentTypeId)) {
            $useMinMax = false;
            if (isset($parentTypeId['min'])) {
                $this->addUsingAlias(TypePeer::PARENT_TYPE_ID, $parentTypeId['min'], Criteria::GREATER_EQUAL);
                $useMinMax = true;
            }
            if (isset($parentTypeId['max'])) {
                $this->addUsingAlias(TypePeer::PARENT_TYPE_ID, $parentTypeId['max'], Criteria::LESS_EQUAL);
                $useMinMax = true;
            }
            if ($useMinMax) {
                return $this;
            }
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }
        }

        return $this->addUsingAlias(TypePeer::PARENT_TYPE_ID, $parentTypeId, $comparison);
    }

    /**
     * Filter the query by a related Type object
     *
     * @param   Type|PropelObjectCollection $type The related object(s) to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TypeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTypeRelatedByParentTypeId($type, $comparison = null)
    {
        if ($type instanceof Type) {
            return $this
                ->addUsingAlias(TypePeer::PARENT_TYPE_ID, $type->getId(), $comparison);
        } elseif ($type instanceof PropelObjectCollection) {
            if (null === $comparison) {
                $comparison = Criteria::IN;
            }

            return $this
                ->addUsingAlias(TypePeer::PARENT_TYPE_ID, $type->toKeyValue('PrimaryKey', 'Id'), $comparison);
        } else {
            throw new PropelException('filterByTypeRelatedByParentTypeId() only accepts arguments of type Type or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the TypeRelatedByParentTypeId relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function joinTypeRelatedByParentTypeId($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('TypeRelatedByParentTypeId');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'TypeRelatedByParentTypeId');
        }

        return $this;
    }

    /**
     * Use the TypeRelatedByParentTypeId relation Type object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Protocol\TypeQuery A secondary query class using the current class as primary query
     */
    public function useTypeRelatedByParentTypeIdQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinTypeRelatedByParentTypeId($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'TypeRelatedByParentTypeId', '\Protocol\TypeQuery');
    }

    /**
     * Filter the query by a related Actions object
     *
     * @param   Actions|PropelObjectCollection $actions  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TypeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByActions($actions, $comparison = null)
    {
        if ($actions instanceof Actions) {
            return $this
                ->addUsingAlias(TypePeer::ID, $actions->getTypeId(), $comparison);
        } elseif ($actions instanceof PropelObjectCollection) {
            return $this
                ->useActionsQuery()
                ->filterByPrimaryKeys($actions->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByActions() only accepts arguments of type Actions or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the Actions relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function joinActions($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('Actions');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'Actions');
        }

        return $this;
    }

    /**
     * Use the Actions relation Actions object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Protocol\ActionsQuery A secondary query class using the current class as primary query
     */
    public function useActionsQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinActions($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'Actions', '\Protocol\ActionsQuery');
    }

    /**
     * Filter the query by a related Type object
     *
     * @param   Type|PropelObjectCollection $type  the related object to use as filter
     * @param     string $comparison Operator to use for the column comparison, defaults to Criteria::EQUAL
     *
     * @return                 TypeQuery The current query, for fluid interface
     * @throws PropelException - if the provided filter is invalid.
     */
    public function filterByTypeRelatedById($type, $comparison = null)
    {
        if ($type instanceof Type) {
            return $this
                ->addUsingAlias(TypePeer::ID, $type->getParentTypeId(), $comparison);
        } elseif ($type instanceof PropelObjectCollection) {
            return $this
                ->useTypeRelatedByIdQuery()
                ->filterByPrimaryKeys($type->getPrimaryKeys())
                ->endUse();
        } else {
            throw new PropelException('filterByTypeRelatedById() only accepts arguments of type Type or PropelCollection');
        }
    }

    /**
     * Adds a JOIN clause to the query using the TypeRelatedById relation
     *
     * @param     string $relationAlias optional alias for the relation
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function joinTypeRelatedById($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        $tableMap = $this->getTableMap();
        $relationMap = $tableMap->getRelation('TypeRelatedById');

        // create a ModelJoin object for this join
        $join = new ModelJoin();
        $join->setJoinType($joinType);
        $join->setRelationMap($relationMap, $this->useAliasInSQL ? $this->getModelAlias() : null, $relationAlias);
        if ($previousJoin = $this->getPreviousJoin()) {
            $join->setPreviousJoin($previousJoin);
        }

        // add the ModelJoin to the current object
        if ($relationAlias) {
            $this->addAlias($relationAlias, $relationMap->getRightTable()->getName());
            $this->addJoinObject($join, $relationAlias);
        } else {
            $this->addJoinObject($join, 'TypeRelatedById');
        }

        return $this;
    }

    /**
     * Use the TypeRelatedById relation Type object
     *
     * @see       useQuery()
     *
     * @param     string $relationAlias optional alias for the relation,
     *                                   to be used as main alias in the secondary query
     * @param     string $joinType Accepted values are null, 'left join', 'right join', 'inner join'
     *
     * @return   \Protocol\TypeQuery A secondary query class using the current class as primary query
     */
    public function useTypeRelatedByIdQuery($relationAlias = null, $joinType = Criteria::LEFT_JOIN)
    {
        return $this
            ->joinTypeRelatedById($relationAlias, $joinType)
            ->useQuery($relationAlias ? $relationAlias : 'TypeRelatedById', '\Protocol\TypeQuery');
    }

    /**
     * Exclude object from result
     *
     * @param   Type $type Object to remove from the list of results
     *
     * @return TypeQuery The current query, for fluid interface
     */
    public function prune($type = null)
    {
        if ($type) {
            $this->addUsingAlias(TypePeer::ID, $type->getId(), Criteria::NOT_EQUAL);
        }

        return $this;
    }

}
