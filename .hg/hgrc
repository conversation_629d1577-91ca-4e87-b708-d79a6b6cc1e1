# example repository config (see 'hg help config' for more info)
[paths]
default = ssh://hg@mcr-hg/mc2api

# path aliases to other clones of this repo in URLs or filesystem paths
# (see 'hg help config.paths' for more info)
#
# default:pushurl = ssh://<EMAIL>/hg/jdoes-fork
# my-fork         = ssh://<EMAIL>/hg/jdoes-fork
# my-clone        = /home/<USER>/jdoes-clone

[ui]
# name and email (local to this repository, optional), e.g.
#
username = cellarosi <<EMAIL>>

